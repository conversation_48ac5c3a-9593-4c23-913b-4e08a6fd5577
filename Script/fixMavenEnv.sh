#!/bin/bash

# 初始化变量
GRADLE_FILE=""
PLATFORM="ios"
ENV="release"
REGION_TYPE="Mainland"
SNAPSHOT_VERSION_SUFFIX='-SNAPSHOT'

# 解析命令行参数
while getopts "g:t:ms" opt; do
  case $opt in
    g) GRADLE_FILE=$OPTARG ;;
    m) PLATFORM="mac" ;;
    t) REGION_TYPE=$OPTARG ;;
    s) ENV="snapshot" ;;
    *) echo "Usage: $0 -g <gradle_file> -t <region: Mainland|Oversea> -m -s"
       exit 1 ;;
  esac
done

# 判断是否提供了必要的参数
if [ -z "$GRADLE_FILE" ]; then
  echo "Missing required arguments. Usage: $0  -g <gradle_file> -t <region: Mainland|Oversea> -m -s"
  exit 1
fi

PLUGIN_NAME=""
PREFIX=""

# 根据平台决定对应的 Maven 依赖前缀
if [ "$PLATFORM" == "ios" ]; then
  PREFIX="com.pwrd.ios"
  if [ "$REGION_TYPE" == "Oversea" ]; then
    PLUGIN_NAME="WPOneEngineBridgeOversea"
  else
    PLUGIN_NAME="WPOneEngineBridgeLaohu"
  fi
elif [ "$PLATFORM" == "mac" ]; then
  PREFIX="com.pwrd.mac"
  if [ "$REGION_TYPE" == "Oversea" ]; then
    PLUGIN_NAME="WPOneEngineBridgeOversea-MacOS"
  else
    PLUGIN_NAME="WPOneEngineBridgeLaohu-MacOS"
  fi
else
  echo "Unsupported platform: $PLATFORM. Only 'ios' or 'mac' are allowed."
  exit 1
fi

# 检查文件是否存在
if [ ! -f "$GRADLE_FILE" ]; then
  echo "File not found: $GRADLE_FILE"
  exit 1
fi

# 提取当前版本号（支持 Gradle 格式的依赖声明）
CURRENT_VERSION=$(grep -E "api \"$PREFIX:[^:]+:[0-9]+\.[0-9]+\.[0-9]+(-SNAPSHOT)?\"" "$GRADLE_FILE" | head -1 | sed -E "s/.*\"$PREFIX:[^:]+:([0-9]+\.[0-9]+\.[0-9]+)(-SNAPSHOT)?\".*/\1\2/")

if [ -z "$CURRENT_VERSION" ]; then
  echo "No version found in $GRADLE_FILE"
  exit 1
fi

CURRENT_ENV="release"
if [[ "$CURRENT_VERSION" == *"-SNAPSHOT" ]]; then
  CURRENT_ENV="snapshot"
fi

# 如果当前环境和目标环境一致，则不修改
if [ "$CURRENT_ENV" == "$ENV" ]; then
  echo "Current environment is already $ENV. No changes made."
  exit 0
fi

# 获取版本号（不包含 SNAPSHOT）
VERSION=$(echo "$CURRENT_VERSION" | sed 's/-SNAPSHOT//')

# 执行替换
if [ "$ENV" == "snapshot" ]; then
  # 替换为 SNAPSHOT 版本（支持普通依赖和带 :SDK@zip 的依赖）
  sed -i '' -E "s|(api \"$PREFIX:[^:]+:)([0-9]+\.[0-9]+\.[0-9]+)(\"|:[^\"]+\")|\1\2$SNAPSHOT_VERSION_SUFFIX\3|g" "$GRADLE_FILE"
  echo "Gradle file updated to use SNAPSHOT version."
elif [ "$ENV" == "release" ]; then
  # 替换为正式版本（去掉 SNAPSHOT 后缀）
  sed -i '' -E "s|(api \"$PREFIX:[^:]+:)([0-9]+\.[0-9]+\.[0-9]+)$SNAPSHOT_VERSION_SUFFIX(\"|:[^\"]+\")|\1\2\3|g" "$GRADLE_FILE"
  echo "Gradle file updated to use release version."
else
  echo "Invalid environment: $ENV. Use 'snapshot' or 'release'."
  exit 1
fi