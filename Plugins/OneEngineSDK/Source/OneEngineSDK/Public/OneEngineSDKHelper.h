#pragma once

#include "Engine/Texture2D.h"
#include "OneEngineSDKHelper.generated.h"

UENUM(BlueprintType)
enum class EOneEngineThirdType: uint8
{
	Guest = 0,          //游客
	Facebook = 1,       //Facebook
	Google = 2,         //Google
	Twitter = 3,        //Twitter
	Line = 4,           //Line
	GooglePlay = 5,     //GooglePlay
	GameCenter = 6,     //GameCenter
	Phone = 7,          //手机
	Email = 8,          //Email
	VK = 9,             //VK
	Naver = 10,          //Naver
	Apple = 11,          //Apple
	WeChat = 12,         //微信
	GuestInherit = 13,   //游客继承码
	Yandex = 14,         //Yandex
	MailRu = 15,         //MailRu
	Infiplay = 16,       //Infiplay
	HW = 17,			 //华为
	ShareCode = 18,      //ShareCode
	HONOR = 19,          //荣耀
	STEAM = 20,          //steam
	PCScanCode = 22,     //PCScanCode
	PlayStation = 23,    //PlayStation 
	APJ = 24,            //APJ
	Crunchyrool = 25,    //Crunchyrool
	LinkAccount = 26,	 //连携ID
	NaverCafe = 50,      //NaverCafe

	WMPass = 100,         //完美通信证
};

// 用户三方平台信息
USTRUCT(BlueprintType)
struct FOneUserThirdInfo
{
    GENERATED_BODY()

    // 用户ID
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
    FString UserId{""};
    
    // 用户ThirdId
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
    FString ThirdId{""};
    
    
    // 用户头像
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
    FString Avatar{""};
    
    // 用户名
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
    FString UserName{""};
    
    // Email
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
    FString Email{""};
    
    //  平台类型
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
    EOneEngineThirdType Type = EOneEngineThirdType::Phone;
    
};

// 用户信息
USTRUCT(BlueprintType)
struct FOneUserInfo
{
	GENERATED_BODY()

	// 用户ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
	FString UserId{""};
	// 用户token
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
	FString Token{""};
	// 手机号
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
	FString Phone{""};
    
    // 用户头像
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
    FString Avatar{""};
    
    // 用户名
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
    FString UserName{""};
    
    // 继承码
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
    FString InheritCode{""};
    
    // 是否存在手机账号密码
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
    bool bPasswordExist{false};

    // 客户端国家代码
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
    FString CountryCode{""};
    
    //  三方信息列表
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
    TArray<FOneUserThirdInfo> Thirds;

    //  平台类型
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
    EOneEngineThirdType Type = EOneEngineThirdType::Phone;

	// 是否为新用户
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
	bool bIsNewCreate{false};

	// 年龄
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
	int32 Age{0};

	// 是否成年人
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
	bool bIsAdult{false};

	// 国家代码
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|UserInfo")
	FString AgeCountryCode{""};
};

// 支付档位信息
USTRUCT(BlueprintType)
struct FOneProductInfo
{
	GENERATED_BODY()

	// 商品ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|ProductInfo")
	FString ProductId{""};
	// 商品价格
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|ProductInfo")
	FString Price{""};
	// 货币代码
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|ProductInfo")
	FString Currency{""};
	// 带货币符号的商品价格
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|ProductInfo")
	FString SymbolPrice{""};
	// 商品标题
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|ProductInfo")
	FString Title{""};
	// 商品描述
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|ProductInfo")
	FString Desc{""};
};

// 支付参数
USTRUCT(BlueprintType)
struct FOnePaymentInfo
{
	GENERATED_BODY()

	// 订单号，必须唯一
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|Pay")
	FString OrderId{""};
	// 商品价格
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|Pay")
	FString Price{""};
	// 商品名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|Pay")
	FString ProductName{""};
	// 游戏服务器ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|Pay")
	FString GameServerId{""};
	// 游戏服务器名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|Pay")
	FString ServerName{""};
	// 角色ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|Pay")
	FString RoleId{""};
	// 角色名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|Pay")
	FString RoleName{""};
	// 商品ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|Pay")
	FString ProductId{""};
	// 商品数量，默认1
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|Pay")
	FString ProductCount{"1"};
	// 扩展字段，用于透传信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|Pay")
	FString ExtInfo{""};
	// 支付成功以后，服务器的回调地址，海外时不能为空
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|Pay")
	FString PaySuccessUrl{""};
};

// 角色信息
USTRUCT(BlueprintType)
struct FOneRoleInfo
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|RoleInfo")
	FString RoleId;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|RoleInfo")
	FString RoleName;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|RoleInfo")
	FString Vip;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|RoleInfo")
	FString Level;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|RoleInfo")
	FString ServerId;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|RoleInfo")
	FString ServerName;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|RoleInfo")
	FString CombatValue;
};

// 资源打点类型
UENUM(BlueprintType)
enum class EOneResEventState : uint8
{
	Begin,
	Success,
	Failed
};

// 用户位置信息
USTRUCT(BlueprintType)
struct FOneUserLocationInfo
{
	GENERATED_BODY()

	//服务端国家代码
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|LocationInfo")
	FString CountryAbbr;
	//国家
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|LocationInfo")
	FString Country;
	//省份
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|LocationInfo")
	FString Province;
	//城市
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|LocationInfo")
	FString City;
	//客户端国家代码
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|LocationInfo")
	FString CountryCode;
	//ip地址
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|LocationInfo")
	FString IP;
};

// 设备信息
USTRUCT(BlueprintType)
struct FOneDeviceInfo
{
	GENERATED_BODY()

	//设备唯一标识
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|DeviceInfo")
	FString DeviceId;
	//设备操作系统版本
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|DeviceInfo")
	FString DeviceSys;
	//设备其他信息，游戏按照需要进行获取
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|DeviceInfo")
	TMap<FString, FString> Ext;
};

//防沉迷信息
USTRUCT(BlueprintType)
struct FOneAntiAddictionInfo
{
	GENERATED_BODY()
	// 应用ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|AntiAddictionInfo")
	FString AppID;
	// 用户ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|AntiAddictionInfo")
	FString UserId;
	// 防沉迷状态: 0 正常可进入游戏; 1 不可进入游戏
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|AntiAddictionInfo")
	int32 Status{0};
	// 心跳间隔，单位:秒
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|AntiAddictionInfo")
	int32 HeartbeatInterval{0};
	// 封禁类型: 1 封禁；2 宵禁；3 单次在线时长超限；4 当日累计在线时长超限 5是未实名体验时间到
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|AntiAddictionInfo")
	int32 BannedType{0};
	// 禁玩理由。如果自行实现提醒或下线的UI，请读取本字段用于显示。
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|AntiAddictionInfo")
	FString BannedReason;
	// 休息通知，仅提醒，不要求登出
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|AntiAddictionInfo")
	FString BreakNotice;
	// 实名类型: 0 未实名; 1 身份证实名; 2 护照实名
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|AntiAddictionInfo")
	int32 Realuser{0};
	// 公民类型: 0 未实名; 1 中国大陆; 2 海外
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|AntiAddictionInfo")
	int32 CivicType{0};
	// 年龄: 0 未知; 其它数值为当前用户的年龄
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|AntiAddictionInfo")
	int32 Age{0};
	// 性别: 0 未知; 1 男; 2 女
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|AntiAddictionInfo")
	int32 Gender{0};
	// 账号类型: 0 未成年;1 成年
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|AntiAddictionInfo")
	int32 AccountType{0};
	// 请求IP
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|AntiAddictionInfo")
	FString RequestIp;
	// 当日在线时长，单位：分钟。关闭防沉迷时该项为 0 
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|AntiAddictionInfo")
	int32 DayOnlineDuration{0};
};

// 兑换码服务获取用户角色列表数据
USTRUCT(BlueprintType)
struct FOneURCRoleInfo
{
	GENERATED_BODY()
	// 用户ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|URedeemCode")
	FString UserId;
	// 角色ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|URedeemCode")
	FString RoleId;
	// 角色名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|URedeemCode")
	FString RoleName;
	// 角色等级
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|URedeemCode")
	FString Level;
	// 游戏服务器ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|URedeemCode")
	FString ServerId;
	// 游戏服务器名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|URedeemCode")
	FString ServerName;
	// 角色性别
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|URedeemCode")
	FString Gender;
	// 角色职业
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|URedeemCode")
	FString Occupation;
	// 最后一次登录的时间
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|URedeemCode")
	FString LastLoginTime;
};

UENUM(BlueprintType)
enum class EOnePermissionType : uint8
{
	Clipboard = 0,//剪贴板
	ReadExternalStorage,//读取存储卡
	WriteExternalStorage,//写入存储卡
	Camera,//相机
	RecordAudio,//麦克风音频
	CoarseLocation,//网络定位
	FineLocation,//GPS定位
	CallPhone,//打电话
	ReadContacts,//读取通讯录
	ReadSms,//读取短信
	ReadCalendar,//读取日历
	BodySensors,//传感器
	Notification,//通知权限
	ATTTrack,//广告追踪权限
};

USTRUCT(BlueprintType)
struct FOnePermissionInfo
{
	GENERATED_BODY()

	// 权限
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK")
	EOnePermissionType PermissionType = EOnePermissionType::Clipboard;
	// 权限名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK")
	FString Title;
	// 权限描述
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK")
	FString Desc;
};

// AIHelp客服类型
UENUM(BlueprintType)
enum class EOneAIHelpType : uint8
{
	Unknown = 0,
	RobotChat = 1,
	FAQ = 2,
};

// 屏幕方向
UENUM(BlueprintType)
enum class EOneScreenOrientation : uint8
{
	Unknown = 0,
	Portrait = 1,
	Landscape = 2,
};

// 分享平台
UENUM(BlueprintType)
enum class EOneShareAppTarget : uint8
{
	PE_WeChatSession = 0,
	PE_WeChatMoment,
	PE_QQ,
	PE_QZone,
	PE_Weibo,
	PE_Bilibili,
	PE_Facebook,
	PE_VK,
	PE_Instagram,
	PE_Twitter,
	PE_Line,
	PE_NaverGame,
	PE_TikTok,
	PE_Discord,
	PE_Telegram,
};

// 分享内容类型
UENUM(BlueprintType)
enum class EOneShareType : uint8
{
	Image,//图片
	WebPage,//网页
	Text,//文本
	ImageSnapShot,//图片instagram 快拍
};

// 微博超话分享内容
USTRUCT(BlueprintType)
struct FOneShareWeiboSuperData
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK")
	FString SuperGroup;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK")
	FString Section;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK")
	TMap<FString, FString> ExtraInfo;
};

// 分享数据内容
USTRUCT(BlueprintType)
struct FOneShareData
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK")
	FString WebPageUrl;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK")
	FString Title;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK")
	FString Content;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK")
	UTexture2D* Thumbnail = nullptr;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK")
	UTexture2D* Image = nullptr;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK")
	FString NetImageUrl;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK")
	FString LocalImagePath;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK")
	FString TopicId;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK")
	FOneShareWeiboSuperData SinaSuperGroup; //微博超话分享
};

USTRUCT(BlueprintType)
struct FOnePushStatus
{
	GENERATED_BODY()

public:
	// 是否是打开了推送权限
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
    bool bIsSysOpen{false};
	//app 是否打开推送
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsAppOpen{false};
};

USTRUCT(BlueprintType)
struct FOnePushTypeInfo
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	FString Name;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bOpen{false};
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int PushType;
};

USTRUCT(BlueprintType)
struct FOnePushMessage
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	FString Title;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Content;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString MessageId;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Ext;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsActive{false}; //是否在前台
};

USTRUCT(BlueprintType)
struct FOnePushNotDisturbInfo
{
	GENERATED_BODY()

public:
	// 是否开启夜间勿扰，NO-不开启，YES-开启
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bNotDisturb{false};

	// 夜间勿扰开始时间，如果开始时间大于结束时间，则代表跨天。示例："01:00"
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString NotDisturbStartTime;

	// 夜间勿扰结束时间，如果开始时间大于结束时间，则代表跨天。示例："10:00"
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString NotDisturbEndTime;
};

USTRUCT(BlueprintType)
struct FOneActiveQualificationInfo
{
	GENERATED_BODY()

public:
	// 用户激活资格（0：无资格，1：有资格)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|ActiveQualification")
	int Status{1};

	// 已登录设备总数
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|ActiveQualification")
	int DeviceTotal{0};
	
	// 用户是否添加到白名单（0：没有添加，1：已添加）
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|ActiveQualification")
	int WhiteList{1};

	// 当前设备是否在已登录设备中（0：不在，1：在）
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OneEngineSDK|ActiveQualification")
	int DeviceLogged{0};
};


USTRUCT(BlueprintType)
struct FUserIpInfo
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Attribution;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString CountryCode;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString CityCode;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Country;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Region;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString City;
};

UENUM(BlueprintType)
enum class EOneEngineSDKRegionType : uint8
{
	Mainland = 0, //大陆
	Oversea = 1 //海外
};

// 定义解锁结果的枚举类型
UENUM(BlueprintType)
enum class EOneUnlockSafeLockResult : uint8 {
	WaitingToUnlock,    // 已触发客户端推送，等待用户解锁
	UnlockSucceeded,    // 解锁成功，用户同意解锁
	UnlockTimedOut,        // 解锁超时，用户未在规定时间内解锁(轮询时间到)
	UnlockFailed        // 解锁失败，其他原因(网络异常、服务器端错误(比如功能未开启、动态码无效等))
};

// 安全锁解锁方式枚举类型
UENUM(BlueprintType)
enum class EOneUnlockSafeLockType : uint8 {
	PushNotification,    // 推送
	DynamicCode          // 动态码或授权码
};

UENUM(BlueprintType)
enum class EOneNaverGameType : uint8
{
	Banner,
	Sorry,
	Board,
	Feed
};

class FOneEngineSDKHelper
{
public:
	FOneEngineSDKHelper(const FOneEngineSDKHelper& Other) = delete;
	FOneEngineSDKHelper& operator=(const FOneEngineSDKHelper& Other) = delete;

	// 登录完成回调
	static void OnLoginResultDelegate(bool bSucceed, int Code, const FString& Msg, const FString& UserId);

	// 登出回调
	static void OnLogoutResultDelegate(bool bSucceed, int Code, const FString& Msg);

	// 支付回调
	static void OnPaymetResultDelegate(bool bSucceed, int Code, const FString& Msg, const FString& OrderId);

	// 防沉迷踢出回调
	static void OnAntiAddictionTimeoutResultDelegate(bool bForceKick, const FOneAntiAddictionInfo& Info);

	// Android 返回键处理
	static bool OnGameShowExitDialog();

	// Android 渠道退出
	static void OnExit();

private:
	FOneEngineSDKHelper() = default;
	~FOneEngineSDKHelper() = default;
};
