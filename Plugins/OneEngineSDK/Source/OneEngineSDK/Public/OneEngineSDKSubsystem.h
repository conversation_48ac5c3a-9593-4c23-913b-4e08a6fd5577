// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/EngineSubsystem.h"
#include "OneEngineSDKHelper.h"
#include "OneEngineSDKSubsystem.generated.h"

/**
 * 
 */
UCLASS()
class ONEENGINESDK_API UOneEngineSDKSubsystem : public UEngineSubsystem
{
	GENERATED_BODY()
public:
	// ========================================================================================
	// === 初始化模块 	=====================================================================
	// ========================================================================================

	virtual void Initialize(FSubsystemCollectionBase& Collection) override;  
	// 通用回调代理
	DECLARE_DYNAMIC_DELEGATE_ThreeParams(FOneGenericResultDelegate,bool,bSuccess, int32, Code, const FString&, Msg);
	
	// 初始化
	DECLARE_DYNAMIC_DELEGATE_ThreeParams(FOneInitDelegate,bool,bSuccess,int, Code, const FString&, Msg);
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void Init(FOneInitDelegate InitDelegate);

	//获取审核开关
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	bool ExaminStatus();

	// 海外设置 appid
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void SetUpConfigAppID(const FString& AppId);

	// 获取 appid
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	FString GetAppId();

	// ========================================================================================
	// === 标准登录模块	=====================================================================
	// ========================================================================================
	
	// 登录
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_FourParams(FOneLoginResultDelegate,bool,bSuccess, int, Code, const FString&, Msg, const FOneUserInfo&, UserInfo);
	UPROPERTY(BlueprintAssignable, Category = "OneEngineSDKSubsystem")
	FOneLoginResultDelegate LoginResultDelegate;
	
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void Login();

	// 扫码登录、支付等，获取扫码结果
	DECLARE_DYNAMIC_DELEGATE_TwoParams(FOneGetQRCodeScanResultDelegate, const FString&, CodeType, const FString&, CodeLink);
	UFUNCTION(BlueprintCallable,Category="OneEngineSDKSubsystem")
	void GetQRCodeScanResult(FOneGetQRCodeScanResultDelegate OnGetQRCodeScanResultDelegate);

	// ========================================================================================
	// === 纯接口登录模块 (海外独有)	===============================================================
	// ========================================================================================
    
    // 获取 token 列表
    DECLARE_DYNAMIC_DELEGATE_FourParams(FOneFetchUserTokenListDelegate,bool,bSuccess, int32, Code, const FString&, Msg, const TArray<FOneUserInfo>&, TokenList);
    UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
    void GetUserTokenList(FOneFetchUserTokenListDelegate OnGetTokenListDelegate);
    
    void GetUserTokenListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)> &OnFinishedLambda);
    // token 登录
    UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
    void TokenLogin(const FString& Token, const FString& Uid, EOneEngineThirdType ThirdType);

    // 三方登录
    UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
    void ThirdLogin(EOneEngineThirdType ThirdType, bool bForcedLogin);
    
    // 游客登录
    UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
    void GuestLogin();
    
	// ========================================================================================
	// === 支付模块	===========================================================================
	// ========================================================================================

	// 支付
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_FourParams(FOnePayResultDelegate,bool,bSuccess, int, Code, const FString&, Msg, const FString&, OrderId);
	UPROPERTY(BlueprintAssignable, Category = "OneEngineSDKSubsystem")
	FOnePayResultDelegate PayResultDelegate;
	
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void Pay(const FOnePaymentInfo& PaymentInfo);

	//获取商品档位信息
	DECLARE_DYNAMIC_DELEGATE_ThreeParams(FOneProductInfoDelegate,bool, bSucceed, int, Code, const TArray<FOneProductInfo>&, ProductListResult);
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void GetProductList(const TArray<FString>& ProductIds, FOneProductInfoDelegate ProductResultDelegate);
	
	// ========================================================================================
	// === 统计打点信息模块	==================================================================
	// ========================================================================================
	
	// 获取平台
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	int32 GetPlatformOS();

	// 获取渠道 platform
	DECLARE_DYNAMIC_DELEGATE_FourParams(FOneGetPlatformDelegate,bool,bSuccess, int, Code, const FString&, Msg, const FString&, Platform);
	UFUNCTION(BlueprintCallable,Category="OneEngineSDKSubsystem")
	void GetChannelPlatform(FOneGetPlatformDelegate OnGetPlatformResultDelegate);

	void GetChannelPlatformLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)> &OnFinishedLambda);
	
	// 获取当前 OneSDK 的渠道号
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	FString GetChannelId();

	// 获取渠道 MediaId
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	FString GetChannelMediaId();

	// 获取设备信息
	DECLARE_DYNAMIC_DELEGATE_OneParam(FOneGetDeviceInfoDelegate, const FOneDeviceInfo&, DeviceInfo);
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void GetDeviceInfo(FOneGetDeviceInfoDelegate Delegate);
	
	void GetDeviceInfoLambda(const TFunction<void(const FOneDeviceInfo& DeviceInfo)> &OnFinishedLambda);
	// ========================================================================================
	// === 统计打点模块	=====================================================================
	// ========================================================================================

	// 角色事件打点
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void TrackEventRoleLoginSucceed(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port);

	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void TrackEventRoleLoginError(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port, const FString& Code, const FString& Msg);

	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void TrackEventRoleLogout(const FOneRoleInfo& RoleInfo);

	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void TrackEventRoleCreate(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port);

	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void TrackEventRoleLevelUp(const FOneRoleInfo& RoleInfo);

	// 资源事件打点
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void GameResReqEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg);

	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void GameUpdateAssetEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg);

	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void GameResDecEvent(EOneResEventState State, const FString& ErrorMsg);

	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void GameGetServerListEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg);

	// 游戏自定义事件打点
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void TrackEvent(const FString& Name, const TMap<FString, FString>& Payload);
	
	// 游戏自定义广告打点
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void TrackEventAD(const FString& Name, const TMap<FString, FString>& Payload);

	// 进入游戏场景打点
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void TrackEventEnterGameScene(const FString& SceneName,int32 Period,const TMap<FString,FString>& HintMap);

	// 退出游戏场景打点
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void TrackEventExitGameScene();

	// 设备打点额外信息
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void TrackEventAddExtraDeviceInfo(const TMap<FString,FString>& ExtraDeviceInfo);

	// ========================================================================================
	// === 大陆合规模块	=====================================================================
	// ========================================================================================
	
	// 防沉迷
	// 获取防沉迷信息
	DECLARE_DYNAMIC_DELEGATE_OneParam(FOneFetchAntiAddictionInfoDelegate, const FOneAntiAddictionInfo&, Info);
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void FetchAntiAddictionInfo(FOneFetchAntiAddictionInfoDelegate OnFetchAntiAddictionInfo);
	
	// 防沉迷到时间踢出回调
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOneAntiAddictionTimeoutDelegate, bool, bForceKick, const FOneAntiAddictionInfo&, Info);
	UPROPERTY(BlueprintAssignable, Category = "OneEngineSDKSubsystem")
	FOneAntiAddictionTimeoutDelegate OnAntiAddictionTimeoutDelegate;
	
	// 开启防沉迷监控
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void StartAntiAddictionNotify(const FString& ServerId, const FString& RoleId);
	
	// 关闭防沉迷监控
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void StopAntiAddictionNotify();

	// 打开法律条款
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void OpenComplianceOnWebView();

	// 打开注销账号页面
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void EnterAccountCancellation();
	
	// 移动端权限
	// 获取声明的权限 可用于展示
	UFUNCTION(BlueprintCallable,Category="OneEngineSDKSubsystem")
	TArray<FOnePermissionInfo> GetPermissions();

	// 检查是否权限已授权
	UFUNCTION(BlueprintCallable,Category="OneEngineSDKSubsystem")
	bool CheckSelfPermission(EOnePermissionType Type);	

	// 申请权限
	DECLARE_DYNAMIC_DELEGATE_TwoParams(FOneRequestPermissionResultDelegate,EOnePermissionType,Type,bool,Granted);
	UFUNCTION(BlueprintCallable,Category="OneEngineSDKSubsystem")
	void RequestPermission(EOnePermissionType Type,FOneRequestPermissionResultDelegate Delegate);
	// Android Type》=7 时需要自己添加 提示语
	void RequestPermission(EOnePermissionType Type,FOneRequestPermissionResultDelegate Delegate,const TArray<FString>& Tips);

	//  关闭剪切板权限
	UFUNCTION(BlueprintCallable,Category="OneEngineSDKSubsystem")
	void CloseClipboardPermission();

	// 打开应用权限设置页
	UFUNCTION(BlueprintCallable,Category="OneEngineSDKSubsystem")
	void OpenApplicationSetting();

	// ========================================================================================
	// === 基础服务模块	=====================================================================
	// ========================================================================================
	
	// 登出回调
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOneLogoutResultDelegate, bool,bSuccess, int, Code, const FString&, Msg);
	UPROPERTY(BlueprintAssignable, Category = "OneEngineSDKSubsystem")
	FOneLogoutResultDelegate LogoutResultDelegate;

	// 切换账号
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void SwitchAccount();

	// 获取用户信息
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	FOneUserInfo GetUserInfo();
	
	// 打开用户中心
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void OpenUserCenter();

	// 获取用户位置信息
	DECLARE_DYNAMIC_DELEGATE_OneParam(FOneUserLocationInfoDelegate, const FOneUserLocationInfo&, LocationInfo);
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void GetUserLocationInfo(FOneUserLocationInfoDelegate LocationInfoDelegate);

	void GetUserLocationInfoLambda(const TFunction<void(const FOneUserLocationInfo& LocationInfo)> &OnFinishedLambda);

	// 获取用户角色信息列表
	DECLARE_DYNAMIC_DELEGATE_FourParams(FOneFetchUserRoleInfoListDelegate,bool,bSuccess, int32, Code, const FString&, Msg, const  TArray<FOneURCRoleInfo>&, RoleList);
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void FetchUserRoleInfoList(FOneFetchUserRoleInfoListDelegate OnFetchUserRoleListDelegate, const FString& ServerId);
	
	void FetchUserRoleInfoListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)> &OnFinishedLambda, const FString& ServerId);

	// 激活码
	// 是否使用 SDK 提供的激活码 toast
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void SetShowDefaultActivationResultToast(bool bShow);

	// 显示激活码窗口
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void DisplayCDKeyDialog(FOneGenericResultDelegate OnCDKeyActivateResult,  const FString& ServerId);

	// 查询是否需要输入激活码 
	DECLARE_DYNAMIC_DELEGATE_FiveParams(FOneQueryActCodeResultDelegate, bool, bSucceed, bool, bNeedActCode, const FString&, ActCodePrompt, int32, Code, const FString&, ErrorMsg);
	UFUNCTION(BlueprintCallable,Category="OneEngineSDKSubsystem")
	void QueryActCode(const FString& ServerId, FOneQueryActCodeResultDelegate OneQueryActCodeResultDelegate);

	void QueryActCodeLambda(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> &OnFinishedLambda);
	
	//验证输入的激活码
	UFUNCTION(BlueprintCallable,Category="OneEngineSDKSubsystem")
	void ExchangeActCode(const FString& ServerId, const FString& ActCode, FOneGenericResultDelegate GenericResultDelegate);

	void ExchangeActCodeLambda(const FString& ServerId, const FString& ActCode, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> &OnFinishedLambda);

	
	// 查询用户有没有激活资格
    DECLARE_DYNAMIC_DELEGATE_FourParams(FOneQueryUserActiveQualificationResultDelegate, bool, bSucceed, int32, Code, const FString&, ErrorMsg, const FOneActiveQualificationInfo&, QualificationInfo);
    UFUNCTION(BlueprintCallable,Category="OneEngineSDKSubsystem")
    void QueryUserActiveQualification(const FString& ServerId, FOneQueryUserActiveQualificationResultDelegate OneQueryUserActiveQualificationResultDelegate);

    void QueryUserActiveQualificationLambda(const FString& ServerId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)> &OnFinishedLambda);
    
    // 激活设备 
    DECLARE_DYNAMIC_DELEGATE_FiveParams(FOneActivateDeviceResultDelegate, bool, bSucceed, bool, bNeedActCode, const FString&, ActCodePrompt, int32, Code, const FString&, ErrorMsg);
    UFUNCTION(BlueprintCallable,Category="OneEngineSDKSubsystem")
    void ActivateDevice(const FString& ServerId, FOneActivateDeviceResultDelegate OneActivateDeviceResultDelegate);

    void ActivateDeviceLambda(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> &OnFinishedLambda);
	// 兑换码
	// 使用兑换码
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void RedeemCouponCode(const FString& CouponCode, const FString& ServerId, const FString& RoleId, const FString& RoleLevel, const FString& VipLevel, const TMap<FString, FString>& ExtraInfo, FOneGenericResultDelegate OnRedeemCouponResult);

	void RedeemCouponCodeLambda(const FString& CouponCode, const FString& ServerId, const FString& RoleId, const FString& RoleLevel, const FString& VipLevel, const TMap<FString, FString>& ExtraInfo,  const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> &OnFinishedLambda);

	// ========================================================================================
	// === 推送模块	===========================================================================
	// ========================================================================================
	
	 // 注册推送
    DECLARE_DYNAMIC_DELEGATE_FourParams(FOneStartUpdatePushDataDelegate, bool, bSucceed, int32, Code, const FString &, ErrorMsg, const FString &, DeviceToken);
    UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
    void StartUpdatePushData(FOneStartUpdatePushDataDelegate Callback);
    
    // 设置点击推送消息后的回调
    DECLARE_DYNAMIC_DELEGATE_OneParam(FOneNotificationDelegate, const FOnePushMessage &, Message);
    
    UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
    void SetupNotificationCallback(FOneNotificationDelegate Callback);
    
    // 获取系统推送开关状态 和 应用推送开关状态
    DECLARE_DYNAMIC_DELEGATE_OneParam(FOnePushStatusDelegate, FOnePushStatus,PushStatus);
    
    UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
    void GetPushStatus(FOnePushStatusDelegate Callback);
    
    // 设置应用推送开关状态
    UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
    void SetProviderPushState(bool bProviderPushState, FOneGenericResultDelegate Callback);

	DECLARE_DYNAMIC_DELEGATE_FourParams(FOnGetPushStateDelegate, bool, bSucceed, int32, Code, const FString &, ErrorMsg, bool, bEnable);

	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void GetProviderPushState(FOnGetPushStateDelegate Delegate);


	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void SetAnalyticsCollectionEnabled(bool bEnable);
	
	
    // 推送设置用户信息
    UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
    void SetPushUserInfo(const FString& ServerId, const FString& RoleId, FOneGenericResultDelegate Callback);
    
    // 推送解绑用户信息
    UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
    void UnSetPushUserInfo(FOneGenericResultDelegate Callback);
    
    // 获取推送类型列表
    DECLARE_DYNAMIC_DELEGATE_FourParams(FOneGetPushTypeInfoListDelegate, bool, bSucceed, int32, Code, const FString &, ErrorMsg, const TArray<FOnePushTypeInfo>&, PushTasks);

    UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
    void GetPushTypeInfoList(FOneGetPushTypeInfoListDelegate Callback);
    
    // 批量设置推送类型开关状态
    UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
    void UpdatePushTypeList(const TArray<FOnePushTypeInfo>& PushTypeList, FOneGenericResultDelegate Callback);

	// 夜间勿扰配置的回调
	DECLARE_DYNAMIC_DELEGATE_FourParams(FOnePushNotDisturbInfoDelegate, bool, bSucceed, int32, Code, const FString &, ErrorMsg, const FOnePushNotDisturbInfo &, DisturbInfo);

	// 获取勿扰模式信息
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void GetPushNotDisturb(FOnePushNotDisturbInfoDelegate Callback);

	// 设置勿扰模式
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void UpdatePushNotDisturb(const FOnePushNotDisturbInfo& DisturbInfo, FOneGenericResultDelegate Callback);

	// ========================================================================================
	// === 分享模块	===========================================================================
	// ========================================================================================

	// 分享 SDK
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void ShareDataToApp(EOneShareAppTarget AppTarget, const EOneShareType Type, const FOneShareData& Data, FOneGenericResultDelegate OnShareResult);
	
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	bool IsInstalledApp(EOneShareAppTarget AppTarget);
	
	// ========================================================================================
	// === 其他模块	===========================================================================
	// ========================================================================================
	
	// 获取登录状态
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	bool IsLoggedIn();
	
	// 日志开关
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void EnableDebugMode(bool Enable);

	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	bool IsDebugMode();

	// 获取当前 SDK 地区
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	EOneEngineSDKRegionType GetRegionType();
	
	// 绑定手机号、Facebook 等
	DECLARE_DYNAMIC_DELEGATE_FiveParams(FOneBindResultDelegate,bool,bSuccess, int32, Code, const FString&, Msg, EOneEngineThirdType, BindType ,const FOneUserInfo&, UserInfo);
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void Bind(FOneBindResultDelegate BindDelegate,EOneEngineThirdType BindType);

	// 用户认证目前只有老虎 用户登录后调用 (PC 端接口)
	// authResult -1 未知，0 未认证，1 已认证
	DECLARE_DYNAMIC_DELEGATE_FourParams(FOneUserAuthenticationResultDelegate, bool, bSucceed,  int32, AuthResult, bool, bHasNetError, const FString&, ErrorMsg);
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void UserAuthentication(FOneUserAuthenticationResultDelegate OnUserAuthenticationResultDelegate);

	// 打开 AIHelp 客服
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void OpenAIHelp(EOneAIHelpType Type, const FString& RoleId, const FString& ServerId, const FString& RoleName);

	// 打开自定义客服
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void OpenCustomerService(const FString& RoleID, const FString& RoleName, const FString& ServerID);

	// 文本翻译
	DECLARE_DYNAMIC_DELEGATE_ThreeParams(FOneTranslateResultDelegate, bool, bSucceed, const FString&, Result, const FString&, ErrorMsg);
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void Translate(const FString& Text, FOneTranslateResultDelegate Callback);
	
	void TranslateLambda(const FString& Text,const TFunction<void(bool bSucceed, const FString& Result,const FString& ErrorMsg)> &OnFinishedLambda);
	
	// 获取当前设置语言 (PC 端全球接口)
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	FString GetCurrentLanguage();

	// 设置语言
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void SetLanguage(const FString& Code);

	// 获取本地语言列表
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	TArray<FString> GetSupportedLanguageCodeList();
	
	// 设置屏幕方向
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void SetScreenOrientation(EOneScreenOrientation Orientation);

	//是否检测 BundleId(iOS 专有接口)
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void ShouldVerifyBundleId(bool bShouldVerify);
	
	// 渠道 SDK 万能方法
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	bool IsCommonFunctionSupported(const FString& FuncName);

	DECLARE_DYNAMIC_DELEGATE_ThreeParams(FOneCommonFunctionDelegate, const FString&, FunctionName, int, Result, const FString&, Msg);
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void CallCommonFunction(const FString& FuncName, const FString& Params, FOneCommonFunctionDelegate CommonFunctionDelegate);

	// Android 独有 游戏在此回调里弹自己的退出弹窗
	DECLARE_DYNAMIC_DELEGATE(FOnChannelNotHavingExitViewDelegate);
	FOnChannelNotHavingExitViewDelegate OnChannelNotHavingExitViewDelegate;
	
	//当走渠道的弹窗退出时 回调
	DECLARE_DYNAMIC_DELEGATE(FOnExitDelegate);
	FOnExitDelegate OnExitDelegate;
	
	//Android 杀死进程
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void KillProcess();

	// ACE client packet 数据回调
	DECLARE_DYNAMIC_DELEGATE_OneParam(FOneOnGetClientPacket, const TArray<uint8>&, data);
	// ACE 登录 accountTypeCommandId default 601, worldId default 0
	
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	bool ACELogin(const FString& accountId, int accountTypeCommandId, int worldId, FOneOnGetClientPacket delegate);

	// ACE client packet receive
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	bool ACEClientPacketReceive(const TArray<uint8>& data);

	// ACE 登出
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	bool ACELogout();

	/// 大陆 获取 Ip 的位置信息
	DECLARE_DYNAMIC_DELEGATE_FourParams(FOnGetIpInfoResultDelegate,bool,bSuccess,FUserIpInfo,IpInfo, int32, Code, const FString&, Msg);
	
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void GetIpInfo(const FString& Ip,FOnGetIpInfoResultDelegate Delegate);

	/// 全球独有的 评价接口和 修改屏幕亮度接口
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	float GetScreenBrightness();

	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void SetScreenBrightness(float BrightnessValue);
	
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void RecoverScreenBrightness();
	///保持屏幕常亮
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void SwitchScreenPermanentBrightnessState(bool bIsTurnOn);
	

	///评价接口
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void InAppRequestStoreReview(FOneGenericResultDelegate Delegate);

	/// AppLink Ios 有这个参数
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void RequestStoreReview(FString AppLink);

	/// 获取渲染器配置文件地址
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	FString GetRenderConfigFilePath();
	
	// 安全锁相关接口
	DECLARE_DYNAMIC_DELEGATE_FiveParams(FOneUnlockSafeLockResultDelegate, EOneUnlockSafeLockResult, UnlockResult, const FString&, UnlockToken, int32, Code, const FString&, ErrorMsg, EOneUnlockSafeLockType, UnlockType);
	
	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void UnlockSafeLockUsingPushNotification(const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate);

	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void UnlockSafeLockUsingDynamicCode(const FString& DynamicCode, const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate);

	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void StopUnlockSafeLockUsingPushNotification();

	// 全球独有的打开 Naver 论坛
	DECLARE_DYNAMIC_DELEGATE(FOneLoadDelegate);
	DECLARE_DYNAMIC_DELEGATE(FOneUnloadDelegate);
	DECLARE_DYNAMIC_DELEGATE_OneParam(FOneInGameMenuDelegate, const FString&, InputString);

	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void OpenCommunityByGame(EOneNaverGameType Type, const FString& Pid, bool Scheduled, FOneLoadDelegate LoadCallback, FOneUnloadDelegate UnloadCallback, FOneInGameMenuDelegate InGameMenuCallback);

	UFUNCTION(BlueprintCallable, Category = "OneEngineSDKSubsystem")
	void TerminateCommunity();

	// ============== PS 平台 特有属性 ============== 
	// 在 PS 平台上，会有一些额外的属性，获取档位信息时，需要配置
	// 如果共用 PS4 的后台商店的话，PS5 为 1，PS4 为 0
	FString PSStoreServerLabel;

	// PS 平台 商品分类标签，默认为空
	FString PSStoreCategoryLabel;
	
private:
#if PLATFORM_IOS
	FOneStartUpdatePushDataDelegate StartUpdatePushDataDelegate;
	FOneNotificationDelegate NotificationDelegate;
#endif
};



