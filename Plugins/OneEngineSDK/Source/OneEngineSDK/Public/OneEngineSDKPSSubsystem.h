// Fill out your copyright notice in the Description page of Project Settings.

#pragma once
#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "OneEngineSDKHelper.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "OneEngineSDKPSSubsystem.generated.h"
DECLARE_LOG_CATEGORY_EXTERN(LogOneEngineSDK_PS, Log, All);

UENUM(BlueprintType)
enum class EOnePSStoreIconPos : uint8
{
	Center = 0,
	Left,
	Right,
};

UENUM(BlueprintType)
enum class EOnePsnAccountState : uint8
{
	Unknown = 0,
	SignedOut,
	SignedIn,
};

USTRUCT(BlueprintType)
struct FOnePSUserProfile
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly, Category="OneEngineSDKPSSubsystem")
	FString AccountId;

	UPROPERTY(BlueprintReadOnly, Category="OneEngineSDKPSSubsystem")
	FString OnlineId;

	UPROPERTY(BlueprintReadOnly, Category="OneEngineSDKPSSubsystem")
	FString AvatarUrl;

	UPROPERTY(BlueprintReadOnly, Category="OneEngineSDKPSSubsystem")
	FString AboutMe;

	UPROPERTY(BlueprintReadOnly, Category="OneEngineSDKPSSubsystem")
	bool bIsOfficiallyVerified {false};

	UPROPERTY(BlueprintReadOnly, Category="OneEngineSDKPSSubsystem")
	int32 SDKuid {0};

	UPROPERTY(BlueprintReadOnly, Category="OneEngineSDKPSSubsystem")
	FString ServerId;

	UPROPERTY(BlueprintReadOnly, Category="OneEngineSDKPSSubsystem")
	FString RoleId;

	UPROPERTY(BlueprintReadOnly, Category="OneEngineSDKPSSubsystem")
	FString RoleName;
};

USTRUCT(BlueprintType)
struct FOnePSUserProfileResponse
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly, Category="OneEngineSDKPSSubsystem")
	TArray<FOnePSUserProfile> Profiles;

	UPROPERTY(BlueprintReadOnly, Category="OneEngineSDKPSSubsystem")
	int32 Total{0};

	UPROPERTY(BlueprintReadOnly, Category="OneEngineSDKPSSubsystem")
	int32 NextOffset{-1};

	UPROPERTY(BlueprintReadOnly, Category="OneEngineSDKPSSubsystem")
	int32 PreviousOffset{0};
};

USTRUCT(BlueprintType)
struct FOnePSPurchaseForm
{
	GENERATED_BODY()

	/**
	 * @brief 游戏订单Id，应由游戏服务器生成
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString GameOrderId;

	/**
	 * @brief 当前游戏角色信息
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString GameRoleId;

	/**
	 * @brief 当前游戏角色名称
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString GameRoleName;

	/**
	 * @brief 当前游戏角色所在服务器ID
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString GameServerId;

	/**
	 * @brief 产品ID，该值在PS平台上，应使用 FOnePSProduct.Label 字段属性
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString ProductId;

	/**
	 * @brief 产品名称，该值在PS平台上，应使用 FOnePSProduct.DisplayName 字段属性
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString ProductName;

	/**
	 * @brief 订单总金额
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString OrderAmount;

	/**
	 * @brief 透传给游戏服务端的信息
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString GameExtraInfo;

	/**
	 * @brief 透传给游戏服务端的信息
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString GameDescription;

	/**
	 * @brief PSN服务器标签, 目前都是默认共享PS4商店的做法，PS4 应该传 0，PS5 为 1
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int PSNServerLabel {0};
};

USTRUCT(BlueprintType)
struct FOnePSProductMediaImage
{
	GENERATED_BODY()

	/**
	 * @brief 如,JPEG,PNG,GIF,WEBP
	 */
	UPROPERTY(BlueprintReadOnly)
	FString Format;

	/**
	 * @brief 在PSN使用类型
	 */
	UPROPERTY(BlueprintReadOnly)
	FString Type;

	/**
	 * @brief 静态图片URL地址
	 */
	UPROPERTY(BlueprintReadOnly)
	FString Url;
};

USTRUCT(BlueprintType)
struct FOnePSProductMedia
{
	GENERATED_BODY()
	UPROPERTY(BlueprintReadOnly)
	TArray<FOnePSProductMediaImage> Images;
};

USTRUCT(BlueprintType)
struct FOnePSProductSku
{
	GENERATED_BODY()

	/**
	 * @brief id
	 */
	UPROPERTY(BlueprintReadOnly)
	FString ID;

	/**
	 * @brief 名称
	 */
	UPROPERTY(BlueprintReadOnly)
	FString Name;

	/**
	 * @brief 标签
	 */
	UPROPERTY(BlueprintReadOnly)
	FString Label;

	/**
	 * @brief 结束日期
	 */
	UPROPERTY(BlueprintReadOnly)
	FString EndDate;

	/**
	 * @brief 类型
	 */
	UPROPERTY(BlueprintReadOnly)
	FString Type;

	/**
	 * @brief  价格
	 */
	UPROPERTY(BlueprintReadOnly)
	int Price {0};

	/**
	 * @brief 表示此产品的显示价格的字符串。如果存在任何折扣，则显示的价格包含折扣价格。
	 */
	UPROPERTY(BlueprintReadOnly)
	FString DisplayPrice;

	/**
	 * @brief 表示此产品的原始价格。当存在任何折扣，该字段有值
	 */
	UPROPERTY(BlueprintReadOnly)
	int OriginalPrice {0};

	/**
	 * @brief 表示原始价格的字符串。始终与删除线搭配使用。
	 */
	UPROPERTY(BlueprintReadOnly)
	FString DisplayOriginalPrice;

	/**
	 * @brief skus 单位数量
	 */
	UPROPERTY(BlueprintReadOnly)
	int UseLimit {0};

	/**
	 * @brief Plus 会员折扣价。此价格是一个整数，不表示货币代码或符号。
	 */
	UPROPERTY(BlueprintReadOnly)
	int PlusUpsellPrice {0};

	/**
	 * @brief 展示 Plus 会员折扣价的字符串。。始终与 PlayStation®Plus 图标搭配使用。
	 */
	UPROPERTY(BlueprintReadOnly)
	FString DisplayPlusUpsellPrice;

	/**
	 * @brief  一个布尔值，表示 price 字段是否为 Plus 会员价
	 */
	UPROPERTY(BlueprintReadOnly)
	bool IsPlusPrice {0};

	/**
	 * @brief 指示 Sku 购买状态的字符串值。“NONE”: 未购买。“BLUE_BAG”: 已购买，可重新购买。“RED_BAG”: 已购买，不可重新购买
	 */
	UPROPERTY(BlueprintReadOnly)
	FString AnnotationName;
};

USTRUCT(BlueprintType)
struct FOnePSProduct
{
	GENERATED_BODY()

	/**
	 * @brief ID
	 */
	UPROPERTY(BlueprintReadOnly)
	FString ID;

	/**
	 * @brief 标签，购买产品需要传入该字段
	 */
	UPROPERTY(BlueprintReadOnly)
	FString Label;

	/**
	 * @brief 允许用户查看游戏中商店中商品的最低年龄。如果用户的年龄小于此值，则产品不包含在响应主体中。值为0表示该项没有年龄要求。
	 */
	UPROPERTY(BlueprintReadOnly)
	int AgeLimit {0};

	/**
	 * @brief 本地化名称。
	 */
	UPROPERTY(BlueprintReadOnly)
	FString DisplayName;

	/**
	 * @brief 详情描述。
	 */
	UPROPERTY(BlueprintReadOnly)
	FString Description;

	/**
	 * @brief 类型, 可能的值，category，product。
	 * @note 只有 Type 等于 product ，才允许购买
	 */
	UPROPERTY(BlueprintReadOnly)
	FString Type;

	/**
	 * @brief 销售单位信息，用于展示商品价格信息
	 */
	UPROPERTY(BlueprintReadOnly)
	TArray<FOnePSProductSku> Skus;

	/**
	 * @brief 媒体信息
	 */
	UPROPERTY(BlueprintReadOnly)
	FOnePSProductMedia Media;
};

USTRUCT(BlueprintType)
struct FOnePSProductCategory
{
	GENERATED_BODY()

	/**
	 * @brief 非 0 表示异常
	 */
	UPROPERTY(BlueprintReadOnly)
	int Code {0};

	/**
	 * @brief PSN 返回的原始数据，如该结构体不满住需求，可使用 RawData 自定义处理
	 */
	UPROPERTY(BlueprintReadOnly)
	FString RawData;

	/**
	 * @brief ID
	 */
	UPROPERTY(BlueprintReadOnly)
	FString ID;

	/**
	 * @brief 标签
	 */
	UPROPERTY(BlueprintReadOnly)
	FString Label;

	/**
	 * @brief 允许用户查看游戏中商店中商品的最低年龄。如果用户的年龄小于此值，则产品不包含在响应主体中。值为0表示该项没有年龄要求。
	 */
	UPROPERTY(BlueprintReadOnly)
	int AgeLimit {0};

	/**
	 * @brief 本地化显示名称
	 */
	UPROPERTY(BlueprintReadOnly)
	FString DisplayName;

	/**
	 * @brief 类型, 可能的值，category，product
	 */
	UPROPERTY(BlueprintReadOnly)
	FString Type;

	/**
	 * @brief 子容器、产品（接口默认返回父容器，实际的产品请从该字段获取）
	 */
	UPROPERTY(BlueprintReadOnly)
	TArray<FOnePSProduct> Children;

	/**
	 * @brief 媒体信息
	 */
	UPROPERTY(BlueprintReadOnly)
	FOnePSProductMedia Media;

	/**
	 * @brief 子容器总数
	 */
	UPROPERTY(BlueprintReadOnly)
	int TotalItemCount {0};
};

/// 主要用于 脏话过滤器
UCLASS()
class UOnePSLocaleEnum : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintPure)
	static FString Japanese() { return TEXT("ja-JP"); }

	UFUNCTION(BlueprintPure)
	static FString English_US() { return TEXT("en-US"); }

	UFUNCTION(BlueprintPure)
	static FString French() { return TEXT("fr-FR"); }

	UFUNCTION(BlueprintPure)
	static FString Spanish() { return TEXT("es-ES"); }

	UFUNCTION(BlueprintPure)
	static FString German() { return TEXT("de-DE"); }

	UFUNCTION(BlueprintPure)
	static FString Italian() { return TEXT("it-IT"); }

	UFUNCTION(BlueprintPure)
	static FString Dutch() { return TEXT("nl-NL"); }

	UFUNCTION(BlueprintPure)
	static FString Portuguese_PT() { return TEXT("pt-PT"); }

	UFUNCTION(BlueprintPure)
	static FString Russian() { return TEXT("ru-RU"); }

	UFUNCTION(BlueprintPure)
	static FString Korean() { return TEXT("ko-KR"); }

	UFUNCTION(BlueprintPure)
	static FString Chinese_T() { return TEXT("zh-Hant"); }

	UFUNCTION(BlueprintPure)
	static FString Chinese_S() { return TEXT("zh-Hans"); }

	UFUNCTION(BlueprintPure)
	static FString Finnish() { return TEXT("fi-FI"); }

	UFUNCTION(BlueprintPure)
	static FString Swedish() { return TEXT("sv-SE"); }

	UFUNCTION(BlueprintPure)
	static FString Danish() { return TEXT("da-DK"); }

	UFUNCTION(BlueprintPure)
	static FString Norwegian() { return TEXT("no-NO"); }

	UFUNCTION(BlueprintPure)
	static FString Polish() { return TEXT("pl-PL"); }

	UFUNCTION(BlueprintPure)
	static FString Portuguese_BR() { return TEXT("pt-BR"); }

	UFUNCTION(BlueprintPure)
	static FString English_GB() { return TEXT("en-GB"); }

	UFUNCTION(BlueprintPure)
	static FString Turkish() { return TEXT("tr-TR"); }

	UFUNCTION(BlueprintPure)
	static FString Spanish_LA() { return TEXT("es-419"); }

	UFUNCTION(BlueprintPure)
	static FString Arabic() { return TEXT("ar-AE"); }

	UFUNCTION(BlueprintPure)
	static FString French_CA() { return TEXT("fr-CA"); }

	UFUNCTION(BlueprintPure)
	static FString Czech() { return TEXT("cs-CZ"); }

	UFUNCTION(BlueprintPure)
	static FString Hungarian() { return TEXT("hu-HU"); }

	UFUNCTION(BlueprintPure)
	static FString Greek() { return TEXT("el-GR"); }

	UFUNCTION(BlueprintPure)
	static FString Romanian() { return TEXT("ro-RO"); }

	UFUNCTION(BlueprintPure)
	static FString Thai() { return TEXT("th-TH"); }

	UFUNCTION(BlueprintPure)
	static FString Vietnamese() { return TEXT("vi-VN"); }

	UFUNCTION(BlueprintPure)
	static FString Indonesian() { return TEXT("id-ID"); }
};

UCLASS()
class ONEENGINESDK_API UOneEngineSDKPSSubsystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:
	DECLARE_DYNAMIC_DELEGATE_OneParam(FOnGetFriendsResultDelegate, const FOnePSUserProfileResponse &, ProfileList);

	DECLARE_DYNAMIC_DELEGATE_OneParam(FOnGetBlockingUsersResultDelegate, const FOnePSUserProfileResponse &, ProfileList);

	DECLARE_DYNAMIC_DELEGATE_OneParam(FOnRestrictionStatusResultDelegate, int, Result);

	DECLARE_DYNAMIC_DELEGATE_TwoParams(FOnCheckPremiumResultDelegate, int, Code, bool, bIsPremium);

	DECLARE_DYNAMIC_DELEGATE_TwoParams(FOnFilterProfanityResultDelegate, int, Code, FString, Text);

	DECLARE_DYNAMIC_DELEGATE_OneParam(FOnOpenDialogResultDelegate, int, Result);

	DECLARE_DYNAMIC_DELEGATE_FourParams(FOnGetProductInfoListPSDelegate, bool, bSucceed, const FOnePSProductCategory&, Category, int32, Code, const FString&, Msg);


	virtual void Initialize(FSubsystemCollectionBase& Collection) override;

	virtual void Deinitialize() override;

	/**
	 * @brief 获取PSN账号状态
	 * @return EOnePsnAccountState PSN账号状态
	 */
	UFUNCTION(BlueprintCallable)
	EOnePsnAccountState GetAccountState() const;

	/**
	 * @brief 获取好友列表
	 * @param Offset 偏移量
	 * @param Limit 限制数量
	 * @param OnGetFriendsResult 获取好友列表回调
	 */
	UFUNCTION(BlueprintCallable)
	void GetFriends(int32 Offset, int32 Limit, FOnGetFriendsResultDelegate OnGetFriendsResult);

	/**
	 * @brief 获取屏蔽列表
	 * @param Offset 偏移量
	 * @param Limit 限制数量
	 * @param OnGetBlockingUsersResult 获取屏蔽列表回调
	 */
	UFUNCTION(BlueprintCallable)
	void GetBlockingUsers(int32 Offset, int32 Limit, FOnGetBlockingUsersResultDelegate OnGetBlockingUsersResult);

	/**
	* @brief 获取当前用户的AccountId
	* @return FString AccountId
	*/
	UFUNCTION(BlueprintCallable)
	FString GetAccountId() const;

	/**
	* @brief 获取当前用户的OnlineId
	* @return  FString OnlineId
	*/
	UFUNCTION(BlueprintCallable)
	FString GetOnlineId() const;

	/**
	 * @brief 获取 CountryCode
	 */
	UFUNCTION(BlueprintCallable)
	FString GetCountryCode();

	/**
	 * @brief 根据国家代码获取国家所属地区
	 * @param CountryCode 国家代码
	 * @return 国家地区
	 */
	UFUNCTION(BlueprintCallable)
	FString GetCountryRegion(const FString& CountryCode);

	/**
	 * @brief 显示商店图标
	 * @param Pos 显示位置
	 * @return 0 成功，其他失败
	 */
	UFUNCTION(BlueprintCallable)
	int32 ShowStoreIcon(EOnePSStoreIconPos Pos);

	/**
	 * @brief 隐藏
	 */
	UFUNCTION(BlueprintCallable)
	int32 HideStoreIcon();


	void Pay(const FOnePSPurchaseForm& PurchaseForm, TFunction<void(int Code, bool HasPurchased, const FString& Tips)> Callback);

	// 同步订单
	void SyncOrder(const FOneRoleInfo& RoleInfo, FString OrderId = "");

	/**
	 * @brief 获取当前用户的通信限制状态
	 * @param OnRestrictionStatusResult 获取通信限制状态回调，Result 可能的值 -1 表示检查失败， 0 表示未检查， 1 表示未限制， 2 表示受限制
	 */
	UFUNCTION(BlueprintCallable)
	void GetCommunicationRestrictionStatus(FOnRestrictionStatusResultDelegate OnRestrictionStatusResult);

	/**
	 * @brief 检查当前用户是否为会员
	 * @param OnCheckPremiumResult 检查会员结果回调，code 非0表示检查异常（有可能是网络原因导致，需要进行重试），bIsPremium 为 true 表示是会员，false 表示不是会员
	 * @warning 需要在进入Premium功能之前调用。网络实时对战就是Premium功能。可早不可晚。因此，对于网络游戏，可以在启动的时候就调用.
	 */
	UFUNCTION(BlueprintCallable)
	void CheckPremium(FOnCheckPremiumResultDelegate OnCheckPremiumResult);

	/**
	 * @brief 开始定期调用通知会员特性，在调用前需要先调用 CheckPremium 检查会员权限。
	 * 需要在发生实际多人游玩的时候调用，不包括大厅等事前准备阶段。
	 * 因此，一般是在进入游戏，玩家可以操作的同时，开始调用。
	 * @param Interval `定期调用的时间间隔,单位为秒，默认为 1 秒（建议）
	 * @param Mark 会员特性标记， 0 表示不指定任何特性， 1 表示满足跨平台播放条件， 2 表示满足引擎内观看条件， 3 表示满足 1 和 2 的条件
	 * @return 0 表示执行成功，其他表示执行失败
	 */
	UFUNCTION(BlueprintCallable)
	int StartNotifyPremiumFeature(int Interval, int Mark);

	/**
	 * @brief 然后在玩家结束本场游戏的时候，停止调用通知会员特性.注意不可以在画面迁移回大厅才停止调用。而是应该在对局结束的同时停止调用
	 * @return 0 表示执行成功，其他表示执行失败
	 */
	UFUNCTION(BlueprintCallable)
	int StopNotifyPremiumFeature();

	/**
	 * @brief 脏话过滤器，审查用户内容字符串中的亵渎行为。该字符串会在响应中返回，并且检测到的任何不雅内容都将替换为“*”字符，如果在文本中未检测到脏话，则原封不动地返回。
	 * @param Text 要过滤的文本。此文本的最大大小为 4KB。
	 * @param Language 例如，“en-GB”,具体支持的locale值 请参考 @see UOnePSLocaleEnum
	 * @param OnFilterProfanityResult 回调
	 * @note 作为最佳实践，忽略调用此 API 时遇到的错误。如果在调用此 API 时发生错误，应用程序应继续前进，就好像未检测到亵渎行为一样。
	 * @warning 调用频率限制 300/minute ，超过限制，SIE将限制客户端
	 */
	UFUNCTION(BlueprintCallable)
	void FilterProfanity(const FString& Text, const FString& Language, FOnFilterProfanityResultDelegate OnFilterProfanityResult);

	/**
	 * @brief  脏话过滤器，审查用户内容字符串中的亵渎行为。 同步接口，会阻塞当前线程
	 * @param Text 要过滤的文本。此文本的最大大小为 4KB。
	 * @param Language 例如，“en-GB”,具体支持的locale值 请参考 @see UOnePSLocaleEnum
	 * @param OutResult 该字符串会在响应中返回，并且检测到的任何不雅内容都将替换为“*”字符，如果在文本中未检测到脏话，则原封不动地返回。
	 * @return 0 表示执行成功，其他表示执行失败
	 */
	UFUNCTION(BlueprintCallable)
	int FilterProfanitySync(const FString& Text, const FString& Language, FString& OutResult);

	/**
	 * @brief 打开商店对话框,鼓励用户成为Plus会员,仅支持PS5平台
	 * @param OnOpenDialogResult 打开商店对话框回调，Result 为 0 表示成功，其他表示失败
	 */
	UFUNCTION(BlueprintCallable)
	void OpenCommerceDialogPremiumMode(FOnOpenDialogResultDelegate OnOpenDialogResult);

	/**
	 * @brief  获取产品信息列表
	 * @param ServiceLabel 如果共用PS4的后台商店的话，PS5 为 1，PS4 为 0
	 * @param CategoryLabel 默认为空字符串
	 * @param Callback  回调
	 */
	UFUNCTION(BlueprintCallable)
	void GetProductInfoListPS(int32 ServiceLabel, FString CategoryLabel, const FOnGetProductInfoListPSDelegate& Callback);

	// bool, bSucceed, const FOnePSProductCategory&, Category, int32, Code, const FString&, Msg


	void GetProductInfoListPSLambda(int32 ServiceLabel, FString CategoryLabel, TFunction<void(bool, const FOnePSProductCategory&, int32, const FString&)> Callback);


	DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FWidgetVisibilityDelegate, int, WidgetType, bool, bVisible);

	/**
	 * @brief Event triggered when a widget's visibility changes.
	 *
	 * @param WidgetType  The type of the widget whose visibility changed.
	 * 1 for WidgetUserPrivacyAgreement,
	 * 2 for WidgetUserLogin,
	 * 3 for WidgetRealNameAuthentication,
	 * 4 for WidgetUserCenter.
	 * @param bVisible The new visibility state of the widget.
	 *
	 * @see FWidgetVisibilityDelegate
	 */
	FWidgetVisibilityDelegate OnWidgetVisibilityChanged;


	/**
	 * @brief Opens a web view with the specified URL.
	 *
	 * This function opens a web view using the provided URL and executes the given completion callback
	 * with the result code once the web view is closed.
	 *
	 * @param Url The URL to open in the web view.
	 * @param Completion The callback function to execute with the result code when the web view is closed.
	 */
	void OpenWebView(const FString& Url, TFunction<void(int Code)> Completion);


	UFUNCTION(BlueprintCallable)
	void SetFontPath(const FString& Path);

private:
	static void ParsePsUserProfileResponse(const FString& Json, FOnePSUserProfileResponse& Out);
};
