#include "OneEngineSDKHelper.h"
#include "OneEngineSDKSubsystem.h"
#include "Async/Async.h"
#include "Engine/Engine.h"
void FOneEngineSDKHelper::OnLoginResultDelegate(bool bSucceed, int Code, const FString& Msg,const FString& UserId)
{
	AsyncTask(ENamedThreads::GameThread, [bSucceed, Code, Msg, UserId]()
	{
		UOneEngineSDKSubsystem* subsystem = GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
        FOneUserInfo UserInfo = subsystem->GetUserInfo();
		subsystem->LoginResultDelegate.Broadcast(bSucceed,Code,Msg,UserInfo);
	});
}

void FOneEngineSDKHelper::OnLogoutResultDelegate(bool bSucceed, int Code, const FString& Msg)
{
	AsyncTask(ENamedThreads::GameThread, [bSucceed, Code, Msg]()
	{
		UOneEngineSDKSubsystem* subsystem = GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
		subsystem->LogoutResultDelegate.Broadcast(bSucceed,Code,Msg);
	});
}

void FOneEngineSDKHelper::OnPaymetResultDelegate(bool bSucceed, int Code, const FString& Msg,const FString& OrderId)
{
	AsyncTask(ENamedThreads::GameThread, [bSucceed, Code, Msg,OrderId]()
	{
		UOneEngineSDKSubsystem* subsystem = GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
		subsystem->PayResultDelegate.Broadcast(bSucceed,Code,Msg,OrderId);
	});
}

void FOneEngineSDKHelper::OnAntiAddictionTimeoutResultDelegate(bool bForceKick, const FOneAntiAddictionInfo& Info)
{
	AsyncTask(ENamedThreads::GameThread, [bForceKick, Info]()
	{
		UOneEngineSDKSubsystem* subsystem = GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
		subsystem->OnAntiAddictionTimeoutDelegate.Broadcast(bForceKick,Info);
	});
}
bool FOneEngineSDKHelper::OnGameShowExitDialog()
{
	UOneEngineSDKSubsystem* subsystem = GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
	if (!subsystem->OnChannelNotHavingExitViewDelegate.IsBound()) {
		return false;
	}
	AsyncTask(ENamedThreads::GameThread,[]()
		{
			UOneEngineSDKSubsystem* subsystem = GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
			subsystem->OnChannelNotHavingExitViewDelegate.Execute();
		}
	);
	return true;
}

void FOneEngineSDKHelper::OnExit()
{
	AsyncTask(ENamedThreads::GameThread,[]()
   {
		UOneEngineSDKSubsystem* subsystem = GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
		if (!subsystem->OnExitDelegate.IsBound()) {
			subsystem->KillProcess();
		}else
		{
			subsystem->OnExitDelegate.ExecuteIfBound();
		}
	});
}



