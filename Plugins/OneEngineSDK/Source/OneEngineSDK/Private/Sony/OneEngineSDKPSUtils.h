﻿#pragma once
#if ENGINE_SUPPORT_SONY

DECLARE_LOG_CATEGORY_EXTERN(LogOneSDK, Log, All);

#define ONE_CURRENT_CLASS_FUNCTION_LOCAL(FUNCTION) (FString(FUNCTION))
#define ONE_CURRENT_LINE_LOCAL __LINE__
#define ONE_CURRENT_CLASS_FUNCTION_LINE_LOCAL(FUNCTION) (ONE_CURRENT_CLASS_FUNCTION_LOCAL(FUNCTION) + TEXT(":") + FString::FromInt(ONE_CURRENT_LINE_LOCAL))

#define ONE_LOG_PRINTF(FormatString, ...) UE_LOG(LogOneSDK, Log, TEXT("[%s] %s"), *ONE_CURRENT_CLASS_FUNCTION_LINE_LOCAL(__FUNCTION__), *FString::Printf(TEXT(FormatString), ##__VA_ARGS__) )
#define ONE_LOG_WARNING_PRINTF(FormatString, ...) UE_LOG(LogOneSDK, Warning, TEXT("[%s] %s"), *ONE_CURRENT_CLASS_FUNCTION_LINE_LOCAL(__FUNCTION__), *FString::Printf(TEXT(FormatString), ##__VA_ARGS__) )
#define ONE_LOG_ERROR_PRINTF(FormatString, ...) UE_LOG(LogOneSDK, Error, TEXT("[%s] %s"), *ONE_CURRENT_CLASS_FUNCTION_LINE_LOCAL(__FUNCTION__), *FString::Printf(TEXT(FormatString), ##__VA_ARGS__) )

class FONEEngineSDKPSUtils
{
public:
	static void ExecInGameThread(TFunction<void()> Func);
	static void AsyncExecInBackgroundThread(TFunction<void()> Func);
	static UWorld* GetWorld();

	// 显示一个 dialog，ok/cancel
	static void ShowOKCancelDialog(const FString& Message, TFunction<void(bool bOk)> Callback);
};

#endif
