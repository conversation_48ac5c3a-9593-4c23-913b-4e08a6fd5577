﻿#if ENGINE_SUPPORT_SONY

#include "OneEngineSDKPSAdapter.h"

#include "OneEngineSDKPSMainLand.h"
#include "OneEngineSDKPSOversea.h"
#include "OneEngineSDKPSUtils.h"
#if PLATFORM_PS4 || PLATFORM_PS5
#include "libpfa.h"
#else
// 纯粹就是为了智能提示
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/libpfa.h"
#endif
namespace
{
	class FONEEngineSDKPSAdapterImpl : public IONEEngineSDKPSInterface
	{
	public:
		virtual void Login() override {}

		virtual void Logout(int Type) override {}

		virtual FOneUserInfo GetUserInfo() override { return FOneUserInfo(); }

		virtual FString GetChannelId() override { return FString(); }

		virtual bool CheckLoggedInStatus() override { return false; }

		virtual FString GetChannelMediaId() override { return FString(); }

		virtual int32 GetPlatformOS() override { return 0; }

		virtual FString GetChannelPlatform() override { return FString(); }

		virtual void RequestAntiAddictionInfo(TFunction<void(const FOneAntiAddictionInfo& Info)> Callback) override {}

		virtual void StartAntiAddictionMonitoring(
			const FString& ServerId, const FString& RoleId, TFunction<void(const FOneAntiAddictionInfo& Info)> Callback) override
		{
		}

		virtual void StopAntiAddictionMonitoring() override {}

		virtual int InitializeSDK(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate) override { return 0; }

		virtual void GetUserLocation(TFunction<void(const FOneUserLocationInfo& LocationInfo)> OnFinishedLambda) override {}

		virtual void SetDebugMode(bool bDebugMode) override {}

		virtual bool GetDebugMode() override { return false; }

		virtual void OpenUserCenter() override {}

		virtual int OpenComplianceOnWebView() override { return 0; }

		virtual void SetLanguage(const FString& Language) override {}

		virtual FString GetCurrentLanguage() override { return FString(); }

		virtual void Track(const FString& EventName, const TMap<FString, FString>& EventParams, FString TaskId) override {}

		virtual int GetFriendList(
			int Offset, int Count, TFunction<void(bool bSuccess, const FString& Result, int ErrCode, const FString& ErrInfo)>) override
		{
			return 0;
		}

		virtual int GetBlockList(
			int Offset, int Count, TFunction<void(bool bSuccess, const FString& Result, int ErrCode, const FString& ErrInfo)>) override
		{
			return 0;
		}

		virtual int GetProductInfoList(
			int32 ServiceLabel, FString CategoryLabel,
			TFunction<void(bool bSuccess, const FString& Result, int ErrCode, const FString& ErrInfo)> CallBack) override
		{
			return 0;
		}

		virtual void UseRedeemCode(
			const FString& ExchangeCode, const FOneRoleInfo& RoleInfo, const TMap<FString, FString>& ExtraInfo,
			TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> CallBack) override
		{
		}

		virtual void GetRoleList(
			const FString& ServerId,
			TFunction<void(bool bSuccess, int32 Code, const FString& Msg, const TArray<FOneURCRoleInfo>& RoleList)> CallBack) override
		{
		}

		virtual void ShowLoading() override {}

		virtual void HideLoading() override {}

		virtual bool ExaminStatus() override { return false; }
	};
} // namespace

FONEEngineSDKPSAdapter::FONEEngineSDKPSAdapter() : PImp(nullptr)
{
}

FONEEngineSDKPSAdapter::~FONEEngineSDKPSAdapter()
{
}

FONEEngineSDKPSAdapter& FONEEngineSDKPSAdapter::Get()
{
	static FONEEngineSDKPSAdapter Instance;
	return Instance;
}

TSharedPtr<IONEEngineSDKPSInterface> FONEEngineSDKPSAdapter::GetPimp()
{
	if (PImp.IsValid())
	{
		return PImp;
	}
	ONE_LOG_ERROR_PRINTF("!!! Please Initialize SDK First !!!");
	// 防止闪退
	static TSharedPtr<IONEEngineSDKPSInterface> TmpPimp = MakeShareable(new FONEEngineSDKPSAdapterImpl());
	return TmpPimp;
}

EOneEngineSDKRegionType FONEEngineSDKPSAdapter::GetRegionType()
{
	switch (SDKRegionType)
	{
	case 0:
		return EOneEngineSDKRegionType::Mainland;
	case 1:
		return EOneEngineSDKRegionType::Oversea;
	default:
		return EOneEngineSDKRegionType::Mainland;
	}
}

void FONEEngineSDKPSAdapter::SetDebugMode(bool bDebug)
{
	this->bDebugMode = bDebug;
	if (PImp.IsValid())
	{
		PImp->SetDebugMode(bDebug);
	}
}

bool FONEEngineSDKPSAdapter::GetDebugMode() const
{
	return bDebugMode;
}

void FONEEngineSDKPSAdapter::MakePimp()
{
	if (PImp.IsValid())
	{
		return;
	}
	switch (GetRegionType())
	{
	case EOneEngineSDKRegionType::Mainland:
		ONE_LOG_PRINTF("Create Mainland Pimp");
		PImp = MakeShareable(new FONEEngineSDKPSMainLand());
		break;
	case EOneEngineSDKRegionType::Oversea:
		ONE_LOG_PRINTF("Create Oversea Pimp");
		PImp = MakeShareable(new FONEEngineSDKPSOversea());
		break;
	default:
		PImp = MakeShareable(new FONEEngineSDKPSMainLand());
	}

	char buff_oid[32];
	int32_t size = 0;
	memset(&buff_oid, 0x0, 32);
	wmc_platform_ps_online_id(buff_oid, &size);
	FString Result = FString(UTF8_TO_TCHAR(buff_oid));

	if (Result.Len() > 0)
	{
		PImp->PsOnlineId = Result;
	}
}

void* IONEEngineSDKPSInterface::GetHandle() const
{
	return Handle;
}

void IONEEngineSDKPSInterface::SetExtraTrackInfo(const TMap<FString, FString>& Info)
{
	ExtraTrackInfo = Info;
}

#endif
