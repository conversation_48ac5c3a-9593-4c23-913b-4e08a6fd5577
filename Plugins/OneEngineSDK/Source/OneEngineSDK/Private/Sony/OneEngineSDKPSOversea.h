﻿#pragma once
#if ENGINE_SUPPORT_SONY

#include "OneEngineSDKPSAdapter.h"

class FONEEngineSDKPSOversea : public IONEEngineSDKPSInterface
{
public:
	FONEEngineSDKPSOversea();
	virtual ~FONEEngineSDKPSOversea() override;
	virtual void Login() override;
	virtual void Logout(int Type) override;
	virtual FOneUserInfo GetUserInfo() override;
	virtual FString GetChannelId() override;
	virtual bool CheckLoggedInStatus() override;
	virtual FString GetChannelMediaId() override;
	virtual int32 GetPlatformOS() override;
	virtual FString GetChannelPlatform() override;
	virtual void RequestAntiAddictionInfo(TFunction<void(const FOneAntiAddictionInfo& Info)> Callback) override;
	virtual void StartAntiAddictionMonitoring(
		const FString& ServerId, const FString& RoleId, TFunction<void(const FOneAntiAddictionInfo& Info)> Callback) override;
	virtual void StopAntiAddictionMonitoring() override;
	virtual int InitializeSDK(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate) override;
	virtual void GetUserLocation(TFunction<void(const FOneUserLocationInfo& LocationInfo)> OnFinishedLambda) override;
	virtual void SetDebugMode(bool bDebugMode) override;
	virtual bool GetDebugMode() override;
	virtual void OpenUserCenter() override;
	virtual int OpenComplianceOnWebView() override;
	virtual void SetLanguage(const FString& Language) override;
	virtual FString GetCurrentLanguage() override;
	virtual bool ExaminStatus() override;
	virtual void Track(const FString& EventName, const TMap<FString, FString>& EventParams, FString TaskId) override;
	virtual int GetFriendList(
		int Offset, int Count, TFunction<void(bool bSuccess, const FString& Result, int ErrCode, const FString& ErrInfo)>) override;
	virtual int GetBlockList(
		int Offset, int Count, TFunction<void(bool bSuccess, const FString& Result, int ErrCode, const FString& ErrInfo)>) override;
	virtual int GetProductInfoList(int32 ServiceLabel, FString CategoryLabel,
		TFunction<void(bool bSuccess, const FString& Result, int ErrCode, const FString& ErrInfo)> CallBack) override;
	virtual void UseRedeemCode(const FString& ExchangeCode, const FOneRoleInfo& RoleInfo, const TMap<FString, FString>& ExtraInfo,
		TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> CallBack) override;
	virtual void GetRoleList(const FString& ServerId,
		TFunction<void(bool bSuccess, int32 Code, const FString& Msg, const TArray<FOneURCRoleInfo>& RoleList)> CallBack) override;
	virtual void ShowLoading() override;
	virtual void HideLoading() override;
	

private:

	void CheckIsAgreePrivacy(TFunction<void(bool Agree, const FString& Title, const FString& Content)> Callback);
	void SetUserAgreementText(const FText& TitleText, const FText& HintText);
	void StartLogin(TFunction<void(int Code, FString Message, const FOneUserInfo& UserInfo)> Function,
		const FString& Email = TEXT(""), const FString& Password = TEXT(""), const FString& VerificationCode = TEXT(""),
		bool bUseVerificationCode = false, bool bSkipAgreement = false);
	void HandleErrorCode(int Code, FString Message);
	void SyncUpdateLoginOption();
	// 已经绑定登录类型
	bool HasLoggedIn();
	// 邮箱密码登录
	void LoginWithEmail(const FString& Email, const FString& Password);
	// 发送登录验证码
	void SendLoginVerificationCode(const FString& Email);
	// 邮箱验证码登录
	void LoginWithVerificationCode(const FString& Email, const FString& VerificationCode);
	// 获取在线设备列表
	void GetOnlineDevices(TFunction<void(bool Success, const FString& Result, int ErrCode, const FString& ErrInfo)> Callback);
	// 离线设备
	void OfflineDevice(const FString& DeviceId,
		TFunction<void(bool Success, const FString& Result, int ErrCode, const FString& ErrInfo)> Callback);

	// 解析并更新用户信息
	// 返回 PSN 用户 ID
	int32 ParseAndUpdateUserInfo(const FString& UserInfoJson);

	// 格式化 UserCenter 需要的数据
	FString FormatUserCenterData();

	void ResetUserInfo();

	TMap<int32, int32> LoginOptionMap;
	TMap<int32, bool> InitBoundAccounts;

	int32 LoginOption{0};
	TMap<FString, FString> RawUserInfo;
	FOneUserInfo CurrentUserInfo;
	TMap<int32, bool> BoundAccounts;
	int32 PsnUserId{0};

	FString IntermediateUserInfo;

	// 用户隐私条款标题
	FText UserAgreementTitle;
	// 用户隐私条款内容
	FText UserAgreementContent;

	bool bDebugEnable{false};
	bool AgreePrivacyAgreement{false};
	bool bEnterButtonAssignCircle{false};
};
#endif
