#if ENGINE_SUPPORT_SONY
#include "OneEngineSDKSubsystem.h"
#include "OneEngineSDKPSSubsystem.h"
#include "OneEngineSDKPSAdapter.h"
#include "OneEngineSDKPSUtils.h"
#include "JsonObjectConverter.h"
#include "Misc/FileHelper.h" // 引入文件帮助类
#include "Misc/Base64.h"    // 引入 Base64 类
#include "Policies/CondensedJsonPrintPolicy.h"
#include "../Views/PSUserWidgetSettings.h"
#if PLATFORM_PS4 || PLATFORM_PS5
#include "libone.h"
#include "wmc/device.h"
#include "libglobal.h"
#include "libpfa.h"
#include "wmc/crypto.h"
#else
// 纯粹就是为了智能提示
#include "../../DependLibraries//Source/ThirdParty/DependLibrariesLibrary/PS5/include/libpfa.h"
#include "../../DependLibraries//Source/ThirdParty/DependLibrariesLibrary/PS5/include/libone.h"
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/libglobal.h"
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/wmc/device.h"
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/wmc/crypto.h"
#endif

namespace
{
// Constants for configuration
const FString IWPLAY_AREA_IDENTIFIER = TEXT("iqecB/XQd+tT0dS52lzTdg==");
const FString HK_COUNTRY_CODE = TEXT("hk");
const FString TW_COUNTRY_CODE = TEXT("tw");

FString KAppID;

// 移除字符串中的数字，小数点，逗号，空格
// 如 1,234.56USD -> USD
// 如 $1,234.56 -> $
FString GetCurrency(const FString& Str)
{
	FString Result;
	// 遍历输入字符串中的每个字符
	for (int32 i = 0; i < Str.Len(); i++)
	{
		const TCHAR Char = Str[i];
		// 如果是数字、小数点、逗号或空白字符则跳过
		if (FChar::IsDigit(Char) || Char == '.' || Char == ',' || FChar::IsWhitespace(Char))
		{
			continue;
		}
		// 其他字符追加到结果中
		Result.AppendChar(Char);
	}
	return Result;
}

unsigned char DeKey[16] = {0x23, 0x62, 0x77, 0x33, 0x46, 0x5a, 0x15, 0xf1, 0xd5, 0x3b, 0x56, 0x7c, 0x64, 0xb2, 0x04, 0x73};

FString DecodeKey(const FString& Str)
{
	char* Output = nullptr;
	size_t Size = 0;
	const int Ret = wmc_decrypt_aes128_pkcs7_ecb_base64(&Output, &Size, TCHAR_TO_UTF8(*Str), Str.Len(),
		reinterpret_cast<const char*>(DeKey), 16);
	if (Ret != 0)
	{
		ONE_LOG_ERROR_PRINTF("Decode failed: %d", Ret);
		return FString();
	}
	FString Result = FString(Size, UTF8_TO_TCHAR(Output));
	wmc_crypto_free(Output);
	return Result;
}

// Get configuration file paths
void GetConfigPaths(FString& OutOneConfigPath, FString& OutGlobalConfigPath)
{
	const FString ContentDir = FPaths::ProjectContentDir() / TEXT("OneSDKConfig") / TEXT("PS");
	OutOneConfigPath = ContentDir / TEXT("OneSDKConfig.json");
	OutGlobalConfigPath = ContentDir / TEXT("GlobalSDKConfig.json");
}

// Get country code from platform
FString GetPlatformCountryCode()
{
	char Buf[8];
	int32_t Size = 0;
	memset(&Buf, 0x0, 8);
	wmc_platform_account_country(Buf, &Size);
	return FString(UTF8_TO_TCHAR(Buf));
}

// Select appropriate config from list based on country code
TSharedPtr<FJsonObject> SelectConfigByCountryCode(const TArray<TSharedPtr<FJsonValue>>& ConfigList)
{
	if (ConfigList.Num() == 1)
	{
		TSharedPtr<FJsonObject> JsonObject = ConfigList[0]->AsObject();
		FString Area;
		if (JsonObject->TryGetStringField(TEXT("Area"), Area))
		{
			const bool bIsIwplayConfig = Area.Equals(IWPLAY_AREA_IDENTIFIER);
			UPSUserWidgetSettings::Get()->bIsIwPlay = bIsIwplayConfig;
			ONE_LOG_PRINTF("** Using %s Config.", *Area);
		}
		return JsonObject;
	}

	if (ConfigList.Num() > 1)
	{
		const FString CountryCode = GetPlatformCountryCode();
		ONE_LOG_PRINTF("** Account Country Code is %s", *CountryCode);
		const bool bIsIwplay = CountryCode.Equals(HK_COUNTRY_CODE) || CountryCode.Equals(TW_COUNTRY_CODE);
		ONE_LOG_PRINTF("** Using %s Config.", bIsIwplay ? TEXT("Iwplay") : TEXT("Other"));
		UPSUserWidgetSettings::Get()->bIsIwPlay = bIsIwplay;
		
		for (const auto& Item : ConfigList)
		{
			TSharedPtr<FJsonObject> JsonObject = Item->AsObject();
			FString Area;
			if (JsonObject->TryGetStringField(TEXT("Area"), Area))
			{
				const bool bIsIwplayConfig = Area.Equals(IWPLAY_AREA_IDENTIFIER);
				if ((bIsIwplay && bIsIwplayConfig) || (!bIsIwplay && !bIsIwplayConfig))
				{
					ONE_LOG_PRINTF("** Using %s SDK.", bIsIwplay ? TEXT("Iwplay") : TEXT("Other"));
					return JsonObject;
				}
			}
		}
	}

	return nullptr;
}

// Decode config values and extract AppID/AppKey
void DecodeConfigValues(TSharedPtr<FJsonObject> ConfigJson, FString& OutAppID, FString& OutAppKey)
{
	for (auto& Pair : ConfigJson->Values)
	{
		if (Pair.Value->Type == EJson::String)
		{
			FString ValueStr = Pair.Value->AsString();
			FString DecodeValue = DecodeKey(ValueStr);

			if (Pair.Key.Equals(TEXT("appID")))
			{
				OutAppID = DecodeValue;
			}
			else if (Pair.Key.Equals(TEXT("appKey")))
			{
				OutAppKey = DecodeValue;
			}

			Pair.Value = MakeShareable(new FJsonValueString(DecodeValue));
		}
	}
}

// Parse global config JSON and select appropriate configuration
TSharedPtr<FJsonObject> ParseAndSelectGlobalConfig(const FString& GlobalConfigContent)
{
	TArray<TSharedPtr<FJsonValue>> ConfigList;
	const TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(GlobalConfigContent);

	if (!FJsonSerializer::Deserialize(JsonReader, ConfigList))
	{
		return nullptr;
	}

	return SelectConfigByCountryCode(ConfigList);
}

// Convert config JSON back to base64 encoded string
FString SerializeConfigToBase64(TSharedPtr<FJsonObject> ConfigJson)
{
	FString JsonStr;
	TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> Writer =
		TJsonWriterFactory<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>::Create(&JsonStr);
	FJsonSerializer::Serialize(ConfigJson.ToSharedRef(), Writer);
	return FBase64::Encode(JsonStr);
}

// Load mainland configuration from OneSDKConfig.json file
bool LoadMainlandConfig(FString& OutAppID, FString& OutAppKey, FString& OutConfigContent,
	const TFunction<void(int, const FString&)>& FailInit)
{
	FString OneConfigPath, GlobalConfigPath;
	GetConfigPaths(OneConfigPath, GlobalConfigPath);

	if (!FPaths::FileExists(OneConfigPath))
	{
		return false;
	}

	ONE_LOG_PRINTF("Initializing SDK for Mainland...");
	FONEEngineSDKPSAdapter::Get().SDKRegionType = static_cast<int>(EOneEngineSDKRegionType::Mainland);

	GConfig->GetString(TEXT("/Script/OneEngineEditor.OneEngineSettings"), TEXT("AppID"), OutAppID, GGameIni);
	GConfig->GetString(TEXT("/Script/OneEngineEditor.OneEngineSettings"), TEXT("OneAppKey"), OutAppKey, GGameIni);

	// Read OneConfigPath and convert to base64
	FString FileContent;
	if (FFileHelper::LoadFileToString(FileContent, *OneConfigPath))
	{
		OutConfigContent = FileContent;
	}

	if (OutAppID.IsEmpty() || OutAppKey.IsEmpty() || OutConfigContent.IsEmpty())
	{
		FailInit(-1, TEXT("AppID or AppKey or Mainland config is empty. Please check your config file!"));
		return false;
	}

	ONE_LOG_PRINTF("AppID loaded for Mainland.");
	return true;
}

// Load oversea configuration from GlobalSDKConfig.json file
bool LoadOverseaConfigFromFile(FString& OutAppID, FString& OutAppKey, FString& OutConfigContent,
	const TFunction<void(int, const FString&)>& FailInit)
{
	FString OneConfigPath, GlobalConfigPath;
	GetConfigPaths(OneConfigPath, GlobalConfigPath);

	if (!FPaths::FileExists(GlobalConfigPath))
	{
		return false;
	}

	ONE_LOG_PRINTF("Initializing SDK for Oversea...");
	FONEEngineSDKPSAdapter::Get().SDKRegionType = static_cast<int>(EOneEngineSDKRegionType::Oversea);

	FString GlobalConfig;
	if (!FFileHelper::LoadFileToString(GlobalConfig, *GlobalConfigPath))
	{
		FailInit(-1, TEXT("Invalid GlobalSDKConfig.json"));
		return false;
	}

	TSharedPtr<FJsonObject> ConfigJson = ParseAndSelectGlobalConfig(GlobalConfig);
	if (!ConfigJson.IsValid())
	{
		FailInit(-1, TEXT("Invalid GlobalSDKConfig.json"));
		return false;
	}

	DecodeConfigValues(ConfigJson, OutAppID, OutAppKey);
	OutConfigContent = SerializeConfigToBase64(ConfigJson);

	if (OutAppID.IsEmpty() || OutAppKey.IsEmpty() || OutConfigContent.IsEmpty())
	{
		FailInit(-1, TEXT("AppID or AppKey or Oversea config is empty. Please check your config file!"));
		return false;
	}

	ONE_LOG_PRINTF("AppID loaded for Oversea.");
	return true;
}

// Load oversea configuration from OneEngineSettings (fallback method)
bool LoadOverseaConfigFromSettings(FString& OutAppID, FString& OutAppKey, FString& OutConfigContent,
	const TFunction<void(int, const FString&)>& FailInit)
{
	FString SDKRegion;
	GConfig->GetString(TEXT("/Script/OneEngineEditor.OneEngineSettings"), TEXT("SDKRegion"), SDKRegion, GGameIni);

	if (SDKRegion.Equals(TEXT("Mainland")))
	{
		// Mainland fallback
		ONE_LOG_PRINTF("Initializing SDK for Mainland...");
		FONEEngineSDKPSAdapter::Get().SDKRegionType = 0;

		GConfig->GetString(TEXT("/Script/OneEngineEditor.OneEngineSettings"), TEXT("AppID"), OutAppID, GGameIni);
		GConfig->GetString(TEXT("/Script/OneEngineEditor.OneEngineSettings"), TEXT("OneAppKey"), OutAppKey, GGameIni);
		GConfig->GetString(TEXT("/Script/OneEngineEditor.OneEngineSettings"), TEXT("PSMainlandConfigData"), OutConfigContent, GGameIni);

		if (OutAppID.IsEmpty() || OutAppKey.IsEmpty() || OutConfigContent.IsEmpty())
		{
			FailInit(-1, TEXT("AppID or AppKey or PSMainlandConfigData is empty, please check your config file!"));
			return false;
		}

		ONE_LOG_PRINTF("AppID: %s", *OutAppID);
		ONE_LOG_PRINTF("AppKey: %s", *OutAppKey);
		ONE_LOG_PRINTF("PSMainlandConfigData: %s", *OutConfigContent);
		return true;
	}
	else
	{
		// Oversea fallback
		FONEEngineSDKPSAdapter::Get().SDKRegionType = 1;

		FString Base64ConfigContent;
		GConfig->GetString(TEXT("/Script/OneEngineEditor.OneEngineSettings"), TEXT("PSOverseaConfigData"), Base64ConfigContent, GGameIni);

		if (Base64ConfigContent.IsEmpty())
		{
			FailInit(-1, TEXT("PSOverseaConfigData is empty, please check your config file!"));
			return false;
		}

		FString GlobalConfig;
		FBase64::Decode(Base64ConfigContent, GlobalConfig);

		TSharedPtr<FJsonObject> ConfigJson = ParseAndSelectGlobalConfig(GlobalConfig);
		if (!ConfigJson.IsValid())
		{
			FailInit(-1, TEXT("Invalid GlobalSDKConfig.json"));
			return false;
		}

		DecodeConfigValues(ConfigJson, OutAppID, OutAppKey);
		OutConfigContent = SerializeConfigToBase64(ConfigJson);

		if (OutAppID.IsEmpty() || OutAppKey.IsEmpty() || OutConfigContent.IsEmpty())
		{
			FailInit(-1, TEXT("AppID or AppKey or Oversea config is empty. Please check your config file!"));
			return false;
		}

		ONE_LOG_PRINTF("AppID loaded for Oversea.");
		return true;
	}
}

// Setup SDK adapter with configuration
void SetupSDKAdapter(const FString& AppID, const FString& AppKey, const FString& ConfigContent,
	UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate, UOneEngineSDKSubsystem* Subsystem)
{
	// Store global AppID
	KAppID = AppID;

	ONE_LOG_PRINTF("Creating PIMP object...");
	FONEEngineSDKPSAdapter::Get().MakePimp();
	FONEEngineSDKPSAdapter::Get().GetPimp()->AppId = AppID;
	FONEEngineSDKPSAdapter::Get().GetPimp()->AppKey = AppKey;
	FONEEngineSDKPSAdapter::Get().GetPimp()->ConfigContent = ConfigContent;

	ONE_LOG_PRINTF("Setting up login callback...");
	FONEEngineSDKPSAdapter::Get().GetPimp()->OnLoginResultReceived = [Subsystem](bool bSuccess, int Code, const FString& Msg,
		const FOneUserInfo& UserInfo)
		{
			check(IsInGameThread());
			Subsystem->LoginResultDelegate.Broadcast(bSuccess, Code, Msg, UserInfo);
		};

	ONE_LOG_PRINTF("Setting up logout callback...");
	FONEEngineSDKPSAdapter::Get().GetPimp()->OnLogoutCallback = [Subsystem](bool bSuccess, int Code, const FString& Msg)
	{
		check(IsInGameThread());
		Subsystem->LogoutResultDelegate.Broadcast(bSuccess, Code, Msg);
	};

	FONEEngineSDKPSAdapter::Get().GetPimp()->InitializeSDK(InitDelegate);
	ONE_LOG_PRINTF("Initialization completed.");
}

}

void UOneEngineSDKSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
}

void UOneEngineSDKSubsystem::Init(FOneInitDelegate InitDelegate)
{
	FString AppID, AppKey, ConfigContent;
	ONE_LOG_PRINTF("Starting OneEngineSDK initialization...");

	auto FailInit = [InitDelegate](int Code, const FString& Msg)
	{
		ONE_LOG_ERROR_PRINTF("%s", *Msg);
		FONEEngineSDKPSUtils::ExecInGameThread([=]()
		{
			auto _ = InitDelegate.ExecuteIfBound(false, Code, Msg);
		});
	};

	// Try different configuration sources in order of preference
	if (LoadMainlandConfig(AppID, AppKey, ConfigContent, FailInit) ||
		LoadOverseaConfigFromFile(AppID, AppKey, ConfigContent, FailInit) ||
		LoadOverseaConfigFromSettings(AppID, AppKey, ConfigContent, FailInit))
	{
		SetupSDKAdapter(AppID, AppKey, ConfigContent, InitDelegate, this);
	}
	// If all configuration loading methods fail, the respective FailInit calls will handle the error reporting
}


EOneEngineSDKRegionType UOneEngineSDKSubsystem::GetRegionType()
{
	return FONEEngineSDKPSAdapter::Get().GetRegionType();
}

void UOneEngineSDKSubsystem::Login()
{
	ONE_LOG_PRINTF("Starting login...");
	FONEEngineSDKPSAdapter::Get().GetPimp()->Login();
}

void UOneEngineSDKSubsystem::SwitchAccount()
{
	ONE_LOG_PRINTF("Starting switch account...");
	FONEEngineSDKPSAdapter::Get().GetPimp()->Logout(4);
}

FOneUserInfo UOneEngineSDKSubsystem::GetUserInfo()
{
	if (!IsLoggedIn())
	{
		ONE_LOG_WARNING_PRINTF("User not logged in! Will return empty user info.");
	}
	return FONEEngineSDKPSAdapter::Get().GetPimp()->GetUserInfo();
}

void UOneEngineSDKSubsystem::Pay(const FOnePaymentInfo& PaymentInfo)
{
	UOneEngineSDKPSSubsystem* Subsystem = FONEEngineSDKPSUtils::GetWorld()->GetGameInstance()->GetSubsystem<
		UOneEngineSDKPSSubsystem>();
	FOnePSPurchaseForm PurchaseForm;
	PurchaseForm.GameOrderId = PaymentInfo.OrderId;
	PurchaseForm.GameRoleId = PaymentInfo.RoleId;
	PurchaseForm.GameRoleName = PaymentInfo.RoleName;
	PurchaseForm.GameServerId = PaymentInfo.GameServerId;
	PurchaseForm.ProductId = PaymentInfo.ProductId;
	PurchaseForm.ProductName = PaymentInfo.ProductName;
	PurchaseForm.OrderAmount = PaymentInfo.Price;
	PurchaseForm.GameExtraInfo = PaymentInfo.ExtInfo;
	// 这里暂时没有使用
	PurchaseForm.GameDescription = "";
	PurchaseForm.PSNServerLabel = FCString::Atoi(*PSStoreServerLabel);

	Subsystem->Pay(PurchaseForm, [this](int Code, bool HasPurchased, const FString& Tips)
	{
		if (HasPurchased)
		{
			ONE_LOG_PRINTF("Payment succeeded!");
			FONEEngineSDKPSUtils::ExecInGameThread([this, Code, Tips]()
			{
				// 注意，这里不返回 OrderId
				const FString OrderId = TEXT("");
				PayResultDelegate.Broadcast(true, Code, Tips, OrderId);
			});
		}
		else
		{
			ONE_LOG_ERROR_PRINTF("Payment failed: %s (%d)", *Tips, Code);
			FONEEngineSDKPSUtils::ExecInGameThread([this ,Code, Tips]()
			{
				const FString OrderId = TEXT("");
				PayResultDelegate.Broadcast(false, Code, Tips, OrderId);
			});
		}
	});
}

// 角色事件打点
void UOneEngineSDKSubsystem::TrackEventRoleLoginSucceed(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
	FONEEngineSDKPSAdapter::Get().GetPimp()->PlayerRoleInfo = RoleInfo;
	FOneUserInfo UserInfo = FONEEngineSDKPSAdapter::Get().GetPimp()->GetUserInfo();
	TMap<FString, FString> EventParams;
	FString EventName = TEXT("roleLoginSDK");
	EventParams.Add(TEXT("uid"), UserInfo.UserId);
	EventParams.Add(TEXT("roleid"), RoleInfo.RoleId);
	EventParams.Add(TEXT("serverid"), RoleInfo.ServerId);
	EventParams.Add(TEXT("vip"), RoleInfo.Vip);
	EventParams.Add(TEXT("level"), RoleInfo.Level);
	TrackEvent(EventName, EventParams);
}

void UOneEngineSDKSubsystem::TrackEventRoleLoginError(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port,
	const FString& Code, const FString& Msg)
{
	FOneUserInfo UserInfo = FONEEngineSDKPSAdapter::Get().GetPimp()->GetUserInfo();
	TMap<FString, FString> EventParams;
	FString EventName = TEXT("roleLoginErrorSDK");
	EventParams.Add(TEXT("uid"), UserInfo.UserId);
	EventParams.Add(TEXT("roleid"), RoleInfo.RoleId);
	EventParams.Add(TEXT("serverid"), RoleInfo.ServerId);
	EventParams.Add(TEXT("vip"), RoleInfo.Vip);
	EventParams.Add(TEXT("level"), RoleInfo.Level);
	TrackEvent(EventName, EventParams);
}

void UOneEngineSDKSubsystem::TrackEventRoleLogout(const FOneRoleInfo& RoleInfo)
{
	FONEEngineSDKPSAdapter::Get().GetPimp()->PlayerRoleInfo = FOneRoleInfo();
	FOneUserInfo UserInfo = FONEEngineSDKPSAdapter::Get().GetPimp()->GetUserInfo();
	TMap<FString, FString> EventParams;
	FString EventName = TEXT("roleLogoutSDK");
	EventParams.Add(TEXT("uid"), UserInfo.UserId);
	EventParams.Add(TEXT("roleid"), RoleInfo.RoleId);
	EventParams.Add(TEXT("serverid"), RoleInfo.ServerId);
	EventParams.Add(TEXT("vip"), RoleInfo.Vip);
	EventParams.Add(TEXT("level"), RoleInfo.Level);
	TrackEvent(EventName, EventParams);
}

void UOneEngineSDKSubsystem::TrackEventRoleCreate(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
	FONEEngineSDKPSAdapter::Get().GetPimp()->PlayerRoleInfo = RoleInfo;
	FOneUserInfo UserInfo = FONEEngineSDKPSAdapter::Get().GetPimp()->GetUserInfo();
	TMap<FString, FString> EventParams;
	const FString EventName = TEXT("Create_role");
	EventParams.Add(TEXT("uid"), UserInfo.UserId);
	EventParams.Add(TEXT("roleid"), RoleInfo.RoleId);
	EventParams.Add(TEXT("serverid"), RoleInfo.ServerId);
	EventParams.Add(TEXT("vip"), RoleInfo.Vip);
	EventParams.Add(TEXT("level"), RoleInfo.Level);
	TrackEvent(EventName, EventParams);
}

void UOneEngineSDKSubsystem::TrackEventRoleLevelUp(const FOneRoleInfo& RoleInfo)
{
	FONEEngineSDKPSAdapter::Get().GetPimp()->PlayerRoleInfo = RoleInfo;
	FOneUserInfo UserInfo = FONEEngineSDKPSAdapter::Get().GetPimp()->GetUserInfo();
	TMap<FString, FString> EventParams;
	FString EventName = TEXT("roleUpdateSDK");
	EventParams.Add(TEXT("uid"), UserInfo.UserId);
	EventParams.Add(TEXT("roleid"), RoleInfo.RoleId);
	EventParams.Add(TEXT("serverid"), RoleInfo.ServerId);
	EventParams.Add(TEXT("vip"), RoleInfo.Vip);
	EventParams.Add(TEXT("level"), RoleInfo.Level);

	TrackEvent(EventName, EventParams);

	// roleLevelUpTo+ 等级
	FString EventName1 = FString::Printf(TEXT("roleLevelUpTo%s"), *RoleInfo.Level);
	TrackEvent(EventName1, EventParams);
}

// 资源事件打点
void UOneEngineSDKSubsystem::GameResReqEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode,
	const FString& ErrorMsg)
{
	FString Event;
	switch (State)
	{
		case EOneResEventState::Success:
			Event = TEXT("gameResReqSuccess");
			break;
		case EOneResEventState::Failed:
			Event = TEXT("gameResReqError");
			break;
		case EOneResEventState::Begin:
			Event = TEXT("gameResReqBegin");
			break;
		default:
			break;
	}
	TMap<FString, FString> EventParams;
	EventParams.Add(TEXT("url"), Url);
	EventParams.Add(TEXT("error"), ErrorMsg);
	TrackEvent(Event, EventParams);
}

void UOneEngineSDKSubsystem::GameUpdateAssetEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode,
	const FString& ErrorMsg)
{
	FString Event;
	switch (State)
	{
		case EOneResEventState::Success:
			Event = TEXT("gameUpdateAssetSuccess");
			break;
		case EOneResEventState::Failed:
			Event = TEXT("gameUpdateAssetError");
			break;
		case EOneResEventState::Begin:
			Event = TEXT("gameUpdateAssetBegin");
			break;
		default:
			break;
	}
	TMap<FString, FString> EventParams;
	EventParams.Add(TEXT("url"), Url);
	EventParams.Add(TEXT("error"), ErrorMsg);
	TrackEvent(Event, EventParams);
}

void UOneEngineSDKSubsystem::GameResDecEvent(EOneResEventState State, const FString& ErrorMsg)
{
	FString Event;
	switch (State)
	{
		case EOneResEventState::Success:
			Event = TEXT("gameResDecSuccess");
			break;
		case EOneResEventState::Failed:
			Event = TEXT("gameResDecError");
			break;
		case EOneResEventState::Begin:
			Event = TEXT("gameResDecBegin");
			break;
		default:
			break;
	}
	TMap<FString, FString> EventParams;
	EventParams.Add(TEXT("error"), ErrorMsg);
	TrackEvent(Event, EventParams);
}

void UOneEngineSDKSubsystem::GameGetServerListEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode,
	const FString& ErrorMsg)
{
	FString Event;
	switch (State)
	{
		case EOneResEventState::Success:
			Event = TEXT("gameGetServerListSuccess");
			break;
		case EOneResEventState::Failed:
			Event = TEXT("gameGetServerListError");
			break;
		case EOneResEventState::Begin:
			Event = TEXT("gameGetServerListBegin");
			break;
		default:
			break;
	}
	TMap<FString, FString> EventParams;
	EventParams.Add(TEXT("url"), Url);
	EventParams.Add(TEXT("error"), ErrorMsg);
	TrackEvent(Event, EventParams);
}

// 自定义事件打点
void UOneEngineSDKSubsystem::TrackEvent(const FString& Name, const TMap<FString, FString>& Payload)
{
	FONEEngineSDKPSAdapter::Get().GetPimp()->Track(Name, Payload, "3");
}

// 广告打点
void UOneEngineSDKSubsystem::TrackEventAD(const FString& Name, const TMap<FString, FString>& Payload)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

//进入游戏场景打点
void UOneEngineSDKSubsystem::TrackEventEnterGameScene(const FString& SceneName, int32 Period, const TMap<FString, FString>& HintMap)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

//退出游戏打点
void UOneEngineSDKSubsystem::TrackEventExitGameScene()
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

//设备打点额外信息
void UOneEngineSDKSubsystem::TrackEventAddExtraDeviceInfo(const TMap<FString, FString>& ExtraDeviceInfo)
{
	for (auto& Item : ExtraDeviceInfo)
	{
		ONE_LOG_PRINTF("TrackEventAddExtraDeviceInfo: %s = %s", *Item.Key, *Item.Value);
	}
	FONEEngineSDKPSAdapter::Get().GetPimp()->SetExtraTrackInfo(ExtraDeviceInfo);
}

void UOneEngineSDKSubsystem::SetUpConfigAppID(const FString& AppId)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

bool UOneEngineSDKSubsystem::IsLoggedIn()
{
	bool IsLoggedIn = FONEEngineSDKPSAdapter::Get().GetPimp()->CheckLoggedInStatus();
	ONE_LOG_PRINTF("CheckLoggedInStatus IsLoggedIn: %d", IsLoggedIn);
	return IsLoggedIn;
}

void UOneEngineSDKSubsystem::EnableDebugMode(bool Enable)
{
	ONE_LOG_PRINTF("EnableDebugMode: %d", Enable);
	FONEEngineSDKPSAdapter::Get().SetDebugMode(Enable);
}

bool UOneEngineSDKSubsystem::IsDebugMode()
{
	return FONEEngineSDKPSAdapter::Get().GetDebugMode();
}

void UOneEngineSDKSubsystem::OpenUserCenter()
{
	ONE_LOG_PRINTF("OpenUserCenter");
	FONEEngineSDKPSAdapter::Get().GetPimp()->OpenUserCenter();
}

void UOneEngineSDKSubsystem::GetQRCodeScanResult(FOneGetQRCodeScanResultDelegate OnGetQRCodeScanResultDelegate)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::OpenComplianceOnWebView()
{
	FONEEngineSDKPSAdapter::Get().GetPimp()->OpenComplianceOnWebView();
}

FString UOneEngineSDKSubsystem::GetChannelId()
{
	return FONEEngineSDKPSAdapter::Get().GetPimp()->GetChannelId();
}

FString UOneEngineSDKSubsystem::GetChannelMediaId()
{
	return FONEEngineSDKPSAdapter::Get().GetPimp()->GetChannelMediaId();
}

int32 UOneEngineSDKSubsystem::GetPlatformOS()
{
	return FONEEngineSDKPSAdapter::Get().GetPimp()->GetPlatformOS();
}

void UOneEngineSDKSubsystem::GetChannelPlatform(FOneGetPlatformDelegate OnGetPlatformResultDelegate)
{
	FONEEngineSDKPSUtils::ExecInGameThread([OnGetPlatformResultDelegate]
	{
		FString Platform = FONEEngineSDKPSAdapter::Get().GetPimp()->GetChannelPlatform();
		auto _ = OnGetPlatformResultDelegate.ExecuteIfBound(true, 0,TEXT(""), Platform);
	});
}

void UOneEngineSDKSubsystem::GetChannelPlatformLambda(
	const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const FString& Platform)>& OnFinishedLambda)
{
	FONEEngineSDKPSUtils::ExecInGameThread([OnFinishedLambda]
	{
		FString Platform = FONEEngineSDKPSAdapter::Get().GetPimp()->GetChannelPlatform();
		if (OnFinishedLambda)
		{
			OnFinishedLambda(true, 0,TEXT(""), Platform);
		}
	});
}

// 万能方法接口
bool UOneEngineSDKSubsystem::IsCommonFunctionSupported(const FString& FuncName)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
	return false;
}

// 调用万能方法
void UOneEngineSDKSubsystem::CallCommonFunction(const FString& FuncName, const FString& Params,
	FOneCommonFunctionDelegate CommonFunctionDelegate)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 获取档位信息 (iOS 专有接口)
void UOneEngineSDKSubsystem::GetProductList(const TArray<FString>& ProductIds, FOneProductInfoDelegate ProductResultDelegate)
{
	FString ServerLabel = PSStoreServerLabel;
	if (ServerLabel.IsEmpty())
	{
		ONE_LOG_ERROR_PRINTF("PSStoreServerLabel is empty, Please set it first!");
		if (ProductResultDelegate.IsBound())
		{
			ProductResultDelegate.Execute(false, -1, {});
		}
		return;
	}
	UOneEngineSDKPSSubsystem* Subsystem = FONEEngineSDKPSUtils::GetWorld()->GetGameInstance()->GetSubsystem<
		UOneEngineSDKPSSubsystem>();
	int32 ServerLabelInt = FCString::Atoi(*ServerLabel);
	Subsystem->GetProductInfoListPSLambda(ServerLabelInt, PSStoreCategoryLabel,
		[ProductResultDelegate](bool bSuccess, const FOnePSProductCategory& Category, int ErrCode, const FString& ErrInfo)
		{
			// bool, bSucceed, int, Code, const TArray<FOneProductInfo>&, ProductListResult
			if (!bSuccess)
			{
				auto _ = ProductResultDelegate.ExecuteIfBound(false, ErrCode, {});
				return;
			}
			TArray<FOneProductInfo> ProductListResult;
			for (const FOnePSProduct& Item : Category.Children)
			{
				FOneProductInfo ProductInfo;
				ProductInfo.ProductId = Item.Label;
				ProductInfo.Title = Item.DisplayName;
				ProductInfo.Desc = Item.Description;

				if (Item.Skus.Num() > 0)
				{
					ProductInfo.SymbolPrice = Item.Skus[0].DisplayPrice;
					ProductInfo.Price = FString::Printf(TEXT("%d"), Item.Skus[0].Price);
					ProductInfo.Currency = GetCurrency(Item.Skus[0].DisplayPrice);
				}

				ProductListResult.Add(ProductInfo);
			}
			auto _ = ProductResultDelegate.ExecuteIfBound(true, 0, ProductListResult);
		});
}

bool UOneEngineSDKSubsystem::ExaminStatus()
{
	return FONEEngineSDKPSAdapter::Get().GetPimp()->ExaminStatus();
}

//是否检测 BundleId(iOS 专有接口)
void UOneEngineSDKSubsystem::ShouldVerifyBundleId(bool bShouldVerify)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::GetUserLocationInfo(FOneUserLocationInfoDelegate LocationInfoDelegate)
{
	return GetUserLocationInfoLambda([LocationInfoDelegate](const FOneUserLocationInfo& LocationInfo)
	{
		bool _ = LocationInfoDelegate.ExecuteIfBound(LocationInfo);
	});
}


void UOneEngineSDKSubsystem::GetUserLocationInfoLambda(
	const TFunction<void(const FOneUserLocationInfo& LocationInfo)>& OnFinishedLambda)
{
	ONE_LOG_PRINTF("GetUserLocationInfo");
	FONEEngineSDKPSAdapter::Get().GetPimp()->GetUserLocation(OnFinishedLambda);
}


void UOneEngineSDKSubsystem::GetDeviceInfo(FOneGetDeviceInfoDelegate Delegate)
{
	return GetDeviceInfoLambda([Delegate](const FOneDeviceInfo& DeviceInfo)
	{
		bool _ = Delegate.ExecuteIfBound(DeviceInfo);
	});
}

void UOneEngineSDKSubsystem::GetDeviceInfoLambda(const TFunction<void(const FOneDeviceInfo& DeviceInfo)>& OnFinishedLambda)
{
	FOneDeviceInfo DeviceInfo;
	char Output[WMC_DEVICE_BUF_MAX];
	wmc_device_id(Output);
	DeviceInfo.DeviceId = Output;

	wmc_device_model(Output);
	FString Model = Output;
	DeviceInfo.Ext.Add(FString("dmd"), Model);

	wmc_device_sys(Output);
	DeviceInfo.DeviceSys = Output;

	DeviceInfo.Ext.Add(FString(TEXT("mmr")), FString(TEXT("")));

	// 目前仅仅支持获取设备 ID，有需要其他信息
	ONE_LOG_PRINTF("GetDeviceInfo: %s", *DeviceInfo.DeviceId);
	FONEEngineSDKPSUtils::ExecInGameThread([DeviceInfo,OnFinishedLambda]
	{
		if (OnFinishedLambda)
		{
			OnFinishedLambda(DeviceInfo);
		}
	});
}

void UOneEngineSDKSubsystem::FetchAntiAddictionInfo(FOneFetchAntiAddictionInfoDelegate OnFetchAntiAddictionInfo)
{
	FONEEngineSDKPSAdapter::Get().GetPimp()->RequestAntiAddictionInfo([OnFetchAntiAddictionInfo](const FOneAntiAddictionInfo& Info)
	{
		bool _ = OnFetchAntiAddictionInfo.ExecuteIfBound(Info);
	});
}

void UOneEngineSDKSubsystem::StartAntiAddictionNotify(const FString& ServerId, const FString& RoleId)
{
	ONE_LOG_PRINTF("StartAntiAddictionNotify");
	FONEEngineSDKPSAdapter::Get().GetPimp()->StartAntiAddictionMonitoring(ServerId, RoleId,
		[this](const FOneAntiAddictionInfo& Info)
		{
			if (Info.Status == 1)
			{
				OnAntiAddictionTimeoutDelegate.Broadcast(true, Info);
			}
		});
}

void UOneEngineSDKSubsystem::StopAntiAddictionNotify()
{
	ONE_LOG_PRINTF("StopAntiAddictionNotify");
	return FONEEngineSDKPSAdapter::Get().GetPimp()->StopAntiAddictionMonitoring();
}

void UOneEngineSDKSubsystem::SetShowDefaultActivationResultToast(bool bShow)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::DisplayCDKeyDialog(FOneGenericResultDelegate OnCDKeyActivateResult, const FString& ServerId)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::QueryActCode(const FString& ServerId, FOneQueryActCodeResultDelegate OnQueryActCodeResultDelegate)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::ExchangeActCode(const FString& ServerId, const FString& ActCode,
	FOneGenericResultDelegate GenericResultDelegate)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::RedeemCouponCode(const FString& CouponCode, const FString& ServerId, const FString& RoleId,
	const FString& RoleLevel, const FString& VipLevel,
	const TMap<FString, FString>& ExtraInfo, FOneGenericResultDelegate OnRedeemCouponResult)
{
	RedeemCouponCodeLambda(CouponCode, ServerId, RoleId, RoleLevel, VipLevel, ExtraInfo,
		[OnRedeemCouponResult](bool bSuccess, int32 Code, const FString& Msg)
		{
			bool _ = OnRedeemCouponResult.ExecuteIfBound(bSuccess, Code, Msg);
		});
}

void UOneEngineSDKSubsystem::RedeemCouponCodeLambda(const FString& CouponCode, const FString& ServerId, const FString& RoleId,
	const FString& RoleLevel, const FString& VipLevel,
	const TMap<FString, FString>& ExtraInfo, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda)
{
	ONE_LOG_PRINTF("RedeemCouponCode, Code: %s, RoleId: %s, ServerId: %s, RoleLevel: %s, VipLevel: %s", *CouponCode, *RoleId,
		*ServerId, *RoleLevel, *VipLevel);
	FOneRoleInfo RoleInfo;
	RoleInfo.RoleId = RoleId;
	RoleInfo.ServerId = ServerId;
	RoleInfo.Vip = VipLevel;
	RoleInfo.Level = RoleLevel;
	FONEEngineSDKPSAdapter::Get().GetPimp()->UseRedeemCode(CouponCode, RoleInfo, ExtraInfo, OnFinishedLambda);
}


void UOneEngineSDKSubsystem::FetchUserRoleInfoList(FOneFetchUserRoleInfoListDelegate OnFetchUserRoleListDelegate,
	const FString& ServerId)
{
	FetchUserRoleInfoListLambda(
		[OnFetchUserRoleListDelegate](bool bSuccess, int32 Code, const FString& Msg, const TArray<FOneURCRoleInfo>& RoleList)
		{
			auto _ = OnFetchUserRoleListDelegate.ExecuteIfBound(bSuccess, Code, Msg, RoleList);
		}, ServerId);
}

void UOneEngineSDKSubsystem::FetchUserRoleInfoListLambda(
	const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneURCRoleInfo>& RoleList)>& OnFinishedLambda,
	const FString& ServerId)
{
	ONE_LOG_PRINTF("FetchUserRoleInfoList, ServerId: %s", *ServerId);
	FONEEngineSDKPSAdapter::Get().GetPimp()->GetRoleList(ServerId, OnFinishedLambda);
}

void UOneEngineSDKSubsystem::UserAuthentication(FOneUserAuthenticationResultDelegate OnUserAuthenticationResultDelegate)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 获取声明的权限 可用于展示
TArray<FOnePermissionInfo> UOneEngineSDKSubsystem::GetPermissions()
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
	TArray<FOnePermissionInfo> PermissionInfos;
	return PermissionInfos;
}

// 检查是否权限已授权
bool UOneEngineSDKSubsystem::CheckSelfPermission(EOnePermissionType Type)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
	return false;
}

// 申请权限
void UOneEngineSDKSubsystem::RequestPermission(EOnePermissionType Type, FOneRequestPermissionResultDelegate Delegate)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::RequestPermission(EOnePermissionType Type, FOneRequestPermissionResultDelegate Delegate,
	const TArray<FString>& Tips)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

//  关闭剪切板权限
void UOneEngineSDKSubsystem::CloseClipboardPermission()
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 打开应用权限设置页
void UOneEngineSDKSubsystem::OpenApplicationSetting()
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 打开 AIHelp 客服
void UOneEngineSDKSubsystem::OpenAIHelp(EOneAIHelpType Type, const FString& RoleId, const FString& ServerId,
	const FString& RoleName)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 打开自定义客服
void UOneEngineSDKSubsystem::OpenCustomerService(const FString& RoleID, const FString& RoleName, const FString& ServerID)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 文本翻译
void UOneEngineSDKSubsystem::Translate(const FString& Text, FOneTranslateResultDelegate Callback)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 获取当前设置语言
FString UOneEngineSDKSubsystem::GetCurrentLanguage()
{
	FString LanguageCode = FONEEngineSDKPSAdapter::Get().GetPimp()->GetCurrentLanguage();
	ONE_LOG_PRINTF("GetCurrentLanguage, LanguageCode: %s", *LanguageCode);
	return LanguageCode;
}

// 设置语言
void UOneEngineSDKSubsystem::SetLanguage(const FString& Code)
{
	ONE_LOG_PRINTF("SetLanguage, Code: %s", *Code);
	FONEEngineSDKPSAdapter::Get().GetPimp()->SetLanguage(Code);
}

// 获取本地语言列表
TArray<FString> UOneEngineSDKSubsystem::GetSupportedLanguageCodeList()
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
	TArray<FString> LanguageCodeList;
	return LanguageCodeList;
}

// 设置屏幕方向
void UOneEngineSDKSubsystem::SetScreenOrientation(EOneScreenOrientation Orientation)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 分享 SDK
void UOneEngineSDKSubsystem::ShareDataToApp(EOneShareAppTarget AppTarget, const EOneShareType Type, const FOneShareData& Data,
	FOneGenericResultDelegate OnShareResult)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 推送相关接口
// 注册推送
void UOneEngineSDKSubsystem::StartUpdatePushData(FOneStartUpdatePushDataDelegate Callback)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 设置点击推送消息后的回调
void UOneEngineSDKSubsystem::SetupNotificationCallback(FOneNotificationDelegate Callback)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 获取系统推送开关状态 和 应用推送开关状态
void UOneEngineSDKSubsystem::GetPushStatus(FOnePushStatusDelegate Callback)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 设置应用推送开关状态
void UOneEngineSDKSubsystem::SetProviderPushState(bool bProviderPushState, FOneGenericResultDelegate Callback)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 推送设置用户信息
void UOneEngineSDKSubsystem::SetPushUserInfo(const FString& ServerId, const FString& RoleId, FOneGenericResultDelegate Callback)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 推送解绑用户信息
void UOneEngineSDKSubsystem::UnSetPushUserInfo(FOneGenericResultDelegate Callback)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 获取推送类型列表
void UOneEngineSDKSubsystem::GetPushTypeInfoList(FOneGetPushTypeInfoListDelegate Callback)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 批量设置推送类型开关状态
void UOneEngineSDKSubsystem::UpdatePushTypeList(const TArray<FOnePushTypeInfo>& PushTypeList, FOneGenericResultDelegate Callback)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 夜间勿扰配置的回调
void UOneEngineSDKSubsystem::GetPushNotDisturb(FOnePushNotDisturbInfoDelegate Callback)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 设置勿扰模式
void UOneEngineSDKSubsystem::UpdatePushNotDisturb(const FOnePushNotDisturbInfo& NotDisturbInfo, FOneGenericResultDelegate Callback)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::KillProcess()
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}


void UOneEngineSDKSubsystem::QueryActCodeLambda(const FString& ServerId,
	const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)>&
	OnFinishedLambda)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

// 激活码
void UOneEngineSDKSubsystem::ExchangeActCodeLambda(const FString& ServerId, const FString& ActCode,
	const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}


void UOneEngineSDKSubsystem::Bind(FOneBindResultDelegate BindDelegate, EOneEngineThirdType BindType)
{
	// 绑定手机号、Facebook 等
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::TranslateLambda(const FString& Text,
	const TFunction<void(bool bSucceed, const FString& Result, const FString& ErrorMsg)>& OnFinishedLambda)
{
	// 文本翻译
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

//
void UOneEngineSDKSubsystem::EnterAccountCancellation()
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::GetUserTokenList(FOneFetchUserTokenListDelegate OnGetTokenListDelegate)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
	OnGetTokenListDelegate.ExecuteIfBound(false, -1, "", TArray<FOneUserInfo>());
}

void UOneEngineSDKSubsystem::GetUserTokenListLambda(
	const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)>& OnFinishedLambda)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
	if (OnFinishedLambda)
	{
		OnFinishedLambda(false, -1, "", TArray<FOneUserInfo>());
	}
}

void UOneEngineSDKSubsystem::GuestLogin()
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::TokenLogin(const FString& Token, const FString& Uid, EOneEngineThirdType ThirdType)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::ThirdLogin(EOneEngineThirdType ThirdType, bool bForcedLogin)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::QueryUserActiveQualification(const FString& ServerId,
	FOneQueryUserActiveQualificationResultDelegate OneQueryUserActiveQualificationResultDelegate)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::QueryUserActiveQualificationLambda(const FString& ServerId,
	const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg,
		const FOneActiveQualificationInfo& QualificationInfo)>& OnFinishedLambda)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::ActivateDevice(const FString& ServerId,
	FOneActivateDeviceResultDelegate OneActivateDeviceResultDelegate)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

void UOneEngineSDKSubsystem::ActivateDeviceLambda(const FString& ServerId,
	const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code,
		const FString& ErrorMsg)>& OnFinishedLambda)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
}

bool UOneEngineSDKSubsystem::ACELogin(const FString& accountId, int accountTypeCommandId, int worldId,
	FOneOnGetClientPacket delegate)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
	return false;
}

bool UOneEngineSDKSubsystem::ACEClientPacketReceive(const TArray<uint8>& data)
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
	return false;
}

bool UOneEngineSDKSubsystem::ACELogout()
{
	ONE_LOG_WARNING_PRINTF("Unsupported function call: %s", *FString(__FUNCTION__));
	return false;
}

void UOneEngineSDKSubsystem::GetIpInfo(const FString& Ip, FOnGetIpInfoResultDelegate Delegate)
{
}

/// 全球独有的 评价接口和 修改屏幕亮度接口
float UOneEngineSDKSubsystem::GetScreenBrightness()
{
	return 0.0f;
}

void UOneEngineSDKSubsystem::SetScreenBrightness(float BrightnessValue)
{
}

void UOneEngineSDKSubsystem::RecoverScreenBrightness()
{
}

///保持屏幕常亮
void UOneEngineSDKSubsystem::SwitchScreenPermanentBrightnessState(bool bIsTurnOn)
{
}


///评价接口
void UOneEngineSDKSubsystem::InAppRequestStoreReview(FOneGenericResultDelegate Delegate)
{
}

/// AppLink Ios 有这个参数
void UOneEngineSDKSubsystem::RequestStoreReview(FString AppLink)
{
}

/// 获取渲染器配置文件地址
FString UOneEngineSDKSubsystem::GetRenderConfigFilePath()
{
	return "";
}

// 安全锁相关接口
void UOneEngineSDKSubsystem::UnlockSafeLockUsingPushNotification(const FString& RoleName, const FString& ServerName,
	FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
}

void UOneEngineSDKSubsystem::UnlockSafeLockUsingDynamicCode(const FString& DynamicCode, const FString& RoleName,
	const FString& ServerName,
	FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
}

void UOneEngineSDKSubsystem::StopUnlockSafeLockUsingPushNotification()
{
}

// 全球独有的打开 Naver 论坛
void UOneEngineSDKSubsystem::OpenCommunityByGame(EOneNaverGameType Type, const FString& Pid, bool Scheduled,
	FOneLoadDelegate LoadCallback, FOneUnloadDelegate UnloadCallback,
	FOneInGameMenuDelegate InGameMenuCallback)
{
}

void UOneEngineSDKSubsystem::TerminateCommunity()
{
}

void UOneEngineSDKSubsystem::GetProviderPushState(FOnGetPushStateDelegate Delegate)
{
}

void UOneEngineSDKSubsystem::SetAnalyticsCollectionEnabled(bool bEnable)
{
}

bool UOneEngineSDKSubsystem::IsInstalledApp(EOneShareAppTarget AppTarget)
{
	return false;
}

FString UOneEngineSDKSubsystem::GetAppId()
{
	return KAppID;
}

#endif