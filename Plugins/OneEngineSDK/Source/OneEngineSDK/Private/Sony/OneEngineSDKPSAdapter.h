﻿#pragma once

#if ENGINE_SUPPORT_SONY


#include "CoreMinimal.h"
#include "OneEngineSDKHelper.h"
#include "OneEngineSDKSubsystem.h"

class IONEEngineSDKPSInterface
{
public:
	IONEEngineSDKPSInterface() = default;
	virtual ~IONEEngineSDKPSInterface() = default;

	// 登录
	virtual void Login() = 0;
	// 登出
	virtual void Logout(int Type) = 0;
	// 获取用户信息
	virtual FOneUserInfo GetUserInfo() = 0;
	// 获取渠道 ID
	virtual FString GetChannelId() = 0;
	// 检查是否登录
	virtual bool CheckLoggedInStatus() = 0;
	// 获取渠道媒体 ID
	virtual FString GetChannelMediaId() = 0;
	// 获取平台类型
	virtual int32 GetPlatformOS() = 0;
	// 获取渠道平台
	virtual FString GetChannelPlatform() = 0;
	// 获取防沉迷信息
	virtual void RequestAntiAddictionInfo(TFunction<void(const FOneAntiAddictionInfo& Info)> Callback) = 0;
	// 开始防沉迷监测
	virtual void StartAntiAddictionMonitoring(
		const FString& ServerId,
		const FString& RoleId,
		TFunction<void(const FOneAntiAddictionInfo& Info)> Callback
	) = 0;
	// 结束防沉迷监测
	virtual void StopAntiAddictionMonitoring() = 0;
	// 初始化
	virtual int InitializeSDK(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate) = 0;
	// 获取用户位置
	virtual void GetUserLocation(TFunction<void(const FOneUserLocationInfo& LocationInfo)> OnFinishedLambda) = 0;
	// 设置是否为 Debug 模式
	virtual void SetDebugMode(bool bDebugMode) = 0;
	// 获取是否为 Debug 模式
	virtual bool GetDebugMode() = 0;
	// 打开用户中心
	virtual void OpenUserCenter() = 0;
	virtual int OpenComplianceOnWebView() = 0;
	//
	virtual void SetLanguage(const FString& Language) = 0;
	virtual FString GetCurrentLanguage() = 0;

	// 获取送审状态
	virtual bool ExaminStatus() = 0;
	
	// 埋点
	virtual void Track(const FString& EventName, const TMap<FString, FString>& EventParams, FString TaskId = "2") = 0;
	

	// 初始化配置
	FString AppId;
	FString AppKey;
	FString ConfigContent;
	// One 角色信息
	FOneRoleInfo PlayerRoleInfo;
	FString PsOnlineId;
	//OnLogoutCallback
	TFunction<void(bool bSuccess, int Code, const FString& Msg)> OnLogoutCallback;

	/**
	 *  登录结果回调
	 *  @param bSuccess 是否成功
	 *  @param Code 错误码
	 *  @param Msg 错误信息
	 *  @param UserId 用户ID
	 */
	TFunction<void(bool bSuccess, int Code, const FString& Msg, const FOneUserInfo& UserInfo)> OnLoginResultReceived;


	// PS 平台专属
	virtual int GetFriendList(int Offset, int Count, TFunction<void(bool bSuccess, const FString& Result, int ErrCode, const FString& ErrInfo)>) = 0;

	virtual int GetBlockList(int Offset, int Count, TFunction<void(bool bSuccess, const FString& Result, int ErrCode, const FString& ErrInfo)>) = 0;

	virtual int GetProductInfoList(int32 ServiceLabel, FString CategoryLabel,
	                               TFunction<void(bool bSuccess, const FString& Result, int ErrCode, const FString& ErrInfo)> CallBack) = 0;

	// 使用兑换码
	virtual void UseRedeemCode(const FString& ExchangeCode, const FOneRoleInfo& RoleInfo, const TMap<FString, FString>& ExtraInfo,
	                           TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> CallBack) = 0;

	virtual void GetRoleList(const FString& ServerId, TFunction<void(bool bSuccess, int32 Code, const FString& Msg, const TArray<FOneURCRoleInfo>& RoleList)> CallBack) = 0;

	// 获取句柄	
	void* GetHandle() const;
	virtual void ShowLoading() = 0;
	virtual void HideLoading() = 0;

	void SetExtraTrackInfo(const TMap<FString, FString>& Info);

protected:
	void* Handle{nullptr};
	bool bDebugMode{false};
	TMap<FString, FString> ExtraTrackInfo;
	FString CurrentLanguage;
};

class FONEEngineSDKPSAdapter
{
public:
	FONEEngineSDKPSAdapter();
	~FONEEngineSDKPSAdapter();
	static FONEEngineSDKPSAdapter& Get();
	TSharedPtr<IONEEngineSDKPSInterface> GetPimp();
	int SDKRegionType;

	EOneEngineSDKRegionType GetRegionType();

	void SetDebugMode(bool bDebug);
	bool GetDebugMode() const;

	void MakePimp();

private:
	TSharedPtr<IONEEngineSDKPSInterface> PImp;
	bool bInitialized{false};
	bool bDebugMode{false};
};

#endif
