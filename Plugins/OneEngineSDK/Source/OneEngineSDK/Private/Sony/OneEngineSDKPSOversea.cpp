﻿#if ENGINE_SUPPORT_SONY

#include "OneEngineSDKPSOversea.h"

#include "../Views/PSOneUIManager.h"
#include "Async/Async.h"
#include "JsonObjectConverter.h"
#include "OneEngineSDKPSSubsystem.h"
#include "OneEngineSDKPSUtils.h"

#if WITH_EDITOR
#include "Editor/EditorEngine.h"
#include "Editor/UnrealEdEngine.h"
#else
#include "Engine/GameEngine.h"
#endif

#if PLATFORM_PS4 || PLATFORM_PS5
#include "libglobal.h"
#include "libpspay.h"
#include "wmc/logger.h"
#else
// 纯粹就是为了智能提示
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/wmc/logger.h"
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/libglobal.h"
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/libpspay.h"
#endif

#if PLATFORM_PS4
#include <system_service.h>
#endif

#define LOCTEXT_NAMESPACE "ONEEngineSDKPSOversea"

constexpr static int32 PSOverseaLoginTypePerfectAccount = 1;	// 绑定完美账号登录类型
constexpr static int32 PSOverseaLoginTypePSN = 2;				// PSN 登录类型

FONEEngineSDKPSOversea::FONEEngineSDKPSOversea()
{
#if PLATFORM_PS4
	// PS4 平台检查确认键是 O 还是 X
	int value = 0;
	sceSystemServiceParamGetInt(SCE_SYSTEM_SERVICE_PARAM_ID_ENTER_BUTTON_ASSIGN, &value);
	if (value == SCE_SYSTEM_PARAM_ENTER_BUTTON_ASSIGN_CIRCLE)
	{
		bEnterButtonAssignCircle = true;	// 确认键是 O
	}
#endif

	// 获取 UI 管理器实例
	auto UIManager = UPSOneUIManager::Get();
	// 使用弱指针防止循环引用和悬挂指针
	auto WeakUIManager = TWeakObjectPtr<UPSOneUIManager>(UIManager);
	// 设置为海外环境 (相对于中国大陆)
	UIManager->SetMainlandEnv(false);

	// 绑定用户同意隐私协议后的回调
	UIManager->OnAgreementAccepted = [this, WeakUIManager]()
	{
		ONE_LOG_PRINTF("User accepted the privacy agreement.");
		// 异步在后台线程执行 SDK 调用
		FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
			[this, WeakUIManager]()
			{
				// 调用 SDK 接口，标记用户已同意协议
				// 注意：第二个参数文档未说明，传入 nullptr
				global_sdk_manager_set_agree(true, nullptr);
				AgreePrivacyAgreement = true;

				// 同步更新当前的登录选项状态 (可能根据同意状态变化)
				SyncUpdateLoginOption();
				// 同意后自动开始登录流程，切换回游戏线程执行
				FONEEngineSDKPSUtils::ExecInGameThread(
					[this, WeakUIManager]()
					{
						// 如果用户已经登录 (例如之前选择了 PSN 快速登录)，则隐藏协议界面
						if (HasLoggedIn())
						{
							ONE_LOG_PRINTF("User already has a login type selected, hiding user agreement UI.");
							WeakUIManager->HideUserAgreement();
						}
						// 开始登录流程
						StartLogin(
							// 登录结果回调
							[this](int Ret, const FString& Message, const FOneUserInfo& UserInfo)
							{
								if (Ret == 0)
								{
									ONE_LOG_PRINTF("Login successful after accepting agreement.");
									OnLoginResultReceived(true, 0, Message, UserInfo);
								}
								else
								{
									ONE_LOG_ERROR_PRINTF(
										"Login failed after accepting agreement. Code = %d, ErrorMessage = %s", Ret, *Message);
									// 登录失败也需要通知上层逻辑
									OnLoginResultReceived(false, Ret, Message, UserInfo);
								}
							},
							// StartLogin 的参数，这里传空表示使用默认登录方式 (PSN 或 绑定账号)
							TEXT(""), TEXT(""), TEXT(""), false, true /* bSkipAgreement = true 因为刚同意过 */);
					});
			});
	};

	// 绑定打开用户协议 URL 的回调
	UIManager->OnOpenUserAgreementURL = [this]()
	{
		ONE_LOG_PRINTF("Opening User Agreement URL via SDK.");
		// 异步调用 SDK 打开用户协议链接
		FONEEngineSDKPSUtils::AsyncExecInBackgroundThread([this]() { global_sdk_manager_open_contract(); });
	};

	// 绑定打开隐私政策 URL 的回调
	UIManager->OnOpenPrivacyPolicyURL = [this]()
	{
		ONE_LOG_PRINTF("Opening Privacy Policy URL via SDK.");
		// 异步调用 SDK 打开隐私政策链接
		FONEEngineSDKPSUtils::AsyncExecInBackgroundThread([this]() { global_sdk_manager_open_privacy(); });
	};

	// 绑定打开法律条款 URL 的回调
	UIManager->OnOpenLegalTermsURL = [this]()
	{
		ONE_LOG_PRINTF("Opening Legal Terms URL via SDK.");
		// 异步调用 SDK 打开法律条款/服务协议链接
		FONEEngineSDKPSUtils::AsyncExecInBackgroundThread([this]() { global_sdk_manager_open_protocol(); });
	};

	// 绑定用户选择 "绑定完美账号" 选项的回调
	UIManager->OnAccountBinding = [this, WeakUIManager]()
	{
		ONE_LOG_PRINTF("User chose 'Account Binding'. Switching to login page.");
		// 切换到游戏线程操作 UI
		FONEEngineSDKPSUtils::ExecInGameThread(
			[this, WeakUIManager]()
			{
				// 隐藏协议界面
				WeakUIManager->HideUserAgreement();
				ONE_LOG_PRINTF("Setting login type to PwrdAccount (1).");
				// 设置登录选项为 1 (绑定完美账号)
				LoginOption = PSOverseaLoginTypePerfectAccount;
				// 调用登录流程
				Login();
			});
	};

	// 绑定用户选择 "PSN 登录" 选项的回调
	UIManager->OnPsnLogin = [this, WeakUIManager]()
	{
		ONE_LOG_PRINTF("User chose 'PSN Login'.");
		// 切换到游戏线程操作 UI
		FONEEngineSDKPSUtils::ExecInGameThread(
			[this, WeakUIManager]()
			{
				WeakUIManager->HideUserAgreement();	   // 隐藏协议界面
				ONE_LOG_PRINTF("Setting login type to PSN (2).");
				// 设置登录选项为 2 (PSN 登录)
				LoginOption = PSOverseaLoginTypePSN;
				// 调用登录流程
				Login();
			});
	};

	// 绑定账号密码登录的回调 (从登录界面触发)
	UIManager->OnLoginWithPwd = [this](const FString& Username, const FString& Password)
	{
		ONE_LOG_PRINTF("Login attempt using email and password.");
		LoginWithEmail(Username, Password);
	};

	// 绑定发送登录验证码的回调 (从登录界面触发)
	UIManager->OnSendLoginVerificationCode = [this](const FString& email)
	{
		ONE_LOG_PRINTF("Requesting login verification code for email: %s", *email);
		SendLoginVerificationCode(email);
	};

	// 绑定邮箱验证码登录的回调 (从登录界面触发)
	UIManager->OnLoginWithVerificationCode = [this](const FString& Email, const FString& VerificationCode)
	{
		ONE_LOG_PRINTF("Login attempt using email and verification code.");
		LoginWithVerificationCode(Email, VerificationCode);
	};

	// 发送绑定邮箱验证码的回调 (用于绑定/解绑等场景)
	UIManager->OnSendEmailVerificationCode =
		[this, WeakUIManager](const FString& Email, UPSOneUIManager::EVerificationCodeType VerifyType,
			TWeakObjectPtr<class UUserWidget> CurrentWidget,	// 当前操作的 Widget (用于上下文)
			TFunction<void(bool Success, const FString& JsonStr, int Code, const FString& Err)> Callback)	 // 结果回调给 UI
	{
		// 检查 Callback 是否有效
		if (!Callback)
		{
			ONE_LOG_ERROR_PRINTF("Callback is not valid in OnSendEmailVerificationCode.");
			return;
		}

		ShowLoading();	  // 显示加载指示器
		// 异步调用 SDK 发送验证码
		FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
			[this, WeakUIManager, Email, VerifyType, CurrentWidget, Callback]()
			{
				FString ResponseStr;
				int Ret = -1;
				bool ValidEmail = false;
				// 包含@符号
				// 长度在 4-100 字符内
				if (Email.Contains("@") && Email.Len() >= 4 && Email.Len() <= 100)
				{
					ValidEmail = true;
				}
				else
				{
					Ret = 1;
					ResponseStr = LOCTEXT("InvalidEmailFormat", "邮箱格式错误，请重新输入").ToString();
				}

				if (ValidEmail)
				{
					char* Response = nullptr;
					// 调用 SDK 接口获取邮箱登录/操作验证码
					Ret = global_sdk_manager_login_email_get_code(TCHAR_TO_UTF8(*Email), &Response);

					// 处理 SDK 返回的响应
					if (Response != nullptr)
					{
						ResponseStr = UTF8_TO_TCHAR(Response);
						global_sdk_manager_free(Response);
					}
				}
				// 切回游戏线程更新 UI 和执行回调
				FONEEngineSDKPSUtils::ExecInGameThread(
					[this, WeakUIManager, Ret, VerifyType, CurrentWidget, Callback, ResponseStr]()
					{
						HideLoading();	  // 隐藏加载指示器
						if (Ret == 0)
						{
							Callback(true, ResponseStr, Ret, TEXT(""));
						}
						else
						{
							// 验证码发送失败，提示
							Callback(false, TEXT(""), Ret, ResponseStr);
							HandleErrorCode(Ret, ResponseStr);
						}
					});
			});
	};

	// 绑定邮箱绑定操作的回调
	UIManager->OnBindEmail = [this, WeakUIManager](const FString& Email, const FString& VerificationCode,
								 TWeakObjectPtr<class UUserWidget> CurrentWidget,
								 TFunction<void(bool Success, const FString& JsonStr, int Code, const FString& Err)> Callback)
	{
		if (!Callback)
		{
			ONE_LOG_ERROR_PRINTF("Callback is not valid in OnBindEmail.");
			return;
		}

		ShowLoading();
		// 异步调用 SDK 绑定第三方账号 (邮箱)
		FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
			[this, WeakUIManager, Email, VerificationCode, CurrentWidget, Callback]()
			{
				// 8 是 SDK 定义的邮箱绑定类型 (ThirdType)
				constexpr int32 EmailBindType = 8;
				char* Response = nullptr;
				FString ResponseStr;
				// 调用 SDK 接口绑定第三方账号
				int Ret = global_sdk_manager_bind_third_account(TCHAR_TO_UTF8(*Email), TCHAR_TO_UTF8(*VerificationCode), &Response);

				if (Response != nullptr)
				{
					ResponseStr = UTF8_TO_TCHAR(Response);
					global_sdk_manager_free(Response);
				}

				// 如果绑定成功，解析返回的用户信息并更新本地缓存
				if (Ret == 0)
				{
					// 更新本地绑定状态
					BoundAccounts.Add(EmailBindType, true);	   // 8 是邮箱绑定类型 (ThirdType)
					ParseAndUpdateUserInfo(ResponseStr);
				}

				// 切回游戏线程处理结果
				FONEEngineSDKPSUtils::ExecInGameThread(
					[this, WeakUIManager, Ret, ResponseStr, Callback]()
					{
						HideLoading();
						if (Ret == 0)	 // 绑定成功
						{
							// 更新 UI 上显示的已绑定账号列表
							WeakUIManager->UpdateBoundAccounts(BoundAccounts);
							Callback(true, ResponseStr, 0, TEXT(""));
						}
						else	// 绑定失败
						{
							Callback(false, TEXT(""), Ret, ResponseStr);	// 将 SDK 原始错误信息传给 UI
							HandleErrorCode(Ret, ResponseStr);				// 同时用通用错误处理逻辑显示提示
						}
					});
			});
	};
	// 绑定邮箱解绑操作的回调
	UIManager->OnUnbindEmail = [this, WeakUIManager](const FString& Email, TWeakObjectPtr<class UUserWidget> CurrentWidget,
								   TFunction<void(bool Success, const FString& JsonStr, int Code, const FString& Err)> Callback)
	{
		if (!Callback)
		{
			ONE_LOG_ERROR_PRINTF("Callback is not valid in OnUnbindEmail.");
			return;
		}

		ShowLoading();
		// 异步调用 SDK 解绑第三方账号
		FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
			[this, WeakUIManager, Callback]()
			{
				// 8 是 SDK 定义的邮箱绑定类型 (ThirdType)
				// TODO: 使用常量或枚举代替魔术数字 8
				constexpr int32 EmailBindType = 8;
				char* Response = nullptr;
				FString ResponseStr;
				// 调用 SDK 接口解绑第三方账号
				int Ret = global_sdk_manager_unbind_third_account(EmailBindType, &Response);

				if (Response != nullptr)
				{
					// 解绑接口返回的信息通常包含更新后的用户信息
					ResponseStr = UTF8_TO_TCHAR(Response);
					// 重要：释放 SDK 分配的内存
					global_sdk_manager_free(Response);
					Response = nullptr;
				}

				// 如果解绑成功，解析返回的用户信息并更新本地缓存
				if (Ret == 0)
				{
					// 更新本地绑定状态
					BoundAccounts.Add(EmailBindType, false);
					ParseAndUpdateUserInfo(ResponseStr);
				}

				// 切回游戏线程处理结果
				FONEEngineSDKPSUtils::ExecInGameThread(
					[this, WeakUIManager, Ret, ResponseStr, Callback]()
					{
						HideLoading();
						if (Ret == 0)	 // 解绑成功
						{
							// 更新 UI 上显示的已绑定账号列表
							WeakUIManager->UpdateBoundAccounts(BoundAccounts);
							Callback(true, ResponseStr, 0, TEXT(""));
						}
						else	// 解绑失败
						{
							Callback(false, TEXT(""), Ret, ResponseStr);
							HandleErrorCode(Ret, ResponseStr);
						}
					});
			});
	};

	// 绑定检查是否允许解绑 PSN 的回调
	UIManager->OnCheckUnbindPSN = [this]() -> bool	  // 返回值告知 UI 是否允许解绑
	{
		// 检查逻辑：如果当前账号只绑定了 PSN (没有绑定邮箱等其他方式)，则不允许解绑
		int trueCount = 0;	  // 计算已绑定的账号数量
		for (const auto& pair : BoundAccounts)
		{
			ONE_LOG_PRINTF("Checking bound account type: %d, IsBound: %d", pair.Key, pair.Value);
			if (pair.Value)	   // Value 为 true 表示已绑定
			{
				trueCount++;
			}
		}

		// 如果绑定数量小于等于 1，则不允许解绑
		if (trueCount <= 1)
		{
			ONE_LOG_WARNING_PRINTF("PSN unbind not allowed because it's the only bound account (Bound count: %d).", trueCount);
			// 切回游戏线程显示提示信息
			FONEEngineSDKPSUtils::ExecInGameThread(
				[this]()
				{
					// 使用本地化文本提示用户需要先绑定邮箱
					HandleErrorCode(1, LOCTEXT("UnbindPSNNotAllowed", "请绑定邮箱后再进行解绑操作").ToString());
				});
			return false;	 // 不允许解绑
		}

		ONE_LOG_PRINTF("PSN unbind is allowed (Bound count: %d).", trueCount);
		return true;	// 允许解绑
	};

	// 绑定解绑 PSN 二次确认的回调
	UIManager->OnShowConfirmDialog = [this](const FString& Message, TFunction<void(bool bOk)> Callback)
	{
		ONE_LOG_PRINTF("Showing OK/Cancel dialog: %s", *Message);
		FONEEngineSDKPSUtils::ShowOKCancelDialog(Message, Callback);
	};

	// 绑定解绑 PSN 操作的回调
	UIManager->OnUnbindPSN = [this, WeakUIManager](TFunction<void(bool Success)> Callback)	  // 结果回调给 UI
	{
		if (!Callback)
		{
			ONE_LOG_ERROR_PRINTF("Callback is not valid in OnUnbindPSN.");
			return;
		}

		ShowLoading();
		// 异步调用 SDK 解绑第三方账号
		FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
			[this, WeakUIManager, Callback]()
			{
				char* Response = nullptr;
				FString ResponseStr;

				// 23 是 SDK 定义的 PSN 绑定类型 (ThirdType)
				// TODO: 使用常量或枚举代替魔术数字 23
				constexpr int32 PsnBindType = 23;
				int Ret = global_sdk_manager_unbind_third_account(PsnBindType, &Response);
				if (Response != nullptr)
				{
					ResponseStr = UTF8_TO_TCHAR(Response);
					// 重要：释放 SDK 分配的内存
					global_sdk_manager_free(Response);
					Response = nullptr;
				}

				ONE_LOG_PRINTF("Unbind PSN result: Code = %d, Message = %s", Ret, *ResponseStr);
				// 切回游戏线程处理结果
				FONEEngineSDKPSUtils::ExecInGameThread(
					[this, WeakUIManager, Ret, ResponseStr, Callback]()
					{
						HideLoading();
						// 注：解绑 PSN 成功后，理论上会触发登出或要求重新登录，这里只回调成功状态
						if (Ret == 0)	 // 解绑成功
						{
							Callback(true);

							// 解绑成功后，登出
							Logout(2);
						}
						else	// 解绑失败
						{
							Callback(false);
							HandleErrorCode(Ret, ResponseStr);
						}
					});
			});
	};

	// 绑定申请删除账号操作的回调
	UIManager->OnApplyDeleteAccount = [this, WeakUIManager](TFunction<void(bool Success)> Callback)	   // 结果回调给 UI
	{
		if (!Callback)
		{
			ONE_LOG_ERROR_PRINTF("Callback is not valid in OnApplyDeleteAccount.");
			return;
		}

		ShowLoading();
		// 异步调用 SDK 申请删除账号
		FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
			[this, WeakUIManager, Callback]()
			{
				char* Response = nullptr;
				FString ResponseStr;
				// 调用 SDK 接口申请删除账号
				int Ret = global_sdk_manager_delete_account(&Response);
				if (Response != nullptr)
				{
					ResponseStr = UTF8_TO_TCHAR(Response);
					// 重要：释放 SDK 分配的内存
					global_sdk_manager_free(Response);
					Response = nullptr;
				}
				ONE_LOG_PRINTF("Apply delete account result: Code = %d, Message = %s", Ret, *ResponseStr);
				// 切回游戏线程处理结果
				FONEEngineSDKPSUtils::ExecInGameThread(
					[this, WeakUIManager, Ret, ResponseStr, Callback]()
					{
						HideLoading();
						// 注：申请删除成功后，账号会进入删除流程，可能需要登出
						if (Ret == 0)	 // 申请成功
						{
							Callback(true);
							// 申请删除成功后，登出
							Logout(2);
						}
						else	// 申请失败
						{
							Callback(false);
							HandleErrorCode(Ret, ResponseStr);
						}
					});
			});
	};

	// 绑定用户中心点击设备下线按钮的回调
	UIManager->OnOfflineDevice = [this, WeakUIManager](const FString& DeviceId)
	{
		ONE_LOG_PRINTF("User requested to offline device: %s", *DeviceId);
		ShowLoading();
		// 调用封装好的设备下线函数
		OfflineDevice(DeviceId,
			// 设备下线结果回调
			[this, WeakUIManager, DeviceId](bool Success, const FString& JsonStr, int Code, const FString& Err)
			{
				if (Success)	// 下线成功
				{
					ONE_LOG_PRINTF("Device offline successful for %s. Refreshing online devices list.", *DeviceId);
					// 刷新设备列表
					GetOnlineDevices(
						// 获取设备列表结果回调
						[this, WeakUIManager](
							bool GetListSuccess, const FString& ListJsonStr, int GetListCode, const FString& GetListErr)
						{
							HideLoading();	  // 隐藏加载
							ONE_LOG_PRINTF("GetOnlineDevices after offline result: Success=%s, Code=%d",
								GetListSuccess ? TEXT("true") : TEXT("false"), GetListCode);
							if (GetListSuccess)
							{
								// 更新 UI 上的设备列表
								WeakUIManager->UpdateDevices(ListJsonStr);
							}
							else
							{
								// 获取列表失败，可能需要提示用户
								HandleErrorCode(GetListCode, GetListErr);
							}
						});
				}
				else	// 下线失败
				{
					HideLoading();
					ONE_LOG_ERROR_PRINTF("Device offline failed for %s. Code: %d, Error: %s", *DeviceId, Code, *Err);
					HandleErrorCode(Code, Err);
				}
			});
	};
	// 绑定用户中心 Tab 页切换的回调
	UIManager->OnUserCenterTabIndexChanged = [this, WeakUIManager](int Index)
	{
		ONE_LOG_PRINTF("User center tab index changed to %d", Index);
		// 定义 Tab 索引对应的功能，增加可读性
		enum class EUserCenterTab : int32
		{
			AccountBinding = 0,		 // 账户绑定信息
			AccountInfo = 1,		 // 账号信息
			RealNameAuth = 2,		 // 实名认证
			DeviceManagement = 3,	 // 设备管理
			LegalTerms = 4,			 // 法律条款
			Other = 5				 // 其他
		};

		// 如果切换到设备管理 Tab (索引 3)
		if (Index == static_cast<int32>(EUserCenterTab::DeviceManagement))
		{
			ONE_LOG_PRINTF("Switched to Device Management tab, fetching online devices.");
			// 获取在线设备列表
			GetOnlineDevices(
				// 获取结果回调
				[this, WeakUIManager](bool Success, const FString& JsonStr, int Code, const FString& Err)
				{
					ONE_LOG_PRINTF("GetOnlineDevices for tab switch result: Success=%s, Code=%d",
						Success ? TEXT("true") : TEXT("false"), Code);
					if (Success)
					{
						// 更新 UI 设备列表
						WeakUIManager->UpdateDevices(JsonStr);
					}
					else
					{
						// 获取失败处理
						HandleErrorCode(Code, Err);
					}
				});
		}
	};

	// 绑定用户中心点击登出/切换账号按钮的回调
	UIManager->OnLogout = [this]()
	{
		ONE_LOG_PRINTF("User requested logout/switch account from user center.");
		// 调用登出接口，参数 2 表示是用户中心切换账号触发
		Logout(2);
	};

	// 绑定 UI Widget 可见性变化的回调 (通知 Subsystem)
	UIManager->OnUIWidgetVisibilityChanged = [this](UPSOneUIManager::EUIWidgetType WidgetType, bool bVisible)
	{
		UWorld* World = FONEEngineSDKPSUtils::GetWorld();	 // 获取当前世界
		if (!World)
		{
			ONE_LOG_WARNING_PRINTF("Cannot get World in OnUIWidgetVisibilityChanged.");
			return;
		}
		UGameInstance* GameInstance = World->GetGameInstance();
		if (!GameInstance)
		{
			ONE_LOG_WARNING_PRINTF("Cannot get GameInstance in OnUIWidgetVisibilityChanged.");
			return;
		}
		// 获取 OneEngineSDK 子系统
		if (UOneEngineSDKPSSubsystem* Subsystem = GameInstance->GetSubsystem<UOneEngineSDKPSSubsystem>())
		{
			// 广播 Widget 可见性变化事件
			Subsystem->OnWidgetVisibilityChanged.Broadcast(static_cast<int>(WidgetType), bVisible);
		}
		else
		{
			ONE_LOG_WARNING_PRINTF("OneEngineSDKPSSubsystem not found in OnUIWidgetVisibilityChanged.");
		}
	};

	// 绑定用户在删除确认界面点击 "确认删除" 的回调
	UIManager->OnClickDeleteAccount =
		[this](const FString& Uid, const FString& Token, TFunction<void(bool bShouldClose)> Callback)	 // 结果回调给 UI
	{
		if (!Callback)
		{
			ONE_LOG_ERROR_PRINTF("Callback is not valid in OnClickDeleteAccount.");
			return;
		}
		// 检查当前用户信息是否有效
		if (Uid.IsEmpty() || Token.IsEmpty())
		{
			ONE_LOG_ERROR_PRINTF("Cannot permanently delete account: Uid or Token is empty.");
			Callback(false);
			HandleErrorCode(-1, TEXT(""));	  // 提供错误提示
			return;
		}

		ONE_LOG_PRINTF("User confirmed permanent account deletion for UserId: %s", *Uid);
		ShowLoading();
		// 异步调用 SDK 永久删除账号接口
		FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
			[this, Uid, Token, Callback]()
			{
				char* Response = nullptr;
				FString ResponseStr;
				// 调用 SDK 接口永久删除账号
				int Ret = global_sdk_manager_delete_account_permanently(TCHAR_TO_UTF8(*Uid), TCHAR_TO_UTF8(*Token), &Response);
				if (Response != nullptr)
				{
					ResponseStr = UTF8_TO_TCHAR(Response);
					// 重要：释放 SDK 分配的内存
					global_sdk_manager_free(Response);
					Response = nullptr;
				}
				ONE_LOG_PRINTF("Permanent account deletion result: Code = %d, Message = %s", Ret, *ResponseStr);
				// 切回游戏线程处理结果
				FONEEngineSDKPSUtils::ExecInGameThread(
					[this, Ret, ResponseStr, Callback]()
					{
						HideLoading();
						// 清空用户信息
						ResetUserInfo();
						// 删除操作，无论成功失败，都应该关闭删除界面
						Callback(true);
						if (Ret != 0)
						{
							// 删除失败，提示用户
							HandleErrorCode(Ret, ResponseStr);
						}
						// 删除操作，无论如何都认为登录失败
						OnLoginResultReceived(false, Ret, ResponseStr, CurrentUserInfo);
					});
			});
	};

	// 绑定用户在删除确认界面点击 "恢复账号" 的回调
	UIManager->OnClickRestoreAccount = [this](const FString& Uid, TFunction<void(bool bShouldClose)> Callback)	  // 结果回调给 UI
	{
		if (!Callback)
		{
			ONE_LOG_ERROR_PRINTF("Callback is not valid in OnClickRestoreAccount.");
			return;
		}

		ONE_LOG_PRINTF("User requested to restore account: %s", *Uid);
		ShowLoading();
		// 异步调用 SDK 恢复账号接口
		FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
			[this, Uid, Callback]()
			{
				char* Response = nullptr;
				FString ResponseStr;
				// 调用 SDK 接口恢复账号
				int Ret = global_sdk_manager_restore_account(TCHAR_TO_UTF8(*IntermediateUserInfo), &Response);
				if (Response != nullptr)
				{
					ResponseStr = UTF8_TO_TCHAR(Response);
					// 重要：释放 SDK 分配的内存
					global_sdk_manager_free(Response);
					Response = nullptr;
				}

				ONE_LOG_PRINTF("Restore account result: Code = %d, Message = %s", Ret, *ResponseStr);
				// 如果恢复成功，解析返回的用户信息并更新本地缓存
				if (Ret == 0)
				{
					Ret = ParseAndUpdateUserInfo(ResponseStr);
				}

				// 切回游戏线程处理结果
				FONEEngineSDKPSUtils::ExecInGameThread(
					[this, Ret, ResponseStr, Callback]()
					{
						HideLoading();
						// 恢复操作，无论成功失败，都应该关闭恢复界面
						Callback(true);

						if (Ret == 0)	 // 恢复成功
						{
							ONE_LOG_PRINTF("Restore account process completed successfully via Login().");
							// 调用登录成功处理函数
							OnLoginResultReceived(true, 0, TEXT(""), CurrentUserInfo);
						}
						else	// 恢复失败
						{
							ResetUserInfo();
							HandleErrorCode(Ret, ResponseStr);
							OnLoginResultReceived(false, Ret, TEXT(""), CurrentUserInfo);
						}
					});
			});
	};

	// 绑定 UI 触发的事件跟踪回调
	UIManager->OnTrackEvent = [this](const FString& EventName, const TMap<FString, FString>& EventData)
	{
		// 调用通用的事件跟踪函数
		Track(EventName, EventData, TEXT(""));	  // TaskId 为空
	};
}

FONEEngineSDKPSOversea::~FONEEngineSDKPSOversea()
{
	// 析构函数，目前为空。可在此处添加必要的资源清理逻辑 (如果 SDK 需要)
}

int FONEEngineSDKPSOversea::InitializeSDK(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate)
{
// 在 Debug 和 Development 构建中启用详细日志记录
#if UE_BUILD_DEBUG || UE_BUILD_DEVELOPMENT
	// 设置特定模块的日志等级为 DEBUG，方便调试
	// wmc_log_set_level(WMC_LOG_LEVEL_INFO, "HTTP"); // HTTP 日志通常信息量较大，可按需开启
	wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "FS");			 // 文件系统相关日志
	wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "PS_PAY");		 // PS 支付相关日志
	wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "GLOBAL_SDK");	 // 全球 SDK 核心日志
	wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "ANALYSIS");		 // 数据分析/打点日志
#endif

	ONE_LOG_PRINTF("Starting OneEngine SDK Initialization (Oversea PSN).");
	// 使用静态变量确保 SDK 只初始化一次
	static bool bInitSuccess = false;
	if (bInitSuccess)
	{
		ONE_LOG_PRINTF("SDK already initialized successfully. Skipping initialization.");
		// 如果已初始化，直接回调成功
		FONEEngineSDKPSUtils::ExecInGameThread([InitDelegate]() { auto _ = InitDelegate.ExecuteIfBound(true, 0, TEXT("")); });
		return 0;	 // 返回 0 表示调用成功 (尽管是跳过的)
	}

	// 检查初始化回调是否有效
	if (!InitDelegate.IsBound())
	{
		ONE_LOG_ERROR_PRINTF("Initialization failed: InitDelegate is not bound.");
		return -1;	  // 返回错误码表示调用失败
	}

	ShowLoading();	  // 显示加载指示器
	// 异步在后台线程执行初始化，避免阻塞游戏线程
	FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
		[this, InitDelegate]
		{
			// 检查配置内容是否为空
			if (ConfigContent.IsEmpty())
			{
				ONE_LOG_ERROR_PRINTF("SDK Initialization failed: ConfigContent is empty.");
				FONEEngineSDKPSUtils::ExecInGameThread(
					[this, InitDelegate]()
					{
						HideLoading();	  // 确保隐藏 Loading
						auto _ = InitDelegate.ExecuteIfBound(false, -1 /* 自定义错误码 */, TEXT("ConfigContent is empty."));
					});
				return;
			}

			// Base64 解码配置内容
			FString InitContent;
			bool bDecodeSuccess = FBase64::Decode(ConfigContent, InitContent);
			if (!bDecodeSuccess)
			{
				ONE_LOG_ERROR_PRINTF("SDK Initialization failed: ConfigContent Base64 decode failed.");
				FONEEngineSDKPSUtils::ExecInGameThread(
					[this, InitDelegate]()
					{
						HideLoading();
						auto _ =
							InitDelegate.ExecuteIfBound(false, -2 /* 自定义错误码 */, TEXT("ConfigContent Base64 decode failed."));
					});
				return;
			}

			// 解析 JSON 配置并添加 App 版本号
			int Code = -1;	  // 初始化 SDK 返回码
			TSharedPtr<FJsonObject> JsonObject;
			TSharedRef<TJsonReader<TCHAR>> JsonReader = TJsonReaderFactory<TCHAR>::Create(InitContent);
			if (FJsonSerializer::Deserialize(JsonReader, JsonObject) && JsonObject.IsValid())
			{
				// 获取项目版本号
				FString AppVersion;
				if (GConfig)
				{
					GConfig->GetString(
						TEXT("/Script/EngineSettings.GeneralProjectSettings"), TEXT("ProjectVersion"), AppVersion, GGameIni);
				}
				if (AppVersion.IsEmpty())
				{
					AppVersion = TEXT("*******");	 // 默认版本号
					ONE_LOG_WARNING_PRINTF("ProjectVersion not found in GGameIni, using default: %s", *AppVersion);
				}
				// 将 App 版本号写入 JSON 对象
				JsonObject->SetStringField(TEXT("appVersion"), AppVersion);
				ONE_LOG_PRINTF("Using App version for SDK initialization: %s", *AppVersion);
			}
			else
			{
				ONE_LOG_ERROR_PRINTF("SDK Initialization failed: Failed to parse JSON config.");
				Code = -60010004;	 // 沿用之前的错误码，表示 JSON 解析失败
									 // 不直接返回，让后续流程处理 Code != 0 的情况
			}

			FString JsonStr;
			// 只有在 JSON 解析成功时才序列化并初始化 SDK
			if (Code != -60010004 && JsonObject.IsValid())
			{
				TSharedRef<TJsonWriter<TCHAR>> JsonWriter = TJsonWriterFactory<TCHAR>::Create(&JsonStr);
				FJsonSerializer::Serialize(JsonObject.ToSharedRef(), JsonWriter);

				if (JsonStr.IsEmpty())
				{
					// 初始化失败 - JSON 序列化失败
					ONE_LOG_ERROR_PRINTF("SDK Initialization failed: JSON serialization resulted in an empty string.");
					Code = -60010004;	 // 复用 JSON 错误码或定义新码
				}
				else
				{
					// 调用 SDK 的初始化函数
					ONE_LOG_PRINTF("Calling global_sdk_manager_init_sdk_with_json with config: %s", *JsonStr);
					Code = global_sdk_manager_init_sdk_with_json(TCHAR_TO_UTF8(*JsonStr));
					ONE_LOG_PRINTF("global_sdk_manager_init_sdk_with_json returned: %d", Code);
				}
			}

			// 如果 SDK 初始化成功 (Code == 0)，则获取支持的绑定类型
			if (Code == 0)
			{
				TArray<int32> BindTypes;	// 存储支持的绑定类型 ID
				char* Response = nullptr;
				FString SupportBindingTypesJson;
				// 调用 SDK 接口获取支持的绑定类型列表 (JSON 格式)
				int Ret = global_sdk_manager_get_binding_types(&Response);
				if (Ret == 0 && Response != nullptr)
				{
					SupportBindingTypesJson = UTF8_TO_TCHAR(Response);
					ONE_LOG_PRINTF("Supported binding types raw response: %s", *SupportBindingTypesJson);
					// 重要：释放 SDK 返回的内存
					global_sdk_manager_free(Response);
					Response = nullptr;

					// 解析 JSON 数组
					TArray<TSharedPtr<FJsonValue>> Array;
					TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(SupportBindingTypesJson);
					if (FJsonSerializer::Deserialize(Reader, Array))
					{
						for (const TSharedPtr<FJsonValue>& Value : Array)
						{
							if (Value.IsValid() && Value->Type == EJson::Number)
							{
								BindTypes.Add(Value->AsNumber());
							}
							else
							{
								ONE_LOG_WARNING_PRINTF("Invalid value found in support binding types JSON array.");
							}
						}
					}
					else
					{
						ONE_LOG_ERROR_PRINTF("Failed to parse supported binding types JSON: %s", *SupportBindingTypesJson);
					}
				}
				else
				{
					ONE_LOG_ERROR_PRINTF("Failed to get supported binding types from SDK. Code = %d", Ret);
					// 初始化仍然算成功，但绑定功能可能受影响
				}

				// 初始化 BoundAccounts 映射，将所有支持的类型标记为未绑定 (false)
				BoundAccounts.Empty();		  // 清空旧数据
				InitBoundAccounts.Empty();	  // 清空初始化时的绑定状态
				for (int32 BindType : BindTypes)
				{
					ONE_LOG_PRINTF("Initializing supported bind type: %d", BindType);
					BoundAccounts.Add(BindType, false);
					InitBoundAccounts.Add(BindType, false);
				}
			}

			// 切回游戏线程执行回调
			FONEEngineSDKPSUtils::ExecInGameThread(
				[this, Code, InitDelegate]()
				{
					HideLoading();	  // 隐藏加载指示器
					if (Code == 0)	  // 初始化成功
					{
						bInitSuccess = true;	// 标记为已成功初始化
						ONE_LOG_PRINTF("SDK Initialization successful.");
						auto _ = InitDelegate.ExecuteIfBound(true, 0, TEXT(""));
					}
					else	// 初始化失败
					{
						bInitSuccess = false;	 // 确保标记为未初始化
						ONE_LOG_ERROR_PRINTF("SDK Initialization failed. Code = %d", Code);
						auto _ = InitDelegate.ExecuteIfBound(false, Code, TEXT(""));
					}
				});
		});
	return 0;	 // 异步任务已启动，返回 0 表示接受了初始化请求
}

void FONEEngineSDKPSOversea::Login()
{
	// Logout(2);
	// FString ConfigPath = TEXT("/download0/global_store/global_store.json");
	// IPlatformFile &PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
	// PlatformFile.DeleteFile(*ConfigPath);

	// 调用通用的登录函数 StartLogin
	StartLogin(
		// 定义登录结果的回调 Lambda
		[this](int Ret, const FString& Message, const FOneUserInfo& UserInfo)
		{
			if (Ret == 0)	 // 登录成功
			{
				ONE_LOG_PRINTF("Login process completed successfully via Login().");
				// 调用登录成功处理函数
				OnLoginResultReceived(true, 0, Message, UserInfo);
			}
			else	// 登录失败
			{
				ONE_LOG_ERROR_PRINTF("Login process failed via Login(). Code = %d, ErrorMessage = %s", Ret, *Message);
				// 调用登录失败处理函数
				OnLoginResultReceived(false, Ret, Message, UserInfo);
			}
		});	   // 使用默认参数调用 StartLogin
}

// 同步调用 SDK 接口，获取并更新当前的 PSN 绑定状态和登录选项
void FONEEngineSDKPSOversea::SyncUpdateLoginOption()
{
	int Ret = -1;
	char* BindingTypeResponse = nullptr;
	// 调用 SDK 获取当前 PSN 账号的绑定类型 (返回 JSON 数组字符串)
	Ret = global_sdk_manager_get_support_types(&BindingTypeResponse);

	int BindTypeFlags = 0;	   // 使用位标记来存储登录选项 (1: 绑定登录，2: 普通登录)
	LoginOptionMap.Empty();	   // 清空旧的映射关系

	if (Ret == 0 && BindingTypeResponse != nullptr)
	{
		const FString ResultString = UTF8_TO_TCHAR(BindingTypeResponse);
		ONE_LOG_PRINTF("Current PSN binding types response from SDK: %s", *ResultString);
		// 重要：释放 SDK 返回的内存
		global_sdk_manager_free(BindingTypeResponse);
		BindingTypeResponse = nullptr;

		// 解析 JSON 数组
		TArray<TSharedPtr<FJsonValue>> Array;
		TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(ResultString);

		if (FJsonSerializer::Deserialize(Reader, Array))
		{
			for (const TSharedPtr<FJsonValue>& Value : Array)
			{
				int32 TypeValue = -1;	 // SDK 返回的类型值

				// 兼容数字和字符串形式的类型值
				if (Value.IsValid())
				{
					if (Value->Type == EJson::Number)
					{
						TypeValue = static_cast<int32>(Value->AsNumber());
					}
					else if (Value->Type == EJson::String)
					{
						// 尝试将字符串转换为整数
						if (!Value->AsString().IsEmpty() && Value->AsString().IsNumeric())
						{
							TypeValue = FCString::Atoi(*Value->AsString());
						}
						else
						{
							ONE_LOG_WARNING_PRINTF(
								"Invalid non-numeric string value in binding types array: %s", *Value->AsString());
						}
					}
					else
					{
						ONE_LOG_WARNING_PRINTF(
							"Unexpected JSON value type (%d) in binding types array.", static_cast<int32>(Value->Type));
					}
				}

				// 根据 SDK 定义的类型值更新登录选项标志位和映射表
				// TODO: 使用常量或枚举代替魔术数字 28, 29
				constexpr int32 BindLoginType = 28;		 // 绑定登录类型
				constexpr int32 NormalLoginType = 29;	 // 普通登录类型

				if (TypeValue == BindLoginType)
				{
					BindTypeFlags |= 1 << 0;	// 设置第 0 位 (绑定登录)
					// LoginOption = 1 时，对应的 SDK 类型是 "28"
					LoginOptionMap.Add(1, BindLoginType);
					ONE_LOG_PRINTF("Found Bind Login type (%d).", BindLoginType);
				}
				else if (TypeValue == NormalLoginType)
				{
					BindTypeFlags |= 1 << 1;	// 设置第 1 位 (普通登录)
					// LoginOption = 2 时，对应的 SDK 类型是 "29"
					LoginOptionMap.Add(2, NormalLoginType);
					ONE_LOG_PRINTF("Found Normal Login type (%d).", NormalLoginType);
				}
				else if (TypeValue != -1)	 // 忽略无效值
				{
					ONE_LOG_WARNING_PRINTF("Unknown binding type value received from SDK: %d", TypeValue);
				}
			}
		}
		else
		{
			ONE_LOG_ERROR_PRINTF("Failed to parse binding types JSON: %s", *ResultString);
		}
	}
	else
	{
		ONE_LOG_ERROR_PRINTF("Failed to get current PSN binding type from SDK. Code = %d", Ret);
		// 获取失败时，不清空 LoginOption，保留旧值
		return;	   // 直接返回，不更新 LoginOption
	}

	// 如果成功获取并解析了有效的绑定类型，则更新 LoginOption
	if (BindTypeFlags != 0)
	{
		// 只有当获取到有效的绑定类型时才更新 LoginOption
		ONE_LOG_PRINTF("Updating LoginOption from %d to %d based on SDK binding types.", LoginOption, BindTypeFlags);
		LoginOption = BindTypeFlags;
	}
	else
	{
		// 如果 SDK 返回空数组或无法解析，也可能需要保留旧值或设置为特定状态
		ONE_LOG_WARNING_PRINTF("No valid binding types found from SDK response. LoginOption (%d) remains unchanged.", LoginOption);
	}
}

// 检查当前是否已有有效的登录选项 (绑定登录或普通登录)
bool FONEEngineSDKPSOversea::HasLoggedIn()
{
	// LoginOption 使用位标记：
	// 第 0 位 (值为 1): 绑定登录 (PwrdAccount)
	// 第 1 位 (值为 2): 普通登录 (PSN)
	// 如果 LoginOption 是 1 或 2 (只有一个位被设置)，表示已选择明确的登录方式
	// 如果 LoginOption 是 3 (1 | 2)，表示两者都可选，需要用户选择
	// 如果 LoginOption 是 0，表示尚未确定或获取失败
	return LoginOption == 1 || LoginOption == 2;
}

void FONEEngineSDKPSOversea::StartLogin(TFunction<void(int Code, FString Message, const FOneUserInfo& UserInfo)> Function,
	const FString& Email, const FString& Password, const FString& VerificationCode, bool bUseVerificationCode, bool bSkipAgreement)
{
	ONE_LOG_PRINTF("Starting login process (StartLogin)... SkipAgreement=%s, UseVerificationCode=%s",
		bSkipAgreement ? TEXT("true") : TEXT("false"), bUseVerificationCode ? TEXT("true") : TEXT("false"));

	// 检查回调是否有效
	if (!Function)
	{
		ONE_LOG_ERROR_PRINTF("StartLogin failed: Callback function is null.");
		return;
	}

	ShowLoading();	  // 显示加载界面

	// 将登录逻辑放入后台线程执行，避免阻塞游戏
	FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
		[this, Function, Email, Password, VerificationCode, bUseVerificationCode, bSkipAgreement]()
		{
			int Ret = -1;			   // SDK 调用结果
			FOneUserInfo UserInfo;	   // 存储登录成功的用户信息
			FString ErrorMessage;	   // 存储错误信息
			FString Response;		   // 存储 SDK 返回的原始 JSON 字符串
			char* Output = nullptr;	   // SDK 返回的 C 字符串指针

			// 步骤 1: 检查用户协议状态 (除非显式跳过)
			if (!bSkipAgreement)
			{
				// 调用 SDK 获取当前用户是否已同意协议
				bool bAgreed = false;
				global_sdk_manager_get_agree_state(&bAgreed);

				ONE_LOG_PRINTF("Current privacy agreement state: %s", bAgreed ? TEXT("Agreed") : TEXT("Not Agreed"));
				AgreePrivacyAgreement = bAgreed;	// 更新缓存的状态

				// 如果用户尚未同意协议
				if (!AgreePrivacyAgreement)
				{
					// 同步更新登录选项 (因为登录选项可能依赖于协议状态，如首次登录)
					SyncUpdateLoginOption();
					// 切回游戏线程显示用户协议界面
					FONEEngineSDKPSUtils::ExecInGameThread(
						[this, Function, UserInfo]()
						{
							HideLoading();
							AgreePrivacyAgreement = false;	  // 确保状态为 false
							// 调用 CheckIsAgreePrivacy 获取协议内容并显示 UI
							CheckIsAgreePrivacy(
								[this](bool bAgree, const FString& Title, const FString& Content)
								{
									// 设置协议文本并显示 UI
									SetUserAgreementText(FText::FromString(Title), FText::FromString(Content));
									UPSOneUIManager* UIManager = UPSOneUIManager::Get();
									if (UIManager)
									{
										// 显示用户协议界面，参数 false 表示非强制展示，LoginOption 决定显示哪些登录按钮
										UIManager->ShowUserAgreement(false, LoginOption, Title, Content);
									}
									else
									{
										ONE_LOG_ERROR_PRINTF("Failed to get UIManager in CheckIsAgreePrivacy callback.");
									}
								});
							// 注意：此处不调用 Function 回调，因为流程尚未结束，等待用户在 UI 上操作
						});
					return;	   // 中断登录流程，等待用户交互
				}
			}

			// 程序执行到这里，意味着用户已同意协议 或 bSkipAgreement 为 true
			ONE_LOG_PRINTF("User has agreed to the privacy agreement or agreement check was skipped. Proceeding with login.");

			// 清空旧的用户信息
			RawUserInfo.Empty();

			// 步骤 2: 根据参数选择并执行具体的登录方式
			if (bUseVerificationCode && !VerificationCode.IsEmpty() && !Email.IsEmpty())
			{
				// 方式 A: 使用邮箱和验证码登录
				ONE_LOG_PRINTF("Attempting login with email verification code.");
				Ret = global_sdk_manager_login_email_code(TCHAR_TO_UTF8(*Email), TCHAR_TO_UTF8(*VerificationCode), &Output);
			}
			else if (!Password.IsEmpty() && !Email.IsEmpty())
			{
				// 方式 B: 使用邮箱和密码登录
				ONE_LOG_PRINTF("Attempting login with email and password.");
				Ret = global_sdk_manager_login_email(TCHAR_TO_UTF8(*Email), TCHAR_TO_UTF8(*Password), &Output);
			}
			else
			{
				// 方式 C: 使用默认登录类型 (PSN 或 绑定账号，由 LoginOption 决定)
				ONE_LOG_PRINTF("Attempting login with default login type (LoginOption: %d).", LoginOption);

				// 记录旧的登录选项，用于后续检查 SDK 状态是否及时更新
				int OldLoginOption = LoginOption;
				// 再次同步登录选项，确保获取最新的状态
				SyncUpdateLoginOption();

				// 检查：如果 SyncUpdateLoginOption 改变了 LoginOption，但之前已经有选择 (OldLoginOption != 0)，
				// 这可能意味着用户刚选择了登录方式，但 SDK 内部状态尚未完全同步。
				// 在这种情况下，我们优先使用用户刚刚选择的 OldLoginOption。
				if (OldLoginOption != 0 && LoginOption != OldLoginOption)
				{
					ONE_LOG_WARNING_PRINTF(
						"LoginOption (%d) changed after sync, but OldLoginOption (%d) was valid. Restoring to OldLoginOption.",
						LoginOption, OldLoginOption);
					LoginOption = OldLoginOption;
				}

				// 检查当前 LoginOption 是否为有效的单一登录方式 (1 或 2)
				if (!HasLoggedIn())	   // 如果 LoginOption 是 0 (未初始化/错误) 或 3 (两者皆可，需选择)
				{
					// 用户已同意协议，但没有有效的单一登录选项
					ONE_LOG_WARNING_PRINTF(
						"User agreed to privacy agreement, but no specific login type selected (LoginOption: %d). Showing user "
						"agreement UI again.",
						LoginOption);
					// 切回游戏线程，再次显示协议/登录选择界面
					FONEEngineSDKPSUtils::ExecInGameThread(
						[this, Function, UserInfo]()
						{
							HideLoading();
							UPSOneUIManager* UIManager = UPSOneUIManager::Get();
							if (UIManager)
							{
								// 显示用户协议界面，参数 true 表示强制展示 (因为需要选择登录方式), LoginOption 决定显示哪些按钮
								UIManager->ShowUserAgreement(true, LoginOption);
							}
							// 不调用 Function 回调，等待用户选择
						});
					return;	   // 中断登录流程
				}

				// 获取 LoginOption 对应的 SDK 登录类型字符串 ("28" 或 "29")
				const int32 RealLoginType = LoginOptionMap.FindRef(LoginOption);	// 使用 FindRef 避免无效 Key 时添加空条目

				if (RealLoginType == 0)
				{
					// 严重错误：LoginOption 有效 (1 或 2)，但在 LoginOptionMap 中找不到对应的 SDK 类型字符串
					ONE_LOG_ERROR_PRINTF("Critical Error: LoginType string is empty for LoginOption %d.", LoginOption);
					Ret = -1;	 // 设置错误码
					// 跳转到错误处理部分
					goto HandleLoginResult;
				}
				// 调用 SDK 的通用登录接口
				ONE_LOG_PRINTF("Calling global_sdk_manager_login with type: %d", RealLoginType);
				Ret = global_sdk_manager_login(RealLoginType, &Output);
			}

			// 步骤 3: 处理登录结果
			if (Output != nullptr)
			{
				// 将 SDK 返回的 C 字符串转为 FString
				Response = UTF8_TO_TCHAR(Output);
				// 重要：释放 SDK 分配的内存
				global_sdk_manager_free(Output);
				Output = nullptr;
			}

			if (Ret != 0)	 // 登录失败
			{
				ONE_LOG_ERROR_PRINTF("Login failed. SDK returned code: %d. Response: %s", Ret, *Response);
				// 特殊错误码处理
				if (Ret == (-60010011))	   // SDK 要求显示邮箱登录界面
				{
					ONE_LOG_PRINTF("Login requires email input (Code -60010011). Showing email login UI.");
					FONEEngineSDKPSUtils::ExecInGameThread(
						[this]()
						{
							HideLoading();
							UPSOneUIManager* UIManager = UPSOneUIManager::Get();
							if (UIManager)
								UIManager->ShowLogin(false);
						});
					// 此处不调用 Function 回调，等待用户在邮箱登录界面操作
					return;	   // 中断流程
				}
				else if (Ret == (-********))	// 账号处于删除冷却期
				{
					ONE_LOG_PRINTF("Login failed because account is pending deletion (Code -********). Showing deletion prompt.");

					IntermediateUserInfo = Response;	// 保存当前用户信息

					FString DelTipsUserId;
					FString DelTipsToken;
					FString DelTipsContent;

					TSharedPtr<FJsonObject> JsonObject;
					const TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(Response);
					// 注意：增加反序列化成功判断
					if (FJsonSerializer::Deserialize(JsonReader, JsonObject) && JsonObject.IsValid())
					{
						// 解析核心字段：uid, token, username
						// 注意：增加字段存在性和类型检查，防止崩溃
						int64 UID_INT64 = 0;
						if (JsonObject->TryGetNumberField(TEXT("uid"), UID_INT64))	  // uid 可能是 Number 类型
						{
							DelTipsUserId = FString::Printf(TEXT("%lld"), UID_INT64);
						}

						else if (!JsonObject->TryGetStringField(TEXT("uid"), DelTipsUserId))	// 也可能是 String 类型
						{
							ONE_LOG_ERROR_PRINTF("Failed to get 'uid' field or it has wrong type.");
						}

						if (!JsonObject->TryGetStringField(TEXT("token"), DelTipsToken))
						{
							ONE_LOG_ERROR_PRINTF("Failed to get 'token' field.");
						}

						// 获取 recoverExpireMessage
						if (!JsonObject->TryGetStringField(TEXT("recoverExpireMessage"), DelTipsContent))
						{
							ONE_LOG_ERROR_PRINTF(" Failed to get 'recoverExpireMessage' field.");
						}
					}

					if (DelTipsContent.IsEmpty())
					{
						DelTipsContent =
							LOCTEXT("DeleteAccountTips", "您已请求删除此账户。该账户将在指定日期后被删除。您是否仍申请删除该账户？")
								.ToString();	// 稍微修改提示
					}

					// Uid 和 token 必须存在，否则无法显示删除提示
					if (!DelTipsUserId.IsEmpty() && !DelTipsToken.IsEmpty())
					{
						// 切回游戏线程显示删除提示 UI
						FONEEngineSDKPSUtils::ExecInGameThread(
							[this, DelTipsUserId, DelTipsToken, DelTipsContent]()
							{
								HideLoading();
								UPSOneUIManager* UIManager = UPSOneUIManager::Get();
								if (UIManager)
								{
									UIManager->HideLogin();													// 可能需要隐藏登录界面
									UIManager->ShowDelTips(DelTipsContent, DelTipsUserId, DelTipsToken);	// 显示删除提示框
								}
							});
						// 此处不调用 Function 回调，等待用户在删除提示框操作
						return;	   // 中断流程
					}

					// 网络异常或字段缺失，直接显示通用错误提示
					ONE_LOG_ERROR_PRINTF("Failed to parse 'uid' or 'token' from JSON response.");
					Ret = -1;
				}
				else	// 其他登录失败情况
				{
					// 如果 SDK 返回了错误信息 Response，则使用它
					if (!Response.IsEmpty())
					{
						ErrorMessage = Response;
					}
				}
			}
			else	// 登录成功 (Ret == 0)
			{
				ONE_LOG_PRINTF("Login successful. SDK response: %s", *Response);
				// 解析并更新用户信息
				Ret = ParseAndUpdateUserInfo(Response);
				if (Ret != 0)
				{
					// 登录成功但解析用户信息失败
					ONE_LOG_ERROR_PRINTF("Login succeeded but failed to parse user info JSON. Response length: %d. Response: %s",
						Response.Len(), *Response);

					// 即使解析失败，按失败处理
					Ret = -1;
				}
				else
				{
					// 登录且解析成功，ErrorMessage 保持为空
					ErrorMessage = TEXT("");
				}
			}

		// 标签用于跳转处理结果
		HandleLoginResult:
			// 步骤 4: 切换回游戏线程，隐藏 UI 并调用最终回调
			FONEEngineSDKPSUtils::ExecInGameThread(
				[this, Function, Ret, ErrorMessage, UserInfo /* = CurrentUserInfo */]()	   // UserInfo 应该是最新的 CurrentUserInfo
				{
					HideLoading();
					if (Ret != 0)	 // 如果最终结果是失败
					{
						ONE_LOG_ERROR_PRINTF("Finalizing failed login. Code = %d, Message = %s", Ret, *ErrorMessage);
						HandleErrorCode(Ret, ErrorMessage);	   // 显示通用错误提示
					}
					else
					{
						UPSOneUIManager* UIManager = UPSOneUIManager::Get();
						if (UIManager)
							UIManager->HideLogin();	   // 隐藏登录界面
					}

					// 调用最初传入的回调函数，通知上层登录结果
					// 注意：UserInfo 应该传递更新后的 CurrentUserInfo
					if (Function)
					{
						Function(Ret, ErrorMessage, CurrentUserInfo);
					}
				});
		});
}

void FONEEngineSDKPSOversea::LoginWithEmail(const FString& Email, const FString& Password)
{
	ONE_LOG_PRINTF("Calling LoginWithEmail for email: %s", *Email);
	StartLogin(
		// 登录结果回调
		[this](int Ret, const FString& Message, const FOneUserInfo& UserInfo)
		{
			if (Ret == 0)
			{
				ONE_LOG_PRINTF("Login with email successful.");
				OnLoginResultReceived(true, 0, Message, UserInfo);
			}
			else
			{
				ONE_LOG_ERROR_PRINTF("Login with email failed. Code = %d, ErrorMessage = %s", Ret, *Message);
				OnLoginResultReceived(false, Ret, Message, UserInfo);
			}
		},
		Email, Password, TEXT("") /* VerificationCode */, false /* bUseVerificationCode */,
		true /* bSkipAgreement, 因为是 UI 触发 */);
}

// 发送邮箱登录验证码
void FONEEngineSDKPSOversea::SendLoginVerificationCode(const FString& Email)
{
	ONE_LOG_PRINTF("Requesting login verification code for email: %s", *Email);
	if (Email.IsEmpty())
	{
		HandleErrorCode(-1, LOCTEXT("EmailEmptyErrorTip", "邮箱不能为空，请输入邮箱").ToString());
		return;
	}

	// 包含@符号
	// 长度在 4-100 字符内
	if (!Email.Contains("@") || Email.Len() < 4 || Email.Len() > 100)
	{
		HandleErrorCode(-1, LOCTEXT("InvalidEmailFormat", "邮箱格式错误，请重新输入").ToString());
		return;
	}

	ShowLoading();
	// 异步调用 SDK 获取验证码
	FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
		[this, Email]()
		{
			int Ret = -1;
			FString ErrorMessage;
			char* Output = nullptr;
			// 调用 SDK 接口获取验证码
			Ret = global_sdk_manager_login_email_get_code(TCHAR_TO_UTF8(*Email), &Output);
			if (Output != nullptr)
			{
				ErrorMessage = UTF8_TO_TCHAR(Output);
				// 重要：释放 SDK 内存
				global_sdk_manager_free(Output);
				Output = nullptr;
			}
			// 切回游戏线程处理结果
			FONEEngineSDKPSUtils::ExecInGameThread(
				[this, Email, Ret, ErrorMessage]()
				{
					HideLoading();
					if (Ret != 0)
					{
						ONE_LOG_ERROR_PRINTF("Failed to send login verification code. Code = %d, Message = %s", Ret, *ErrorMessage);
						HandleErrorCode(
							Ret, ErrorMessage.IsEmpty() ? LOCTEXT("SendCodeFailed", "发送验证码失败").ToString() : ErrorMessage);
					}
					else
					{
						ONE_LOG_PRINTF("Successfully requested login verification code for email: %s", *Email);
						// 可能需要 UI 提示发送成功
						UPSOneUIManager* UIManager = UPSOneUIManager::Get();
						if (UIManager)
						{
							UIManager->ShowToast(LOCTEXT("CodeSent", "验证码已发送"));
						}
					}
				});
		});
}

// 使用邮箱登录验证码登录
void FONEEngineSDKPSOversea::LoginWithVerificationCode(const FString& Email, const FString& VerificationCode)
{
	ONE_LOG_PRINTF("Calling LoginWithVerificationCode for email: %s", *Email);
	StartLogin(
		// 登录结果回调
		[this](int Ret, const FString& Message, const FOneUserInfo& UserInfo)
		{
			if (Ret == 0)
			{
				ONE_LOG_PRINTF("Login with verification code successful.");
				OnLoginResultReceived(true, 0, Message, UserInfo);
			}
			else
			{
				ONE_LOG_ERROR_PRINTF("Login with verification code failed. Code = %d, ErrorMessage = %s", Ret, *Message);
				OnLoginResultReceived(false, Ret, Message, UserInfo);
			}
		},
		Email, TEXT("") /* Password */, VerificationCode, true /* bUseVerificationCode */, true /* bSkipAgreement */);
}

void FONEEngineSDKPSOversea::Logout(int Type)
{
	ONE_LOG_PRINTF("Logout/Switch Account requested. Type = %d", Type);
	ResetUserInfo();
	// 触发登出回调，通知上层逻辑
	if (OnLogoutCallback)
	{
		// 假设 SDK 调用总是成功 (或者没有返回值表示失败)，回调成功
		OnLogoutCallback(true, 0, TEXT("Logout/Switch account successful."));
	}
	else
	{
		ONE_LOG_WARNING_PRINTF("OnLogoutCallback is not bound.");
	}
}

FOneUserInfo FONEEngineSDKPSOversea::GetUserInfo()
{
	return CurrentUserInfo;
}

FString FONEEngineSDKPSOversea::GetChannelId()
{
	return GetChannelMediaId();
}

bool FONEEngineSDKPSOversea::CheckLoggedInStatus()
{
	// 简单检查 UserId 和 Token 是否非空
	const bool Valid = !CurrentUserInfo.UserId.IsEmpty() && !CurrentUserInfo.Token.IsEmpty();
	return Valid && PsnUserId > 0;
}

FString FONEEngineSDKPSOversea::GetChannelMediaId()
{
#if defined(__ORBIS__)	  // PS4
	return TEXT("67");
#elif defined(__PROSPERO__)	   // PS5
	return TEXT("68");
#else						   // 其他平台或编辑器环境
	// 返回空字符串或特定值表示非 PS 平台
	return TEXT("");	// 或者根据需要返回 "EDITOR" 等
#endif
}

int32 FONEEngineSDKPSOversea::GetPlatformOS()
{
	// TODO: 确认 15 的具体含义，最好使用 SDK 提供的常量或枚举
	return 15;	  // 假设 15 代表 PlayStation 平台
}

FString FONEEngineSDKPSOversea::GetChannelPlatform()
{
#if defined(__ORBIS__)	  // PS4
	return TEXT("67");
#elif defined(__PROSPERO__)	   // PS5
	return TEXT("68");
#else						   // 其他平台或编辑器环境
	return TEXT("");
#endif
}

void FONEEngineSDKPSOversea::RequestAntiAddictionInfo(TFunction<void(const FOneAntiAddictionInfo& Info)> Callback)
{
	ONE_LOG_WARNING_PRINTF("RequestAntiAddictionInfo is not supported in Oversea PSN SDK. Returning default status 0.");
	// 异步切换到游戏线程执行回调
	FONEEngineSDKPSUtils::ExecInGameThread(
		[Callback]()
		{
			if (Callback)
			{
				FOneAntiAddictionInfo Info;
				Info.Status = 0;	// 返回默认状态 0 (通常表示成年或无限制)
				Callback(Info);
			}
		});
}

void FONEEngineSDKPSOversea::StartAntiAddictionMonitoring(
	const FString& ServerId, const FString& RoleId, TFunction<void(const FOneAntiAddictionInfo& Info)> Callback)
{
	ONE_LOG_WARNING_PRINTF("StartAntiAddictionMonitoring is not supported in Oversea PSN SDK. Returning default status 0.");
	// 异步切换到游戏线程执行回调
	FONEEngineSDKPSUtils::ExecInGameThread(
		[Callback]()
		{
			if (Callback)
			{
				FOneAntiAddictionInfo Info;
				Info.Status = 0;	// 返回默认状态 0
				Callback(Info);
			}
		});
}

void FONEEngineSDKPSOversea::StopAntiAddictionMonitoring()
{
	ONE_LOG_WARNING_PRINTF("StopAntiAddictionMonitoring is not supported in Oversea PSN SDK. Doing nothing.");
}

void FONEEngineSDKPSOversea::GetUserLocation(TFunction<void(const FOneUserLocationInfo& LocationInfo)> OnFinishedLambda)
{
	ONE_LOG_PRINTF("Requesting user location information...");
	if (!OnFinishedLambda)
	{
		ONE_LOG_ERROR_PRINTF("GetUserLocation failed: OnFinishedLambda is null.");
		return;
	}

	// 异步调用 SDK 获取 IP 定位信息
	FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
		[this, OnFinishedLambda]	// 使用 this 捕获是安全的，因为 SDK 实例生命周期通常较长
		{
			int Ret = 0;
			FString JsonString;
			char* Output = nullptr;
			// 调用 SDK 接口获取 IP 位置信息
			Ret = global_sdk_manager_get_ip_location(&Output);
			if (Output != nullptr)
			{
				JsonString = UTF8_TO_TCHAR(Output);
				// 重要：释放 SDK 内存
				global_sdk_manager_free(Output);
				Output = nullptr;
			}

			FOneUserLocationInfo LocationInfo;	  // 存储解析结果
			if (Ret == 0 && JsonString.Len() > 0)
			{
				ONE_LOG_PRINTF("Received IP location data: %s", *JsonString);
				// 解析 JSON 字符串
				TSharedPtr<FJsonObject> JsonObject;
				TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(JsonString);
				if (FJsonSerializer::Deserialize(JsonReader, JsonObject) && JsonObject.IsValid())
				{
					// 安全地获取字段值，如果字段不存在或类型不匹配，返回空字符串
					JsonObject->TryGetStringField(TEXT("city"), LocationInfo.City);
					JsonObject->TryGetStringField(TEXT("country"), LocationInfo.Country);
					JsonObject->TryGetStringField(TEXT("countryAbbr"), LocationInfo.CountryAbbr);
					JsonObject->TryGetStringField(TEXT("ip"), LocationInfo.IP);
					JsonObject->TryGetStringField(TEXT("province"), LocationInfo.Province);
					JsonObject->TryGetStringField(TEXT("countryCode"), LocationInfo.CountryCode);
					// 注意：原始代码中获取 Region 并设置 CurrencyCode 的逻辑被注释掉了，这里不添加
					ONE_LOG_PRINTF("Parsed location: Country=%s, Province=%s, City=%s, IP=%s", *LocationInfo.Country,
						*LocationInfo.Province, *LocationInfo.City, *LocationInfo.IP);
				}
				else
				{
					ONE_LOG_ERROR_PRINTF("Failed to parse IP location JSON: %s", *JsonString);
					// 解析失败，LocationInfo 将保持默认值 (空字符串)
				}
			}
			else
			{
				ONE_LOG_ERROR_PRINTF("Failed to get IP location from SDK. Code = %d", Ret);
				// 获取失败，LocationInfo 将保持默认值 (空字符串)
			}

			// 切回游戏线程执行回调
			FONEEngineSDKPSUtils::ExecInGameThread(
				[OnFinishedLambda, LocationInfo]()
				{
					if (OnFinishedLambda)
					{
						OnFinishedLambda(LocationInfo);
					}
				});
		});
}

void FONEEngineSDKPSOversea::SetDebugMode(bool bDebugMode)
{
	// 更新本地缓存状态
	bDebugEnable = bDebugMode;
	// 调用 SDK 接口设置 Debug 开关
	global_sdk_manager_debug_enable(bDebugMode);
	ONE_LOG_PRINTF("SDK Debug mode set to: %s", bDebugMode ? TEXT("Enabled") : TEXT("Disabled"));
	if (bDebugMode)
	{
		wmc_log_set_level(WMC_LOG_LEVEL_INFO, "HTTP");
		wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "FS");
		wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "PS_PAY");
		wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "GLOBAL_SDK");
		wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "ANALYSIS");
	}
	else
	{
		wmc_log_set_level(WMC_LOG_LEVEL_WARN, "HTTP");
		wmc_log_set_level(WMC_LOG_LEVEL_WARN, "FS");
		wmc_log_set_level(WMC_LOG_LEVEL_WARN, "PS_PAY");
		wmc_log_set_level(WMC_LOG_LEVEL_WARN, "GLOBAL_SDK");
		wmc_log_set_level(WMC_LOG_LEVEL_WARN, "ANALYSIS");
	}
}

bool FONEEngineSDKPSOversea::GetDebugMode()
{
	return bDebugEnable;	// 返回本地缓存的状态
}

void FONEEngineSDKPSOversea::OpenUserCenter()
{
	// 必须在游戏线程调用以操作 UI
	check(IsInGameThread());

	// 检查用户是否已登录
	if (!CheckLoggedInStatus())	   // 使用封装好的检查函数
	{
		ONE_LOG_ERROR_PRINTF("Cannot open user center: User is not logged in.");
		return;
	}

	ONE_LOG_PRINTF("Opening user center...");
	UPSOneUIManager* UIManager = UPSOneUIManager::Get();
	if (!UIManager)
	{
		ONE_LOG_ERROR_PRINTF("Cannot open user center: UIManager is null.");
		return;
	}

	// 序列化用户信息 JSON
	const FString UserInfoJsonString = FormatUserCenterData();

	// 更新 UI 的用户信息
	ONE_LOG_PRINTF("Updating User Info for User Center: %s", *UserInfoJsonString);
	UIManager->UpdateUserInfo(UserInfoJsonString);

	ONE_LOG_PRINTF("Updating Bound Accounts for User Center.");
	UIManager->UpdateBoundAccounts(BoundAccounts);

	// 显示用户中心界面
	UIManager->ShowUserCenter();
}

int FONEEngineSDKPSOversea::OpenComplianceOnWebView()
{
	FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
		[this]()
		{
			int Result = global_sdk_manager_open_protocol();
			if (Result != 0)
			{
				ONE_LOG_ERROR_PRINTF("Open Compliance On WebView failed, Result: %d", Result);
			}
		});
	return 0;
}

void FONEEngineSDKPSOversea::SetLanguage(const FString& Language)
{
	if (Language.IsEmpty())
	{
		ONE_LOG_WARNING_PRINTF("SetLanguage called with empty language string. Ignoring.");
		return;
	}
	// 更新本地缓存
	CurrentLanguage = Language;
	// 调用 SDK 接口设置语言 (需要转换为 UTF8)
	global_sdk_manager_reset_sdk_language(TCHAR_TO_UTF8(*Language));
	ONE_LOG_PRINTF("SDK language set to: %s", *Language);
}

FString FONEEngineSDKPSOversea::GetCurrentLanguage()
{
	// 返回本地缓存的语言设置
	return CurrentLanguage;
}

bool FONEEngineSDKPSOversea::ExaminStatus()
{
	bool bExaminStatus = false;
	global_sdk_manager_get_shkg_state(&bExaminStatus);
	return bExaminStatus;
}

void FONEEngineSDKPSOversea::Track(const FString& EventName, const TMap<FString, FString>& EventParams, FString TaskId)
{
	if (EventName.IsEmpty())
	{
		ONE_LOG_WARNING_PRINTF("Track called with empty event name. Ignoring.");
		return;
	}

	// 创建 JSON 对象用于存储事件参数
	TSharedPtr<FJsonObject> EventJsonObject = MakeShareable(new FJsonObject());

	// 添加固定的额外跟踪信息 (如果存在)
	if (PsOnlineId.Len() > 0)
	{
		EventJsonObject->SetStringField(TEXT("OnlineID"), PsOnlineId);
	}
	// 添加通过 SetExtraTrackInfo 设置的额外参数
	for (const auto& Param : ExtraTrackInfo)
	{
		EventJsonObject->SetStringField(Param.Key, Param.Value);
	}

	// 添加本次事件的特定参数
	for (const auto& Param : EventParams)
	{
		EventJsonObject->SetStringField(Param.Key, Param.Value);
	}

	FString ParamsJsonString;
	// FJsonObjectWrapper 方式
	FJsonObjectWrapper Wrapper;
	Wrapper.JsonObject = EventJsonObject;
	if (!Wrapper.JsonObjectToString(ParamsJsonString))
	{
		ONE_LOG_ERROR_PRINTF("Failed to serialize event parameters to JSON string for event: %s", *EventName);
		ParamsJsonString = TEXT("{}");	  // 发送空 JSON 对象以防 SDK 崩溃
	}

	if (!ParamsJsonString.IsEmpty())
	{
		ONE_LOG_PRINTF("Tracking event - Name: '%s', TaskID: '%s', Params: %s", *EventName, *TaskId, *ParamsJsonString);
		// 调用 SDK 的事件跟踪接口 (需要转换为 UTF8)
		// 注意：原始代码 TaskId 未使用，这里也保持未使用
		global_sdk_manager_track_event(TCHAR_TO_UTF8(*EventName), TCHAR_TO_UTF8(*ParamsJsonString));
	}
}

int FONEEngineSDKPSOversea::GetFriendList(int Offset, int Count,
	TFunction<void(bool bSuccess, const FString& Result, int ErrCode, const FString& ErrInfo)> OnGetFriendsResult)
{
	// 异步执行，避免阻塞游戏线程
	AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask,
		[Offset, Count, OnGetFriendsResult]()
		{
			char* Output = nullptr;
			FString Result;
			// 调用底层 SDK 获取好友列表
			// 注意：需要确认 global_sdk_manager_get_friends 在各种错误情况下 Output 的状态 (例如是否为 nullptr 或包含错误信息)
			int Ret = global_sdk_manager_get_friends(Offset, Count, &Output);
			if (Ret == 0 && Output != nullptr)
			{
				Result = FString(UTF8_TO_TCHAR(Output));
				// 释放 SDK 分配的内存
				global_sdk_manager_free(Output);
				Output = nullptr;	 // 防止悬挂指针
			}
			// 改进日志：更清晰地描述 SDK 调用结果
			ONE_LOG_PRINTF("GetFriends SDK call returned %d. Result JSON: %s", Ret, *Result);
			// 切换回游戏线程处理回调
			AsyncTask(ENamedThreads::GameThread,
				[Result, Ret, OnGetFriendsResult]()
				{
					if (OnGetFriendsResult)
					{
						if (Ret == 0)
						{
							// 成功：返回好友列表 JSON 字符串
							// 注意：调用方需要解析 Result JSON，处理可能的解析失败
							OnGetFriendsResult(true, Result, 0, TEXT(""));
						}
						else
						{
							// 失败：返回错误码和可能的错误信息 (此时 Result 可能为空或包含 SDK 错误描述)
							OnGetFriendsResult(false, TEXT(""), Ret, Result);
						}
					}
				});
		});
	return 0;	 // 注意：此函数总是同步返回 0，实际结果通过回调异步返回
}

int FONEEngineSDKPSOversea::GetBlockList(int Offset, int Count,
	TFunction<void(bool bSuccess, const FString& Result, int ErrCode, const FString& ErrInfo)> OnGetBlockingUsersResult)
{
	// 异步执行
	AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask,
		[Offset, Count, OnGetBlockingUsersResult]()
		{
			char* Output = nullptr;
			FString Result;
			// 调用底层 SDK 获取屏蔽用户列表
			// 注意：需要确认 global_sdk_manager_get_blocking_users 在各种错误情况下 Output 的状态
			int Ret = global_sdk_manager_get_blocking_users(Offset, Count, &Output);
			if (Ret == 0 && Output != nullptr)
			{
				Result = FString(UTF8_TO_TCHAR(Output));
				global_sdk_manager_free(Output);
				Output = nullptr;
			}

			// 改进日志
			ONE_LOG_PRINTF("GetBlockingUsers SDK call returned %d. Result JSON: %s", Ret, *Result);
			// 切换到游戏线程
			AsyncTask(ENamedThreads::GameThread,
				[Result, Ret, OnGetBlockingUsersResult]()
				{
					if (OnGetBlockingUsersResult)
					{
						if (Ret == 0)
						{
							// 成功：返回屏蔽列表 JSON
							// 注意：调用方需要解析 Result JSON
							OnGetBlockingUsersResult(true, Result, 0, TEXT(""));
						}
						else
						{
							// 失败：返回错误码和信息
							OnGetBlockingUsersResult(false, TEXT(""), Ret, Result);
						}
					}
				});
		});
	return 0;	 // 同样，总是同步返回 0
}

int FONEEngineSDKPSOversea::GetProductInfoList(int32 ServiceLabel, FString CategoryLabel,
	TFunction<void(bool bSuccess, const FString& Result, int ErrCode, const FString& ErrInfo)> CallBack)
{
	// 异步执行
	AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask,
		[ServiceLabel, CategoryLabel, CallBack]()
		{
			char* Output = nullptr;
			FString Result;
			// 调用底层 SDK 获取商品信息
			// 注意：需要确认 global_sdk_manager_get_products 在各种错误情况下 Output 的状态
			int Ret = global_sdk_manager_get_products(ServiceLabel, TCHAR_TO_UTF8(*CategoryLabel), &Output);
			if (Ret == 0 && Output != nullptr)
			{
				Result = FString(UTF8_TO_TCHAR(Output));
				global_sdk_manager_free(Output);
				Output = nullptr;
			}
			// 改进日志
			ONE_LOG_PRINTF("GetProductInfoList SDK call returned %d. Result JSON: %s", Ret, *Result);
			// 切换到游戏线程
			AsyncTask(ENamedThreads::GameThread,
				[Result, Ret, CallBack]()
				{
					if (CallBack)
					{
						if (Ret == 0)
						{
							// 成功：返回商品列表 JSON
							// 注意：调用方需要解析 Result JSON
							CallBack(true, Result, 0, TEXT(""));
						}
						else
						{
							// 失败：返回错误码和信息
							CallBack(false, TEXT(""), Ret, Result);
						}
					}
				});
		});
	return 0;	 // 总是同步返回 0
}

void FONEEngineSDKPSOversea::UseRedeemCode(const FString& ExchangeCode, const FOneRoleInfo& RoleInfo,
	const TMap<FString, FString>& ExtraInfo, TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> CallBack)
{
	// 构造 ExtraInfo 的 JSON 字符串
	// 注意：这种手动拼接 JSON 的方式容易出错，特别是当 Value 包含特殊字符时。建议使用 FJsonObjectConverter 或 FJsonSerializer
	// 来构建。
	FString Extra = TEXT("{");
	if (ExtraInfo.Num() > 0)
	{
		for (auto& Pair : ExtraInfo)
		{
			// // 简单处理，未对 Key 和 Value 中的引号等特殊字符做转义
			Extra += TEXT("\"") + Pair.Key + TEXT("\"") + TEXT(":") + TEXT("\"") + Pair.Value + TEXT("\"") + TEXT(",");
		}
		Extra.RemoveFromEnd(TEXT(","));
	}
	Extra += TEXT("}");

	// 异步执行
	FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
		[ExchangeCode, RoleInfo, Extra, CallBack]()
		{
			char* Output = nullptr;
			FString Result;

			// 调用底层 SDK 验证兑换码
			// 注意：FCString::Atoi 在输入非数字字符串时会返回 0，可能导致逻辑错误。应考虑使用更健壮的转换方式或进行输入验证。
			// 注意：需要确认 global_sdk_manager_redeem_code_verify 在各种错误情况下 Output 的状态
			int Ret = global_sdk_manager_redeem_code_verify(TCHAR_TO_UTF8(*RoleInfo.RoleId), FCString::Atoi(*RoleInfo.Level),
				FCString::Atoi(*RoleInfo.Vip), TCHAR_TO_UTF8(*RoleInfo.ServerId), TCHAR_TO_UTF8(*ExchangeCode),
				TCHAR_TO_UTF8(*Extra), &Output);

			if (Ret == 0 && Output != nullptr)
			{
				Result = FString(UTF8_TO_TCHAR(Output));
				global_sdk_manager_free(Output);
				Output = nullptr;
			}
			// 改进日志
			ONE_LOG_PRINTF("UseRedeemCode (global_sdk_manager_redeem_code_verify) SDK call returned %d. Result: %s", Ret, *Result);
			// 切换到游戏线程
			FONEEngineSDKPSUtils::ExecInGameThread(
				[Result, Ret, CallBack]()
				{
					if (CallBack)
					{
						if (Ret == 0)
						{
							// 成功：Msg 通常包含成功信息或空字符串
							CallBack(true, 0, Result);
						}
						else
						{
							// 失败：Code 为错误码，Msg 可能包含错误描述
							CallBack(false, Ret, Result);
						}
					}
				});
		});
}

void FONEEngineSDKPSOversea::GetRoleList(const FString& ServerId,
	TFunction<void(bool bSuccess, int32 Code, const FString& Msg, const TArray<FOneURCRoleInfo>& RoleList)> CallBack)
{
	// 异步执行
	FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
		[ServerId, CallBack]()
		{
			char* Output = nullptr;
			FString Result;
			// 调用底层 SDK 获取角色列表
			// 注意：需要确认 global_sdk_manager_get_roles 在各种错误情况下 Output 的状态
			int Ret = global_sdk_manager_get_roles(TCHAR_TO_UTF8(*ServerId), &Output);
			if (Ret == 0 && Output != nullptr)
			{
				Result = FString(UTF8_TO_TCHAR(Output));
				global_sdk_manager_free(Output);
				Output = nullptr;
			}
			// 改进日志
			ONE_LOG_PRINTF("GetRoleList SDK call returned %d. Result JSON: %s", Ret, *Result);
			// 切换到游戏线程
			FONEEngineSDKPSUtils::ExecInGameThread(
				[Result, Ret, CallBack]()
				{
					if (CallBack)
					{
						TArray<FOneURCRoleInfo> RoleList;
						if (Ret == 0)
						{
							// 解析返回的 JSON 数组
							TArray<TSharedPtr<FJsonValue>> List;
							const TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(Result);
							// 注意：增加反序列化成功判断
							if (FJsonSerializer::Deserialize(JsonReader, List))	   //&& List.Num() > 0) // 允许空列表
							{
								for (const TSharedPtr<FJsonValue>& i : List)
								{
									// 注意：增加对 Value 类型的检查，以及字段是否存在的检查，防止崩溃
									if (i.IsValid() && i->Type == EJson::Object)
									{
										TSharedPtr<FJsonObject> roleObject = i->AsObject();
										if (roleObject.IsValid())
										{
											FOneURCRoleInfo role;
											// 建议使用 TryGetStringField 等方法增加健壮性
											roleObject->TryGetStringField(TEXT("userId"), role.UserId);
											roleObject->TryGetStringField(TEXT("roleId"), role.RoleId);
											roleObject->TryGetStringField(TEXT("roleName"), role.RoleName);
											roleObject->TryGetStringField(TEXT("serverId"), role.ServerId);
											roleObject->TryGetStringField(TEXT("serverName"), role.ServerName);
											roleObject->TryGetStringField(TEXT("lastLogin"), role.LastLoginTime);
											// 对于数字类型，先检查是否存在且为 Number
											double level = 0.0, gender = 0.0, occupation = 0.0;
											if (roleObject->TryGetNumberField(TEXT("lev"), level))
											{
												role.Level = FString::SanitizeFloat(level);
											}
											if (roleObject->TryGetNumberField(TEXT("gender"), gender))
											{
												role.Gender = FString::SanitizeFloat(gender);
											}
											if (roleObject->TryGetNumberField(TEXT("occupation"), occupation))
											{
												role.Occupation = FString::SanitizeFloat(occupation);
											}
											RoleList.Add(role);
										}
										else
										{
											// 日志：记录 JSON 对象无效
											ONE_LOG_PRINTF("GetRoleList: Invalid role object in JSON array.");
										}
									}
									else
									{
										// 日志：记录非对象类型的元素
										ONE_LOG_PRINTF("GetRoleList: Non-object element found in JSON array.");
									}
								}
								CallBack(true, 0, Result, RoleList);	// 即使解析部分失败，也认为接口调用成功，返回已解析的角色
							}
							else
							{
								// JSON 解析失败
								ONE_LOG_PRINTF("GetRoleList: Failed to deserialize JSON: %s", *Result);
								CallBack(false, -1, TEXT("Failed to parse role list JSON"), RoleList);	  // 返回特定错误码
							}
						}
						else
						{
							// SDK 调用失败
							CallBack(false, Ret, Result, RoleList);
						}
					}
				});
		});
}

void FONEEngineSDKPSOversea::ShowLoading()
{
	// 确保在游戏线程执行 UI 操作
	FONEEngineSDKPSUtils::ExecInGameThread([this]() { UPSOneUIManager::Get()->ShowLoading(); });
}

void FONEEngineSDKPSOversea::HideLoading()
{
	// 确保在游戏线程执行 UI 操作
	FONEEngineSDKPSUtils::ExecInGameThread([this]() { UPSOneUIManager::Get()->HideLoading(); });
}

void FONEEngineSDKPSOversea::CheckIsAgreePrivacy(TFunction<void(bool Agree, const FString& Title, const FString& Content)> Callback)
{
	// 如果本地缓存已同意，则直接回调
	if (AgreePrivacyAgreement)
	{
		FONEEngineSDKPSUtils::ExecInGameThread(
			[Callback]()
			{
				if (Callback)	 // 增加回调有效性检查
				{
					Callback(true, TEXT(""), TEXT(""));
				}
			});
	}
	else	// 否则，尝试从 SDK 获取协议内容
	{
		// 异步获取，避免阻塞当前线程 (虽然当前函数可能在任何线程调用，但 SDK 调用最好异步)
		FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
			[this, Callback]()
			{
				FString PrivacyTitle;
				FString Content;
				bool bNeedShow = false;	   // 标记是否需要显示协议（即 SDK 调用成功且解析成功）

				char* PrivacyCstr = nullptr;
				// 调用 SDK 获取隐私协议内容
				// 注意：确认 global_sdk_manager_get_privacyContents 返回值和错误情况
				int PrivacyRet = global_sdk_manager_get_privacyContents(&PrivacyCstr);
				if (PrivacyRet == 0)
				{
					if (PrivacyCstr != nullptr)
					{
						FString PrivacyJsonString = FString(UTF8_TO_TCHAR(PrivacyCstr));
						TSharedPtr<FJsonObject> JsonObject;	   // 移出 if 作用域
						const TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(PrivacyJsonString);
						// 注意：增加反序列化成功判断
						if (FJsonSerializer::Deserialize(JsonReader, JsonObject) && JsonObject.IsValid())
						{
							// 注意：增加字段存在性检查
							if (JsonObject->TryGetStringField(TEXT("playstationTitle"), PrivacyTitle) &&
								JsonObject->TryGetStringField(TEXT("playstationContent"), Content))
							{
								bNeedShow = true;	 // 成功获取标题和内容
							}
							else
							{
								// 日志：记录缺少必要字段
								ONE_LOG_PRINTF("CheckIsAgreePrivacy: Missing 'playstationTitle' or 'playstationContent' in JSON.");
							}
						}
						else
						{
							// 日志：记录 JSON 解析失败
							ONE_LOG_PRINTF(
								"CheckIsAgreePrivacy: Failed to deserialize privacy content JSON: %s", *PrivacyJsonString);
						}
						// 无论成功失败，都需要释放内存
						global_sdk_manager_free(PrivacyCstr);
						PrivacyCstr = nullptr;
					}
					else
					{
						// 日志：记录 SDK 返回成功但指针为空
						ONE_LOG_PRINTF(
							"CheckIsAgreePrivacy: global_sdk_manager_get_privacyContents returned 0 but output is null.");
					}
				}
				else
				{
					// 日志：记录 SDK 调用失败
					ONE_LOG_PRINTF("CheckIsAgreePrivacy: global_sdk_manager_get_privacyContents failed with code %d", PrivacyRet);
				}

				// 切换到游戏线程回调
				FONEEngineSDKPSUtils::ExecInGameThread(
					[bNeedShow, PrivacyTitle, Content, Callback]()
					{
						if (Callback)	 // 增加回调有效性检查
						{
							if (bNeedShow)
							{
								// 需要显示协议（未同意，且成功获取到内容）
								Callback(false, PrivacyTitle, Content);
							}
							else
							{
								// 不需要显示协议（可能已同意，或获取协议内容失败）
								// 这里根据产品逻辑决定：如果获取失败，是算作同意还是不同意？
								// 当前逻辑是：获取失败则不弹出协议，行为类似已同意，但 Agree 参数仍为 false
								// 或者，如果获取失败应该强制弹出一个默认提示？
								// Callback(false, TEXT(""), TEXT("")); // 或者根据策略调整
								// 如果获取失败需要用户重试，可能需要传递错误信息
								Callback(false, TEXT(""), TEXT(""));	// 保持原逻辑，不显示
							}
						}
					});
			});
	}
}

// 设置用户协议界面显示的文本 (PS 平台特定)
void FONEEngineSDKPSOversea::SetUserAgreementText(const FText& TitleText, const FText& HintText)
{
	UserAgreementTitle = TitleText;
	UserAgreementContent = HintText;
}

// 处理 SDK 返回的错误码
void FONEEngineSDKPSOversea::HandleErrorCode(int Code, FString Message)
{
	// 特定错误码：用户 Token 失效，执行登出操作
	if (Code == 20001)	  // 建议定义为常量 EOneSDKErrorCode::TokenInvalid = 20001
	{
		ONE_LOG_PRINTF("HandleErrorCode: Received error code %d (Token Invalid?), logging out.", Code);
		Logout(0);	  // 参数 0 的含义需要确认，通常表示本地登出
		return;		  // 处理完登出后直接返回
	}

	// 如果 Message 不为空，尝试解析为 JSON 并提取更友好的提示信息
	if (!Message.IsEmpty())
	{
		TSharedPtr<FJsonObject> JsonObject;	   // 移出 if
		const TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(Message);
		// 尝试反序列化
		if (FJsonSerializer::Deserialize(JsonReader, JsonObject) && JsonObject.IsValid())
		{
			FString ResultMessage;
			// 优先尝试 "message" 字段
			if (!JsonObject->TryGetStringField(TEXT("message"), ResultMessage) || ResultMessage.IsEmpty())
			{
				// 其次尝试 "msg" 字段
				JsonObject->TryGetStringField(TEXT("msg"), ResultMessage);
			}

			// 如果成功提取到信息，则显示
			if (!ResultMessage.IsEmpty())
			{
				UPSOneUIManager::Get()->ShowToast(FText::FromString(ResultMessage));
				return;	   // 显示了提取的信息，直接返回
			}
			// 如果 JSON 解析成功但没有 message 或 msg 字段，或者字段为空，则降级显示原始 Message (可能不是用户友好的)
			// 或者也可以选择不显示原始 JSON 字符串
			// else {
			//     UPSOneUIManager::Get()->ShowToast(FText::FromString(Message));
			// }
		}
		else
		{
			// Message 不是有效的 JSON 字符串，直接显示原始 Message
			// 注意：原始 Message 可能是任何字符串，不一定适合直接展示给用户
			UPSOneUIManager::Get()->ShowToast(FText::FromString(Message));
		}
		return;	   // 无论是否解析成功，处理完 Message 后返回
	}

	if (Code == (-********))
	{
		// 报错：当前登录账号已绑定其他账号，请先解绑
		const FText Content = LOCTEXT("AccountAlreadyBound", "当前登录账号已绑定其他账号，请先解绑");
		UPSOneUIManager::Get()->ShowToast(Content);
		return;
	}

	// 如果 Message 为空，根据错误码范围显示通用提示
	// 建议将这些魔法数字定义为常量或枚举
	// 例如：const int32 NetworkErrorStart = -********; const int32 NetworkErrorEnd = -********;
	if ((-******** <= Code && Code <= -********) || Code == -1)	   // 通用操作失败范围？
	{
		const FText Content = FText::FormatOrdered(
			LOCTEXT("OprationFailed", "操作失败，请稍后再试 ({0})"), FText::AsNumber(Code));	// 显示错误码帮助排查
		UPSOneUIManager::Get()->ShowToast(Content);
		return;
	}

	// 支付或网络错误范围
	if ((-******** <= Code && Code <= -********) || (-******** <= Code && Code <= -********))
	{
		int DisplayCode = Code;	   // 用于显示的错误码
#if PLATFORM_PS4 || PLATFORM_PS5
		// 特定平台下，获取更详细的支付错误码
		if (Code == -********)	  // 支付模块通用错误码？
		{
			int PSPayErr = wmc_pspay_errno();	 // 获取 PS 平台特定的支付错误码
			// 可以根据 PSPayErr 的值显示更具体的错误信息，如果映射已知的话
			DisplayCode = PSPayErr;	   // 使用更具体的错误码进行显示
			ONE_LOG_PRINTF("HandleErrorCode: PS Payment error. Original code: %d, PSPayErr: %d", Code, PSPayErr);
		}
#endif
		// 显示网络异常或支付相关错误提示
		const FText Content = FText::FormatOrdered(LOCTEXT("NetworkOrPaymentException", "网络异常，请稍后再试 ({0})"),
			FText::AsNumber(DisplayCode));	  // 显示最终确定的错误码
		UPSOneUIManager::Get()->ShowToast(Content);
		return;
	}

	// 对于其他未明确处理的错误码，可以考虑记录日志或显示一个默认错误提示
	ONE_LOG_PRINTF("HandleErrorCode: Unhandled error code %d with empty message.", Code);
}

// 获取当前账号在线的设备列表
void FONEEngineSDKPSOversea::GetOnlineDevices(
	TFunction<void(bool Success, const FString& Result, int ErrCode, const FString& ErrInfo)> Callback)
{
	FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
		[this, Callback]()
		{
			char* Response = nullptr;
			FString Result;
			// 调用 SDK 获取在线设备列表
			// 注意：确认 global_sdk_manager_get_login_device 的错误情况和 Response 格式
			int Ret = global_sdk_manager_get_login_devices(&Response);
			if (Ret == 0 && Response != nullptr)
			{
				Result = FString(UTF8_TO_TCHAR(Response));
				global_sdk_manager_free(Response);
				Response = nullptr;
			}

			// 改进日志
			ONE_LOG_PRINTF(
				"GetOnlineDevices (global_sdk_manager_get_login_device) SDK call returned %d. Result JSON: %s", Ret, *Result);
			// 切换到游戏线程回调
			FONEEngineSDKPSUtils::ExecInGameThread(
				[Result, Ret, Callback]()
				{
					if (Callback)	 // 检查回调有效性
					{
						FString ErrInfo;
						if (Ret == 0)
						{
							ErrInfo = TEXT("");
						}
						else
						{
							ErrInfo = FText::FormatOrdered(
								LOCTEXT("GetOnlineDevicesFailed", "获取在线设备列表失败。({0})"), FText::AsNumber(Ret))
										  .ToString();	  // 带上错误码
						}
						Callback(Ret == 0, Result, Ret, ErrInfo);
					}
				});
		});
}

// 强制下线指定设备
void FONEEngineSDKPSOversea::OfflineDevice(
	const FString& DeviceId, TFunction<void(bool Success, const FString& Result, int ErrCode, const FString& ErrInfo)> Callback)
{
	FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(
		[this, DeviceId, Callback]()
		{
			// 调用 SDK 移除设备
			// 注意：确认 global_sdk_manager_remove_login_device 的返回值和错误情况
			int Ret = global_sdk_manager_remove_login_device(TCHAR_TO_UTF8(*DeviceId), nullptr);	// 第三个参数用途？
			// 改进日志
			ONE_LOG_PRINTF(
				"OfflineDevice (global_sdk_manager_remove_login_device) SDK call for device %s returned %d.", *DeviceId, Ret);
			// 切换到游戏线程回调
			FONEEngineSDKPSUtils::ExecInGameThread(
				[Ret, Callback]()
				{
					if (Callback)	 // 检查回调有效性
					{
						FString ErrInfo;
						if (Ret == 0)
						{
							ErrInfo = TEXT("");
						}
						else
						{
							ErrInfo =
								FText::FormatOrdered(LOCTEXT("OfflineDeviceFailed", "设备下线失败。({0})"), FText::AsNumber(Ret))
									.ToString();	// 带上错误码
						}
						// Result 固定为空字符串
						Callback(Ret == 0, TEXT(""), Ret, ErrInfo);
					}
				});
		});
}

// 解析并更新用户信息 (从登录或用户信息接口返回的 JSON)，使用场景：
// 登录成功、
// 绑定第三方账号成功后、
// 解绑第三方账号成功后、
// 删除账号恢复成功
int32 FONEEngineSDKPSOversea::ParseAndUpdateUserInfo(const FString& UserInfoJson)
{
	TSharedPtr<FJsonObject> JsonObject;	   // 移出 if
	const TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(UserInfoJson);

	// 注意：增加反序列化成功判断
	if (FJsonSerializer::Deserialize(JsonReader, JsonObject) && JsonObject.IsValid())
	{
		FOneUserInfo UserInfo;	  // 临时存储解析出的核心信息
		TMap<int32, bool> UserBoundAccounts;
		TMap<FString, FString> UserRawInfo;
		int32 ThirdUserId = 0;

		// 解析核心字段：uid, token, username
		// 注意：增加字段存在性和类型检查，防止崩溃
		int64 uid_int64 = 0;
		if (JsonObject->TryGetNumberField(TEXT("uid"), uid_int64))	  // uid 可能是 Number 类型
		{
			UserInfo.UserId = FString::Printf(TEXT("%lld"), uid_int64);
		}

		else if (!JsonObject->TryGetStringField(TEXT("uid"), UserInfo.UserId))	  // 也可能是 String 类型
		{
			ONE_LOG_ERROR_PRINTF("Failed to get 'uid' field or it has wrong type.");
			return -1;	  // uid 是关键字段，获取失败则认为解析失败
		}

		if (!JsonObject->TryGetStringField(TEXT("token"), UserInfo.Token))
		{
			ONE_LOG_ERROR_PRINTF("Failed to get 'token' field.");
			return -1;	  // token 是关键字段
		}

		JsonObject->TryGetStringField(TEXT("username"), UserInfo.UserName);	   // username 可能为空

		// 遍历 JSON 对象，存储所有字段到 RawUserInfo
		for (const auto& Pair : JsonObject->Values)
		{
			FString Key = Pair.Key;
			const TSharedPtr<FJsonValue>& Value = Pair.Value;
			if (!Value.IsValid())
				continue;	 // 跳过无效值

			// 根据不同类型转换为字符串存储
			if (Value->Type == EJson::String)
			{
				UserRawInfo.Add(Key, Value->AsString());
			}
			else if (Value->Type == EJson::Number)
			{
				UserRawInfo.Add(Key, Value->AsString());
			}
			else if (Value->Type == EJson::Boolean)
			{
				UserRawInfo.Add(Key, Value->AsBool() ? TEXT("true") : TEXT("false"));
			}
		}

		// 解析第三方账号绑定信息 (thirdUsers 数组)
		const TArray<TSharedPtr<FJsonValue>>* ThirdUsersArrayPtr;
		if (JsonObject->TryGetArrayField(TEXT("thirdUsers"), ThirdUsersArrayPtr))
		{
			for (const auto& ThirdUserValue : *ThirdUsersArrayPtr)
			{
				if (ThirdUserValue.IsValid() && ThirdUserValue->Type == EJson::Object)
				{
					const TSharedPtr<FJsonObject> ThirdUserObject = ThirdUserValue->AsObject();

					if (ThirdUserObject.IsValid())
					{
						int32 ThirdType = -1;
						// 注意：检查 thirdType 字段是否存在且为数字
						if (ThirdUserObject->TryGetNumberField(TEXT("thirdType"), ThirdType))
						{
							UserBoundAccounts.Add(ThirdType, true);	   // 标记该类型已绑定

							// 特殊处理：类型 8 (根据上下文猜测是 PSN?)，需要解密邮箱
							// 建议定义常量：const int32 PSNAccountType = 8;
							if (ThirdType == 8)	   // PSN Account Type
							{
								FString EncryptedEmail;
								if (ThirdUserObject->TryGetStringField(TEXT("thirdEmail"), EncryptedEmail) &&
									!EncryptedEmail.IsEmpty())
								{
									char* DecryptedEmailCstr = nullptr;
									// 调用 SDK 解密
									// 注意：确认 global_sdk_manager_decryption 的错误处理和返回值
									FString DecryptedEmail;
									int DecryptRet =
										global_sdk_manager_decryption(TCHAR_TO_UTF8(*EncryptedEmail), &DecryptedEmailCstr);
									if (DecryptedEmailCstr != nullptr)
									{
										DecryptedEmail = FString(UTF8_TO_TCHAR(DecryptedEmailCstr));
										global_sdk_manager_free(DecryptedEmailCstr);	// 释放内存
										DecryptedEmailCstr = nullptr;
									}

									if (DecryptRet == 0)
									{
										UserRawInfo.Add(TEXT("showEmail"), DecryptedEmail);	   // 用于显示的邮箱？
										UserRawInfo.Add(TEXT("email"), DecryptedEmail);		   // 通用邮箱字段？
										ONE_LOG_PRINTF("Decrypted email for thirdType %d.", ThirdType);
									}
									else
									{
										// 日志：记录解密失败
										ONE_LOG_ERROR_PRINTF(
											"Failed to decrypt email for thirdType %d. SDK returned %d.", ThirdType, DecryptRet);
									}
								}
								else
								{
									// 日志：记录 thirdType 8 缺少 thirdEmail 字段或为空
									ONE_LOG_ERROR_PRINTF("Missing or empty 'thirdEmail' for thirdType 8.");
								}
							}
							else if (ThirdType == 23)
							{
								// 特殊处理：类型 23 (根据上下文猜测是 PSN)
								ThirdUserObject->TryGetNumberField(TEXT("uid"), ThirdUserId);
							}
						}
						else
						{
							// 日志：记录 thirdUsers 中对象缺少 thirdType 字段或类型错误
							ONE_LOG_PRINTF("Missing or invalid 'thirdType' in thirdUsers object.");
						}
					}
				}
				else
				{
					// 日志：记录 thirdUsers 数组中包含非对象元素
					ONE_LOG_PRINTF("Non-object element found in 'thirdUsers' array.");
				}
			}
		}
		else
		{
			ONE_LOG_PRINTF("Missing or invalid 'thirdUsers' array field.");
			return -1;
		}

		if (ThirdUserId > 0)
		{
			BoundAccounts = InitBoundAccounts;

			// 更新绑定状态
			for (const auto& Pair : UserBoundAccounts)
			{
				BoundAccounts.Add(Pair.Key, Pair.Value);
			}

			// 更新 PSN 用户 ID
			this->PsnUserId = ThirdUserId;

			// 更新原始信息
			RawUserInfo = UserRawInfo;

			// 更新当前用户信息缓存
			CurrentUserInfo = UserInfo;

			ONE_LOG_PRINTF("User info updated successfully for UserID: %s", *CurrentUserInfo.UserId);

			return 0;	 // 解析成功
		}

		ONE_LOG_PRINTF("Failed to get valid PsnUserId.");
		return -1;	  // PsnUserId 无效
	}
	// JSON 反序列化失败
	ONE_LOG_PRINTF("Failed to deserialize UserInfo JSON: %s", *UserInfoJson);
	return -1;	  // 返回错误码
}

FString FONEEngineSDKPSOversea::FormatUserCenterData()
{
	// 准备传递给 UI 的用户信息 JSON
	TSharedPtr<FJsonObject> UserInfoJsonObject = MakeShareable(new FJsonObject());
	// 添加基础信息
	UserInfoJsonObject->SetStringField(TEXT("userId"), CurrentUserInfo.UserId);
	UserInfoJsonObject->SetStringField(TEXT("token"), CurrentUserInfo.Token);
	UserInfoJsonObject->SetStringField(TEXT("username"), CurrentUserInfo.UserName);

	if (RawUserInfo.Contains(TEXT("showEmail")))
	{
		UserInfoJsonObject->SetStringField(TEXT("showEmail"), RawUserInfo.FindRef(TEXT("showEmail")));
	}

	if (RawUserInfo.Contains(TEXT("email")))
	{
		UserInfoJsonObject->SetStringField(TEXT("email"), RawUserInfo.FindRef(TEXT("email")));
	}

	if (RawUserInfo.Contains(TEXT("nickname")))
	{
		UserInfoJsonObject->SetStringField(TEXT("nickname"), RawUserInfo.FindRef(TEXT("nickname")));
	}

	if (RawUserInfo.Contains(TEXT("headImg")))
	{
		UserInfoJsonObject->SetStringField(TEXT("headImg"), RawUserInfo.FindRef(TEXT("headImg")));
	}

	// 序列化用户信息 JSON
	FString UserInfoJsonString;
	const TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&UserInfoJsonString);
	FJsonSerializer::Serialize(UserInfoJsonObject.ToSharedRef(), Writer);
	return UserInfoJsonString;
}

void FONEEngineSDKPSOversea::ResetUserInfo()
{
	// 重置登录选项
	LoginOption = 0;

	// 清空本地缓存的用户信息
	RawUserInfo.Empty();

	// 重置为默认构造
	CurrentUserInfo = FOneUserInfo();

	// 恢复初始化的绑定选项
	BoundAccounts = InitBoundAccounts;

	// 重置 PSN 用户 ID
	PsnUserId = 0;

	// 重置中间用户信息
	IntermediateUserInfo = TEXT("");

	// 调用 SDK 的切换账号接口 (这通常会清理 SDK 内部状态并可能触发平台相关的登出)
	global_sdk_manager_switch_account();
}

#undef LOCTEXT_NAMESPACE
#endif
