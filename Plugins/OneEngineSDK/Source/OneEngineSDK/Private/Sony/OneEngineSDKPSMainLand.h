﻿#pragma once
#if ENGINE_SUPPORT_SONY

#include "OneEngineSDKPSAdapter.h"


class FONEEngineSDKPSMainLand : public IONEEngineSDKPSInterface
{
public:
	struct FRequestContext
	{
		int32 Id;
		void* UserData;
	};

	// 回调函数类型别名
	using FAntiAddictionCallback = TFunction<void(const FOneAntiAddictionInfo& Info)>;
	using FGenericCallback = TFunction<void(bool bSuccess, const FString& Result, int ErrCode, const FString& ErrInfo)>;
	using FRoleListCallback = TFunction<void(bool bSuccess, int32 Code, const FString& Msg, const TArray<FOneURCRoleInfo>& RoleList)>;

	virtual ~FONEEngineSDKPSMainLand() override;

	// 基本功能
	virtual void Login() override;
	virtual void Logout(int Type) override;
	virtual FOneUserInfo GetUserInfo() override;
	virtual FString GetChannelId() override;
	virtual bool CheckLoggedInStatus() override;
	virtual FString GetChannelMediaId() override;

	// 防沉迷相关
	virtual void RequestAntiAddictionInfo(FAntiAddictionCallback Callback) override;
	virtual void StartAntiAddictionMonitoring(
		const FString& ServerId,
		const FString& RoleId,
		FAntiAddictionCallback Callback
	) override;
	virtual void StopAntiAddictionMonitoring() override;

	// SDK 初始化和设置
	virtual int InitializeSDK(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate) override;
	virtual int32 GetPlatformOS() override;
	virtual FString GetChannelPlatform() override;
	virtual void GetUserLocation(TFunction<void(const FOneUserLocationInfo& LocationInfo)> OnFinishedLambda) override;
	virtual void SetDebugMode(bool bDebugMode) override;
	virtual bool GetDebugMode() override;

	// 用户中心和合规性
	virtual void OpenUserCenter() override;
	virtual int OpenComplianceOnWebView() override;

	// 好友和拉黑列表
	virtual int GetFriendList(int Offset, int Count, FGenericCallback) override;
	virtual int GetBlockList(int Offset, int Count, FGenericCallback) override;

	// 商品信息
	virtual int GetProductInfoList(int32 ServiceLabel, FString CategoryLabel, FGenericCallback CallBack) override;

	// 兑换码
	virtual void UseRedeemCode(const FString& ExchangeCode, const FOneRoleInfo& RoleInfo, const TMap<FString, FString>& ExtraInfo,
	                           TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> CallBack) override;
	// 角色列表
	virtual void GetRoleList(const FString& ServerId, FRoleListCallback CallBack) override;

	// 语言设置
	virtual void SetLanguage(const FString& Language) override;
	virtual FString GetCurrentLanguage() override;

	virtual bool ExaminStatus() override;

	// 事件追踪
	virtual void Track(const FString& EventName, const TMap<FString, FString>& EventParams, FString TaskId = "2") override;

	// UI 相关
	virtual void ShowLoading() override;
	virtual void HideLoading() override;

	// 线程安全的计数器和互斥锁
	FCriticalSection Mutex;
	FThreadSafeCounter RequestIdCounter{0};

	// 回调映射
	TMap<FRequestContext*, FAntiAddictionCallback> AntiAddictionCallbacks;
	FRequestContext* AntiAddictionNotificationParam{nullptr};
	TMap<FRequestContext*, FGenericCallback> GenericCallbacks;

	// 用户信息相关
	void SetCurrentUserInfo(const FOneUserInfo& UserInfo);
	FORCEINLINE FString GetRealUserId() const;
	FORCEINLINE FString GetRealNameTicket() const;
	void SetRealUserId(const FString& InRealUserId);
	void SetRealNameTicket(const FString& InRealNameTicket);
	FORCEINLINE FString GetAccount() const;
	FORCEINLINE FString GetSecret() const;
	FORCEINLINE TMap<FString, FString>& GetRawUserInfo();

private:
	bool bInitialized{false};
	FOneUserInfo CurrentUserInfo{};
	TMap<FString, FString> RawUserInfo;

	FString RealUserId;
	FString RealNameTicket;

	FString CurrentAccount;
	FString CurrentSecret;


	bool IsInitialized() const;
	bool IsLogin() const;
	static void OnFetchAntiAddictionInfo(void* Handle, const char* Result, void* UserData);
	static void OnAntiAddictionNotification(void* Handle, const char* Result, void* UserData);
	static void OnGeneralEvent(void* Handle, bool Success, const char* Result, int ErrCode, const char* ErrInfo, void* UserData);


	// 登录相关私有方法
	void LoginWithCredentials(const FString& Username, const FString& Password);
	void AuthenticateRealName(const FString& Name, const FString& IdentityNumber);
	void SendLoginVerificationCode(const FString& Phone);
	void LoginWithVerificationCode(const FString& Phone, const FString& VerificationCode);
	void LoginWithQRCode();
	void CancelLoginWithQRCode();
	
	void UpdateUserInfo(TFunction<void(bool)>Completion);
	

	// 手机相关私有方法
	void GetPhoneAreaCode();
	void CheckPhoneNumberValidity(int AreaCodeId, const FString& PhoneNumber, FGenericCallback Callback);
	void SendVerificationCode(int AreaCodeId, const FString& PhoneNumber, const FString& SendType, FGenericCallback Callback);
	void VerifyVerificationCode(int AreaCodeId, const FString& PhoneNumber, const FString& VerificationCode, bool BindPhone, const FString& OldPhoneNumber,
	                            FGenericCallback Callback);
	void UnbindPhoneNumber(const FString& VerificationCode, FGenericCallback Callback);

	// 密码重置和设备管理
	void ResetPasswordByMobile(const FString& VerificationCode, const FString& NewPassword, FGenericCallback Callback);
	void OfflineDevice(const FString& DeviceId, FGenericCallback Callback);
	void GetOnlineDevices(FGenericCallback Callback);

	FRequestContext* CreateNewRequestContext();
};

#endif
