﻿#if ENGINE_SUPPORT_SONY

#include "OneEngineSDKPSUtils.h"
#include "Async/Async.h"

#if PLATFORM_PS4 || PLATFORM_PS5
#include <message_dialog.h>
#include <kernel.h>
#include <libsysmodule.h>
#endif


DEFINE_LOG_CATEGORY(LogOneSDK);

void FONEEngineSDKPSUtils::ExecInGameThread(TFunction<void()> Func)
{
	// 如果当前是 GameThread，则直接执行
	if (IsInGameThread())
	{
		Func();
	}
	else
	{
		AsyncTask(ENamedThreads::GameThread, Func);
	}
}

void FONEEngineSDKPSUtils::AsyncExecInBackgroundThread(TFunction<void()> Func)
{
	AsyncTask(ENamedThreads::AnyNormalThreadNormalTask, Func);
}

UWorld* FONEEngineSDKPSUtils::GetWorld()
{
	UWorld* World = nullptr;
#if WITH_EDITOR
	if (GIsEditor)
	{
		if (GPlayInEditorID == -1)
		{
			const FWorldContext* WorldContext = GEditor->GetPIEWorldContext(1);
			if (WorldContext == nullptr)
			{
				if (const UGameViewportClient* Viewport = GEngine->GameViewport)
				{
					World = Viewport->GetWorld();
				}
			}
			else
			{
				World = WorldContext->World();
			}
		}
		else
		{
			const FWorldContext* WorldContext = GEditor->GetPIEWorldContext(GPlayInEditorID);
			if (WorldContext == nullptr)
			{
				return nullptr;
			}
			World = WorldContext->World();
		}
	}
	else
	{
		World = GEngine->GetCurrentPlayWorld(nullptr);
	}
#else
	World = GEngine->GetCurrentPlayWorld(nullptr);
#endif
	return World;
}

void FONEEngineSDKPSUtils::ShowOKCancelDialog(const FString& Message, TFunction<void(bool bOk)> Callback)
{
#if PLATFORM_PS4 || PLATFORM_PS5
	AsyncExecInBackgroundThread([Message, Callback]()
	{
		bool bDialogInitialized = false;
		bool bCallbackCalled = false;
		const int32 SleepMs = 200;

		// 确保回调只被调用一次
		auto SafeCallback = [&bCallbackCalled, Callback](bool bOk)
		{
			if (!bCallbackCalled)
			{
				bCallbackCalled = true;
				// GameThread
				ExecInGameThread([Callback, bOk]()
				{
					Callback(bOk);
				});
			}
		};

		// 错误清理函数
		auto CleanupAndFail = [&bDialogInitialized, &SafeCallback]()
		{
			if (bDialogInitialized)
			{
				sceMsgDialogTerminate();
				bDialogInitialized = false;
			}
			SafeCallback(false);
		};

		// 1. 检查并加载消息对话框模块
		int ret = 0;
		if (sceSysmoduleIsLoaded(SCE_SYSMODULE_MESSAGE_DIALOG) == SCE_SYSMODULE_ERROR_UNLOADED)
		{
			ret = sceSysmoduleLoadModule(SCE_SYSMODULE_MESSAGE_DIALOG); // 修复：加载正确的模块
			if (ret < 0)
			{
				ONE_LOG_ERROR_PRINTF("sceSysmoduleLoadModule(SCE_SYSMODULE_MESSAGE_DIALOG) failed: %d", ret);
				SafeCallback(false);
				return;
			}
		}

		// 2. 初始化对话框
		ret = sceMsgDialogInitialize();
		if (ret < 0 && ret != SCE_COMMON_DIALOG_ERROR_ALREADY_INITIALIZED)
		{
			ONE_LOG_ERROR_PRINTF("sceMsgDialogInitialize failed: %d", ret);
			SafeCallback(false);
			return;
		}
		bDialogInitialized = true;

		// 3. 设置对话框参数
		SceMsgDialogUserMessageParam messageParam;
		SceMsgDialogParam dialogParam;

		sceMsgDialogParamInitialize(&dialogParam);
		memset(&messageParam, 0x00, sizeof(messageParam));


		FString CopyMessage = Message;
		dialogParam.userMsgParam = &messageParam;
		dialogParam.mode = SCE_MSG_DIALOG_MODE_USER_MSG;
		messageParam.buttonType = SCE_MSG_DIALOG_BUTTON_TYPE_OK_CANCEL;
		messageParam.msg = TCHAR_TO_UTF8(*CopyMessage);

		// 4. 打开对话框
		ret = sceMsgDialogOpen(&dialogParam);
		if (ret < 0)
		{
			ONE_LOG_ERROR_PRINTF("sceMsgDialogOpen failed: %d", ret);
			CleanupAndFail();
			return;
		}

		// 5. 检查初始状态
		SceCommonDialogStatus status = sceMsgDialogGetStatus();
		if (SCE_COMMON_DIALOG_STATUS_RUNNING != status)
		{
			ONE_LOG_ERROR_PRINTF("Status is not running: %d", status);
			CleanupAndFail();
			return;
		}

		// 6. 等待对话框完成（带超时保护）
		bool bOk = false;
		bool bFinished = false;
		SceCommonDialogStatus currentStatus;

		while (!bFinished)
		{
			currentStatus = sceMsgDialogUpdateStatus();

			if (SCE_COMMON_DIALOG_STATUS_FINISHED == currentStatus)
			{
				SceMsgDialogResult dialogResult;
				memset(&dialogResult, 0x0, sizeof(SceMsgDialogResult));

				int32_t resultRet = sceMsgDialogGetResult(&dialogResult);
				if (resultRet < 0)
				{
					ONE_LOG_PRINTF("sceMsgDialogGetResult failed: %d", resultRet);
				}
				else
				{
					// https://game.develop.playstation.net/resources/documents/SDK/11.000/MsgDialog-Reference/sce-msg-dialog-result.html
					ONE_LOG_PRINTF("sceMsgDialogGetResult result: %d, button: %d", dialogResult.result, dialogResult.buttonId);
					if (dialogResult.result == SCE_COMMON_DIALOG_RESULT_OK && dialogResult.buttonId == SCE_MSG_DIALOG_BUTTON_ID_OK)
					{
						ONE_LOG_PRINTF("User select OK");
						bOk = true;
					}
				}
				bFinished = true;
			}
			else if (SCE_COMMON_DIALOG_STATUS_NONE == currentStatus)
			{
				// 对话框意外关闭
				ONE_LOG_PRINTF("Dialog unexpectedly closed");
				bFinished = true;
			}

			if (!bFinished)
			{
				sceKernelUsleep(SleepMs * 1000); // 200ms
			}
		}

		// 7. 清理并回调
		sceMsgDialogTerminate();
		SafeCallback(bOk);
	});
#endif

}

#endif

