﻿#if ENGINE_SUPPORT_SONY

#include "OneEngineSDKPSMainLand.h"
#include "JsonObjectWrapper.h"
#include "OneEngineSDKPSUtils.h"
#include "Async/Async.h"
#include "Serialization/JsonReader.h"
#include "Serialization/JsonSerializer.h"
#include "Widgets/Input/SEditableText.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Input/SEditableTextBox.h"
#include "../Views/PSOneUIManager.h"
#include "OneEngineSDKPSSubsystem.h"
#if PLATFORM_PS4 || PLATFORM_PS5
#include "wmc/logger.h"
#include "libone.h"
#include "libpspay.h"
#else
// 纯粹就是为了智能提示
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/wmc/logger.h"
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/libone.h"
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/libpspay.h"
#endif

#define LOCTEXT_NAMESPACE "OneEngineSDKPSMainLand"

// !FPlatformMisc::SetStoredValue( TEXT( "Epic Games" ), TEXT( "Unreal Engine/Identifiers" ), TEXT( "MachineId" ), MachineIdString )
namespace
{
	const FString GOneUserAgreementURL = TEXT("https://www.wanmei.com/safestatic/contract.html");
	const FString GOnePrivacyPolicyURL = TEXT("https://www.wanmei.com/safestatic/privacy.html");
	const FString GOneChildPrivacyPolicyURL = TEXT("https://www.wanmei.com/safestatic/kidsprivacy.html");
	const FString GOneLegalTermsURL = TEXT("https://www.wanmei.com/safestatic/protocolList.html");
	const FString GOneAccountCancellationURL = TEXT("https://www.wanmei.com/safestatic/cancel.html");

	typedef enum
	{
		LoginType_Token,
		LoginType_Password,
		LoginType_QRCode,
		LoginType_SMS,
		LoginType_RealName
	} ELogin_Type;

	ELogin_Type GUserLoginType = LoginType_Password;

	template <typename DelegateType, typename... Args>
	void HandleCallback(
		FONEEngineSDKPSMainLand *Self,
		const FONEEngineSDKPSMainLand::FRequestContext *Param,
		TMap<FONEEngineSDKPSMainLand::FRequestContext *, DelegateType> &DelegateMap,
		const TFunction<void(DelegateType)> &InvokeDelegate,
		bool RemoveAfterInvoke = true)
	{
		DelegateType Delegate;
		{
			FScopeLock Lock(&Self->Mutex);
			if (DelegateMap.Contains(Param))
			{
				Delegate = DelegateMap[Param];
				if (RemoveAfterInvoke)
				{
					ONE_LOG_PRINTF("Removed Param:%d from DelegateMap", Param->Id);
					DelegateMap.Remove(Param);
				}
			}
		}

		InvokeDelegate(Delegate);

		if (RemoveAfterInvoke)
		{
			ONE_LOG_PRINTF("Released Param:%d", Param->Id);
			delete Param;
		}
	}

	void OnEvent(void *Handle, OneEvent EventType, const char *EventData, void *UserData)
	{
		FONEEngineSDKPSMainLand *Self = static_cast<FONEEngineSDKPSMainLand *>(UserData);
		if (Self == nullptr)
		{
			ONE_LOG_ERROR_PRINTF("Failed to cast UserData to FONEEngineSDKPSMainLand");
			return;
		}
		// 捕获到事件数据
		FString EventDataStr = UTF8_TO_TCHAR(EventData);
		// 必须切换到游戏线程
		AsyncTask(ENamedThreads::GameThread, [Self, EventType, EventDataStr]()
				  {
			auto UIManager = UPSOneUIManager::Get();

			// 解析 EventData
			const TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(EventDataStr);
			TSharedPtr<FJsonObject> JsonObject;
			FJsonSerializer::Deserialize(JsonReader, JsonObject);
			switch (EventType)
			{
			case ERR_DIALOG:
				{
					UIManager->HideLoading();
					FString Message;
					if (JsonObject.IsValid())
					{
						Message = JsonObject->GetStringField(TEXT("message")); // Message
					}
					if (Message.IsEmpty())
					{
						ONE_LOG_ERROR_PRINTF("Message is empty, using EventData: %s", *EventDataStr);
						Message = EventDataStr;
					}
					ONE_LOG_PRINTF("On receive ERR_DIALOG, Displaying toast ... %s", *Message);

					UIManager->ShowToast(FText::FromString(Message));
				}
				break;
			case TOKEN_INVALID:
				{
					// 告知游戏返回登录页面
					ONE_LOG_PRINTF("On receive TOKEN_INVALID, Logging out...");
					Self->Logout(0);
				}
				break;
			case SHOW_PRIVACY_POLICY:
				{
					// {"loginOptionConfig":"95"}
					ONE_LOG_PRINTF("On receive SHOW_PRIVACY_POLICY, Displaying privacy policy...");
					// 解析 Json 字符串 EventData
					int32 LoginOptions = 0;
					if (JsonObject.IsValid())
					{
						FString LoginOptionConfig;
						JsonObject->TryGetStringField(TEXT("loginOptionConfig"), LoginOptionConfig);
						// FString to int32
						LoginOptions = FCString::Atoi(*LoginOptionConfig);
					}
					else
					{
						ONE_LOG_ERROR_PRINTF("Failed to parse EventData: %s", *EventDataStr);
					}
					UIManager->HideLoading();
					UIManager->ShowUserAgreement(false, LoginOptions);
				}
				break;
			case SHOW_ACCOUNT_BIND:
				{
					ONE_LOG_PRINTF("On receive SHOW_ACCOUNT_BIND, Displaying account bind...");
					int32 LoginOptions = 0;
					if (JsonObject.IsValid())
					{
						FString LoginOptionConfig;
						JsonObject->TryGetStringField(TEXT("loginOptionConfig"), LoginOptionConfig);
						// FString to int32
						LoginOptions = FCString::Atoi(*LoginOptionConfig);
					}
					else
					{
						ONE_LOG_ERROR_PRINTF("Failed to parse EventData: %s", *EventDataStr);
					}
					UIManager->HideLoading();
					UIManager->ShowUserAgreement(true, LoginOptions);
				}
				break;
			case LOGIN_START:
				{
					ONE_LOG_PRINTF("On receive LOGIN_START, showing loading...");
					UIManager->ShowLoading();
					Self->Track(TEXT("loginBegin"), {});
				}
				break;
			case LOGIN_WITH_TOKEN:
				break;
			case LOGIN_SUCCESS:
				{
					UIManager->HideLoading();
					if (JsonObject.IsValid())
					{
#if UE_BUILD_DEBUG || UE_BUILD_DEVELOPMENT
						ONE_LOG_PRINTF("**** (UE build development) ,On receive LOGIN_SUCCESS, user info: %s", *EventDataStr);
#endif
						// 登录成功
						// Save UserInfo
						UIManager->UpdateUserInfo(EventDataStr);

						// clear raw user info
						TMap<FString, FString>& RawUserInfo = Self->GetRawUserInfo();
						RawUserInfo.Empty();
						
						// 遍历 JsonObject 存储到 RawUserInfo
						for (const auto& Pair : JsonObject->Values)
						{
							FString Key = Pair.Key;
							const TSharedPtr<FJsonValue>& Value = Pair.Value;
							if (!Value.IsValid())
								continue;	 // 跳过无效值

							// 根据不同类型转换为字符串存储
							if (Value->Type == EJson::String)
							{
								RawUserInfo.Add(Key, Value->AsString());
							}
							else if (Value->Type == EJson::Number)
							{
								RawUserInfo.Add(Key, Value->AsString());
							}
							else if (Value->Type == EJson::Boolean)
							{
								RawUserInfo.Add(Key, Value->AsBool() ? TEXT("true") : TEXT("false"));
							}
						}
						

						// FString UserID;
						FString Token;
						FString Nickname;
						FString CellPhone;
						FString OneID;
						JsonObject->TryGetStringField(TEXT("oneId"), OneID);
						// JsonObject->TryGetStringField(TEXT("userId"), UserID);
						JsonObject->TryGetStringField(TEXT("token"), Token);
						JsonObject->TryGetStringField(TEXT("nickname"), Nickname);
						JsonObject->TryGetStringField(TEXT("cellphone"), CellPhone);

						// 检查参数
						if (OneID.IsEmpty() || Token.IsEmpty())
						{
							ONE_LOG_ERROR_PRINTF("Invalid Login EventData: %s", *EventDataStr);
							FOneUserInfo UserInfo;
							Self->OnLoginResultReceived(false, -1, TEXT("Invalid login data"), UserInfo);
							return;
						}

						UIManager->HideLogin();
						UIManager->HideRealName();

						FOneUserInfo UserInfo;
						UserInfo.UserId = OneID;
						UserInfo.Token = Token;
						UserInfo.Phone = CellPhone;
						Self->SetCurrentUserInfo(UserInfo);

						// 通知游戏登录成功
						if (Self->OnLoginResultReceived)
						{
							Self->OnLoginResultReceived(true, 0, TEXT(""), UserInfo);
						}

						// Check if the user is logged in with a RealName
						if (GUserLoginType == LoginType_RealName)
						{
							ONE_LOG_PRINTF("Login successful. LoginType: RealName");
							Self->Track(TEXT("loginRealNameSuccess"), {{TEXT("uid"), Self->GetRealUserId()}});
						}

						// loginSDK 打点
						Self->Track(TEXT("loginSDK"), {{TEXT("uid"), OneID}});
						// 登录成功打点
						FString LoginType = (GUserLoginType == LoginType_Token) ? TEXT("token") : TEXT("user");
						TMap<FString, FString> EventParams;
						EventParams.Add(TEXT("type"), TEXT("ps"));
						EventParams.Add(TEXT("loginway"), LoginType);
						EventParams.Add(TEXT("uid"), OneID);
						Self->Track(TEXT("loginSuccessSDK"), EventParams);
					}
					else
					{
						ONE_LOG_ERROR_PRINTF("On receive LOGIN_SUCCESS, but failed to parse EventData: %s", *EventDataStr);

						if (Self->OnLoginResultReceived)
						{
							Self->OnLoginResultReceived(false, -1, TEXT("Invalid login data"), FOneUserInfo());
						}
					}
				}
				break;
			case LOGIN_FAIL:
				{
					// ---- 登录失败 ----
					UIManager->HideLoading();

					FString Message;
					int32 ErrCode;
					// 解析数据
					if (JsonObject.IsValid())
					{
						JsonObject->TryGetNumberField(TEXT("code"), ErrCode);
						JsonObject->TryGetStringField(TEXT("message"), Message);
					}
					// 如果有错误信息，则显示
					if (!Message.IsEmpty())
					{
						UIManager->ShowToast(FText::FromString(Message));
					}

					// 显示登录界面
					ONE_LOG_ERROR_PRINTF("On receive LOGIN_FAIL, code: %d, message: %s, clear user info.", ErrCode, *Message);
					FOneUserInfo UserInfo;
					Self->SetCurrentUserInfo(UserInfo);


					if (Self->OnLoginResultReceived)
					{
						Self->OnLoginResultReceived(false, ErrCode, Message, UserInfo);
					}

					if (GUserLoginType == LoginType_QRCode)
					{
						// 停止 QR Code 登录
						ONE_LOG_WARNING_PRINTF("QR Code login failed. Stopping QR Code login ...");
						UIManager->SetQrCodeValidity(false);
					}

					if (GUserLoginType == LoginType_RealName)
					{
						Self->Track(TEXT("loginRealNameFail"), {{TEXT("uid"), Self->GetRealUserId()}});
					}
				}
				break;
			case SHOW_LOGIN:
				{
					ONE_LOG_PRINTF("Displaying Login Screen ...");
					UIManager->HideLoading();
					UIManager->ShowLogin(true);
				}
				break;
			case LOGIN_WITH_ACCOUNT:
				{
					ONE_LOG_PRINTF("Logging in with password account ...");
				}
				break;
			case LOGIN_WITH_PHONE:
				{
					// 手机验证码登录
					ONE_LOG_PRINTF("Logging in with phone ...");
				}
				break;
			case LOGIN_WITH_ID_CARD:
				{
					ONE_LOG_PRINTF("Logging in with ID card ...");
				}
				break;
			case SHOW_ID_CARD_AUTHENTICATION:
				{
					ONE_LOG_PRINTF("Displaying ID Card Authentication ...");
					// Hide Widget
					UIManager->HideLoading();
					UIManager->HideLogin();
					// 不需要缓存账号信息
					// Self->GetUIManager().CacheAccountToLocal(Self->GetAccount(), Self->GetSecret());
					if (JsonObject.IsValid())
					{
						FString UID;
						FString Tik;
						JsonObject->TryGetStringField(TEXT("userId"), UID);
						JsonObject->TryGetStringField(TEXT("ticket"), Tik);
						if (UID.IsEmpty() || Tik.IsEmpty())
						{
							ONE_LOG_ERROR_PRINTF("Failed to parse userId or ticket from EventData: %s", *EventDataStr);
							if (Self->OnLoginResultReceived)
							{
								FOneUserInfo UserInfo;
								Self->OnLoginResultReceived(false, -1, TEXT("Invalid login data"), UserInfo);
							}
							return;
						}
						Self->SetRealUserId(UID);
						Self->SetRealNameTicket(Tik);
						ONE_LOG_PRINTF("Displaying ID Card Authentication ...");
						// Self->GetUIManager().DisplayIdentityVerificationScreen();
						UIManager->ShowRealName();
						Self->Track(TEXT("loginRealNameBegin"), {{TEXT("uid"), UID}});
					}
					else
					{
						// 异常信息
						ONE_LOG_ERROR_PRINTF("Failed to parse EventData: %s", *EventDataStr);
						if (Self->OnLoginResultReceived)
						{
							FOneUserInfo UserInfo;
							Self->OnLoginResultReceived(false, -1, TEXT("Invalid login data"), UserInfo);
						}
						return;
					}
				}
				break;
			case LOGIN_WITH_QR_CODE:
				{
					ONE_LOG_PRINTF("Logging in with QR Code ...");
				}
				break;
			case START_GET_QR_CODE:
				{
					ONE_LOG_PRINTF("Starting to get QR Code ...");
				}
				break;
			case GET_QR_CODE_SUCCESS:
				{
					ONE_LOG_PRINTF("QR Code retrieved successfully ...");

					if (JsonObject.IsValid())
					{
						FString QrBase64;
						JsonObject->TryGetStringField(TEXT("icon"), QrBase64);
						if (!QrBase64.IsEmpty())
						{
							TArray<uint8> QrCode;
							FBase64::Decode(QrBase64, QrCode);
							UIManager->SetQrCodeImageData(QrCode);
							UIManager->SetQrCodeValidity(true);
						}
						else
						{
							ONE_LOG_ERROR_PRINTF("Failed to parse icon from EventData: %s", *EventDataStr);
							UIManager->SetQrCodeValidity(false);
						}
					}
					else
					{
						ONE_LOG_ERROR_PRINTF("Failed to parse EventData: %s", *EventDataStr);
						UIManager->SetQrCodeValidity(false);
					}
				}
				break;
			case GET_QR_CODE_FAIL:
				{
					ONE_LOG_PRINTF("Failed to get QR Code ...");
					UIManager->SetQrCodeValidity(false);
				}
				break;
			case GET_QR_CODE_EXPIRED:
				{
					ONE_LOG_PRINTF("QR Code expired ...");
					UIManager->SetQrCodeValidity(false);
				}
				break;
			case CHECK_QR_CODE_STATUS:
				{
					ONE_LOG_PRINTF("Checking QR Code status ...");
				}
				break;
			case CLEAR_QR_CODE:
				{
					ONE_LOG_PRINTF("Clearing QR Code...");
				}
				break;
			case SHOW_FACE_AUTHENTICATION:
				{
					// Don't support face authentication
				}
				break;
			default:
				break;
			} });
	}
}

FONEEngineSDKPSMainLand::~FONEEngineSDKPSMainLand()
{
	// 这里有定时器，因此需要主动停止
	FONEEngineSDKPSMainLand::StopAntiAddictionMonitoring();
	if (Handle != nullptr)
	{
		one_unload(Handle);
		Handle = nullptr;
	}
}

int FONEEngineSDKPSMainLand::InitializeSDK(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate)
{
#if UE_BUILD_DEBUG || UE_BUILD_DEVELOPMENT
	wmc_log_set_level(WMC_LOG_LEVEL_INFO, "HTTP");
	wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "FS");
	wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "PS_PAY");
	wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "ONE_SDK");
	wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "ONE_API");
	wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "ANALYSIS");
#endif

	// 同一时间只允许一个进程初始化
	FScopeLock Lock(&Mutex);

	if (bInitialized)
	{
		// 已经初始化过了
		ONE_LOG_PRINTF("Already initialized.");
		FONEEngineSDKPSUtils::ExecInGameThread([InitDelegate]()
											   { auto _ = InitDelegate.ExecuteIfBound(true, 0, TEXT("")); });
		return 0;
	}
	auto UIManager = UPSOneUIManager::Get();
	auto WeakUIManager = TWeakObjectPtr<UPSOneUIManager>(UIManager);
	UIManager->SetMainlandEnv(true);
	UIManager->OnAgreementAccepted = [this, WeakUIManager]()
	{
		one_set_agree_privacy_agreement(Handle);
		// FIXME: 1.9.3，如果登录选项只有一种，是否应该直接登录？如果已经选择过了登录方式，则直接登录
		// 需要提供接口，用于获取是否已经选择过登录方式
		WeakUIManager->SwitchToPreparePage();
	};
	UIManager->OnOpenUserAgreementURL = []()
	{
		ONE_LOG_PRINTF("OnOpenUserAgreementURL: Opening User Agreement web view");
		UWorld *World = FONEEngineSDKPSUtils::GetWorld();
		if (World)
		{
			if (UOneEngineSDKPSSubsystem *Subsystem = World->GetGameInstance()->GetSubsystem<UOneEngineSDKPSSubsystem>())
			{
				Subsystem->OpenWebView(GOneUserAgreementURL, nullptr);
			}
		}
		else
		{
			ONE_LOG_ERROR_PRINTF("OnOpenUserAgreementURL: Failed to get World");
		}
	};
	UIManager->OnOpenPrivacyPolicyURL = []()
	{
		ONE_LOG_PRINTF("OnOpenPrivacyPolicyURL: Opening Privacy Policy web view");
		UWorld *World = FONEEngineSDKPSUtils::GetWorld();
		if (World)
		{
			if (UOneEngineSDKPSSubsystem *Subsystem = World->GetGameInstance()->GetSubsystem<UOneEngineSDKPSSubsystem>())
			{
				Subsystem->OpenWebView(GOnePrivacyPolicyURL, nullptr);
			}
		}
		else
		{
			ONE_LOG_ERROR_PRINTF("OnOpenPrivacyPolicyURL: Failed to get World");
		}
	};
	UIManager->OnOpenChildPrivacyPolicyURL = []()
	{
		ONE_LOG_PRINTF("OnOpenChildPrivacyPolicyURL: Opening Child Privacy Policy web view");
		UWorld *World = FONEEngineSDKPSUtils::GetWorld();
		if (World)
		{
			if (UOneEngineSDKPSSubsystem *Subsystem = World->GetGameInstance()->GetSubsystem<UOneEngineSDKPSSubsystem>())
			{
				Subsystem->OpenWebView(GOneChildPrivacyPolicyURL, nullptr);
			}
		}
		else
		{
			ONE_LOG_ERROR_PRINTF("OnOpenChildPrivacyPolicyURL: Failed to get World");
		}
	};
	UIManager->OnOpenLegalTermsURL = []()
	{
		ONE_LOG_PRINTF("OnOpenLegalTermsURL: Opening Legal Terms web view");
		UWorld *World = FONEEngineSDKPSUtils::GetWorld();
		if (World)
		{
			if (UOneEngineSDKPSSubsystem *Subsystem = World->GetGameInstance()->GetSubsystem<UOneEngineSDKPSSubsystem>())
			{
				Subsystem->OpenWebView(GOneLegalTermsURL, nullptr);
			}
		}
		else
		{
			ONE_LOG_ERROR_PRINTF("OnOpenLegalTermsURL: Failed to get World");
		}
	};

	UIManager->OnAccountBinding = [this, WeakUIManager]()
	{
		// 游戏线程
		FONEEngineSDKPSUtils::ExecInGameThread([this, WeakUIManager]()
		{
			WeakUIManager->HideUserAgreement();
			one_record_account_bind_type(Handle, 1);
			WeakUIManager->ShowLogin(true);
		});
	};
	UIManager->OnPsnLogin = [this, WeakUIManager]()
	{
		FONEEngineSDKPSUtils::ExecInGameThread([this, WeakUIManager]()
		{
			WeakUIManager->HideUserAgreement();
			one_record_account_bind_type(Handle, 2);
			WeakUIManager->ShowLoading();
			one_login_with_psn(Handle, this);
		});
	};
	UIManager->OnPwrdLogin = [this, WeakUIManager]()
	{
		FONEEngineSDKPSUtils::ExecInGameThread([this, WeakUIManager]()
		{
			WeakUIManager->HideUserAgreement();
			one_record_account_bind_type(Handle, 4);
			WeakUIManager->ShowLogin(true);
		});
	};

	// 账号密码登录回调
	UIManager->OnLoginWithPwd = [this](const FString &Username, const FString &Password)
	{
		// 同意后才进行初始化
		// ONE_LOG_PRINTF("lh s2 %s(%d)", *Username, Password.Len());
		LoginWithCredentials(Username, Password);
	};
	// 实名认证登录
	UIManager->OnLoginWithIdentityVerification = [this](const FString &FullName, const FString &IdentificationNumber)
	{
		// 同意后才进行初始化
		AuthenticateRealName(FullName, IdentificationNumber);
	};

	// 发送验证码
	UIManager->OnSendLoginVerificationCode = [this](const FString &PhoneNumber)
	{
		SendLoginVerificationCode(PhoneNumber);
	};

	// 手机登录
	UIManager->OnLoginWithVerificationCode = [this](const FString &PhoneNumber, const FString &AuthCode)
	{
		LoginWithVerificationCode(PhoneNumber, AuthCode);
	};

	// 获取二维码

	UIManager->OnQRCodeActivated = [this]()
	{
		ONE_LOG_PRINTF("Starting QR Code login ...");
		LoginWithQRCode();
	};

	UIManager->OnQRCodeCanceled = [this]()
	{
		ONE_LOG_PRINTF("Cancelling QR Code login ...");
		CancelLoginWithQRCode();
	};

	UIManager->OnLogout = [this]()
	{
		ONE_LOG_PRINTF("Switching account ...");
		// 更新账号绑定类型为 PSN 登录
		int BindType = 0;
		one_get_account_bind_type(Handle, &BindType);
		if (BindType == 1)
		{
			ONE_LOG_PRINTF("Pre bind type: %d,Updating account bind type to PSN", BindType);
			one_record_account_bind_type(Handle, 2);
		}
		Logout(2);
	};

	UIManager->OnUIWidgetVisibilityChanged = [this](UPSOneUIManager::EUIWidgetType WidgetType, bool bVisible)
	{
		UWorld *World = FONEEngineSDKPSUtils::GetWorld();
		if (World)
		{
			if (const UOneEngineSDKPSSubsystem *Subsystem = World->GetGameInstance()->GetSubsystem<UOneEngineSDKPSSubsystem>())
			{
				Subsystem->OnWidgetVisibilityChanged.Broadcast(static_cast<int>(WidgetType), bVisible);
			}
		}
		if (WidgetType == UPSOneUIManager::EUIWidgetType::UserCenter && bVisible)
		{
			UpdateUserInfo(nullptr);
		}
		if (WidgetType == UPSOneUIManager::EUIWidgetType::RealName && bVisible == false)
		{
			Track(TEXT("loginRealNameCancel"), {{TEXT("uid"), RealUserId}});
		}
	};

	UIManager->OnTrackEvent = [this](const FString &EventName, const TMap<FString, FString> &EventData)
	{
		Track(EventName, EventData);
	};

	UIManager->OnNicknameChange = [this, WeakUIManager](const FString &NickName, TWeakObjectPtr<class UUserWidget> CurrentWidget)
	{
		WeakUIManager->ShowLoading();
		// 修改昵称
		FRequestContext *RequestParam = CreateNewRequestContext();
		{
			FScopeLock Lock(&Mutex);
			GenericCallbacks.Add(RequestParam, [this, NickName, WeakUIManager, CurrentWidget](bool bSuccess, const FString &Result, int ErrCode, const FString &ErrInfo)
								 { FONEEngineSDKPSUtils::ExecInGameThread([this, WeakUIManager, CurrentWidget, bSuccess, ErrCode, ErrInfo]()
																		  {
					if (bSuccess)
					{
						UpdateUserInfo([this,WeakUIManager,CurrentWidget](bool bSuccess)
						{
							WeakUIManager->HideLoading();
							UPSOneUIManager::RemoveWidgetFromViewport(CurrentWidget);
							WeakUIManager->PopHiddenWidgetsUntilUserCenter();
						});
					}
					else
					{
						WeakUIManager->HideLoading();
						FString Fmt = FString::Printf(TEXT("%s(%d)"), *ErrInfo, ErrCode);
						WeakUIManager->ShowToast(FText::FromString(Fmt));
					} }); });
		}
		one_modify_nickname(Handle, TCHAR_TO_UTF8(*NickName), OnGeneralEvent, RequestParam);
	};
	UIManager->OnSendPhoneVerificationCode = [this, WeakUIManager](int AreaCodeId, const FString &PhoneNumber, UPSOneUIManager::EVerificationCodeType VerifyType,
															   bool NeedCheckPhoneNumber,
															   FGenericCallback Callback)
	{
		WeakUIManager->ShowLoading();

		static const TMap<UPSOneUIManager::EVerificationCodeType, FString> VerifyTypeMap = {
			{UPSOneUIManager::EVerificationCodeType::General, TEXT("2")},
			{UPSOneUIManager::EVerificationCodeType::Binding, TEXT("12")},
			{UPSOneUIManager::EVerificationCodeType::Change, TEXT("13")},
			{UPSOneUIManager::EVerificationCodeType::Unbinding, TEXT("22")},
			{UPSOneUIManager::EVerificationCodeType::RestPwd, TEXT("10")}};

		FString VerifyTypeStr = VerifyTypeMap.FindRef(VerifyType);
		auto FinalCallback = [WeakUIManager, Callback](bool Success, const FString &JsonStr, int Code, const FString &Err)
		{
			WeakUIManager->HideLoading();
			if (!Success && !Err.IsEmpty())
			{
				FString Fmt = FString::Printf(TEXT("%s(%d)"), *Err, Code);
				WeakUIManager->ShowToast(FText::FromString(Fmt));
			}
			if (Callback)
			{
				Callback(Success, JsonStr, Code, Err);
			}
		};

		auto SendCode = [this, AreaCodeId, PhoneNumber, VerifyTypeStr, FinalCallback]()
		{
			SendVerificationCode(AreaCodeId, PhoneNumber, VerifyTypeStr, FinalCallback);
		};

		if (NeedCheckPhoneNumber)
		{
			CheckPhoneNumberValidity(
				AreaCodeId, PhoneNumber,
				[SendCode, FinalCallback](bool Success, const FString &JsonStr, int Code, const FString &Err)
				{
					if (Success)
						SendCode();
					else
						FinalCallback(Success, JsonStr, Code, Err);
				});
		}
		else
		{
			SendCode();
		}
	};

	UIManager->OnValidateVerificationCodeAndBindPhone = [this, WeakUIManager](int AreaCodeId, const FString &PhoneNumber, const FString &AuthCode, bool BindPhone,
																			const FString &OldPhoneNumber,
																			FGenericCallback Callback)
	{
		WeakUIManager->ShowLoading();

		auto FinalCallback = [WeakUIManager, Callback](bool Success, const FString &JsonStr, int Code, const FString &Err)
		{
			WeakUIManager->HideLoading();
			if (!Success && !Err.IsEmpty())
			{
				FString Fmt = FString::Printf(TEXT("%s(%d)"), *Err, Code);
				WeakUIManager->ShowToast(FText::FromString(Fmt));
			}
			if (Callback)
			{
				Callback(Success, JsonStr, Code, Err);
			}
		};

		VerifyVerificationCode(
			AreaCodeId, PhoneNumber, AuthCode, BindPhone, OldPhoneNumber,
			[this, WeakUIManager, BindPhone, FinalCallback](bool Success, const FString &JsonStr, int Code, const FString &Err)
			{
				if (Success && BindPhone)
				{
					UpdateUserInfo(nullptr);
					FTimerHandle TimerHandle;
					FONEEngineSDKPSUtils::GetWorld()->GetTimerManager().SetTimer(TimerHandle, [WeakUIManager, FinalCallback, Success, JsonStr, Code, Err]()
																				 { FinalCallback(Success, JsonStr, Code, Err); }, 1, false);
				}
				else
				{
					FinalCallback(Success, JsonStr, Code, Err);
				}
			});
	};

	UIManager->OnUnbindPhoneNumber = [this, WeakUIManager](const FString &AuthCode, TWeakObjectPtr<UUserWidget> CurrentWidget, FGenericCallback Callback)
	{
		WeakUIManager->ShowLoading();

		auto FinalCallback = [WeakUIManager, Callback](bool Success, const FString &JsonStr, int Code, const FString &Err)
		{
			WeakUIManager->HideLoading();
			if (!Success && !Err.IsEmpty())
			{
				FString Fmt = FString::Printf(TEXT("%s(%d)"), *Err, Code);
				WeakUIManager->ShowToast(FText::FromString(Fmt));
			}
			if (Callback)
			{
				Callback(Success, JsonStr, Code, Err);
			}
		};

		UnbindPhoneNumber(
			AuthCode, [this, WeakUIManager, CurrentWidget, Callback, FinalCallback](bool Success, const FString &JsonStr, int Code, const FString &Err)
			{
				ONE_LOG_PRINTF("Verification code validation result - Success: %s, Response: %s, Error Code: %d, Error Message: %s", Success ? TEXT("True") : TEXT("False"),
				               *JsonStr, Code, *Err);
				if (Success)
				{
					// 触发推出登录
					Logout(4);
					WeakUIManager->HideLoading();
					UPSOneUIManager::RemoveWidgetFromViewport(CurrentWidget);
					// 隐藏 UserCenter
					WeakUIManager->PopHiddenWidgetsUntilUserCenter();
					WeakUIManager->HideUserCenter();
					if (Callback)
					{
						Callback(Success, JsonStr, Code, Err);
					}
				}
				else
				{
					WeakUIManager->HideLoading();

					if (!Err.IsEmpty())
					{
						FString Fmt = FString::Printf(TEXT("%s(%d)"), *Err, Code);
						WeakUIManager->ShowToast(FText::FromString(Fmt));
					}

					if (Callback)
					{
						Callback(Success, JsonStr, Code, Err);
					}
				} });
	};

	UIManager->OnChangePassword = [this, WeakUIManager](bool HavePwd, const FString &VerificationCode, const FString &NewPassword, const FString &ConfirmNewPassword,
														TWeakObjectPtr<UUserWidget> CurrentWidget)
	{
		ONE_LOG_PRINTF("OnChangePassword: HavePwd: %s, VerificationCode length: %d, Pwd length: %d, CPwd length: %d",
					   HavePwd ? TEXT("true") : TEXT("false"), VerificationCode.Len(), NewPassword.Len(), ConfirmNewPassword.Len());
		if (VerificationCode.IsEmpty())
		{
			WeakUIManager->ShowToast(LOCTEXT("VerificationCodeIsEmpty", "验证码不能为空，请输入验证码"));
			return;
		}

		if (NewPassword.IsEmpty())
		{
			ONE_LOG_PRINTF("OnChangePassword: Verification code is empty");
			WeakUIManager->ShowToast(LOCTEXT("NewPasswordIsEmpty", "密码不能为空，请输入密码"));
			return;
		}

		if (ConfirmNewPassword.IsEmpty())
		{
			ONE_LOG_PRINTF("OnChangePassword: New password is empty");
			WeakUIManager->ShowToast(LOCTEXT("ConfirmNewPasswordIsEmpty", "确认密码不能为空，请输入确认密码"));
			return;
		}

		if (NewPassword != ConfirmNewPassword)
		{
			ONE_LOG_PRINTF("OnChangePassword: Passwords do not match");
			WeakUIManager->ShowToast(LOCTEXT("PasswordNotMatch", "两次密码输入不匹配，请再试一次"));
			return;
		}

		WeakUIManager->ShowLoading();
		ResetPasswordByMobile(VerificationCode, NewPassword, [this, WeakUIManager, CurrentWidget](bool Success, const FString &JsonStr, int Code, const FString &Err)
							  {
			ONE_LOG_PRINTF("OnChangePassword: ResetPasswordByMobile callback received. Success: %s, Code: %d",
			               Success ? TEXT("true") : TEXT("false"), Code);
			if (Success)
			{
				ONE_LOG_PRINTF("OnChangePassword: Password reset successful. Updating user info.");
				UpdateUserInfo(nullptr);
				//延迟一秒关闭 loading
				FTimerHandle TimerHandle;
				FONEEngineSDKPSUtils::GetWorld()->GetTimerManager().SetTimer(TimerHandle, [WeakUIManager,CurrentWidget]()
				{
					WeakUIManager->HideLoading();
					UPSOneUIManager::RemoveWidgetFromViewport(CurrentWidget);
					WeakUIManager->PopHiddenWidgetsUntilUserCenter();
				}, 1, false);
			}
			else
			{
				ONE_LOG_ERROR_PRINTF("OnChangePassword: Password reset failed. Error: %s, Code: %d", *Err, Code);
				WeakUIManager->HideLoading();
				FString FormatErr = FString::Printf(TEXT("%s(%d)"), *Err, Code);
				WeakUIManager->ShowToast(FText::FromString(FormatErr));
			} });
		ONE_LOG_PRINTF("OnChangePassword: ResetPasswordByMobile function called. Waiting for callback...");
	};

	UIManager->OnOfflineDevice = [this, WeakUIManager](const FString &DeviceId)
	{
		ONE_LOG_PRINTF("OnOfflineDevice: Starting device offline process for DeviceId: %s", *DeviceId);
		WeakUIManager->ShowLoading();
		OfflineDevice(DeviceId, [this, WeakUIManager](bool Success, const FString &JsonStr, int Code, const FString &Err)
					  {
			if (Success)
			{
				ONE_LOG_PRINTF("OnOfflineDevice: Device offline successful. Fetching updated online devices.");
				GetOnlineDevices([this,WeakUIManager](bool Success, const FString& JsonStr, int Code, const FString& Err)
				{
					WeakUIManager->HideLoading();
					ONE_LOG_PRINTF("OnOfflineDevice: GetOnlineDevices callback received. Success: %s, Code: %d",
					               Success ? TEXT("true") : TEXT("false"), Code);
					if (Success)
					{
						WeakUIManager->UpdateDevices(JsonStr);
					}
				});
			}
			else
			{
				ONE_LOG_ERROR_PRINTF("OnOfflineDevice: Device offline failed. Error: %s, Code: %d", *Err, Code);
				FString Message = FString::Printf(TEXT("%s(%d)"), *Err, Code);
				WeakUIManager->ShowToast(FText::FromString(Message));
			} });
	};

	UIManager->OnUserCenterTabIndexChanged = [this, WeakUIManager](int Index)
	{
		ONE_LOG_PRINTF("OnUserCenterTabIndexChanged: Tab index changed to %d", Index);
		// 0 账户绑定信息 1 账号信息 2 实名认证 3 设备管理 4 法律条款 5 其他
		if (Index == 3)
		{
			GetOnlineDevices([this, WeakUIManager](bool Success, const FString &JsonStr, int Code, const FString &Err)
							 {
				ONE_LOG_PRINTF("OnUserCenterTabIndexChanged: GetOnlineDevices callback received. Success: %s, Code: %d",
				               Success ? TEXT("true") : TEXT("false"), Code);
				if (Success)
				{
					WeakUIManager->UpdateDevices(JsonStr);
				} });
		}
	};

	UIManager->OnOpenAccountCancellation = [this]()
	{
		// TODO: 打开账户注销页面，由于涉及到 TRC, 待后续讨论后再实现
		ONE_LOG_ERROR_PRINTF("Unimplemented Open Account Cancellation");
	};

	FString Version;
	if (GConfig)
	{
		GConfig->GetString(TEXT("/Script/EngineSettings.GeneralProjectSettings"), TEXT("ProjectVersion"), Version, GGameIni);
	}
	if (Version.IsEmpty())
	{
		ONE_LOG_WARNING_PRINTF("Version is empty, using default value: *******");
		Version = "*******";
	}
	ONE_LOG_PRINTF("Setting up OneSDK with appId: %s , App version: %s", *AppId, *Version);
	const FString ExtraInfo = FString::Format(TEXT("{\"app_version\":\"{0}\"}"), {*Version});
	void *OneHandle = one_init_with_content(TCHAR_TO_UTF8(*AppId), TCHAR_TO_UTF8(*AppKey), TCHAR_TO_UTF8(*ConfigContent), TCHAR_TO_UTF8(*ExtraInfo));
	if (OneHandle == nullptr)
	{
		ONE_LOG_ERROR_PRINTF("Failed to initialize SDK.");
		return -1;
	}
	// 设置事件回调
	ONE_LOG_PRINTF("Setting up OneSDK event callback ...");
	one_set_on_event_callback(OneHandle, OnEvent);
	Handle = OneHandle;

	bInitialized = true;
	ONE_LOG_PRINTF("SDK initialized.");
	FONEEngineSDKPSUtils::ExecInGameThread([InitDelegate]()
										   { auto _ = InitDelegate.ExecuteIfBound(true, 0, TEXT("")); });

	Track(TEXT("startSDK"), {});
	// 获取手机区号
	GetPhoneAreaCode();
	return 0;
}

void FONEEngineSDKPSMainLand::Login()
{
	if (!bInitialized)
	{
		ONE_LOG_ERROR_PRINTF("SDK not initialized!");
		FONEEngineSDKPSUtils::ExecInGameThread([this]()
											   {
			if (OnLoginResultReceived)
			{
				FOneUserInfo UserInfo;
				OnLoginResultReceived(false, -1, "SDK not initialized", UserInfo);
			} });
		return;
	}
	ONE_LOG_PRINTF("Starting login...");
	one_login(Handle, this);
}

/**
 * 退出登录
 * @param Type  > 0 时，会打点
 */
void FONEEngineSDKPSMainLand::Logout(int Type)
{
	// 退出登录
	ONE_LOG_PRINTF("Logging out ... type: %d", Type);
	ONE_LOG_PRINTF("Clearing user info ...");
	CurrentUserInfo = FOneUserInfo();
	CurrentAccount = "";
	CurrentSecret = "";
	RealUserId = "";
	RealNameTicket = "";
	RawUserInfo.Empty();
	if (Type > 0)
	{
		// 区分登出类型，写在 hint 里
		// "type":
		// 浮标退出按钮 (现已删除)：1
		// 个人中心切换账号按钮：2
		// 游戏调用退出接口：3 (没有 退出接口)
		// 游戏调用切换账号接口：4

		FString StrType = FString::FromInt(Type);
		FONEEngineSDKPSAdapter::Get().GetPimp()->Track(TEXT("logoutSDK"), {{TEXT("type"), StrType}}, "2");
	}
	// 清除内存的用户信息，以及停止定时器
	one_logout(Handle, nullptr);
	if (OnLogoutCallback)
	{
		OnLogoutCallback(true, 0, TEXT(""));
	}
}

void FONEEngineSDKPSMainLand::LoginWithCredentials(const FString &Username, const FString &Password)
{
	check(IsInGameThread());
	CurrentAccount = Username;
	CurrentSecret = Password;

	ONE_LOG_PRINTF("Logging in with credentials ...");
	UPSOneUIManager::Get()->ShowLoading();
	GUserLoginType = LoginType_Password;
	one_login_with_account_and_pwd(Handle, TCHAR_TO_UTF8(*Username), TCHAR_TO_UTF8(*Password), this);
}

void FONEEngineSDKPSMainLand::AuthenticateRealName(const FString &Name, const FString &IdentityNumber)
{
	check(IsInGameThread());

	ONE_LOG_PRINTF("Authenticating real name ...");
	UPSOneUIManager::Get()->ShowLoading();
	GUserLoginType = LoginType_RealName;
	one_login_with_id_card(Handle, TCHAR_TO_UTF8(*Name), TCHAR_TO_UTF8(*IdentityNumber), TCHAR_TO_UTF8(*RealUserId), TCHAR_TO_UTF8(*RealNameTicket), this);
}

void FONEEngineSDKPSMainLand::SendLoginVerificationCode(const FString &Phone)
{
	check(IsInGameThread());

	ONE_LOG_PRINTF("Sending login verification code ...");
	UPSOneUIManager::Get()->ShowLoading();
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [](bool bSuccess, const FString &Result, int ErrCode, const FString &ErrInfo)
							 { FONEEngineSDKPSUtils::ExecInGameThread([bSuccess, ErrCode, ErrInfo]()
																	  {
				UPSOneUIManager::Get()->HideLoading();
				ONE_LOG_PRINTF("Login verification code sent result: %s", bSuccess ? TEXT("true") : TEXT("false"));
				if (!bSuccess && !ErrInfo.IsEmpty())
				{
					FString FormatErr = FString::Printf(TEXT("%s(%d)"), *ErrInfo, ErrCode);
					UPSOneUIManager::Get()->ShowToast(FText::FromString(FormatErr));
				} }); });
	}
	one_send_mobile_login_code(Handle, TCHAR_TO_UTF8(*Phone), "1", OnGeneralEvent, RequestParam);
}

void FONEEngineSDKPSMainLand::LoginWithVerificationCode(const FString &Phone, const FString &VerificationCode)
{
	check(IsInGameThread());

	ONE_LOG_PRINTF("Logging in with verification code ...");
	UPSOneUIManager::Get()->ShowLoading();
	GUserLoginType = LoginType_SMS;
	one_login_with_mobile_code(Handle, TCHAR_TO_UTF8(*Phone), "1", TCHAR_TO_UTF8(*VerificationCode), this);
}

void FONEEngineSDKPSMainLand::LoginWithQRCode()
{
	check(IsInGameThread());
	ONE_LOG_PRINTF("Logging in with QR code");
	GUserLoginType = LoginType_QRCode;
	one_login_with_qr_code(Handle, this);
}

void FONEEngineSDKPSMainLand::CancelLoginWithQRCode()
{
	check(IsInGameThread());
	ONE_LOG_PRINTF("Cancelling login with QR code");
	one_cancel_login_with_qr_code(Handle, this);
}

void FONEEngineSDKPSMainLand::UpdateUserInfo(TFunction<void(bool)>Completion)
{
	if (!IsLogin())
	{
		ONE_LOG_PRINTF("User is not logged in. Skipping update.");
		return;
	}
	ONE_LOG_PRINTF("Updating user info ...");
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [Completion](bool bSuccess, const FString &Result, int ErrCode, const FString &ErrInfo)
		{
			FONEEngineSDKPSUtils::ExecInGameThread([bSuccess, Result, ErrCode, ErrInfo,Completion]()
			{
				ONE_LOG_PRINTF("Update user info result: %d", bSuccess);
				if (bSuccess)
				{
					UPSOneUIManager::Get()->UpdateUserInfo(Result);
				}
				if (Completion)
				{
					Completion(bSuccess);
				}
			});
		});
	}
	one_update_user_info(Handle, OnGeneralEvent, RequestParam);
}

void FONEEngineSDKPSMainLand::GetPhoneAreaCode()
{
	if (!UPSOneUIManager::Get()->GetCountryCode().IsEmpty())
	{
		ONE_LOG_PRINTF("GetPhoneAreaCode: Country code already exists. Skipping fetch.");
		return;
	}
	ONE_LOG_PRINTF("GetPhoneAreaCode: Starting to fetch phone area code");
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [](bool bSuccess, const FString &Result, int ErrCode, const FString &ErrInfo)
		{
			FONEEngineSDKPSUtils::ExecInGameThread([bSuccess, Result, ErrCode, ErrInfo]()
			{
				if (bSuccess)
				{
					ONE_LOG_PRINTF("GetPhoneAreaCode: Successfully fetched phone area code.");
					UPSOneUIManager::Get()->UpdateCountryCode(Result);
				}
				else
				{
					ONE_LOG_ERROR_PRINTF("GetPhoneAreaCode: Failed to fetch phone area code. ErrCode: %d, ErrInfo: %s", ErrCode, *ErrInfo);
				}
			});
		});
	}
	ONE_LOG_PRINTF("GetPhoneAreaCode: Calling one_get_area_code function");
	one_get_area_code(Handle, OnGeneralEvent, RequestParam);
}

void FONEEngineSDKPSMainLand::CheckPhoneNumberValidity(int AreaCodeId, const FString &PhoneNumber,
													   FGenericCallback Callback)
{
	ONE_LOG_PRINTF("Checking phone number validity ...");
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [Callback](bool bSuccess, const FString &Result, int Code, const FString &Err)
							 {
			ONE_LOG_PRINTF("Check phone number validity result: %s", *Result);
			FONEEngineSDKPSUtils::ExecInGameThread([Callback, bSuccess, Result, Code, Err]()
			{
				if (Callback)
				{
					Callback(bSuccess, Result, Code, Err);
				}
			}); });
	}
	one_check_mobile_available(Handle, TCHAR_TO_UTF8(*PhoneNumber), OnGeneralEvent, RequestParam);
}

void FONEEngineSDKPSMainLand::SendVerificationCode(int AreaCodeId, const FString &PhoneNumber, const FString &SendType,
												   FGenericCallback Callback)
{
	ONE_LOG_PRINTF("SendVerificationCode: AreaCodeId: %d, PhoneNumberLen: %d, SendType: %s", AreaCodeId, PhoneNumber.Len(), *SendType);
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [Callback](bool bSuccess, const FString &Result, int Code, const FString &Err)
							 {
			ONE_LOG_PRINTF("SendVerificationCode: Received result - Success: %s, Result: %s, Code: %d, Err: %s",
			               bSuccess ? TEXT("true") : TEXT("false"), *Result, Code, *Err);
			FONEEngineSDKPSUtils::ExecInGameThread([Callback, bSuccess, Result, Code, Err]()
			{
				if (Callback)
				{
					Callback(bSuccess, Result, Code, Err);
				}
			}); });
	}
	FString AreaCodeIdStr = FString::FromInt(AreaCodeId);
	one_send_verify_code(Handle, TCHAR_TO_UTF8(*PhoneNumber), TCHAR_TO_UTF8(*AreaCodeIdStr), TCHAR_TO_UTF8(*SendType), OnGeneralEvent, RequestParam);
}

void FONEEngineSDKPSMainLand::VerifyVerificationCode(int AreaCodeId, const FString &PhoneNumber, const FString &VerificationCode, bool BindPhone, const FString &OldPhoneNumber,
													 FGenericCallback Callback)
{
	ONE_LOG_PRINTF("VerifyVerificationCode: AreaCodeId: %d, PhoneNumberLen: %d, VerificationCodeLen: %d, BindPhone: %d, OldPhoneNumberLen: %d",
				   AreaCodeId, PhoneNumber.Len(), VerificationCode.Len(), BindPhone, OldPhoneNumber.Len());
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [Callback](bool bSuccess, const FString &Result, int Code, const FString &Err)
							 {
			ONE_LOG_PRINTF("VerifyVerificationCode: Callback triggered. Success: %s, Code: %d", bSuccess ? TEXT("true") : TEXT("false"), Code);
			FONEEngineSDKPSUtils::ExecInGameThread([Callback, bSuccess, Result, Code, Err]()
			{
				if (Callback)
				{
					Callback(bSuccess, Result, Code, Err);
				}
			}); });
	}
	FString AreaCodeIdStr;

	if (AreaCodeId > 0)
	{
		AreaCodeIdStr = FString::FromInt(AreaCodeId);
	}

	if (BindPhone)
	{
		ONE_LOG_PRINTF("VerifyVerificationCode: Binding phone number ...");
		one_verify_and_bind_mobile(
			Handle, TCHAR_TO_UTF8(*PhoneNumber), TCHAR_TO_UTF8(*AreaCodeIdStr), TCHAR_TO_UTF8(*VerificationCode), TCHAR_TO_UTF8(*OldPhoneNumber),
			OnGeneralEvent, RequestParam);
	}
	else
	{
		ONE_LOG_PRINTF("VerifyVerificationCode: Verifying phone number ...");
		one_verify_code(Handle, TCHAR_TO_UTF8(*PhoneNumber), TCHAR_TO_UTF8(*AreaCodeIdStr), TCHAR_TO_UTF8(*VerificationCode), OnGeneralEvent, RequestParam);
	}
}

void FONEEngineSDKPSMainLand::UnbindPhoneNumber(const FString &VerificationCode, FGenericCallback Callback)
{
	ONE_LOG_PRINTF("UnbindPhoneNumber: VerificationCode length: %d", VerificationCode.Len());
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [Callback](bool bSuccess, const FString &Result, int Code, const FString &Err)
							 {
			ONE_LOG_PRINTF("UnbindPhoneNumber: Callback triggered. Success: %s, Code: %d", bSuccess ? TEXT("true") : TEXT("false"), Code);
			FONEEngineSDKPSUtils::ExecInGameThread([Callback, bSuccess, Result, Code, Err]()
			{
				if (Callback)
				{
					Callback(bSuccess, Result, Code, Err);
				}
			}); });
	}
	one_unbind_mobile(Handle, TCHAR_TO_UTF8(*VerificationCode), OnGeneralEvent, RequestParam);
	ONE_LOG_PRINTF("UnbindPhoneNumber: Unbinding phone number. Waiting for response...");
}

void FONEEngineSDKPSMainLand::ResetPasswordByMobile(const FString &VerificationCode, const FString &NewPassword, FGenericCallback Callback)
{
	ONE_LOG_PRINTF("ResetPasswordByMobile: VerificationCode length: %d, NewPassword length: %d", VerificationCode.Len(), NewPassword.Len());
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [Callback](bool bSuccess, const FString &Result, int Code, const FString &Err)
							 {
			ONE_LOG_PRINTF("ResetPasswordByMobile: Callback triggered. Success: %s, Code: %d", bSuccess ? TEXT("true") : TEXT("false"), Code);
			ONE_LOG_PRINTF("ResetPasswordByMobile: Result: %s", *Result);
			if (!bSuccess && !Err.IsEmpty())
			{
				ONE_LOG_PRINTF("ResetPasswordByMobile: Error encountered: %s", *Err);
			}

			FONEEngineSDKPSUtils::ExecInGameThread([Callback, bSuccess, Result, Code, Err]()
			{
				if (Callback)
				{
					ONE_LOG_PRINTF("ResetPasswordByMobile: Invoking user-provided callback");
					Callback(bSuccess, Result, Code, Err);
				}
				else
				{
					ONE_LOG_PRINTF("ResetPasswordByMobile: Warning - User-provided callback is null");
				}
			}); });
	}
	one_reset_password(Handle, TCHAR_TO_UTF8(*VerificationCode), TCHAR_TO_UTF8(*NewPassword), OnGeneralEvent, RequestParam);
	ONE_LOG_PRINTF("ResetPasswordByMobile: Password reset request sent. Waiting for response...");
}

void FONEEngineSDKPSMainLand::OfflineDevice(const FString &DeviceId, FGenericCallback Callback)
{
	ONE_LOG_PRINTF("OfflineDevice: DeviceId: %s", *DeviceId);
	if (!IsLogin())
	{
		return;
	}
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [Callback](bool bSuccess, const FString &Result, int Code, const FString &Err)
							 {
			ONE_LOG_PRINTF("OfflineDevice: Callback triggered. Success: %s, Code: %d", bSuccess ? TEXT("true") : TEXT("false"), Code);
			ONE_LOG_PRINTF("OfflineDevice: Result: %s", *Result);
			if (!bSuccess && !Err.IsEmpty())
			{
				ONE_LOG_PRINTF("OfflineDevice: Error encountered: %s", *Err);
			}

			FONEEngineSDKPSUtils::ExecInGameThread([Callback, bSuccess, Result, Code, Err]()
			{
				if (Callback)
				{
					ONE_LOG_PRINTF("OfflineDevice: Invoking user-provided callback");
					Callback(bSuccess, Result, Code, Err);
				}
				else
				{
					ONE_LOG_PRINTF("OfflineDevice: Warning - User-provided callback is null");
				}
			}); });
	}
	one_logout_device(Handle, TCHAR_TO_UTF8(*DeviceId), OnGeneralEvent, RequestParam);
	ONE_LOG_PRINTF("OfflineDevice: Request sent. Waiting for response...");
}

void FONEEngineSDKPSMainLand::GetOnlineDevices(FGenericCallback Callback)
{
	ONE_LOG_PRINTF("GetOnlineDevices");
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [Callback](bool bSuccess, const FString &Result, int Code, const FString &Err)
							 {
			ONE_LOG_PRINTF("GetOnlineDevices: Callback triggered. Success: %s, Code: %d", bSuccess ? TEXT("true") : TEXT("false"), Code);
			ONE_LOG_PRINTF("GetOnlineDevices: Result: %s", *Result);
			if (!bSuccess && !Err.IsEmpty())
			{
				ONE_LOG_PRINTF("GetOnlineDevices: Error encountered: %s", *Err);
			}

			FONEEngineSDKPSUtils::ExecInGameThread([Callback, bSuccess, Result, Code, Err]()
			{
				if (Callback)
				{
					ONE_LOG_PRINTF("GetOnlineDevices: Invoking user-provided callback");
					Callback(bSuccess, Result, Code, Err);
				}
				else
				{
					ONE_LOG_PRINTF("GetOnlineDevices: Warning - User-provided callback is null");
				}
			}); });
	}
	one_get_login_device_list(Handle, OnGeneralEvent, RequestParam);
	ONE_LOG_PRINTF("GetOnlineDevices: Request sent. Waiting for response...");
}

FOneUserInfo FONEEngineSDKPSMainLand::GetUserInfo()
{
	return CurrentUserInfo;
}

FString FONEEngineSDKPSMainLand::GetChannelId()
{
	int Cid = 0;
	one_get_channel_id(Handle, &Cid);
	FString ChannelId = FString::FromInt(Cid);
	return ChannelId;
}

bool FONEEngineSDKPSMainLand::CheckLoggedInStatus()
{
	return IsLogin();
}

FString FONEEngineSDKPSMainLand::GetChannelMediaId()
{
#if PLATFORM_PS4
	return "6051";
#elif PLATFORM_PS5
	return "6052";
#endif
	return "";
}

void FONEEngineSDKPSMainLand::RequestAntiAddictionInfo(TFunction<void(const FOneAntiAddictionInfo &Info)> Callback)
{
	// 判断是否登录
	ONE_LOG_PRINTF("Requesting anti-addiction information.");
	if (!IsLogin())
	{
		FONEEngineSDKPSUtils::ExecInGameThread([Callback]()
											   {
			FOneAntiAddictionInfo Info;
			// Status = 1 不可进入游戏
			Info.Status = 1;
			// 未登录不
			Info.BannedReason = "Please login first.";
			if (Callback)
			{
				ONE_LOG_PRINTF("Executing anti-addiction callback with status: %d, reason: %s", Info.Status, *Info.BannedReason);
				Callback(Info);
			} });
		return;
	}

	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		AntiAddictionCallbacks.Add(RequestParam, Callback);
	}
	const FString UserId = CurrentUserInfo.UserId;
	ONE_LOG_PRINTF("User is logged in. Requesting anti-addiction info for UserId: %s", *UserId);
	one_query_anti_addiction_info(Handle, TCHAR_TO_UTF8(*UserId), FONEEngineSDKPSMainLand::OnFetchAntiAddictionInfo, RequestParam);
}

void FONEEngineSDKPSMainLand::StartAntiAddictionMonitoring(
	const FString &ServerId,
	const FString &RoleId,
	TFunction<void(const FOneAntiAddictionInfo &Info)> Callback)
{
	// 判断是否登录
	if (!IsLogin())
	{
		FONEEngineSDKPSUtils::ExecInGameThread([Callback]()
											   {
			FOneAntiAddictionInfo Info;
			Info.Status = 1;
			Info.BannedReason = "Please login first.";
			if (Callback)
			{
				ONE_LOG_PRINTF("Executing anti-addiction callback with status: %d, reason: %s", Info.Status, *Info.BannedReason);
				Callback(Info);
			} });
		return;
	}
	const FString UserId = CurrentUserInfo.UserId;
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		AntiAddictionCallbacks.Add(RequestParam, Callback);
	}
	AntiAddictionNotificationParam = RequestParam;
	ONE_LOG_PRINTF("User is logged in. Requesting anti-addiction info for UserId: %s ServerId: %s RoleId: %s", *UserId, *ServerId, *RoleId);
	one_set_anti_addiction_callback(Handle, FONEEngineSDKPSMainLand::OnAntiAddictionNotification);
	one_start_anti_addiction_notify(Handle, TCHAR_TO_UTF8(*UserId), TCHAR_TO_UTF8(*ServerId), TCHAR_TO_UTF8(*RoleId), RequestParam);
}

void FONEEngineSDKPSMainLand::StopAntiAddictionMonitoring()
{
	ONE_LOG_PRINTF("Stopping anti-addiction monitoring.");
	one_stop_anti_addiction_notify(Handle);
	if (AntiAddictionNotificationParam != nullptr)
	{
		FScopeLock Lock(&Mutex);
		AntiAddictionCallbacks.Remove(AntiAddictionNotificationParam);
		delete AntiAddictionNotificationParam;
		AntiAddictionNotificationParam = nullptr;
	}
}

int32 FONEEngineSDKPSMainLand::GetPlatformOS()
{
	return 15;
}

FString FONEEngineSDKPSMainLand::GetChannelPlatform()
{
	return FString(TEXT("laohu_9"));
}

void FONEEngineSDKPSMainLand::GetUserLocation(TFunction<void(const FOneUserLocationInfo &LocationInfo)> Callback)
{
	// FONEEngineSDKPSUtils::ExecInGameThread([this, OnFinishedLambda]()
	// 									   {
	// 	FOneUserLocationInfo LocationInfo;
	// 	FString* Value = RawUserInfo.Find(FString(TEXT("userIp")));
	// 	if (Value != nullptr)
	// 	{
	// 		LocationInfo.IP = *Value;
	// 		ONE_LOG_PRINTF("GetUserLocation IP: %s", *LocationInfo.IP);
	// 	}
	// 	else
	// 	{
	// 		ONE_LOG_WARNING_PRINTF("GetUserLocation IP is null");
	// 	}
	//
	// 	if (OnFinishedLambda)
	// 	{
	// 		OnFinishedLambda(LocationInfo);
	// 	} });

	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [Callback](bool bSuccess, const FString &Result, int ErrCode, const FString &ErrInfo)
		{
			ONE_LOG_PRINTF("Friend list retrieval completed.");
			FONEEngineSDKPSUtils::ExecInGameThread([Callback,bSuccess, Result, ErrCode, ErrInfo]()
			{
				// {"code":0,"message":"","result":{"ip":"*************","attribution":" 北 京","countryCode":"CN","cityCode":"110000","country":" 中 国 ","region":" 北 京","city":"北京"}}
				FOneUserLocationInfo LocationInfo;
				if (bSuccess)
				{
					TSharedPtr<FJsonObject> JsonObject;
					const TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(Result);
					if (FJsonSerializer::Deserialize(JsonReader, JsonObject))
					{
						JsonObject->TryGetStringField("attribution", LocationInfo.CountryAbbr);
						JsonObject->TryGetStringField("ip", LocationInfo.IP);
						JsonObject->TryGetStringField("country", LocationInfo.Country);
						JsonObject->TryGetStringField("region", LocationInfo.Province);
						JsonObject->TryGetStringField("city", LocationInfo.City);
						JsonObject->TryGetStringField("countryCode", LocationInfo.CountryCode);
						
					}
				}
				Callback(LocationInfo);
			});
		});
	}

	ONE_LOG_PRINTF("Requesting user location.");
	one_get_ip_location(Handle, OnGeneralEvent, RequestParam);
}

void FONEEngineSDKPSMainLand::SetDebugMode(bool bEnable)
{
	bDebugMode = bEnable;
	ONE_LOG_PRINTF("SDK Debug mode set to: %s", bEnable ? TEXT("Enabled") : TEXT("Disabled"));
	if (bEnable)
	{
		wmc_log_set_level(WMC_LOG_LEVEL_INFO, "HTTP");
		wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "FS");
		wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "PS_PAY");
		wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "ONE_SDK");
		wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "ONE_API");
		wmc_log_set_level(WMC_LOG_LEVEL_DEBUG, "ANALYSIS");
	}
	else
	{
		wmc_log_set_level(WMC_LOG_LEVEL_WARN, "HTTP");
		wmc_log_set_level(WMC_LOG_LEVEL_WARN, "FS");
		wmc_log_set_level(WMC_LOG_LEVEL_WARN, "PS_PAY");
		wmc_log_set_level(WMC_LOG_LEVEL_WARN, "ONE_SDK");
		wmc_log_set_level(WMC_LOG_LEVEL_WARN, "ONE_API");
		wmc_log_set_level(WMC_LOG_LEVEL_WARN, "ANALYSIS");
	}
}

bool FONEEngineSDKPSMainLand::GetDebugMode()
{
	return bDebugMode;
}

void FONEEngineSDKPSMainLand::OpenUserCenter()
{
	check(IsInGameThread());
	if (IsLogin())
	{
		ONE_LOG_PRINTF("Opening user center.");
		UPSOneUIManager::Get()->ShowUserCenter();
	}
	else
	{
		ONE_LOG_ERROR_PRINTF("Opening user center failed, User is not logged in.");
	}
}

int FONEEngineSDKPSMainLand::OpenComplianceOnWebView()
{
	FONEEngineSDKPSUtils::AsyncExecInBackgroundThread([this]()
													  {
		int Result = one_open_compliance_page(Handle);
		if (Result != 0)
		{
			ONE_LOG_ERROR_PRINTF("Open Compliance On WebView failed, Result: %d", Result);
		} });
	return 0;
}

int FONEEngineSDKPSMainLand::GetFriendList(int Offset, int Count, FGenericCallback Callback)
{
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [Callback](bool bSuccess, const FString &Result, int ErrCode, const FString &ErrInfo)
							 {
			ONE_LOG_PRINTF("Friend list retrieval completed.");
			FONEEngineSDKPSUtils::ExecInGameThread([Callback,bSuccess, Result, ErrCode, ErrInfo]()
			{
				Callback(bSuccess, Result, ErrCode, ErrInfo);
			}); });
	}

	ONE_LOG_PRINTF("Retrieving friend list.");
	one_get_relation_list_ps(Handle, 1, Offset, Count, OnGeneralEvent, RequestParam);
	return 0;
}

int FONEEngineSDKPSMainLand::GetBlockList(int Offset, int Count, FGenericCallback Callback)
{
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [Callback](bool bSuccess, const FString &Result, int ErrCode, const FString &ErrInfo)
							 {
			ONE_LOG_PRINTF("Block list retrieval completed.");
			FONEEngineSDKPSUtils::ExecInGameThread([Callback,bSuccess, Result, ErrCode, ErrInfo]()
			{
				Callback(bSuccess, Result, ErrCode, ErrInfo);
			}); });
	}

	ONE_LOG_PRINTF("Retrieving block list.");
	one_get_relation_list_ps(Handle, 2, Offset, Count, OnGeneralEvent, RequestParam);
	return 0;
}

int FONEEngineSDKPSMainLand::GetProductInfoList(int32 ServiceLabel, FString CategoryLabel, FGenericCallback Callback)
{
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [Callback](bool bSuccess, const FString &Result, int ErrCode, const FString &ErrInfo)
							 {
			ONE_LOG_PRINTF("Product info list retrieval completed.");
			FONEEngineSDKPSUtils::ExecInGameThread([Callback,bSuccess, Result, ErrCode, ErrInfo]()
			{
				Callback(bSuccess, Result, ErrCode, ErrInfo);
			}); });
	}
	ONE_LOG_PRINTF("Starting to retrieve product info list.");
	one_get_product_list_ps(Handle, ServiceLabel, TCHAR_TO_UTF8(*CategoryLabel), OnGeneralEvent, RequestParam);
	return 0;
}

void FONEEngineSDKPSMainLand::UseRedeemCode(const FString &ExchangeCode, const FOneRoleInfo &RoleInfo, const TMap<FString, FString> &ExtraInfo,
											TFunction<void(bool bSuccess, int32 Code, const FString &Msg)> CallBack)
{
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [CallBack, this, ExchangeCode, RoleInfo](bool bSuccess, const FString &Result, int ErrCode, const FString &ErrInfo)
							 {
			ONE_LOG_PRINTF("Redeem code usage completed. success: %d, errCode: %d, errInfo: %s", bSuccess, ErrCode, *ErrInfo);
			FString Uid = CurrentUserInfo.UserId;
			FString RoleId = RoleInfo.RoleId;
			if (bSuccess)
			{
				Track(TEXT("verifyRedeemCodeSuccess"), {
					      {TEXT("uid"), Uid},
					      {TEXT("roleId"), RoleId},
					      {TEXT("RedeemCode"), ExchangeCode}
				      });
			}
			else
			{
				Track(TEXT("verifyRedeemCodeFail"), {
					      {TEXT("uid"), Uid},
					      {TEXT("roleId"), RoleId},
					      {TEXT("RedeemCode"), ExchangeCode}
				      });
			}

			FONEEngineSDKPSUtils::ExecInGameThread([CallBack,bSuccess, Result, ErrCode, ErrInfo]()
			{
				CallBack(bSuccess, ErrCode, ErrInfo);
			}); });
	}

	const TSharedPtr<FJsonObject> JSONObject = MakeShareable(new FJsonObject);
	JSONObject->SetStringField(TEXT("roleId"), RoleInfo.RoleId);
	JSONObject->SetStringField(TEXT("roleLevel"), RoleInfo.RoleName);
	JSONObject->SetStringField(TEXT("vipLevel"), RoleInfo.Vip);
	JSONObject->SetStringField(TEXT("serverId"), RoleInfo.ServerId);
	FJsonObjectWrapper Wrapper;
	Wrapper.JsonObject = JSONObject;
	FString ValueJson;
	Wrapper.JsonObjectToString(ValueJson);
	ONE_LOG_PRINTF("Starting to use redeem code.");
	one_use_redeem_code(Handle, TCHAR_TO_UTF8(*ExchangeCode), TCHAR_TO_UTF8(*ValueJson), OnGeneralEvent, RequestParam);
}

void FONEEngineSDKPSMainLand::GetRoleList(const FString &ServerId, FRoleListCallback CallBack)
{
	FRequestContext *RequestParam = CreateNewRequestContext();
	{
		FScopeLock Lock(&Mutex);
		GenericCallbacks.Add(RequestParam, [CallBack](bool bSuccess, const FString &Result, int ErrCode, const FString &ErrInfo)
							 { FONEEngineSDKPSUtils::ExecInGameThread([CallBack, bSuccess, Result, ErrCode, ErrInfo]()
																	  {
				TArray<FOneURCRoleInfo> RoleList;
				if (bSuccess && Result.Len() > 0)
				{
					TArray<TSharedPtr<FJsonValue>> List;
					const TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(Result);
					if (FJsonSerializer::Deserialize(JsonReader, List) && List.Num() > 0)
					{
						ONE_LOG_PRINTF("Role list retrieval completed. count: %d", List.Num());
						for (auto& Item : List)
						{
							FOneURCRoleInfo RoleInfo;
							const TSharedPtr<FJsonObject> RoleObject = Item->AsObject();
							RoleInfo.UserId = RoleObject->GetStringField(TEXT("userId"));
							RoleInfo.RoleId = RoleObject->GetStringField(TEXT("roleId"));
							RoleInfo.RoleName = RoleObject->GetStringField(TEXT("roleName"));
							RoleInfo.ServerId = RoleObject->GetStringField(TEXT("serverId"));
							RoleInfo.ServerName = RoleObject->GetStringField(TEXT("serverName"));
							RoleInfo.LastLoginTime = RoleObject->GetStringField(TEXT("lastLogin"));
							int32 Level = 0;
							Level = RoleObject->GetIntegerField(TEXT("lev"));
							RoleInfo.Level = FString::Printf(TEXT("%d"), Level);
							int32 Gender = 0;
							Gender = RoleObject->GetIntegerField(TEXT("gender"));
							RoleInfo.Gender = FString::Printf(TEXT("%d"), Gender);
							int Occupation = 0;
							Occupation = RoleObject->GetIntegerField(TEXT("occupation"));
							RoleInfo.Occupation = FString::Printf(TEXT("%d"), Occupation);
							RoleList.Add(RoleInfo);
						}
					}
					else
					{
						ONE_LOG_ERROR_PRINTF("Role list deserialization failed.");
					}
				}
				CallBack(bSuccess, ErrCode, ErrInfo, RoleList); }); });
	}
	ONE_LOG_PRINTF("Starting to retrieve role list.");
	one_get_role_list(Handle, TCHAR_TO_UTF8(*ServerId), OnGeneralEvent, RequestParam);
}

void FONEEngineSDKPSMainLand::SetLanguage(const FString &Language)
{
	CurrentLanguage = Language;
}

FString FONEEngineSDKPSMainLand::GetCurrentLanguage()
{
	return CurrentLanguage;
}

bool FONEEngineSDKPSMainLand::ExaminStatus()
{
	return one_is_in_review(Handle);
}

void FONEEngineSDKPSMainLand::ShowLoading()
{
	FONEEngineSDKPSUtils::ExecInGameThread([this]()
										   { UPSOneUIManager::Get()->ShowLoading(); });
}

void FONEEngineSDKPSMainLand::HideLoading()
{
	FONEEngineSDKPSUtils::ExecInGameThread([this]()
										   { UPSOneUIManager::Get()->HideLoading(); });
}

void FONEEngineSDKPSMainLand::SetCurrentUserInfo(const FOneUserInfo &UserInfo)
{
	CurrentUserInfo = UserInfo;
}

FString FONEEngineSDKPSMainLand::GetRealUserId() const
{
	return RealUserId;
}

FString FONEEngineSDKPSMainLand::GetRealNameTicket() const
{
	return RealNameTicket;
}

void FONEEngineSDKPSMainLand::SetRealUserId(const FString &InRealUserId)
{
	RealUserId = InRealUserId;
}

void FONEEngineSDKPSMainLand::SetRealNameTicket(const FString &InRealNameTicket)
{
	RealNameTicket = InRealNameTicket;
}

FString FONEEngineSDKPSMainLand::GetAccount() const
{
	return CurrentAccount;
}

FString FONEEngineSDKPSMainLand::GetSecret() const
{
	return CurrentSecret;
}

TMap<FString, FString> &FONEEngineSDKPSMainLand::GetRawUserInfo()
{
	return RawUserInfo;
}

bool FONEEngineSDKPSMainLand::IsInitialized() const
{
	return bInitialized;
}

bool FONEEngineSDKPSMainLand::IsLogin() const
{
	return (!CurrentUserInfo.UserId.IsEmpty() && !CurrentUserInfo.Token.IsEmpty());
}



void FONEEngineSDKPSMainLand::OnFetchAntiAddictionInfo(void *Handle, const char *Result, void *UserData)
{
	const FRequestContext *Param = static_cast<FRequestContext *>(UserData);
	if (Param == nullptr)
	{
		ONE_LOG_ERROR_PRINTF("OnFetchAntiAddictionInfo: UserData is nullptr.");
		return;
	}
	FONEEngineSDKPSMainLand *Self = static_cast<FONEEngineSDKPSMainLand *>(Param->UserData);
	if (Self == nullptr)
	{
		ONE_LOG_ERROR_PRINTF("OnFetchAntiAddictionInfo: Failed to cast UserData to FONEEngineSDKPSMainLand.");
		return;
	}
	ONE_LOG_PRINTF("OnFetchAntiAddictionInfo: UserData cast successful.");
	HandleCallback<FAntiAddictionCallback>(
		Self,
		Param,
		Self->AntiAddictionCallbacks, [Result](FAntiAddictionCallback Callback)
		{
			if (Callback)
			{
				FOneAntiAddictionInfo Info;
				TSharedPtr<FJsonObject> JsonObject;
				const TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(UTF8_TO_TCHAR(Result));
				if (FJsonSerializer::Deserialize(JsonReader, JsonObject) && JsonObject.IsValid())
				{
					ONE_LOG_PRINTF("JSON deserialization successful for AntiAddiction info.");
					JsonObject->TryGetStringField(TEXT("user_id"), Info.UserId);
					JsonObject->TryGetNumberField(TEXT("real_user"), Info.Realuser);
					JsonObject->TryGetNumberField(TEXT("age"), Info.Age);
					JsonObject->TryGetNumberField(TEXT("account_type"), Info.AccountType);
					JsonObject->TryGetNumberField(TEXT("civic_type"), Info.CivicType);
					JsonObject->TryGetNumberField(TEXT("gender"), Info.Gender);

					JsonObject->TryGetNumberField(TEXT("status"), Info.Status);
					JsonObject->TryGetNumberField(TEXT("banned_type"), Info.BannedType);
					JsonObject->TryGetStringField(TEXT("banned_reason"), Info.BannedReason);
				}
				else
				{
					ONE_LOG_PRINTF("JSON deserialization failed for AntiAddiction info.");
				}
				FONEEngineSDKPSUtils::ExecInGameThread([Callback, Info]()
				{
					ONE_LOG_PRINTF("Executing AntiAddiction callback on game thread.");
					Callback(Info);
				});
			} }, true);
}

void FONEEngineSDKPSMainLand::OnAntiAddictionNotification(void *Handle, const char *Result, void *UserData)
{
	const FRequestContext *Param = static_cast<FRequestContext *>(UserData);
	if (Param == nullptr)
	{
		ONE_LOG_PRINTF("OnAntiAddictionNotification: UserData is nullptr.");
		return;
	}
	FONEEngineSDKPSMainLand *Self = static_cast<FONEEngineSDKPSMainLand *>(Param->UserData);
	if (Self == nullptr)
	{
		ONE_LOG_PRINTF("OnAntiAddictionNotification: Failed to cast UserData to FONEEngineSDKPSMainLand.");
		return;
	}
	ONE_LOG_PRINTF("OnAntiAddictionNotification: UserData cast successful.");
	HandleCallback<FAntiAddictionCallback>(
		Self,
		Param,
		Self->AntiAddictionCallbacks, [Result](FAntiAddictionCallback Callback)
		{
			if (Callback)
			{
				FOneAntiAddictionInfo Info;
				TSharedPtr<FJsonObject> JsonObject;
				const TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(UTF8_TO_TCHAR(Result));
				if (FJsonSerializer::Deserialize(JsonReader, JsonObject) && JsonObject.IsValid())
				{
					ONE_LOG_PRINTF("JSON deserialization successful for AntiAddiction info.");
					JsonObject->TryGetStringField(TEXT("user_id"), Info.UserId);
					JsonObject->TryGetNumberField(TEXT("real_user"), Info.Realuser);
					JsonObject->TryGetNumberField(TEXT("age"), Info.Age);
					JsonObject->TryGetNumberField(TEXT("account_type"), Info.AccountType);
					JsonObject->TryGetNumberField(TEXT("civic_type"), Info.CivicType);
					JsonObject->TryGetNumberField(TEXT("gender"), Info.Gender);

					JsonObject->TryGetNumberField(TEXT("status"), Info.Status);
					JsonObject->TryGetNumberField(TEXT("banned_type"), Info.BannedType);
					JsonObject->TryGetStringField(TEXT("banned_reason"), Info.BannedReason);
				}
				else
				{
					ONE_LOG_PRINTF("JSON deserialization failed for AntiAddiction info.");
				}
				FONEEngineSDKPSUtils::ExecInGameThread([Callback, Info]()
				{
					ONE_LOG_PRINTF("Executing AntiAddiction callback on game thread.");
					Callback(Info);
				});
			} }, false // 这里不需要移除
	);
}

void FONEEngineSDKPSMainLand::OnGeneralEvent(void *Handle, bool Success, const char *Result, int ErrCode, const char *ErrInfo, void *UserData)
{
	const FRequestContext *Param = static_cast<FRequestContext *>(UserData);
	if (Param == nullptr)
	{
		ONE_LOG_PRINTF("OnGeneralEvent: UserData is nullptr.");
		return;
	}
	FONEEngineSDKPSMainLand *Self = static_cast<FONEEngineSDKPSMainLand *>(Param->UserData);
	if (Self == nullptr)
	{
		ONE_LOG_PRINTF("OnGeneralEvent: Failed to cast UserData to FONEEngineSDKPSMainLand.");
		return;
	}
	ONE_LOG_PRINTF("OnGeneralEvent: UserData cast successful.");
	HandleCallback<FGenericCallback>(
		Self,
		Param,
		Self->GenericCallbacks, [Success, Result, ErrCode, ErrInfo](const FGenericCallback &Callback)
		{
			if (Callback)
			{
				Callback(Success, UTF8_TO_TCHAR(Result), ErrCode, UTF8_TO_TCHAR(ErrInfo));
			} }, true);
}

void FONEEngineSDKPSMainLand::Track(const FString &EventName, const TMap<FString, FString> &EventParams, FString TaskId)
{
	const TSharedPtr<FJsonObject> JSONObject = MakeShareable(new FJsonObject);
	// 添加 OnlineID
	if (PsOnlineId.Len() > 0)
	{
		JSONObject->SetStringField(FString(TEXT("OnlineID")), PsOnlineId);
	}

	for (const auto &Param : ExtraTrackInfo)
	{
		JSONObject->SetStringField(Param.Key, Param.Value);
	}

	for (const auto &Param : EventParams)
	{
		JSONObject->SetStringField(Param.Key, Param.Value);
	}
	FJsonObjectWrapper Wrapper;
	Wrapper.JsonObject = JSONObject;
	FString ValueJson;
	Wrapper.JsonObjectToString(ValueJson);
	if (ValueJson.Len() > 0)
	{
		ONE_LOG_PRINTF("Track - Event: %s, Task ID: %s, Params: %s", *EventName, *TaskId, *ValueJson);
		one_track_event_with_tid(TCHAR_TO_UTF8(*EventName), TCHAR_TO_UTF8(*TaskId), TCHAR_TO_UTF8(*ValueJson));
	}
}

FONEEngineSDKPSMainLand::FRequestContext *FONEEngineSDKPSMainLand::CreateNewRequestContext()
{
	FRequestContext *RequestParam = new FRequestContext();
	const int Rid = RequestIdCounter.Increment();
	RequestParam->Id = Rid;
	RequestParam->UserData = this;
	return RequestParam;
}
#undef LOCTEXT_NAMESPACE

#endif
