
#include "JsonObjectWrapper.h"
#include "OneEngineSDKSubsystem.h"
#include "OneGlobalBridge.h"
#include "Dom/JsonValue.h"
#include "Serialization/JsonReader.h"
#include "Serialization/JsonSerializer.h"

void UOneEngineSDKSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
	OneGlobalBridge::Get();
}

void UOneEngineSDKSubsystem::Init(FOneInitDelegate InitDelegate)
{
	OneGlobalBridge::Get().init( InitDelegate);
}

EOneEngineSDKRegionType UOneEngineSDKSubsystem::GetRegionType()
{
	if(OneGlobalBridge::Get().isMainland())
	{
		return EOneEngineSDKRegionType::Mainland;
	}else
	{
		return EOneEngineSDKRegionType::Oversea;
	}
}

void UOneEngineSDKSubsystem::Login()
{
	OneGlobalBridge::Get().login();
}

void UOneEngineSDKSubsystem::GetUserTokenList(FOneFetchUserTokenListDelegate OnGetTokenListDelegate)
{
    GetUserTokenListLambda([OnGetTokenListDelegate](bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)
    {
    	OnGetTokenListDelegate.ExecuteIfBound(bSucceed,Code,Msg,TokenList);
    });
}

void UOneEngineSDKSubsystem::GetUserTokenListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)> &OnFinishedLambda)
{
    OneGlobalBridge::Get().GetUserTokenList(OnFinishedLambda);
}

void UOneEngineSDKSubsystem::TokenLogin(const FString& Token, const FString& Uid, EOneEngineThirdType ThirdType)
{
    OneGlobalBridge::Get().TokenLogin(Token,Uid,ThirdType);
}

void UOneEngineSDKSubsystem::ThirdLogin(EOneEngineThirdType ThirdType, bool bForcedLogin)
{
	OneGlobalBridge::Get().ThirdLogin(ThirdType,bForcedLogin);
}

void UOneEngineSDKSubsystem::GuestLogin()
{
	OneGlobalBridge::Get().GuestLogin();
}

void UOneEngineSDKSubsystem::SwitchAccount()
{
	OneGlobalBridge::Get().logout();
}

FOneUserInfo UOneEngineSDKSubsystem::GetUserInfo()
{
	return OneGlobalBridge::Get().getUserInfo();
}

void UOneEngineSDKSubsystem::Pay(const FOnePaymentInfo& PaymentInfo)
{
	// private String orderId;
	// private String productId;
	// private String roleId;
	// private int serverId;
	// private String ext;
	// private int price;
	// private String productName;
	// private int productCount;
	// private String roleName;
	// private String serverName;
	// private String callbackUrl;
	FJsonObjectWrapper Wrapper;
	TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
	JsonObj->SetStringField(TEXT("orderId"), PaymentInfo.OrderId);
	JsonObj->SetStringField(TEXT("productId"), PaymentInfo.ProductId);
	JsonObj->SetStringField(TEXT("roleId"), PaymentInfo.RoleId);
	JsonObj->SetStringField(TEXT("serverId"), PaymentInfo.GameServerId);
	JsonObj->SetStringField(TEXT("ext"), PaymentInfo.ExtInfo);
	JsonObj->SetStringField(TEXT("price"), PaymentInfo.Price);
	JsonObj->SetStringField(TEXT("productName"), PaymentInfo.ProductName);
	JsonObj->SetStringField(TEXT("productCount"), PaymentInfo.ProductCount);
	JsonObj->SetStringField(TEXT("roleName"), PaymentInfo.RoleName);
	JsonObj->SetStringField(TEXT("serverName"), PaymentInfo.ServerName);
	JsonObj->SetStringField(TEXT("callbackUrl"), PaymentInfo.PaySuccessUrl);
	Wrapper.JsonObject = JsonObj;
	FString Result;
	Wrapper.JsonObjectToString(Result);
	OneGlobalBridge::Get().pay(Result);
}

// 角色事件打点
void UOneEngineSDKSubsystem::TrackEventRoleLoginSucceed(const FOneRoleInfo& RoleInfo, const FString& Ip,
                                                        const FString& Port)
{
	FJsonObjectWrapper Wrapper;
	TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
	JsonObj->SetStringField(TEXT("roleId"), RoleInfo.RoleId);
	JsonObj->SetStringField(TEXT("roleName"), RoleInfo.RoleName);
	JsonObj->SetStringField(TEXT("serverId"), RoleInfo.ServerId);
	JsonObj->SetStringField(TEXT("lv"), RoleInfo.Level);
	JsonObj->SetStringField(TEXT("vip"), RoleInfo.Vip);
	JsonObj->SetStringField(TEXT("combatValue"), RoleInfo.CombatValue);
	JsonObj->SetStringField(TEXT("serverName"), RoleInfo.ServerName);
	Wrapper.JsonObject = JsonObj;
	FString Result;
	Wrapper.JsonObjectToString(Result);
	OneGlobalBridge::Get().trackEventRoleLogin(Result,Ip,Port);
}

void UOneEngineSDKSubsystem::TrackEventRoleLoginError(const FOneRoleInfo& RoleInfo, const FString& Ip,
                                                      const FString& Port, const FString& Code, const FString& Msg)
{
	FJsonObjectWrapper Wrapper;
	TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
	JsonObj->SetStringField(TEXT("roleId"), RoleInfo.RoleId);
	JsonObj->SetStringField(TEXT("roleName"), RoleInfo.RoleName);
	JsonObj->SetStringField(TEXT("serverId"), RoleInfo.ServerId);
	JsonObj->SetStringField(TEXT("lv"), RoleInfo.Level);
	JsonObj->SetStringField(TEXT("vip"), RoleInfo.Vip);
	JsonObj->SetStringField(TEXT("combatValue"), RoleInfo.CombatValue);
	JsonObj->SetStringField(TEXT("serverName"), RoleInfo.ServerName);
	Wrapper.JsonObject = JsonObj;
	FString Result;
	Wrapper.JsonObjectToString(Result);
	OneGlobalBridge::Get().trackEventRoleLoginError(Result,Ip,Port,Code,Msg);
}

void UOneEngineSDKSubsystem::TrackEventRoleLogout(const FOneRoleInfo& RoleInfo)
{
	FJsonObjectWrapper Wrapper;
	TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
	JsonObj->SetStringField(TEXT("roleId"), RoleInfo.RoleId);
	JsonObj->SetStringField(TEXT("roleName"), RoleInfo.RoleName);
	JsonObj->SetStringField(TEXT("serverId"), RoleInfo.ServerId);
	JsonObj->SetStringField(TEXT("lv"), RoleInfo.Level);
	JsonObj->SetStringField(TEXT("vip"), RoleInfo.Vip);
	JsonObj->SetStringField(TEXT("combatValue"), RoleInfo.CombatValue);
	JsonObj->SetStringField(TEXT("serverName"), RoleInfo.ServerName);
	Wrapper.JsonObject = JsonObj;
	FString Result;
	Wrapper.JsonObjectToString(Result);
	OneGlobalBridge::Get().trackEventRoleLogout(Result);
}

void UOneEngineSDKSubsystem::TrackEventRoleCreate(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
	FJsonObjectWrapper Wrapper;
	TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
	JsonObj->SetStringField(TEXT("roleId"), RoleInfo.RoleId);
	JsonObj->SetStringField(TEXT("roleName"), RoleInfo.RoleName);
	JsonObj->SetStringField(TEXT("serverId"), RoleInfo.ServerId);
	JsonObj->SetStringField(TEXT("lv"), RoleInfo.Level);
	JsonObj->SetStringField(TEXT("vip"), RoleInfo.Vip);
	JsonObj->SetStringField(TEXT("combatValue"), RoleInfo.CombatValue);
	JsonObj->SetStringField(TEXT("serverName"), RoleInfo.ServerName);
	Wrapper.JsonObject = JsonObj;
	FString Result;
	Wrapper.JsonObjectToString(Result);
	OneGlobalBridge::Get().trackEventRoleCreate(Result,Ip,Port);
}

void UOneEngineSDKSubsystem::TrackEventRoleLevelUp(const FOneRoleInfo& RoleInfo)
{
	FJsonObjectWrapper Wrapper;
	TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
	JsonObj->SetStringField(TEXT("roleId"), RoleInfo.RoleId);
	JsonObj->SetStringField(TEXT("roleName"), RoleInfo.RoleName);
	JsonObj->SetStringField(TEXT("serverId"), RoleInfo.ServerId);
	JsonObj->SetStringField(TEXT("lv"), RoleInfo.Level);
	JsonObj->SetStringField(TEXT("vip"), RoleInfo.Vip);
	JsonObj->SetStringField(TEXT("combatValue"), RoleInfo.CombatValue);
	JsonObj->SetStringField(TEXT("serverName"), RoleInfo.ServerName);
	Wrapper.JsonObject = JsonObj;
	FString Result;
	Wrapper.JsonObjectToString(Result);
	OneGlobalBridge::Get().trackEventRoleUpdate(Result);
}

// 资源事件打点
void UOneEngineSDKSubsystem::GameResReqEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode,
                                             const FString& ErrorMsg)
{
	OneGlobalBridge::Get().trackGameResReqEvent(static_cast<int>(State),Url,ErrorCode,ErrorMsg);
}

void UOneEngineSDKSubsystem::GameUpdateAssetEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode,
                                                  const FString& ErrorMsg)
{
	OneGlobalBridge::Get().trackGameUpdateAssetEvent(static_cast<int>(State),Url,ErrorCode,ErrorMsg);
}

void UOneEngineSDKSubsystem::GameResDecEvent(EOneResEventState State, const FString& ErrorMsg)
{
	OneGlobalBridge::Get().trackGameResDecEvent(static_cast<int>(State),ErrorMsg);
}

void UOneEngineSDKSubsystem::GameGetServerListEvent(EOneResEventState State, const FString& Url,
                                                    const FString& ErrorCode, const FString& ErrorMsg)
{
	OneGlobalBridge::Get().trackGameGetServerListEvent(static_cast<int>(State),Url,
	                                                   ErrorCode,ErrorMsg);
}

// 自定义事件打点
void UOneEngineSDKSubsystem::TrackEvent(const FString& Name, const TMap<FString, FString>& Payload)
{
	TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
	for (const auto& Pair : Payload)
	{
		JsonObj->SetStringField(Pair.Key, Pair.Value);
	}
	FJsonObjectWrapper Wrapper;
	Wrapper.JsonObject = JsonObj;
	FString Result;
	Wrapper.JsonObjectToString(Result);
	OneGlobalBridge::Get().trackEvent(Name,Result);
}

void UOneEngineSDKSubsystem::TrackEventAD(const FString& Name, const TMap<FString, FString>& Payload)
{
	TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
	for (const auto& Pair : Payload)
	{
		JsonObj->SetStringField(Pair.Key, Pair.Value);
	}
	FJsonObjectWrapper Wrapper;
	Wrapper.JsonObject = JsonObj;
	FString Result;
	Wrapper.JsonObjectToString(Result);
	OneGlobalBridge::Get().trackEventAD(Name,Result);
}

//进入游戏场景打点
void UOneEngineSDKSubsystem::TrackEventEnterGameScene(const FString& SceneName, int32 Period,
                                                      const TMap<FString, FString>& HintMap)
{
	TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
	for (const auto& Pair : HintMap)
	{
		JsonObj->SetStringField(Pair.Key, Pair.Value);
	}
	FJsonObjectWrapper Wrapper;
	Wrapper.JsonObject = JsonObj;
	FString Result;
	Wrapper.JsonObjectToString(Result);
	OneGlobalBridge::Get().trackEventEnterGameScene(SceneName,
	                                                Period,Result);
}

//退出游戏打点
void UOneEngineSDKSubsystem::TrackEventExitGameScene()
{
	OneGlobalBridge::Get().trackEventExitGameScene();
}

//设备打点额外信息
void UOneEngineSDKSubsystem::TrackEventAddExtraDeviceInfo(const TMap<FString, FString>& ExtraDeviceInfo)
{
	TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
	for (const auto& Pair : ExtraDeviceInfo)
	{
		JsonObj->SetStringField(Pair.Key, Pair.Value);
	}
	FJsonObjectWrapper Wrapper;
	Wrapper.JsonObject = JsonObj;
	FString Result;
	Wrapper.JsonObjectToString(Result);
	OneGlobalBridge::Get().trackEventAddExtraDeviceInfo(Result);
}

//万能方法接口
bool UOneEngineSDKSubsystem::IsCommonFunctionSupported(const FString& FuncName)
{
	return OneGlobalBridge::Get().isSupportedCommonFunction(FuncName);
}

void UOneEngineSDKSubsystem::CallCommonFunction(const FString& FuncName, const FString& Params,
                                                FOneCommonFunctionDelegate CommonFunctionDelegate)
{
	OneGlobalBridge::Get().callCommonFunction(FuncName,Params, CommonFunctionDelegate);
}

bool UOneEngineSDKSubsystem::IsLoggedIn()
{
	return OneGlobalBridge::Get().isHasLogin();
}

void UOneEngineSDKSubsystem::EnableDebugMode(bool Enable)
{
	OneGlobalBridge::Get().setDebug(Enable);
}

void UOneEngineSDKSubsystem::SetUpConfigAppID(const FString& AppId)
{
	OneGlobalBridge::Get().setUpConfigAppId(AppId);
}

FString UOneEngineSDKSubsystem::GetAppId()
{
    return OneGlobalBridge::Get().GetGlobalOneAppId();
}

bool UOneEngineSDKSubsystem::IsDebugMode()
{
	return OneGlobalBridge::Get().isDebug();
}

void UOneEngineSDKSubsystem::OpenUserCenter()
{
	OneGlobalBridge::Get().openUserCenter();
}

void UOneEngineSDKSubsystem::GetQRCodeScanResult(FOneGetQRCodeScanResultDelegate OnGetQRCodeScanResultDelegate)
{
	OneGlobalBridge::Get().scanQRCode(OnGetQRCodeScanResultDelegate);
}

void UOneEngineSDKSubsystem::OpenComplianceOnWebView()
{
	OneGlobalBridge::Get().openComplianceOnWebView();
}

void UOneEngineSDKSubsystem::EnterAccountCancellation()
{

}

FString UOneEngineSDKSubsystem::GetChannelId()
{
	return FString::FromInt(OneGlobalBridge::Get().getChannelId());
}

FString UOneEngineSDKSubsystem::GetChannelMediaId()
{
	return OneGlobalBridge::Get().getChannelMediaId();
}

int32 UOneEngineSDKSubsystem::GetPlatformOS()
{
	return OneGlobalBridge::Get().getPlatformOs();
}


void UOneEngineSDKSubsystem::GetChannelPlatform(FOneGetPlatformDelegate OnGetPlatformResultDelegate)
{
	GetChannelPlatformLambda([OnGetPlatformResultDelegate](bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)
	{
		OnGetPlatformResultDelegate.ExecuteIfBound(bSucceed,Code,Msg,Platform);
	});
}

void UOneEngineSDKSubsystem::GetChannelPlatformLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)> &OnFinishedLambda)
{
	OneGlobalBridge::Get().getChannelPlatform(OnFinishedLambda);
}

void UOneEngineSDKSubsystem::GetUserLocationInfo(FOneUserLocationInfoDelegate LocationInfoDelegate)
{
	FOneUserLocationInfoDelegate* CallbackCopy = new FOneUserLocationInfoDelegate(LocationInfoDelegate);
	OneGlobalBridge::Get().getUserLocationInfo([CallbackCopy](const FOneUserLocationInfo& LocationInfo)
	{
		CallbackCopy->ExecuteIfBound(LocationInfo);
		delete CallbackCopy;
	});
}

void UOneEngineSDKSubsystem::GetUserLocationInfoLambda(const TFunction<void(const FOneUserLocationInfo& LocationInfo)> &OnFinishedLambda)
{
	OneGlobalBridge::Get().getUserLocationInfo( OnFinishedLambda);

}
void UOneEngineSDKSubsystem::GetDeviceInfo(FOneGetDeviceInfoDelegate Delegate)
{
	GetDeviceInfoLambda([Delegate](const FOneDeviceInfo& DeviceInfo)
	{
		Delegate.ExecuteIfBound(DeviceInfo);
	});
}

void UOneEngineSDKSubsystem::GetDeviceInfoLambda(const TFunction<void(const FOneDeviceInfo& DeviceInfo)> &OnFinishedLambda)
{
	FOneDeviceInfo DeviceInfo;
	FString DeviceInfoStr=OneGlobalBridge::Get().getDeviceInfo();
	FJsonObjectWrapper Wrapper;
	Wrapper.JsonObjectFromString(DeviceInfoStr);
	DeviceInfo.DeviceId = Wrapper.JsonObject->GetStringField(TEXT("deviceID"));
	DeviceInfo.DeviceSys = Wrapper.JsonObject->GetStringField(TEXT("deviceSys"));

	TSharedPtr<FJsonObject> ExtObj = Wrapper.JsonObject->GetObjectField(TEXT("ext"));
	if (ExtObj)
	{
		for (const auto& Pair : ExtObj->Values)
		{
			FString Key = Pair.Key;
			FString Value;
			if (Pair.Value->TryGetString(Value))
			{
				DeviceInfo.Ext.Add(Key, Value);
			}
		}
	}
	OnFinishedLambda(DeviceInfo);
}

void UOneEngineSDKSubsystem::FetchAntiAddictionInfo(FOneFetchAntiAddictionInfoDelegate OnFetchAntiAddictionInfo)
{
	
	OneGlobalBridge::Get().fetchAntiAddictionInfo(OnFetchAntiAddictionInfo);
}

void UOneEngineSDKSubsystem::StartAntiAddictionNotify(const FString& ServerId, const FString& RoleId)
{
	OneGlobalBridge::Get().startAntiAddictionNotify(RoleId,ServerId);
}

void UOneEngineSDKSubsystem::StopAntiAddictionNotify()
{
	OneGlobalBridge::Get().stopAntiAddictionNotify();
}

void UOneEngineSDKSubsystem::SetShowDefaultActivationResultToast(bool bShow)
{
	OneGlobalBridge::Get().setShowActivationResultToast(bShow);
}

void UOneEngineSDKSubsystem::DisplayCDKeyDialog(FOneGenericResultDelegate OnCDKeyActivateResult,
                                                const FString& ServerId)
{
	OneGlobalBridge::Get().displayCDKeyDialog(ServerId,OnCDKeyActivateResult);
}

// 查询用户有没有激活资格
void UOneEngineSDKSubsystem::QueryUserActiveQualification(const FString& ServerId, FOneQueryUserActiveQualificationResultDelegate Callback)
{
	OneGlobalBridge::Get().QueryUserActiveQualification(ServerId,[Callback](bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)
	{
		Callback.ExecuteIfBound(bSucceed,Code,ErrorMsg,QualificationInfo);
	});
}

void UOneEngineSDKSubsystem::QueryUserActiveQualificationLambda(const FString& ServerId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)> &OnFinishedLambda)
{
	OneGlobalBridge::Get().QueryUserActiveQualification(ServerId,[OnFinishedLambda](bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)
	{
		OnFinishedLambda(bSucceed,Code,ErrorMsg,QualificationInfo);
	});
}
	
// 激活设备 
void UOneEngineSDKSubsystem::ActivateDevice(const FString& ServerId, FOneActivateDeviceResultDelegate OneActivateDeviceResultDelegate)
{
	FOneQueryActCodeResultDelegate * CallbackCopy = new FOneQueryActCodeResultDelegate(OneActivateDeviceResultDelegate);
	ActivateDeviceLambda(ServerId, [CallbackCopy](bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)
	{
		CallbackCopy->ExecuteIfBound(bSucceed,bNeedActCode,ActCodePrompt,Code,ErrorMsg);
		delete CallbackCopy;
	});
}

void UOneEngineSDKSubsystem::ActivateDeviceLambda(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> &OnFinishedLambda)
{
	OneGlobalBridge::Get().queryActCode(ServerId, OnFinishedLambda);
}
	

void UOneEngineSDKSubsystem::QueryActCode(const FString& ServerId,
                                          FOneQueryActCodeResultDelegate OnQueryActCodeResultDelegate)
{
	FOneQueryActCodeResultDelegate * CallbackCopy = new FOneQueryActCodeResultDelegate(OnQueryActCodeResultDelegate);
	QueryActCodeLambda(ServerId, [CallbackCopy](bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)
	{
		CallbackCopy->ExecuteIfBound(bSucceed,bNeedActCode,ActCodePrompt,Code,ErrorMsg);
		delete CallbackCopy;
	});
}

void UOneEngineSDKSubsystem::QueryActCodeLambda(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> &OnFinishedLambda)
{
	OneGlobalBridge::Get().queryActCode(ServerId, OnFinishedLambda);

}

void UOneEngineSDKSubsystem::ExchangeActCode(const FString& ServerId, const FString& ActCode, FOneGenericResultDelegate GenericResultDelegate)
{
	FOneGenericResultDelegate * CallbackCopy = new FOneGenericResultDelegate(GenericResultDelegate);
	OneGlobalBridge::Get().exchangeActCode(ServerId,ActCode, [CallbackCopy](bool bSuccess, int32 Code, const FString& Msg)
	{
		CallbackCopy->ExecuteIfBound(bSuccess,Code,Msg);
		delete CallbackCopy;
	});
}

void UOneEngineSDKSubsystem::ExchangeActCodeLambda(const FString& ServerId, const FString& ActCode, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> &OnFinishedLambda)
{
	OneGlobalBridge::Get().exchangeActCode(ServerId,ActCode,OnFinishedLambda);
}

void UOneEngineSDKSubsystem::RedeemCouponCode(const FString& CouponCode, const FString& ServerId, const FString& RoleId,
                                              const FString& RoleLevel,
                                              const FString& VipLevel, const TMap<FString, FString>& ExtraInfo,
                                              FOneGenericResultDelegate OnRedeemCouponResult)
{
	FOneGenericResultDelegate * CallbackCopy =new FOneGenericResultDelegate(OnRedeemCouponResult);
	
	RedeemCouponCodeLambda(CouponCode,RoleId,ServerId,RoleLevel,VipLevel,
										   ExtraInfo, [CallbackCopy](bool bSuccess, int32 Code, const FString& Msg)
										   {
											   CallbackCopy->ExecuteIfBound(bSuccess,Code,Msg);
										   		delete CallbackCopy;
										   });
}

void UOneEngineSDKSubsystem::RedeemCouponCodeLambda(const FString& CouponCode, const FString& ServerId, const FString& RoleId, const FString& RoleLevel, const FString& VipLevel, const TMap<FString, FString>& ExtraInfo,  const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> &OnFinishedLambda)
{
	FJsonObjectWrapper Wrapper;
	TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
	for (auto& Pair : ExtraInfo)
	{
		JsonObj->SetStringField(Pair.Key, Pair.Value);
	}
	FString JSON;
	Wrapper.JsonObject = JsonObj;
	Wrapper.JsonObjectToString(JSON);
	OneGlobalBridge::Get().redeemCouponCode(CouponCode,RoleId,ServerId,RoleLevel,VipLevel,
										   JSON, OnFinishedLambda);
}
void UOneEngineSDKSubsystem::FetchUserRoleInfoList(FOneFetchUserRoleInfoListDelegate OnFetchUserRoleListDelegate,
                                                   const FString& ServerId)
{
	FOneFetchUserRoleInfoListDelegate * CallbackCopy = new FOneFetchUserRoleInfoListDelegate(OnFetchUserRoleListDelegate);
	FetchUserRoleInfoListLambda([CallbackCopy](bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)
	{
		CallbackCopy->ExecuteIfBound(bSucceed,Code,Msg,RoleList);
		delete CallbackCopy;
	},ServerId);
}

void UOneEngineSDKSubsystem::FetchUserRoleInfoListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)> &OnFinishedLambda, const FString& ServerId)
{
	int ServerIdInt = 0;
	if(!ServerId.IsEmpty())
	{
		ServerIdInt = FCString::Atoi(*ServerId);
	}
	OneGlobalBridge::Get().getUserRoleInfoList(ServerIdInt,OnFinishedLambda);
}

void UOneEngineSDKSubsystem::Bind(FOneBindResultDelegate StartBindPhoneDelegate, EOneEngineThirdType BindType)
{

	OneGlobalBridge::Get().bindThirdAccount(static_cast<int>(BindType), StartBindPhoneDelegate);
}

void UOneEngineSDKSubsystem::UserAuthentication(FOneUserAuthenticationResultDelegate OnUserAuthenticationResultDelegate)
{
	OnUserAuthenticationResultDelegate.ExecuteIfBound(false,-101,false,TEXT("not support this method"));
}

// 获取声明的权限 可用于展示
TArray<FOnePermissionInfo> UOneEngineSDKSubsystem::GetPermissions()
{
	return OneGlobalBridge::Get().getPermissionList();
}

// 检查是否权限已授权
bool UOneEngineSDKSubsystem::CheckSelfPermission(EOnePermissionType Type)
{
	return OneGlobalBridge::Get().checkSelfPermission(static_cast<int>(Type));
}

// 申请权限
void UOneEngineSDKSubsystem::RequestPermission(EOnePermissionType Type,
                                               FOneRequestPermissionResultDelegate RequestDelegate)
{
	
	OneGlobalBridge::Get().requestPermission( static_cast<int>(Type),"", RequestDelegate);
}

void UOneEngineSDKSubsystem::RequestPermission(EOnePermissionType Type,
                                               FOneRequestPermissionResultDelegate RequestDelegate,
                                               const TArray<FString>& Tips)
{
	
	// 创建一个Json写入器
	FString JsonString;
	TSharedRef<TJsonWriter<TCHAR>> JsonWriter = TJsonWriterFactory<>::Create(&JsonString);
	JsonWriter->WriteArrayStart();
	for (const FString& Value : Tips)
	{
		JsonWriter->WriteValue(Value);
	}
	JsonWriter->WriteArrayEnd();
	JsonWriter->Close();

	OneGlobalBridge::Get().requestPermission( static_cast<int>(Type),JsonString, RequestDelegate);
}

//  关闭剪切板权限
void UOneEngineSDKSubsystem::CloseClipboardPermission()
{
	OneGlobalBridge::Get().closeClipboardPermission();
}

// 打开应用权限设置页
void UOneEngineSDKSubsystem::OpenApplicationSetting()
{
	OneGlobalBridge::Get().openApplicationSetting();
}

// 打开AIHelp客服
void UOneEngineSDKSubsystem::OpenAIHelp(EOneAIHelpType Type, const FString& RoleId, const FString& ServerId,
                                        const FString& RoleName)
{
	if (Type == EOneAIHelpType::RobotChat)
	{
		OneGlobalBridge::Get().openAIHelpChat(RoleId,RoleName,ServerId);
	}
	else
	{
		OneGlobalBridge::Get().openAIHelpFAQ(RoleId,RoleName,ServerId);
	}
}

// 打开自定义客服
void UOneEngineSDKSubsystem::OpenCustomerService(const FString& RoleID, const FString& RoleName,
                                                 const FString& ServerID)
{
	OneGlobalBridge::Get().openCustomService(RoleID,RoleName,ServerID, true);
}

// 文本翻译
void UOneEngineSDKSubsystem::TranslateLambda(const FString& Text,const TFunction<void(bool bSucceed, const FString& Result,const FString& ErrorMsg)> &OnFinishedLambda)
{
	OneGlobalBridge::Get().translate(Text, OnFinishedLambda);
}
void UOneEngineSDKSubsystem::Translate(const FString& Text, FOneTranslateResultDelegate Callback)
{
	FOneTranslateResultDelegate * CallbackCopy = new FOneTranslateResultDelegate(Callback);
	OneGlobalBridge::Get().translate(Text, [CallbackCopy](bool bSucceed, const FString& Result,const FString& ErrorMsg)
	{
		CallbackCopy->ExecuteIfBound(bSucceed,Result,ErrorMsg);
		delete CallbackCopy;
	});
}

// 获取当前设置语言
FString UOneEngineSDKSubsystem::GetCurrentLanguage()
{
	return "";
}

// 设置语言
void UOneEngineSDKSubsystem::SetLanguage(const FString& Code)
{
	OneGlobalBridge::Get().setLanguage(Code);
}

// 获取本地语言列表
TArray<FString> UOneEngineSDKSubsystem::GetSupportedLanguageCodeList()
{
	TArray<FString> LanguageCodeList;
	FString Languages=OneGlobalBridge::Get().getLocalLanguages();
	if (!Languages.IsEmpty())
	{
		TArray<TSharedPtr<FJsonValue>> JsonArray;
		TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(Languages);
		if (!FJsonSerializer::Deserialize(JsonReader, JsonArray))
		{
			UE_LOG(LogJson, Warning, TEXT("JsonArrayStringToUStruct - Unable to parse. json=[%s]"), *Languages);
		}
		else
		{
			if (JsonArray.Num() > 0)
			{
				for (int32 i = 0; i < JsonArray.Num(); i++)
				{
					FString Obj = JsonArray[i]->AsString();
					LanguageCodeList.Add(Obj);
				}
			}
		}
	}
	return LanguageCodeList;
}

// 设置屏幕方向
void UOneEngineSDKSubsystem::SetScreenOrientation(EOneScreenOrientation Orientation)
{
}

// 获取档位信息(iOS专有接口)
void UOneEngineSDKSubsystem::GetProductList(const TArray<FString>& ProductIds,
                                            FOneProductInfoDelegate ProductResultDelegate)
{
	// 创建一个Json写入器
	
	OneGlobalBridge::Get().getProductList(ProductIds, ProductResultDelegate);
}

//获取审核开关(iOS专有接口)
bool UOneEngineSDKSubsystem::ExaminStatus()
{
	return OneGlobalBridge::Get().examinStatus();
}

//是否检测BundleId(iOS专有接口)
void UOneEngineSDKSubsystem::ShouldVerifyBundleId(bool bShouldVerify)
{
}

// 分享SDK
void UOneEngineSDKSubsystem::ShareDataToApp(EOneShareAppTarget AppTarget, const EOneShareType Type,
                                            const FOneShareData& Data, FOneGenericResultDelegate OnShareResult)
{
	
	OneGlobalBridge::Get().share( AppTarget,Type,Data, OnShareResult);
}

// 推送相关接口
// 注册推送
void UOneEngineSDKSubsystem::StartUpdatePushData(FOneStartUpdatePushDataDelegate Callback)
{
	OneGlobalBridge::Get().registerPush(Callback);
}

// 设置点击推送消息后的回调
void UOneEngineSDKSubsystem::SetupNotificationCallback(FOneNotificationDelegate Callback)
{
	OneGlobalBridge::Get().setReceiveMsgListener(Callback);
}

// 获取系统推送开关状态 和 应用推送开关状态
void UOneEngineSDKSubsystem::GetPushStatus(FOnePushStatusDelegate Callback)
{
	FString Result=OneGlobalBridge::Get().getPushStatus();
	FOnePushStatus PushStatus;
	if (!Result.IsEmpty())
	{
		FJsonObjectWrapper Wrapper;
		Wrapper.JsonObjectFromString(Result);
		PushStatus.bIsAppOpen = Wrapper.JsonObject->GetBoolField(TEXT("appOpen"));
		PushStatus.bIsSysOpen = Wrapper.JsonObject->GetBoolField(TEXT("sysOpen"));
	}
	Callback.ExecuteIfBound(PushStatus);
}

// 设置应用推送开关状态
void UOneEngineSDKSubsystem::SetProviderPushState(bool bProviderPushState, FOneGenericResultDelegate Callback)
{
	OneGlobalBridge::Get().setProviderPushState(bProviderPushState, Callback);
}

// 推送设置用户信息
void UOneEngineSDKSubsystem::SetPushUserInfo(const FString& ServerId, const FString& RoleId,
                                             FOneGenericResultDelegate Callback)
{
	OneGlobalBridge::Get().bindUserId(ServerId,RoleId, Callback);
}

// 推送解绑用户信息
void UOneEngineSDKSubsystem::UnSetPushUserInfo(FOneGenericResultDelegate Callback)
{
	OneGlobalBridge::Get().unbindUserId(Callback);
}

// 获取推送类型列表
void UOneEngineSDKSubsystem::GetPushTypeInfoList(FOneGetPushTypeInfoListDelegate Callback)
{
	OneGlobalBridge::Get().getPushTypeInfoList(Callback);
}

// 批量设置推送类型开关状态
void UOneEngineSDKSubsystem::UpdatePushTypeList(const TArray<FOnePushTypeInfo>& PushTypeList,
                                                FOneGenericResultDelegate Callback)
{
	OneGlobalBridge::Get().updatePushTypeList(PushTypeList, Callback);
}

// 夜间勿扰配置的回调
void UOneEngineSDKSubsystem::GetPushNotDisturb(FOnePushNotDisturbInfoDelegate Callback)
{
	OneGlobalBridge::Get().getPushNotDisturbInfo(Callback);
}

// 设置勿扰模式
void UOneEngineSDKSubsystem::UpdatePushNotDisturb(const FOnePushNotDisturbInfo& NotDisturbInfo,
                                                  FOneGenericResultDelegate Callback)
{
	OneGlobalBridge::Get().updatePushNotDisturb(NotDisturbInfo, Callback);
}

void UOneEngineSDKSubsystem::KillProcess()
{
	OneGlobalBridge::Get().killProcess();
}

bool UOneEngineSDKSubsystem::ACELogin(const FString& accountId, int accountTypeCommandId, int worldId, FOneOnGetClientPacket delegate)
{
	return false;
}

bool UOneEngineSDKSubsystem::ACEClientPacketReceive(const TArray<uint8>& data)
{
	return false;
}

bool UOneEngineSDKSubsystem::ACELogout()
{
	return false;
}
void UOneEngineSDKSubsystem::GetIpInfo(const FString& Ip,FOnGetIpInfoResultDelegate Delegate)
{
	OneGlobalBridge::Get().GetIpInfo(Ip,Delegate);
}

/// 全球独有的 评价接口和 修改屏幕亮度接口
float UOneEngineSDKSubsystem::GetScreenBrightness()
{
	return OneGlobalBridge::Get().GetScreenBrightness();
}

void UOneEngineSDKSubsystem::SetScreenBrightness(float BrightnessValue)
{
	OneGlobalBridge::Get().SetScreenBrightness(BrightnessValue);
}
	
void UOneEngineSDKSubsystem::RecoverScreenBrightness()
{
	OneGlobalBridge::Get().RecoverScreenBrightness();
}
///保持屏幕常亮
void UOneEngineSDKSubsystem::SwitchScreenPermanentBrightnessState(bool bIsTurnOn)
{
	OneGlobalBridge::Get().SwitchScreenPermanentBrightnessState(bIsTurnOn);
}
	

///评价接口
void UOneEngineSDKSubsystem::InAppRequestStoreReview(FOneGenericResultDelegate Delegate)
{
	OneGlobalBridge::Get().InAppRequestStoreReview(Delegate);
}

/// AppLink Ios有这个参数
void UOneEngineSDKSubsystem::RequestStoreReview(FString AppLink)
{
	OneGlobalBridge::Get().RequestStoreReview(AppLink);
}

/// 获取渲染器配置文件地址
FString UOneEngineSDKSubsystem::GetRenderConfigFilePath()
{
	return 	OneGlobalBridge::Get().GetRenderConfigFilePath();
}

// 安全锁相关接口
void UOneEngineSDKSubsystem::UnlockSafeLockUsingPushNotification(const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
	OnUnlockSafeLockResultDelegate.ExecuteIfBound(EOneUnlockSafeLockResult(),"", -101, TEXT(" not support this method"),EOneUnlockSafeLockType::PushNotification);
}

void UOneEngineSDKSubsystem::UnlockSafeLockUsingDynamicCode(const FString& DynamicCode, const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
	OnUnlockSafeLockResultDelegate.ExecuteIfBound(EOneUnlockSafeLockResult(),"", -101, TEXT(" not support this method"),EOneUnlockSafeLockType::PushNotification);
}

void UOneEngineSDKSubsystem::StopUnlockSafeLockUsingPushNotification()
{
}

// 全球独有的打开Naver论坛
void UOneEngineSDKSubsystem::OpenCommunityByGame(EOneNaverGameType Type, const FString& Pid, bool Scheduled, FOneLoadDelegate LoadCallback, FOneUnloadDelegate UnloadCallback, FOneInGameMenuDelegate InGameMenuCallback)
{
	OneGlobalBridge::Get().OpenCommunityByGame(Type,Pid,Scheduled,LoadCallback,UnloadCallback,InGameMenuCallback);
}

void UOneEngineSDKSubsystem::TerminateCommunity()
{
	OneGlobalBridge::Get().TerminateCommunity();
}

void UOneEngineSDKSubsystem::GetProviderPushState(FOnGetPushStateDelegate Delegate)
{
	OneGlobalBridge::Get().GetProviderPushState(Delegate);
}

void UOneEngineSDKSubsystem::SetAnalyticsCollectionEnabled(bool bEnable)
{
	OneGlobalBridge::Get().SetAnalyticsCollectionEnabled(bEnable);
}

bool UOneEngineSDKSubsystem::IsInstalledApp(EOneShareAppTarget AppTarget)
{
	return OneGlobalBridge::Get().IsInstalledApp(AppTarget);
}