#pragma once

#ifndef USE_ANDROID_JNI
#define USE_ANDROID_JNI

#endif // USE_ANDROID_JNI
#include "Android/AndroidJavaEnv.h"

#include "OneEngineSDKSubsystem.h"

class OneGlobalBridge
{
public:
	static OneGlobalBridge& Get()
	{
		static OneGlobalBridge oneGlobalBridge;
		return oneGlobalBridge;
	}
	typedef TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> OnGenericResult;

	typedef  TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)> OnGetDeviceTokenResult;

	typedef TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)> OnGetPlatformResult;
	
	/**
	 * 1.初始化
	 */
	void init(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate);

	/**
	 * 是 大陆还是海外,默认 true 为大陆
	 */
	bool isMainland();

	/**
	 * 2. 设置debug
	 * @param isDebug
	 */
	void setDebug(bool isDebug);

	bool isDebug();

	/**
	* 3.登录
	*/
	void login();

	/**
	 * 4.退出登录
	 */
	void logout();

	/**
	 * 5. 是否已经登录
	 */
	bool isHasLogin();


	/**
	 * 7. 获取UserId
	 */
	FOneUserInfo getUserInfo();

	/**
	 * 8. 打开用户中心()
	 */
	void openUserCenter();


	/**
	 * 9. 支付接口 统一
	 */
	void pay(FString info);


	/**
	 * 10. 获取支付档位 全球
	 */
	void getProductList(TArray<FString> productIds, UOneEngineSDKSubsystem::FOneProductInfoDelegate callback);

	/**
	 * 11. 获取支付类型 全球
	 */
	int getPayChannelId();

	/**
	 * 是否显示激活码结果 toast
	 * @param isShowDefaultToast
	 */
	void setShowActivationResultToast(bool isShowDefaultToast);

	/**
	 * 12. 激活码接口 统一
	 *
	 * @param serverId 大陆不传,全球需要传
	 */
	void displayCDKeyDialog(FString serverId, UOneEngineSDKSubsystem::FOneGenericResultDelegate callback);

	typedef TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)> QueryUserActiveQualificationCallback;
	// 查询用户有没有激活资格
	void QueryUserActiveQualification(const FString& ServerId, QueryUserActiveQualificationCallback Callback);
	
	// 激活设备 


	/**
	 * 13. 查询是否需要输入激活码，游戏需要自定义UI 统一
	 */
	typedef  TFunction<void( bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> OnQueryActCodeLambda;
	void queryActCode(FString serverId, OnQueryActCodeLambda callback);
	void ActivateDevice(const FString& ServerId, OnQueryActCodeLambda callback);

	/**
	 * 14. 验证输入的激活码, 游戏需要自定义UI  统一
	 */
	void exchangeActCode(FString serverId, FString activeCode,OnGenericResult callback);

	/**
	 * 15. 兑换码 统一
	 */
	void redeemCouponCode(FString couponCode, FString roleId, FString serverId, FString roleLevel,
	                      FString vip, FString ext, OnGenericResult callback);


	/**
	 * 16. 角色列表
	 */
	typedef TFunction<void(bool bSuccess, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)> OnGetUserRoleInfoResult;
	void getUserRoleInfoList(int serverId, OnGetUserRoleInfoResult callback);


	/**
	 * 17. 绑定页面  PHONE 0, WMPASS 1, EMAIL 2, GOOGLE 3, FACEBOOK 4;
	 */
	void bindThirdAccount(int bindType, UOneEngineSDKSubsystem::FOneBindResultDelegate callback);

	/**
	 * 18. 扫码二维码接口
	 */
	void scanQRCode(UOneEngineSDKSubsystem::FOneGetQRCodeScanResultDelegate callback);


	/**
	 * 2. 获取防沉迷信息 preRoleLogin 大陆
	 */
	void fetchAntiAddictionInfo(UOneEngineSDKSubsystem::FOneFetchAntiAddictionInfoDelegate callback);

	FString getAntiAddictionInfo();


	/**
	 * 3. 开始监听防沉迷信息 afterRoleLogin 大陆
	 */
	void startAntiAddictionNotify(FString roleId, FString serverId);

	/**
	 * 4. 退出登录 大陆
	 */
	void stopAntiAddictionNotify();


	/**
	 * 7. 打开法律条款 统一
	 */
	void openComplianceOnWebView();

	/**
	 * 8. 获取权限列表 大陆
	 */
	TArray<FOnePermissionInfo> getPermissionList();


	/**
	 * 9. 请求权限 统一
	 */
	void requestPermission(int permission, FString tips,
	                       UOneEngineSDKSubsystem::FOneRequestPermissionResultDelegate onePermissionListener);

	/**
	 * 10. 检查是否有权限 大陆
	 */
	bool checkSelfPermission(int permission);

	/**
	 * 11. 关闭剪切板权限 大陆
	 */
	void closeClipboardPermission();

	//12. 打开设置页面

	void openApplicationSetting();

	// 打点接口

	/**
	 * 1. channelPlatform
	 */
	void getChannelPlatform(OnGetPlatformResult CallBack);

	/**
	 * 2. channelId
	 */
	int getChannelId();

	int getSubChannelId();

	FString getBIName();


	/**
	 * 3. mediaId
	 */
	FString getChannelMediaId();

	/**
	 * 4. platformOs
	 */
	int getPlatformOs();

	/**
	 * 5. deviceInfo (多个接口的合并)
	 */
	FString getDeviceInfo();

	/**
	 * 6. 获取用户位置信息, 全球是全的,大陆 地区默认为空
	 */
	typedef TFunction<void(const FOneUserLocationInfo& LocationInfo)> OnGetUserLocationLambda;
	
	void getUserLocationInfo(OnGetUserLocationLambda OnGetUserLocation);

	/**
	 * 7. 自定义打点
	 */
	void trackEvent(FString eventName, FString ext);

	/**
	 * 8. 广告打点
	 */
	void trackEventAD(FString eventName, FString ext);

	/**
	 * 9. 角色创建
	 */
	void trackEventRoleCreate(FString info, FString ip, FString port);

	/**
	 * 10. 角色登录
	 */
	void trackEventRoleLogin(FString info, FString ip, FString port);

	void trackEventRoleLoginError(FString info, FString ip, FString port, FString code, FString msg);

	/**
	 * 11. 角色升级
	 */
	void trackEventRoleUpdate(FString info);

	/**
	 * 12. 角色登出
	 */
	void trackEventRoleLogout(FString info);

	/**
	 * 13.资源版本核对  0 开始 1 成功 2 失败
	 */
	void trackGameResReqEvent(int stateIndex, FString url, FString errorCode, FString errorMsg);

	/**
	 * 14. 资源文件下载 0 开始 1 成功 2 失败
	 */
	void trackGameUpdateAssetEvent(int stateIndex, FString url, FString errorCode, FString errorMsg);

	/**
	 * 15. 资源解压缩 0 开始 1 成功 2 失败
	 */
	void trackGameResDecEvent(int stateIndex, FString errorMsg);

	/**
	 * 16. 获取服务器列表 0 开始 1 成功 2 失败
	 */
	void trackGameGetServerListEvent(int stateIndex, FString url, FString errorCode, FString errorMsg);

	/**
	 * 17. 进入游戏场景
	 */
	void trackEventEnterGameScene(FString scene, int period, FString ext);

	/**
	 * 18. 退出游戏场景
	 */
	void trackEventExitGameScene();

	/**
	 * 19. 添加设备信息
	 */
	void trackEventAddExtraDeviceInfo(FString ext);

	/**
	 * 1. 是否支持万能方法
	 */
	bool isSupportedCommonFunction(FString funcName);


	/**
	 * 2. 万能方法调用
	 */
	void callCommonFunction(FString funcName, FString params,
	                        UOneEngineSDKSubsystem::FOneCommonFunctionDelegate callback);

	

	/**
	 * 1. 打开聊天机器人 全球
	 */
	void openAIHelpChat(FString roleId, FString roleName, FString serverId);

	/**
	 * 2. 打开常见问题
	 */
	void openAIHelpFAQ(FString roleId, FString roleName, FString serverId);

	void openCustomService(FString roleId, FString roleName, FString serverId, bool showNavbar);


	// 审核
	bool examinStatus();

	/**
	 * 3. 翻译
	 */
	typedef TFunction<void(bool bSucceed, const FString& Result, const FString& ErrorMsg)> OnTranslateCallback;
	void translate(FString text, OnTranslateCallback callback);

	/**
	 * 4. 获取本地语言列表
	 */
	FString getLocalLanguages();

	/**
	 * 5. 设置语言
	 */
	void setLanguage(FString locale);

	/**
	 * 6. 多appId 设置,全球可以放多个config文件,通过该接口控制使用哪个配置id 的配置
	 */
	void setUpConfigAppId(FString appId);


	/**
	 * 1. 分享
	 */
	void share(EOneShareAppTarget AppTarget, const EOneShareType Type,
	           const FOneShareData& Data, UOneEngineSDKSubsystem::FOneGenericResultDelegate OnShareResult);

	void setReceiveMsgListener(UOneEngineSDKSubsystem::FOneNotificationDelegate listener);

	FString getPushStatus();

	/**
	 * 2. Push注册
	 * FString roleId,FString serverId  全球使用,大陆不用
	 */
	void registerPush(UOneEngineSDKSubsystem::FOneStartUpdatePushDataDelegate callback);

	/**
	 * 2.1 打开或者关闭push
	 */

	void setProviderPushState(bool enablePush, UOneEngineSDKSubsystem::FOneGenericResultDelegate callback);

	/**
	 * 3. 绑定用户
	 */
	void bindUserId(FString serverId, FString roleId, UOneEngineSDKSubsystem::FOneGenericResultDelegate callback);

	/**
	 * 4. 取消绑定用户
	 */
	void unbindUserId(UOneEngineSDKSubsystem::FOneGenericResultDelegate callback);


	/**
	 * 5. 获取push 细分的type 列表,可单独开关某细分类型的push
	 */
	void getPushTypeInfoList(UOneEngineSDKSubsystem::FOneGetPushTypeInfoListDelegate callback);

	/**
	 * 6. 可根据类型 批量打开或者关闭
	 */
	void updatePushTypeList(const TArray<FOnePushTypeInfo>& PushTypeList,
	                        UOneEngineSDKSubsystem::FOneGenericResultDelegate Callback);


	/**
	 * 7. 获取push免打扰信息
	 */
	void getPushNotDisturbInfo(UOneEngineSDKSubsystem::FOnePushNotDisturbInfoDelegate callBack);

	/**
	 * 8. 更新免打扰信息
	 */
	void updatePushNotDisturb(FOnePushNotDisturbInfo notDisturbInfo,
	                          UOneEngineSDKSubsystem::FOneGenericResultDelegate callBack);

	/**
	 * 杀进程
	 */
	void killProcess();

	void GetUserTokenList(OnGetDeviceTokenResult Callback);
    
	void TokenLogin(const FString& Token, const FString& Uid, EOneEngineThirdType ThirdType);
	
	void ThirdLogin(EOneEngineThirdType ThirdType, bool bForcedLogin);
	
	void GuestLogin();

	void GetIpInfo(const FString& Ip,UOneEngineSDKSubsystem::FOnGetIpInfoResultDelegate Delegate);

	/// 全球独有的 评价接口和 修改屏幕亮度接口
	float GetScreenBrightness();

	void SetScreenBrightness(float BrightnessValue);
	
	void RecoverScreenBrightness();
	///保持屏幕常亮
	void SwitchScreenPermanentBrightnessState(bool bIsTurnOn);
	

	///评价接口
	void InAppRequestStoreReview(UOneEngineSDKSubsystem::FOneGenericResultDelegate Delegate);

	/// AppLink Ios有这个参数
	void RequestStoreReview(FString AppLink);

	/// 获取渲染器配置文件地址
	FString GetRenderConfigFilePath();

	// 全球独有的打开Naver论坛
	void OpenCommunityByGame(EOneNaverGameType Type, const FString& Pid, bool Scheduled, UOneEngineSDKSubsystem::FOneLoadDelegate LoadCallback, UOneEngineSDKSubsystem::FOneUnloadDelegate UnloadCallback, UOneEngineSDKSubsystem::FOneInGameMenuDelegate InGameMenuCallback);

	void TerminateCommunity();

	void GetProviderPushState(UOneEngineSDKSubsystem::FOnGetPushStateDelegate Delegate);
	
	void SetAnalyticsCollectionEnabled(bool bEnable);

	bool IsInstalledApp(EOneShareAppTarget Target);
	
	FString GetGlobalOneAppId();
	

private:
	OneGlobalBridge();

	
	void initJNI();
	void registerJNI();

	
	JNIEnv* jniEnv;
	jclass jOneGlobalBridgeClass;
	jobject OneGlobalObj;
	jmethodID jInit;
	jmethodID jGetGlobalOneAppId;
	jmethodID jGetInitResult;
	jmethodID jSetDebug;
	jmethodID jIsDebug;
	jmethodID jLogin;
	jmethodID jLogout;
	jmethodID jIsHasLogin;
	jmethodID jGetUserInfo;
	jmethodID jOpenUserCenter;
	jmethodID jPay;
	jmethodID jGetProductList;
	jmethodID jGetPayChannelId;
	jmethodID jSetShowActivationResultToast;
	jmethodID jDisplayCDKeyDialog;
	jmethodID jQueryActCode;
	jmethodID jExchangeActCode;
	jmethodID jRedeemCouponCode;
	jmethodID jGetUserRoleInfoList;
	jmethodID jBindThirdAccount;
	jmethodID jScanQRCode;
	jmethodID jFetchAntiAddictionInfo;
	jmethodID jStartAntiAddictionNotify;
	jmethodID jStopAntiAddictionNotify;
	jmethodID jOpenComplianceOnWebView;
	jmethodID jGetPermissionList;
	jmethodID jRequestPermission;
	jmethodID jCheckSelfPermission;
	jmethodID jCloseClipboardPermission;
	jmethodID jOpenApplicationSetting;
	jmethodID jGetChannelPlatform;
	jmethodID jGetChannelId;
	jmethodID jGetChannelMediaId;
	jmethodID jGetPlatformOs;
	jmethodID jGetDeviceInfo;
	jmethodID jGetUserLocationInfo;
	jmethodID jTrackEvent;
	jmethodID jTrackEventAD;
	jmethodID jTrackEventRoleCreate;
	jmethodID jTrackEventRoleLogin;
	jmethodID jTrackEventRoleLoginError;
	jmethodID jTrackEventRoleUpdate;
	jmethodID jTrackEventRoleLogout;
	jmethodID jTrackGameResReqEvent;
	jmethodID jTrackGameUpdateAssetEvent;
	jmethodID jTrackGameResDecEvent;
	jmethodID jTrackGameGetServerListEvent;
	jmethodID jTrackEventEnterGameScene;
	jmethodID jTrackEventExitGameScene;
	jmethodID jTrackEventAddExtraDeviceInfo;

	jmethodID jIsSupportedCommonFunction;
	jmethodID jCallCommonFunction;
	jmethodID jVerifyPhone;
	jmethodID jOpenAIHelpChat;
	jmethodID jOpenAIHelpFAQ;
	jmethodID jOpenCustomService;
	jmethodID jTranslate;

	jmethodID jGetLocalLanguages;
	jmethodID jSetLanguage;
	jmethodID jSetUpConfigAppId;
	jmethodID jShare;
	jmethodID jSetReceiveMsgListener;
	jmethodID jGetPushStatus;
	jmethodID jRegisterPush;
	jmethodID jSetProviderPushState;
	jmethodID jBindUserId;
	jmethodID jUnbindUserId;
	jmethodID jGetPushTypeInfoList;
	jmethodID jUpdatePushTypeList;
	jmethodID jGetPushNotDisturbInfo;
	jmethodID jUpdatePushNotDisturb;
	jmethodID jKillProcess;

	jmethodID jGetSubChannelId;
	jmethodID jGetUserIP;
	jmethodID jGetBIName;
	jmethodID jGetBindPhone;
	jmethodID jShowWmPassport;
	jmethodID jOpenNaverCommunity;
	jmethodID jTerminalNaverCommunity;
	jmethodID jOpenUrlByGame;
	jmethodID jAnnouncementBeforeLogin;
	jmethodID jAnnouncementAfterLogin;
	jmethodID jExaminStatus;
	jmethodID jGetDFUniqueIDs;
	jmethodID jGlobalRequestPermissions;
	jmethodID jGetAntiAddictionInfo;
	jmethodID jIsMainland;

	jmethodID jGetDeviceToken;
	jmethodID jLoginByGame;
	jmethodID jThirdLoginByGame;
	jmethodID jGuestLoginByGame;
	
	jmethodID jQueryUserActiveQualification;
	jmethodID jActivateDevice;
	
	jmethodID jGetScreenBrightness;
	jmethodID jSetScreenBrightness;
	jmethodID jRecoverScreenBrightness;
	jmethodID jSwitchScreenPermanentBrightnessState;
	jmethodID jInAppRequestStoreReview;
	jmethodID jRequestStoreReview;
	jmethodID jGetRenderConfigFilePath;
	jmethodID jGetIpInfo;

	jmethodID jGetProviderPushState;
	jmethodID jSetAnalyticsCollectionEnabled;
	jmethodID jIsInstalledApp;
};
