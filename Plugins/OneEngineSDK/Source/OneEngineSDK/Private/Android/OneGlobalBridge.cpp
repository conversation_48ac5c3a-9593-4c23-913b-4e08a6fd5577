#include "OneGlobalBridge.h"

#include "ImageUtils.h"
#include "JsonObjectWrapper.h"
#include "Android/AndroidJavaEnv.h"
#include "Dom/JsonValue.h"
#include "Misc/FileHelper.h"
#include "Serialization/JsonReader.h"
#include "Serialization/JsonSerializer.h"
#include <jni.h>
#include "OneGlobalJNI.h"
#include "Engine/Texture2D.h"
#include "TextureResource.h"
#include "Runtime/Launch/Resources/Version.h"
OneGlobalBridge::OneGlobalBridge()
{
	registerJNI();
	initJNI();
}

void OneGlobalBridge::registerJNI()
{
	
	jclass jOneJNI = AndroidJavaEnv::FindJavaClass("com/wpsdk/one/global/bridge/OneGlobalJNI");
	 JNINativeMethod methods[] = {
            {"OnGeneralResult", "(ZILjava/lang/String;J)V", (void*)OneGlobalJNI_OnGeneralResult},
		    {"OnInitResult", "(ZLjava/lang/String;J)V", (void*)OneGlobalJNI_OnInitResult},
		    {"OnLoginSuccessResult", "(Ljava/lang/String;Ljava/lang/String;)V", (void*)OneGlobalJNI_OnLoginSuccessResult},
		    {"OnLoginFail", "(IILjava/lang/String;)V", (void*)OneGlobalJNI_OnLoginFail},
		    {"OnLogoutResult", "(ZLjava/lang/String;)V", (void*)OneGlobalJNI_OnLogoutResult},
		    {"OnPaySuccessResult", "(Ljava/lang/String;)V", (void*)OneGlobalJNI_OnPaySuccessResult},
		    {"OnPayFailResult", "(ILjava/lang/String;Ljava/lang/String;)V", (void*)OneGlobalJNI_OnPayFailResult},
		    {"OnGetProductListResult", "(ZLjava/lang/String;ILjava/lang/String;J)V", (void*)OneGlobalJNI_OnGetProductListResult},
		    {"OnQueryActCodeResult", "(ZZILjava/lang/String;J)V", (void*)OneGlobalJNI_OnQueryActCodeResult},
		    {"OnRedeemCouponCode", "(ZLjava/lang/String;J)V", (void*)OneGlobalJNI_OnRedeemCouponCode},
		    {"OnGetUserRoleListResult", "(ZILjava/lang/String;Ljava/lang/String;J)V", (void*)OneGlobalJNI_OnGetUserRoleListResult},
		    {"OnScanResult", "(Ljava/lang/String;Ljava/lang/String;J)V", (void*)OneGlobalJNI_OnScanResult},
		    {"OnGetLocationResult", "(Ljava/lang/String;J)V", (void*)OneGlobalJNI_OnGetLocationResult},
		    {"OnGetAntiAddictionInfoResult", "(Ljava/lang/String;J)V", (void*)OneGlobalJNI_OnGetAntiAddictionInfoResult},
		    {"OnForbidGameResult", "(ZLjava/lang/String;)V", (void*)OneGlobalJNI_OnForbidGameResult},
		    {"OnRequestPermissionResult", "(IZJ)V", (void*)OneGlobalJNI_OnRequestPermissionResult},
		    {"OnReceivePushMsg", "(Ljava/lang/String;J)V", (void*)OneGlobalJNI_OnReceivePushMsg},
		    {"OnGetPushTypeListResult", "(ZLjava/lang/String;ILjava/lang/String;J)V", (void*)OneGlobalJNI_OnGetPushTypeListResult},
		    {"OnGetPushNoDisturbInfoResult", "(ZLjava/lang/String;ILjava/lang/String;J)V", (void*)OneGlobalJNI_OnGetPushNoDisturbInfoResult},
		    {"OnTranslateResult", "(ZLjava/lang/String;Ljava/lang/String;J)V", (void*)OneGlobalJNI_OnTranslateResult},
		    {"OnCallFunctionResult", "(Ljava/lang/String;ILjava/lang/String;J)V", (void*)OneGlobalJNI_OnCallFunctionResult},
		    {"OnBindResult", "(ZIILjava/lang/String;J)V", (void*)OneGlobalJNI_OnBindResult},
		    {"OnExit", "()V", (void*)OneGlobalJNI_OnExit},
		    {"IsGameHasExitDialog", "()Z", (void*)OneGlobalJNI_IsGameHasExitDialog},
		    {"OnSdkDidLoaded", "(J)V", (void*)OneGlobalJNI_OnSdkDidLoaded},
		    {"OnSdkDidUnloaded", "(J)V", (void*)OneGlobalJNI_OnSdkDidUnloaded},
		    {"OnCallInGameMenuCode", "(Ljava/lang/String;J)V", (void*)OneGlobalJNI_OnCallInGameMenuCode},
		    {"OnRegisterPushResult", "(ZILjava/lang/String;J)V", (void*)OneGlobalJNI_OnRegisterPushResult},
		    {"OnGenericLambdaResult", "(ZILjava/lang/String;J)V", (void*)OneGlobalJNI_OnGenericLambdaResult},
		    {"OnGetChannelPlatformResult", "(ZLjava/lang/String;ILjava/lang/String;J)V", (void*)OneGlobalJNI_OnGetChannelPlatformResult},
		    {"OnGetDeviceTokensResult", "(ZLjava/lang/String;ILjava/lang/String;J)V", (void*)OneGlobalJNI_OnGetDeviceTokensResult},
		    {"OnQueryUserActiveQualificationResult", "(ZLjava/lang/String;ILjava/lang/String;J)V", (void*)OneGlobalJNI_OnQueryUserActiveQualificationResult},
		    {"OnDisplayCDKeyDialogResult", "(ZILjava/lang/String;J)V", (void*)OneGlobalJNI_OnDisplayCDKeyDialogResult},
		    {"OnGetIpLocationInfoResult", "(ZLjava/lang/String;ILjava/lang/String;J)V", (void*)OneGlobalJNI_OnGetIpLocationInfoResult},
		    {"OnGetPushNotificationResult", "(ZZILjava/lang/String;J)V", (void*)OneGlobalJNI_OnGetPushNotificationResult}
		
	 };

	if (AndroidJavaEnv::GetJavaEnv()->RegisterNatives(jOneJNI, methods, sizeof(methods) / sizeof(methods[0])) < 0) {
		UE_LOG(LogTemp,Warning,TEXT("Failed to register native methods "));
	}

}

void OneGlobalBridge::initJNI()
{
	UE_LOG(LogTemp,Warning,TEXT("初始化完成设置 initJNI ThreadId:%d"),FPlatformTLS::GetCurrentThreadId());

	jniEnv = AndroidJavaEnv::GetJavaEnv(true);
	jOneGlobalBridgeClass = AndroidJavaEnv::FindJavaClassGlobalRef("com/wpsdk/one/global/bridge/OneGlobalBridge");
	jmethodID jGetDefaultId = jniEnv->GetStaticMethodID(jOneGlobalBridgeClass, "getDefault",
	                                                    "()Lcom/wpsdk/one/global/bridge/OneGlobalBridge;");
	jobject Default = jniEnv->CallStaticObjectMethod(jOneGlobalBridgeClass, jGetDefaultId);
	OneGlobalObj = jniEnv->NewGlobalRef(Default);
	jInit = jniEnv->GetMethodID(jOneGlobalBridgeClass, "init", "(Landroid/app/Activity;ILjava/lang/String;J)V");
	jSetDebug = jniEnv->GetMethodID(jOneGlobalBridgeClass, "setDebug", "(Z)V");
	jIsDebug = jniEnv->GetMethodID(jOneGlobalBridgeClass, "isDebug", "()Z");
	jGetInitResult = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getInitResult", "()Z");
	jLogin = jniEnv->GetMethodID(jOneGlobalBridgeClass, "login", "()V");
	jLogout = jniEnv->GetMethodID(jOneGlobalBridgeClass, "logout", "()V");
	jIsHasLogin = jniEnv->GetMethodID(jOneGlobalBridgeClass, "isHasLogin", "()Z");
	jGetUserInfo = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getUserInfo", "()Ljava/lang/String;");
	jOpenUserCenter = jniEnv->GetMethodID(jOneGlobalBridgeClass, "openUserCenter", "()V");
	jPay = jniEnv->GetMethodID(jOneGlobalBridgeClass, "pay", "(Ljava/lang/String;)V");
	jGetProductList = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getProductList",
	                                      "(Landroid/content/Context;Ljava/lang/String;J)V");
	jGetPayChannelId = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getPayChannelId", "()I");
	jSetShowActivationResultToast = jniEnv->GetMethodID(jOneGlobalBridgeClass, "setShowActivationResultToast", "(Z)V");
	jDisplayCDKeyDialog = jniEnv->GetMethodID(jOneGlobalBridgeClass, "displayCDKeyDialog",
	                                          "(Landroid/app/Activity;Ljava/lang/String;J)V");
	jQueryActCode = jniEnv->GetMethodID(jOneGlobalBridgeClass, "queryActCode",
	                                    "(Landroid/app/Activity;Ljava/lang/String;J)V");
	jExchangeActCode = jniEnv->GetMethodID(jOneGlobalBridgeClass, "exchangeActCode",
	                                       "(Landroid/app/Activity;Ljava/lang/String;Ljava/lang/String;J)V");
	jRedeemCouponCode = jniEnv->GetMethodID(jOneGlobalBridgeClass, "redeemCouponCode",
	                                        "(Landroid/app/Activity;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V");
	jGetUserRoleInfoList = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getUserRoleInfoList",
	                                           "(Landroid/app/Activity;IJ)V");
	jBindThirdAccount = jniEnv->GetMethodID(jOneGlobalBridgeClass, "bindThirdAccount", "(IJ)V");
	jScanQRCode = jniEnv->GetMethodID(jOneGlobalBridgeClass, "scanQRCode", "(Landroid/app/Activity;J)V");
	//合规接口
	jFetchAntiAddictionInfo = jniEnv->GetMethodID(jOneGlobalBridgeClass, "fetchAntiAddictionInfo",
	                                              "(Landroid/content/Context;J)V");
	jStartAntiAddictionNotify = jniEnv->GetMethodID(jOneGlobalBridgeClass, "startAntiAddictionNotify",
	                                                "(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)V");
	jStopAntiAddictionNotify = jniEnv->GetMethodID(jOneGlobalBridgeClass, "stopAntiAddictionNotify", "()V");
	jOpenComplianceOnWebView = jniEnv->GetMethodID(jOneGlobalBridgeClass, "openComplianceOnWebView",
	                                               "(Landroid/app/Activity;)V");
	jGetPermissionList = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getPermissionList",
	                                         "(Landroid/content/Context;)Ljava/lang/String;");
	jRequestPermission = jniEnv->GetMethodID(jOneGlobalBridgeClass, "requestPermission",
	                                         "(Landroid/app/Activity;ILjava/lang/String;J)V");
	jCheckSelfPermission = jniEnv->GetMethodID(jOneGlobalBridgeClass, "checkSelfPermission",
	                                           "(Landroid/content/Context;I)Z");
	jCloseClipboardPermission = jniEnv->GetMethodID(jOneGlobalBridgeClass, "closeClipboardPermission",
	                                                "(Landroid/content/Context;)V");
	jOpenApplicationSetting = jniEnv->GetMethodID(jOneGlobalBridgeClass, "openApplicationSettings",
	                                              "(Landroid/app/Activity;)V");

	jGetChannelPlatform = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getChannelPlatform", "(J)Ljava/lang/String;");
	jGetChannelMediaId = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getChannelMediaId", "()Ljava/lang/String;");
	jGetChannelId = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getChannelId", "()I");
	jGetPlatformOs = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getPlatformOs", "()I");
	jGetDeviceInfo = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getDeviceInfo",
	                                     "(Landroid/content/Context;)Ljava/lang/String;");
	jGetUserLocationInfo = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getUserLocationInfo",
	                                           "(Landroid/app/Activity;J)V");
	jTrackEvent = jniEnv->GetMethodID(jOneGlobalBridgeClass, "trackEvent", "(Ljava/lang/String;Ljava/lang/String;)V");
	jTrackEventAD = jniEnv->GetMethodID(jOneGlobalBridgeClass, "trackEventAD",
	                                    "(Ljava/lang/String;Ljava/lang/String;)V");
	jTrackEventRoleCreate = jniEnv->GetMethodID(jOneGlobalBridgeClass, "trackEventRoleCreate",
	                                            "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");
	jTrackEventRoleLogin = jniEnv->GetMethodID(jOneGlobalBridgeClass, "trackEventRoleLogin",
	                                           "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");
	jTrackEventRoleLoginError = jniEnv->GetMethodID(jOneGlobalBridgeClass, "trackEventRoleLoginError",
	                                                "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");
	jTrackEventRoleUpdate = jniEnv->GetMethodID(jOneGlobalBridgeClass, "trackEventRoleUpdate", "(Ljava/lang/String;)V");
	jTrackEventRoleLogout = jniEnv->GetMethodID(jOneGlobalBridgeClass, "trackEventRoleLogout", "(Ljava/lang/String;)V");
	jTrackGameResReqEvent = jniEnv->GetMethodID(jOneGlobalBridgeClass, "trackGameResReqEvent",
	                                            "(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");
	jTrackGameUpdateAssetEvent = jniEnv->GetMethodID(jOneGlobalBridgeClass, "trackGameUpdateAssetEvent",
	                                                 "(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");
	jTrackGameResDecEvent = jniEnv->
		GetMethodID(jOneGlobalBridgeClass, "trackGameResDecEvent", "(ILjava/lang/String;)V");
	jTrackGameGetServerListEvent = jniEnv->GetMethodID(jOneGlobalBridgeClass, "trackGameGetServerListEvent",
	                                                   "(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");
	jTrackEventEnterGameScene = jniEnv->GetMethodID(jOneGlobalBridgeClass, "trackEventEnterGameScene",
	                                                "(Landroid/content/Context;Ljava/lang/String;ILjava/lang/String;)V");
	jTrackEventExitGameScene = jniEnv->GetMethodID(jOneGlobalBridgeClass, "trackEventExitGameScene", "()V");
	jTrackEventAddExtraDeviceInfo = jniEnv->GetMethodID(jOneGlobalBridgeClass, "trackEventAddExtraDeviceInfo",
	                                                    "(Ljava/lang/String;)V");


	jIsSupportedCommonFunction = jniEnv->GetMethodID(jOneGlobalBridgeClass, "isSupportedCommonFunction",
	                                                 "(Ljava/lang/String;)Z");
	jCallCommonFunction = jniEnv->GetMethodID(jOneGlobalBridgeClass, "callCommonFunction",
	                                          "(Ljava/lang/String;Ljava/util/HashMap;J)V");
	jVerifyPhone = jniEnv->GetMethodID(jOneGlobalBridgeClass, "verifyPhone", "(J)V");
	jOpenAIHelpChat = jniEnv->GetMethodID(jOneGlobalBridgeClass, "openAIHelpChat",
	                                      "(Landroid/app/Activity;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");
	jOpenAIHelpFAQ = jniEnv->GetMethodID(jOneGlobalBridgeClass, "openAIHelpFAQ",
	                                     "(Landroid/app/Activity;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");
	jTranslate = jniEnv->GetMethodID(jOneGlobalBridgeClass, "translate",
	                                 "(Landroid/app/Activity;Ljava/lang/String;J)V");

	jGetLocalLanguages = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getLocalLanguages",
	                                         "(Landroid/content/Context;)Ljava/lang/String;");
	jSetLanguage = jniEnv->GetMethodID(jOneGlobalBridgeClass, "setLanguage",
	                                   "(Landroid/app/Activity;Ljava/lang/String;)V");
	jSetUpConfigAppId = jniEnv->GetMethodID(jOneGlobalBridgeClass, "setUpConfigAppId",
	                                        "(Landroid/app/Activity;Ljava/lang/String;)V");
	jShare = jniEnv->GetMethodID(jOneGlobalBridgeClass, "share", "(Landroid/content/Context;IILjava/lang/String;J)V");
	jSetReceiveMsgListener = jniEnv->GetMethodID(jOneGlobalBridgeClass, "setReceiveMsgListener", "(J)V");
	jGetPushStatus = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getPushStatus", "()Ljava/lang/String;");
	jRegisterPush = jniEnv->GetMethodID(jOneGlobalBridgeClass, "registerPush", "(J)V");
	jSetProviderPushState = jniEnv->GetMethodID(jOneGlobalBridgeClass, "setProviderPushState", "(ZJ)V");
	jBindUserId = jniEnv->GetMethodID(jOneGlobalBridgeClass, "bindUserId", "(Ljava/lang/String;Ljava/lang/String;J)V");
	jUnbindUserId = jniEnv->GetMethodID(jOneGlobalBridgeClass, "unbindUserId", "(J)V");
	jGetPushTypeInfoList = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getPushTypeInfoList", "(J)V");
	jUpdatePushTypeList = jniEnv->GetMethodID(jOneGlobalBridgeClass, "updatePushTypeList", "(Ljava/lang/String;J)V");
	jGetPushNotDisturbInfo = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getPushNotDisturbInfo", "(J)V");
	jUpdatePushNotDisturb = jniEnv->
		GetMethodID(jOneGlobalBridgeClass, "updatePushNotDisturb", "(Ljava/lang/String;J)V");
	jOpenCustomService = jniEnv->GetMethodID(jOneGlobalBridgeClass, "openCustomService",
	                                         "(Landroid/app/Activity;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V");
	jKillProcess = jniEnv->GetMethodID(jOneGlobalBridgeClass, "killProcess", "()V");

	jGetSubChannelId = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getSubChannelId", "()I");
	jGetUserIP = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getUserIP", "()Ljava/lang/String;");
	jGetBIName = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getBIName", "()Ljava/lang/String;");
	jGetBindPhone = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getBindPhone", "(J)V");
	jShowWmPassport = jniEnv->GetMethodID(jOneGlobalBridgeClass, "showWmPassport", "(J)V");
	jOpenNaverCommunity = jniEnv->GetMethodID(jOneGlobalBridgeClass, "openCommunityByGameWithType",
	                                          "(Landroid/app/Activity;IJZJJJ)V");
	jTerminalNaverCommunity = jniEnv->GetMethodID(jOneGlobalBridgeClass, "terminateCommunity",
	                                              "(Landroid/app/Activity;)V");
	jOpenUrlByGame = jniEnv->GetMethodID(jOneGlobalBridgeClass, "openUrlByGame",
	                                     "(Landroid/app/Activity;Ljava/lang/String;Z)V");
	jAnnouncementBeforeLogin = jniEnv->GetMethodID(jOneGlobalBridgeClass, "announcementBeforeLogin",
	                                               "(Landroid/app/Activity;IJ)V");
	jAnnouncementAfterLogin = jniEnv->GetMethodID(jOneGlobalBridgeClass, "announcementAfterLogin",
	                                              "(Landroid/app/Activity;ILjava/lang/String;Ljava/lang/String;J)V");

	jExaminStatus = jniEnv->GetMethodID(jOneGlobalBridgeClass, "examinStatus", "(Landroid/content/Context;)Z");
	jGetDFUniqueIDs = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getDFUniqueIDs",
	                                      "(Landroid/content/Context;)Ljava/lang/String;");
	jGlobalRequestPermissions = jniEnv->GetMethodID(jOneGlobalBridgeClass, "globalRequestPermission",
	                                                "(Landroid/app/Activity;ZLjava/lang/String;Ljava/lang/String;J)V");
	jGetAntiAddictionInfo = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getAntiAddictionInfo", "()Ljava/lang/String;");
	jIsMainland = jniEnv->GetMethodID(jOneGlobalBridgeClass, "isMainlandAPI", "()Z");

	jGetDeviceToken=jniEnv->GetMethodID(jOneGlobalBridgeClass, "getDeviceTokens", "(Landroid/content/Context;J)V");
	jLoginByGame = jniEnv->GetMethodID(jOneGlobalBridgeClass,"loginByGame", "(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;I)V");
	jThirdLoginByGame = jniEnv->GetMethodID(jOneGlobalBridgeClass,"thirdLoginByGame", "(Landroid/content/Context;IZ)V");
	jGuestLoginByGame = jniEnv->GetMethodID(jOneGlobalBridgeClass,"guestLoginByGame", "(Landroid/content/Context;)V");

	jQueryUserActiveQualification =  jniEnv->GetMethodID(jOneGlobalBridgeClass,"queryUserActiveQualification", "(Landroid/app/Activity;Ljava/lang/String;J)V");
	jActivateDevice = jniEnv->GetMethodID(jOneGlobalBridgeClass,"activateDevice", "(Landroid/app/Activity;Ljava/lang/String;J)V");

	jGetScreenBrightness = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getScreenBrightness", "(Landroid/app/Activity;)F");
	jSetScreenBrightness = jniEnv->GetMethodID(jOneGlobalBridgeClass, "setScreenBrightness", "(Landroid/app/Activity;F)V");
	jRecoverScreenBrightness = jniEnv->GetMethodID(jOneGlobalBridgeClass, "recoverScreenBrightness", "(Landroid/app/Activity;)V");
	jSwitchScreenPermanentBrightnessState = jniEnv->GetMethodID(jOneGlobalBridgeClass, "switchScreenPermanentBrightnessState", "(Landroid/app/Activity;Z)V");
	jInAppRequestStoreReview = jniEnv->GetMethodID(jOneGlobalBridgeClass, "inAppRequestStoreReview", "(Landroid/app/Activity;J)V");
	jRequestStoreReview = jniEnv->GetMethodID(jOneGlobalBridgeClass, "requestStoreReview", "(Landroid/app/Activity;)V");
	jGetRenderConfigFilePath = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getRenderConfigFilePath", "(Landroid/app/Activity;)Ljava/lang/String;");
	jGetIpInfo = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getIpInfo", "(Landroid/content/Context;Ljava/lang/String;J)V");

	jGetProviderPushState  = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getPushNotificationEnabled", "(Landroid/app/Activity;J)V");
	jSetAnalyticsCollectionEnabled  = jniEnv->GetMethodID(jOneGlobalBridgeClass, "setAnalyticsCollectionEnabled", "(Landroid/content/Context;Z)V");
	jIsInstalledApp  = jniEnv->GetMethodID(jOneGlobalBridgeClass, "isInstalledApp", "(I)Z");
	jGetGlobalOneAppId  = jniEnv->GetMethodID(jOneGlobalBridgeClass, "getGlobalOneAppId", "(Landroid/content/Context;)Ljava/lang/String;");
}

void OneGlobalBridge::init(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate)
{
	bool bIsInitSuccess= jniEnv->CallBooleanMethod(OneGlobalObj, jGetInitResult);
	if (bIsInitSuccess)
	{
		InitDelegate.ExecuteIfBound(true, 0, "");
	}
	else
	{
		UOneEngineSDKSubsystem::FOneInitDelegate* onGeneralCallback = new UOneEngineSDKSubsystem::FOneInitDelegate(InitDelegate);
		jniEnv->CallVoidMethod(OneGlobalObj, jInit, AndroidJavaEnv::GetGameActivityThis(), 0,NULL, reinterpret_cast<int64>(onGeneralCallback));
	}
}

void OneGlobalBridge::setDebug(bool isDebug)
{
	jniEnv->CallVoidMethod(OneGlobalObj, jSetDebug, isDebug);
}

bool OneGlobalBridge::isDebug()
{
	return jniEnv->CallBooleanMethod(OneGlobalObj, jIsDebug);
}

void OneGlobalBridge::login()
{
	jniEnv->CallVoidMethod(OneGlobalObj, jLogin);
}

void OneGlobalBridge::logout()
{
	jniEnv->CallVoidMethod(OneGlobalObj, jLogout);
}

bool OneGlobalBridge::isHasLogin()
{
	return jniEnv->CallBooleanMethod(OneGlobalObj, jIsHasLogin);
}

FOneUserInfo OneGlobalBridge::getUserInfo()
{
	jstring RetVal = static_cast<jstring>(jniEnv->CallObjectMethod(OneGlobalObj, jGetUserInfo));
	FString Result= FJavaHelper::FStringFromParam(jniEnv, RetVal);
	FOneUserInfo UserInfo;
	FJsonObjectWrapper Wrapper;
	Wrapper.JsonObjectFromString(Result);
	if (Wrapper.JsonObject.IsValid())
	{
		Wrapper.JsonObject->TryGetStringField(TEXT("uid"),UserInfo.UserId);
		Wrapper.JsonObject->TryGetStringField(TEXT("token"),UserInfo.Token);
		Wrapper.JsonObject->TryGetStringField(TEXT("phoneNumber"),UserInfo.Phone);
		Wrapper.JsonObject->TryGetStringField(TEXT("avatar"),UserInfo.Avatar);
		Wrapper.JsonObject->TryGetStringField(TEXT("username"),UserInfo.UserName);
		Wrapper.JsonObject->TryGetStringField(TEXT("inheritCode"),UserInfo.InheritCode);
		Wrapper.JsonObject->TryGetStringField(TEXT("countryCode"),UserInfo.CountryCode);
		Wrapper.JsonObject->TryGetNumberField(TEXT("age"),UserInfo.Age);
		Wrapper.JsonObject->TryGetBoolField(TEXT("isNewCreated"),UserInfo.bIsNewCreate);
		Wrapper.JsonObject->TryGetBoolField(TEXT("isAdult"),UserInfo.bIsAdult);
		Wrapper.JsonObject->TryGetStringField(TEXT("ageCountryCode"),UserInfo.AgeCountryCode);
		int PasswordExist = 0;
		Wrapper.JsonObject->TryGetNumberField(TEXT("passwordExist"),PasswordExist);
		UserInfo.bPasswordExist = PasswordExist > 0;
		int Type = 2;
		Wrapper.JsonObject->TryGetNumberField(TEXT("thirdType"),Type);
		UserInfo.Type = static_cast<EOneEngineThirdType>(Type);
		const TArray< TSharedPtr<FJsonValue> > *thirdUsers;
		if (Wrapper.JsonObject->TryGetArrayField(TEXT("thirdUsers"), thirdUsers))
		{
			if (thirdUsers->Num() > 0)
			{
				TArray<FOneUserThirdInfo> ThirdPartyUserInfoList;
				for (auto value : *thirdUsers)
				{
					FOneUserThirdInfo thirdUserInfo;
					TSharedPtr<FJsonObject> object = value->AsObject();
					if (object)
					{
						object->TryGetStringField(TEXT("uid"), thirdUserInfo.UserId);
						object->TryGetStringField(TEXT("thirdId"), thirdUserInfo.ThirdId);
						object->TryGetStringField(TEXT("username"), thirdUserInfo.UserName);
						object->TryGetStringField(TEXT("thirdEmail"), thirdUserInfo.Email);
						object->TryGetStringField(TEXT("avatar"), thirdUserInfo.Avatar);
						int loginType = 0;
						object->TryGetNumberField(TEXT("thirdType"), loginType);
						thirdUserInfo.Type = static_cast<EOneEngineThirdType>(loginType);
						ThirdPartyUserInfoList.Add(thirdUserInfo);
					}

				}
				UserInfo.Thirds = ThirdPartyUserInfoList;
			}
		}
	}
	// private String uid;
	// private String token;
	// private String phoneNumber;
	// private String avatar;
	// private String username;
	// private String type;
	//
	// private String thirdType;
	// private String inheritCode;
	// private int passwordExist;
	// private String countryCode;
	// private List<ThirdUserInfo> thirdUsers;
	//
	// public static class ThirdUserInfo {
	// 	private String avatar;
	// 	private String thirdId;
	// 	private String thirdType;
	// 	private String uid;
	// 	private String username;
	// 	private String thirdEmail;

	return UserInfo;
	
}

void OneGlobalBridge::openUserCenter()
{
	jniEnv->CallVoidMethod(OneGlobalObj, jOpenUserCenter);
}

void OneGlobalBridge::pay(FString info)
{
	jniEnv->CallVoidMethod(OneGlobalObj, jPay, *FJavaHelper::ToJavaString(jniEnv, info));
}

void OneGlobalBridge::getProductList(TArray<FString>  productIds,
                                     UOneEngineSDKSubsystem::FOneProductInfoDelegate callback)
{
	FString JsonString;
	TSharedRef<TJsonWriter<TCHAR>> JsonWriter = TJsonWriterFactory<>::Create(&JsonString);
	JsonWriter->WriteArrayStart();
	for (const FString& Value : productIds)
	{
		JsonWriter->WriteValue(Value);
	}
	JsonWriter->WriteArrayEnd();
	JsonWriter->Close();
	auto jInfo = FJavaHelper::ToJavaString(jniEnv, JsonString);
	auto* onGeneralCallback = new UOneEngineSDKSubsystem::FOneProductInfoDelegate(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jGetProductList, AndroidJavaEnv::GetGameActivityThis(),
	                                              *jInfo,reinterpret_cast<int64>(onGeneralCallback));
}

int OneGlobalBridge::getPayChannelId()
{
	return jniEnv->CallIntMethod(OneGlobalObj, jGetPayChannelId);
}

void OneGlobalBridge::setShowActivationResultToast(bool isShowDefaultToast)
{
	jniEnv->CallVoidMethod(OneGlobalObj, jSetShowActivationResultToast, isShowDefaultToast);
}

void OneGlobalBridge::displayCDKeyDialog(FString serverId, UOneEngineSDKSubsystem::FOneGenericResultDelegate callback)
{
	auto jInfo = FJavaHelper::ToJavaString(jniEnv, serverId);
	UOneEngineSDKSubsystem::FOneGenericResultDelegate* onGeneralCallback = new
		UOneEngineSDKSubsystem::FOneGenericResultDelegate(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jDisplayCDKeyDialog,
	                                              AndroidJavaEnv::GetGameActivityThis(),
	                                              *jInfo, reinterpret_cast<int64>(onGeneralCallback));
}

// 查询用户有没有激活资格
void OneGlobalBridge::QueryUserActiveQualification(const FString& ServerId, QueryUserActiveQualificationCallback Callback)
{
	auto jInfo = FJavaHelper::ToJavaString(jniEnv, ServerId);
	QueryUserActiveQualificationCallback* onGeneralCallback = new QueryUserActiveQualificationCallback(Callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jQueryUserActiveQualification,
												  AndroidJavaEnv::GetGameActivityThis(),
												  *jInfo, reinterpret_cast<int64>(onGeneralCallback));
}
// 激活设备 
void OneGlobalBridge::ActivateDevice(const FString& ServerId, OnQueryActCodeLambda Callback)
{
	auto jInfo = FJavaHelper::ToJavaString(jniEnv, ServerId);
	OnQueryActCodeLambda* onGeneralCallback = new OnQueryActCodeLambda(Callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jActivateDevice, AndroidJavaEnv::GetGameActivityThis(),
												  *jInfo,
												  reinterpret_cast<int64>(onGeneralCallback));
}

void OneGlobalBridge::queryActCode(FString serverId, OnQueryActCodeLambda callback)
{
	auto jInfo = FJavaHelper::ToJavaString(jniEnv, serverId);
	OnQueryActCodeLambda* onGeneralCallback = new OnQueryActCodeLambda(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jQueryActCode, AndroidJavaEnv::GetGameActivityThis(),
	                                              *jInfo,
	                                              reinterpret_cast<int64>(onGeneralCallback));
}

void OneGlobalBridge::exchangeActCode(FString serverId, FString activeCode,OnGenericResult callback)
{
	OnGenericResult* onGeneralCallback = new OnGenericResult(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jExchangeActCode, AndroidJavaEnv::GetGameActivityThis(),
	                                              *FJavaHelper::ToJavaString(jniEnv, serverId),
	                                              *FJavaHelper::ToJavaString(jniEnv, activeCode),
	                                              reinterpret_cast<int64>(onGeneralCallback));
}

void OneGlobalBridge::redeemCouponCode(FString couponCode, FString roleId, FString serverId,
                                       FString roleLevel, FString vip, FString ext,
                                       OnGenericResult callback)
{
	auto jCouponCode = FJavaHelper::ToJavaString(jniEnv, couponCode);
	auto jRoleId = FJavaHelper::ToJavaString(jniEnv, roleId);
	auto jServerId = FJavaHelper::ToJavaString(jniEnv, serverId);
	auto jRoleLelvel = FJavaHelper::ToJavaString(jniEnv, roleLevel);
	auto jVip = FJavaHelper::ToJavaString(jniEnv, vip);
	auto jExt = FJavaHelper::ToJavaString(jniEnv, ext);
	OnGenericResult* onGeneralCallback = new OnGenericResult(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jRedeemCouponCode, AndroidJavaEnv::GetGameActivityThis(),
	                                              *jCouponCode, *jRoleId, *jServerId, *jRoleLelvel, *jVip,
	                                              *jExt, reinterpret_cast<int64>(onGeneralCallback)
	);
}

void OneGlobalBridge::getUserRoleInfoList(int serverId,
                                          OnGetUserRoleInfoResult callback)
{
	OnGetUserRoleInfoResult* onGeneralCallback = new OnGetUserRoleInfoResult(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jGetUserRoleInfoList,
	                                              AndroidJavaEnv::GetGameActivityThis(), serverId,
	                                              reinterpret_cast<int64>(onGeneralCallback));
}

void OneGlobalBridge::bindThirdAccount(int bindType, UOneEngineSDKSubsystem::FOneBindResultDelegate callback)
{
	UOneEngineSDKSubsystem::FOneBindResultDelegate* onGeneralCallback = new
		UOneEngineSDKSubsystem::FOneBindResultDelegate(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jBindThirdAccount, bindType,
	                                              reinterpret_cast<int64>(onGeneralCallback));
}

void OneGlobalBridge::scanQRCode(UOneEngineSDKSubsystem::FOneGetQRCodeScanResultDelegate callback)
{
	UOneEngineSDKSubsystem::FOneGetQRCodeScanResultDelegate* onGeneralCallback = new
		UOneEngineSDKSubsystem::FOneGetQRCodeScanResultDelegate(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jScanQRCode, AndroidJavaEnv::GetGameActivityThis(),
	                                              reinterpret_cast<int64>(onGeneralCallback));
}

void OneGlobalBridge::fetchAntiAddictionInfo(UOneEngineSDKSubsystem::FOneFetchAntiAddictionInfoDelegate callback)
{
	UOneEngineSDKSubsystem::FOneFetchAntiAddictionInfoDelegate* onGeneralCallback = new
		UOneEngineSDKSubsystem::FOneFetchAntiAddictionInfoDelegate(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jFetchAntiAddictionInfo,
	                                              AndroidJavaEnv::GetGameActivityThis(),
	                                              reinterpret_cast<int64>(onGeneralCallback));
}

void OneGlobalBridge::startAntiAddictionNotify(FString roleId, FString serverId)
{
	auto jRoleId = FJavaHelper::ToJavaString(jniEnv, roleId);
	auto jServerId = FJavaHelper::ToJavaString(jniEnv, serverId);
	jniEnv->CallVoidMethod(OneGlobalObj, jStartAntiAddictionNotify,
	                                              AndroidJavaEnv::GetGameActivityThis(),
	                                              *jRoleId, *jServerId);
}

void OneGlobalBridge::stopAntiAddictionNotify()
{
	jniEnv->CallVoidMethod(OneGlobalObj, jStopAntiAddictionNotify);
}

void OneGlobalBridge::openComplianceOnWebView()
{
	jniEnv->CallVoidMethod(OneGlobalObj, jOpenComplianceOnWebView,
	                                              AndroidJavaEnv::GetGameActivityThis());
}

TArray<FOnePermissionInfo> OneGlobalBridge::getPermissionList()
{
	jstring jResult = static_cast<jstring>(jniEnv->CallObjectMethod(OneGlobalObj, jGetPermissionList,
	                                                                 AndroidJavaEnv::GetGameActivityThis()));
	FString Result = FJavaHelper::FStringFromParam(jniEnv, jResult);
	TArray<FOnePermissionInfo> PermissionInfos;
	if (!Result.IsEmpty())
	{
		TArray<TSharedPtr<FJsonValue>> JsonArray;
		TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(Result);
		if (!FJsonSerializer::Deserialize(JsonReader, JsonArray))
		{
			UE_LOG(LogJson, Warning, TEXT("JsonArrayStringToUStruct - Unable to parse. json=[%s]"), *Result);
		}
		else
		{
			if (JsonArray.Num() > 0)
			{
				for (int32 i = 0; i < JsonArray.Num(); i++)
				{
					FOnePermissionInfo PermissionInfo;
					const TSharedPtr<FJsonObject>& Obj = JsonArray[i]->AsObject();
					if (Obj.IsValid())
					{
						PermissionInfo.Title = Obj->GetStringField(TEXT("title"));
						PermissionInfo.Desc = Obj->GetStringField(TEXT("desc"));
						PermissionInfo.PermissionType = static_cast<EOnePermissionType>(Obj->GetIntegerField(TEXT("permissionType")));
						PermissionInfos.Add(PermissionInfo);
					}
				}
			}
		}
	}
	return PermissionInfos;
}

void OneGlobalBridge::requestPermission(int permission, FString tips,
                                        UOneEngineSDKSubsystem::FOneRequestPermissionResultDelegate callback)
{
	auto jTips = FJavaHelper::ToJavaString(jniEnv, tips);
	UOneEngineSDKSubsystem::FOneRequestPermissionResultDelegate* onGeneralCallback = new
		UOneEngineSDKSubsystem::FOneRequestPermissionResultDelegate(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jRequestPermission,
	                                              AndroidJavaEnv::GetGameActivityThis(),
	                                              permission, *jTips,
	                                              reinterpret_cast<int64>(onGeneralCallback));
}

void OneGlobalBridge::closeClipboardPermission()
{
	jniEnv->CallVoidMethod(OneGlobalObj, jCloseClipboardPermission,
	                                              AndroidJavaEnv::GetGameActivityThis());
}

bool OneGlobalBridge::checkSelfPermission(int permission)
{
	return jniEnv->CallBooleanMethod(OneGlobalObj, jCheckSelfPermission,
	                                                        AndroidJavaEnv::GetGameActivityThis(), permission);
}

void OneGlobalBridge::getChannelPlatform(OnGetPlatformResult CallBack)
{
	OnGetPlatformResult *CallBackCopy = new OnGetPlatformResult(CallBack);
	jniEnv->CallObjectMethod(OneGlobalObj, jGetChannelPlatform,reinterpret_cast<int64>(CallBackCopy));
}

int OneGlobalBridge::getChannelId()
{
	return jniEnv->CallIntMethod(OneGlobalObj, jGetChannelId);
}

FString OneGlobalBridge::getChannelMediaId()
{
	jstring jResult = static_cast<jstring>(jniEnv->CallObjectMethod(OneGlobalObj, jGetChannelMediaId));
	FString result = FJavaHelper::FStringFromParam(jniEnv, jResult);
	return result;
}

int OneGlobalBridge::getPlatformOs()
{
	return jniEnv->CallIntMethod(OneGlobalObj, jGetPlatformOs);
}

FString OneGlobalBridge::getDeviceInfo()
{
	jstring jResult = static_cast<jstring>(jniEnv->CallObjectMethod(OneGlobalObj, jGetDeviceInfo,
	                                                                 AndroidJavaEnv::GetGameActivityThis()));
	FString result = FJavaHelper::FStringFromParam(jniEnv, jResult);
	return result;
}


void OneGlobalBridge::getUserLocationInfo(OnGetUserLocationLambda OnGetUserLocation)
{
	OnGetUserLocationLambda* onGeneralCallback = new OnGetUserLocationLambda(OnGetUserLocation);
	jniEnv->CallVoidMethod(OneGlobalObj, jGetUserLocationInfo,
												  AndroidJavaEnv::GetGameActivityThis(),
												  reinterpret_cast<int64>(onGeneralCallback));
}

void OneGlobalBridge::trackEvent(FString eventName, FString ext)
{
	jniEnv->CallVoidMethod(OneGlobalObj, jTrackEvent,
	                                              *FJavaHelper::ToJavaString(jniEnv, eventName),
	                                              *FJavaHelper::ToJavaString(jniEnv, ext));
}

void OneGlobalBridge::trackEventAD(FString eventName, FString ext)
{
	jniEnv->CallVoidMethod(OneGlobalObj, jTrackEventAD,
	                                              *FJavaHelper::ToJavaString(jniEnv, eventName),
	                                              *FJavaHelper::ToJavaString(jniEnv, ext));
}

void OneGlobalBridge::trackEventRoleCreate(FString info, FString ip, FString port)
{
	auto jInfo = FJavaHelper::ToJavaString(jniEnv, info);
	auto jIP = FJavaHelper::ToJavaString(jniEnv, ip);
	auto jPort = FJavaHelper::ToJavaString(jniEnv, port);
	jniEnv->CallVoidMethod(OneGlobalObj, jTrackEventRoleCreate, *jInfo, *jIP, *jPort);
}

void OneGlobalBridge::trackEventRoleLogin(FString info, FString ip, FString port)
{
	auto jInfo = FJavaHelper::ToJavaString(jniEnv, info);
	auto jIP = FJavaHelper::ToJavaString(jniEnv, ip);
	auto jPort = FJavaHelper::ToJavaString(jniEnv, port);
	jniEnv->CallVoidMethod(OneGlobalObj, jTrackEventRoleLogin, *jInfo, *jIP, *jPort);
}

void OneGlobalBridge::trackEventRoleLoginError(FString info, FString ip, FString port, FString code, FString msg)
{
	auto jInfo = FJavaHelper::ToJavaString(jniEnv, info);
	auto jIP = FJavaHelper::ToJavaString(jniEnv, ip);
	auto jPort = FJavaHelper::ToJavaString(jniEnv, port);
	auto jCode = FJavaHelper::ToJavaString(jniEnv, code);
	auto jMsg = FJavaHelper::ToJavaString(jniEnv, msg);
	jniEnv->CallVoidMethod(OneGlobalObj, jTrackEventRoleLoginError, *jInfo, *jIP, *jPort, *jCode,
	                                              *jMsg);
}

void OneGlobalBridge::trackEventRoleUpdate(FString info)
{
	auto jInfo = FJavaHelper::ToJavaString(jniEnv, info);
	jniEnv->CallVoidMethod(OneGlobalObj, jTrackEventRoleUpdate, *jInfo);
}

void OneGlobalBridge::trackEventRoleLogout(FString info)
{
	auto jInfo = FJavaHelper::ToJavaString(jniEnv, info);
	jniEnv->CallVoidMethod(OneGlobalObj, jTrackEventRoleLogout, *jInfo);
}

void OneGlobalBridge::trackGameResReqEvent(int stateIndex, FString url, FString errorCode, FString errorMsg)
{
	auto jUrl = FJavaHelper::ToJavaString(jniEnv, url);
	auto jErrorCode = FJavaHelper::ToJavaString(jniEnv, errorCode);
	auto jErrorMsg = FJavaHelper::ToJavaString(jniEnv, errorMsg);

	jniEnv->CallVoidMethod(OneGlobalObj, jTrackGameResReqEvent, stateIndex,
	                                              *jUrl, *jErrorCode, *jErrorMsg);
}

void OneGlobalBridge::trackGameUpdateAssetEvent(int stateIndex, FString url, FString errorCode, FString errorMsg)
{
	auto jUrl = FJavaHelper::ToJavaString(jniEnv, url);
	auto jErrorCode = FJavaHelper::ToJavaString(jniEnv, errorCode);
	auto jErrorMsg = FJavaHelper::ToJavaString(jniEnv, errorMsg);
	jniEnv->CallVoidMethod(OneGlobalObj, jTrackGameUpdateAssetEvent, stateIndex,*jUrl, *jErrorCode, *jErrorMsg);
}

void OneGlobalBridge::trackGameResDecEvent(int stateIndex, FString errorMsg)
{
	auto jErrorMsg = FJavaHelper::ToJavaString(jniEnv, errorMsg);
	jniEnv->CallVoidMethod(OneGlobalObj, jTrackGameResDecEvent, stateIndex, *jErrorMsg);
}

void OneGlobalBridge::trackGameGetServerListEvent(int stateIndex, FString url, FString errorCode, FString errorMsg)
{
	auto jUrl = FJavaHelper::ToJavaString(jniEnv, url);
	auto jErrorCode = FJavaHelper::ToJavaString(jniEnv, errorCode);
	auto jErrorMsg = FJavaHelper::ToJavaString(jniEnv, errorMsg);
	jniEnv->CallVoidMethod(OneGlobalObj, jTrackGameGetServerListEvent, stateIndex,
	                                              *jUrl, *jErrorCode, *jErrorMsg);
}

void OneGlobalBridge::trackEventEnterGameScene(FString scene, int period, FString ext)
{
	auto jScene = FJavaHelper::ToJavaString(jniEnv, scene);
	auto jExt = FJavaHelper::ToJavaString(jniEnv, ext);

	jniEnv->CallVoidMethod(OneGlobalObj, jTrackEventEnterGameScene,
	                                              AndroidJavaEnv::GetGameActivityThis(),
	                                              *jScene, period, *jExt);
}

void OneGlobalBridge::trackEventExitGameScene()
{
	jniEnv->CallVoidMethod(OneGlobalObj, jTrackEventExitGameScene);
}

void OneGlobalBridge::trackEventAddExtraDeviceInfo(FString ext)
{
	auto jExt = FJavaHelper::ToJavaString(jniEnv, ext);
	jniEnv->CallVoidMethod(OneGlobalObj, jTrackEventAddExtraDeviceInfo,
	                                              *jExt);
}

bool OneGlobalBridge::isSupportedCommonFunction(FString funcName)
{
	auto jFuncName = FJavaHelper::ToJavaString(jniEnv, funcName);
	bool support = jniEnv->CallBooleanMethod(OneGlobalObj, jIsSupportedCommonFunction,
	                                                                *jFuncName);
	return support;
}

void OneGlobalBridge::callCommonFunction(FString funcName, FString params,
                                         UOneEngineSDKSubsystem::FOneCommonFunctionDelegate callback)
{
	auto jFuncName = FJavaHelper::ToJavaString(jniEnv, funcName);
	auto jParams = FJavaHelper::ToJavaString(jniEnv, params);

	UOneEngineSDKSubsystem::FOneCommonFunctionDelegate* callFunctionCallback = new UOneEngineSDKSubsystem::FOneCommonFunctionDelegate(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jCallCommonFunction,
	                                              *jFuncName, *jParams,
	                                              reinterpret_cast<int64>(callFunctionCallback));
}


void OneGlobalBridge::openAIHelpChat( FString roleId, FString roleName, FString serverId)
{
	auto jRoleId = FJavaHelper::ToJavaString(jniEnv, roleId);
	auto jRoleName = FJavaHelper::ToJavaString(jniEnv, roleName);
	auto jServerId = FJavaHelper::ToJavaString(jniEnv, serverId);

	jniEnv->CallVoidMethod(OneGlobalObj, jOpenAIHelpChat, AndroidJavaEnv::GetGameActivityThis(),
	                                              *jRoleId, *jRoleName, *jServerId);
}

void OneGlobalBridge::openAIHelpFAQ( FString roleId, FString roleName, FString serverId)
{
	auto jRoleId = FJavaHelper::ToJavaString(jniEnv, roleId);
	auto jRoleName = FJavaHelper::ToJavaString(jniEnv, roleName);
	auto jServerId = FJavaHelper::ToJavaString(jniEnv, serverId);
	jniEnv->CallVoidMethod(OneGlobalObj, jOpenAIHelpFAQ, AndroidJavaEnv::GetGameActivityThis(),
	                                              *jRoleId, *jRoleName, *jServerId);
}

void OneGlobalBridge::translate( FString text, OnTranslateCallback callback)
{
	auto jText = FJavaHelper::ToJavaString(jniEnv, text);
	OnTranslateCallback* callFunctionCallback = new OnTranslateCallback(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jTranslate, AndroidJavaEnv::GetGameActivityThis(),
	                                              *jText, reinterpret_cast<int64>(callFunctionCallback)
	);
}

void OneGlobalBridge::openCustomService( FString roleId, FString roleName, FString serverId,
                                        bool showNavbar)
{
	auto jRoleId = FJavaHelper::ToJavaString(jniEnv, roleId);
	auto jRoleName = FJavaHelper::ToJavaString(jniEnv, roleName);
	auto jServerId = FJavaHelper::ToJavaString(jniEnv, serverId);
	jniEnv->CallVoidMethod(OneGlobalObj, jOpenCustomService, AndroidJavaEnv::GetGameActivityThis(),
	                                              *jRoleId, *jRoleName, *jServerId, showNavbar
	);
}

FString OneGlobalBridge::getLocalLanguages()
{
	jstring jResult = static_cast<jstring>(jniEnv->CallObjectMethod( OneGlobalObj, jGetLocalLanguages, AndroidJavaEnv::GetGameActivityThis()));
	return FJavaHelper::FStringFromParam(jniEnv,  jResult);
}

void OneGlobalBridge::setLanguage(FString locale)
{
	auto jLocale = FJavaHelper::ToJavaString(jniEnv, locale);
	jniEnv->CallVoidMethod(OneGlobalObj, jSetLanguage, AndroidJavaEnv::GetGameActivityThis(), *jLocale);
}

void OneGlobalBridge::setUpConfigAppId( FString appId)
{
	auto jAppId = FJavaHelper::ToJavaString(jniEnv, appId);
	jniEnv->CallVoidMethod(OneGlobalObj, jSetUpConfigAppId, AndroidJavaEnv::GetGameActivityThis(), *jAppId);
}

void OneGlobalBridge::share(EOneShareAppTarget AppTarget, const EOneShareType Type,
											const FOneShareData& Data,UOneEngineSDKSubsystem::FOneGenericResultDelegate callback)
{

	FString ShareData;
	const TSharedPtr<FJsonObject> Obj = MakeShareable(new FJsonObject);
	Obj->SetStringField(TEXT("title"), Data.Title);
	Obj->SetStringField(TEXT("content"), Data.Content);
	Obj->SetStringField(TEXT("localImagePath"), Data.LocalImagePath);
	Obj->SetStringField(TEXT("netImageUrl"), Data.NetImageUrl);
	Obj->SetStringField(TEXT("webPageUrl"), Data.WebPageUrl);
	Obj->SetStringField(TEXT("topicId"), Data.TopicId);
	if (AppTarget == EOneShareAppTarget::PE_Weibo && !Data.SinaSuperGroup.SuperGroup.IsEmpty())
	{
		{
			const TSharedPtr<FJsonObject> JsonSuperData = MakeShareable(new FJsonObject);
			JsonSuperData->SetStringField(TEXT("superGroup"), Data.SinaSuperGroup.SuperGroup);
			JsonSuperData->SetStringField(TEXT("section"), Data.SinaSuperGroup.Section);
			const TSharedPtr<FJsonObject> JsonExtraInfo = MakeShareable(new FJsonObject);
			for (auto& Element : Data.SinaSuperGroup.ExtraInfo)
			{
				JsonExtraInfo->SetStringField(Element.Key, Element.Value);
			}
			FString JsonExtraInfoStr;
			const TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonExtraInfoStr);
			FJsonSerializer::Serialize(JsonExtraInfo.ToSharedRef(), Writer);
			JsonSuperData->SetObjectField(TEXT("extraInfo"), JsonExtraInfo);
			Obj->SetObjectField(TEXT("weiboSuperData"), JsonSuperData);
		}
	}
	if (Type == EOneShareType::Image)
	{
		if (Data.Image)
		{
#if ENGINE_MAJOR_VERSION>=5
			FTexture2DMipMap& MipMap =  Data.Image->GetPlatformData()->Mips[0];
#else
			FTexture2DMipMap& MipMap =  Data.Image->PlatformData->Mips[0];
#endif
    		FByteBulkData& ImageSourceData = MipMap.BulkData;
    		Obj->SetNumberField(TEXT("imageWidth"), MipMap.SizeX);
    		Obj->SetNumberField(TEXT("imageHeight"), MipMap.SizeY);
    		
    		TArray<FColor> SrcImageData(reinterpret_cast<FColor*>(ImageSourceData.Lock(LOCK_READ_ONLY)), ImageSourceData.GetBulkDataSize() / 4);

    		TArray<uint8> ImageData;
#if ENGINE_MAJOR_VERSION==5 && ENGINE_MINOR_VERSION>=1
			FImageUtils::ThumbnailCompressImageArray(MipMap.SizeX, MipMap.SizeY, SrcImageData, ImageData);
#else
			FImageUtils::CompressImageArray(MipMap.SizeX, MipMap.SizeY, SrcImageData, ImageData);
#endif
    		const FString ExternalFilesDir = FPlatformMisc::GamePersistentDownloadDir();
    		FString TempPath = ExternalFilesDir+TEXT("/share")+FString::FromInt(FDateTime::Now().GetMillisecond())+TEXT(".png");
    		if (FFileHelper::SaveArrayToFile(ImageData, *TempPath))
    		{
    			Obj->SetStringField(TEXT("localImagePath"), TempPath);
    		}else
    		{
    			UE_LOG(LogTemp,Warning,TEXT("分享的图片数据保存到本地失败：地址 %s"),*TempPath);
    		}
    		
    		ImageSourceData.Unlock();		
			
		}
		
		
	}
	else if (Type == EOneShareType::WebPage)
	{
		if (Data.Thumbnail)
		{
#if ENGINE_MAJOR_VERSION>=5 
			FTexture2DMipMap& MipMap =  Data.Image->GetPlatformData()->Mips[0];
#else
			FTexture2DMipMap& MipMap =  Data.Image->PlatformData->Mips[0];
#endif
            FByteBulkData& ImageSourceData = MipMap.BulkData;
            Obj->SetNumberField(TEXT("imageWidth"), MipMap.SizeX);
            Obj->SetNumberField(TEXT("imageHeight"), MipMap.SizeY);
            
            TArray<FColor> SrcImageData(reinterpret_cast<FColor*>(ImageSourceData.Lock(LOCK_READ_ONLY)), ImageSourceData.GetBulkDataSize() / 4);

            TArray<uint8> ImageData;
#if ENGINE_MAJOR_VERSION==5 && ENGINE_MINOR_VERSION>=1
			FImageUtils::ThumbnailCompressImageArray(MipMap.SizeX, MipMap.SizeY, SrcImageData, ImageData);
#else
			FImageUtils::CompressImageArray(MipMap.SizeX, MipMap.SizeY, SrcImageData, ImageData);
#endif
            const FString ExternalFilesDir = FPlatformMisc::GamePersistentDownloadDir();
            FString TempPath = ExternalFilesDir+TEXT("/share")+FString::FromInt(FDateTime::Now().GetMillisecond())+TEXT(".png");
            if (FFileHelper::SaveArrayToFile(ImageData, *TempPath))
            {
            	Obj->SetStringField(TEXT("localImagePath"), TempPath);
            }else
            {
            	UE_LOG(LogTemp,Warning,TEXT("分享的图片数据保存到本地失败：地址 %s"),*TempPath);
            }
            
            ImageSourceData.Unlock();
		}
		
	}

	FJsonObjectWrapper Wrapper;
	Wrapper.JsonObject = Obj;
	Wrapper.JsonObjectToString(ShareData);
	
	auto jData = FJavaHelper::ToJavaString(jniEnv, ShareData);
	UOneEngineSDKSubsystem::FOneGenericResultDelegate* callFunctionCallback = new UOneEngineSDKSubsystem::FOneGenericResultDelegate(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jShare, AndroidJavaEnv::GetGameActivityThis(),
		static_cast<int>(AppTarget),static_cast<int>(Type),*jData,reinterpret_cast<int64>(callFunctionCallback)
	);
	
}

void OneGlobalBridge::setReceiveMsgListener(UOneEngineSDKSubsystem::FOneNotificationDelegate listener)
{
	UOneEngineSDKSubsystem::FOneNotificationDelegate* callFunctionCallback = new UOneEngineSDKSubsystem::FOneNotificationDelegate(listener);
	jniEnv->CallVoidMethod(OneGlobalObj, jSetReceiveMsgListener,
	                                              reinterpret_cast<int64>(callFunctionCallback));
}

FString OneGlobalBridge::getPushStatus()
{
	auto result =static_cast<jstring>(jniEnv->CallObjectMethod( OneGlobalObj, jGetPushStatus));
	return FJavaHelper::FStringFromParam(jniEnv,  result);
}

void OneGlobalBridge::registerPush(UOneEngineSDKSubsystem::FOneStartUpdatePushDataDelegate callback)
{
	UOneEngineSDKSubsystem::FOneStartUpdatePushDataDelegate* callFunctionCallback = new UOneEngineSDKSubsystem::FOneStartUpdatePushDataDelegate(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jRegisterPush,
	                                              reinterpret_cast<int64>(callFunctionCallback));
}

void OneGlobalBridge::setProviderPushState(bool enablePush, UOneEngineSDKSubsystem::FOneGenericResultDelegate callback)
{
	UOneEngineSDKSubsystem::FOneGenericResultDelegate* callFunctionCallback = new UOneEngineSDKSubsystem::FOneGenericResultDelegate(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jSetProviderPushState, enablePush,
	                                              reinterpret_cast<int64>(callFunctionCallback));
}

void OneGlobalBridge::bindUserId(FString serverId, FString roleId, UOneEngineSDKSubsystem::FOneGenericResultDelegate callback)
{
	auto jServerId = FJavaHelper::ToJavaString(jniEnv, serverId);
	auto jRoleId = FJavaHelper::ToJavaString(jniEnv, roleId);

	UOneEngineSDKSubsystem::FOneGenericResultDelegate* callFunctionCallback = new UOneEngineSDKSubsystem::FOneGenericResultDelegate(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jBindUserId,
	                                              *jRoleId, *jServerId,
	                                              reinterpret_cast<int64>(callFunctionCallback));
}

void OneGlobalBridge::unbindUserId(UOneEngineSDKSubsystem::FOneGenericResultDelegate callback)
{
	UOneEngineSDKSubsystem::FOneGenericResultDelegate* callFunctionCallback = new UOneEngineSDKSubsystem::FOneGenericResultDelegate(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jUnbindUserId,
	                                              reinterpret_cast<int64>(callFunctionCallback));
}

void OneGlobalBridge::getPushTypeInfoList(UOneEngineSDKSubsystem::FOneGetPushTypeInfoListDelegate callback)
{
	UOneEngineSDKSubsystem::FOneGetPushTypeInfoListDelegate* callFunctionCallback = new UOneEngineSDKSubsystem::FOneGetPushTypeInfoListDelegate(callback);
	jniEnv->CallVoidMethod(OneGlobalObj, jGetPushTypeInfoList,
	                                              reinterpret_cast<int64>(callFunctionCallback));
}

void OneGlobalBridge::updatePushTypeList(const TArray<FOnePushTypeInfo>& PushTypeList,
												UOneEngineSDKSubsystem::FOneGenericResultDelegate callBack)
{

	TArray<TSharedPtr<FJsonValue>> JsonValues;
	
	for (const FOnePushTypeInfo& Info : PushTypeList)
	{
		TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
		JsonObject->SetStringField(TEXT("name"), Info.Name);
		JsonObject->SetNumberField(TEXT("pushType"), Info.PushType);
		JsonObject->SetBoolField(TEXT("isOpen"), Info.bOpen);
		TSharedPtr<FJsonValueObject> JsonValue = MakeShareable(new FJsonValueObject(JsonObject));
		JsonValues.Add(JsonValue);
	}

	FString Result;
	TSharedRef<TJsonWriter<>> JsonWriter = TJsonWriterFactory<>::Create(&Result);
	FJsonSerializer::Serialize(JsonValues, JsonWriter);
	JsonWriter->Close();
	
	
	auto jTypeList = FJavaHelper::ToJavaString(jniEnv, Result);
	UOneEngineSDKSubsystem::FOneGenericResultDelegate* callFunctionCallback = new UOneEngineSDKSubsystem::FOneGenericResultDelegate(callBack);
	jniEnv->CallVoidMethod(OneGlobalObj, jUpdatePushTypeList,
	                                              *jTypeList,reinterpret_cast<int64>(callFunctionCallback));
}

void OneGlobalBridge::getPushNotDisturbInfo(UOneEngineSDKSubsystem::FOnePushNotDisturbInfoDelegate callBack)
{
	UOneEngineSDKSubsystem::FOnePushNotDisturbInfoDelegate* callFunctionCallback = new UOneEngineSDKSubsystem::FOnePushNotDisturbInfoDelegate(callBack);
	jniEnv->CallVoidMethod(OneGlobalObj, jGetPushNotDisturbInfo,
	                                              reinterpret_cast<int64>(callFunctionCallback));
}

void OneGlobalBridge::updatePushNotDisturb(FOnePushNotDisturbInfo notDisturbInfo, UOneEngineSDKSubsystem::FOneGenericResultDelegate callBack)
{
	TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
	JsonObj->SetBoolField(TEXT("notDisturb"), notDisturbInfo.bNotDisturb);
	JsonObj->SetStringField(TEXT("notDisturbEndTime"), notDisturbInfo.NotDisturbEndTime);
	JsonObj->SetStringField(TEXT("notDisturbStartTime"), notDisturbInfo.NotDisturbStartTime);
	FJsonObjectWrapper Wrapper;
	Wrapper.JsonObject = JsonObj;
	FString Result;
	Wrapper.JsonObjectToString(Result);
	
	auto jNotDisturbInfo = FJavaHelper::ToJavaString(jniEnv, Result);
	UOneEngineSDKSubsystem::FOneGenericResultDelegate* callFunctionCallback = new UOneEngineSDKSubsystem::FOneGenericResultDelegate(callBack);
	jniEnv->CallVoidMethod(OneGlobalObj, jUpdatePushNotDisturb,
	                                              *jNotDisturbInfo,
	                                              reinterpret_cast<int64>(callFunctionCallback));
}

void OneGlobalBridge::openApplicationSetting()
{
	jniEnv->CallVoidMethod(OneGlobalObj, jOpenApplicationSetting, AndroidJavaEnv::GetGameActivityThis());
}

void OneGlobalBridge::killProcess()
{
	jniEnv->CallVoidMethod(OneGlobalObj, jKillProcess);
}

void OneGlobalBridge::GetUserTokenList(OnGetDeviceTokenResult Callback)
{
	OnGetDeviceTokenResult* CallbackCopy = new OnGetDeviceTokenResult(Callback);
	jniEnv->CallVoidMethod(OneGlobalObj,jGetDeviceToken,AndroidJavaEnv::GetGameActivityThis(),reinterpret_cast<int64>(CallbackCopy));
}

void OneGlobalBridge::TokenLogin(const FString& Token, const FString& Uid, EOneEngineThirdType ThirdType)
{
	auto jToken = FJavaHelper::ToJavaString(jniEnv, Token);
	auto jUid = FJavaHelper::ToJavaString(jniEnv, Uid);
	jniEnv->CallVoidMethod(OneGlobalObj,jLoginByGame,AndroidJavaEnv::GetGameActivityThis(),*jUid,*jToken,
	                       static_cast<int>(ThirdType));
}

void OneGlobalBridge::ThirdLogin(EOneEngineThirdType ThirdType, bool bForcedLogin)
{
	jniEnv->CallVoidMethod(OneGlobalObj,jThirdLoginByGame,AndroidJavaEnv::GetGameActivityThis(), static_cast<int>(ThirdType),bForcedLogin);
}

void OneGlobalBridge::GuestLogin()
{
	jniEnv->CallVoidMethod(OneGlobalObj,jGuestLoginByGame,AndroidJavaEnv::GetGameActivityThis());
}

int OneGlobalBridge::getSubChannelId()
{
	return jniEnv->CallIntMethod( OneGlobalObj, jGetSubChannelId);
}

FString OneGlobalBridge::getBIName()
{
	jstring jResult = static_cast<jstring>(jniEnv->CallObjectMethod(OneGlobalObj,jGetBIName));
	return FJavaHelper::FStringFromParam(jniEnv,jResult);
}

FString OneGlobalBridge::getAntiAddictionInfo()
{
	jstring jResult = static_cast<jstring>(jniEnv->CallObjectMethod(OneGlobalObj,jGetAntiAddictionInfo));
	return FJavaHelper::FStringFromParam(jniEnv,jResult);
}

bool OneGlobalBridge::isMainland()
{
	return jniEnv->CallBooleanMethod(OneGlobalObj, jIsMainland);
}
bool OneGlobalBridge::examinStatus()
{
	return jniEnv->CallBooleanMethod( OneGlobalObj, jExaminStatus, AndroidJavaEnv::GetGameActivityThis());
}
void OneGlobalBridge::GetIpInfo(const FString& Ip,UOneEngineSDKSubsystem::FOnGetIpInfoResultDelegate Delegate)
{
	auto jIp = FJavaHelper::ToJavaString(jniEnv, Ip);
	UOneEngineSDKSubsystem::FOnGetIpInfoResultDelegate* CallbackCopy = new UOneEngineSDKSubsystem::FOnGetIpInfoResultDelegate(Delegate);
	jniEnv->CallVoidMethod(OneGlobalObj, jGetIpInfo, AndroidJavaEnv::GetGameActivityThis(), *jIp,reinterpret_cast<int64>(CallbackCopy));
}

/// 全球独有的 评价接口和 修改屏幕亮度接口
float OneGlobalBridge::GetScreenBrightness()
{
	return jniEnv->CallFloatMethod(OneGlobalObj, jGetScreenBrightness, AndroidJavaEnv::GetGameActivityThis());
}

void OneGlobalBridge::SetScreenBrightness(float BrightnessValue)
{
	jniEnv->CallVoidMethod(OneGlobalObj, jSetScreenBrightness, AndroidJavaEnv::GetGameActivityThis(), BrightnessValue);
}
	
void OneGlobalBridge::RecoverScreenBrightness()
{
	jniEnv->CallVoidMethod(OneGlobalObj, jRecoverScreenBrightness, AndroidJavaEnv::GetGameActivityThis());
}
///保持屏幕常亮
void OneGlobalBridge::SwitchScreenPermanentBrightnessState(bool bIsTurnOn)
{
	jniEnv->CallVoidMethod(OneGlobalObj, jSwitchScreenPermanentBrightnessState, AndroidJavaEnv::GetGameActivityThis(),bIsTurnOn);
}
	

///评价接口
void OneGlobalBridge::InAppRequestStoreReview(UOneEngineSDKSubsystem::FOneGenericResultDelegate Delegate)
{
	UOneEngineSDKSubsystem::FOneGenericResultDelegate* CallbackCopy = new UOneEngineSDKSubsystem::FOneGenericResultDelegate(Delegate);
	jniEnv->CallVoidMethod(OneGlobalObj, jInAppRequestStoreReview, AndroidJavaEnv::GetGameActivityThis(),reinterpret_cast<int64>(CallbackCopy));
}

/// AppLink Ios有这个参数
void OneGlobalBridge::RequestStoreReview(FString AppLink)
{
	jniEnv->CallVoidMethod(OneGlobalObj, jRequestStoreReview, AndroidJavaEnv::GetGameActivityThis());
}

/// 获取渲染器配置文件地址
FString OneGlobalBridge::GetRenderConfigFilePath()
{
	return FJavaHelper::FStringFromParam(jniEnv, (jstring)jniEnv->CallObjectMethod(OneGlobalObj, jGetRenderConfigFilePath, AndroidJavaEnv::GetGameActivityThis()));
}

void OneGlobalBridge::OpenCommunityByGame(EOneNaverGameType Type, const FString& Pid, bool Scheduled, UOneEngineSDKSubsystem::FOneLoadDelegate LoadCallback, UOneEngineSDKSubsystem::FOneUnloadDelegate UnloadCallback, UOneEngineSDKSubsystem::FOneInGameMenuDelegate InGameMenuCallback)
{
	UOneEngineSDKSubsystem::FOneLoadDelegate* LoadCopy = new UOneEngineSDKSubsystem::FOneLoadDelegate(LoadCallback);
	UOneEngineSDKSubsystem::FOneUnloadDelegate* UnloadCopy = new UOneEngineSDKSubsystem::FOneUnloadDelegate(UnloadCallback);
	UOneEngineSDKSubsystem::FOneInGameMenuDelegate* InGameCopy = new UOneEngineSDKSubsystem::FOneInGameMenuDelegate(InGameMenuCallback);
	auto jPid = FJavaHelper::ToJavaString(jniEnv, Pid);
	jniEnv->CallVoidMethod(OneGlobalObj, jOpenNaverCommunity, AndroidJavaEnv::GetGameActivityThis(),static_cast<int>(Type),*jPid,Scheduled,
		reinterpret_cast<int64>(LoadCopy),
		reinterpret_cast<int64>(UnloadCopy),
		reinterpret_cast<int64>(InGameCopy));
}
void OneGlobalBridge::TerminateCommunity()
{
	jniEnv->CallVoidMethod(OneGlobalObj, jTerminalNaverCommunity, AndroidJavaEnv::GetGameActivityThis());
}
void OneGlobalBridge::GetProviderPushState(UOneEngineSDKSubsystem::FOnGetPushStateDelegate Delegate)
{
	UOneEngineSDKSubsystem::FOnGetPushStateDelegate* CallbackCopy = new UOneEngineSDKSubsystem::FOnGetPushStateDelegate(Delegate);
	jniEnv->CallVoidMethod(OneGlobalObj, jGetProviderPushState, AndroidJavaEnv::GetGameActivityThis(),reinterpret_cast<int64>(CallbackCopy));
}
	
void OneGlobalBridge::SetAnalyticsCollectionEnabled(bool bEnable)
{
	jniEnv->CallVoidMethod(OneGlobalObj, jSetAnalyticsCollectionEnabled, AndroidJavaEnv::GetGameActivityThis(),bEnable);
}

bool OneGlobalBridge::IsInstalledApp(EOneShareAppTarget Target)
{
	return jniEnv->CallBooleanMethod(OneGlobalObj, jIsInstalledApp, static_cast<int>(Target));
}

FString OneGlobalBridge::GetGlobalOneAppId()
{
	jstring jResult = static_cast<jstring>(jniEnv->CallObjectMethod(OneGlobalObj,jGetGlobalOneAppId,AndroidJavaEnv::GetGameActivityThis()));
	return FJavaHelper::FStringFromParam(jniEnv,jResult);
}