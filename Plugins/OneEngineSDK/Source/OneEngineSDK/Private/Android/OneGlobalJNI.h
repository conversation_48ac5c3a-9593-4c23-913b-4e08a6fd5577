/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_wpsdk_one_global_bridge_OneGlobalJNI */

#ifndef _Included_com_wpsdk_one_global_bridge_OneGlobalJNI
#define _Included_com_wpsdk_one_global_bridge_OneGlobalJNI
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnGeneralResult
 * Signature: (ZILjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnGeneralResult
  (JNIEnv *, jclass, jboolean, jint, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnInitResult
 * Signature: (ZLjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnInitResult
  (JNIEnv *, jclass, jboolean, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnLoginSuccessResult
 * Signature: (Ljava/lang/String;Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnLoginSuccessResult
  (JNIEnv *, jclass, jstring, jstring);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnLoginFail
 * Signature: (IILjava/lang/String;)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnLoginFail
  (JNIEnv *, jclass, jint, jint, jstring);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnLogoutResult
 * Signature: (ZLjava/lang/String;)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnLogoutResult
  (JNIEnv *, jclass, jboolean, jstring);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnPaySuccessResult
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnPaySuccessResult
  (JNIEnv *, jclass, jstring);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnPayFailResult
 * Signature: (ILjava/lang/String;Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnPayFailResult
  (JNIEnv *, jclass, jint, jstring, jstring);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnGetProductListResult
 * Signature: (ZLjava/lang/String;ILjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnGetProductListResult
  (JNIEnv *, jclass, jboolean, jstring, jint, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnQueryActCodeResult
 * Signature: (ZZILjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnQueryActCodeResult
  (JNIEnv *, jclass, jboolean, jboolean, jint, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnRedeemCouponCode
 * Signature: (ZLjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnRedeemCouponCode
  (JNIEnv *, jclass, jboolean, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnGetUserRoleListResult
 * Signature: (ZILjava/lang/String;Ljava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnGetUserRoleListResult
  (JNIEnv *, jclass, jboolean, jint, jstring, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnScanResult
 * Signature: (Ljava/lang/String;Ljava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnScanResult
  (JNIEnv *, jclass, jstring, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnGetLocationResult
 * Signature: (Ljava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnGetLocationResult
  (JNIEnv *, jclass, jstring, jlong);
  
/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnGetAntiAddictionInfoResult
 * Signature: (Ljava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnGetAntiAddictionInfoResult
  (JNIEnv *, jclass, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnForbidGameResult
 * Signature: (ZLjava/lang/String;)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnForbidGameResult
  (JNIEnv *, jclass, jboolean, jstring);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnRequestPermissionResult
 * Signature: (IZJ)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnRequestPermissionResult
  (JNIEnv *, jclass, jint, jboolean, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnReceivePushMsg
 * Signature: (Ljava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnReceivePushMsg
  (JNIEnv *, jclass, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnGetPushTypeListResult
 * Signature: (ZLjava/lang/String;ILjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnGetPushTypeListResult
  (JNIEnv *, jclass, jboolean, jstring, jint, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnGetPushNoDisturbInfoResult
 * Signature: (ZLjava/lang/String;ILjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnGetPushNoDisturbInfoResult
  (JNIEnv *, jclass, jboolean, jstring, jint, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnTranslateResult
 * Signature: (ZLjava/lang/String;Ljava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnTranslateResult
  (JNIEnv *, jclass, jboolean, jstring, jstring, jlong);
  
/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnCallFunctionResult
 * Signature: (Ljava/lang/String;ILjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnCallFunctionResult
  (JNIEnv *, jclass, jstring, jint, jstring, jlong);
  

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnBindResult
 * Signature: (ZIILjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnBindResult
  (JNIEnv *, jclass, jboolean, jint, jint, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnExit
 * Signature: ()V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnExit
  (JNIEnv *, jclass);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    IsGameHasExitDialog
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL OneGlobalJNI_IsGameHasExitDialog
  (JNIEnv *, jclass);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnSdkDidLoaded
 * Signature: (J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnSdkDidLoaded
  (JNIEnv *, jclass, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnSdkDidUnloaded
 * Signature: (J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnSdkDidUnloaded
  (JNIEnv *, jclass, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnCallInGameMenuCode
 * Signature: (Ljava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnCallInGameMenuCode
  (JNIEnv *, jclass, jstring, jlong);
  

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnRegisterPushResult
 * Signature: (ZILjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnRegisterPushResult
  (JNIEnv *, jclass, jboolean, jint, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnGenericLambdaResult
 * Signature: (ZILjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnGenericLambdaResult
  (JNIEnv *, jclass, jboolean, jint, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnGetChannelPlatformResult
 * Signature: (ZLjava/lang/String;ILjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnGetChannelPlatformResult
  (JNIEnv *, jclass, jboolean, jstring, jint, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnGetDeviceTokensResult
 * Signature: (ZLjava/lang/String;ILjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnGetDeviceTokensResult
  (JNIEnv *, jclass, jboolean, jstring, jint, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnQueryUserActiveQualificationResult
 * Signature: (ZLjava/lang/String;ILjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnQueryUserActiveQualificationResult
  (JNIEnv *, jclass, jboolean, jstring, jint, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnDisplayCDKeyDialogResult
 * Signature: (ZILjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnDisplayCDKeyDialogResult
  (JNIEnv *, jclass, jboolean, jint, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnGetIpLocationInfoResult
 * Signature: (ZLjava/lang/String;ILjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnGetIpLocationInfoResult
  (JNIEnv *, jclass, jboolean, jstring, jint, jstring, jlong);

/*
 * Class:     com_wpsdk_one_global_bridge_OneGlobalJNI
 * Method:    OnGetPushNotificationResult
 * Signature: (ZZILjava/lang/String;J)V
 */
JNIEXPORT void JNICALL OneGlobalJNI_OnGetPushNotificationResult
  (JNIEnv *, jclass, jboolean, jboolean, jint, jstring, jlong);

#ifdef __cplusplus
}
#endif
#endif
