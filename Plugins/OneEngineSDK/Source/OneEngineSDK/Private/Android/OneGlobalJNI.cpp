#include "OneGlobalJNI.h"
#include <jni.h>

#include "JsonObjectWrapper.h"
#include "OneEngineSDKSubsystem.h"
#include "OneGlobalBridge.h"

#include "Android/AndroidJavaEnv.h"
#include "Async/Async.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonReader.h"
#include "Serialization/JsonSerializer.h"
#include "Engine/Engine.h"

extern "C"
JNIEXPORT void JNICALL OneGlobalJNI_OnGeneralResult(JNIEnv* env, jclass clazz, jboolean success, jint code,
                                                              jstring msg, jlong handle)
{
	UOneEngineSDKSubsystem::FOneGenericResultDelegate* onGeneralCallback = reinterpret_cast<
		UOneEngineSDKSubsystem::FOneGenericResultDelegate*>(handle);
	FString Msg = FJavaHelper::FStringFromParam(env, msg);
	if (onGeneralCallback)
	{
		AsyncTask(ENamedThreads::GameThread, [onGeneralCallback,success,code,Msg]()
		{
			onGeneralCallback->ExecuteIfBound(success, code, Msg);
			delete onGeneralCallback;
		});
	}
}

extern "C" JNIEXPORT void JNICALL OneGlobalJNI_OnInitResult(
	JNIEnv* env, jclass clazz, jboolean success, jstring error_msg, jlong handle)
{
	if (handle != 0)
	{
		FString Msg = FJavaHelper::FStringFromParam(env, error_msg);
		UOneEngineSDKSubsystem::FOneInitDelegate* InitDelegateCopy = reinterpret_cast<
			UOneEngineSDKSubsystem::FOneInitDelegate*>(handle);
		AsyncTask(ENamedThreads::GameThread, [InitDelegateCopy,success,Msg]()
		{
			if (InitDelegateCopy)
			{
				InitDelegateCopy->ExecuteIfBound(success, success ? 0 : -1, Msg);
			}
		});
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("走到初始化结果了111"));
		UE_LOG(LogTemp, Warning, TEXT("初始化完成设置  ThreadId:%d"), FPlatformTLS::GetCurrentThreadId());

		FString Msg = FJavaHelper::FStringFromParam(env, error_msg);
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnLoginSuccessResult(JNIEnv* env, jclass clazz, jstring uid,
                                                                   jstring token)
{
	FString Uid = FJavaHelper::FStringFromParam(env, uid);
	FString Token = FJavaHelper::FStringFromParam(env, token);
	FOneEngineSDKHelper::OnLoginResultDelegate(true, 0, "", Uid);
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnLoginFail(JNIEnv* env, jclass clazz, jint code, jint state,
                                                          jstring error_msg)
{
	FString Msg = FJavaHelper::FStringFromParam(env, error_msg);
	FOneEngineSDKHelper::OnLoginResultDelegate(false, code, Msg, "");
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnLogoutResult(JNIEnv* env, jclass clazz, jboolean success,
                                                             jstring error_msg)
{
	FString Msg = FJavaHelper::FStringFromParam(env, error_msg);
	FOneEngineSDKHelper::OnLogoutResultDelegate(success, success ? 0 : -1, Msg);
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnPaySuccessResult(JNIEnv* env, jclass clazz, jstring order_id)
{
	FString Msg;
	FString OrderId = FJavaHelper::FStringFromParam(env, order_id);
	FOneEngineSDKHelper::OnPaymetResultDelegate(true, 0, Msg, OrderId);
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnPayFailResult(JNIEnv* env, jclass clazz, jint code, jstring msg,
                                                              jstring order_id)
{
	FString Msg = FJavaHelper::FStringFromParam(env, msg);
	FString OrderId = FJavaHelper::FStringFromParam(env, order_id);
	FOneEngineSDKHelper::OnPaymetResultDelegate(false, code, Msg, OrderId);
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnGetProductListResult(JNIEnv* env, jclass clazz, jboolean success,
                                                                     jstring result, jint code, jstring msg,
                                                                     jlong handle)
{
	UOneEngineSDKSubsystem::FOneProductInfoDelegate* onGeneralCallback = reinterpret_cast<
		UOneEngineSDKSubsystem::FOneProductInfoDelegate*>(handle);
	FString Msg = FJavaHelper::FStringFromParam(env, msg);
	FString Result = FJavaHelper::FStringFromParam(env, result);

	AsyncTask(ENamedThreads::GameThread, [onGeneralCallback,success,code,Msg,Result]()
	{
		TArray<FOneProductInfo> ProductInfoList;
		if (success)
		{
			if (!Result.IsEmpty())
			{
				TArray<TSharedPtr<FJsonValue>> JsonValues;
				TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(Result);
				if (FJsonSerializer::Deserialize(JsonReader, JsonValues))
				{
					for (const TSharedPtr<FJsonValue>& JsonValue : JsonValues)
					{
						if (JsonValue->Type == EJson::Object)
						{
							// 如果JSON值是一个对象，则可以按键值对的方式提取信息
							const TSharedPtr<FJsonObject>& JsonObject = JsonValue->AsObject();
							FOneProductInfo PushTypeInfo;
							PushTypeInfo.ProductId = JsonObject->GetStringField(TEXT("productId"));
							PushTypeInfo.Price = JsonObject->GetStringField(TEXT("price"));
							PushTypeInfo.Currency = JsonObject->GetStringField(TEXT("currency"));
							PushTypeInfo.SymbolPrice = JsonObject->GetStringField(TEXT("symbolPrice"));
							PushTypeInfo.Title = JsonObject->GetStringField(TEXT("title"));
							PushTypeInfo.Desc = JsonObject->GetStringField(TEXT("desc"));
							ProductInfoList.Add(PushTypeInfo);
						}
					}
				}
				else
				{
					// 解析失败，处理错误
					UE_LOG(LogTemp, Error, TEXT("Failed to parse JSON string: %s"), *Result);
				}
			}
		}
		onGeneralCallback->ExecuteIfBound(success, code, ProductInfoList);
		delete onGeneralCallback;
	});
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnQueryActCodeResult(JNIEnv* env, jclass clazz, jboolean success,
                                                                   jboolean need_act_code, jint code, jstring msg,
                                                                   jlong handle)
{
	OneGlobalBridge::OnQueryActCodeLambda* Callback = reinterpret_cast<OneGlobalBridge::OnQueryActCodeLambda*>(handle);

	FString ActCodePrompt = FJavaHelper::FStringFromParam(env, msg);
	FString Msg = FJavaHelper::FStringFromParam(env, msg);
	AsyncTask(ENamedThreads::GameThread, [Callback,success,need_act_code,ActCodePrompt,code,Msg]()
	{
		(*Callback)(success, need_act_code, ActCodePrompt, code, Msg);
		delete Callback;
	});
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnRedeemCouponCode(JNIEnv* env, jclass clazz, jboolean success,
                                                                 jstring msg, jlong handle)
{
	OneGlobalBridge::OnGenericResult* onGeneralCallback = reinterpret_cast<OneGlobalBridge::OnGenericResult*>(handle);
	FString Msg = FJavaHelper::FStringFromParam(env, msg);
	if (onGeneralCallback)
	{
		AsyncTask(ENamedThreads::GameThread, [onGeneralCallback,success,Msg]()
		{
			(*onGeneralCallback)(success, success ? 0 : -1, Msg);
			delete onGeneralCallback;
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnGetUserRoleListResult(JNIEnv* env, jclass clazz, jboolean success,
                                                                      jint code, jstring msg, jstring result,
                                                                      jlong handle)
{
	OneGlobalBridge::OnGetUserRoleInfoResult* Delegate = reinterpret_cast<OneGlobalBridge::OnGetUserRoleInfoResult*>(
		handle);
	if (Delegate)
	{
		FString Result = FJavaHelper::FStringFromParam(env, result);
		FString Msg = FJavaHelper::FStringFromParam(env, msg);

		AsyncTask(ENamedThreads::GameThread, [Delegate,success,code,Msg,Result]()
		{
			TArray<FOneURCRoleInfo> UserList;
			if (success)
			{
				TArray<TSharedPtr<FJsonValue>> JsonArray;
				TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(Result);
				if (!FJsonSerializer::Deserialize(JsonReader, JsonArray))
				{
					UE_LOG(LogJson, Warning, TEXT("JsonArrayStringToUStruct - Unable to parse. json=[%s]"), *Result);
				}
				else
				{
					if (JsonArray.Num() > 0)
					{
						for (int32 i = 0; i < JsonArray.Num(); i++)
						{
							const TSharedPtr<FJsonObject>& RoleObj = JsonArray[i]->AsObject();
							if (RoleObj.IsValid())
							{
								FOneURCRoleInfo RoleInfo;
								RoleInfo.UserId = RoleObj->GetStringField(TEXT("userId"));
								RoleInfo.Gender = RoleObj->GetStringField(TEXT("gender"));
								RoleInfo.RoleId = RoleObj->GetStringField(TEXT("roleId"));
								RoleInfo.RoleName = RoleObj->GetStringField(TEXT("roleName"));
								RoleInfo.ServerId = RoleObj->GetStringField(TEXT("serverId"));
								RoleInfo.ServerName = RoleObj->GetStringField(TEXT("serverName"));
								RoleInfo.LastLoginTime = RoleObj->GetStringField(TEXT("lastLogin"));
								RoleInfo.Level = RoleObj->GetStringField(TEXT("lev"));
								RoleInfo.Occupation = RoleObj->GetStringField(TEXT("occupation"));
								UserList.Add(RoleInfo);
							}
						}
					}
				}
			}

			(*Delegate)(success, code, Msg, UserList);
			delete Delegate;
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnScanResult(JNIEnv* env, jclass clazz, jstring code_type,
                                                           jstring code_link, jlong handle)
{
	FString CodeType = FJavaHelper::FStringFromParam(env, code_type);
	FString CodeLink = FJavaHelper::FStringFromParam(env, code_link);
	UOneEngineSDKSubsystem::FOneGetQRCodeScanResultDelegate* Callback = reinterpret_cast<
		UOneEngineSDKSubsystem::FOneGetQRCodeScanResultDelegate*>(handle);
	AsyncTask(ENamedThreads::GameThread, [Callback, CodeType,CodeLink]()
	{
		Callback->ExecuteIfBound(CodeType, CodeLink);
		delete Callback;
	});
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnGetLocationResult(JNIEnv* env, jclass clazz, jstring location_info,
                                                                  jlong handle)
{
	OneGlobalBridge::OnGetUserLocationLambda* Delegate = reinterpret_cast<OneGlobalBridge::OnGetUserLocationLambda*>(
		handle);
	FString Data = FJavaHelper::FStringFromParam(env, location_info);
	if (Delegate)
	{
		AsyncTask(ENamedThreads::GameThread, [Delegate,Data]()
		{
			FOneUserLocationInfo OneUserLocationInfo;
			if (!Data.IsEmpty())
			{
				TSharedRef<TJsonReader<TCHAR>> JsonReader = TJsonReaderFactory<TCHAR>::Create(Data);
				TSharedPtr<FJsonObject> JsonObject;
				if (FJsonSerializer::Deserialize(JsonReader, JsonObject) && JsonObject.IsValid())
				{
					OneUserLocationInfo.IP = JsonObject->GetStringField(TEXT("ip"));
					OneUserLocationInfo.Country = JsonObject->GetStringField(TEXT("country"));
					OneUserLocationInfo.CountryAbbr = JsonObject->GetStringField(TEXT("countryAbbr"));
					OneUserLocationInfo.CountryCode = JsonObject->GetStringField(TEXT("countryCode"));
					OneUserLocationInfo.City = JsonObject->GetStringField(TEXT("countryCode"));
					OneUserLocationInfo.Province = JsonObject->GetStringField(TEXT("province"));
				}
			}
			(*Delegate)(OneUserLocationInfo);
			delete Delegate;
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnGetAntiAddictionInfoResult(
	JNIEnv* env, jclass clazz, jstring anti_addiction_info, jlong callback)
{
	UOneEngineSDKSubsystem::FOneFetchAntiAddictionInfoDelegate* Delegate = reinterpret_cast<
		UOneEngineSDKSubsystem::FOneFetchAntiAddictionInfoDelegate*>(callback);
	FString AntiAddictionInfo = FJavaHelper::FStringFromParam(env, anti_addiction_info);

	if (Delegate)
	{
		AsyncTask(ENamedThreads::GameThread, [Delegate,AntiAddictionInfo]()
		{
			FOneAntiAddictionInfo OneAntiAddictionInfo;
			FJsonObjectWrapper Wrapper;
			Wrapper.JsonObjectFromString(AntiAddictionInfo);
			if (Wrapper.JsonObject.IsValid())
			{
				Wrapper.JsonObject->TryGetNumberField(TEXT("age"),OneAntiAddictionInfo.Age);
				Wrapper.JsonObject->TryGetNumberField(TEXT("gender"),OneAntiAddictionInfo.Gender);
				Wrapper.JsonObject->TryGetNumberField(TEXT("realUser"),OneAntiAddictionInfo.Realuser);
				Wrapper.JsonObject->TryGetNumberField(TEXT("status"),OneAntiAddictionInfo.Status);
				Wrapper.JsonObject->TryGetNumberField(TEXT("accountType"),OneAntiAddictionInfo.AccountType);
				Wrapper.JsonObject->TryGetStringField(TEXT("bannedReason"),OneAntiAddictionInfo.BannedReason);
				Wrapper.JsonObject->TryGetNumberField(TEXT("bannedType"),OneAntiAddictionInfo.BannedType);
				Wrapper.JsonObject->TryGetStringField(TEXT("breakNotice"),OneAntiAddictionInfo.BreakNotice);
				Wrapper.JsonObject->TryGetNumberField(TEXT("civicType"),OneAntiAddictionInfo.CivicType);
				Wrapper.JsonObject->TryGetNumberField(TEXT("newHeartbeatInterval"),OneAntiAddictionInfo.HeartbeatInterval);
				Wrapper.JsonObject->TryGetStringField(TEXT("requestIp"),OneAntiAddictionInfo.RequestIp);
				Wrapper.JsonObject->TryGetStringField(TEXT("userId"),OneAntiAddictionInfo.UserId);
				Wrapper.JsonObject->TryGetStringField(TEXT("appId"),OneAntiAddictionInfo.AppID );
				Wrapper.JsonObject->TryGetNumberField(TEXT("dayOnlineDuration"),OneAntiAddictionInfo.DayOnlineDuration);
			}
			Delegate->ExecuteIfBound(OneAntiAddictionInfo);
			delete Delegate;
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnForbidGameResult(JNIEnv* env, jclass clazz, jboolean is_force_quit,
                                                                 jstring anti_addiction_info)
{
	FString AntiAddictionInfo = FJavaHelper::FStringFromParam(env, anti_addiction_info);
	FOneAntiAddictionInfo OneAntiAddictionInfo;
	FJsonObjectWrapper Wrapper;
	Wrapper.JsonObjectFromString(AntiAddictionInfo);
	if (Wrapper.JsonObject.IsValid())
	{
		Wrapper.JsonObject->TryGetNumberField(TEXT("age"),OneAntiAddictionInfo.Age);
		Wrapper.JsonObject->TryGetNumberField(TEXT("gender"),OneAntiAddictionInfo.Gender);
		Wrapper.JsonObject->TryGetNumberField(TEXT("realUser"),OneAntiAddictionInfo.Realuser);
		Wrapper.JsonObject->TryGetNumberField(TEXT("status"),OneAntiAddictionInfo.Status);
		Wrapper.JsonObject->TryGetNumberField(TEXT("accountType"),OneAntiAddictionInfo.AccountType);
		Wrapper.JsonObject->TryGetStringField(TEXT("bannedReason"),OneAntiAddictionInfo.BannedReason);
		Wrapper.JsonObject->TryGetNumberField(TEXT("bannedType"),OneAntiAddictionInfo.BannedType);
		Wrapper.JsonObject->TryGetStringField(TEXT("breakNotice"),OneAntiAddictionInfo.BreakNotice);
		Wrapper.JsonObject->TryGetNumberField(TEXT("civicType"),OneAntiAddictionInfo.CivicType);
		Wrapper.JsonObject->TryGetNumberField(TEXT("newHeartbeatInterval"),OneAntiAddictionInfo.HeartbeatInterval);
		Wrapper.JsonObject->TryGetStringField(TEXT("requestIp"),OneAntiAddictionInfo.RequestIp);
		Wrapper.JsonObject->TryGetStringField(TEXT("userId"),OneAntiAddictionInfo.UserId);
		Wrapper.JsonObject->TryGetStringField(TEXT("appId"),OneAntiAddictionInfo.AppID );
		Wrapper.JsonObject->TryGetNumberField(TEXT("dayOnlineDuration"),OneAntiAddictionInfo.DayOnlineDuration);
	}

	FOneEngineSDKHelper::OnAntiAddictionTimeoutResultDelegate(is_force_quit, OneAntiAddictionInfo);
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnRequestPermissionResult(JNIEnv* env, jclass clazz, jint permission,
                                                                        jboolean is_granted, jlong handle)
{
	UOneEngineSDKSubsystem::FOneRequestPermissionResultDelegate* Delegate = reinterpret_cast<
		UOneEngineSDKSubsystem::FOneRequestPermissionResultDelegate*>(handle);
	if (Delegate)
	{
		AsyncTask(ENamedThreads::GameThread, [Delegate,permission,is_granted]()
		{
			Delegate->ExecuteIfBound(static_cast<EOnePermissionType>(permission), is_granted);
			delete Delegate;
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnReceivePushMsg(JNIEnv* env, jclass clazz, jstring push_msg,
                                                               jlong handle)
{
	UOneEngineSDKSubsystem::FOneNotificationDelegate* Delegate = reinterpret_cast<
		UOneEngineSDKSubsystem::FOneNotificationDelegate*>(handle);

	if (Delegate)
	{
		FString Msg = FJavaHelper::FStringFromParam(env, push_msg);
		AsyncTask(ENamedThreads::GameThread, [Delegate,Msg]()
		{
			FOnePushMessage PushMessage;
			FJsonObjectWrapper Wrapper;
			Wrapper.JsonObjectFromString(Msg);
			if (Wrapper.JsonObject.IsValid())
			{
				PushMessage.MessageId = Wrapper.JsonObject->GetStringField(TEXT("messageId"));
				PushMessage.Content = Wrapper.JsonObject->GetStringField(TEXT("content"));
				PushMessage.Ext = Wrapper.JsonObject->GetStringField(TEXT("ext"));
				PushMessage.Title = Wrapper.JsonObject->GetStringField(TEXT("title"));
				Delegate->ExecuteIfBound(PushMessage);
			}
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnGetPushTypeListResult(JNIEnv* env, jclass clazz, jboolean success,
                                                                      jstring push_type_list, jint code, jstring msg,
                                                                      jlong handle)
{
	UOneEngineSDKSubsystem::FOneGetPushTypeInfoListDelegate* Delegate = reinterpret_cast<
		UOneEngineSDKSubsystem::FOneGetPushTypeInfoListDelegate*>(handle);
	if (Delegate)
	{
		FString Msg = FJavaHelper::FStringFromParam(env, msg);
		FString Result = FJavaHelper::FStringFromParam(env, push_type_list);
		AsyncTask(ENamedThreads::GameThread, [Delegate,success,code,Msg,Result]()
		{
			TArray<FOnePushTypeInfo> PushTypeList;
			if (success)
			{
				TArray<TSharedPtr<FJsonValue>> JsonValues;
				TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(Result);
				if (FJsonSerializer::Deserialize(JsonReader, JsonValues))
				{
					for (const TSharedPtr<FJsonValue>& JsonValue : JsonValues)
					{
						if (JsonValue->Type == EJson::Object)
						{
							// 如果JSON值是一个对象，则可以按键值对的方式提取信息
							const TSharedPtr<FJsonObject>& JsonObject = JsonValue->AsObject();
							FOnePushTypeInfo PushTypeInfo;
							PushTypeInfo.Name = JsonObject->GetStringField(TEXT("name"));
							PushTypeInfo.PushType = JsonObject->GetNumberField(TEXT("pushType"));
							PushTypeInfo.bOpen = JsonObject->GetBoolField(TEXT("isOpen"));
							PushTypeList.Add(PushTypeInfo);
						}
					}
				}
				else
				{
					// 解析失败，处理错误
					UE_LOG(LogTemp, Error, TEXT("Failed to parse JSON string"));
				}
			}
			Delegate->ExecuteIfBound(success, code, Msg, PushTypeList);
			delete Delegate;
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnGetPushNoDisturbInfoResult(
	JNIEnv* env, jclass clazz, jboolean success, jstring no_disturb_info, jint code, jstring msg, jlong handle)
{
	UOneEngineSDKSubsystem::FOnePushNotDisturbInfoDelegate* Delegate = reinterpret_cast<
		UOneEngineSDKSubsystem::FOnePushNotDisturbInfoDelegate*>(handle);
	if (Delegate)
	{
		FString Msg = FJavaHelper::FStringFromParam(env, msg);
		FString Result = FJavaHelper::FStringFromParam(env, no_disturb_info);
		AsyncTask(ENamedThreads::GameThread, [Delegate,success,code,Msg,Result]()
		{
			FOnePushNotDisturbInfo PushNotDisturbInfo;
			if (success)
			{
				FJsonObjectWrapper Wrapper;
				Wrapper.JsonObjectFromString(Result);
				TSharedPtr<FJsonObject>& JsonObj = Wrapper.JsonObject;

				if (JsonObj.IsValid())
				{
					PushNotDisturbInfo.bNotDisturb = JsonObj->GetBoolField(TEXT("notDisturb"));
					PushNotDisturbInfo.NotDisturbStartTime = JsonObj->GetStringField(TEXT("notDisturbStartTime"));
					PushNotDisturbInfo.NotDisturbEndTime = JsonObj->GetStringField(TEXT("notDisturbEndTime"));
				}
			}
			Delegate->ExecuteIfBound(success, code, Msg, PushNotDisturbInfo);
			delete Delegate;
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnTranslateResult(JNIEnv* env, jclass clazz, jboolean success,
                                                                jstring result, jstring msg, jlong callback)
{
	OneGlobalBridge::OnTranslateCallback* Delegate = reinterpret_cast<OneGlobalBridge::OnTranslateCallback*>(callback);

	if (Delegate)
	{
		FString Result = FJavaHelper::FStringFromParam(env, result);
		FString Msg = FJavaHelper::FStringFromParam(env, msg);
		AsyncTask(ENamedThreads::GameThread, [Delegate,success,Result,Msg]()
		{
			(*Delegate)(success, Result, Msg);
			delete Delegate;
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnCallFunctionResult(JNIEnv* env, jclass clazz, jstring funcName,
                                                                   jint code, jstring data, jlong callback)
{
	UOneEngineSDKSubsystem::FOneCommonFunctionDelegate* Delegate = reinterpret_cast<
		UOneEngineSDKSubsystem::FOneCommonFunctionDelegate*>(callback);
	if (Delegate)
	{
		FString FuncName = FJavaHelper::FStringFromParam(env, funcName);
		FString Msg = FJavaHelper::FStringFromParam(env, data);
		AsyncTask(ENamedThreads::GameThread, [Delegate,FuncName,code,Msg]()
		{
			Delegate->ExecuteIfBound(FuncName, code, Msg);
			delete Delegate;
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnBindResult(JNIEnv* env, jclass clazz, jboolean success, jint bind_type,
                                                           jint code, jstring msg,
                                                           jlong handle)
{
	UOneEngineSDKSubsystem::FOneBindResultDelegate* Delegate = reinterpret_cast<
		UOneEngineSDKSubsystem::FOneBindResultDelegate*>(handle);

	if (Delegate)
	{
		FString Msg = FJavaHelper::FStringFromParam(env, msg);
		AsyncTask(ENamedThreads::GameThread, [Delegate,success,code,Msg,bind_type]()
		{
			UOneEngineSDKSubsystem* subsystem = GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
			FOneUserInfo UserInfo = subsystem->GetUserInfo();
			Delegate->ExecuteIfBound(success, code, Msg, static_cast<EOneEngineThirdType>(bind_type),UserInfo);
			delete Delegate;
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnExit(JNIEnv* env, jclass clazz)
{
	FOneEngineSDKHelper::OnExit();
}

extern "C"
JNIEXPORT jboolean JNICALL
OneGlobalJNI_IsGameHasExitDialog(JNIEnv* env, jclass clazz)
{
	return FOneEngineSDKHelper::OnGameShowExitDialog();
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnRegisterPushResult(JNIEnv* env, jclass clazz, jboolean success,
                                                                   jint code, jstring msg, jlong handle)
{
	UOneEngineSDKSubsystem::FOneStartUpdatePushDataDelegate* onGeneralCallback = reinterpret_cast<
		UOneEngineSDKSubsystem::FOneStartUpdatePushDataDelegate*>(handle);
	FString Msg = FJavaHelper::FStringFromParam(env, msg);
	if (onGeneralCallback)
	{
		AsyncTask(ENamedThreads::GameThread, [onGeneralCallback,success,code,Msg]()
		{
			onGeneralCallback->ExecuteIfBound(success, code, Msg, "");
			delete onGeneralCallback;
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnGenericLambdaResult(JNIEnv* env, jclass clazz, jboolean success,
                                                                    jint code, jstring msg, jlong handle)
{
	OneGlobalBridge::OnGenericResult* onGeneralCallback = reinterpret_cast<OneGlobalBridge::OnGenericResult*>(handle);
	FString Msg = FJavaHelper::FStringFromParam(env, msg);
	if (onGeneralCallback)
	{
		AsyncTask(ENamedThreads::GameThread, [onGeneralCallback,success,code,Msg]()
		{
			(*onGeneralCallback)(success, code, Msg);
			delete onGeneralCallback;
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnGetChannelPlatformResult(
	JNIEnv* env, jclass clazz, jboolean success, jstring platform, jint code, jstring msg, jlong handle)
{
	OneGlobalBridge::OnGetPlatformResult* Callback = reinterpret_cast<OneGlobalBridge::OnGetPlatformResult*>(handle);
	FString Msg = FJavaHelper::FStringFromParam(env, msg);
	FString Platform = FJavaHelper::FStringFromParam(env, platform);
	if (Callback)
	{
		AsyncTask(ENamedThreads::GameThread, [Callback,success,code,Msg,Platform]()
		{
			(*Callback)(success, code, Msg, Platform);
			delete Callback;
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnGetDeviceTokensResult(JNIEnv* env, jclass clazz, jboolean success,
                                                                      jstring result, jint code, jstring msg,
                                                                      jlong handle)
{
	OneGlobalBridge::OnGetDeviceTokenResult* Callback = reinterpret_cast<OneGlobalBridge::OnGetDeviceTokenResult*>(
		handle);
	FString Msg = FJavaHelper::FStringFromParam(env, msg);
	FString Result = FJavaHelper::FStringFromParam(env, result);
	if (Callback)
	{
		AsyncTask(ENamedThreads::GameThread, [Callback,success,code,Msg,Result]()
		{
			TArray<FOneUserInfo> TokenList;
			if (success)
			{
				TArray<TSharedPtr<FJsonValue>> JsonValues;
				TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(Result);
				if (FJsonSerializer::Deserialize(JsonReader, JsonValues))
				{
					for (const TSharedPtr<FJsonValue>& JsonValue : JsonValues)
					{
						if (JsonValue->Type == EJson::Object)
						{
							// 如果JSON值是一个对象，则可以按键值对的方式提取信息
							//  private String uid;
							// private String token;
							// private String type;
							// private String thirdType;
							// private String username;
							// private String avatar;
							// private long lastLoginTime;
							// private int loginCount;
							const TSharedPtr<FJsonObject>& JsonObject = JsonValue->AsObject();
							FOneUserInfo TokenInfo;
							JsonObject->TryGetStringField(TEXT("uid"), TokenInfo.UserId);
							JsonObject->TryGetStringField(TEXT("token"),TokenInfo.Token);
							int Type =2;
							JsonObject->TryGetNumberField(TEXT("thirdType"),Type);
							TokenInfo.Type=static_cast<EOneEngineThirdType>(Type);
							JsonObject->TryGetStringField(TEXT("username"),TokenInfo.UserName);
							JsonObject->TryGetStringField(TEXT("avatar"),TokenInfo.Avatar);
							TokenList.Add(TokenInfo);
						}
					}
				}
				else
				{
					// 解析失败，处理错误
					UE_LOG(LogTemp, Error, TEXT("Failed to parse JSON string"));
				}
			}
			(*Callback)(success, code, Msg, TokenList);
			delete Callback;
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnQueryUserActiveQualificationResult(JNIEnv *env, jclass clazz, jboolean success, jstring result, jint code, jstring msg, jlong handle) {
	OneGlobalBridge::QueryUserActiveQualificationCallback * Callback = reinterpret_cast<OneGlobalBridge::QueryUserActiveQualificationCallback*>(handle);
	if (Callback)
	{
		FString Result = FJavaHelper::FStringFromParam(env, result);
		FString Msg = FJavaHelper::FStringFromParam(env, msg);
		AsyncTask(ENamedThreads::GameThread, [Callback,success,code,Result,Msg]()
		{
			FOneActiveQualificationInfo ActiveInfo;
			if(success)
			{
				FJsonObjectWrapper Wrapper;
				Wrapper.JsonObjectFromString(Result);
				TSharedPtr<FJsonObject>& JsonObj = Wrapper.JsonObject;
				if (JsonObj.IsValid())
				{
					ActiveInfo.Status = JsonObj->GetIntegerField(TEXT("status"));
					ActiveInfo.DeviceLogged = JsonObj->GetIntegerField(TEXT("deviceLogged"));
					ActiveInfo.DeviceTotal = JsonObj->GetIntegerField(TEXT("deviceTotal"));
					ActiveInfo.WhiteList = JsonObj->GetIntegerField(TEXT("whiteList"));
				}
			}
			
			(*Callback)(success, code, Msg,ActiveInfo);
			delete Callback;
		});
	}
}
extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnDisplayCDKeyDialogResult(JNIEnv *env, jclass clazz, jboolean success, jint code, jstring msg, jlong handle) {
	UOneEngineSDKSubsystem::FOneGenericResultDelegate* onGeneralCallback = reinterpret_cast<
		UOneEngineSDKSubsystem::FOneGenericResultDelegate*>(handle);
	FString Msg = FJavaHelper::FStringFromParam(env, msg);
	if (onGeneralCallback)
	{
		AsyncTask(ENamedThreads::GameThread, [onGeneralCallback,success,code,Msg]()
		{
			onGeneralCallback->ExecuteIfBound(success, code, Msg);
		});
	}
}

extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnGetIpLocationInfoResult(JNIEnv *env, jclass clazz, jboolean success, jstring result, jint code, jstring msg, jlong handle) {
	
	UOneEngineSDKSubsystem::FOnGetIpInfoResultDelegate* onGeneralCallback = reinterpret_cast<
		UOneEngineSDKSubsystem::FOnGetIpInfoResultDelegate*>(handle);
	FString Msg = FJavaHelper::FStringFromParam(env, msg);
	FString Result = FJavaHelper::FStringFromParam(env, result);
	if (onGeneralCallback)
	{
		AsyncTask(ENamedThreads::GameThread, [onGeneralCallback,Result,success,code,Msg]()
		{
			FUserIpInfo IpInfo;
			if(success)
			{
				FJsonObjectWrapper Wrapper;
				Wrapper.JsonObjectFromString(Result);
				TSharedPtr<FJsonObject>& JsonObj = Wrapper.JsonObject;
				if (JsonObj.IsValid())
				{
					JsonObj->TryGetStringField(TEXT("attribution"),IpInfo.Attribution);
					JsonObj->TryGetStringField(TEXT("city"),IpInfo.City);
					JsonObj->TryGetStringField(TEXT("cityCode"),IpInfo.CityCode);
					JsonObj->TryGetStringField(TEXT("country"),IpInfo.Country);
					JsonObj->TryGetStringField(TEXT("countryCode"),IpInfo.CountryCode);
					JsonObj->TryGetStringField(TEXT("region"),IpInfo.Region);
				}
			}
			onGeneralCallback->ExecuteIfBound(success,IpInfo, code, Msg);
			delete onGeneralCallback;
		});
	}
}
extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnSdkDidLoaded(JNIEnv *env, jclass clazz, jlong load_id) {
	UOneEngineSDKSubsystem::FOneLoadDelegate *onNaverLoad = reinterpret_cast<UOneEngineSDKSubsystem::FOneLoadDelegate *>(load_id);
	if (onNaverLoad) {
		AsyncTask(ENamedThreads::GameThread, [onNaverLoad]()
		{
			onNaverLoad->ExecuteIfBound();
			delete onNaverLoad;
		});
		
	}
}
extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnSdkDidUnloaded(JNIEnv *env, jclass clazz, jlong unload_id) {
	UOneEngineSDKSubsystem::FOneUnloadDelegate *onNaverLoad = reinterpret_cast<UOneEngineSDKSubsystem::FOneUnloadDelegate *>(unload_id);
	if (onNaverLoad) {
		AsyncTask(ENamedThreads::GameThread, [onNaverLoad]()
		{
			onNaverLoad->ExecuteIfBound();
			delete onNaverLoad;
		});
	}
}
extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnCallInGameMenuCode(JNIEnv *env, jclass clazz, jstring move_to, jlong call_id) {
	UOneEngineSDKSubsystem::FOneInGameMenuDelegate *onNaverLoad = reinterpret_cast<UOneEngineSDKSubsystem::FOneInGameMenuDelegate *>(call_id);
	if (onNaverLoad) {
		FString Msg = FJavaHelper::FStringFromParam(env, move_to);
		AsyncTask(ENamedThreads::GameThread, [onNaverLoad,Msg]()
		{
			onNaverLoad->ExecuteIfBound(Msg);
			delete onNaverLoad;
		});
		
	}
}
extern "C"
JNIEXPORT void JNICALL
OneGlobalJNI_OnGetPushNotificationResult(JNIEnv *env, jclass clazz, jboolean success, jboolean result, jint code, jstring msg, jlong handle) {
	UOneEngineSDKSubsystem::FOnGetPushStateDelegate *onGeneralCallback = reinterpret_cast<UOneEngineSDKSubsystem::FOnGetPushStateDelegate *>(handle);
	if (onGeneralCallback) {
		FString Msg = FJavaHelper::FStringFromParam(env, msg);
		AsyncTask(ENamedThreads::GameThread, [onGeneralCallback,success,result,code,Msg]()
		{
			onGeneralCallback->ExecuteIfBound(success,static_cast<int>(code),Msg,static_cast<bool>(result));
			delete onGeneralCallback;
		});
		
	}
}