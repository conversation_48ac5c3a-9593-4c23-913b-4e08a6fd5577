// Fill out your copyright notice in the Description page of Project Settings.


#include "OneEngineSDKIOSAdapter.h"
#include "OneEngineSDKHelper.h"
#include "IOS/IOSAppDelegate.h"

@interface IOSAppDelegate (OneEngineSDKExt)
- (BOOL)application :(UIApplication*)application
continueUserActivity : (nonnull NSUserActivity*)userActivity
restorationHandler : (nonnull void(^)(NSArray<id<UIUserActivityRestoring>> *_Nullable))restorationHandler;
@end

@implementation IOSAppDelegate (OneEngineSDKExt)
- (BOOL)application:(UIApplication*)application continueUserActivity : (nonnull NSUserActivity*)userActivity restorationHandler : (nonnull void(^)(NSArray<id<UIUserActivityRestoring>> *_Nullable))restorationHandler {
    return[WPOneEngineManager application : application continueUserActivity : userActivity restorationHandler : restorationHandler] ;
}
@end


@interface OneEngineSDKIOSAdapter ()
@property (nonatomic, copy) WPOneEngineGenericResultHandler initResultHandler;
@property (nonatomic, copy) WPOneEngineQRCodeScanResultHandler qrCodeScanResultHandler;
@property (nonatomic, copy) WPOneEngineGenericResultHandler showActCodeResultHandler;
@property (nonatomic, copy) WPOneEngineGenericResultHandler redeemCodeResultHandler;
@end

@implementation OneEngineSDKIOSAdapter

+ (OneEngineSDKIOSAdapter *)GetBridge
{
    static dispatch_once_t onceToken;
    static OneEngineSDKIOSAdapter* bridge;
    dispatch_once(&onceToken, ^{
        bridge = [[OneEngineSDKIOSAdapter alloc] init];
    });
    return bridge;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        // 初始化
        [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(initSucceed:) name:WPOneEngineInitializationSucceedNotification object:nil];
        [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(initFailed:) name:WPOneEngineInitializationFailedNotification object:nil];
        
        // 登录
        [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(loginSucceed:) name:WPOneEngineLoginSucceedNotification object:nil];
        [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(loginFailed:) name:WPOneEngineLoginFailedNotification object:nil];
        
        //登出
        [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(logoutSucceed:) name:WPOneEngineLogoutSucceedNotification object:nil];
        
        // 支付
        [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(paymentSucceed:) name:WPOneEnginePurchaseSucceedNotification object:nil];
        [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(paymentFailed:) name:WPOneEnginePurchaseFailedNotification object:nil];
        
        // 扫码回调
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(didReceivePCScanNotification:)
                                                     name:WPOneEngineCPQRCodeScanResultNotification
                                                   object:nil];
        
        // 防沉迷到时间踢出回调
        [NSNotificationCenter.defaultCenter addObserver:self
                                               selector:@selector(onForceKickNote:)
                                                   name:WPOneEngineFatigueManageShouldLogoutNotification
                                                 object:nil];
        
        // 激活码
        [NSNotificationCenter.defaultCenter addObserver:self
                                               selector:@selector(onCDKeySucceedNote:)
                                                   name:WPOneEngineActCodeVerifySucceedNotification
                                                 object:nil];
        [NSNotificationCenter.defaultCenter addObserver:self
                                               selector:@selector(onCDKeyFailedNote:)
                                                   name:WPOneEngineActCodeVerifyFailedNotification
                                                 object:nil];
        //兑换码
        [NSNotificationCenter.defaultCenter addObserver:self
                                               selector:@selector(onRedeemCouponSucceedNote:)
                                                   name:WPOneEngineRedeemCodeVerifySucceedNotification
                                                 object:nil];
        [NSNotificationCenter.defaultCenter addObserver:self
                                               selector:@selector(onRedeemCouponFailedNote:)
                                                   name:WPOneEngineRedeemCodeVerifyFailedNotification
                                                 object:nil];
        
        UE_LOG(LogTemp, Warning, TEXT("--OneEngineSDK--init") );
    }
    
    return self;
}

- (void)registerInitCallBackCompletion:(WPOneEngineGenericResultHandler)completion
{
    self.initResultHandler = completion;
}

- (void)initSucceed:(NSNotification *)note
{
    if(self.initResultHandler) {
        self.initResultHandler(0,@"");
        self.initResultHandler =  nil;
    }
}

- (void)initFailed:(NSNotification *)note
{
    NSError* error = note.object;
    if(self.initResultHandler) {
        self.initResultHandler(error.code,error.localizedDescription);
        self.initResultHandler =  nil;
    }
}

- (void)loginSucceed:(NSNotification *)note
{
    NSDictionary* _result = note.object;
    NSString* userId = _result[@"userId"];
    FOneEngineSDKHelper::OnLoginResultDelegate(true, 0,"",FString(userId));
}

- (void)loginFailed:(NSNotification *)note
{
    NSError* error = note.object;
    int32 Code = error.code;
    FOneEngineSDKHelper::OnLoginResultDelegate(false, Code,FString(error.localizedDescription),"");
}

- (void)logoutSucceed:(NSNotification *)note
{
    FOneEngineSDKHelper::OnLogoutResultDelegate(true,0,"");
}

- (void)paymentSucceed:(NSNotification *)note
{
    NSDictionary* result = note.object;
    NSString* orderId = result[@"order"];
    FOneEngineSDKHelper::OnPaymetResultDelegate(true,0,"",FString(orderId));
}

- (void)paymentFailed:(NSNotification *)note
{
    NSError* error = note.object;
    int32 Code = error.code;
    FOneEngineSDKHelper::OnPaymetResultDelegate(false,Code,FString(error.localizedDescription),"");
}

- (void)launchPCScanLoginCompletion:(void(^)(NSString * __nullable codeType,NSString * __nullable codeLink))completion
{
    self.qrCodeScanResultHandler = completion;
    [WPOneEngineManager launchPCScanLogin];
}

- (void)didReceivePCScanNotification:(NSNotification *)notification
{
    NSDictionary *result = (NSDictionary *)notification.object;
    NSString *codeType = [result objectForKey:@"codeType"];
    NSString *codeLink = [result objectForKey:@"codeLink"];
    if(self.qrCodeScanResultHandler) {
        self.qrCodeScanResultHandler(codeType,codeLink);
        self.qrCodeScanResultHandler =  nil;
    }
}

- (void)onForceKickNote:(NSNotification *)note
{
    WPOneEngineFMBaseInfo *info = (WPOneEngineFMBaseInfo *)note.object;
    FOneAntiAddictionInfo FAntiAddictionInfo;
    FAntiAddictionInfo.AppID = FString(@(info.appId).stringValue);
    FAntiAddictionInfo.UserId = FString(info.userId);
    FAntiAddictionInfo.Status = (int32_t)info.status;
    FAntiAddictionInfo.HeartbeatInterval = (int32_t)info.heartbeatInterval;
    FAntiAddictionInfo.BannedType = (int32_t)info.bannedType;
    FAntiAddictionInfo.BannedReason = FString(info.bannedReason);
    FAntiAddictionInfo.BreakNotice = FString(info.breakNotice);
    FAntiAddictionInfo.Realuser = (int32_t)info.realUser;
    FAntiAddictionInfo.CivicType = (int32_t)info.civicType;
    FAntiAddictionInfo.Age = (int32_t)info.age;
    FAntiAddictionInfo.Gender = (int32_t)info.gender;
    FAntiAddictionInfo.AccountType = (int32_t)info.accountType;
    FAntiAddictionInfo.RequestIp = FString(info.requestIp);
    FAntiAddictionInfo.DayOnlineDuration = (int32_t)info.dayOnlineDuration;
    FOneEngineSDKHelper::OnAntiAddictionTimeoutResultDelegate(true,FAntiAddictionInfo);
}

// #pragma mark - 激活码
- (void)showActCodeViewWithServerId:(NSString *)serviceId completion:(WPOneEngineGenericResultHandler)completion
{
    self.showActCodeResultHandler = completion;
    [WPOneEngineManager showActCodeViewWithServerId:serviceId];
}

- (void)onCDKeySucceedNote:(NSNotification *)note
{
    NSError *error = (NSError *)note.object;
    if(self.showActCodeResultHandler) {
        self.showActCodeResultHandler(error?error.code:0,error?error.localizedDescription:nil);
        self.showActCodeResultHandler = nil;
    }
}

- (void)onCDKeyFailedNote:(NSNotification *)note
{
    NSError *error = (NSError *)note.object;
    if(self.showActCodeResultHandler) {
        self.showActCodeResultHandler(error?error.code:0,error?error.localizedDescription:nil);
        self.showActCodeResultHandler = nil;
    }
}

// #pragma mark - 兑换码
- (void)verifyRedeemCodeWithRoleId:(NSString *)roleId
                            serverId:(NSString *)serverId
                            level:(NSInteger)level
                            vip:(NSInteger)vip
                            redeemCode:(NSString *)redeemCode
                            extraInfo:(NSDictionary *)extraInfo
                            completion:(WPOneEngineGenericResultHandler)completion
{
    self.redeemCodeResultHandler = completion;
    [WPOneEngineManager verifyRedeemCodeWithRoleId:roleId
                                          serverId:serverId
                                             level:level
                                               vip:vip
                                        redeemCode:redeemCode
                                         extraInfo:extraInfo];
}
- (void)onRedeemCouponSucceedNote:(NSNotification *)note
{
    NSString* msg =  [note.object isKindOfClass:[NSDictionary class]] ? note.object[@"message"] : nil;
    if(self.redeemCodeResultHandler) {
        self.redeemCodeResultHandler(0,msg);
        self.redeemCodeResultHandler = nil;
    }
}

- (void)onRedeemCouponFailedNote:(NSNotification *)note
{
    NSError *error = (NSError *)note.object;
    if(self.redeemCodeResultHandler) {
        self.redeemCodeResultHandler(error?error.code:0,error?error.localizedDescription:nil);
        self.redeemCodeResultHandler = nil;
    }
}

@end
