
#include "OneEngineSDKSubsystem.h"
#include "IOS/IOSAppDelegate.h"
#include "Misc/ScopeLock.h"
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"
#include "OneEngineSDKHelper.h"
#include "OneEngineSDKIOSAdapter.h"
#import <Foundation/Foundation.h>
#include <objc/objc.h>
#import <WPOneEngineBridge/WPOneEngineBridge.h>
#include "JsonObjectWrapper.h"
#include "Runtime/Launch/Resources/Version.h"
#include "TextureResource.h"
#include "Engine/Texture2D.h"
#include "Serialization/BulkData.h"
#include "Misc/ConfigCacheIni.h"
#include "Dom/JsonObject.h"
#include "Async/Async.h"
#include "OneEngineSDK.h"
#import <StoreKit/StoreKit.h>

#ifdef KONEENGINE_REGION_MAINLAND
#import <AutoShowTerms/AutoShowTerms.h>
IMPL_SWIZZLE_FINISHLAUNCH_AST(IOSAppDelegate)
#endif

static WPOneEngineTrackEventResState OneEngineStateFromResState(EOneResEventState state)
{
    switch (state) {
        case EOneResEventState::Begin:
            return WPOneEngineTrackEventResStateBegin;
            break;
        case EOneResEventState::Success:
            return WPOneEngineTrackEventResStateSuccess;
            break;
        case EOneResEventState::Failed:
            return WPOneEngineTrackEventResStateError;
            break;
            
        default:
            break;
    }
    return WPOneEngineTrackEventResStateError;
}

// 权限类型转换
static WPOneEngineAppPrivacyType OneEnginePrivacyTypeWithPermissionType(EOnePermissionType type)
{
    int undefineType = -1;
    WPOneEngineAppPrivacyType undefinePrivacyType = static_cast<WPOneEngineAppPrivacyType>(undefineType);
    switch (type) {
        case EOnePermissionType::Clipboard:
            return WPOneEngineAppPrivacyTypePasteboardCopy;
        default:
            return undefinePrivacyType;
    }
}

static UIImage* OneEngineUIImageFromUTexture2D(UTexture2D* Tex)
{
#if ENGINE_MAJOR_VERSION==5 && ENGINE_MINOR_VERSION>=4
    FTexture2DMipMap& MipMap = Tex->GetPlatformData()->Mips[0];
#else
    FTexture2DMipMap& MipMap = Tex->PlatformData->Mips[0];
#endif
    FByteBulkData& ImageSourceData = MipMap.BulkData;
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    CGContextRef context = CGBitmapContextCreate(ImageSourceData.Lock(LOCK_READ_ONLY), MipMap.SizeX, MipMap.SizeY, 8, MipMap.SizeX * 4, colorSpace, kCGBitmapByteOrder32Little | kCGImageAlphaPremultipliedFirst);
    
    CGImageRef imageRef = CGBitmapContextCreateImage(context);
    
    UIImage* result = [UIImage imageWithCGImage : imageRef];
    CGContextRelease(context);
    CGColorSpaceRelease(colorSpace);
    CGImageRelease(imageRef);
    ImageSourceData.Unlock();
    
    return result;
}

static WPOneEngineSharePlatformType OneEngineSocialTypeFromShareAppTarget(EOneShareAppTarget appTarget)
{
    switch (appTarget) {
        case EOneShareAppTarget::PE_WeChatSession:
            return WPOneEngineSharePlatformTypeWechatSession;
        case EOneShareAppTarget::PE_WeChatMoment:
            return WPOneEngineSharePlatformTypeWechatTimeLine;
        case EOneShareAppTarget::PE_QQ:
            return WPOneEngineSharePlatformTypeQQ;
        case EOneShareAppTarget::PE_QZone:
            return WPOneEngineSharePlatformTypeQZone;
        case EOneShareAppTarget::PE_Weibo:
            return WPOneEngineSharePlatformTypeSina;
        case EOneShareAppTarget::PE_Bilibili:
            return WPOneEngineSharePlatformTypeBilibili;
        case EOneShareAppTarget::PE_Facebook:
            return WPOneEngineSharePlatformTypeFacebook;
        case EOneShareAppTarget::PE_VK:
            return WPOneEngineSharePlatformTypeVK;
        case EOneShareAppTarget::PE_Instagram:
            return WPOneEngineSharePlatformTypeInstagram;
        case EOneShareAppTarget::PE_Twitter:
            return WPOneEngineSharePlatformTypeTwitter;
        case EOneShareAppTarget::PE_Line:
            return WPOneEngineSharePlatformTypeLine;
        case EOneShareAppTarget::PE_NaverGame:
            return WPOneEngineSharePlatformTypeNaverGame;
        case EOneShareAppTarget::PE_TikTok:
            return WPOneEngineSharePlatformTypeTikTok;
        case EOneShareAppTarget::PE_Discord:
            return WPOneEngineSharePlatformTypeDiscord;
        case EOneShareAppTarget::PE_Telegram:
            return WPOneEngineSharePlatformTypeTelegram;
        default:
        {
            int undefineType = -1;
            WPOneEngineSharePlatformType undefinePlatformType = static_cast<WPOneEngineSharePlatformType>(undefineType);
            return undefinePlatformType;
        }
    }
}

static FOneUserInfo WPOneUserInfoFromNative(WPOneEngineUserInfo *userInfo)
{
    FOneUserInfo Item;
    Item.UserId = FString(userInfo.userId);
    Item.Token = FString(userInfo.token);
    Item.Phone = FString(userInfo.phone);
    Item.Avatar = FString(userInfo.avatar);
    Item.UserName = FString(userInfo.name);
    Item.InheritCode = FString(userInfo.inheritCode);
    Item.bPasswordExist = userInfo.passwordExist;
    Item.CountryCode = FString(userInfo.countryCode);
    Item.Age = userInfo.age;
    Item.bIsNewCreate = userInfo.isNewFlag;
    Item.bIsAdult = userInfo.isAdult;
    Item.AgeCountryCode = FString(userInfo.ageCountryCode);
    
    TArray<FOneUserThirdInfo> ThirdList;
    for (WPOneEngineThirdInfo *thirdInfo in userInfo.thirds) {
        FOneUserThirdInfo ThirdItem;
        ThirdItem.UserId = FString(thirdInfo.userId);
        ThirdItem.ThirdId = FString(thirdInfo.thirdId);
        ThirdItem.Avatar = FString(thirdInfo.avatar);
        ThirdItem.UserName = FString(thirdInfo.name);
        ThirdItem.Email = FString(thirdInfo.email);
        ThirdItem.Type = (EOneEngineThirdType)thirdInfo.type;
        ThirdList.Add(ThirdItem);
    }
    Item.Thirds = ThirdList;
    Item.Type = (EOneEngineThirdType)userInfo.type;
    return Item;
}

static void HandleOpenURL(UIApplication* application, NSURL* url, NSString* sourceApplication, id annotation)
{
    [WPOneEngineManager application : application openURL : url options : @{}] ;
}

void UOneEngineSDKSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
}

static bool initFlag = false;
void UOneEngineSDKSubsystem::Init(FOneInitDelegate InitDelegate)
{
    check(IsInGameThread());
    if (initFlag) {
        //防止重复调用初始化
        return;
    }
    initFlag = true;
    [[OneEngineSDKIOSAdapter GetBridge] registerInitCallBackCompletion:^(NSInteger code, NSString *message) {
        int32 Code = code;
        bool  bSuccess = (Code == 0) ? true : false;
        FString Msg = FString(message);
        [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
        {
            InitDelegate.ExecuteIfBound(bSuccess,Code, Msg);
            return true;
        }];
    }];
    
#ifdef KONEENGINE_REGION_MAINLAND
    if(!GConfig) return;
    FString AppId;
    FString AppKey;
    FString BilibiliAppSecret;
    GConfig->GetString(TEXT("/Script/OneEngineSDK"), TEXT("AppID"), AppId, GGameIni);
    GConfig->GetString(TEXT("/Script/OneEngineSDK"), TEXT("AppKey"), AppKey, GGameIni);
    GConfig->GetString(TEXT("/Script/OneEngineSDK"), TEXT("BilibiliAppSecret"), BilibiliAppSecret, GGameIni);
    
    // UE_LOG(LogTemp, Warning, TEXT("--OneSDK-- OneSDKAppId=%s"), *AppId);
    // UE_LOG(LogTemp, Warning, TEXT("--OneSDK-- OneSDKAppKey=%s"), *AppKey);
    
    FString ChannelId = "9";
    NSString* appId_ns = AppId.GetNSString();
    NSString* appKey_ns = AppKey.GetNSString();
    NSString* configPath_ns = [[NSBundle mainBundle] pathForResource:@"OneSDKiOSConfigFile.config" ofType:nil];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        //初始化
        [WPOneEngineManager shouldVerifyBundleId:NO];
        [WPOneEngineManager initWithAppId:appId_ns
                                   appKey:appKey_ns
                                   config:configPath_ns];
    });

    // 判断是否需要初始化Bilibili
    if(!BilibiliAppSecret.IsEmpty())
    {
        NSString* BilibiliAppSecret_ns = BilibiliAppSecret.GetNSString();
        dispatch_async(dispatch_get_main_queue(), ^{
            [WPOneEngineManager initAppSecretWithPlatfomType:WPOneEngineSharePlatformTypeBilibili appSecret:BilibiliAppSecret_ns];
        });
    }
    
#else
    dispatch_async(dispatch_get_main_queue(), ^{
        //初始化
        [WPOneEngineManager initWithAppId:nil
                                   appKey:nil
                                   config:nil];
    });
#endif
    
    FIOSCoreDelegates::OnOpenURL.AddStatic(&HandleOpenURL);
}

EOneEngineSDKRegionType UOneEngineSDKSubsystem::GetRegionType()
{
    WPOneEngineRegionType Type = [WPOneEngineManager regionType];
    return static_cast<EOneEngineSDKRegionType>(Type);
}

void UOneEngineSDKSubsystem::Login()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager login];
    });
}

void UOneEngineSDKSubsystem::GetUserTokenList(FOneFetchUserTokenListDelegate OnGetTokenListDelegate)
{
    auto GetTokenListResultLambda = [OnGetTokenListDelegate](bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)
    {
        OnGetTokenListDelegate.ExecuteIfBound(bSucceed,Code, Msg, TokenList );
    };
    GetUserTokenListLambda(GetTokenListResultLambda);
}

void UOneEngineSDKSubsystem::GetUserTokenListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)> &OnFinishedLambda)
{
    TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)> CopiedLambda = OnFinishedLambda;
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager getDeviceTokensWithComplete:^(id responseObject, NSError *error) {
            [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
             {
                TArray<FOneUserInfo> UserTokenList;
                if (!error) {
                    for (WPOneEngineUserInfo *userInfo in responseObject) {
                        FOneUserInfo Item = WPOneUserInfoFromNative(userInfo);
                        UserTokenList.Add(Item);
                    }
                    CopiedLambda(true, 0,"",UserTokenList);
                } else {
                    int32 Code = error.code;
                    CopiedLambda(false, Code,FString(error.localizedDescription) ,UserTokenList);
                }
                return true;
            }];
        }];
    });
}

void UOneEngineSDKSubsystem::TokenLogin(const FString& Token, const FString& Uid, EOneEngineThirdType ThirdType)
{
    NSString* token_ns = Token.GetNSString();
    NSString* uid_ns = Uid.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager tokenLogin:token_ns uid:uid_ns loginType:static_cast<WPOneEnginePlatformType>(ThirdType) complete:^(id responseObject, NSError *error) {
            [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
             {
                if (!error) {
                    NSString *userId = ((WPOneEngineUserInfo *)responseObject).userId;
                    FOneEngineSDKHelper::OnLoginResultDelegate(true, 0,"",FString(userId));
                } else {
                    int32 Code = error.code;
                    FOneEngineSDKHelper::OnLoginResultDelegate(false, Code,FString(error.localizedDescription),"");
                }
                return true;
            }];
        }];
    });
}

void UOneEngineSDKSubsystem::ThirdLogin(EOneEngineThirdType ThirdType, bool bForcedLogin)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager thirdLoginWithPlatformType:static_cast<WPOneEnginePlatformType>(ThirdType) isForceLogin:bForcedLogin complete:^(id responseObject, NSError *error) {
            [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
             {
                if (!error) {
                    NSString *userId = ((WPOneEngineUserInfo *)responseObject).userId;
                    FOneEngineSDKHelper::OnLoginResultDelegate(true, 0,"",FString(userId));
                } else {
                    int32 Code = error.code;
                    FOneEngineSDKHelper::OnLoginResultDelegate(false, Code,FString(error.localizedDescription),"");
                }
                return true;
            }];
        }];
    });
}

void UOneEngineSDKSubsystem::GuestLogin()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager guestLoginWithComplete:^(id responseObject, NSError *error) {
            [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
             {
                if (!error) {
                    NSString *userId = ((WPOneEngineUserInfo *)responseObject).userId;
                    FOneEngineSDKHelper::OnLoginResultDelegate(true, 0,"",FString(userId));
                } else {
                    int32 Code = error.code;
                    FOneEngineSDKHelper::OnLoginResultDelegate(false, Code,FString(error.localizedDescription),"");
                }
                return true;
            }];
        }];
    });
}

void UOneEngineSDKSubsystem::SwitchAccount()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager switchAccount];
    });
}

void UOneEngineSDKSubsystem::Pay(const FOnePaymentInfo& PaymentInfo)
{
    NSString* price = PaymentInfo.Price.GetNSString();
    NSString* orderNum = PaymentInfo.OrderId.GetNSString();
    NSString* productName = PaymentInfo.ProductName.GetNSString();
    NSString* gameServerId = PaymentInfo.GameServerId.GetNSString();
    NSString* gameServerName = PaymentInfo.ServerName.GetNSString();
    NSString* ext = PaymentInfo.ExtInfo.GetNSString();
    NSString* productId = PaymentInfo.ProductId.GetNSString();
    NSString* productCount = PaymentInfo.ProductCount.GetNSString();
    NSString* roleId = PaymentInfo.RoleId.GetNSString();
    NSString* roleName = PaymentInfo.RoleName.GetNSString();
    NSString* paySuccessUrl = PaymentInfo.PaySuccessUrl.GetNSString();
    
    WPOneEnginePurchaseInfo *purchaseInfo = [[WPOneEnginePurchaseInfo alloc] init];
    purchaseInfo.productPrice = [price integerValue];
    purchaseInfo.orderId = orderNum;
    purchaseInfo.roleName = roleName;
    purchaseInfo.productName = productName;
    purchaseInfo.gameServerId = [gameServerId integerValue];
    purchaseInfo.gameServerName = gameServerName;
    purchaseInfo.ext = ext;
    purchaseInfo.productId = productId;
    purchaseInfo.productCount = [productCount integerValue];
    purchaseInfo.roleId = roleId;
    purchaseInfo.paySuccessUrl = paySuccessUrl;
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager purchaseWithInfo:purchaseInfo];
    });
}

FOneUserInfo UOneEngineSDKSubsystem::GetUserInfo()
{
    WPOneEngineUserInfo *userInfo = [WPOneEngineManager getUserInfo];
    FOneUserInfo UserInfo = WPOneUserInfoFromNative(userInfo);
    return UserInfo;
}

// 获取档位信息
void UOneEngineSDKSubsystem::GetProductList(const TArray<FString>& ProductIds, FOneProductInfoDelegate ProductResultDelegate)
{
    NSMutableArray* productIdArray = [[NSMutableArray alloc] init];
    for (auto item : ProductIds)
    {
        [productIdArray addObject:item.GetNSString()];
    }
    [WPOneEngineManager getProductList:productIdArray
                              complete:^(id responseObject, NSError *error) {
        [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
         {
            TArray<FOneProductInfo> ProductResult;
            if (!error) {
                for (WPOneEngineProductInfo  *product in responseObject) {
                    FOneProductInfo item;
                    item.ProductId = FString(product.productId);
                    item.Price = FString(product.price);
                    item.Currency = FString(product.currency);
                    item.SymbolPrice = FString(product.symbolPrice);
                    item.Title = FString(product.title);
                    item.Desc = FString(product.desc);
                    ProductResult.Add(item);
                }
                ProductResultDelegate.ExecuteIfBound(true, 0, ProductResult);
            } else {
                int32 Code = error.code;
                ProductResultDelegate.ExecuteIfBound(false, Code, ProductResult);;
            }
            return true;
        }];
    }];
}

// 角色事件打点
void UOneEngineSDKSubsystem::TrackEventRoleCreate(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
    NSString* roleId = RoleInfo.RoleId.GetNSString();
    NSString* serverId = RoleInfo.ServerId.GetNSString();
    NSString* level = RoleInfo.Level.GetNSString();
    NSString* vip = RoleInfo.Vip.GetNSString();
    NSString* ip_ns = Ip.GetNSString();
    NSString* port_ns = Port.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventRoleCreateRoleId:roleId
                                          serverId:serverId
                                             level:level
                                               vip:vip
                                                ip:ip_ns
                                              port:port_ns];
    });
}

void UOneEngineSDKSubsystem::TrackEventRoleLoginSucceed(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
    NSString* roleId = RoleInfo.RoleId.GetNSString();
    NSString* serverId = RoleInfo.ServerId.GetNSString();
    NSString* roleName = RoleInfo.RoleName.GetNSString();
    NSString* serverName = RoleInfo.ServerName.GetNSString();
    NSString* level = RoleInfo.Level.GetNSString();
    NSString* vip = RoleInfo.Vip.GetNSString();
    NSString* ip_ns = Ip.GetNSString();
    NSString* port_ns = Port.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventRoleLoginRoleId:roleId
                                            roleName:roleName
                                            serverId:serverId
                                            serverName:serverName
                                            level:level
                                            vip:vip
                                            ip:ip_ns
                                            port:port_ns];
    });
}

void UOneEngineSDKSubsystem::TrackEventRoleLoginError(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port, const FString& Code, const FString& Msg)
{
    NSString* roleId = RoleInfo.RoleId.GetNSString();
    NSString* serverId = RoleInfo.ServerId.GetNSString();
    NSString* level = RoleInfo.Level.GetNSString();
    NSString* vip = RoleInfo.Vip.GetNSString();
    NSString* ip_ns = Ip.GetNSString();
    NSString* port_ns = Port.GetNSString();
    NSString* errorCode = Code.GetNSString();
    NSString* errorMsg = Msg.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventRoleLoginErrorRoleId:roleId
                                              serverId:serverId
                                                 level:level
                                                   vip:vip
                                                    ip:ip_ns
                                                  port:port_ns
                                             errorCode:errorCode
                                              errorMsg:errorMsg];
    });
}

void UOneEngineSDKSubsystem::TrackEventRoleLevelUp(const FOneRoleInfo& RoleInfo)
{
    NSString* roleId = RoleInfo.RoleId.GetNSString();
    NSString* serverId = RoleInfo.ServerId.GetNSString();
    NSString* level = RoleInfo.Level.GetNSString();
    NSString* vip = RoleInfo.Vip.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventRoleUpdateRoleId:roleId
                                          serverId:serverId
                                             level:level
                                               vip:vip];
    });
}


void UOneEngineSDKSubsystem::TrackEventRoleLogout(const FOneRoleInfo& RoleInfo)
{
    NSString* roleId = RoleInfo.RoleId.GetNSString();
    NSString* serverId = RoleInfo.ServerId.GetNSString();
    NSString* level = RoleInfo.Level.GetNSString();
    NSString* vip = RoleInfo.Vip.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventRoleLogoutRoleId:roleId
                                          serverId:serverId
                                             level:level
                                               vip:vip];
    });
}

// 资源事件打点
void UOneEngineSDKSubsystem::GameResReqEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
    NSString* url_ns = Url.GetNSString();
    NSString* errorCode = ErrorCode.GetNSString();
    NSString* errorMsg = ErrorMsg.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager gameResReqEvent:OneEngineStateFromResState(State)
                                    url:url_ns
                              errorCode:errorCode
                               errorMsg:errorMsg];
    });
}

void UOneEngineSDKSubsystem::GameUpdateAssetEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
    NSString* url_ns = Url.GetNSString();
    NSString* errorCode = ErrorCode.GetNSString();
    NSString* errorMsg = ErrorMsg.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager gameUpdateAssetEvent:OneEngineStateFromResState(State)
                                         url:url_ns
                                   errorCode:errorCode
                                    errorMsg:errorMsg];
    });
}

void UOneEngineSDKSubsystem::GameResDecEvent(EOneResEventState State, const FString& ErrorMsg)
{
    NSString* errorMsg = ErrorMsg.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager gameResDecEvent:OneEngineStateFromResState(State) errorMsg:errorMsg];
    });
}

void UOneEngineSDKSubsystem::GameGetServerListEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
    NSString* url_ns = Url.GetNSString();
    NSString* errorCode = ErrorCode.GetNSString();
    NSString* errorMsg = ErrorMsg.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager gameGetServerListEvent:OneEngineStateFromResState(State)
                                           url:url_ns
                                     errorCode:errorCode
                                      errorMsg:errorMsg];
    });
}

// 自定义事件打点
void UOneEngineSDKSubsystem::TrackEvent(const FString& Name, const TMap<FString, FString>& Payload)
{
    NSMutableDictionary* dic = [NSMutableDictionary dictionaryWithCapacity:Payload.Num()];
    for (auto& KVP : Payload)
    {
        NSString* key = KVP.Key.GetNSString();
        NSString* val = KVP.Value.GetNSString();
        dic[key] = val;
    }
    NSString* eventName_NS = Name.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackCustomEvent:eventName_NS
                                    info:dic];
    });
}

void UOneEngineSDKSubsystem::TrackEventAD(const FString& Name, const TMap<FString, FString>& Payload)
{
    NSMutableDictionary* dic = [NSMutableDictionary dictionaryWithCapacity:Payload.Num()];
    for (auto& KVP : Payload)
    {
        NSString* key = KVP.Key.GetNSString();
        NSString* val = KVP.Value.GetNSString();
        dic[key] = val;
    }
    NSString* eventName_NS = Name.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackCustomEventAD:eventName_NS
                                      info:dic];
    });
}

//进入游戏场景打点
void UOneEngineSDKSubsystem::TrackEventEnterGameScene(const FString& SceneName,int32 Period,const TMap<FString,FString>& HintMap)
{
    NSString* SceneName_NS = SceneName.GetNSString();
    NSMutableDictionary* dic = [NSMutableDictionary dictionaryWithCapacity:HintMap.Num()];
    for (auto& KVP : HintMap)
    {
        NSString* key = KVP.Key.GetNSString();
        NSString* val = KVP.Value.GetNSString();
        dic[key] = val;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventEnterScene:SceneName_NS
                                      period:Period
                                  attributes:dic];
    });
}

//退出游戏打点
void UOneEngineSDKSubsystem::TrackEventExitGameScene()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventExitScene:@{}];
    });
}

//设备打点额外信息
void UOneEngineSDKSubsystem::TrackEventAddExtraDeviceInfo(const TMap<FString,FString>& ExtraDeviceInfo)
{
    NSMutableDictionary* dic = [NSMutableDictionary dictionaryWithCapacity:ExtraDeviceInfo.Num()];
    for (auto& KVP : ExtraDeviceInfo)
    {
        NSString* key = KVP.Key.GetNSString();
        NSString* val = KVP.Value.GetNSString();
        dic[key] = val;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventAddExtraDeviceInfo:dic];
    });
}

bool UOneEngineSDKSubsystem::IsLoggedIn()
{
    return [WPOneEngineManager isLogined];
}

void UOneEngineSDKSubsystem::SetUpConfigAppID(const FString& AppId)
{
    NSString* AppId_NS = AppId.GetNSString();
    [WPOneEngineManager setupConfigAppID:AppId_NS];
}

FString UOneEngineSDKSubsystem::GetAppId()
{
    return FString([WPOneEngineManager getAppId]);
}

void UOneEngineSDKSubsystem::EnableDebugMode(bool Enable)
{
    [WPOneEngineManager enableDebugMode:Enable];
}

bool UOneEngineSDKSubsystem::IsDebugMode()
{
    return [WPOneEngineManager isDebugMode];
}

void UOneEngineSDKSubsystem::OpenUserCenter()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        //初始化
        [WPOneEngineManager enterUserCenter];
    });
}

void UOneEngineSDKSubsystem::GetQRCodeScanResult(FOneGetQRCodeScanResultDelegate OnGetQRCodeScanResultDelegate)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [[OneEngineSDKIOSAdapter GetBridge] launchPCScanLoginCompletion:^(NSString * _Nullable codeType, NSString * _Nullable codeLink) {
            FString CodeType = FString(codeType);
            FString CodeLink = FString(codeLink);
            [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
            {
                OnGetQRCodeScanResultDelegate.ExecuteIfBound(CodeType,CodeLink);     
                return true;
            }];
        }];
    });
}

void UOneEngineSDKSubsystem::OpenComplianceOnWebView()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager openComplianceOnWebView];
    });
}

void UOneEngineSDKSubsystem::EnterAccountCancellation()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager enterAccountCancellation];
    });
}

FString UOneEngineSDKSubsystem::GetChannelId()
{
    return FString::FromInt([WPOneEngineManager channelId]);
}

FString UOneEngineSDKSubsystem::GetChannelMediaId()
{
    return FString([WPOneEngineManager getChannelMediaId]);
}

int32 UOneEngineSDKSubsystem::GetPlatformOS()
{
    return [WPOneEngineManager getPlatformOS];;
}

void UOneEngineSDKSubsystem::GetChannelPlatform(FOneGetPlatformDelegate OnGetPlatformResultDelegate)
{
    auto PlatformInfoResultLambda = [OnGetPlatformResultDelegate](bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)
    {
        OnGetPlatformResultDelegate.ExecuteIfBound(bSucceed,Code,Msg,Platform);
    };
    GetChannelPlatformLambda(PlatformInfoResultLambda);
}

void UOneEngineSDKSubsystem::GetChannelPlatformLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)> &OnFinishedLambda)
{
    TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)> CopiedLambda = OnFinishedLambda;
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager getPlatformWithComplete:^(id responseObject, NSError *error) {
            if (error)
            {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                FString Result = "";
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                {
                    CopiedLambda(false, Code, Msg, Result);
                    return true;
                }];
            }
            else
            {
                FString Result = FString(responseObject);
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                {
                    CopiedLambda(true, 0, "", Result);
                    return true;
                }];
            } 
        }];
    });
}

void UOneEngineSDKSubsystem::GetUserLocationInfo(FOneUserLocationInfoDelegate LocationInfoDelegate)
{
    auto LocationInfoResultLambda = [LocationInfoDelegate](const FOneUserLocationInfo& LocationInfo)
    {
        LocationInfoDelegate.ExecuteIfBound(LocationInfo);
    };
    GetUserLocationInfoLambda(LocationInfoResultLambda);
}

void UOneEngineSDKSubsystem::GetUserLocationInfoLambda(const TFunction<void(const FOneUserLocationInfo& LocationInfo)> &OnFinishedLambda)
{
    TFunction<void(const FOneUserLocationInfo&)> CopiedLambda = OnFinishedLambda;
    dispatch_async(dispatch_get_main_queue(), ^{
            [WPOneEngineManager getUserLocationInfo:^(WPOneEngineEventLocationInfo * _Nonnull result) {
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                {
                    FOneUserLocationInfo LocationInfo;
                    if (result) {
                        LocationInfo.CountryAbbr =  FString(result.countryAbbr);
                        LocationInfo.Country =  FString(result.country);
                        LocationInfo.Province =  FString(result.province);
                        LocationInfo.City =  FString(result.city);
                        LocationInfo.CountryCode =  FString(result.countryCode);
                        LocationInfo.IP =  FString(result.ip);
                    }
                    CopiedLambda(LocationInfo);
                    return true;
                }];
            }];
    });
}

void UOneEngineSDKSubsystem::GetDeviceInfo(FOneGetDeviceInfoDelegate Delegate)
{
    auto GetDeviceInfoResultLambda = [Delegate](const FOneDeviceInfo& DeviceInfo)
    {
        Delegate.ExecuteIfBound(DeviceInfo);
    };
    GetDeviceInfoLambda(GetDeviceInfoResultLambda);
}

void UOneEngineSDKSubsystem::GetDeviceInfoLambda(const TFunction<void(const FOneDeviceInfo& DeviceInfo)> &OnFinishedLambda)
{
    TFunction<void(const FOneDeviceInfo&)> CopiedLambda = OnFinishedLambda;
    dispatch_async(dispatch_get_main_queue(), ^{
        WPOneEngineEventDeviceInfo *deviceInfo = [WPOneEngineManager getDeviceInfo];
        FOneDeviceInfo DeviceInfo;
        DeviceInfo.DeviceId = FString(deviceInfo.deviceId);
        DeviceInfo.DeviceSys = FString(deviceInfo.deviceSys);

        TMap<FString, FString> ExtParams;
        for (NSString *itemKey in deviceInfo.ext)
        {
           NSString *itemValue = deviceInfo.ext[itemKey];
           ExtParams.Add(FString(itemKey), FString(itemValue));
        }
        DeviceInfo.Ext = ExtParams;
        [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
        {
            CopiedLambda(DeviceInfo);
            return true;
        }];
    });
}

void UOneEngineSDKSubsystem::FetchAntiAddictionInfo(FOneFetchAntiAddictionInfoDelegate OnFetchAntiAddictionInfo)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager preLoginWithUserId:@"" completion:^(WPOneEngineFMBaseInfo * _Nullable info, NSError *error) {
            [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
            {
                FOneAntiAddictionInfo FAntiAddictionInfo;
                if (!error && info) {
                    FAntiAddictionInfo.AppID = FString(@(info.appId).stringValue);
                    FAntiAddictionInfo.UserId = FString(info.userId);
                    FAntiAddictionInfo.Status = (int32_t)info.status;
                    FAntiAddictionInfo.HeartbeatInterval = (int32_t)info.heartbeatInterval;
                    FAntiAddictionInfo.BannedType = (int32_t)info.bannedType;
                    FAntiAddictionInfo.BannedReason = FString(info.bannedReason);
                    FAntiAddictionInfo.BreakNotice = FString(info.breakNotice);
                    FAntiAddictionInfo.Realuser = (int32_t)info.realUser;
                    FAntiAddictionInfo.CivicType = (int32_t)info.civicType;
                    FAntiAddictionInfo.Age = (int32_t)info.age;
                    FAntiAddictionInfo.Gender = (int32_t)info.gender;
                    FAntiAddictionInfo.AccountType = (int32_t)info.accountType;
                    FAntiAddictionInfo.RequestIp = FString(info.requestIp);
                    FAntiAddictionInfo.DayOnlineDuration = (int32_t)info.dayOnlineDuration;
                }
                
                OnFetchAntiAddictionInfo.ExecuteIfBound(FAntiAddictionInfo);
                return true;
            }];
        }];
    });
}

void UOneEngineSDKSubsystem::StartAntiAddictionNotify(const FString &ServerId, const FString &RoleId)
{
    NSString* ServerId_ns = ServerId.GetNSString();
    NSString* RoleId_ns = RoleId.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager roleLoginWithUserId:@""
                                       serverId:[ServerId_ns integerValue]
                                         roleId:RoleId_ns
                                     completion:^(WPOneEngineFMBaseInfo * _Nullable info, NSError *error) {
            if (info.status != 0) {
                // 会触发通知回调，回调里会处理
            } else {
                // do nothing
            }
        }];
    });
}

void UOneEngineSDKSubsystem::StopAntiAddictionNotify()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager roleLogout];
    });
}

void UOneEngineSDKSubsystem::SetShowDefaultActivationResultToast(bool bShow)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager setShowActivationResultToast:bShow];
    });
}

void UOneEngineSDKSubsystem::DisplayCDKeyDialog(FOneGenericResultDelegate OnCDKeyActivateResult, const FString &ServerId)
{
    NSString* ServerId_ns = ServerId.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [[OneEngineSDKIOSAdapter GetBridge] showActCodeViewWithServerId:ServerId_ns completion:^(NSInteger code, NSString *message) {
            int32 Code = code;
            bool bSucceed = (code == 0) ? true : false;
            FString Msg = FString(message);
            [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
            {
                OnCDKeyActivateResult.ExecuteIfBound(bSucceed,Code, Msg);
                return true;
            }];
        }];
    });
}

void UOneEngineSDKSubsystem::QueryActCode(const FString& ServerId, FOneQueryActCodeResultDelegate OnQueryActCodeResultDelegate)
{
    auto OnQueryActCodeResultLambda = [OnQueryActCodeResultDelegate](bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)
    {
        OnQueryActCodeResultDelegate.ExecuteIfBound(bSucceed, bNeedActCode, ActCodePrompt, Code, ErrorMsg);
    };
    QueryActCodeLambda(ServerId,OnQueryActCodeResultLambda);
}

void UOneEngineSDKSubsystem::QueryActCodeLambda(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> &OnFinishedLambda)
{
    TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> CopiedLambda = OnFinishedLambda;
    NSString* ServerId_ns = ServerId.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager queryActCodeWithServerId:ServerId_ns
                                          completion:^(NSDictionary *responseObject, NSError *error){
            if (error)
            {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                FString Tips = "";
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                {
                    CopiedLambda(false, false, Tips, Code, Msg);
                    return true;
                }];
            }
            else
            {
                bool isShowActCode = [[responseObject valueForKey:@"isShowActCode"] boolValue];
                NSString *tips_ns = [responseObject valueForKey:@"tips"];
                FString Tips = FString(tips_ns);
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                {
                    CopiedLambda(true, isShowActCode, Tips, 0, "");
                    return true;
                }];
            }
        }];
    });
}

void UOneEngineSDKSubsystem::QueryUserActiveQualification(const FString& ServerId, FOneQueryUserActiveQualificationResultDelegate OneQueryUserActiveQualificationResultDelegate)
{
    auto OnQueryUserActiveQualificationResultLambda = [OneQueryUserActiveQualificationResultDelegate](bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)
    {
        OneQueryUserActiveQualificationResultDelegate.ExecuteIfBound(bSucceed, Code, ErrorMsg, QualificationInfo);
    };
    QueryUserActiveQualificationLambda(ServerId,OnQueryUserActiveQualificationResultLambda);
}

void UOneEngineSDKSubsystem::QueryUserActiveQualificationLambda(const FString& ServerId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)> &OnFinishedLambda)
{
    TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)> CopiedLambda = OnFinishedLambda;
    NSString* ServerId_ns = ServerId.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager queryUserActiveQualificationWithServerId:ServerId_ns
                                                complete:^(WPOneEngineQualificationInfo *info, NSError *error){
            FOneActiveQualificationInfo ActiveQualificationInfo;
            if (error)
            {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                {
                    CopiedLambda(false, Code, Msg, ActiveQualificationInfo);
                    return true;
                }];
            }
            else
            {
                ActiveQualificationInfo.Status = (int32_t)info.status;
                ActiveQualificationInfo.DeviceTotal = (int32_t)info.deviceTotal;
                ActiveQualificationInfo.WhiteList = (int32_t)info.whiteList;
                ActiveQualificationInfo.DeviceLogged = (int32_t)info.deviceLogged;
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                {
                    CopiedLambda(true, 0, "",ActiveQualificationInfo);
                    return true;
                }];
            }
        }];
    });
}

void UOneEngineSDKSubsystem::ActivateDevice(const FString& ServerId, FOneActivateDeviceResultDelegate OneActivateDeviceResultDelegate)
{
    auto OnActivateDeviceResultLambda = [OneActivateDeviceResultDelegate](bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)
    {
        OneActivateDeviceResultDelegate.ExecuteIfBound(bSucceed, bNeedActCode, ActCodePrompt, Code, ErrorMsg);
    };
    ActivateDeviceLambda(ServerId,OnActivateDeviceResultLambda);
}

void UOneEngineSDKSubsystem::ActivateDeviceLambda(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> &OnFinishedLambda)
{
    TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> CopiedLambda = OnFinishedLambda;
    NSString* ServerId_ns = ServerId.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager activateDeviceWithServerId:ServerId_ns
                                            complete:^(NSDictionary *responseObject, NSError *error){
            if (error)
            {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                FString Tips = "";
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                {
                    CopiedLambda(false, false, Tips, Code, Msg);
                    return true;
                }];
            }
            else
            {
                bool isShowActCode = [[responseObject valueForKey:@"isShowActCode"] boolValue];
                NSString *tips_ns = [responseObject valueForKey:@"tips"];
                FString Tips = FString(tips_ns);
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                {
                    CopiedLambda(true, isShowActCode, Tips, 0, "");
                    return true;
                }];
            }
        }];
    });
}

void UOneEngineSDKSubsystem::ExchangeActCode(const FString& ServerId, const FString& ActCode, FOneGenericResultDelegate GenericResultDelegate)
{
    auto ExchangeActCodeResultLambda = [GenericResultDelegate](bool bSuccess, int32 Code, const FString& Msg)
    {
        GenericResultDelegate.ExecuteIfBound(bSuccess, Code, Msg);
    };
    ExchangeActCodeLambda(ServerId,ActCode, ExchangeActCodeResultLambda);
}

void UOneEngineSDKSubsystem::ExchangeActCodeLambda(const FString& ServerId, const FString& ActCode, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> &OnFinishedLambda)
{
    TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> CopiedLambda = OnFinishedLambda;
    NSString* ServerId_ns = ServerId.GetNSString();
    NSString* ActCode_ns = ActCode.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager exchangeActCodeWithServerId:ServerId_ns
                                                actCode:ActCode_ns
                                             completion:^(NSDictionary *responseObject, NSError *error){
            if (error)
            {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                {
                    CopiedLambda(false, Code, Msg);
                    return true;
                }];
            }
            else
            {
                int Code = [[responseObject objectForKey:@"code"] intValue];
                NSString *msg = [responseObject objectForKey:@"message"] ?: @"激活成功";
                FString Msg = FString(msg);
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                {
                    CopiedLambda(true, Code, Msg);
                    return true;
                }];
            }
        }];
    });
}

void UOneEngineSDKSubsystem::RedeemCouponCode(const FString &CouponCode,  const FString &ServerId, const FString &RoleId, const FString &RoleLevel, const FString &VipLevel, const TMap<FString, FString> &ExtraInfo, FOneGenericResultDelegate OnRedeemCouponResult)
{
    auto OnRedeemCouponLambda = [OnRedeemCouponResult](bool bSuccess, int32 Code, const FString& Msg)
    {
        OnRedeemCouponResult.ExecuteIfBound(bSuccess, Code, Msg);
    };
    RedeemCouponCodeLambda(CouponCode, ServerId, RoleId, RoleLevel, VipLevel, ExtraInfo, OnRedeemCouponLambda);
}

void UOneEngineSDKSubsystem::RedeemCouponCodeLambda(const FString& CouponCode, const FString& ServerId, const FString& RoleId, const FString& RoleLevel, const FString& VipLevel, const TMap<FString, FString>& ExtraInfo,  const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> &OnFinishedLambda)
{
    TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> CopiedLambda = OnFinishedLambda;
    NSString* CouponCode_ns = CouponCode.GetNSString();
    NSString* ServerId_ns = ServerId.GetNSString();
    NSString* RoleId_ns = RoleId.GetNSString();
    NSString* RoleLevel_ns = RoleLevel.GetNSString();
    NSString* VipLevel_ns = VipLevel.GetNSString();
    NSMutableDictionary* ExtraInfo_ns = [[NSMutableDictionary alloc]initWithCapacity:ExtraInfo.Num()];
    for (auto& KVP : ExtraInfo)
    {
        NSString* field = KVP.Key.GetNSString();
        NSString* value = KVP.Value.GetNSString();
        [ExtraInfo_ns setObject : value forKey : field];
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [[OneEngineSDKIOSAdapter GetBridge] verifyRedeemCodeWithRoleId:RoleId_ns
                                                              serverId:ServerId_ns
                                                                 level:[RoleLevel_ns integerValue]
                                                                   vip:[VipLevel_ns integerValue]
                                                            redeemCode:CouponCode_ns
                                                             extraInfo:ExtraInfo_ns
                                                            completion:^(NSInteger code, NSString *message) {
            int32 Code = code;
            FString Msg = FString(message);
            bool bSucceed = (code == 0) ? true : false;     
            [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
            {
                CopiedLambda(bSucceed, Code, Msg);
                return true;
            }];
        }];
    });
}

void UOneEngineSDKSubsystem::FetchUserRoleInfoList(FOneFetchUserRoleInfoListDelegate OnFetchUserRoleListDelegate, const FString& ServerId)
{
    auto OnFetchUserRoleListLamdba = [OnFetchUserRoleListDelegate](bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)
    {
        OnFetchUserRoleListDelegate.ExecuteIfBound(bSucceed, Code, Msg, RoleList);
    };
    FetchUserRoleInfoListLambda(OnFetchUserRoleListLamdba,ServerId);
}

void UOneEngineSDKSubsystem::FetchUserRoleInfoListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)> &OnFinishedLambda, const FString& ServerId)
{
    TFunction<void(bool, int32, const FString&,  const  TArray<FOneURCRoleInfo>&)> CopiedLambda = OnFinishedLambda;
    NSString* ServerId_ns = ServerId.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager getUserRoleInfoListWithServerId:ServerId_ns completion:^(NSArray<WPOneEngineRoleInfo *> *roleInfoList, NSError *error) {
            
            if (!error) {
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                {
                    TArray<FOneURCRoleInfo> RoleInfoArray;
                    for (WPOneEngineRoleInfo *model in roleInfoList) {
                        FOneURCRoleInfo RoleItem;
                        RoleItem.UserId = FString(model.userId);
                        RoleItem.RoleId = FString(model.roleId);
                        RoleItem.RoleName = FString(model.roleName);
                        RoleItem.Level = FString(@(model.level).stringValue);
                        RoleItem.ServerId = FString(model.serverId);
                        RoleItem.ServerName = FString(model.serverName);
                        RoleItem.Gender = FString(@(model.gender).stringValue);
                        RoleItem.Occupation = FString(@(model.occupation).stringValue);
                        RoleItem.LastLoginTime = FString(model.lastLoginTime);
                        RoleInfoArray.Emplace(RoleItem);
                    }
                    CopiedLambda(true, 0, "",RoleInfoArray);
                    return true;
                }];
            } else {
                int32 Code = error.code;
                FString ErrorMsg = FString(error.localizedDescription);
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                {
                    TArray<FOneURCRoleInfo> RoleInfoArray;
                    CopiedLambda(false, Code, ErrorMsg,RoleInfoArray);
                    return true;
                }];
            }
        }];
    });
}

void UOneEngineSDKSubsystem::Bind(FOneBindResultDelegate StartBindPhoneDelegate,EOneEngineThirdType BindType)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager startBindWithType:static_cast<WPOneEnginePlatformType>(BindType) completion:^(WPOneEnginePlatformType bindType, id  _Nullable responseObject, NSError * _Nullable error) {
            int32 Code = error.code;
            FString Msg = FString(error.localizedDescription);
            bool bSucceed = (Code == 0) ? true : false;
            [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
             {
                FOneUserInfo UserInfo = GetUserInfo();
                StartBindPhoneDelegate.ExecuteIfBound(bSucceed, Code, Msg, BindType, UserInfo);
                return true;
            }];
        }];
    });
}

void UOneEngineSDKSubsystem::UserAuthentication(FOneUserAuthenticationResultDelegate OnUserAuthenticationResultDelegate)
{
    
} 
// 获取声明的权限 可用于展示
TArray<FOnePermissionInfo> UOneEngineSDKSubsystem::GetPermissions()
{
    TArray<FOnePermissionInfo> PermissionInfos;
    return  PermissionInfos;
}

// 检查是否权限已授权
bool UOneEngineSDKSubsystem::CheckSelfPermission(EOnePermissionType Type)
{
    WPOneEngineAppPrivacyType privacyType = OneEnginePrivacyTypeWithPermissionType(Type);
    if (privacyType == -1)
    {
        return false;
    }
    WPOneEngineAppPrivacyInfo *info = [WPOneEngineManager selectPrivacyStatusWithType:privacyType];
    bool status = (info.status == WPOneEngineAppPrivacyAuthStatusConfirmed);
    return status;
}

// 申请权限
void UOneEngineSDKSubsystem::RequestPermission(EOnePermissionType Type,FOneRequestPermissionResultDelegate Delegate)
{
    TArray<FString>  Tips;
    RequestPermission(Type,Delegate,Tips);
}
void UOneEngineSDKSubsystem::RequestPermission(EOnePermissionType Type,FOneRequestPermissionResultDelegate Delegate,const TArray<FString>& Tips)
{
    WPOneEngineAppPrivacyType privacyType = OneEnginePrivacyTypeWithPermissionType(Type);
    if (privacyType == -1)
    {
        [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
        {
            Delegate.ExecuteIfBound(Type,false);
            return true;
        }];
        return ;
    }
    NSString *usageDesc = nil;
    if (Tips.Num() > 0)
    {
        FString FirstTip = Tips[0];
        usageDesc = FirstTip.GetNSString();
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager requestAuthorizationWithType:privacyType
                                               usageDesc:usageDesc
                                              completion:^(WPOneEngineAppPrivacyInfo * _Nonnull info) {
            bool status = false;
            if (info) {
                status = (info.status == WPOneEngineAppPrivacyAuthStatusConfirmed);
            }
            [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
            {
                Delegate.ExecuteIfBound(Type,status);
                return true;
            }];
        }];
    });
}
//  关闭剪切板权限
void UOneEngineSDKSubsystem::CloseClipboardPermission()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager closePrivacyWithType:WPOneEngineAppPrivacyTypePasteboardCopy];
    });
}

// 打开应用权限设置页
void UOneEngineSDKSubsystem::OpenApplicationSetting()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager openSystemPrivacyView];
    });
}

// 打开AIHelp客服
void UOneEngineSDKSubsystem::OpenAIHelp(EOneAIHelpType Type, const FString& RoleId, const FString& ServerId, const FString& RoleName)
{
    NSString* RoleId_ios = RoleId.GetNSString();
    NSString* ServerId_ios = ServerId.GetNSString();
    NSString* RoleName_ios = RoleName.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^ {
        [WPOneEngineManager openAIHelpWithType:(WPOneEngineAIHelpServiceType)Type
                                       roleId : RoleId_ios
                                     roleName : RoleName_ios
                                     serverId : ServerId_ios
        ];
    });
}

// 打开自定义客服
void UOneEngineSDKSubsystem::OpenCustomerService(const FString& RoleID, const FString& RoleName, const FString& ServerID)
{
    NSString *RoleId_ios = RoleID.GetNSString();
    NSString *RoleName_ios = RoleName.GetNSString();
    NSString *ServerID_ios = ServerID.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager openCustomerServiceWithRoleId:RoleId_ios roleName:RoleName_ios serverId:ServerID_ios];
    });
}

// 文本翻译
void UOneEngineSDKSubsystem::TranslateLambda(const FString& Text,const TFunction<void(bool bSucceed, const FString& Result,const FString& ErrorMsg)> &OnFinishedLambda)
{
    TFunction<void(bool, const FString&, const FString&)> CopiedLambda = OnFinishedLambda;
    NSString* Text_ns = Text.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager translateBySdk:Text_ns
                                completion: ^ (NSString * translated, NSError * error) {
            if (error) {
                FString Msg = error.localizedDescription;
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    CopiedLambda(false, "", Msg);
                    return true;
                }];
                
            }
            else {
                FString Result = FString(translated);
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    CopiedLambda(true, Result, "");
                    return true;
                }];
            }
        }];
    });
}

void UOneEngineSDKSubsystem::Translate(const FString& Text, FOneTranslateResultDelegate Callback)
{
    auto TranslateResultLambda = [Callback](bool bSucceed, const FString& Result,const FString& ErrorMsg)
    {
        Callback.ExecuteIfBound(bSucceed, Result, ErrorMsg);
    };
    TranslateLambda(Text, TranslateResultLambda);
}

// 获取当前设置语言
FString UOneEngineSDKSubsystem::GetCurrentLanguage()
{
	return "";
}

// 设置语言
void UOneEngineSDKSubsystem::SetLanguage(const FString& Code)
{
    [WPOneEngineManager setLanguage:Code.GetNSString()];
}

// 获取本地语言列表
TArray<FString> UOneEngineSDKSubsystem::GetSupportedLanguageCodeList()
{
    TArray<FString> Result;
    NSArray<NSString*>* LanguageList = [WPOneEngineManager getLocalLanguages];
    for (NSString* code in LanguageList) {
        Result.Emplace(code);
    }
    return Result;
}

// 设置屏幕方向
void UOneEngineSDKSubsystem::SetScreenOrientation(EOneScreenOrientation Orientation)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager setScreenOrientation:(WPOneEngineOrientationMask)Orientation];
    });
}

//获取审核开关(iOS专有接口)
bool UOneEngineSDKSubsystem::ExaminStatus()
{
    return [WPOneEngineManager examinStatus];
}
//是否检测BundleId(iOS专有接口)
void UOneEngineSDKSubsystem::ShouldVerifyBundleId(bool bShouldVerify)
{
    [WPOneEngineManager shouldVerifyBundleId:bShouldVerify];
}

//万能方法接口
bool UOneEngineSDKSubsystem::IsCommonFunctionSupported(const FString& FuncName)
{
    return false;
}

void UOneEngineSDKSubsystem::CallCommonFunction(const FString& FuncName, const FString& Params, FOneCommonFunctionDelegate CommonFunctionDelegate)
{
    
}

// 分享SDK
void UOneEngineSDKSubsystem::ShareDataToApp(EOneShareAppTarget AppTarget, const EOneShareType Type, const FOneShareData& Data, FOneGenericResultDelegate OnShareResult)
{
    WPOneEngineShareContentType contentType = WPOneEngineShareContentTypeText;
    WPOneEngineShareInfo* messageObject;
    if (AppTarget == EOneShareAppTarget::PE_Weibo) {
        messageObject = [[WPOneEngineSinaWeiboShareInfo alloc] init];
        
        if(!Data.SinaSuperGroup.SuperGroup.IsEmpty()) {
            NSMutableDictionary* ExtraInfo_ns = [[NSMutableDictionary alloc]initWithCapacity:Data.SinaSuperGroup.ExtraInfo.Num()];
            for (auto& KVP : Data.SinaSuperGroup.ExtraInfo)
            {
                NSString* field = KVP.Key.GetNSString();
                NSString* value = KVP.Value.GetNSString();
                [ExtraInfo_ns setObject : value forKey : field];
            }
            
            ((WPOneEngineSinaWeiboShareInfo *)messageObject).superGroup = Data.SinaSuperGroup.SuperGroup.GetNSString();;
            ((WPOneEngineSinaWeiboShareInfo *)messageObject).section = Data.SinaSuperGroup.Section.GetNSString();;
            ((WPOneEngineSinaWeiboShareInfo *)messageObject).extData = ExtraInfo_ns;
        }
    } else {
        messageObject = [[WPOneEngineShareInfo alloc] init];
    }
    
    messageObject.title =  Data.Title.GetNSString();
    messageObject.content = Data.Content.GetNSString();
    messageObject.topicId =  Data.TopicId.GetNSString();
    
    if (Type == EOneShareType::Text) {
        // 不需要再单独设置分享内容
        contentType = WPOneEngineShareContentTypeText;
    }
    else if (Type == EOneShareType::Image || Type == EOneShareType::ImageSnapShot) {
        if (Type == EOneShareType::ImageSnapShot)
        {
            contentType = WPOneEngineShareContentTypeImageSnapshot;
        }
        else
        {
            contentType = WPOneEngineShareContentTypeImage;
        }
        
        UIImage *thumbnail = nil;
        if (Data.Thumbnail) {
            thumbnail = OneEngineUIImageFromUTexture2D(Data.Thumbnail);
        }
        UIImage *shareImage = nil;
        if (Data.Image) {
            shareImage = OneEngineUIImageFromUTexture2D(Data.Image);
        }
        NSString *netImageUrl = Data.NetImageUrl.GetNSString();
        NSString *localImagePath = Data.LocalImagePath.GetNSString();
        
        if (shareImage) {
            messageObject.image = shareImage;
        } else if (localImagePath.length > 0) {
            UIImage *localImage = [UIImage imageWithContentsOfFile:localImagePath];
            if (localImage) {
                messageObject.image = localImage;
            }
        } else if(netImageUrl.length > 0){
            messageObject.picUrl = netImageUrl;
        } else {
            messageObject.image = thumbnail;
        }
    } else if (Type == EOneShareType::WebPage) {
        contentType = WPOneEngineShareContentTypeWebpage;
        messageObject.link = Data.WebPageUrl.GetNSString();
    } else {
        [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
         {
            OnShareResult.ExecuteIfBound(false,-1, FString(@"不支持的分享类型"));
            return true;
        }];
        return;
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager shareToPlatform:OneEngineSocialTypeFromShareAppTarget(AppTarget)
                                messageType:contentType
                              messageObject:messageObject
                                 completion: ^ (id result, NSError * error) {
            if (error) {
                int32 Code = error.code;
                FString Msg = error.localizedDescription;
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    OnShareResult.ExecuteIfBound(false,Code, Msg);
                    return true;
                }];
            }
            else {
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    OnShareResult.ExecuteIfBound(true,0, "");
                    return true;
                }];
            }
        }];
    });
}

// 推送相关接口
// 注册推送
void UOneEngineSDKSubsystem::StartUpdatePushData(FOneStartUpdatePushDataDelegate Callback)
{
    StartUpdatePushDataDelegate = Callback;
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager startUpdatePushDataWithCompletion:^(NSDictionary * _Nullable responseObject, NSError * _Nullable error){
            if (error) {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    StartUpdatePushDataDelegate.ExecuteIfBound(false, Code, Msg, "");
                    return true;
                }];
            } else {
                NSString *deviceToken = @"";
                if([responseObject isKindOfClass:[NSDictionary class]])
                {
                    deviceToken = responseObject[@"deviceToken"];
                }
                FString DeviceToken = FString(deviceToken);
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    StartUpdatePushDataDelegate.ExecuteIfBound(true, 0, "", DeviceToken);
                    return true;
                }];
            }
        }];
    });
}

// 设置点击推送消息后的回调
void UOneEngineSDKSubsystem::SetupNotificationCallback(FOneNotificationDelegate Callback)
{
    NotificationDelegate = Callback;
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager setupV2NotificationCallbackWithCompletion:^(NSDictionary *userInfo, BOOL isActive)
         {
            
            NSError *error;
            NSData *jsonData = [NSJSONSerialization dataWithJSONObject:userInfo
                                                               options:NSJSONWritingPrettyPrinted
                                                                 error:&error];
            if (!jsonData) {
                NSLog(@"PushSDK-FNotificationDelegate: Got an json error: %@", error);
            } else {
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    FOnePushMessage Message;
                    NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
                    FString JsonString = FString(jsonString);
                    FJsonObjectWrapper Wrapper;
                    Wrapper.JsonObjectFromString(JsonString);
                    auto& JsonObj = Wrapper.JsonObject;
                    auto& ApsJsonObj = JsonObj->GetObjectField(TEXT("aps"));
                    auto& AlertJsonObj = ApsJsonObj->GetObjectField(TEXT("alert"));
                    bool bIsActive = isActive;
                    Message.Title=AlertJsonObj->GetStringField(TEXT("title"));
                    Message.Content=AlertJsonObj->GetStringField(TEXT("body"));
                    Message.MessageId=JsonObj->GetStringField(TEXT("msgId"));
                    Message.Ext=JsonObj->GetStringField(TEXT("extension"));
                    Message.bIsActive = bIsActive;
                    
                    NotificationDelegate.ExecuteIfBound(Message);
                    return true;
                }];
            }
        }];
    });
}

// 获取系统推送开关状态 和 应用推送开关状态
void UOneEngineSDKSubsystem::GetPushStatus(FOnePushStatusDelegate Callback)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager getPushStatusWithCompletion:^(UIUserNotificationType notificationType, BOOL providerPushState)
         {
            FOnePushStatus Result;
            Result.bIsAppOpen = providerPushState;
            if(notificationType != UIUserNotificationTypeNone){
                Result.bIsSysOpen = true;
            }else{
                Result.bIsSysOpen = false;
            }
            [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
             {
                Callback.ExecuteIfBound(Result);
                return true;
            }];
        }];
    });
}

// 设置应用推送开关状态
void UOneEngineSDKSubsystem::SetProviderPushState(bool bProviderPushState, FOneGenericResultDelegate Callback)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager setProviderPushState:bProviderPushState completion:^(id responseObject, NSError *error)
        {
            bool bSucceed = error ? NO : YES;
            [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
             {
                if(bSucceed){
                    Callback.ExecuteIfBound(true, 0, "");
                }else{
                    Callback.ExecuteIfBound(false, -1, "SetProviderPushState Failed");
                }
                return true;
            }];
        }];
    });
}

void UOneEngineSDKSubsystem::GetProviderPushState(FOnGetPushStateDelegate Delegate)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager getPushNotificationEnabledWithCompletion:^(BOOL status, NSError *error ) {
            if (error) {
                 int32 Code = error.code;
                 FString Msg = FString(error.localizedDescription);
                 [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    Delegate.ExecuteIfBound(false, Code, Msg, status);
                    return true;
                 }];
            } else {
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                {
                   Delegate.ExecuteIfBound(true, 0, "", status);
                   return true;
                }];
            }
        }];
    });
}


// 推送设置用户信息
void UOneEngineSDKSubsystem::SetPushUserInfo(const FString& ServerId, const FString& RoleId, FOneGenericResultDelegate Callback)
{
    NSString* ServerId_ns = ServerId.GetNSString();
    NSString* RoleId_ns = RoleId.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager setUserId:@""
                             serverId:ServerId_ns
                               roleId:RoleId_ns
                           completion:^(id responseObject, NSError *error)
             {
                bool success = [responseObject isKindOfClass:[NSNumber class]] ? [responseObject boolValue] : false;
                if (!success || error) {
                    int32 Code = error.code;
                    FString Msg = FString(error.localizedDescription);
                    [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                     {
                        Callback.ExecuteIfBound(false, Code, Msg);
                        return true;
                    }];
                } else {
                    [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                     {
                        Callback.ExecuteIfBound(true, 0, "");
                        return true;
                    }];
                }
            }
        ];
    });
}

// 推送解绑用户信息
void UOneEngineSDKSubsystem::UnSetPushUserInfo(FOneGenericResultDelegate Callback)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager unsetUserInfoWithCompletion:^(id responseObject, NSError *error)
             {
                bool success = [responseObject isKindOfClass:[NSNumber class]] ? [responseObject boolValue] : false;
                if (!success || error) {
                    int32 Code = error.code;
                    FString Msg = FString(error.localizedDescription);
                    [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                     {
                        Callback.ExecuteIfBound(false, Code, Msg);
                        return true;
                    }];
                } else {
                    [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                     {
                        Callback.ExecuteIfBound(true, 0, "");
                        return true;
                    }];
                }
            }
        ];
    });
}

// 获取推送类型列表
void UOneEngineSDKSubsystem::GetPushTypeInfoList(FOneGetPushTypeInfoListDelegate Callback)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager getPushTypeInfoList:^(NSArray<WPOneEnginePushTypeInfo *> * _Nullable pushTasks, NSError * _Nullable error)
        {
            if (error) {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    TArray<FOnePushTypeInfo> Result;
                    Callback.ExecuteIfBound(false, Code, Msg, Result);
                    return true;
                }];
            } else {
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    TArray<FOnePushTypeInfo> Result;
                    for (WPOneEnginePushTypeInfo *i in pushTasks)
                    {
                        FOnePushTypeInfo item;
                        item.Name = FString(i.name);
                        item.bOpen = i.isOpen;
                        item.PushType = (int)i.pushType;
                        Result.Add(item);
                    }
                    Callback.ExecuteIfBound(true, 0, "", Result);
                    return true;
                }];
            }
        }];
    });
}

// 批量设置推送类型开关状态
void UOneEngineSDKSubsystem::UpdatePushTypeList(const TArray<FOnePushTypeInfo>& PushTypeList, FOneGenericResultDelegate Callback)
{
    NSMutableArray* array = [[NSMutableArray alloc] init];
    for (auto item : PushTypeList)
    {
        WPOneEnginePushTypeInfo *i = [[WPOneEnginePushTypeInfo alloc] init];
        NSString *Name_NS = item.Name.GetNSString();
        i.isOpen = item.bOpen;
        i.name = Name_NS;
        i.pushType = item.PushType;
        [array addObject:i];
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager updatePushTypeList:array completion:^(id responseObject, NSError *error)
         {
            bool success = [responseObject isKindOfClass:[NSNumber class]] ? [responseObject boolValue] : false;
            if (!success || error) {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    Callback.ExecuteIfBound(false, Code, Msg);
                    return true;
                }];
                
            } else {
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    Callback.ExecuteIfBound(true, 0, "");
                    return true;
                }];
            }
        }];
    });
}

// 夜间勿扰配置的回调
void UOneEngineSDKSubsystem::GetPushNotDisturb(FOnePushNotDisturbInfoDelegate Callback)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager getPushNotDisturb:^(WPOneEnginePushNotDisturbInfo * _Nullable notDisturbInfo, NSError * _Nullable error)
         {
            
            if (error || !notDisturbInfo) {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    FOnePushNotDisturbInfo DisturbInfo;
                    Callback.ExecuteIfBound(false, Code, Msg,DisturbInfo);
                    return true;
                }];
            } else {
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    FOnePushNotDisturbInfo DisturbInfo;
                    DisturbInfo.bNotDisturb = notDisturbInfo.notDisturb;
                    DisturbInfo.NotDisturbStartTime = FString(notDisturbInfo.notDisturbStartTime);
                    DisturbInfo.NotDisturbEndTime = FString(notDisturbInfo.notDisturbEndTime);
                    
                    Callback.ExecuteIfBound(true, 0, "",DisturbInfo);
                    return true;
                }];
            }
        }];
    });
}

// 设置勿扰模式
void UOneEngineSDKSubsystem::UpdatePushNotDisturb(const FOnePushNotDisturbInfo& NotDisturbInfo, FOneGenericResultDelegate Callback)
{
    WPOneEnginePushNotDisturbInfo *notDisturbInfo = [[WPOneEnginePushNotDisturbInfo alloc] init];
    notDisturbInfo.notDisturb = NotDisturbInfo.bNotDisturb;
    notDisturbInfo.notDisturbStartTime = NotDisturbInfo.NotDisturbStartTime.GetNSString();;
    notDisturbInfo.notDisturbEndTime = NotDisturbInfo.NotDisturbEndTime.GetNSString();;

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager updatePushNotDisturb:notDisturbInfo completion:^(id responseObject, NSError *error)
         {
            bool success = [responseObject isKindOfClass:[NSNumber class]] ? [responseObject boolValue] : false;
            if (!success || error) {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    Callback.ExecuteIfBound(false, Code, Msg);
                    return true;
                }];
            } else {
                [FIOSAsyncTask CreateTaskWithBlock : ^ bool(void)
                 {
                    Callback.ExecuteIfBound(true, 0, "");
                    return true;
                }];
            }
        }];
    });
}
void UOneEngineSDKSubsystem::KillProcess()
{
	
}

bool UOneEngineSDKSubsystem::ACELogin(const FString& accountId, int accountTypeCommandId, int worldId, FOneOnGetClientPacket delegate)
{
    return false;
}

bool UOneEngineSDKSubsystem::ACEClientPacketReceive(const TArray<uint8>& data)
{
    return false;
}

bool UOneEngineSDKSubsystem::ACELogout()
{
    return false;
}
void UOneEngineSDKSubsystem::GetIpInfo(const FString& Ip,FOnGetIpInfoResultDelegate Delegate)
{
    NSString* IP_ns = Ip.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager getLocationInfoWithIp:IP_ns completion:^(WPOneEngineIpLocationInfo * _Nullable locationInfo, NSError * _Nullable error) {
            FUserIpInfo IPLocationInfo;
            if(locationInfo && !error) {
                IPLocationInfo.Attribution = FString(locationInfo.attribution);
                IPLocationInfo.CountryCode = FString(locationInfo.countryCode);
                IPLocationInfo.Country = FString(locationInfo.country);
                IPLocationInfo.Region = FString(locationInfo.region);
                IPLocationInfo.CityCode = FString(locationInfo.cityCode);
                IPLocationInfo.City = FString(locationInfo.city);
                AsyncTask(ENamedThreads::GameThread, [Delegate, IPLocationInfo]()
                {
                      Delegate.ExecuteIfBound(true, IPLocationInfo, 0, "");
                });
            } else {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                AsyncTask(ENamedThreads::GameThread, [Delegate, Code, Msg, IPLocationInfo]()
                {
                      Delegate.ExecuteIfBound(false, IPLocationInfo, Code, Msg);
                });
            }
        }];
    });
}

/// 全球独有的 评价接口和 修改屏幕亮度接口
float UOneEngineSDKSubsystem::GetScreenBrightness()
{
    return [WPOneEngineManager screenBrightnessValue];
}

void UOneEngineSDKSubsystem::SetScreenBrightness(float BrightnessValue)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager setScreenBrightness:BrightnessValue];
    });
}
	
void UOneEngineSDKSubsystem::RecoverScreenBrightness()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager recoverScreenBrightness];
    });
}
///保持屏幕常亮
void UOneEngineSDKSubsystem::SwitchScreenPermanentBrightnessState(bool bIsTurnOn)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager switchScreenPermanentBrightnessState:bIsTurnOn];
    });
}
	

///评价接口
void UOneEngineSDKSubsystem::InAppRequestStoreReview(FOneGenericResultDelegate Delegate)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager inappRequestStoreReview];
    });
}

/// AppLink Ios有这个参数
void UOneEngineSDKSubsystem::RequestStoreReview(FString AppLink)
{
    NSString* AppLink_ns = AppLink.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager requestStoreReviewWithAppLink:AppLink_ns];
    });
}

/// 获取渲染器配置文件地址
FString UOneEngineSDKSubsystem::GetRenderConfigFilePath()
{
    return "";
}

// 安全锁相关接口
void UOneEngineSDKSubsystem::UnlockSafeLockUsingPushNotification(const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
}

void UOneEngineSDKSubsystem::UnlockSafeLockUsingDynamicCode(const FString& DynamicCode, const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
}

void UOneEngineSDKSubsystem::StopUnlockSafeLockUsingPushNotification()
{
}

// 全球独有的打开Naver论坛
void UOneEngineSDKSubsystem::OpenCommunityByGame(EOneNaverGameType Type, const FString& Pid, bool Scheduled, FOneLoadDelegate LoadCallback, FOneUnloadDelegate UnloadCallback, FOneInGameMenuDelegate InGameMenuCallback)
{
    NSString *Pid_ios = Pid.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
      [WPOneEngineManager openCommunityByGame:(int)Type
          pid:Pid_ios
          scheduled:Scheduled
          completion:^(NSMutableDictionary *dict,NSError *error) {
              int cbType =  [dict[@"cbType"] integerValue];
              if(cbType == 0)
              {
                  AsyncTask(ENamedThreads::GameThread, [LoadCallback]()
                {
                      LoadCallback.ExecuteIfBound();
                });
              }
              else if(cbType == 1)
              {
                  AsyncTask(ENamedThreads::GameThread, [UnloadCallback]()
                {
                      UnloadCallback.ExecuteIfBound();
                });
              }
              else if(cbType == 2)
              {
                  NSString *input = dict[@"code"];
                  FString Input = FString(input);
                  AsyncTask(ENamedThreads::GameThread, [InGameMenuCallback, Input]()
                {
                      InGameMenuCallback.ExecuteIfBound(Input);
                });
              }
          }];
    });
}

void UOneEngineSDKSubsystem::TerminateCommunity()
{
	dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager terminateCommunity];
	});
}

void UOneEngineSDKSubsystem::SetAnalyticsCollectionEnabled(bool bEnable)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager setAnalyticsCollectionEnabled:bEnable];
    });
}
bool UOneEngineSDKSubsystem::IsInstalledApp(EOneShareAppTarget AppTarget)
{
    return [WPOneEngineManager isInstalledApp:OneEngineSocialTypeFromShareAppTarget(AppTarget)];
}