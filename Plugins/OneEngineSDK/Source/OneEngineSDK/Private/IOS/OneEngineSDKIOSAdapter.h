// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#import <Foundation/Foundation.h>
#import <WPOneEngineBridge/WPOneEngineBridge.h>

// 扫码结果回调
typedef void(^WPOneEngineQRCodeScanResultHandler)(NSString * __nullable codeType,NSString * __nullable codeLink);
// 通用回调
typedef void(^WPOneEngineGenericResultHandler)(NSInteger code,NSString *message);
@interface OneEngineSDKIOSAdapter : NSObject

+ (OneEngineSDKIOSAdapter *)GetBridge;

- (void)registerInitCallBackCompletion:(WPOneEngineGenericResultHandler)completion;

- (void)launchPCScanLoginCompletion:(WPOneEngineQRCodeScanResultHandler)completion;

- (void)showActCodeViewWithServerId:(NSString *)serviceId completion:(WPOneEngineGenericResultHandler)completion;

- (void)verifyRedeemCodeWithRoleId:(NSString *)roleId
						  serverId:(NSString *)serverId
							 level:(NSInteger)level
							   vip:(NSInteger)vip
						redeemCode:(NSString *)redeemCode
						 extraInfo:(NSDictionary *)extraInfo
						completion:(WPOneEngineGenericResultHandler)completion;
@end
