

#if !defined(PLATFORM_OPENHARMONY)
#define PLATFORM_OPENHARMONY 0
#endif
#if PLATFORM_OPENHARMONY

#include <string>
#include "OneEngineSDKSubsystem.h"
#include "aki/jsbind.h"
#include <functional>
#include <map>

#include "ImageUtils.h"
#include "JsonObjectConverter.h"
#include "JsonObjectWrapper.h"
#include "Async/Async.h"
#include "Dom/JsonObject.h"
#include "Misc/FileHelper.h"
#include "Serialization/JsonReader.h"
#include "Serialization/JsonSerializer.h"

void UOneEngineSDKSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
}

void UOneEngineSDKSubsystem::Init(FOneInitDelegate InitDelegate)
{
	int AppId;
	FString AppKey;

	GConfig->GetInt(TEXT("/Script/OneEngineEditor.OneEngineSettings"), TEXT("AppID"), AppId, GGameIni);
	GConfig->GetString(TEXT("/Script/OneEngineEditor.OneEngineSettings"), TEXT("OneAppKey"), AppKey, GGameIni);
	std::string Key(TCHAR_TO_UTF8(*AppKey));

	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.setLoginListener"))
	{
		std::function<void(bool,bool, int, std::string)> loginCb = [](bool success,bool bIsCacel, int code, std::string msg)
		{
			FString Msg(UTF8_TO_TCHAR(msg.c_str()));
			AsyncTask(ENamedThreads::GameThread, [success,code,Msg,bIsCacel]()
			{
				UOneEngineSDKSubsystem* Subsystem = GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
				if(bIsCacel)
				{
					//取消华为登录或one登录
					Subsystem->LoginResultDelegate.Broadcast(success, -10002, Msg, Subsystem->GetUserInfo());
				}else
				{
					Subsystem->LoginResultDelegate.Broadcast(success,code , Msg, Subsystem->GetUserInfo());
				}
			});
		};
		std::function<void(bool, int, std::string)> LogoutCb = [](bool success, int code, std::string msg)
		{
			FString Msg(UTF8_TO_TCHAR(msg.c_str()));
			AsyncTask(ENamedThreads::GameThread, [success,code,Msg]()
			{
				UOneEngineSDKSubsystem* Subsystem = GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();

				Subsystem->LogoutResultDelegate.Broadcast(success, code, Msg);
			});
		};
		jsFunc->Invoke<void>(loginCb,LogoutCb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find setLoginListener method"))
	}
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.init"))
	{
		std::function<void(bool, int, std::string)> cb = [InitDelegate](bool success, int code, std::string msg)
		{
			AsyncTask(ENamedThreads::GameThread, [InitDelegate,success,code,msg]()
			{
				InitDelegate.ExecuteIfBound(success, code, FString(UTF8_TO_TCHAR(msg.c_str())));
			});
		};
		jsFunc->Invoke<void>(AppId, Key, cb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find init method"))
		InitDelegate.ExecuteIfBound(false, -101, TEXT("didn't find init method"));
	}
}

EOneEngineSDKRegionType UOneEngineSDKSubsystem::GetRegionType()
{
	return EOneEngineSDKRegionType::Mainland;
}

void UOneEngineSDKSubsystem::Login()
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.login"))
	{
		jsFunc->Invoke<void>();
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find login method"))
	}
}

void UOneEngineSDKSubsystem::GetUserTokenList(FOneFetchUserTokenListDelegate OnGetTokenListDelegate)
{
	OnGetTokenListDelegate.ExecuteIfBound(false,-101, TEXT("not support getUserTokenList method"), TArray<FOneUserInfo>());
}

void UOneEngineSDKSubsystem::GetUserTokenListLambda(
	const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)>&
	OnFinishedLambda)
{
	OnFinishedLambda(false, -101, TEXT("not support getUserTokenList method"), TArray<FOneUserInfo>());
}

void UOneEngineSDKSubsystem::TokenLogin(const FString& Token, const FString& Uid, EOneEngineThirdType ThirdType)
{
}

void UOneEngineSDKSubsystem::ThirdLogin(EOneEngineThirdType ThirdType, bool bForcedLogin)
{
}

void UOneEngineSDKSubsystem::GuestLogin()
{
}

void UOneEngineSDKSubsystem::SwitchAccount()
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.logout"))
	{
		jsFunc->Invoke<void>();
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find logout method"))
	}
}

FOneUserInfo UOneEngineSDKSubsystem::GetUserInfo()
{
	FOneUserInfo UserInfo;
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getUserInfo"))
	{
		std::string result = jsFunc->Invoke<std::string>();
		FString Result(UTF8_TO_TCHAR(result.c_str()));
		FJsonObjectWrapper Wrapper;
		Wrapper.JsonObjectFromString(Result);
		if (Wrapper.JsonObject.IsValid())
		{
			Wrapper.JsonObject->TryGetStringField("uid", UserInfo.UserId);
			Wrapper.JsonObject->TryGetStringField("token", UserInfo.Token);
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find getUserInfo method"))
	}

	return UserInfo;
}

void UOneEngineSDKSubsystem::Pay(const FOnePaymentInfo& PaymentInfo)
{
	//orderId: string //订单号
	// productId: string //商品id
	// productName: string //商品名字
	// productPrice: number //商品价格
	// // productCount: string //商品数量
	// serverId: string //游戏区服id
	// // serverName: string //游戏区服名称
	// roleId: string //角色id
	// roleName: string //角色名称
	// ext: string //游戏需要one服务器给游戏服务器透传的字段
	FJsonObjectWrapper Wrapper;
	TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
	JsonObj->SetStringField("orderId", PaymentInfo.OrderId);
	JsonObj->SetStringField("productId", PaymentInfo.ProductId);
	JsonObj->SetStringField("productName", PaymentInfo.ProductName);
	JsonObj->SetStringField("productPrice", PaymentInfo.Price);
	JsonObj->SetStringField("productCount", PaymentInfo.ProductCount);
	JsonObj->SetStringField("serverId", PaymentInfo.GameServerId);
	JsonObj->SetStringField("serverName", PaymentInfo.ServerName);
	JsonObj->SetStringField("roleName", PaymentInfo.RoleName);
	JsonObj->SetStringField("roleId", PaymentInfo.RoleId);
	JsonObj->SetStringField("price", PaymentInfo.Price);
	JsonObj->SetStringField("ext", PaymentInfo.ExtInfo);
	Wrapper.JsonObject = JsonObj;
	FString Result;
	Wrapper.JsonObjectToString(Result);
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.pay"))
	{
		std::function<void(bool,bool, int, std::string, std::string)> cb = [
			](bool success,bool bIsCacel, int code, std::string msg, std::string orderId)
		{
			FString Msg(UTF8_TO_TCHAR(msg.c_str()));
			FString OrderId(UTF8_TO_TCHAR(orderId.c_str()));
			AsyncTask(ENamedThreads::GameThread, [success,code,Msg,OrderId,bIsCacel]()
			{
				UOneEngineSDKSubsystem* Subsystem = GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
				if(bIsCacel)
				{
					Subsystem->PayResultDelegate.Broadcast(success, -10002, Msg, "");
				}else
				{
					Subsystem->PayResultDelegate.Broadcast(success, code, Msg, "");
				}
			});
		};
		jsFunc->Invoke<void>(TCHAR_TO_UTF8(*Result), cb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find pay method"))
		UOneEngineSDKSubsystem* Subsystem = GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
		Subsystem->PayResultDelegate.Broadcast(false, -101, TEXT("didn't find pay method"),"");
	}
}

// 角色事件打点
void UOneEngineSDKSubsystem::TrackEventRoleLoginSucceed(const FOneRoleInfo& RoleInfo, const FString& Ip,
                                                        const FString& Port)
{
	//trackEventRoleLogin
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.trackEventRoleLogin"))
	{
		std::map<std::string, std::string> val;
		val.insert({"roleId",TCHAR_TO_UTF8(*RoleInfo.RoleId)});
		val.insert({"roleName",TCHAR_TO_UTF8(*RoleInfo.RoleName)});
		val.insert({"serverId",TCHAR_TO_UTF8(*RoleInfo.ServerId)});
		val.insert({"serverName",TCHAR_TO_UTF8(*RoleInfo.ServerName)});
		val.insert({"vipLevel",TCHAR_TO_UTF8(*RoleInfo.Vip)});
		val.insert({"roleLevel",TCHAR_TO_UTF8(*RoleInfo.Level)});
		val.insert({"ip",TCHAR_TO_UTF8(*Ip)});
		val.insert({"port",TCHAR_TO_UTF8(*Port)});
		jsFunc->Invoke<void>(val);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find trackEventRoleLogin method"))
	}
}

void UOneEngineSDKSubsystem::TrackEventRoleLoginError(const FOneRoleInfo& RoleInfo, const FString& Ip,
                                                      const FString& Port, const FString& Code, const FString& Msg)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.trackEventRoleLoginError"))
	{
		std::map<std::string, std::string> val;
		val.insert({"roleId",TCHAR_TO_UTF8(*RoleInfo.RoleId)});
		val.insert({"roleName",TCHAR_TO_UTF8(*RoleInfo.RoleName)});
		val.insert({"serverId",TCHAR_TO_UTF8(*RoleInfo.ServerId)});
		val.insert({"serverName",TCHAR_TO_UTF8(*RoleInfo.ServerName)});
		val.insert({"vipLevel",TCHAR_TO_UTF8(*RoleInfo.Vip)});
		val.insert({"roleLevel",TCHAR_TO_UTF8(*RoleInfo.Level)});
		val.insert({"ip",TCHAR_TO_UTF8(*Ip)});
		val.insert({"port",TCHAR_TO_UTF8(*Port)});
		jsFunc->Invoke<void>(val);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find trackEventRoleLoginError method"))
	}
}

void UOneEngineSDKSubsystem::TrackEventRoleLogout(const FOneRoleInfo& RoleInfo)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.trackEventRoleLogout"))
	{
		std::map<std::string, std::string> val;
		val.insert({"roleId",TCHAR_TO_UTF8(*RoleInfo.RoleId)});
		val.insert({"roleName",TCHAR_TO_UTF8(*RoleInfo.RoleName)});
		val.insert({"serverId",TCHAR_TO_UTF8(*RoleInfo.ServerId)});
		val.insert({"serverName",TCHAR_TO_UTF8(*RoleInfo.ServerName)});
		val.insert({"vipLevel",TCHAR_TO_UTF8(*RoleInfo.Vip)});
		val.insert({"roleLevel",TCHAR_TO_UTF8(*RoleInfo.Level)});
		jsFunc->Invoke<void>(val);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find trackEventRoleLogout method"))
	}
}

void UOneEngineSDKSubsystem::TrackEventRoleCreate(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
	//trackEventRoleCreate
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.trackEventRoleCreate"))
	{
		//  roleId: string
		// roleName: string
		// serverId: string
		// serverName: string
		// vipLevel: string
		// roleLevel: string
		// ip?: string
		// port?: string
		std::map<std::string, std::string> val;
		val.insert({"roleId",TCHAR_TO_UTF8(*RoleInfo.RoleId)});
		val.insert({"roleName",TCHAR_TO_UTF8(*RoleInfo.RoleName)});
		val.insert({"serverId",TCHAR_TO_UTF8(*RoleInfo.ServerId)});
		val.insert({"serverName",TCHAR_TO_UTF8(*RoleInfo.ServerName)});
		val.insert({"vipLevel",TCHAR_TO_UTF8(*RoleInfo.Vip)});
		val.insert({"roleLevel",TCHAR_TO_UTF8(*RoleInfo.Level)});
		val.insert({"ip",TCHAR_TO_UTF8(*Ip)});
		val.insert({"port",TCHAR_TO_UTF8(*Port)});
		jsFunc->Invoke<void>(val);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find trackEventRoleCreate method"))
	}
}

void UOneEngineSDKSubsystem::TrackEventRoleLevelUp(const FOneRoleInfo& RoleInfo)
{
	//trackEventRoleLevelUp
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.trackEventRoleLevelUp"))
	{
		std::map<std::string, std::string> val;
		val.insert({"roleId",TCHAR_TO_UTF8(*RoleInfo.RoleId)});
		val.insert({"roleName",TCHAR_TO_UTF8(*RoleInfo.RoleName)});
		val.insert({"serverId",TCHAR_TO_UTF8(*RoleInfo.ServerId)});
		val.insert({"serverName",TCHAR_TO_UTF8(*RoleInfo.ServerName)});
		val.insert({"vipLevel",TCHAR_TO_UTF8(*RoleInfo.Vip)});
		val.insert({"roleLevel",TCHAR_TO_UTF8(*RoleInfo.Level)});
		jsFunc->Invoke<void>(val);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find trackEventRoleLevelUp method"))
	}
}

// 资源事件打点
void UOneEngineSDKSubsystem::GameResReqEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode,
                                             const FString& ErrorMsg)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.wanmeiGameResReqEvent"))
	{
		FString Status;
		//  BEGIN = "gameResReqBegin", SUCCESS = "gameResReqSuccess", ERROR = "gameResReqError"
		if (State == EOneResEventState::Begin)
		{
			Status = "gameResReqBegin";
		}
		else if (State == EOneResEventState::Success)
		{
			Status = "gameResReqSuccess";
		}
		else
		{
			Status = "gameResReqError";
		}
		jsFunc->Invoke<void>(TCHAR_TO_UTF8(*Status),TCHAR_TO_UTF8(*Url),TCHAR_TO_UTF8(*ErrorCode),
		                     TCHAR_TO_UTF8(*ErrorMsg));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find wanmeiGameResReqEvent method"))
	}
}

void UOneEngineSDKSubsystem::GameUpdateAssetEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode,
                                                  const FString& ErrorMsg)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.wanmeiGameUpdateAssetEvent"))
	{
		FString Status;
		//    BEGIN = "gameUpdateAssetBegin", SUCCESS = "gameUpdateAssetSuccess", ERROR = "gameUpdateAssetError"
		if (State == EOneResEventState::Begin)
		{
			Status = "gameUpdateAssetBegin";
		}
		else if (State == EOneResEventState::Success)
		{
			Status = "gameUpdateAssetSuccess";
		}
		else
		{
			Status = "gameUpdateAssetError";
		}
		jsFunc->Invoke<void>(TCHAR_TO_UTF8(*Status),TCHAR_TO_UTF8(*Url),TCHAR_TO_UTF8(*ErrorCode),
		                     TCHAR_TO_UTF8(*ErrorMsg));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find wanmeiGameUpdateAssetEvent method"))
	}
}

void UOneEngineSDKSubsystem::GameResDecEvent(EOneResEventState State, const FString& ErrorMsg)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.wanmeiGameResDecEvent"))
	{
		FString Status;
		//     BEGIN = "gameResDecBegin", SUCCESS = "gameResDecSuccess", ERROR = "gameResDecError"
		if (State == EOneResEventState::Begin)
		{
			Status = "gameResDecBegin";
		}
		else if (State == EOneResEventState::Success)
		{
			Status = "gameResDecSuccess";
		}
		else
		{
			Status = "gameResDecError";
		}
		jsFunc->Invoke<void>(TCHAR_TO_UTF8(*Status),TCHAR_TO_UTF8(*ErrorMsg));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find wanmeiGameResDecEvent method"))
	}
}

void UOneEngineSDKSubsystem::GameGetServerListEvent(EOneResEventState State, const FString& Url,
                                                    const FString& ErrorCode, const FString& ErrorMsg)
{
	//wanmeiGameGetServerListEvent
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.wanmeiGameGetServerListEvent"))
	{
		FString Status;
		//     BEGIN = "gameGetServerListBegin", SUCCESS = "gameGetServerListSuccess", ERROR = "gameGetServerListError"
		if (State == EOneResEventState::Begin)
		{
			Status = "gameGetServerListBegin";
		}
		else if (State == EOneResEventState::Success)
		{
			Status = "gameGetServerListSuccess";
		}
		else
		{
			Status = "gameGetServerListError";
		}
		jsFunc->Invoke<void>(TCHAR_TO_UTF8(*Status),TCHAR_TO_UTF8(*ErrorMsg));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find wanmeiGameGetServerListEvent method"))
	}
}

// 自定义事件打点
void UOneEngineSDKSubsystem::TrackEvent(const FString& Name, const TMap<FString, FString>& Payload)
{
	//wanmeiTrackEvent
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.wanmeiTrackEvent"))
	{
		std::map<std::string, std::string> val;
		// val.Set("name", "aki"); // {'name': 'aki'};
		for (const auto& Pair : Payload)
		{
			val.insert({TCHAR_TO_UTF8(*Pair.Key), TCHAR_TO_UTF8(*Pair.Value)});
		}
		jsFunc->Invoke<void>(TCHAR_TO_UTF8(*Name), val);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find wanmeiTrackEvent method"))
	}
}

void UOneEngineSDKSubsystem::TrackEventAD(const FString& Name, const TMap<FString, FString>& Payload)
{
}

//进入游戏场景打点
void UOneEngineSDKSubsystem::TrackEventEnterGameScene(const FString& SceneName, int32 Period,
                                                      const TMap<FString, FString>& HintMap)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.trackEnterGameScene"))
	{
		std::map<std::string, std::string> params;
		for (const TPair<FString, FString>& Pair : HintMap)
		{
			params.insert(std::make_pair(TCHAR_TO_UTF8(*Pair.Key), TCHAR_TO_UTF8(*Pair.Value)));
		}
		jsFunc->Invoke<void>(TCHAR_TO_UTF8(*SceneName),Period,params);
	}else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find trackEnterGameScene method"))
	}
}

//退出游戏打点
void UOneEngineSDKSubsystem::TrackEventExitGameScene()
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.trackEventExitGameScene"))
	{
		jsFunc->Invoke<void>();
	}else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find trackEventExitGameScene method"))
	}
}

//设备打点额外信息
void UOneEngineSDKSubsystem::TrackEventAddExtraDeviceInfo(const TMap<FString, FString>& ExtraDeviceInfo)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.trackEventAddExtraDeviceInfo"))
	{
		std::map<std::string, std::string> params;
		for (const TPair<FString, FString>& Pair : ExtraDeviceInfo)
		{
			params.insert(std::make_pair(TCHAR_TO_UTF8(*Pair.Key), TCHAR_TO_UTF8(*Pair.Value)));
		}
		jsFunc->Invoke<void>(params);
	}else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find trackEventAddExtraDeviceInfo method"))
	}
}

void UOneEngineSDKSubsystem::SetUpConfigAppID(const FString& AppId)
{
}

FString UOneEngineSDKSubsystem::GetAppId()
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getAppId"))
	{
		std::string result = jsFunc->Invoke<std::string>();
		FString Result(UTF8_TO_TCHAR(result.c_str()));
		return Result;
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find getAppId method"))
	}
	return "";
}

bool UOneEngineSDKSubsystem::IsLoggedIn()
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.hasLogin"))
	{
		return jsFunc->Invoke<bool>();
	}else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find wanmeiTrackEvent method"))
	}
	return false;
}

void UOneEngineSDKSubsystem::EnableDebugMode(bool Enable)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.setDebug"))
	{
	 jsFunc->Invoke<void>(Enable);
	}else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find setDebug method"))
	}
}

bool UOneEngineSDKSubsystem::IsDebugMode()
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.isDebug"))
	{
		return jsFunc->Invoke<bool>();
	}else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find isDebug method"))
	}
	return false;
}

void UOneEngineSDKSubsystem::OpenUserCenter()
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.openUserCenter"))
	{
		return jsFunc->Invoke<void>();
	}else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find openUserCenter method"))
	}
}

void UOneEngineSDKSubsystem::GetQRCodeScanResult(FOneGetQRCodeScanResultDelegate OnGetQRCodeScanResultDelegate)
{
	//launchPCScanLogin
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.launchPCScanLogin"))
	{
		std::function<void(std::string, std::string)> cb = [OnGetQRCodeScanResultDelegate](std::string type, std::string link)
		{
			FString Type(UTF8_TO_TCHAR(type.c_str()));
			FString Link(UTF8_TO_TCHAR(link.c_str()));
			AsyncTask(ENamedThreads::GameThread, [OnGetQRCodeScanResultDelegate,Type,Link]()
			{
				OnGetQRCodeScanResultDelegate.ExecuteIfBound(Type,Link);
			});
		};
		jsFunc->Invoke<void>(cb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find launchPCScanLogin method"))
		OnGetQRCodeScanResultDelegate.ExecuteIfBound("", "");
	}
}

void UOneEngineSDKSubsystem::OpenComplianceOnWebView()
{
	//openCompliance
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.openCompliance"))
	{
		jsFunc->Invoke<void>();
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find openCompliance method"))
	}
}

void UOneEngineSDKSubsystem::EnterAccountCancellation()
{
}

FString UOneEngineSDKSubsystem::GetChannelId()
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getChannelId"))
	{
		std::string result = jsFunc->Invoke<std::string>();
		FString Result(UTF8_TO_TCHAR(result.c_str()));
		return Result;
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find getChannelId method"))
	}
	return "";
}

FString UOneEngineSDKSubsystem::GetChannelMediaId()
{
	// 获取渠道id
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getChannelMediaId"))
	{
		std::string result = jsFunc->Invoke<std::string>();
		FString Result(UTF8_TO_TCHAR(result.c_str()));
		return Result;
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find GetChannelMediaId method"))
	}
	return "";
}

int32 UOneEngineSDKSubsystem::GetPlatformOS()
{
	// 获取渠道id
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getPlatformOs"))
	{
		int result = jsFunc->Invoke<std::int8_t>();
		return result;
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find getPlatformOs method"))
	}
	return 0;
}

void UOneEngineSDKSubsystem::GetChannelPlatform(FOneGetPlatformDelegate OnGetPlatformResultDelegate)
{
	// 获取渠道id
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getPlatformChannel"))
	{
		std::string result = jsFunc->Invoke<std::string>();
		FString Result(UTF8_TO_TCHAR(result.c_str()));
		OnGetPlatformResultDelegate.ExecuteIfBound(true, 0, "", Result);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find GetChannelMediaId method"))
		OnGetPlatformResultDelegate.ExecuteIfBound(false, -101, "didn't find GetChannelMediaId method", "");
	}
}

void UOneEngineSDKSubsystem::GetChannelPlatformLambda(
	const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const FString& Platform)>& OnFinishedLambda)
{
	// 获取渠道id
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getPlatformChannel"))
	{
		std::string result = jsFunc->Invoke<std::string>();
		FString Result(UTF8_TO_TCHAR(result.c_str()));
		OnFinishedLambda(true, 0, "", Result);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find getPlatformChannel method"))
		OnFinishedLambda(false, -1, "didn't find getPlatformChannel method", "");
	}
}

//万能方法接口
bool UOneEngineSDKSubsystem::IsCommonFunctionSupported(const FString& FuncName)
{
	return false;
}

void UOneEngineSDKSubsystem::CallCommonFunction(const FString& FuncName, const FString& Params,
                                                FOneCommonFunctionDelegate CommonFunctionDelegate)
{
	CommonFunctionDelegate.ExecuteIfBound(FuncName,-101, "not support this method");
}

// 获取档位信息(iOS专有接口)
void UOneEngineSDKSubsystem::GetProductList(const TArray<FString>& ProductIds,
                                            FOneProductInfoDelegate ProductResultDelegate)
{
	ProductResultDelegate.ExecuteIfBound(false,-101,TArray<FOneProductInfo>());
}

//获取审核开关(iOS专有接口)
bool UOneEngineSDKSubsystem::ExaminStatus()
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.examinStatus"))
	{
		return jsFunc->Invoke<bool>();
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find examinStatus method"))
	}
	return false;
}

//是否检测BundleId(iOS专有接口)
void UOneEngineSDKSubsystem::ShouldVerifyBundleId(bool bShouldVerify)
{
}

void UOneEngineSDKSubsystem::GetUserLocationInfo(FOneUserLocationInfoDelegate LocationInfoDelegate)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getUserIp"))
	{
		std::string result = jsFunc->Invoke<std::string>();
		FOneUserLocationInfo UserLocationInfo;
		UserLocationInfo.IP = FString(UTF8_TO_TCHAR(result.c_str()));
		LocationInfoDelegate.ExecuteIfBound(UserLocationInfo);
	}else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find getUserIp method"))
		FOneUserLocationInfo UserLocationInfo;
		LocationInfoDelegate.ExecuteIfBound(UserLocationInfo);
	}
}

void UOneEngineSDKSubsystem::GetUserLocationInfoLambda(
	const TFunction<void(const FOneUserLocationInfo& LocationInfo)>& OnFinishedLambda)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getUserIp"))
	{
		std::string result = jsFunc->Invoke<std::string>();
		FOneUserLocationInfo UserLocationInfo;
		UserLocationInfo.IP = FString(UTF8_TO_TCHAR(result.c_str()));
		OnFinishedLambda(UserLocationInfo);
	}else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find getUserIp method"))
		FOneUserLocationInfo UserLocationInfo;
		OnFinishedLambda(UserLocationInfo);
	}
}

void UOneEngineSDKSubsystem::GetDeviceInfo(FOneGetDeviceInfoDelegate Delegate)
{
	// 获取渠道id
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getDeviceInfo"))
	{
		std::string result = jsFunc->Invoke<std::string>();
		FString DeviceInfoStr(UTF8_TO_TCHAR(result.c_str()));
		FOneDeviceInfo DeviceInfo;
		FJsonObjectWrapper Wrapper;
		Wrapper.JsonObjectFromString(DeviceInfoStr);
		DeviceInfo.DeviceId = Wrapper.JsonObject->GetStringField("deviceID");
		DeviceInfo.DeviceSys = Wrapper.JsonObject->GetStringField("deviceSys");

		TSharedPtr<FJsonObject> ExtObj = Wrapper.JsonObject->GetObjectField("ext");
		if (ExtObj)
		{
			for (const auto& Pair : ExtObj->Values)
			{
				FString Key = Pair.Key;
				FString Value;
				if (Pair.Value->TryGetString(Value))
				{
					DeviceInfo.Ext.Add(Key, Value);
				}
			}
		}
		Delegate.ExecuteIfBound(DeviceInfo);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find getDeviceInfo method"))
		FOneDeviceInfo DeviceInfo;
		Delegate.ExecuteIfBound(DeviceInfo);
	}
	
}

void UOneEngineSDKSubsystem::GetDeviceInfoLambda(
	const TFunction<void(const FOneDeviceInfo& DeviceInfo)>& OnFinishedLambda)
{
	// 获取渠道id
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getDeviceInfo"))
	{
		std::string result = jsFunc->Invoke<std::string>();
		FString DeviceInfoStr(UTF8_TO_TCHAR(result.c_str()));
		FOneDeviceInfo DeviceInfo;
		FJsonObjectWrapper Wrapper;
		Wrapper.JsonObjectFromString(DeviceInfoStr);
		DeviceInfo.DeviceId = Wrapper.JsonObject->GetStringField("deviceID");
		DeviceInfo.DeviceSys = Wrapper.JsonObject->GetStringField("deviceSys");

		TSharedPtr<FJsonObject> ExtObj = Wrapper.JsonObject->GetObjectField("ext");
		if (ExtObj)
		{
			for (const auto& Pair : ExtObj->Values)
			{
				FString Key = Pair.Key;
				FString Value;
				if (Pair.Value->TryGetString(Value))
				{
					DeviceInfo.Ext.Add(Key, Value);
				}
			}
		}
		OnFinishedLambda(DeviceInfo);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find getDeviceInfo method"))
		FOneDeviceInfo DeviceInfo;
		OnFinishedLambda(DeviceInfo);
	}
}

void UOneEngineSDKSubsystem::FetchAntiAddictionInfo(FOneFetchAntiAddictionInfoDelegate OnFetchAntiAddictionInfo)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.fetchAntiAddictionInfo"))
	{
		std::function<void(std::string)> cb = [OnFetchAntiAddictionInfo](std::string result)
		{
			FString Result(UTF8_TO_TCHAR(result.c_str()));
			AsyncTask(ENamedThreads::GameThread, [OnFetchAntiAddictionInfo,Result]()
			{
				FOneAntiAddictionInfo OneAntiAddictionInfo;
				FJsonObjectWrapper Wrapper;
				Wrapper.JsonObjectFromString(Result);
				if (Wrapper.JsonObject.IsValid())
				{
					Wrapper.JsonObject->TryGetNumberField("age",OneAntiAddictionInfo.Age);
					Wrapper.JsonObject->TryGetNumberField("gender",OneAntiAddictionInfo.Gender);
					Wrapper.JsonObject->TryGetNumberField("realUser",OneAntiAddictionInfo.Realuser);
					Wrapper.JsonObject->TryGetNumberField("status",OneAntiAddictionInfo.Status);
					Wrapper.JsonObject->TryGetNumberField("accountType",OneAntiAddictionInfo.AccountType);
					Wrapper.JsonObject->TryGetStringField("bannedReason",OneAntiAddictionInfo.BannedReason);
					Wrapper.JsonObject->TryGetNumberField("bannedType",OneAntiAddictionInfo.BannedType);
					Wrapper.JsonObject->TryGetStringField("breakNotice",OneAntiAddictionInfo.BreakNotice);
					Wrapper.JsonObject->TryGetNumberField("civicType",OneAntiAddictionInfo.CivicType);
					Wrapper.JsonObject->TryGetNumberField("newHeartbeatInterval",OneAntiAddictionInfo.HeartbeatInterval);
					Wrapper.JsonObject->TryGetStringField("requestIp",OneAntiAddictionInfo.RequestIp);
					Wrapper.JsonObject->TryGetStringField("userId",OneAntiAddictionInfo.UserId);
					Wrapper.JsonObject->TryGetStringField("appId",OneAntiAddictionInfo.AppID);
					Wrapper.JsonObject->TryGetNumberField("dayOnlineDuration",OneAntiAddictionInfo.DayOnlineDuration);
				}
				OnFetchAntiAddictionInfo.ExecuteIfBound(OneAntiAddictionInfo);
			});
		};
		 jsFunc->Invoke<void>(cb);
	}else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find fetchAntiAddictionInfo method"))
		FOneAntiAddictionInfo OneAntiAddictionInfo;
		OnFetchAntiAddictionInfo.ExecuteIfBound(OneAntiAddictionInfo);
	}
}

void UOneEngineSDKSubsystem::StartAntiAddictionNotify(const FString& ServerId, const FString& RoleId)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.startAntiAddictionNotify"))
	{
		std::function<void(std::string)> cb = [](std::string result)
		{
			FString Result(UTF8_TO_TCHAR(result.c_str()));
			AsyncTask(ENamedThreads::GameThread, [Result]()
			{
				FOneAntiAddictionInfo OneAntiAddictionInfo;
				FJsonObjectWrapper Wrapper;
				Wrapper.JsonObjectFromString(Result);
				if (Wrapper.JsonObject.IsValid())
				{
					Wrapper.JsonObject->TryGetNumberField("age",OneAntiAddictionInfo.Age);
					Wrapper.JsonObject->TryGetNumberField("gender",OneAntiAddictionInfo.Gender);
					Wrapper.JsonObject->TryGetNumberField("realUser",OneAntiAddictionInfo.Realuser);
					Wrapper.JsonObject->TryGetNumberField("status",OneAntiAddictionInfo.Status);
					Wrapper.JsonObject->TryGetNumberField("accountType",OneAntiAddictionInfo.AccountType);
					Wrapper.JsonObject->TryGetStringField("bannedReason",OneAntiAddictionInfo.BannedReason);
					Wrapper.JsonObject->TryGetNumberField("bannedType",OneAntiAddictionInfo.BannedType);
					Wrapper.JsonObject->TryGetStringField("breakNotice",OneAntiAddictionInfo.BreakNotice);
					Wrapper.JsonObject->TryGetNumberField("civicType",OneAntiAddictionInfo.CivicType);
					Wrapper.JsonObject->TryGetNumberField("newHeartbeatInterval",OneAntiAddictionInfo.HeartbeatInterval);
					Wrapper.JsonObject->TryGetStringField("requestIp",OneAntiAddictionInfo.RequestIp);
					Wrapper.JsonObject->TryGetStringField("userId",OneAntiAddictionInfo.UserId);
					Wrapper.JsonObject->TryGetStringField("appId",OneAntiAddictionInfo.AppID);
					Wrapper.JsonObject->TryGetNumberField("dayOnlineDuration",OneAntiAddictionInfo.DayOnlineDuration);
				}
				UOneEngineSDKSubsystem *Subsystem =GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
				Subsystem->OnAntiAddictionTimeoutDelegate.Broadcast(true,OneAntiAddictionInfo);
			});
		};
		jsFunc->Invoke<void>(TCHAR_TO_UTF8(*RoleId),TCHAR_TO_UTF8(*ServerId),cb);
	}else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find startAntiAddictionNotify method"))
		FOneAntiAddictionInfo OneAntiAddictionInfo;
		UOneEngineSDKSubsystem *Subsystem =GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
		Subsystem->OnAntiAddictionTimeoutDelegate.Broadcast(true,OneAntiAddictionInfo);
	}
}

void UOneEngineSDKSubsystem::StopAntiAddictionNotify()
{
	//stopAntiAddictionNotify
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.stopAntiAddictionNotify"))
	{
		jsFunc->Invoke<void>();
	}else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find stopAntiAddictionNotify method"))
	}
}

void UOneEngineSDKSubsystem::SetShowDefaultActivationResultToast(bool bShow)
{
}

void UOneEngineSDKSubsystem::DisplayCDKeyDialog(FOneGenericResultDelegate OnCDKeyActivateResult,
                                                const FString& ServerId)
{
	OnCDKeyActivateResult.ExecuteIfBound(false,-101,TEXT("not support this method"));
}

// 查询用户有没有激活资格
void UOneEngineSDKSubsystem::QueryUserActiveQualification(const FString& ServerId, FOneQueryUserActiveQualificationResultDelegate Callback)
{
	Callback.ExecuteIfBound(false,-101,TEXT("not support this method"),FOneActiveQualificationInfo());
}

void UOneEngineSDKSubsystem::QueryUserActiveQualificationLambda(const FString& ServerId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)> &OnFinishedLambda)
{
	OnFinishedLambda(false, -101, TEXT("not support this method"), FOneActiveQualificationInfo());
}
	
// 激活设备 
void UOneEngineSDKSubsystem::ActivateDevice(const FString& ServerId, FOneActivateDeviceResultDelegate OneActivateDeviceResultDelegate)
{
	OneActivateDeviceResultDelegate.ExecuteIfBound(false,"","",-101,TEXT("not support this method"));
}

void UOneEngineSDKSubsystem::ActivateDeviceLambda(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> &OnFinishedLambda)
{
	OnFinishedLambda(false, false, TEXT(""), -101, TEXT("not support this method"));
}
void UOneEngineSDKSubsystem::QueryActCode(const FString& ServerId,
                                          FOneQueryActCodeResultDelegate OnQueryActCodeResultDelegate)
{
	OnQueryActCodeResultDelegate.ExecuteIfBound(false, "", "", -101, TEXT("not support this method"));
}

void UOneEngineSDKSubsystem::QueryActCodeLambda(const FString& ServerId,
                                                const TFunction<void(bool bSucceed, bool bNeedActCode,
                                                                     const FString& ActCodePrompt, int32 Code,
                                                                     const FString& ErrorMsg)>& OnFinishedLambda)
{
	OnFinishedLambda(false, false, TEXT(""), -101, TEXT("not support this method"));
}

void UOneEngineSDKSubsystem::ExchangeActCode(const FString& ServerId, const FString& ActCode,
                                             FOneGenericResultDelegate GenericResultDelegate)
{
	GenericResultDelegate.ExecuteIfBound(false, -101, TEXT("not support this method"));
}

void UOneEngineSDKSubsystem::ExchangeActCodeLambda(const FString& ServerId, const FString& ActCode,
                                                   const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>&
                                                   OnFinishedLambda)
{
	OnFinishedLambda(false, -101, TEXT("not support this method"));
}

void UOneEngineSDKSubsystem::RedeemCouponCode(const FString& CouponCode, const FString& ServerId, const FString& RoleId,
                                              const FString& RoleLevel, const FString& VipLevel,
                                              const TMap<FString, FString>& ExtraInfo,
                                              FOneGenericResultDelegate OnRedeemCouponResult)
{
	RedeemCouponCodeLambda(CouponCode,ServerId,RoleId,RoleLevel,VipLevel,ExtraInfo,[OnRedeemCouponResult](bool bSuccess, int32 Code, const FString& Msg)
	{
		OnRedeemCouponResult.ExecuteIfBound(bSuccess,Code,Msg);
	});
}

void UOneEngineSDKSubsystem::RedeemCouponCodeLambda(const FString& CouponCode, const FString& ServerId,
                                                    const FString& RoleId, const FString& RoleLevel,
                                                    const FString& VipLevel, const TMap<FString, FString>& ExtraInfo,
                                                    const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>
                                                    & OnRedeemCouponResult)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.setRoleInfo"))
	{
		FJsonObjectWrapper Wrapper;
		TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
		JsonObj->SetStringField("roleId", RoleId);
		JsonObj->SetStringField("serverId", ServerId);
		JsonObj->SetStringField("vip", VipLevel);
		JsonObj->SetStringField("lv", RoleLevel);
		Wrapper.JsonObject = JsonObj;
		FString Result;
		Wrapper.JsonObjectToString(Result);
		jsFunc->Invoke<void>(TCHAR_TO_UTF8(*Result));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find setRoleInfo method"))
	}
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.verifyRedeemCode"))
	{
		std::function<void(bool, int, std::string)> cb = [OnRedeemCouponResult](bool success, int code, std::string msg)
		{
			FString Msg(UTF8_TO_TCHAR(msg.c_str()));
			AsyncTask(ENamedThreads::GameThread, [OnRedeemCouponResult,success,code,Msg]()
			{
				OnRedeemCouponResult(success,code,Msg);
			});
		};
		std::map<std::string,std::string> ext;
		for (const auto& Elem : ExtraInfo) {
           
			std::string Key(TCHAR_TO_UTF8(*Elem.Key));
			std::string Value(TCHAR_TO_UTF8(*Elem.Value));
			ext.insert({Key, Value});
		}
		jsFunc->Invoke<void>(TCHAR_TO_UTF8(*CouponCode),ext,cb);
	}else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find verifyRedeemCode method"))
		OnRedeemCouponResult(false,-101,TEXT("didn't find verifyRedeemCode method"));
	}
}

void UOneEngineSDKSubsystem::FetchUserRoleInfoList(FOneFetchUserRoleInfoListDelegate OnFetchUserRoleListDelegate,
                                                   const FString& ServerId)
{
	FetchUserRoleInfoListLambda([OnFetchUserRoleListDelegate](bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneURCRoleInfo>& RoleList)
	{
		OnFetchUserRoleListDelegate.ExecuteIfBound(bSucceed,Code,Msg,RoleList);
	},ServerId);
}

void UOneEngineSDKSubsystem::FetchUserRoleInfoListLambda(
	const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneURCRoleInfo>& RoleList)>&
	OnFinishedLambda, const FString& ServerId)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getUserRoleInfoList"))
	{
		std::function<void(bool, std::string, int, std::string)> cb = [OnFinishedLambda](bool success, std::string result, int code, std::string msg)
		{
			FString Msg(UTF8_TO_TCHAR(msg.c_str()));
			FString Result(UTF8_TO_TCHAR(result.c_str()));
			AsyncTask(ENamedThreads::GameThread, [OnFinishedLambda,success,code,Msg,Result]()
			{
				TArray<FOneURCRoleInfo> UserList;
				if (success)
				{
					TArray<TSharedPtr<FJsonValue>> JsonArray;
					TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(Result);
					if (!FJsonSerializer::Deserialize(JsonReader, JsonArray))
					{
						UE_LOG(LogJson, Warning, TEXT("JsonArrayStringToUStruct - Unable to parse. json=[%s]"), *Result);
					}
					else
					{
						if (JsonArray.Num() > 0)
						{
							for (int32 i = 0; i < JsonArray.Num(); i++)
							{
								const TSharedPtr<FJsonObject>& RoleObj = JsonArray[i]->AsObject();
								if (RoleObj.IsValid())
								{
									FOneURCRoleInfo RoleInfo;
									RoleInfo.UserId = RoleObj->GetStringField("userId");
									RoleInfo.Gender = RoleObj->GetStringField("gender");
									RoleInfo.RoleId = RoleObj->GetStringField("roleId");
									RoleInfo.RoleName = RoleObj->GetStringField("roleName");
									RoleInfo.ServerId = RoleObj->GetStringField("serverId");
									RoleInfo.ServerName = RoleObj->GetStringField("serverName");
									RoleInfo.LastLoginTime = RoleObj->GetStringField("lastLogin");
									RoleInfo.Level = RoleObj->GetStringField("lev");
									RoleInfo.Occupation = RoleObj->GetStringField("occupation");
									UserList.Add(RoleInfo);
								}
							}
						}
					}
				}
				OnFinishedLambda(success,code,Msg,UserList);
			});
		};
       
		jsFunc->Invoke<void>(TCHAR_TO_UTF8(*ServerId),cb);
	}else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find getUserRoleInfoList method"))
		OnFinishedLambda(false,-101,TEXT("didn't find getUserRoleInfoList method"),TArray<FOneURCRoleInfo>());
	}
}

void UOneEngineSDKSubsystem::Bind(FOneBindResultDelegate StartBindPhoneDelegate, EOneEngineThirdType BindType)
{
	if(BindType == EOneEngineThirdType::Phone)
	{
		if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.startBindPhone"))
		{
			std::function<void(bool, int, std::string)> cb = [StartBindPhoneDelegate](bool success, int code, std::string msg)
			{
				FString Msg(UTF8_TO_TCHAR(msg.c_str()));
				AsyncTask(ENamedThreads::GameThread, [StartBindPhoneDelegate,success,code,Msg]()
				{
					UOneEngineSDKSubsystem* Subsystem = GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
					StartBindPhoneDelegate.ExecuteIfBound(success,code,Msg,EOneEngineThirdType::Phone,Subsystem->GetUserInfo());
				});
			};
			
			jsFunc->Invoke<void>(cb);
		}else
		{
			UE_LOG(LogTemp, Error, TEXT("didn't find startBindPhone method"))
		}
	}else
	{
		StartBindPhoneDelegate.ExecuteIfBound(false,-101,TEXT("didn't find startBindPhone method"),EOneEngineThirdType::Phone,FOneUserInfo());
	}
}

void UOneEngineSDKSubsystem::UserAuthentication(FOneUserAuthenticationResultDelegate OnUserAuthenticationResultDelegate)
{
	OnUserAuthenticationResultDelegate.ExecuteIfBound(false,-101,false,TEXT("didn't find UserAuthentication method"));
}

// 获取声明的权限 可用于展示
TArray<FOnePermissionInfo> UOneEngineSDKSubsystem::GetPermissions()
{
	TArray<FOnePermissionInfo> PermissionInfos;
	return PermissionInfos;
}

// 检查是否权限已授权
bool UOneEngineSDKSubsystem::CheckSelfPermission(EOnePermissionType Type)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.checkPermissionsFromUser"))
	{
		return jsFunc->Invoke<bool>(Type);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find checkPermissionsFromUser method"))
	}
	return false;
}

// 申请权限
void UOneEngineSDKSubsystem::RequestPermission(EOnePermissionType Type, FOneRequestPermissionResultDelegate Delegate)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.reqPermissionsFromUser"))
	{
		std::function<void(bool, int)> cb = [Delegate,Type](bool success, int type)
		{
			AsyncTask(ENamedThreads::GameThread, [Delegate,Type,success,type]()
			{
				Delegate.ExecuteIfBound(Type,success);
			});
		};
		jsFunc->Invoke<void>(static_cast<int>(Type), cb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find reqPermissionsFromUser method"))
	}
}

void UOneEngineSDKSubsystem::RequestPermission(EOnePermissionType Type, FOneRequestPermissionResultDelegate Delegate,
                                               const TArray<FString>& Tips)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.reqPermissionsFromUser"))
	{
		std::function<void(bool, int)> cb = [Delegate,Type](bool success, int type)
		{
			AsyncTask(ENamedThreads::GameThread, [Delegate,Type,success,type]()
			{
				Delegate.ExecuteIfBound(Type,success);
			});
		};
		jsFunc->Invoke<void>(static_cast<int>(Type), cb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find reqPermissionsFromUser method"))
	}
}

//  关闭剪切板权限
void UOneEngineSDKSubsystem::CloseClipboardPermission()
{
}

// 打开应用权限设置页
void UOneEngineSDKSubsystem::OpenApplicationSetting()
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.openSystemPermissionSetting"))
	{
		jsFunc->Invoke<void>();
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find openSystemPermissionSetting method"))
	}
}

// 打开AIHelp客服
void UOneEngineSDKSubsystem::OpenAIHelp(EOneAIHelpType Type, const FString& RoleId, const FString& ServerId,
                                        const FString& RoleName)
{
}

// 打开自定义客服
void UOneEngineSDKSubsystem::OpenCustomerService(const FString& RoleID, const FString& RoleName,
                                                 const FString& ServerID)
{
}

// 文本翻译
void UOneEngineSDKSubsystem::TranslateLambda(const FString& Text,
                                             const TFunction<void(bool bSucceed, const FString& Result,
                                                                  const FString& ErrorMsg)>& OnFinishedLambda)
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::TranslateLambda not support"));
	OnFinishedLambda(false, "", TEXT("didn't support TranslateLambda method"));
}

void UOneEngineSDKSubsystem::Translate(const FString& Text, FOneTranslateResultDelegate Callback)
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::Translate not support"));
	Callback.ExecuteIfBound(false, "", TEXT("didn't support Translate method"));
}

// 获取当前设置语言
FString UOneEngineSDKSubsystem::GetCurrentLanguage()
{
	return "";
}

// 设置语言
void UOneEngineSDKSubsystem::SetLanguage(const FString& Code)
{
}

// 获取本地语言列表
TArray<FString> UOneEngineSDKSubsystem::GetSupportedLanguageCodeList()
{
	TArray<FString> LanguageCodeList;
	return LanguageCodeList;
}

// 设置屏幕方向
void UOneEngineSDKSubsystem::SetScreenOrientation(EOneScreenOrientation Orientation)
{
}

// 分享SDK
void UOneEngineSDKSubsystem::ShareDataToApp(EOneShareAppTarget AppTarget, const EOneShareType Type,
                                            const FOneShareData& Data, FOneGenericResultDelegate OnShareResult)
{
	FString ShareData;
	const TSharedPtr<FJsonObject> Obj = MakeShareable(new FJsonObject);
	Obj->SetStringField(TEXT("title"), Data.Title);
	Obj->SetStringField(TEXT("content"), Data.Content);
	Obj->SetStringField(TEXT("localImagePath"), Data.LocalImagePath);
	Obj->SetStringField(TEXT("netImageUrl"), Data.NetImageUrl);
	Obj->SetStringField(TEXT("webPageUrl"), Data.WebPageUrl);
	Obj->SetStringField(TEXT("topicId"), Data.TopicId);
	if (AppTarget == EOneShareAppTarget::PE_Weibo && !Data.SinaSuperGroup.SuperGroup.IsEmpty())
	{
		{
			const TSharedPtr<FJsonObject> JsonSuperData = MakeShareable(new FJsonObject);
			JsonSuperData->SetStringField(TEXT("superGroup"), Data.SinaSuperGroup.SuperGroup);
			JsonSuperData->SetStringField(TEXT("section"), Data.SinaSuperGroup.Section);
			const TSharedPtr<FJsonObject> JsonExtraInfo = MakeShareable(new FJsonObject);
			for (auto& Element : Data.SinaSuperGroup.ExtraInfo)
			{
				JsonExtraInfo->SetStringField(Element.Key, Element.Value);
			}
			FString JsonExtraInfoStr;
			const TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonExtraInfoStr);
			FJsonSerializer::Serialize(JsonExtraInfo.ToSharedRef(), Writer);
			JsonSuperData->SetObjectField(TEXT("extraInfo"), JsonExtraInfo);
			Obj->SetObjectField(TEXT("weiboSuperData"), JsonSuperData);
		}
	}
	if (Type == EOneShareType::Image)
	{
		if (Data.Image)
		{
#if ENGINE_MAJOR_VERSION>=5
			FTexture2DMipMap& MipMap =  Data.Image->GetPlatformData()->Mips[0];
#else
			FTexture2DMipMap& MipMap =  Data.Image->PlatformData->Mips[0];
#endif
    		FByteBulkData& ImageSourceData = MipMap.BulkData;
    		TArray<FColor> SrcImageData(reinterpret_cast<FColor*>(ImageSourceData.Lock(LOCK_READ_ONLY)), ImageSourceData.GetBulkDataSize() / 4);

    		TArray<uint8> ImageData;
#if ENGINE_MAJOR_VERSION==5 && ENGINE_MINOR_VERSION>=1
			FImageUtils::ThumbnailCompressImageArray(MipMap.SizeX, MipMap.SizeY, SrcImageData, ImageData);
#else
			FImageUtils::CompressImageArray(MipMap.SizeX, MipMap.SizeY, SrcImageData, ImageData);
#endif
    		const FString ExternalFilesDir = FPlatformMisc::GamePersistentDownloadDir();
    		FString TempPath = ExternalFilesDir+TEXT("/share")+FString::FromInt(FDateTime::Now().GetMillisecond())+TEXT(".png");
    		if (FFileHelper::SaveArrayToFile(ImageData, *TempPath))
    		{
    			Obj->SetStringField(TEXT("localImagePath"), TempPath);
    		}else
    		{
    			UE_LOG(LogTemp,Warning,TEXT("分享的图片数据保存到本地失败：地址 %s"),*TempPath);
    		}
    		
    		ImageSourceData.Unlock();		
			
		}
		
	}
	else if (Type == EOneShareType::WebPage)
	{
		if (Data.Thumbnail)
		{
#if ENGINE_MAJOR_VERSION>=5 
			FTexture2DMipMap& MipMap =  Data.Image->GetPlatformData()->Mips[0];
#else
			FTexture2DMipMap& MipMap =  Data.Image->PlatformData->Mips[0];
#endif
            FByteBulkData& ImageSourceData = MipMap.BulkData;
            TArray<FColor> SrcImageData(reinterpret_cast<FColor*>(ImageSourceData.Lock(LOCK_READ_ONLY)), ImageSourceData.GetBulkDataSize() / 4);

            TArray<uint8> ImageData;
#if ENGINE_MAJOR_VERSION==5 && ENGINE_MINOR_VERSION>=1
			FImageUtils::ThumbnailCompressImageArray(MipMap.SizeX, MipMap.SizeY, SrcImageData, ImageData);
#else
			FImageUtils::CompressImageArray(MipMap.SizeX, MipMap.SizeY, SrcImageData, ImageData);
#endif
            const FString ExternalFilesDir = FPlatformMisc::GamePersistentDownloadDir();
            FString TempPath = ExternalFilesDir+TEXT("/share")+FString::FromInt(FDateTime::Now().GetMillisecond())+TEXT(".png");
            if (FFileHelper::SaveArrayToFile(ImageData, *TempPath))
            {
            	Obj->SetStringField(TEXT("localImagePath"), TempPath);
            }else
            {
            	UE_LOG(LogTemp,Warning,TEXT("分享的图片数据保存到本地失败：地址 %s"),*TempPath);
            }
            
            ImageSourceData.Unlock();
		}
		
	}

	FJsonObjectWrapper Wrapper;
	Wrapper.JsonObject = Obj;
	Wrapper.JsonObjectToString(ShareData);
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.share"))
	{
		std::function<void(bool, int, std::string)> cb = [OnShareResult](bool success,  int code, std::string msg)
		{
			AsyncTask(ENamedThreads::GameThread, [OnShareResult,success,code,msg]()
			{
				OnShareResult.ExecuteIfBound(success, code, FString(UTF8_TO_TCHAR(msg.c_str())));
			});
		};
		jsFunc->Invoke<void>(static_cast<int>(AppTarget),static_cast<int>(Type),TCHAR_TO_UTF8(*ShareData), cb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find share method"))
	}
}

// 推送相关接口
// 注册推送
void UOneEngineSDKSubsystem::StartUpdatePushData(FOneStartUpdatePushDataDelegate Callback)
{
	int AppId;
	FString AppKey;

	GConfig->GetInt(TEXT("/Script/OneEngineEditor.OneEngineSettings"), TEXT("AppID"), AppId, GGameIni);
	GConfig->GetString(TEXT("/Script/OneEngineEditor.OneEngineSettings"), TEXT("OneAppKey"), AppKey, GGameIni);
	std::string Key(TCHAR_TO_UTF8(*AppKey));

	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.initPush"))
	{
		std::function<void(bool, std::string, int, std::string)> cb = [Callback
			](bool success, std::string deviceToken, int code, std::string msg)
		{
			AsyncTask(ENamedThreads::GameThread, [Callback,success,deviceToken,code,msg]()
			{
				Callback.ExecuteIfBound(success, code, FString(UTF8_TO_TCHAR(msg.c_str())),
				                        FString(UTF8_TO_TCHAR(deviceToken.c_str())));
			});
		};
		jsFunc->Invoke<void>(AppId,TCHAR_TO_UTF8(*AppKey), cb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find initPush method"))
	}
}

// 设置点击推送消息后的回调
void UOneEngineSDKSubsystem::SetupNotificationCallback(FOneNotificationDelegate Callback)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.setPushMsgListener"))
	{
		std::function<void(std::string)> cb = [Callback](std::string msg)
		{
			FString Result(UTF8_TO_TCHAR(msg.c_str()));
			AsyncTask(ENamedThreads::GameThread, [Callback,Result]()
			{
				FOnePushMessage Message;
				FJsonObjectWrapper Wrapper;
				Wrapper.JsonObjectFromString(Result);
				Wrapper.JsonObject->TryGetStringField("messageId", Message.MessageId);
				Wrapper.JsonObject->TryGetStringField("ext", Message.Ext);
				Callback.ExecuteIfBound(Message);
			});
		};
		jsFunc->Invoke<void>(cb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find setPushMsgListener method"))
	}
}

// 获取系统推送开关状态 和 应用推送开关状态
void UOneEngineSDKSubsystem::GetPushStatus(FOnePushStatusDelegate Callback)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getPushStatus"))
	{
		std::string result=	jsFunc->Invoke<std::string>();
		FString Result(UTF8_TO_TCHAR(result.c_str()));
		FOnePushStatus Message;
		FJsonObjectWrapper Wrapper;
		Wrapper.JsonObjectFromString(Result);
		Wrapper.JsonObject->TryGetBoolField("isAppOpen", Message.bIsAppOpen);
		Wrapper.JsonObject->TryGetBoolField("isSysOpen", Message.bIsSysOpen);
		Callback.ExecuteIfBound(Message);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find getPushStatus method"))
		Callback.ExecuteIfBound(FOnePushStatus());
	}
}

// 设置应用推送开关状态
void UOneEngineSDKSubsystem::SetProviderPushState(bool bProviderPushState, FOneGenericResultDelegate Callback)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.changePushState"))
	{
		std::function<void(bool, int, std::string)> cb = [Callback](bool success, int code, std::string msg)
		{
			AsyncTask(ENamedThreads::GameThread, [Callback,success,code,msg]()
			{
				Callback.ExecuteIfBound(success, code, FString(UTF8_TO_TCHAR(msg.c_str())));
			});
		};
		jsFunc->Invoke<void>(bProviderPushState, cb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find changePushState method"))
		Callback.ExecuteIfBound(false, -101, TEXT("didn't find changePushState method"));
	}
}

// 推送设置用户信息
void UOneEngineSDKSubsystem::SetPushUserInfo(const FString& ServerId, const FString& RoleId,
                                             FOneGenericResultDelegate Callback)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.bindUserId"))
	{
		std::function<void(bool, int, std::string)> cb = [Callback](bool success, int code, std::string msg)
		{
			AsyncTask(ENamedThreads::GameThread, [Callback,success,code,msg]()
			{
				Callback.ExecuteIfBound(success, code, FString(UTF8_TO_TCHAR(msg.c_str())));
			});
		};
		jsFunc->Invoke<void>(TCHAR_TO_UTF8(*RoleId),TCHAR_TO_UTF8(*ServerId), cb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find bindUserId method"))
		Callback.ExecuteIfBound(false, -101, TEXT("didn't find bindUserId method"));
	}
}

// 推送解绑用户信息
void UOneEngineSDKSubsystem::UnSetPushUserInfo(FOneGenericResultDelegate Callback)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.unbindUserId"))
	{
		std::function<void(bool, int, std::string)> cb = [Callback](bool success, int code, std::string msg)
		{
			AsyncTask(ENamedThreads::GameThread, [Callback,success,code,msg]()
			{
				Callback.ExecuteIfBound(success, code, FString(UTF8_TO_TCHAR(msg.c_str())));
			});
		};
		jsFunc->Invoke<void>(cb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find unbindUserId method"))
		Callback.ExecuteIfBound(false, -101, TEXT("didn't find unbindUserId method"));
	}
}

// 获取推送类型列表
void UOneEngineSDKSubsystem::GetPushTypeInfoList(FOneGetPushTypeInfoListDelegate Callback)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getPushTypeList"))
	{
		std::function<void(bool, int, std::string, std::string)> cb = [Callback
			](bool success, int code, std::string msg, std::string result)
		{
			FString Result(UTF8_TO_TCHAR(result.c_str()));
			AsyncTask(ENamedThreads::GameThread, [Callback,success,code,msg,Result]()
			{
				TArray<FOnePushTypeInfo> TypeList;
				if (success)
				{
					TArray<TSharedPtr<FJsonValue>> JsonValues;
					TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(Result);
					if (FJsonSerializer::Deserialize(JsonReader, JsonValues))
					{
						for (const TSharedPtr<FJsonValue>& JsonValue : JsonValues)
						{
							if (JsonValue->Type == EJson::Object)
							{
								// 如果JSON值是一个对象，则可以按键值对的方式提取信息
								const TSharedPtr<FJsonObject>& JsonObject = JsonValue->AsObject();
								FOnePushTypeInfo PushTypeInfo;
								PushTypeInfo.Name = JsonObject->GetStringField("name");
								PushTypeInfo.PushType = JsonObject->GetNumberField("pushType");
								PushTypeInfo.bOpen = JsonObject->GetBoolField("isOpen");
								TypeList.Add(PushTypeInfo);
							}
						}
					}
					else
					{
						// 解析失败，处理错误
						UE_LOG(LogTemp, Error, TEXT("Failed to parse JSON string"));
					}
				}
				Callback.ExecuteIfBound(success, code, FString(UTF8_TO_TCHAR(msg.c_str())), TypeList);
			});
		};
		jsFunc->Invoke<void>(cb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find getPushTypeList method"))
		Callback.ExecuteIfBound(false, -101, TEXT("didn't find getPushTypeList method"), TArray<FOnePushTypeInfo>());
	}
}

// 批量设置推送类型开关状态
void UOneEngineSDKSubsystem::UpdatePushTypeList(const TArray<FOnePushTypeInfo>& PushTypeList,
                                                FOneGenericResultDelegate Callback)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.updatePushType"))
	{
		TArray<TSharedPtr<FJsonValue>> JsonValues;

		for (const FOnePushTypeInfo& Info : PushTypeList)
		{
			TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
			JsonObject->SetStringField(TEXT("name"), Info.Name);
			JsonObject->SetNumberField(TEXT("pushType"), Info.PushType);
			JsonObject->SetBoolField(TEXT("isOpen"), Info.bOpen);
			TSharedPtr<FJsonValueObject> JsonValue = MakeShareable(new FJsonValueObject(JsonObject));
			JsonValues.Add(JsonValue);
		}

		FString Result;
		TSharedRef<TJsonWriter<>> JsonWriter = TJsonWriterFactory<>::Create(&Result);
		FJsonSerializer::Serialize(JsonValues, JsonWriter);
		JsonWriter->Close();
		std::function<void(bool, int, std::string)> cb = [Callback](bool success, int code, std::string msg)
		{
			AsyncTask(ENamedThreads::GameThread, [Callback,success,code,msg]()
			{
				Callback.ExecuteIfBound(success, code, FString(UTF8_TO_TCHAR(msg.c_str())));
			});
		};
		jsFunc->Invoke<void>(TCHAR_TO_UTF8(*Result),cb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find updatePushType method"))
		Callback.ExecuteIfBound(false, -101, TEXT("didn't find updatePushType method"));
	}
}

// 夜间勿扰配置的回调
void UOneEngineSDKSubsystem::GetPushNotDisturb(FOnePushNotDisturbInfoDelegate Callback)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getPushNotDisturbInfo"))
	{
		std::function<void(bool, int, std::string, std::string)> cb = [Callback
			](bool success, int code, std::string msg, std::string result)
		{
			FString Result(UTF8_TO_TCHAR(result.c_str()));
			AsyncTask(ENamedThreads::GameThread, [Callback,success,code,msg,Result]()
			{
				FOnePushNotDisturbInfo PushNotDisturbInfo;
				if (success)
				{
					FJsonObjectWrapper Wrapper;
					Wrapper.JsonObjectFromString(Result);
					TSharedPtr<FJsonObject>& JsonObj = Wrapper.JsonObject;

					if (JsonObj.IsValid())
					{
						PushNotDisturbInfo.bNotDisturb = JsonObj->GetBoolField("notDisturb");
						PushNotDisturbInfo.NotDisturbStartTime = JsonObj->GetStringField("notDisturbStartTime");
						PushNotDisturbInfo.NotDisturbEndTime = JsonObj->GetStringField("notDisturbEndTime");
					}
				}
				Callback.ExecuteIfBound(success, code, FString(UTF8_TO_TCHAR(msg.c_str())), PushNotDisturbInfo);
			});
		};
		jsFunc->Invoke<void>(cb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find getPushNotDisturbInfo method"))
		Callback.ExecuteIfBound(false, -101, TEXT("didn't find getPushNotDisturbInfo method"), FOnePushNotDisturbInfo());
	}
}

// 设置勿扰模式
void UOneEngineSDKSubsystem::UpdatePushNotDisturb(const FOnePushNotDisturbInfo& NotDisturbInfo,
                                                  FOneGenericResultDelegate Callback)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.updateNotDisturbInfo"))
	{
		std::function<void(bool, int, std::string)> cb = [Callback](bool success, int code, std::string msg)
		{
			AsyncTask(ENamedThreads::GameThread, [Callback,success,code,msg]()
			{
				Callback.ExecuteIfBound(success, code, FString(UTF8_TO_TCHAR(msg.c_str())));
			});
		};

		
		TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);

		JsonObject->SetStringField(TEXT("notDisturbEndTime"), NotDisturbInfo.NotDisturbEndTime);
		JsonObject->SetStringField(TEXT("notDisturbStartTime"), NotDisturbInfo.NotDisturbStartTime);
		JsonObject->SetBoolField(TEXT("notDisturb"), NotDisturbInfo.bNotDisturb);
		FString Result;
		FJsonObjectWrapper Wrapper;
		Wrapper.JsonObject = JsonObject;
		Wrapper.JsonObjectToString(Result);
		jsFunc->Invoke<void>(TCHAR_TO_UTF8(*Result),cb);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find updateNotDisturbInfo method"))
		Callback.ExecuteIfBound(false, -101, TEXT("didn't find updateNotDisturbInfo method"));
	}
}

void UOneEngineSDKSubsystem::KillProcess()
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.killProcess"))
	{
		jsFunc->Invoke<void>();
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find killProcess method"))
	}
}

bool UOneEngineSDKSubsystem::ACELogin(const FString& accountId, int accountTypeCommandId, int worldId, FOneOnGetClientPacket delegate)
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::ACELogin not support"));
	return false;
}

bool UOneEngineSDKSubsystem::ACEClientPacketReceive(const TArray<uint8>& data)
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::ACEClientPacketReceive not support"));
	return false;
}

bool UOneEngineSDKSubsystem::ACELogout()
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::ACELogout not support"));
	return false;
}
void UOneEngineSDKSubsystem::GetIpInfo(const FString& Ip,FOnGetIpInfoResultDelegate Delegate)
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::GetIpInfo not support"));
	Delegate.ExecuteIfBound(false, FUserIpInfo(),-101, TEXT("harmony not support this method"));
}

/// 全球独有的 评价接口和 修改屏幕亮度接口
float UOneEngineSDKSubsystem::GetScreenBrightness()
{
	return 0.0f;
}

void UOneEngineSDKSubsystem::SetScreenBrightness(float BrightnessValue)
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::SetScreenBrightness not support"));
}
	
void UOneEngineSDKSubsystem::RecoverScreenBrightness()
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::RecoverScreenBrightness not support"));
}
///保持屏幕常亮
void UOneEngineSDKSubsystem::SwitchScreenPermanentBrightnessState(bool bIsTurnOn)
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::SwitchScreenPermanentBrightnessState not support"));
}
	

///评价接口
void UOneEngineSDKSubsystem::InAppRequestStoreReview(FOneGenericResultDelegate Delegate)
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::InAppRequestStoreReview not support"));
	Delegate.ExecuteIfBound(false, -101, TEXT("harmony not support this method"));
}

/// AppLink Ios有这个参数
void UOneEngineSDKSubsystem::RequestStoreReview(FString AppLink)
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::RequestStoreReview not support"));
}

/// 获取渲染器配置文件地址
FString UOneEngineSDKSubsystem::GetRenderConfigFilePath()
{
	return "";
}

// 安全锁相关接口
void UOneEngineSDKSubsystem::UnlockSafeLockUsingPushNotification(const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::UnlockSafeLockUsingPushNotification not support"));
	OnUnlockSafeLockResultDelegate.ExecuteIfBound(EOneUnlockSafeLockResult(),"", -101, TEXT("harmony not support this method"),EOneUnlockSafeLockType::PushNotification);
}

void UOneEngineSDKSubsystem::UnlockSafeLockUsingDynamicCode(const FString& DynamicCode, const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::UnlockSafeLockUsingDynamicCode not support"));
	OnUnlockSafeLockResultDelegate.ExecuteIfBound(EOneUnlockSafeLockResult(),"", -101, TEXT("harmony not support this method"),EOneUnlockSafeLockType::PushNotification);
}

void UOneEngineSDKSubsystem::StopUnlockSafeLockUsingPushNotification()
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::StopUnlockSafeLockUsingPushNotification not support"));
}

// 全球独有的打开Naver论坛
void UOneEngineSDKSubsystem::OpenCommunityByGame(EOneNaverGameType Type, const FString& Pid, bool Scheduled, FOneLoadDelegate LoadCallback, FOneUnloadDelegate UnloadCallback, FOneInGameMenuDelegate InGameMenuCallback)
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::OpenCommunityByGame not support"));
}

void UOneEngineSDKSubsystem::TerminateCommunity()
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::TerminateCommunity not support"));
}
void UOneEngineSDKSubsystem::GetProviderPushState(FOnGetPushStateDelegate Callback)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.getPushStatus"))
	{
		std::string result=	jsFunc->Invoke<std::string>();
		FString Result(UTF8_TO_TCHAR(result.c_str()));
		FOnePushStatus Message;
		FJsonObjectWrapper Wrapper;
		Wrapper.JsonObjectFromString(Result);
		Wrapper.JsonObject->TryGetBoolField("isAppOpen", Message.bIsAppOpen);
		Wrapper.JsonObject->TryGetBoolField("isSystemOpen", Message.bIsSysOpen);
		Callback.ExecuteIfBound(true,0,"",Message.bIsAppOpen);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find getPushStatus method"))
		Callback.ExecuteIfBound(false, -101, TEXT("didn't find getPushStatus method"), false);
	}
}


void UOneEngineSDKSubsystem::SetAnalyticsCollectionEnabled(bool bEnable)
{
	UE_LOG(LogTemp, Error, TEXT("UOneEngineSDKSubsystem::SetAnalyticsCollectionEnabled not support"));
}
bool UOneEngineSDKSubsystem::IsInstalledApp(EOneShareAppTarget AppTarget)
{
	if (auto jsFunc = aki::JSBind::GetJSFunction("OneGlobalSDKBridge.isInstalledApp"))
	{
		return jsFunc->Invoke<bool>(static_cast<int>(AppTarget));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("didn't find isInstalledApp method"))
	}
	return false;
}
#endif