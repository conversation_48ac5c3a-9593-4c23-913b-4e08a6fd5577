// Fill out your copyright notice in the Description page of Project Settings.


#include "OneEngineSDKPSSubsystem.h"
#include "OneEngineSDKSubsystem.h"
#include "Engine/Font.h"
#include "Async/Async.h"
#include "JsonUtilities.h"
#include "./Views/PSUserWidgetSettings.h"
#include "Engine/Engine.h"
// ========= 发布时需要关闭 =============
#define PS_SUBSYSTEM_DEV_MODE 0

#if ENGINE_SUPPORT_SONY
#if PS_SUBSYSTEM_DEV_MODE
#include "Sony/OneEngineSDKPSAdapter.h"
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/libone.h"
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/libpspay.h"
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/libglobal.h"
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/libpfa.h"
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/libwebapi.h"
#include "../../DependLibraries/Source/ThirdParty/DependLibrariesLibrary/PS5/include/libwebbrowser.h"
#endif
#if PLATFORM_PS4 || PLATFORM_PS5
		#include "Sony/OneEngineSDKPSAdapter.h"
		#include "libpspay.h"
		#include "libpfa.h"
		#include "libone.h"
		#include "libglobal.h"
		#include "libwebapi.h"
		#include "libwebbrowser.h"
		#include "wmc/logger.h"
		#include <np.h>
		#include <np_commerce_dialog.h>
		#include <thread>
#endif
#else
#if PS_SUBSYSTEM_DEV_MODE
		#define PS_SUBSYSTEM_DEV_MODE 0
#endif
	#define PLATFORM_PS4 0
	#define PLATFORM_PS5 0
#endif


DEFINE_LOG_CATEGORY(LogOneEngineSDK_PS);

#define PS_CURRENT_CLASS_FUNCTION_LOCAL(FUNCTION) (FString(FUNCTION))
#define PS_CURRENT_LINE_LOCAL __LINE__
#define PS_CURRENT_CLASS_FUNCTION_LINE_LOCAL(FUNCTION) (PS_CURRENT_CLASS_FUNCTION_LOCAL(FUNCTION) + TEXT(":") + FString::FromInt(PS_CURRENT_LINE_LOCAL))

#define PS_LOG_PRINTF(FormatString, ...) UE_LOG(LogOneEngineSDK_PS, Log, TEXT("%s: %s"), *PS_CURRENT_CLASS_FUNCTION_LINE_LOCAL(__FUNCTION__), *FString::Printf(TEXT(FormatString), ##__VA_ARGS__) )
#define PS_LOG_WARNING_PRINTF(FormatString, ...) UE_LOG(LogOneEngineSDK_PS, Warning, TEXT("%s: %s"), *PS_CURRENT_CLASS_FUNCTION_LINE_LOCAL(__FUNCTION__), *FString::Printf(TEXT(FormatString), ##__VA_ARGS__) )
#define PS_LOG_ERROR_PRINTF(FormatString, ...) UE_LOG(LogOneEngineSDK_PS, Error, TEXT("%s: %s"), *PS_CURRENT_CLASS_FUNCTION_LINE_LOCAL(__FUNCTION__), *FString::Printf(TEXT(FormatString), ##__VA_ARGS__) )

namespace
{

	TMap<FString, FString> CreateCountryCodeToRegionMap()
	{
		TMap<FString, FString> CountryCodeToRegionMap;

		// 添加国家代码和相应的所属区域
		CountryCodeToRegionMap.Add(TEXT("ae"), TEXT("SIEE")); // UAE
		CountryCodeToRegionMap.Add(TEXT("ar"), TEXT("SIEA")); // Argentina
		CountryCodeToRegionMap.Add(TEXT("at"), TEXT("SIEE")); // Austria
		CountryCodeToRegionMap.Add(TEXT("au"), TEXT("SIEE")); // Australia
		CountryCodeToRegionMap.Add(TEXT("be"), TEXT("SIEE")); // Belgium
		CountryCodeToRegionMap.Add(TEXT("bg"), TEXT("SIEE")); // Bulgaria
		CountryCodeToRegionMap.Add(TEXT("bh"), TEXT("SIEE")); // Bahrain
		CountryCodeToRegionMap.Add(TEXT("bo"), TEXT("SIEA")); // Bolivia
		CountryCodeToRegionMap.Add(TEXT("br"), TEXT("SIEA")); // Brazil
		CountryCodeToRegionMap.Add(TEXT("ca"), TEXT("SIEA")); // Canada
		CountryCodeToRegionMap.Add(TEXT("ch"), TEXT("SIEE")); // Switzerland
		CountryCodeToRegionMap.Add(TEXT("cl"), TEXT("SIEA")); // Chile
		CountryCodeToRegionMap.Add(TEXT("cn"), TEXT("SIEJA-Asia")); // China
		CountryCodeToRegionMap.Add(TEXT("co"), TEXT("SIEA")); // Colombia
		CountryCodeToRegionMap.Add(TEXT("cr"), TEXT("SIEA")); // Costa Rica
		CountryCodeToRegionMap.Add(TEXT("cy"), TEXT("SIEE")); // Cyprus
		CountryCodeToRegionMap.Add(TEXT("cz"), TEXT("SIEE")); // Czech Republic
		CountryCodeToRegionMap.Add(TEXT("de"), TEXT("SIEE")); // Germany
		CountryCodeToRegionMap.Add(TEXT("dk"), TEXT("SIEE")); // Denmark
		CountryCodeToRegionMap.Add(TEXT("ec"), TEXT("SIEA")); // Ecuador
		CountryCodeToRegionMap.Add(TEXT("es"), TEXT("SIEE")); // Spain
		CountryCodeToRegionMap.Add(TEXT("fi"), TEXT("SIEE")); // Finland
		CountryCodeToRegionMap.Add(TEXT("fr"), TEXT("SIEE")); // France
		CountryCodeToRegionMap.Add(TEXT("gb"), TEXT("SIEE")); // UK
		CountryCodeToRegionMap.Add(TEXT("gr"), TEXT("SIEE")); // Greece
		CountryCodeToRegionMap.Add(TEXT("gt"), TEXT("SIEA")); // Guatemala
		CountryCodeToRegionMap.Add(TEXT("hk"), TEXT("SIEJA-Asia")); // Hong Kong
		CountryCodeToRegionMap.Add(TEXT("hn"), TEXT("SIEA")); // Honduras
		CountryCodeToRegionMap.Add(TEXT("hr"), TEXT("SIEE")); // Croatia
		CountryCodeToRegionMap.Add(TEXT("hu"), TEXT("SIEE")); // Hungary
		CountryCodeToRegionMap.Add(TEXT("id"), TEXT("SIEJA-Asia")); // Indonesia
		CountryCodeToRegionMap.Add(TEXT("ie"), TEXT("SIEE")); // Ireland
		CountryCodeToRegionMap.Add(TEXT("il"), TEXT("SIEE")); // Israel
		CountryCodeToRegionMap.Add(TEXT("in"), TEXT("SIEE")); // India
		CountryCodeToRegionMap.Add(TEXT("is"), TEXT("SIEE")); // Iceland
		CountryCodeToRegionMap.Add(TEXT("it"), TEXT("SIEE")); // Italy
		CountryCodeToRegionMap.Add(TEXT("jp"), TEXT("SIEJA-Japan")); // Japan
		CountryCodeToRegionMap.Add(TEXT("kr"), TEXT("SIEJA-Asia")); // Korea
		CountryCodeToRegionMap.Add(TEXT("kw"), TEXT("SIEE")); // Kuwait
		CountryCodeToRegionMap.Add(TEXT("lb"), TEXT("SIEE")); // Lebanon
		CountryCodeToRegionMap.Add(TEXT("lu"), TEXT("SIEE")); // Luxembourg
		CountryCodeToRegionMap.Add(TEXT("mt"), TEXT("SIEE")); // Malta
		CountryCodeToRegionMap.Add(TEXT("mx"), TEXT("SIEA")); // Mexico
		CountryCodeToRegionMap.Add(TEXT("my"), TEXT("SIEJA-Asia")); // Malaysia
		CountryCodeToRegionMap.Add(TEXT("ni"), TEXT("SIEA")); // Nicaragua
		CountryCodeToRegionMap.Add(TEXT("nl"), TEXT("SIEE")); // Netherlands
		CountryCodeToRegionMap.Add(TEXT("no"), TEXT("SIEE")); // Norway
		CountryCodeToRegionMap.Add(TEXT("nz"), TEXT("SIEE")); // New Zealand
		CountryCodeToRegionMap.Add(TEXT("om"), TEXT("SIEE")); // Oman
		CountryCodeToRegionMap.Add(TEXT("pa"), TEXT("SIEA")); // Panama
		CountryCodeToRegionMap.Add(TEXT("pe"), TEXT("SIEA")); // Peru
		CountryCodeToRegionMap.Add(TEXT("pl"), TEXT("SIEE")); // Poland
		CountryCodeToRegionMap.Add(TEXT("pt"), TEXT("SIEE")); // Portugal
		CountryCodeToRegionMap.Add(TEXT("py"), TEXT("SIEA")); // Paraguay
		CountryCodeToRegionMap.Add(TEXT("qa"), TEXT("SIEE")); // Qatar
		CountryCodeToRegionMap.Add(TEXT("ro"), TEXT("SIEE")); // Romania
		CountryCodeToRegionMap.Add(TEXT("ru"), TEXT("SIEE")); // Russia
		CountryCodeToRegionMap.Add(TEXT("sa"), TEXT("SIEE")); // Saudi Arabia
		CountryCodeToRegionMap.Add(TEXT("se"), TEXT("SIEE")); // Sweden
		CountryCodeToRegionMap.Add(TEXT("sg"), TEXT("SIEJA-Asia")); // Singapore
		CountryCodeToRegionMap.Add(TEXT("si"), TEXT("SIEE")); // Slovenia
		CountryCodeToRegionMap.Add(TEXT("sk"), TEXT("SIEE")); // Slovakia
		CountryCodeToRegionMap.Add(TEXT("sv"), TEXT("SIEA")); // El Salvador
		CountryCodeToRegionMap.Add(TEXT("th"), TEXT("SIEJA-Asia")); // Thailand
		CountryCodeToRegionMap.Add(TEXT("tr"), TEXT("SIEE")); // Turkey
		CountryCodeToRegionMap.Add(TEXT("tw"), TEXT("SIEJA-Asia")); // Taiwan
		CountryCodeToRegionMap.Add(TEXT("ua"), TEXT("SIEE")); // Ukraine
		CountryCodeToRegionMap.Add(TEXT("us"), TEXT("SIEA")); // United States
		CountryCodeToRegionMap.Add(TEXT("uy"), TEXT("SIEA")); // Uruguay
		CountryCodeToRegionMap.Add(TEXT("za"), TEXT("SIEE")); // South Africa
		return CountryCodeToRegionMap;
	}

	std::atomic_bool GIsDialogOpen{false};

	int SyncOpenCommerceDialogPremiumMode()
	{
		int ret = 0;
		if (GIsDialogOpen)
		{
			return -1;
		}
		// 由于该已申请豁免，PS4 无需实现，PS5 必须实现
#if PLATFORM_PS5
		SceCommonDialogStatus status;
		SceUserServiceUserId user;
		ret = sceUserServiceGetInitialUser(&user);
		if (ret < SCE_OK)
		{
			PS_LOG_ERROR_PRINTF("sceUserServiceGetInitialUser failed: 0x%08x", ret);
			return ret;
		}
		ret = sceNpCommerceDialogInitialize();
		if (ret < SCE_OK)
		{
			PS_LOG_ERROR_PRINTF("sceNpCommerceDialogInitialize failed: 0x%08x", ret);
			return ret;
		}

		SceNpCommerceDialogParam param;
		sceNpCommerceDialogParamInitialize(&param);
		// #if PLATFORM_PS5
		param.mode = SCE_NP_COMMERCE_DIALOG_MODE_PREMIUM;
		param.features = SCE_NP_PREMIUM_FEATURE_REALTIME_MULTIPLAY;
		// #else
		// 		param.mode = SCE_NP_COMMERCE_DIALOG_MODE_PLUS;
		// 		param.features = SCE_NP_PLUS_FEATURE_REALTIME_MULTIPLAY;
		// #endif

		param.userId = user;

		ret = sceNpCommerceDialogOpen(&param);
		if (ret < SCE_OK)
		{
			PS_LOG_ERROR_PRINTF("sceNpCommerceDialogOpen failed: 0x%08x", ret);
			return ret;
		}
		GIsDialogOpen = true;
		do
		{
			// Wait for the dialog to finish
			std::this_thread::sleep_for(std::chrono::milliseconds(200));
			status = sceNpCommerceDialogUpdateStatus();
		} while(status != SCE_COMMON_DIALOG_STATUS_FINISHED);

		GIsDialogOpen = false;

		int tmp_ret = sceNpCommerceDialogTerminate();

		if (tmp_ret < SCE_OK)
		{
			PS_LOG_ERROR_PRINTF("sceNpCommerceDialogTerminate failed: 0x%08x", tmp_ret);
		}

#endif
		return ret;
	}
}

void UOneEngineSDKPSSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
}

void UOneEngineSDKPSSubsystem::Deinitialize()
{
	Super::Deinitialize();
}

EOnePsnAccountState UOneEngineSDKPSSubsystem::GetAccountState() const
{
	int State = 0;
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
	wmc_platform_account_state(&State);
#endif
	return static_cast<EOnePsnAccountState>(State);
}

void UOneEngineSDKPSSubsystem::GetFriends(int32 Offset, int32 Limit, FOnGetFriendsResultDelegate OnGetFriendsResult)
{
	UE_LOG(LogOneEngineSDK_PS, Display, TEXT("GetFriends: %d, %d"), Offset, Limit);
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
	auto Pimp = FONEEngineSDKPSAdapter::Get().GetPimp();
	if (Pimp)
	{
		Pimp->GetFriendList(Offset, Limit, [this,OnGetFriendsResult](bool bSuccess, const FString& Result, int ErrCode, const FString& ErrInfo)
		{
			FOnePSUserProfileResponse Response;
			if (bSuccess)
			{
				ParsePsUserProfileResponse(Result, Response);
			}
			else
			{
				PS_LOG_ERROR_PRINTF("GetFriends failed: %d, %s", ErrCode, *ErrInfo);
			}
			auto _ = OnGetFriendsResult.ExecuteIfBound(Response);
		});
	}
	else
	{
		PS_LOG_ERROR_PRINTF("GetFriends failed: Pimp is null");
	}
#endif
}

void UOneEngineSDKPSSubsystem::GetBlockingUsers(int32 Offset, int32 Limit, FOnGetBlockingUsersResultDelegate OnGetBlockingUsersResult)
{
	UE_LOG(LogOneEngineSDK_PS, Display, TEXT("GetBlockingUsers: %d, %d"), Offset, Limit);
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
	auto Pimp = FONEEngineSDKPSAdapter::Get().GetPimp();
	if (Pimp)
	{
		Pimp->GetBlockList(Offset, Limit, [this,OnGetBlockingUsersResult](bool bSuccess, const FString& Result, int ErrCode, const FString& ErrInfo)
		{
			FOnePSUserProfileResponse Response;
			if (bSuccess)
			{
				ParsePsUserProfileResponse(Result, Response);
			}
			else
			{
				PS_LOG_ERROR_PRINTF("GetFriends failed: %d, %s", ErrCode, *ErrInfo);
			}
			auto _ = OnGetBlockingUsersResult.ExecuteIfBound(Response);
		});
	}
	else
	{
		PS_LOG_ERROR_PRINTF("GetFriends failed: Pimp is null");
	}
#endif
}

FString UOneEngineSDKPSSubsystem::GetAccountId() const
{
	FString Result;
#if PLATFORM_PS4 || PLATFORM_PS5
	char buff_aid[16];
	int32_t size = 0;
	memset(&buff_aid, 0x0, sizeof(16));
	wmc_platform_account_id(buff_aid, &size);
	SceNpAccountId aid = 0;
	memcpy(&aid, buff_aid, size);
	Result = FString::Printf(TEXT("%llu"), aid);
#endif
	UE_LOG(LogOneEngineSDK_PS, Display, TEXT("GetAccountId: %s"), *Result);
	return Result;
}

FString UOneEngineSDKPSSubsystem::GetOnlineId() const
{
	FString Result;
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
	char buff_oid[32];
	int32_t size = 0;
	memset(&buff_oid, 0x0, 32);
	wmc_platform_ps_online_id(buff_oid, &size);
	Result = FString(UTF8_TO_TCHAR(buff_oid));
#endif
	UE_LOG(LogOneEngineSDK_PS, Display, TEXT("GetOnlineId: %s"), *Result);
	return Result;
}

FString UOneEngineSDKPSSubsystem::GetCountryCode()
{
	FString Result;
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
	char buff_cc[3];
	int32_t size = 0;
	memset(&buff_cc, 0x0, 3);
	wmc_platform_account_country(buff_cc, &size);
	Result = FString(UTF8_TO_TCHAR(buff_cc));
#endif
	UE_LOG(LogOneEngineSDK_PS, Display, TEXT("GetCountryCode: %s"), *Result);
	return Result;
}

FString UOneEngineSDKPSSubsystem::GetCountryRegion(const FString& CountryCode) {
	auto Region = CreateCountryCodeToRegionMap();
	if (Region.Contains(CountryCode))
	{
		PS_LOG_ERROR_PRINTF("GetCountryRegion: %s =>%s", *CountryCode, *Region[CountryCode]);
		return Region[CountryCode];
	}
	return TEXT("");
}

int32 UOneEngineSDKPSSubsystem::ShowStoreIcon(EOnePSStoreIconPos Pos)
{
	int Ret = 0;
	int PosInt = (int)Pos;
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
	Ret = wmc_pspay_show_store_icon((PS_STORE_ICON_POS)PosInt);
#endif
	UE_LOG(LogOneEngineSDK_PS, Display, TEXT("ShowStoreIcon: %d"), Ret);
	return Ret;
}

int32 UOneEngineSDKPSSubsystem::HideStoreIcon()
{
	int Ret = 0;
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
	Ret = wmc_pspay_hidden_store_icon();
#endif
	return Ret;
}

void UOneEngineSDKPSSubsystem::Pay(const FOnePSPurchaseForm& PurchaseForm, TFunction<void(int Code, bool HasPurchased, const FString& Tips)> Callback)
{

	UE_LOG(LogOneEngineSDK_PS, Display, TEXT("Begin Pay: %s %s"), *PurchaseForm.GameOrderId, *PurchaseForm.ProductId);

#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
    // 显示加载界面
    auto Pimp = FONEEngineSDKPSAdapter::Get().GetPimp();
    // 内部会切换到 GameThread 执行
    Pimp->ShowLoading();
#endif

	// 开启异步线程
	AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this, PurchaseForm,Callback]()
	{
		int Ret = -1;
		bool Purchased = false;
		auto ProductId = PurchaseForm.ProductId;
		FString OrderId;

		TSharedPtr<FJsonObject> JSONObject = MakeShareable(new FJsonObject);
		UOneEngineSDKSubsystem* Subsystem = GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
		if (Subsystem->GetRegionType() == EOneEngineSDKRegionType::Mainland)
		{
			// 构造 Json 串
			JSONObject->SetStringField(TEXT("appOrder"), PurchaseForm.GameOrderId);
			JSONObject->SetStringField(TEXT("amount"), PurchaseForm.OrderAmount);
			JSONObject->SetStringField(TEXT("serverId"), PurchaseForm.GameServerId);
			JSONObject->SetStringField(TEXT("productId"), PurchaseForm.ProductId);
			JSONObject->SetStringField(TEXT("productName"), PurchaseForm.ProductName);
			JSONObject->SetStringField(TEXT("roleId"), PurchaseForm.GameRoleId);
			JSONObject->SetStringField(TEXT("roleName"), PurchaseForm.GameRoleName);
			JSONObject->SetStringField(TEXT("extraJson"), PurchaseForm.GameExtraInfo);
			JSONObject->SetStringField(TEXT("description"), PurchaseForm.GameDescription);
		}
		else
		{
			JSONObject->SetStringField(TEXT("gameOrderId"), PurchaseForm.GameOrderId);
			JSONObject->SetStringField(TEXT("orderAmount"), PurchaseForm.OrderAmount);
			JSONObject->SetStringField(TEXT("gameServerId"), PurchaseForm.GameServerId);
			JSONObject->SetStringField(TEXT("productId"), PurchaseForm.ProductId);
			JSONObject->SetStringField(TEXT("productName"), PurchaseForm.ProductName);
			JSONObject->SetStringField(TEXT("gameRoleId"), PurchaseForm.GameRoleId);
			// Not Need
			// JSONObject->SetStringField(TEXT("roleName"), PurchaseForm.GameRoleName);
			JSONObject->SetStringField(TEXT("productQuantity"), TEXT("1"));
			JSONObject->SetStringField(TEXT("gameExtraInfo"), PurchaseForm.GameExtraInfo);
			// JSONObject->SetStringField(TEXT("description"), PurchaseForm.GameDescription);
		}
		FString CopyStr;
		FJsonObjectWrapper Wrapper;
		Wrapper.JsonObject = JSONObject;
		Wrapper.JsonObjectToString(CopyStr);

		PS_LOG_PRINTF("Pay payload: %s", *CopyStr);

		{
#if PLATFORM_PS4 || PLATFORM_PS5|| PS_SUBSYSTEM_DEV_MODE
			// 未成年人下单
			auto Pimp = FONEEngineSDKPSAdapter::Get().GetPimp();

			// 弹窗文字提醒
			FString Tips;
			char* TipsC = nullptr;
			char* OutputJsonStr = nullptr;
			int OrderResult = 0;

			if (Subsystem->GetRegionType() == EOneEngineSDKRegionType::Mainland)
			{
				void* OneHandle = Pimp->GetHandle();
				OrderResult = one_unify_pay_sync(OneHandle, OnePlatform::PlayStation, TCHAR_TO_UTF8(*CopyStr), &OutputJsonStr, &TipsC);
			}
			else
			{
				OrderResult = global_sdk_manager_create_order(TCHAR_TO_UTF8(*CopyStr), &OutputJsonStr);
			}


			if (OrderResult == 0)
			{
				// 下单成功
				FString OrderJsonObjectStr = UTF8_TO_TCHAR(OutputJsonStr);

				PS_LOG_PRINTF("Successfully created order, ret: %d, output: %s", OrderResult, *OrderJsonObjectStr);

				if (Subsystem->GetRegionType() == EOneEngineSDKRegionType::Mainland)
				{
					TSharedPtr<FJsonObject> OutputJsonObject = MakeShareable(new FJsonObject);
					TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(OrderJsonObjectStr);
					if (FJsonSerializer::Deserialize(Reader, OutputJsonObject) && OutputJsonObject.IsValid())
					{
						OutputJsonObject->TryGetStringField(TEXT("orderId"), OrderId);
					}
					one_free(OutputJsonStr);
				}
				else
				{
					TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject());
					TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(OrderJsonObjectStr);
					if (FJsonSerializer::Deserialize(JsonReader, JsonObject) && JsonObject.IsValid())
					{
						JsonObject->TryGetStringField(TEXT("sdkOrderId"), OrderId);
					}
					global_sdk_manager_free(OutputJsonStr);
				}
			}
			else
			{
				if (TipsC != nullptr)
				{
					Tips = UTF8_TO_TCHAR(TipsC);
					one_free(TipsC);
				}

				PS_LOG_ERROR_PRINTF("Failed to create order, ret: %d, finish pay", OrderResult);

				AsyncTask(ENamedThreads::GameThread, [OrderResult, Tips, Callback]()
				{
					auto Pimp = FONEEngineSDKPSAdapter::Get().GetPimp();
					Pimp->HideLoading();
					if (Callback)
					{
						Callback(OrderResult, false, Tips);
					}
				});
				return;
			}
#endif
		}

		// 后台创建订单成功

#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
		// 隐藏 loading
		auto Pimp = FONEEngineSDKPSAdapter::Get().GetPimp();
		Pimp->HideLoading();
#endif

		// 进入系统购买页面
		PS_LOG_PRINTF("Entering system purchase page, OrderId: %s", *OrderId)

		int PayResult = -1;

#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
		Ret = wmc_pspay_buy_products(PurchaseForm.PSNServerLabel, TCHAR_TO_UTF8(*PurchaseForm.ProductId), &PayResult);
#endif
		if (Ret == 0 && PayResult == 2)
		{
			// 支付成功
			PS_LOG_PRINTF("User successfully paid, OrderId: %s", *OrderId);
			Purchased = true;
			int SynchronizingOrderResult = -1;
			// 同步订单状态
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
			// 同步订单
			if (Subsystem->GetRegionType() == EOneEngineSDKRegionType::Mainland)
			{
				FString RoleInfo;
				TSharedPtr<FJsonObject> JSONObject1 = MakeShareable(new FJsonObject);
				JSONObject1->SetStringField(TEXT("serverId"), PurchaseForm.GameServerId);
				JSONObject1->SetStringField(TEXT("roleId"), PurchaseForm.GameRoleId);
				JSONObject1->SetStringField(TEXT("roleName"), PurchaseForm.GameRoleName);
				FJsonObjectWrapper Wrapper1;
				Wrapper1.JsonObject = JSONObject1;
				Wrapper1.JsonObjectToString(RoleInfo);

				void* OneHandle = Pimp->GetHandle();
				if (!OrderId.IsEmpty())
				{
					PS_LOG_PRINTF("SynchronizingOrder: %s, OrderId: %s", *RoleInfo, *OrderId);
					one_entitlement_check_ps(OneHandle, TCHAR_TO_UTF8(*RoleInfo), TCHAR_TO_UTF8(*OrderId));
				}
				else
				{
					PS_LOG_PRINTF("SynchronizingOrder: %s", *RoleInfo);
					one_entitlement_check_ps(OneHandle, TCHAR_TO_UTF8(*RoleInfo), nullptr);
				}
			}
			else
			{
				TSharedPtr<FJsonObject> JSONObject1 = MakeShareable(new FJsonObject);
				JSONObject1->SetStringField(TEXT("gameRoleId"), PurchaseForm.GameRoleId);
				JSONObject1->SetStringField(TEXT("gameServerId"), PurchaseForm.GameServerId);
				if (!OrderId.IsEmpty())
				{
					JSONObject1->SetStringField(TEXT("sdkOrderId"), OrderId);
				}
				FString SyncWalletParamsJsonString;
				FJsonObjectWrapper Wrapper1;
				Wrapper1.JsonObject = JSONObject1;
				Wrapper1.JsonObjectToString(SyncWalletParamsJsonString);
				PS_LOG_PRINTF("SynchronizingOrder: %s", *SyncWalletParamsJsonString);
				SynchronizingOrderResult = global_sdk_manager_synchronizing_order(TCHAR_TO_UTF8((*SyncWalletParamsJsonString)), nullptr);
			}
#endif
		}
		else
		{
			PS_LOG_ERROR_PRINTF("User failed to pay, ret: %d, code: %d", Ret, PayResult);
		}

		AsyncTask(ENamedThreads::GameThread, [Ret, Purchased, Callback]()
		{
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
			auto Pimp = FONEEngineSDKPSAdapter::Get().GetPimp();
			Pimp->HideLoading();
#endif
			if (Callback)
			{
				Callback(Ret, Purchased, "");
			}
		});
	});
}

void UOneEngineSDKPSSubsystem::SyncOrder(const FOneRoleInfo& RoleInfo, FString OrderId)
{

	FString JsonStr;
	UOneEngineSDKSubsystem* Subsystem = GEngine->GetEngineSubsystem<UOneEngineSDKSubsystem>();
	if (Subsystem->GetRegionType() == EOneEngineSDKRegionType::Mainland)
	{
		TSharedPtr<FJsonObject> JSONObject1 = MakeShareable(new FJsonObject);
		JSONObject1->SetStringField(TEXT("serverId"), RoleInfo.ServerId);
		JSONObject1->SetStringField(TEXT("roleId"), RoleInfo.RoleId);
		JSONObject1->SetStringField(TEXT("roleName"), RoleInfo.RoleName);
		FJsonObjectWrapper Wrapper1;
		Wrapper1.JsonObject = JSONObject1;
		Wrapper1.JsonObjectToString(JsonStr);
	}
	else
	{
		TSharedPtr<FJsonObject> JSONObject1 = MakeShareable(new FJsonObject);
		JSONObject1->SetStringField(TEXT("gameRoleId"), RoleInfo.RoleId);
		JSONObject1->SetStringField(TEXT("gameServerId"), RoleInfo.ServerId);
		if (!OrderId.IsEmpty())
		{
			JSONObject1->SetStringField(TEXT("sdkOrderId"), OrderId);
		}
		FJsonObjectWrapper Wrapper1;
		Wrapper1.JsonObject = JSONObject1;
		Wrapper1.JsonObjectToString(JsonStr);
	}

	AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this, JsonStr,OrderId]()
	{
		PS_LOG_PRINTF("SynchronizingOrder: %s", *JsonStr);

#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
		if (FONEEngineSDKPSAdapter::Get().GetRegionType() == EOneEngineSDKRegionType::Mainland)
		{
			auto Pimp = FONEEngineSDKPSAdapter::Get().GetPimp();
			void* OneHandle = Pimp->GetHandle();
			if (OrderId.IsEmpty())
			{
				one_entitlement_check_ps(OneHandle, TCHAR_TO_UTF8(*JsonStr), nullptr);
			}
			else
			{
				one_entitlement_check_ps(OneHandle, TCHAR_TO_UTF8(*JsonStr), TCHAR_TO_UTF8(*OrderId));
			}
		}
		else
		{
			global_sdk_manager_synchronizing_order(TCHAR_TO_UTF8((*JsonStr)), nullptr);
		}
#endif
	});
}

void UOneEngineSDKPSSubsystem::GetCommunicationRestrictionStatus(FOnRestrictionStatusResultDelegate OnRestrictionStatusResult)
{
	// 异步获取通讯限制状态
	AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this, OnRestrictionStatusResult]()
	{
		int Status = 0;
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
		wm_webapi_get_communication_restriction_status(0, &Status);
#endif
		// 切换到游戏线程
		AsyncTask(ENamedThreads::GameThread, [Status, OnRestrictionStatusResult]()
		{
			PS_LOG_PRINTF("GetCommunicationRestrictionStatus: %d", Status);
			bool _ = OnRestrictionStatusResult.ExecuteIfBound(Status);
		});
	});
}

void UOneEngineSDKPSSubsystem::CheckPremium(FOnCheckPremiumResultDelegate OnCheckPremiumResult)
{

	AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this, OnCheckPremiumResult]()
	{
		int Ret = 0;
		bool IsPremium = 0;
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
		Ret = wm_webapi_check_premium(&IsPremium);
#endif
		AsyncTask(ENamedThreads::GameThread, [Ret, IsPremium, OnCheckPremiumResult]()
		{
			PS_LOG_PRINTF("CheckPremium: %d, %d", Ret, IsPremium);
			auto _ = OnCheckPremiumResult.ExecuteIfBound(Ret, IsPremium);
		});
	});
}

int UOneEngineSDKPSSubsystem::StartNotifyPremiumFeature(int Interval, int Mark)
{

	int Ret = 0;
	// 间隔时间不能小于 1
	if (Interval <= 0)
	{
		Interval = 1;
	}
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
	Ret = wm_webapi_start_notify_premium_feature(Interval, Mark);
#endif
	return Ret;
}

int UOneEngineSDKPSSubsystem::StopNotifyPremiumFeature()
{

	int Ret = 0;
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
	Ret = wm_webapi_stop_notify_premium_feature();
#endif
	return Ret;
}

void UOneEngineSDKPSSubsystem::FilterProfanity(const FString& Text, const FString& Language, FOnFilterProfanityResultDelegate OnFilterProfanityResult)
{
	AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this, Text, Language,OnFilterProfanityResult]()
	{
		FString Result;
		int Status = FilterProfanitySync(Text, Language, Result);
		if (Status != 0)
		{
			// 失败，返回原始文本
			Result = Text;
			PS_LOG_ERROR_PRINTF("FilterProfanity failed, code:%d, return original text:%s", Status, *Text);
		}
		AsyncTask(ENamedThreads::GameThread, [Status, Result, OnFilterProfanityResult]()
		{
			auto _ = OnFilterProfanityResult.ExecuteIfBound(Status, Result);
		});
	});
}

int UOneEngineSDKPSSubsystem::FilterProfanitySync(const FString& Text, const FString& Language, FString& OutResult)
{
	int Status = 0;
	FString Result;
	char* Output = nullptr;
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
	Status = wm_webapi_filter_profanity(TCHAR_TO_UTF8(*Text), TCHAR_TO_UTF8(*Language), nullptr, &Output);
	if (Status == 0)
	{
		Result = FString(UTF8_TO_TCHAR(Output));
		wm_webapi_free(Output);
	}
#endif
	OutResult = Result;
	return Status;
}

void UOneEngineSDKPSSubsystem::OpenCommerceDialogPremiumMode(FOnOpenDialogResultDelegate OnOpenDialogResult)
{
	AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [OnOpenDialogResult]()
	{
		int Ret = 0;
		if (GIsDialogOpen)
		{
			AsyncTask(ENamedThreads::GameThread, [OnOpenDialogResult]()
			{
				PS_LOG_ERROR_PRINTF("OpenCommerceDialogPremiumMode failed, dialog is open");
				auto _ = OnOpenDialogResult.ExecuteIfBound(-1);
			});
			return;
		}
		Ret = SyncOpenCommerceDialogPremiumMode();
		AsyncTask(ENamedThreads::GameThread, [Ret, OnOpenDialogResult]()
		{
			PS_LOG_PRINTF("OpenCommerceDialogPremiumMode: %d", Ret);
			auto _ = OnOpenDialogResult.ExecuteIfBound(Ret);
		});
	});
}

void UOneEngineSDKPSSubsystem::GetProductInfoListPS(int32 ServiceLabel, FString CategoryLabel, const FOnGetProductInfoListPSDelegate& Callback)
{
	GetProductInfoListPSLambda(ServiceLabel, CategoryLabel, [Callback](bool bSuccess, const FOnePSProductCategory& Category, int ErrCode, const FString& ErrInfo)
	{
		if (Callback.IsBound())
		{
			Callback.Execute(bSuccess, Category, ErrCode, ErrInfo);
		}
	});
}

void UOneEngineSDKPSSubsystem::GetProductInfoListPSLambda(int32 ServiceLabel, FString CategoryLabel, TFunction<void(bool, const FOnePSProductCategory&, int32, const FString&)> Callback)
{
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
	auto Pimp = FONEEngineSDKPSAdapter::Get().GetPimp();
	Pimp->GetProductInfoList(ServiceLabel, CategoryLabel, [Callback](bool bSuccess, const FString& JsonString, int ErrCode, const FString& ErrInfo)
	{
		PS_LOG_PRINTF("GetProductInfoListPS: %s", *JsonString);
		FOnePSProductCategory Result;
		Result.Code = ErrCode;
		Result.RawData = JsonString;
		if (bSuccess)
		{
			TArray<TSharedPtr<FJsonValue>> List;
			const TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(JsonString);
			if (FJsonSerializer::Deserialize(JsonReader, List) && List.Num() == 1)
			{
				Result.Code = 0;
				const auto RootJsonObject = List[0]->AsObject();
				Result.ID = RootJsonObject->GetStringField(TEXT("id"));
				Result.Label = RootJsonObject->GetStringField(TEXT("label"));
				Result.AgeLimit = RootJsonObject->GetIntegerField(TEXT("ageLimit"));
				Result.DisplayName = RootJsonObject->GetStringField(TEXT("displayName"));
				Result.Type = RootJsonObject->GetStringField(TEXT("type"));
				Result.TotalItemCount = RootJsonObject->GetIntegerField(TEXT("totalItemCount"));
				auto Children = RootJsonObject->GetArrayField(TEXT("children"));
				for (const TSharedPtr<FJsonValue>& ProductJsonValue : Children)
				{
					auto ProductJsonObject = ProductJsonValue->AsObject();
					FOnePSProduct Product;
					Product.ID = ProductJsonObject->GetStringField(TEXT("id"));
					Product.Label = ProductJsonObject->GetStringField(TEXT("label"));
					Product.Description = ProductJsonObject->GetStringField(TEXT("description"));
					Product.AgeLimit = ProductJsonObject->GetIntegerField(TEXT("ageLimit"));
					Product.DisplayName = ProductJsonObject->GetStringField(TEXT("displayName"));
					Product.Type = ProductJsonObject->GetStringField(TEXT("type"));
					// https://p.siedev.net/resources/documents/WebAPI/1/In_Game_Catalog-Overview/0002.html
					auto Skus = ProductJsonObject->GetArrayField(TEXT("skus"));
					for (const TSharedPtr<FJsonValue>& SkusJsonValue : Skus)
					{
						auto SkusJsonObject = SkusJsonValue->AsObject();
						FOnePSProductSku Sku;
						Sku.ID = SkusJsonObject->GetStringField(TEXT("id"));
						Sku.Name = SkusJsonObject->GetStringField(TEXT("name"));
						Sku.Label = SkusJsonObject->GetStringField(TEXT("label"));
						Sku.EndDate = SkusJsonObject->GetStringField(TEXT("endDate"));
						Sku.Type = SkusJsonObject->GetStringField(TEXT("type"));
						Sku.Price = SkusJsonObject->GetIntegerField(TEXT("price"));
						Sku.DisplayPrice = SkusJsonObject->GetStringField(TEXT("displayPrice"));
						Sku.OriginalPrice = SkusJsonObject->GetIntegerField(TEXT("originalPrice"));
						Sku.DisplayOriginalPrice = SkusJsonObject->GetStringField(TEXT("displayOriginalPrice"));
						Sku.UseLimit = SkusJsonObject->GetIntegerField(TEXT("useLimit"));
						Sku.PlusUpsellPrice = SkusJsonObject->GetIntegerField(TEXT("plusUpsellPrice"));
						Sku.DisplayPlusUpsellPrice = SkusJsonObject->GetStringField(TEXT("displayPlusUpsellPrice"));
						Sku.IsPlusPrice = SkusJsonObject->GetBoolField(TEXT("isPlusPrice"));
						Sku.AnnotationName = SkusJsonObject->GetStringField(TEXT("annotationName"));
						Product.Skus.Push(MoveTemp(Sku));
					}

					auto Media = ProductJsonObject->GetObjectField(TEXT("media"));
					if (Media)
					{
						auto Images = Media->GetArrayField(TEXT("images"));
						for (const TSharedPtr<FJsonValue>& ImagesJsonValue : Images)
						{
							auto ImageJsonObject = ImagesJsonValue->AsObject();
							FOnePSProductMediaImage Image;
							Image.Format = ImageJsonObject->GetStringField(TEXT("format"));
							Image.Type = ImageJsonObject->GetStringField(TEXT("type"));
							Image.Url = ImageJsonObject->GetStringField(TEXT("url"));
							Product.Media.Images.Push(MoveTemp(Image));
						}
					}

					Result.Children.Push(MoveTemp(Product));
				}

				auto Media = RootJsonObject->GetObjectField(TEXT("media"));
				if (Media)
				{
					auto Images = Media->GetArrayField(TEXT("images"));
					for (const TSharedPtr<FJsonValue>& ImagesJsonValue : Images)
					{
						auto ImageJsonObject = ImagesJsonValue->AsObject();
						FOnePSProductMediaImage Image;
						Image.Format = ImageJsonObject->GetStringField(TEXT("format"));
						Image.Type = ImageJsonObject->GetStringField(TEXT("type"));
						Image.Url = ImageJsonObject->GetStringField(TEXT("url"));
						Result.Media.Images.Push(MoveTemp(Image));
					}
				}
			}
			else
			{
				PS_LOG_ERROR_PRINTF("GetProductInfoListPS failed, deserialize failed");
				Result.Code = -1;
			}
		}
		if (Callback)
		{
			Callback(bSuccess, Result, ErrCode, ErrInfo);
		}
	});
#endif
}


void UOneEngineSDKPSSubsystem::OpenWebView(const FString& Url, TFunction<void(int Code)> Completion)
{
#if PLATFORM_PS4 || PLATFORM_PS5 || PS_SUBSYSTEM_DEV_MODE
	AsyncTask(ENamedThreads::AnyNormalThreadNormalTask, [Url,Completion]()
	{
		PS_LOG_PRINTF("Open webView with URL: %s", *Url);
		int Ret = 0;
		Ret = wmc_open_webview(TCHAR_TO_UTF8(*Url));
		PS_LOG_PRINTF("Close webView, ret: %d", Ret);
		AsyncTask(ENamedThreads::GameThread, [Completion,Ret]()
		{
			if (Completion)
			{
				Completion(Ret);
			}
		});
	});
#endif
}

void UOneEngineSDKPSSubsystem::SetFontPath(const FString& Path)
{
	if (Path.IsEmpty())
	{
		return;
	}
	UFont *TargetFont = LoadObject<UFont>(nullptr, *Path);
	if (!TargetFont)
	{
		PS_LOG_ERROR_PRINTF("Load UFont failed: %s", *Path);
		return;
	}
	UPSUserWidgetSettings *Settigns = UPSUserWidgetSettings::Get();
	if(!Settigns->CustomFont){
		return;
	}
	UFont* Font = Settigns->CustomFont;
	Font->FontCacheType = EFontCacheType::Runtime;
	Font->CompositeFont = TargetFont->CompositeFont;
}

void UOneEngineSDKPSSubsystem::ParsePsUserProfileResponse(const FString& Json, FOnePSUserProfileResponse& Out)
{
	// {"profiles":[{"accountId":"986191048552744156","onlineId":"Ray2048","avatarUrl":"http:\/\/static-resource.sp-int.community.playstation.net\/avatar_s\/WWS_E\/E2098_s.png","aboutMe":"","isOfficiallyVerified":false,"SDKuid":"0","roleId":"","serverId":"","roleName":""}],"total":1,"nextOffset":-1,"previousOffset":-1}
	TSharedPtr<FJsonObject> RootObject = MakeShareable(new FJsonObject());
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Json);
	if (FJsonSerializer::Deserialize(Reader, RootObject))
	{
		Out.Total = RootObject->GetIntegerField(TEXT("total"));
		Out.NextOffset = RootObject->GetIntegerField(TEXT("nextOffset"));
		Out.PreviousOffset = RootObject->GetIntegerField(TEXT("previousOffset"));
		TArray<TSharedPtr<FJsonValue>> JsonArray = RootObject->GetArrayField(TEXT("profiles"));
		TArray<FOnePSUserProfile> Friends;
		Out.Profiles.Empty();
		for (auto& JsonValue : JsonArray)
		{
			TSharedPtr<FJsonObject> JsonObject = JsonValue->AsObject();
			FOnePSUserProfile Friend;
			Friend.AccountId = JsonObject->GetStringField(TEXT("accountId"));
			Friend.OnlineId = JsonObject->GetStringField(TEXT("onlineId"));
			Friend.AvatarUrl = JsonObject->GetStringField(TEXT("avatarUrl"));
			Friend.AboutMe = JsonObject->GetStringField(TEXT("aboutMe"));
			Friend.bIsOfficiallyVerified = JsonObject->GetBoolField(TEXT("isOfficiallyVerified"));
			Friend.SDKuid = JsonObject->GetIntegerField(TEXT("SDKuid"));
			Friend.RoleId = JsonObject->GetStringField(TEXT("roleId"));
			Friend.ServerId = JsonObject->GetStringField(TEXT("serverId"));
			Friend.RoleName = JsonObject->GetStringField(TEXT("roleName"));
			Out.Profiles.Add(Friend);
		}
	}
}
