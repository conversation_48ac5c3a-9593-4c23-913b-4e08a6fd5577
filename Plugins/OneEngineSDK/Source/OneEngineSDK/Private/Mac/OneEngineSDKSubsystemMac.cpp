#include "OneEngineSDKSubsystem.h"
#include "OneEngineSDKMacAdapter.h"
#include "Async/Async.h"
#import <StoreKit/StoreKit.h>

#ifdef INSTALL_ONE_ENGINE_MAC_LIBRARY
#include "WMXAppKitManager.h"
#endif

#ifdef INSTALL_ONE_ENGINE_MAC_LIBRARY
static WPOneEngineTrackEventResState OneEngineStateFromResState(EOneResEventState state)
{
    switch (state) {
    case EOneResEventState::Begin:
        return WPOneEngineTrackEventResStateBegin;
        break;
    case EOneResEventState::Success:
        return WPOneEngineTrackEventResStateSuccess;
        break;
    case EOneResEventState::Failed:
        return WPOneEngineTrackEventResStateError;
        break;
            
    default:
        break;
    }
    return WPOneEngineTrackEventResStateError;
}

static EOneUnlockSafeLockResult OneUnLockStateFromiOSUnlockResult(WPOneEngineUnlockSafeLockResult unlockResult)
{
    switch (unlockResult)
    {
    case WPOneEngineUnlockSafeLockResultWaiting:
        return EOneUnlockSafeLockResult::WaitingToUnlock;
        break;
    case WPOneEngineUnlockSafeLockResultSucceeded:
        return  EOneUnlockSafeLockResult::UnlockSucceeded;
        break;
    case WPOneEngineUnlockSafeLockResultTimedOut:
        return EOneUnlockSafeLockResult::UnlockTimedOut;
        break;
    default:
        return EOneUnlockSafeLockResult::UnlockFailed;
        break;
    }
}

static FOneUserInfo WPOneUserInfoFromNative(WPOneEngineUserInfo *userInfo)
{
    FOneUserInfo Item;
    Item.UserId = FString(userInfo.userId);
    Item.Token = FString(userInfo.token);
    Item.Phone = FString(userInfo.phone);
    Item.Avatar = FString(userInfo.avatar);
    Item.UserName = FString(userInfo.name);
    Item.InheritCode = FString(userInfo.inheritCode);
    Item.bPasswordExist = userInfo.passwordExist;
    Item.CountryCode = FString(userInfo.countryCode);
    Item.Age = userInfo.age;
    Item.bIsNewCreate = userInfo.isNewFlag;
    Item.bIsAdult = userInfo.isAdult;
    Item.AgeCountryCode = FString(userInfo.ageCountryCode);
    
    TArray<FOneUserThirdInfo> ThirdList;
    for (WPOneEngineThirdInfo *thirdInfo in userInfo.thirds) {
        FOneUserThirdInfo ThirdItem;
        ThirdItem.UserId = FString(thirdInfo.userId);
        ThirdItem.ThirdId = FString(thirdInfo.thirdId);
        ThirdItem.Avatar = FString(thirdInfo.avatar);
        ThirdItem.UserName = FString(thirdInfo.name);
        ThirdItem.Email = FString(thirdInfo.email);
        ThirdItem.Type = (EOneEngineThirdType)thirdInfo.type;
        ThirdList.Add(ThirdItem);
    }
    Item.Thirds = ThirdList;
    Item.Type = (EOneEngineThirdType)userInfo.type;
    return Item;
}

void UOneEngineSDKSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
}

static bool initFlag = false;
void UOneEngineSDKSubsystem::Init(FOneInitDelegate InitDelegate)
{
	check(IsInGameThread());

	if (initFlag) {
		//防止重复调用初始化
		return;
	}
	initFlag = true;
	
	[[OneEngineSDKMacAdapter GetBridge] registerInitCallBackCompletion:^(NSInteger code, NSString *message) {
        
		int32 Code = code;
		bool  bSuccess = (Code == 0) ? true : false;
		FString Msg = FString(message);
		AsyncTask(ENamedThreads::GameThread, [InitDelegate,bSuccess, Code, Msg]()
				  {
            InitDelegate.ExecuteIfBound(bSuccess,Code, Msg);
		});
	}];
    
#ifdef KONEENGINE_REGION_MAINLAND
	if(!GConfig) return;
	FString AppId;
	FString AppKey;
	GConfig->GetString(TEXT("/Script/OneEngineSDK"), TEXT("AppID"), AppId, GGameIni);
	GConfig->GetString(TEXT("/Script/OneEngineSDK"), TEXT("AppKey"), AppKey, GGameIni);
	// UE_LOG(LogTemp, Warning, TEXT("--OneSDK-- OneSDKAppId=%s"), *AppId);
	// UE_LOG(LogTemp, Warning, TEXT("--OneSDK-- OneSDKAppKey=%s"), *AppKey);
    
	FString ChannelId = "9";
	NSString* appId_ns = AppId.GetNSString();
	NSString* appKey_ns = AppKey.GetNSString();
	NSString* configPath_ns = [[NSBundle mainBundle] pathForResource:@"OneSDKMacConfigFile.config" ofType:nil];
    
	dispatch_async(dispatch_get_main_queue(), ^{
		//初始化
	    [WPOneEngineManager shouldVerifyBundleId:NO];
		[WPOneEngineManager initWithAppId:appId_ns
								   appKey:appKey_ns
								   config:configPath_ns];

	    [WMXAppKitManager preventGlobalEventsRespondForEngine];
	});
#else
	dispatch_async(dispatch_get_main_queue(), ^{
		//初始化
		[WPOneEngineManager initWithAppId:nil
								   appKey:nil
								   config:nil];

	    [WMXAppKitManager preventGlobalEventsRespondForEngine];
	});
#endif
}

EOneEngineSDKRegionType UOneEngineSDKSubsystem::GetRegionType()
{
    WPOneEngineRegionType Type = [WPOneEngineManager regionType];
    return static_cast<EOneEngineSDKRegionType>(Type);
}

void UOneEngineSDKSubsystem::Login()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager login];
    });
}

void UOneEngineSDKSubsystem::GetUserTokenList(FOneFetchUserTokenListDelegate OnGetTokenListDelegate)
{
    auto GetTokenListResultLambda = [OnGetTokenListDelegate](bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)
    {
        OnGetTokenListDelegate.ExecuteIfBound(bSucceed,Code, Msg, TokenList );
    };
    GetUserTokenListLambda(GetTokenListResultLambda);
}

void UOneEngineSDKSubsystem::GetUserTokenListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)> &OnFinishedLambda)
{
    TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)> CopiedLambda = OnFinishedLambda;
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager getDeviceTokensWithComplete:^(id responseObject, NSError *error) {
            
            TArray<FOneUserInfo> UserTokenList;
            if (!error) {
                for (WPOneEngineUserInfo *userInfo in responseObject) {
                    FOneUserInfo Item = WPOneUserInfoFromNative(userInfo);
                    UserTokenList.Add(Item);
                }
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, UserTokenList]() 
                {
                    CopiedLambda(true, 0,"",UserTokenList);
                });
            } else {
                int32 Code = error.code;
                FString Msg = error.localizedDescription;
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, Code,Msg, UserTokenList]()
                          {
                    CopiedLambda(false, Code, Msg, UserTokenList);
                });
            }
        }];
    });
}

void UOneEngineSDKSubsystem::TokenLogin(const FString& Token, const FString& Uid, EOneEngineThirdType ThirdType)
{
    NSString* token_ns = Token.GetNSString();
    NSString* uid_ns = Uid.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager tokenLogin:token_ns uid:uid_ns loginType:static_cast<WPOneEnginePlatformType>(ThirdType) complete:^(id responseObject, NSError *error) {
            if (!error) {
                NSString *userId = ((WPOneEngineUserInfo *)responseObject).userId;
                FOneEngineSDKHelper::OnLoginResultDelegate(true, 0,"",FString(userId));
            } else {
                int32 Code = error.code;
                FOneEngineSDKHelper::OnLoginResultDelegate(false, Code,FString(error.localizedDescription),"");
            }
        }];
    });
}

void UOneEngineSDKSubsystem::ThirdLogin(EOneEngineThirdType ThirdType, bool bForcedLogin)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager thirdLoginWithPlatformType:static_cast<WPOneEnginePlatformType>(ThirdType) isForceLogin:bForcedLogin complete:^(id responseObject, NSError *error) {
            if (!error) {
                NSString *userId = ((WPOneEngineUserInfo *)responseObject).userId;
                FOneEngineSDKHelper::OnLoginResultDelegate(true, 0,"",FString(userId));
            } else {
                int32 Code = error.code;
                FOneEngineSDKHelper::OnLoginResultDelegate(false, Code,FString(error.localizedDescription),"");
            }
        }];
    });
}

void UOneEngineSDKSubsystem::GuestLogin()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager guestLoginWithComplete:^(id responseObject, NSError *error) {
            if (!error) {
                NSString *userId = ((WPOneEngineUserInfo *)responseObject).userId;
                FOneEngineSDKHelper::OnLoginResultDelegate(true, 0,"",FString(userId));
            } else {
                int32 Code = error.code;
                FOneEngineSDKHelper::OnLoginResultDelegate(false, Code,FString(error.localizedDescription),"");
            }
        }];
    });
}

void UOneEngineSDKSubsystem::SwitchAccount()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager switchAccount];
    });
}

void UOneEngineSDKSubsystem::Pay(const FOnePaymentInfo& PaymentInfo)
{
    NSString* price = PaymentInfo.Price.GetNSString();
    NSString* orderNum = PaymentInfo.OrderId.GetNSString();
    NSString* productName = PaymentInfo.ProductName.GetNSString();
    NSString* gameServerId = PaymentInfo.GameServerId.GetNSString();
    NSString* gameServerName = PaymentInfo.ServerName.GetNSString();
    NSString* ext = PaymentInfo.ExtInfo.GetNSString();
    NSString* productId = PaymentInfo.ProductId.GetNSString();
    NSString* productCount = PaymentInfo.ProductCount.GetNSString();
    NSString* roleId = PaymentInfo.RoleId.GetNSString();
    NSString* roleName = PaymentInfo.RoleName.GetNSString();
    NSString* paySuccessUrl = PaymentInfo.PaySuccessUrl.GetNSString();
    
    WPOneEnginePurchaseInfo *purchaseInfo = [[WPOneEnginePurchaseInfo alloc] init];
    purchaseInfo.productPrice = [price integerValue];
    purchaseInfo.orderId = orderNum;
    purchaseInfo.roleName = roleName;
    purchaseInfo.productName = productName;
    purchaseInfo.gameServerId = [gameServerId integerValue];
    purchaseInfo.gameServerName = gameServerName;
    purchaseInfo.ext = ext;
    purchaseInfo.productId = productId;
    purchaseInfo.productCount = [productCount integerValue];
    purchaseInfo.roleId = roleId;
    purchaseInfo.paySuccessUrl = paySuccessUrl;
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager purchaseWithInfo:purchaseInfo];
    });
}

FOneUserInfo UOneEngineSDKSubsystem::GetUserInfo()
{
    WPOneEngineUserInfo *userInfo = [WPOneEngineManager getUserInfo];
    FOneUserInfo UserInfo = WPOneUserInfoFromNative(userInfo);
    return UserInfo;
}

// 获取档位信息
void UOneEngineSDKSubsystem::GetProductList(const TArray<FString>& ProductIds, FOneProductInfoDelegate ProductResultDelegate)
{
    NSMutableArray* productIdArray = [[NSMutableArray alloc] init];
    for (auto item : ProductIds)
    {
        [productIdArray addObject:item.GetNSString()];
    }
    
    [WPOneEngineManager getProductList:productIdArray
                              complete:^(id responseObject, NSError *error) {
        
        TArray<FOneProductInfo> ProductResult;
        if (!error) {
            for (WPOneEngineProductInfo  *product in responseObject) {
                FOneProductInfo item;
                item.ProductId = FString(product.productId);
                item.Price = FString(product.price);
                item.Currency = FString(product.currency);
                item.SymbolPrice = FString(product.symbolPrice);
                item.Title = FString(product.title);
                item.Desc = FString(product.desc);
                ProductResult.Add(item);
            }
            
            AsyncTask(ENamedThreads::GameThread, [ProductResultDelegate,ProductResult]()
                      {
                ProductResultDelegate.ExecuteIfBound(true, 0, ProductResult);
            });
            
        }
        else {
            int32 Code = error.code;
            AsyncTask(ENamedThreads::GameThread, [ProductResultDelegate, Code, ProductResult]()
                      {
                ProductResultDelegate.ExecuteIfBound(false, Code, ProductResult);
            });
        }
    }];
}

// 角色事件打点
void UOneEngineSDKSubsystem::TrackEventRoleCreate(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
    NSString* roleId = RoleInfo.RoleId.GetNSString();
    NSString* serverId = RoleInfo.ServerId.GetNSString();
    NSString* level = RoleInfo.Level.GetNSString();
    NSString* vip = RoleInfo.Vip.GetNSString();
    NSString* ip_ns = Ip.GetNSString();
    NSString* port_ns = Port.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventRoleCreateRoleId:roleId
                                          serverId:serverId
                                             level:level
                                               vip:vip
                                                ip:ip_ns
                                              port:port_ns];
    });
}

void UOneEngineSDKSubsystem::TrackEventRoleLoginSucceed(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
    NSString* roleId = RoleInfo.RoleId.GetNSString();
    NSString* serverId = RoleInfo.ServerId.GetNSString();
    NSString* roleName = RoleInfo.RoleName.GetNSString();
    NSString* serverName = RoleInfo.ServerName.GetNSString();
    NSString* level = RoleInfo.Level.GetNSString();
    NSString* vip = RoleInfo.Vip.GetNSString();
    NSString* ip_ns = Ip.GetNSString();
    NSString* port_ns = Port.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventRoleLoginRoleId:roleId
                                            roleName:roleName
                                            serverId:serverId
                                            serverName:serverName
                                            level:level
                                            vip:vip
                                            ip:ip_ns
                                            port:port_ns];
    });
}

void UOneEngineSDKSubsystem::TrackEventRoleLoginError(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port, const FString& Code, const FString& Msg)
{
    NSString* roleId = RoleInfo.RoleId.GetNSString();
    NSString* serverId = RoleInfo.ServerId.GetNSString();
    NSString* level = RoleInfo.Level.GetNSString();
    NSString* vip = RoleInfo.Vip.GetNSString();
    NSString* ip_ns = Ip.GetNSString();
    NSString* port_ns = Port.GetNSString();
    NSString* errorCode = Code.GetNSString();
    NSString* errorMsg = Msg.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventRoleLoginErrorRoleId:roleId
                                              serverId:serverId
                                                 level:level
                                                   vip:vip
                                                    ip:ip_ns
                                                  port:port_ns
                                             errorCode:errorCode
                                              errorMsg:errorMsg];
    });
}

void UOneEngineSDKSubsystem::TrackEventRoleLevelUp(const FOneRoleInfo& RoleInfo)
{
    NSString* roleId = RoleInfo.RoleId.GetNSString();
    NSString* serverId = RoleInfo.ServerId.GetNSString();
    NSString* level = RoleInfo.Level.GetNSString();
    NSString* vip = RoleInfo.Vip.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventRoleUpdateRoleId:roleId
                                          serverId:serverId
                                             level:level
                                               vip:vip];
    });
}


void UOneEngineSDKSubsystem::TrackEventRoleLogout(const FOneRoleInfo& RoleInfo)
{
    NSString* roleId = RoleInfo.RoleId.GetNSString();
    NSString* serverId = RoleInfo.ServerId.GetNSString();
    NSString* level = RoleInfo.Level.GetNSString();
    NSString* vip = RoleInfo.Vip.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventRoleLogoutRoleId:roleId
                                          serverId:serverId
                                             level:level
                                               vip:vip];
    });
}

// 资源事件打点
void UOneEngineSDKSubsystem::GameResReqEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
    NSString* url_ns = Url.GetNSString();
    NSString* errorCode = ErrorCode.GetNSString();
    NSString* errorMsg = ErrorMsg.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager gameResReqEvent:OneEngineStateFromResState(State)
                                    url:url_ns
                              errorCode:errorCode
                               errorMsg:errorMsg];
    });
}

void UOneEngineSDKSubsystem::GameUpdateAssetEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
    NSString* url_ns = Url.GetNSString();
    NSString* errorCode = ErrorCode.GetNSString();
    NSString* errorMsg = ErrorMsg.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager gameUpdateAssetEvent:OneEngineStateFromResState(State)
                                         url:url_ns
                                   errorCode:errorCode
                                    errorMsg:errorMsg];
    });
}

void UOneEngineSDKSubsystem::GameResDecEvent(EOneResEventState State, const FString& ErrorMsg)
{
    NSString* errorMsg = ErrorMsg.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager gameResDecEvent:OneEngineStateFromResState(State) errorMsg:errorMsg];
    });
}

void UOneEngineSDKSubsystem::GameGetServerListEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
    NSString* url_ns = Url.GetNSString();
    NSString* errorCode = ErrorCode.GetNSString();
    NSString* errorMsg = ErrorMsg.GetNSString();

    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager gameGetServerListEvent:OneEngineStateFromResState(State)
                                           url:url_ns
                                     errorCode:errorCode
                                      errorMsg:errorMsg];
    });
}

// 自定义事件打点
void UOneEngineSDKSubsystem::TrackEvent(const FString& Name, const TMap<FString, FString>& Payload)
{
    NSMutableDictionary* dic = [NSMutableDictionary dictionaryWithCapacity:Payload.Num()];
    NSString* eventName_NS = Name.GetNSString();
    for (auto& KVP : Payload)
    {
        NSString* key = KVP.Key.GetNSString();
        NSString* val = KVP.Value.GetNSString();
        dic[key] = val;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackCustomEvent:eventName_NS
                                    info:dic];
    });
}

void UOneEngineSDKSubsystem::TrackEventAD(const FString& Name, const TMap<FString, FString>& Payload)
{
    NSMutableDictionary* dic = [NSMutableDictionary dictionaryWithCapacity:Payload.Num()];
    for (auto& KVP : Payload)
    {
        NSString* key = KVP.Key.GetNSString();
        NSString* val = KVP.Value.GetNSString();
        dic[key] = val;
    }
    NSString* eventName_NS = Name.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackCustomEventAD:eventName_NS
                                      info:dic];
    });
}

//进入游戏场景打点
void UOneEngineSDKSubsystem::TrackEventEnterGameScene(const FString& SceneName,int32 Period,const TMap<FString,FString>& HintMap)
{
    NSString* SceneName_NS = SceneName.GetNSString();
    NSMutableDictionary* dic = [NSMutableDictionary dictionaryWithCapacity:HintMap.Num()];
    for (auto& KVP : HintMap)
    {
        NSString* key = KVP.Key.GetNSString();
        NSString* val = KVP.Value.GetNSString();
        dic[key] = val;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventEnterScene:SceneName_NS
                                      period:Period
                                  attributes:dic];
    });
}

//退出游戏打点
void UOneEngineSDKSubsystem::TrackEventExitGameScene()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventExitScene:@{}];
    });
}

//设备打点额外信息
void UOneEngineSDKSubsystem::TrackEventAddExtraDeviceInfo(const TMap<FString,FString>& ExtraDeviceInfo)
{
    NSMutableDictionary* dic = [NSMutableDictionary dictionaryWithCapacity:ExtraDeviceInfo.Num()];
    for (auto& KVP : ExtraDeviceInfo)
    {
        NSString* key = KVP.Key.GetNSString();
        NSString* val = KVP.Value.GetNSString();
        dic[key] = val;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager trackEventAddExtraDeviceInfo:dic];
    });
}

bool UOneEngineSDKSubsystem::IsLoggedIn()
{
    return [WPOneEngineManager isLogined];
}

void UOneEngineSDKSubsystem::SetUpConfigAppID(const FString& AppId)
{
    NSString* AppId_NS = AppId.GetNSString();
    [WPOneEngineManager setupConfigAppID:AppId_NS];
}

FString UOneEngineSDKSubsystem::GetAppId()
{
    return FString([WPOneEngineManager getAppId]);
}

void UOneEngineSDKSubsystem::EnableDebugMode(bool Enable)
{
    [WPOneEngineManager enableDebugMode:Enable];
}

bool UOneEngineSDKSubsystem::IsDebugMode()
{
    return [WPOneEngineManager isDebugMode];
}

void UOneEngineSDKSubsystem::OpenUserCenter()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        //初始化
        [WPOneEngineManager enterUserCenter];
    });
}



void UOneEngineSDKSubsystem::OpenComplianceOnWebView()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager openComplianceOnWebView];
    });
}

void UOneEngineSDKSubsystem::EnterAccountCancellation()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager enterAccountCancellation];
    });
}

FString UOneEngineSDKSubsystem::GetChannelId()
{
    return FString::FromInt([WPOneEngineManager channelId]);
}

FString UOneEngineSDKSubsystem::GetChannelMediaId()
{
    return FString([WPOneEngineManager getChannelMediaId]);
}

int32 UOneEngineSDKSubsystem::GetPlatformOS()
{
    return [WPOneEngineManager getPlatformOS];;
}

void UOneEngineSDKSubsystem::GetChannelPlatform(FOneGetPlatformDelegate OnGetPlatformResultDelegate)
{
    auto PlatformInfoResultLambda = [OnGetPlatformResultDelegate](bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)
    {
        OnGetPlatformResultDelegate.ExecuteIfBound(bSucceed,Code,Msg,Platform);
    };
    GetChannelPlatformLambda(PlatformInfoResultLambda);
}

void UOneEngineSDKSubsystem::GetChannelPlatformLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)> &OnFinishedLambda)
{
    TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)> CopiedLambda = OnFinishedLambda;
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager getPlatformWithComplete:^(id responseObject, NSError *error) {
            if (error)
            {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                FString Result = "";
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, Code, Msg, Result]()
                {
                      CopiedLambda(false, Code, Msg, Result);
                });
            }
            else
            {
                FString Result = FString(responseObject);
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, Result]()
                {
                      CopiedLambda(true, 0, "", Result);
                });
            } 
        }];
    });
}

void UOneEngineSDKSubsystem::GetUserLocationInfo(FOneUserLocationInfoDelegate LocationInfoDelegate)
{
    auto LocationInfoResultLambda = [LocationInfoDelegate](const FOneUserLocationInfo& LocationInfo)
    {
        LocationInfoDelegate.ExecuteIfBound(LocationInfo);
    };
    GetUserLocationInfoLambda(LocationInfoResultLambda);
}

void UOneEngineSDKSubsystem::GetUserLocationInfoLambda(const TFunction<void(const FOneUserLocationInfo& LocationInfo)> &OnFinishedLambda)
{
    TFunction<void(const FOneUserLocationInfo& LocationInfo)> CopiedLambda = OnFinishedLambda;
    dispatch_async(dispatch_get_main_queue(), ^{
            [WPOneEngineManager getUserLocationInfo:^(WPOneEngineEventLocationInfo * _Nonnull result) {
                FOneUserLocationInfo LocationInfo;
                if (result) {
                    LocationInfo.CountryAbbr =  FString(result.countryAbbr);
                    LocationInfo.Country =  FString(result.country);
                    LocationInfo.Province =  FString(result.province);
                    LocationInfo.City =  FString(result.city);
                    LocationInfo.CountryCode =  FString(result.countryCode);
                    LocationInfo.IP =  FString(result.ip);
                }
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, LocationInfo]()
                          {
                    CopiedLambda(LocationInfo);
                });
            }];
    });
}

void UOneEngineSDKSubsystem::GetDeviceInfo(FOneGetDeviceInfoDelegate Delegate)
{
    auto GetDeviceInfoResultLambda = [Delegate](const FOneDeviceInfo& DeviceInfo)
    {
        Delegate.ExecuteIfBound(DeviceInfo);
    };
    GetDeviceInfoLambda(GetDeviceInfoResultLambda);
}

void UOneEngineSDKSubsystem::GetDeviceInfoLambda(const TFunction<void(const FOneDeviceInfo& DeviceInfo)> &OnFinishedLambda)
{
    TFunction<void(const FOneDeviceInfo& DeviceInfo)> CopiedLambda = OnFinishedLambda;
    dispatch_async(dispatch_get_main_queue(), ^{
        WPOneEngineEventDeviceInfo *deviceInfo = [WPOneEngineManager getDeviceInfo];
        FOneDeviceInfo DeviceInfo;
        DeviceInfo.DeviceId = FString(deviceInfo.deviceId);
        DeviceInfo.DeviceSys = FString(deviceInfo.deviceSys);

        TMap<FString, FString> ExtParams;
        for (NSString *itemKey in deviceInfo.ext)
        {
           NSString *itemValue = deviceInfo.ext[itemKey];
           ExtParams.Add(FString(itemKey), FString(itemValue));
        }
        DeviceInfo.Ext = ExtParams;
        AsyncTask(ENamedThreads::GameThread, [CopiedLambda, DeviceInfo]()
                          {
                    CopiedLambda(DeviceInfo);
                });
    });
}

void UOneEngineSDKSubsystem::FetchAntiAddictionInfo(FOneFetchAntiAddictionInfoDelegate OnFetchAntiAddictionInfo)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager preLoginWithUserId:@"" completion:^(WPOneEngineFMBaseInfo * _Nullable info, NSError *error) {
            FOneAntiAddictionInfo FAntiAddictionInfo;
            if (!error && info) {
                FAntiAddictionInfo.AppID = FString(@(info.appId).stringValue);
                FAntiAddictionInfo.UserId = FString(info.userId);
                FAntiAddictionInfo.Status = (int32_t)info.status;
                FAntiAddictionInfo.HeartbeatInterval = (int32_t)info.heartbeatInterval;
                FAntiAddictionInfo.BannedType = (int32_t)info.bannedType;
                FAntiAddictionInfo.BannedReason = FString(info.bannedReason);
                FAntiAddictionInfo.BreakNotice = FString(info.breakNotice);
                FAntiAddictionInfo.Realuser = (int32_t)info.realUser;
                FAntiAddictionInfo.CivicType = (int32_t)info.civicType;
                FAntiAddictionInfo.Age = (int32_t)info.age;
                FAntiAddictionInfo.Gender = (int32_t)info.gender;
                FAntiAddictionInfo.AccountType = (int32_t)info.accountType;
                FAntiAddictionInfo.RequestIp = FString(info.requestIp);
                FAntiAddictionInfo.DayOnlineDuration = (int32_t)info.dayOnlineDuration;
            }
            
            AsyncTask(ENamedThreads::GameThread, [OnFetchAntiAddictionInfo,FAntiAddictionInfo]()
                      {
                OnFetchAntiAddictionInfo.ExecuteIfBound(FAntiAddictionInfo);
            });
        }];
    });
}

void UOneEngineSDKSubsystem::StartAntiAddictionNotify(const FString &ServerId, const FString &RoleId)
{
    NSString* ServerId_ns = ServerId.GetNSString();
    NSString* RoleId_ns = RoleId.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager roleLoginWithUserId:@""
                                       serverId:[ServerId_ns integerValue]
                                         roleId:RoleId_ns
                                     completion:^(WPOneEngineFMBaseInfo * _Nullable info, NSError *error) {
            if (info.status != 0) {
                // 会触发通知回调，回调里会处理
            } else {
                // do nothing
            }
        }];
    });
}

void UOneEngineSDKSubsystem::StopAntiAddictionNotify()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager roleLogout];
    });
}

void UOneEngineSDKSubsystem::SetShowDefaultActivationResultToast(bool bShow)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager setShowActivationResultToast:bShow];
    });
}

void UOneEngineSDKSubsystem::DisplayCDKeyDialog(FOneGenericResultDelegate OnCDKeyActivateResult, const FString &ServerId)
{
    NSString* ServerId_ns = ServerId.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [[OneEngineSDKMacAdapter GetBridge] showActCodeViewWithServerId:ServerId_ns completion:^(NSInteger code, NSString *message) {
            int32 Code = code;
            bool bSucceed = (code == 0) ? true : false;
            FString Msg = FString(message);
            AsyncTask(ENamedThreads::GameThread, [OnCDKeyActivateResult, bSucceed, Code, Msg]()
                      {
                OnCDKeyActivateResult.ExecuteIfBound(bSucceed,Code, Msg);
            });
        }];
    });
}

void UOneEngineSDKSubsystem::QueryActCode(const FString& ServerId, FOneQueryActCodeResultDelegate OnQueryActCodeResultDelegate)
{
    auto OnQueryActCodeResultLambda = [OnQueryActCodeResultDelegate](bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)
    {
        OnQueryActCodeResultDelegate.ExecuteIfBound(bSucceed, bNeedActCode, ActCodePrompt, Code, ErrorMsg);
    };
    QueryActCodeLambda(ServerId,OnQueryActCodeResultLambda);
}

void UOneEngineSDKSubsystem::QueryActCodeLambda(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> &OnFinishedLambda)
{
    TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> CopiedLambda = OnFinishedLambda;
    NSString* ServerId_ns = ServerId.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager queryActCodeWithServerId:ServerId_ns
                                          completion:^(NSDictionary *responseObject, NSError *error){
            if (error)
            {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                FString Tips = "";
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, Code, Msg, Tips]()
                {
                      CopiedLambda(false, false, Tips, Code, Msg);
                });
            }
            else
            {
                bool isShowActCode = [[responseObject valueForKey:@"isShowActCode"] boolValue];
                NSString *tips_ns = [responseObject valueForKey:@"tips"];
                FString Tips = FString(tips_ns);
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, isShowActCode, Tips]()
                {
                      CopiedLambda(true, isShowActCode, Tips, 0, "");
                });
            }
        }];
    });
}
void UOneEngineSDKSubsystem::QueryUserActiveQualification(const FString& ServerId, FOneQueryUserActiveQualificationResultDelegate OneQueryUserActiveQualificationResultDelegate)
{
    auto OnQueryUserActiveQualificationResultLambda = [OneQueryUserActiveQualificationResultDelegate](bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)
    {
        OneQueryUserActiveQualificationResultDelegate.ExecuteIfBound(bSucceed, Code, ErrorMsg, QualificationInfo);
    };
    QueryUserActiveQualificationLambda(ServerId,OnQueryUserActiveQualificationResultLambda);
}

void UOneEngineSDKSubsystem::QueryUserActiveQualificationLambda(const FString& ServerId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)> &OnFinishedLambda)
{
    TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)> CopiedLambda = OnFinishedLambda;
    NSString* ServerId_ns = ServerId.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager queryUserActiveQualificationWithServerId:ServerId_ns
                                                complete:^(WPOneEngineQualificationInfo *info, NSError *error){
            FOneActiveQualificationInfo ActiveQualificationInfo;
            if (error)
            {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, Code, Msg, ActiveQualificationInfo]()
                {
                      CopiedLambda(false, Code, Msg, ActiveQualificationInfo);
                });
            }
            else
            {
                ActiveQualificationInfo.Status = (int32_t)info.status;
                ActiveQualificationInfo.DeviceTotal = (int32_t)info.deviceTotal;
                ActiveQualificationInfo.WhiteList = (int32_t)info.whiteList;
                ActiveQualificationInfo.DeviceLogged = (int32_t)info.deviceLogged;
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, ActiveQualificationInfo]()
                {
                      CopiedLambda(true, 0, "",ActiveQualificationInfo);
                });
            }
        }];
    });
}

void UOneEngineSDKSubsystem::ActivateDevice(const FString& ServerId, FOneActivateDeviceResultDelegate OneActivateDeviceResultDelegate)
{
    auto OnActivateDeviceResultLambda = [OneActivateDeviceResultDelegate](bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)
    {
        OneActivateDeviceResultDelegate.ExecuteIfBound(bSucceed, bNeedActCode, ActCodePrompt, Code, ErrorMsg);
    };
    ActivateDeviceLambda(ServerId,OnActivateDeviceResultLambda);
}

void UOneEngineSDKSubsystem::ActivateDeviceLambda(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> &OnFinishedLambda)
{
    TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> CopiedLambda = OnFinishedLambda;
    NSString* ServerId_ns = ServerId.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager activateDeviceWithServerId:ServerId_ns
                                            complete:^(NSDictionary *responseObject, NSError *error){
            if (error)
            {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                FString Tips = "";
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, Code, Msg, Tips]()
                {
                      CopiedLambda(false, false, Tips, Code, Msg);
                });
            }
            else
            {
                bool isShowActCode = [[responseObject valueForKey:@"isShowActCode"] boolValue];
                NSString *tips_ns = [responseObject valueForKey:@"tips"];
                FString Tips = FString(tips_ns);
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, isShowActCode, Tips]()
                {
                      CopiedLambda(true, isShowActCode, Tips, 0, "");
                });
            }
        }];
    });
}

void UOneEngineSDKSubsystem::ExchangeActCode(const FString& ServerId, const FString& ActCode, FOneGenericResultDelegate GenericResultDelegate)
{
    auto ExchangeActCodeResultLambda = [GenericResultDelegate](bool bSuccess, int32 Code, const FString& Msg)
    {
        GenericResultDelegate.ExecuteIfBound(bSuccess, Code, Msg);
    };
    ExchangeActCodeLambda(ServerId,ActCode, ExchangeActCodeResultLambda);
}

void UOneEngineSDKSubsystem::ExchangeActCodeLambda(const FString& ServerId, const FString& ActCode, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> &OnFinishedLambda)
{
    TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> CopiedLambda = OnFinishedLambda;
    NSString* ServerId_ns = ServerId.GetNSString();
    NSString* ActCode_ns = ActCode.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager exchangeActCodeWithServerId:ServerId_ns
                                                actCode:ActCode_ns
                                             completion:^(NSDictionary *responseObject, NSError *error){
            if (error)
            {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, Code, Msg]()
                {
                      CopiedLambda(false, Code, Msg);
                });
            }
            else
            {
                int Code = [[responseObject objectForKey:@"code"] intValue];
                NSString *msg = [responseObject objectForKey:@"message"] ?: @"激活成功";
                FString Msg = FString(msg);
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, Code, Msg]()
                {
                      CopiedLambda(true, Code, Msg);
                });
            }
        }];
    });
}

void UOneEngineSDKSubsystem::RedeemCouponCode(const FString &CouponCode,  const FString &ServerId, const FString &RoleId, const FString &RoleLevel, const FString &VipLevel, const TMap<FString, FString> &ExtraInfo, FOneGenericResultDelegate OnRedeemCouponResult)
{
    auto OnRedeemCouponLambda = [OnRedeemCouponResult](bool bSuccess, int32 Code, const FString& Msg)
    {
        OnRedeemCouponResult.ExecuteIfBound(bSuccess, Code, Msg);
    };
    RedeemCouponCodeLambda(CouponCode, ServerId, RoleId, RoleLevel, VipLevel, ExtraInfo, OnRedeemCouponLambda);
}

void UOneEngineSDKSubsystem::RedeemCouponCodeLambda(const FString& CouponCode, const FString& ServerId, const FString& RoleId, const FString& RoleLevel, const FString& VipLevel, const TMap<FString, FString>& ExtraInfo,  const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> &OnFinishedLambda)
{
    TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> CopiedLambda = OnFinishedLambda;
    NSString* CouponCode_ns = CouponCode.GetNSString();
    NSString* ServerId_ns = ServerId.GetNSString();
    NSString* RoleId_ns = RoleId.GetNSString();
    NSString* RoleLevel_ns = RoleLevel.GetNSString();
    NSString* VipLevel_ns = VipLevel.GetNSString();
    NSMutableDictionary* ExtraInfo_ns = [[NSMutableDictionary alloc]initWithCapacity:ExtraInfo.Num()];
    for (auto& KVP : ExtraInfo)
    {
        NSString* field = KVP.Key.GetNSString();
        NSString* value = KVP.Value.GetNSString();
        [ExtraInfo_ns setObject : value forKey : field];
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        [[OneEngineSDKMacAdapter GetBridge] verifyRedeemCodeWithRoleId:RoleId_ns
                                                              serverId:ServerId_ns
                                                                 level:[RoleLevel_ns integerValue]
                                                                   vip:[VipLevel_ns integerValue]
                                                            redeemCode:CouponCode_ns
                                                             extraInfo:ExtraInfo_ns
                                                            completion:^(NSInteger code, NSString *message) {
            int32 Code = code;
            FString Msg = FString(message);
            bool bSucceed = (code == 0) ? true : false;                                                    
            AsyncTask(ENamedThreads::GameThread, [CopiedLambda,bSucceed, Code, Msg]()
                      {
                CopiedLambda(bSucceed, Code, Msg);
            });
        }];
    });
}

void UOneEngineSDKSubsystem::FetchUserRoleInfoList(FOneFetchUserRoleInfoListDelegate OnFetchUserRoleListDelegate, const FString& ServerId)
{
    auto OnFetchUserRoleListLamdba = [OnFetchUserRoleListDelegate](bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)
    {
        OnFetchUserRoleListDelegate.ExecuteIfBound(bSucceed, Code, Msg, RoleList);
    };
    FetchUserRoleInfoListLambda(OnFetchUserRoleListLamdba,ServerId);
}

void UOneEngineSDKSubsystem::FetchUserRoleInfoListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)> &OnFinishedLambda, const FString& ServerId)
{
    TFunction<void(bool, int32, const FString&,  const  TArray<FOneURCRoleInfo>&)> CopiedLambda = OnFinishedLambda;
    NSString* ServerId_ns = ServerId.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager getUserRoleInfoListWithServerId:ServerId_ns completion:^(NSArray<WPOneEngineRoleInfo *> *roleInfoList, NSError *error) {
            TArray<FOneURCRoleInfo> RoleInfoArray;
            if (!error) {
                for (WPOneEngineRoleInfo *model in roleInfoList) {
                    FOneURCRoleInfo RoleItem;
                    RoleItem.UserId = FString(model.userId);
                    RoleItem.RoleId = FString(model.roleId);
                    RoleItem.RoleName = FString(model.roleName);
                    RoleItem.Level = FString(@(model.level).stringValue);
                    RoleItem.ServerId = FString(model.serverId);
                    RoleItem.ServerName = FString(model.serverName);
                    RoleItem.Gender = FString(@(model.gender).stringValue);
                    RoleItem.Occupation = FString(@(model.occupation).stringValue);
                    RoleItem.LastLoginTime = FString(model.lastLoginTime);
                    RoleInfoArray.Emplace(RoleItem);
                }
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, RoleInfoArray]()
                          {
                    CopiedLambda(true, 0, "",RoleInfoArray);
                });
            } else {
                int32 Code = error.code;
                FString ErrorMsg = FString(error.localizedDescription);
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, Code,ErrorMsg,RoleInfoArray]()
                          {
                    CopiedLambda(false, Code, ErrorMsg,RoleInfoArray);
                });
            }
        }];
    });
}

void UOneEngineSDKSubsystem::Bind(FOneBindResultDelegate StartBindPhoneDelegate,EOneEngineThirdType BindType)
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager startBindWithType:static_cast<WPOneEnginePlatformType>(BindType)  completion:^(WPOneEnginePlatformType bindType, id  _Nullable responseObject, NSError * _Nullable error) {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                bool bSucceed = (Code == 0) ? true : false;
                FOneUserInfo UserInfo = GetUserInfo();
            AsyncTask(ENamedThreads::GameThread, [StartBindPhoneDelegate, bSucceed,Code,Msg,BindType,UserInfo]()
                           {
                StartBindPhoneDelegate.ExecuteIfBound(bSucceed, Code, Msg, BindType, UserInfo);
                 });
        }];
    });
}

void UOneEngineSDKSubsystem::UserAuthentication(FOneUserAuthenticationResultDelegate OnUserAuthenticationResultDelegate)
{
    dispatch_async(dispatch_get_main_queue(), ^{
         [WPOneEngineManager userAuthenticationWithCompletion:^(NSInteger result, NSError *error) {
            if (error)
            {
                int32 Code = error.code;
                int32 AuthResult = result;
                FString Msg = FString(error.localizedDescription);
                AsyncTask(ENamedThreads::GameThread, [OnUserAuthenticationResultDelegate, Code, AuthResult, Msg]()
                {
                      if (Code == -12) 
                      {
                          // 用户取消操作
                          OnUserAuthenticationResultDelegate.ExecuteIfBound(true, AuthResult, false, "cancel authResult");
                      }
                      else
                      {
                          OnUserAuthenticationResultDelegate.ExecuteIfBound(false, AuthResult, true, Msg);
                      }
                });
            }
            else
            {
                int32 AuthResult = result;
                AsyncTask(ENamedThreads::GameThread, [OnUserAuthenticationResultDelegate, AuthResult]()
                {
                    OnUserAuthenticationResultDelegate.ExecuteIfBound(true, AuthResult, false, "");
                });
            }
           
        }];

    });
}

// 打开AIHelp客服
void UOneEngineSDKSubsystem::OpenAIHelp(EOneAIHelpType Type, const FString& RoleId, const FString& ServerId, const FString& RoleName)
{
    NSString* RoleId_ios = RoleId.GetNSString();
    NSString* ServerId_ios = ServerId.GetNSString();
    NSString* RoleName_ios = RoleName.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^ {
        [WPOneEngineManager openAIHelpWithType:(WPOneEngineAIHelpServiceType)Type
                                       roleId : RoleId_ios
                                     roleName : RoleName_ios
                                     serverId : ServerId_ios
        ];
    });
}

// 打开自定义客服
void UOneEngineSDKSubsystem::OpenCustomerService(const FString& RoleID, const FString& RoleName, const FString& ServerID)
{
    NSString *RoleId_ios = RoleID.GetNSString();
    NSString *RoleName_ios = RoleName.GetNSString();
    NSString *ServerID_ios = ServerID.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager openCustomerServiceWithRoleId:RoleId_ios roleName:RoleName_ios serverId:ServerID_ios];
    });
}
void UOneEngineSDKSubsystem::TranslateLambda(const FString& Text,const TFunction<void(bool bSucceed, const FString& Result,const FString& ErrorMsg)> &OnFinishedLambda)
{
    TFunction<void(bool, const FString&, const FString&)> CopiedLambda = OnFinishedLambda;
    [WPOneEngineManager translateBySdk:Text.GetNSString()
                               completion : ^ (NSString * translated, NSError * error) {
            if (error) {
                FString Msg = error.localizedDescription;
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, Msg]()
                          {
                    CopiedLambda(false, "", Msg);
                });

            }
            else {
                FString Result = FString(translated);
                AsyncTask(ENamedThreads::GameThread, [CopiedLambda, Result]()
                          {
                    CopiedLambda(true, Result, "");
                });

            }
        }];
}
// 文本翻译
void UOneEngineSDKSubsystem::Translate(const FString& Text, FOneTranslateResultDelegate Callback)
{
    auto TranslateResultLambda = [Callback](bool bSucceed, const FString& Result,const FString& ErrorMsg)
    {
        Callback.ExecuteIfBound(bSucceed, Result, ErrorMsg);
    };
    TranslateLambda(Text, TranslateResultLambda);
}

// 设置语言
void UOneEngineSDKSubsystem::SetLanguage(const FString& Code)
{
    [WPOneEngineManager setLanguage:Code.GetNSString()];
}

// 获取本地语言列表
TArray<FString> UOneEngineSDKSubsystem::GetSupportedLanguageCodeList()
{
    TArray<FString> Result;
    NSArray<NSString*>* LanguageList = [WPOneEngineManager getLocalLanguages];
    for (NSString* code in LanguageList) {
        Result.Emplace(code);
    }
    return Result;
}

//是否检测BundleId(iOS专有接口)
void UOneEngineSDKSubsystem::ShouldVerifyBundleId(bool bShouldVerify)
{
    [WPOneEngineManager shouldVerifyBundleId:bShouldVerify];
}

//获取审核开关(iOS专有接口)
bool UOneEngineSDKSubsystem::ExaminStatus()
{
    return [WPOneEngineManager examinStatus];
}

void UOneEngineSDKSubsystem::GetIpInfo(const FString& Ip,FOnGetIpInfoResultDelegate Delegate)
{
    NSString* IP_ns = Ip.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager getLocationInfoWithIp:IP_ns completion:^(WPOneEngineIpLocationInfo * _Nullable locationInfo, NSError * _Nullable error) {
            FUserIpInfo IPLocationInfo;
            if(locationInfo && !error) {
                IPLocationInfo.Attribution = FString(locationInfo.attribution);
                IPLocationInfo.CountryCode = FString(locationInfo.countryCode);
                IPLocationInfo.Country = FString(locationInfo.country);
                IPLocationInfo.Region = FString(locationInfo.region);
                IPLocationInfo.CityCode = FString(locationInfo.cityCode);
                IPLocationInfo.City = FString(locationInfo.city);
                AsyncTask(ENamedThreads::GameThread, [Delegate, IPLocationInfo]()
                {
                      Delegate.ExecuteIfBound(true, IPLocationInfo, 0, "");
                });
            } else {
                int32 Code = error.code;
                FString Msg = FString(error.localizedDescription);
                AsyncTask(ENamedThreads::GameThread, [Delegate, Code, Msg, IPLocationInfo]()
                {
                      Delegate.ExecuteIfBound(false, IPLocationInfo, Code, Msg);
                });
            }
        }];
    });
}

// 安全锁相关接口
void UOneEngineSDKSubsystem::UnlockSafeLockUsingPushNotification(const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
    NSString* RoleName_ns = RoleName.GetNSString();
    NSString* ServerName_ns = ServerName.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager unlockSecurityLockUsingOneClickWithRoleName:RoleName_ns
            serverName:ServerName_ns
            completion:^(WPOneEngineUnlockSafeLockResult unlockResult, NSString * _Nullable unlockToken, NSError * _Nullable error) {
                EOneUnlockSafeLockResult Result = OneUnLockStateFromiOSUnlockResult(unlockResult);
                int32 Code = 0;
                FString Msg = "";
                FString UnlockToken = "";
                if(error)
                {
                    Code = error.code;
                    Msg = FString(error.localizedDescription);
                }
                else
                {
                    UnlockToken = FString(unlockToken);
                }
                AsyncTask(ENamedThreads::GameThread, [OnUnlockSafeLockResultDelegate, Result, Code, Msg, UnlockToken]()
                {
                      OnUnlockSafeLockResultDelegate.ExecuteIfBound(Result,UnlockToken, Code, Msg, EOneUnlockSafeLockType::PushNotification);
                });
        }];
    });
}

void UOneEngineSDKSubsystem::UnlockSafeLockUsingDynamicCode(const FString& DynamicCode, const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
    NSString* DynamicCode_ns = DynamicCode.GetNSString();
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager unlockSecurityLockUsingDynamicCode:DynamicCode_ns
            completion:^(WPOneEngineUnlockSafeLockResult unlockResult, NSString * _Nullable unlockToken, NSError * _Nullable error) {
                EOneUnlockSafeLockResult Result = OneUnLockStateFromiOSUnlockResult(unlockResult);
                int32 Code = 0;
                FString Msg = "";
                FString UnlockToken = "";
                if(error)
                {
                    Code = error.code;
                    Msg = FString(error.localizedDescription);
                }
                else
                {
                    UnlockToken = FString(unlockToken);
                }
                AsyncTask(ENamedThreads::GameThread, [OnUnlockSafeLockResultDelegate, Result, Code, Msg, UnlockToken]()
                {
                      OnUnlockSafeLockResultDelegate.ExecuteIfBound(Result,UnlockToken, Code, Msg, EOneUnlockSafeLockType::DynamicCode);
                });
        }];
    });
}

void UOneEngineSDKSubsystem::StopUnlockSafeLockUsingPushNotification()
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [WPOneEngineManager stopUnlockUsingOneClickPush];
    });
}

#else
// 未安装macOS NativeSDK时的默认函数

void UOneEngineSDKSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
}

void UOneEngineSDKSubsystem::Init(FOneInitDelegate InitDelegate)
{
}

EOneEngineSDKRegionType UOneEngineSDKSubsystem::GetRegionType()
{
    return static_cast<EOneEngineSDKRegionType>(-1);
}

void UOneEngineSDKSubsystem::Login()
{
}

void UOneEngineSDKSubsystem::GetUserTokenList(FOneFetchUserTokenListDelegate OnGetTokenListDelegate)
{
    
}

void UOneEngineSDKSubsystem::GetUserTokenListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)> &OnFinishedLambda)
{
    
}

void UOneEngineSDKSubsystem::TokenLogin(const FString& Token, const FString& Uid, EOneEngineThirdType ThirdType)
{
    
}

void UOneEngineSDKSubsystem::ThirdLogin(EOneEngineThirdType ThirdType, bool bForcedLogin)
{
    
}

void UOneEngineSDKSubsystem::GuestLogin()
{
    
}

void UOneEngineSDKSubsystem::SwitchAccount()
{
}

void UOneEngineSDKSubsystem::Pay(const FOnePaymentInfo& PaymentInfo)
{
}

FOneUserInfo UOneEngineSDKSubsystem::GetUserInfo()
{
    FOneUserInfo UserInfo;
    return UserInfo;
}

// 获取档位信息
void UOneEngineSDKSubsystem::GetProductList(const TArray<FString>& ProductIds, FOneProductInfoDelegate ProductResultDelegate)
{
}

// 角色事件打点
void UOneEngineSDKSubsystem::TrackEventRoleCreate(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
}

void UOneEngineSDKSubsystem::TrackEventRoleLoginSucceed(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
}

void UOneEngineSDKSubsystem::TrackEventRoleLoginError(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port, const FString& Code, const FString& Msg)
{
}

void UOneEngineSDKSubsystem::TrackEventRoleLevelUp(const FOneRoleInfo& RoleInfo)
{
}


void UOneEngineSDKSubsystem::TrackEventRoleLogout(const FOneRoleInfo& RoleInfo)
{
}

// 资源事件打点
void UOneEngineSDKSubsystem::GameResReqEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
}

void UOneEngineSDKSubsystem::GameUpdateAssetEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
}

void UOneEngineSDKSubsystem::GameResDecEvent(EOneResEventState State, const FString& ErrorMsg)
{
}

void UOneEngineSDKSubsystem::GameGetServerListEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
}

// 自定义事件打点
void UOneEngineSDKSubsystem::TrackEvent(const FString& Name, const TMap<FString, FString>& Payload)
{
}

void UOneEngineSDKSubsystem::TrackEventAD(const FString& Name, const TMap<FString, FString>& Payload)
{
}

//进入游戏场景打点
void UOneEngineSDKSubsystem::TrackEventEnterGameScene(const FString& SceneName,int32 Period,const TMap<FString,FString>& HintMap)
{
}

//退出游戏打点
void UOneEngineSDKSubsystem::TrackEventExitGameScene()
{
}

//设备打点额外信息
void UOneEngineSDKSubsystem::TrackEventAddExtraDeviceInfo(const TMap<FString,FString>& ExtraDeviceInfo)
{
}

bool UOneEngineSDKSubsystem::IsLoggedIn()
{
    return false;
}

void UOneEngineSDKSubsystem::SetUpConfigAppID(const FString& AppId)
{
}

FString UOneEngineSDKSubsystem::GetAppId()
{
    return FString(TEXT(""));
}

void UOneEngineSDKSubsystem::EnableDebugMode(bool Enable)
{
}

bool UOneEngineSDKSubsystem::IsDebugMode()
{
    return false;
}

void UOneEngineSDKSubsystem::OpenUserCenter()
{
}

void UOneEngineSDKSubsystem::OpenComplianceOnWebView()
{
}

void UOneEngineSDKSubsystem::EnterAccountCancellation()
{
}

FString UOneEngineSDKSubsystem::GetChannelId()
{
    return FString(TEXT(""));
}

FString UOneEngineSDKSubsystem::GetChannelMediaId()
{
    return FString(TEXT(""));
}

int32 UOneEngineSDKSubsystem::GetPlatformOS()
{
    return 0;
}

void UOneEngineSDKSubsystem::GetChannelPlatform(FOneGetPlatformDelegate OnGetPlatformResultDelegate)
{
    
}

void UOneEngineSDKSubsystem::GetChannelPlatformLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)> &OnFinishedLambda)
{
    
}

void UOneEngineSDKSubsystem::GetUserLocationInfo(FOneUserLocationInfoDelegate LocationInfoDelegate)
{
}

void UOneEngineSDKSubsystem::GetUserLocationInfoLambda(const TFunction<void(const FOneUserLocationInfo& LocationInfo)> &OnFinishedLambda)
{
	
}

void UOneEngineSDKSubsystem::GetDeviceInfo(FOneGetDeviceInfoDelegate Delegate)
{
}

void UOneEngineSDKSubsystem::GetDeviceInfoLambda(const TFunction<void(const FOneDeviceInfo& DeviceInfo)> &OnFinishedLambda)
{
	
}

void UOneEngineSDKSubsystem::FetchAntiAddictionInfo(FOneFetchAntiAddictionInfoDelegate OnFetchAntiAddictionInfo)
{
}

void UOneEngineSDKSubsystem::StartAntiAddictionNotify(const FString &ServerId, const FString &RoleId)
{
}

void UOneEngineSDKSubsystem::StopAntiAddictionNotify()
{
}

void UOneEngineSDKSubsystem::SetShowDefaultActivationResultToast(bool bShow)
{
}

void UOneEngineSDKSubsystem::DisplayCDKeyDialog(FOneGenericResultDelegate OnCDKeyActivateResult, const FString &ServerId)
{
}

void UOneEngineSDKSubsystem::QueryActCode(const FString& ServerId, FOneQueryActCodeResultDelegate OnQueryActCodeResultDelegate)
{
}

void UOneEngineSDKSubsystem::QueryActCodeLambda(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> &OnFinishedLambda)
{
	
}

void UOneEngineSDKSubsystem::QueryUserActiveQualification(const FString& ServerId, FOneQueryUserActiveQualificationResultDelegate OneQueryUserActiveQualificationResultDelegate)
{
}

void UOneEngineSDKSubsystem::QueryUserActiveQualificationLambda(const FString& ServerId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)> &OnFinishedLambda)
{
}

void UOneEngineSDKSubsystem::ActivateDevice(const FString& ServerId, FOneActivateDeviceResultDelegate OneActivateDeviceResultDelegate)
{
}

void UOneEngineSDKSubsystem::ActivateDeviceLambda(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> &OnFinishedLambda)
{
}

void UOneEngineSDKSubsystem::ExchangeActCode(const FString& ServerId, const FString& ActCode, FOneGenericResultDelegate GenericResultDelegate)
{
}

void UOneEngineSDKSubsystem::ExchangeActCodeLambda(const FString& ServerId, const FString& ActCode, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> &OnFinishedLambda)
{
	
}

void UOneEngineSDKSubsystem::RedeemCouponCode(const FString &CouponCode,  const FString &ServerId, const FString &RoleId, const FString &RoleLevel, const FString &VipLevel, const TMap<FString, FString> &ExtraInfo, FOneGenericResultDelegate OnRedeemCouponResult)
{
}

void UOneEngineSDKSubsystem::RedeemCouponCodeLambda(const FString& CouponCode, const FString& ServerId, const FString& RoleId, const FString& RoleLevel, const FString& VipLevel, const TMap<FString, FString>& ExtraInfo,  const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> &OnFinishedLambda)
{
	
}

void UOneEngineSDKSubsystem::FetchUserRoleInfoList(FOneFetchUserRoleInfoListDelegate OnFetchUserRoleListDelegate, const FString& ServerId)
{
}

void UOneEngineSDKSubsystem::FetchUserRoleInfoListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)> &OnFinishedLambda, const FString& ServerId)
{
	
}

void UOneEngineSDKSubsystem::Bind(FOneBindResultDelegate StartBindPhoneDelegate,EOneEngineThirdType BindType)
{
}

void UOneEngineSDKSubsystem::UserAuthentication(FOneUserAuthenticationResultDelegate OnUserAuthenticationResultDelegate)
{
}

// 打开AIHelp客服
void UOneEngineSDKSubsystem::OpenAIHelp(EOneAIHelpType Type, const FString& RoleId, const FString& ServerId, const FString& RoleName)
{
}

// 打开自定义客服
void UOneEngineSDKSubsystem::OpenCustomerService(const FString& RoleID, const FString& RoleName, const FString& ServerID)
{
}

void UOneEngineSDKSubsystem::TranslateLambda(const FString& Text,const TFunction<void(bool bSucceed, const FString& Result,const FString& ErrorMsg)> &OnFinishedLambda)
{
}

// 文本翻译
void UOneEngineSDKSubsystem::Translate(const FString& Text, FOneTranslateResultDelegate Callback)
{
}

// 设置语言
void UOneEngineSDKSubsystem::SetLanguage(const FString& Code)
{
}

// 获取本地语言列表
TArray<FString> UOneEngineSDKSubsystem::GetSupportedLanguageCodeList()
{
    TArray<FString> Result;
    return Result;
}

//是否检测BundleId(iOS专有接口)
void UOneEngineSDKSubsystem::ShouldVerifyBundleId(bool bShouldVerify)
{
}

//获取审核开关(iOS专有接口)
bool UOneEngineSDKSubsystem::ExaminStatus()
{
    return false;
}
void UOneEngineSDKSubsystem::GetIpInfo(const FString& Ip,FOnGetIpInfoResultDelegate Delegate)
{
	
}

// 安全锁相关接口
void UOneEngineSDKSubsystem::UnlockSafeLockUsingPushNotification(const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
}

void UOneEngineSDKSubsystem::UnlockSafeLockUsingDynamicCode(const FString& DynamicCode, const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
}

void UOneEngineSDKSubsystem::StopUnlockSafeLockUsingPushNotification()
{
}
#endif

// macOS不支持的函数，统一返回默认值

// 获取声明的权限 可用于展示
TArray<FOnePermissionInfo> UOneEngineSDKSubsystem::GetPermissions()
{
    TArray<FOnePermissionInfo> PermissionInfos;
    return  PermissionInfos;
}

// 检查是否权限已授权
bool UOneEngineSDKSubsystem::CheckSelfPermission(EOnePermissionType Type)
{
    return false;
}

// 申请权限
void UOneEngineSDKSubsystem::RequestPermission(EOnePermissionType Type,FOneRequestPermissionResultDelegate Delegate)
{
}
void UOneEngineSDKSubsystem::RequestPermission(EOnePermissionType Type,FOneRequestPermissionResultDelegate Delegate,const TArray<FString>& Tips)
{
}
//  关闭剪切板权限
void UOneEngineSDKSubsystem::CloseClipboardPermission()
{
}

// 打开应用权限设置页
void UOneEngineSDKSubsystem::OpenApplicationSetting()
{
}

void UOneEngineSDKSubsystem::GetQRCodeScanResult(FOneGetQRCodeScanResultDelegate OnGetQRCodeScanResultDelegate)
{
}

// 获取当前设置语言
FString UOneEngineSDKSubsystem::GetCurrentLanguage()
{
    return FString(TEXT(""));
}

// 设置屏幕方向
void UOneEngineSDKSubsystem::SetScreenOrientation(EOneScreenOrientation Orientation)
{
}

//万能方法接口
bool UOneEngineSDKSubsystem::IsCommonFunctionSupported(const FString& FuncName)
{
    return false;
}

void UOneEngineSDKSubsystem::CallCommonFunction(const FString& FuncName, const FString& Params, FOneCommonFunctionDelegate CommonFunctionDelegate)
{
}

// 分享SDK
void UOneEngineSDKSubsystem::ShareDataToApp(EOneShareAppTarget AppTarget, const EOneShareType Type, const FOneShareData& Data, FOneGenericResultDelegate OnShareResult)
{
}

// 推送相关接口
// 注册推送
void UOneEngineSDKSubsystem::StartUpdatePushData(FOneStartUpdatePushDataDelegate Callback)
{
}

// 设置点击推送消息后的回调
void UOneEngineSDKSubsystem::SetupNotificationCallback(FOneNotificationDelegate Callback)
{
}

// 获取系统推送开关状态 和 应用推送开关状态
void UOneEngineSDKSubsystem::GetPushStatus(FOnePushStatusDelegate Callback)
{
}

// 设置应用推送开关状态
void UOneEngineSDKSubsystem::SetProviderPushState(bool bProviderPushState, FOneGenericResultDelegate Callback)
{
}

// 推送设置用户信息
void UOneEngineSDKSubsystem::SetPushUserInfo(const FString& ServerId, const FString& RoleId, FOneGenericResultDelegate Callback)
{
}

// 推送解绑用户信息
void UOneEngineSDKSubsystem::UnSetPushUserInfo(FOneGenericResultDelegate Callback)
{
}

// 获取推送类型列表
void UOneEngineSDKSubsystem::GetPushTypeInfoList(FOneGetPushTypeInfoListDelegate Callback)
{
}

// 批量设置推送类型开关状态
void UOneEngineSDKSubsystem::UpdatePushTypeList(const TArray<FOnePushTypeInfo>& PushTypeList, FOneGenericResultDelegate Callback)
{
}

// 夜间勿扰配置的回调
void UOneEngineSDKSubsystem::GetPushNotDisturb(FOnePushNotDisturbInfoDelegate Callback)
{
}

// 设置勿扰模式
void UOneEngineSDKSubsystem::UpdatePushNotDisturb(const FOnePushNotDisturbInfo& NotDisturbInfo, FOneGenericResultDelegate Callback)
{
}
void UOneEngineSDKSubsystem::KillProcess()
{
}

bool UOneEngineSDKSubsystem::ACELogin(const FString& accountId, int accountTypeCommandId, int worldId, FOneOnGetClientPacket delegate)
{
    return false;
}

bool UOneEngineSDKSubsystem::ACEClientPacketReceive(const TArray<uint8>& data)
{
    return false;
}

bool UOneEngineSDKSubsystem::ACELogout()
{
    return false;
}

/// 全球独有的 评价接口和 修改屏幕亮度接口
float UOneEngineSDKSubsystem::GetScreenBrightness()
{
    return 0.0f;
}

void UOneEngineSDKSubsystem::SetScreenBrightness(float BrightnessValue)
{
	
}
	
void UOneEngineSDKSubsystem::RecoverScreenBrightness()
{
	
}
///保持屏幕常亮
void UOneEngineSDKSubsystem::SwitchScreenPermanentBrightnessState(bool bIsTurnOn)
{
	
}
	

///评价接口
void UOneEngineSDKSubsystem::InAppRequestStoreReview(FOneGenericResultDelegate Delegate)
{
	
}

/// AppLink Ios有这个参数
void UOneEngineSDKSubsystem::RequestStoreReview(FString AppLink)
{
	
}

/// 获取渲染器配置文件地址
FString UOneEngineSDKSubsystem::GetRenderConfigFilePath()
{
    return "";
}

// 全球独有的打开Naver论坛
void UOneEngineSDKSubsystem::OpenCommunityByGame(EOneNaverGameType Type, const FString& Pid, bool Scheduled, FOneLoadDelegate LoadCallback, FOneUnloadDelegate UnloadCallback, FOneInGameMenuDelegate InGameMenuCallback)
{
	
}

void UOneEngineSDKSubsystem::TerminateCommunity()
{
	
}

void UOneEngineSDKSubsystem::GetProviderPushState(FOnGetPushStateDelegate Delegate)
{
	
}


void UOneEngineSDKSubsystem::SetAnalyticsCollectionEnabled(bool bEnable)
{
	
}
bool UOneEngineSDKSubsystem::IsInstalledApp(EOneShareAppTarget AppTarget)
{
    return false;
}
