// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#import <Foundation/Foundation.h>
#ifdef INSTALL_ONE_ENGINE_MAC_LIBRARY
	#import "WPOneEngineBridge.h"
#endif

// 通用回调
typedef void(^WPOneEngineGenericResultHandler)(NSInteger code,NSString *message);
@interface OneEngineSDKMacAdapter : NSObject

+ (OneEngineSDKMacAdapter *)GetBridge;

- (void)registerInitCallBackCompletion:(WPOneEngineGenericResultHandler)completion;

- (void)showActCodeViewWithServerId:(NSString *)serviceId completion:(WPOneEngineGenericResultHandler)completion;

- (void)verifyRedeemCodeWithRoleId:(NSString *)roleId
						  serverId:(NSString *)serverId
							 level:(NSInteger)level
							   vip:(NSInteger)vip
						redeemCode:(NSString *)redeemCode
						 extraInfo:(NSDictionary *)extraInfo
						completion:(WPOneEngineGenericResultHandler)completion;
@end
