#include "OneEngineSDKSubsystem.h"
void UOneEngineSDKSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
}

void UOneEngineSDKSubsystem::Init(FOneInitDelegate InitDelegate)
{

}
EOneEngineSDKRegionType UOneEngineSDKSubsystem::GetRegionType()
{
	return EOneEngineSDKRegionType::Oversea;
}
void UOneEngineSDKSubsystem::Login()
{

}

void UOneEngineSDKSubsystem::GetUserTokenList(FOneFetchUserTokenListDelegate OnGetTokenListDelegate)
{
    
}

void UOneEngineSDKSubsystem::GetUserTokenListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)> &OnFinishedLambda)
{
    
}

void UOneEngineSDKSubsystem::TokenLogin(const FString& Token, const FString& Uid, EOneEngineThirdType ThirdType)
{
    
}

void UOneEngineSDKSubsystem::ThirdLogin(EOneEngineThirdType ThirdType, bool bForcedLogin)
{
    
}

void UOneEngineSDKSubsystem::GuestLogin()
{
    
}

void UOneEngineSDKSubsystem::SwitchAccount()
{

}

FOneUserInfo UOneEngineSDKSubsystem::GetUserInfo()
{
	FOneUserInfo UserInfo;
	return UserInfo;
}

void UOneEngineSDKSubsystem::Pay(const FOnePaymentInfo& PaymentInfo)
{
    
}

// 角色事件打点
void UOneEngineSDKSubsystem::TrackEventRoleLoginSucceed(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
	
}

void UOneEngineSDKSubsystem::TrackEventRoleLoginError(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port, const FString& Code, const FString& Msg)
{
	
}

void UOneEngineSDKSubsystem::TrackEventRoleLogout(const FOneRoleInfo& RoleInfo)
{
	
}

void UOneEngineSDKSubsystem::TrackEventRoleCreate(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
	
}

void UOneEngineSDKSubsystem::TrackEventRoleLevelUp(const FOneRoleInfo& RoleInfo)
{
	
}

// 资源事件打点
void UOneEngineSDKSubsystem::GameResReqEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
	
}

void UOneEngineSDKSubsystem::GameUpdateAssetEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
	
}

void UOneEngineSDKSubsystem::GameResDecEvent(EOneResEventState State, const FString& ErrorMsg)
{
	
}

void UOneEngineSDKSubsystem::GameGetServerListEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
	
}

// 自定义事件打点
void UOneEngineSDKSubsystem::TrackEvent(const FString& Name, const TMap<FString, FString>& Payload)
{
	
}

void UOneEngineSDKSubsystem::TrackEventAD(const FString& Name, const TMap<FString, FString>& Payload)
{
	
}

//进入游戏场景打点
void UOneEngineSDKSubsystem::TrackEventEnterGameScene(const FString& SceneName,int32 Period,const TMap<FString,FString>& HintMap)
{
	
}

//退出游戏打点
void UOneEngineSDKSubsystem::TrackEventExitGameScene()
{
	
}

//设备打点额外信息
void UOneEngineSDKSubsystem::TrackEventAddExtraDeviceInfo(const TMap<FString,FString>& ExtraDeviceInfo)
{
	
}

void UOneEngineSDKSubsystem::SetUpConfigAppID(const FString& AppId)
{
	
}

FString UOneEngineSDKSubsystem::GetAppId()
{
    return FString(TEXT(""));
}

bool UOneEngineSDKSubsystem::IsLoggedIn()
{
	return false;
}

void UOneEngineSDKSubsystem::EnableDebugMode(bool Enable)
{
	
}

bool UOneEngineSDKSubsystem::IsDebugMode()
{
	return false;
}

void UOneEngineSDKSubsystem::OpenUserCenter()
{
	
}

void UOneEngineSDKSubsystem::GetQRCodeScanResult(FOneGetQRCodeScanResultDelegate OnGetQRCodeScanResultDelegate)
{
	
}

void UOneEngineSDKSubsystem::OpenComplianceOnWebView()
{

}

void UOneEngineSDKSubsystem::EnterAccountCancellation()
{

}

FString UOneEngineSDKSubsystem::GetChannelId()
{
	return "";
}

FString UOneEngineSDKSubsystem::GetChannelMediaId()
{
	return "";
}

int32 UOneEngineSDKSubsystem::GetPlatformOS()
{
	return 0;
}

void UOneEngineSDKSubsystem::GetChannelPlatform(FOneGetPlatformDelegate OnGetPlatformResultDelegate)
{
}

void UOneEngineSDKSubsystem::GetChannelPlatformLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)> &OnFinishedLambda)
{
}

//万能方法接口
bool UOneEngineSDKSubsystem::IsCommonFunctionSupported(const FString& FuncName)
{
	return false;
}

void UOneEngineSDKSubsystem::CallCommonFunction(const FString& FuncName, const FString& Params, FOneCommonFunctionDelegate CommonFunctionDelegate)
{
    
}

// 获取档位信息(iOS专有接口)
void UOneEngineSDKSubsystem::GetProductList(const TArray<FString>& ProductIds, FOneProductInfoDelegate ProductResultDelegate)
{
	
}

//获取审核开关(iOS专有接口)
bool UOneEngineSDKSubsystem::ExaminStatus()
{
	return false;
}
//是否检测BundleId(iOS专有接口)
void UOneEngineSDKSubsystem::ShouldVerifyBundleId(bool bShouldVerify)
{
	
}

void UOneEngineSDKSubsystem::GetUserLocationInfo(FOneUserLocationInfoDelegate LocationInfoDelegate)
{
	
}

void UOneEngineSDKSubsystem::GetUserLocationInfoLambda(const TFunction<void(const FOneUserLocationInfo& LocationInfo)> &OnFinishedLambda)
{
	
}

void UOneEngineSDKSubsystem::GetDeviceInfo(FOneGetDeviceInfoDelegate Delegate)
{
	FOneDeviceInfo DeviceInfo;
}

void UOneEngineSDKSubsystem::GetDeviceInfoLambda(const TFunction<void(const FOneDeviceInfo& DeviceInfo)> &OnFinishedLambda)
{
	FOneDeviceInfo DeviceInfo;
	OnFinishedLambda(DeviceInfo);
}

void UOneEngineSDKSubsystem::FetchAntiAddictionInfo(FOneFetchAntiAddictionInfoDelegate OnFetchAntiAddictionInfo)
{
   
}

void UOneEngineSDKSubsystem::StartAntiAddictionNotify(const FString &ServerId, const FString &RoleId)
{
   
}

void UOneEngineSDKSubsystem::StopAntiAddictionNotify()
{

}

void UOneEngineSDKSubsystem::SetShowDefaultActivationResultToast(bool bShow)
{

}

void UOneEngineSDKSubsystem::DisplayCDKeyDialog(FOneGenericResultDelegate OnCDKeyActivateResult,  const FString &ServerId)
{

}

void UOneEngineSDKSubsystem::QueryActCode(const FString& ServerId, FOneQueryActCodeResultDelegate OnQueryActCodeResultDelegate)
{
	
}

void UOneEngineSDKSubsystem::QueryActCodeLambda(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> &OnFinishedLambda)
{
	
}

void UOneEngineSDKSubsystem::QueryUserActiveQualification(const FString& ServerId, FOneQueryUserActiveQualificationResultDelegate OneQueryUserActiveQualificationResultDelegate)
{
}

void UOneEngineSDKSubsystem::QueryUserActiveQualificationLambda(const FString& ServerId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)> &OnFinishedLambda)
{
}

void UOneEngineSDKSubsystem::ActivateDevice(const FString& ServerId, FOneActivateDeviceResultDelegate OneActivateDeviceResultDelegate)
{
}

void UOneEngineSDKSubsystem::ActivateDeviceLambda(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> &OnFinishedLambda)
{
}

void UOneEngineSDKSubsystem::ExchangeActCode(const FString& ServerId, const FString& ActCode, FOneGenericResultDelegate GenericResultDelegate)
{
	
}

void UOneEngineSDKSubsystem::ExchangeActCodeLambda(const FString& ServerId, const FString& ActCode, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> &OnFinishedLambda)
{
	
}

void UOneEngineSDKSubsystem::RedeemCouponCode(const FString &CouponCode,  const FString &ServerId, const FString &RoleId, const FString &RoleLevel, const FString &VipLevel, const TMap<FString, FString> &ExtraInfo, FOneGenericResultDelegate OnRedeemCouponResult)
{
    
}

void UOneEngineSDKSubsystem::RedeemCouponCodeLambda(const FString& CouponCode, const FString& ServerId, const FString& RoleId, const FString& RoleLevel, const FString& VipLevel, const TMap<FString, FString>& ExtraInfo,  const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> &OnFinishedLambda)
{
	
}

void UOneEngineSDKSubsystem::FetchUserRoleInfoList(FOneFetchUserRoleInfoListDelegate OnFetchUserRoleListDelegate, const FString& ServerId)
{
	
}

void UOneEngineSDKSubsystem::FetchUserRoleInfoListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)> &OnFinishedLambda, const FString& ServerId)
{
	
}

void UOneEngineSDKSubsystem::Bind(FOneBindResultDelegate StartBindPhoneDelegate,EOneEngineThirdType BindType)
{
	
}

void UOneEngineSDKSubsystem::UserAuthentication(FOneUserAuthenticationResultDelegate OnUserAuthenticationResultDelegate)
{

}
// 获取声明的权限 可用于展示
TArray<FOnePermissionInfo> UOneEngineSDKSubsystem::GetPermissions()
{
	TArray<FOnePermissionInfo> PermissionInfos;
	return  PermissionInfos;
}

// 检查是否权限已授权
bool UOneEngineSDKSubsystem::CheckSelfPermission(EOnePermissionType Type)
{
	return false;
}

// 申请权限
void UOneEngineSDKSubsystem::RequestPermission(EOnePermissionType Type,FOneRequestPermissionResultDelegate Delegate)
{
	
}
void UOneEngineSDKSubsystem::RequestPermission(EOnePermissionType Type,FOneRequestPermissionResultDelegate Delegate,const TArray<FString>& Tips)
{
	
}
//  关闭剪切板权限
void UOneEngineSDKSubsystem::CloseClipboardPermission()
{
	
}

// 打开应用权限设置页
void UOneEngineSDKSubsystem::OpenApplicationSetting()
{
	
}

// 打开AIHelp客服
void UOneEngineSDKSubsystem::OpenAIHelp(EOneAIHelpType Type, const FString& RoleId, const FString& ServerId, const FString& RoleName)
{
	
}

// 打开自定义客服
void UOneEngineSDKSubsystem::OpenCustomerService(const FString& RoleID, const FString& RoleName, const FString& ServerID)
{
	
}

// 文本翻译
void UOneEngineSDKSubsystem::TranslateLambda(const FString& Text,const TFunction<void(bool bSucceed, const FString& Result,const FString& ErrorMsg)> &OnFinishedLambda)
{
}

void UOneEngineSDKSubsystem::Translate(const FString& Text, FOneTranslateResultDelegate Callback)
{
	
}

// 获取当前设置语言
FString UOneEngineSDKSubsystem::GetCurrentLanguage()
{
	return "";
}

// 设置语言
void UOneEngineSDKSubsystem::SetLanguage(const FString& Code)
{
	
}

// 获取本地语言列表
TArray<FString> UOneEngineSDKSubsystem::GetSupportedLanguageCodeList()
{
	TArray<FString> LanguageCodeList;
	return LanguageCodeList;
}

// 设置屏幕方向
void UOneEngineSDKSubsystem::SetScreenOrientation(EOneScreenOrientation Orientation)
{
	
}

// 分享SDK
void UOneEngineSDKSubsystem::ShareDataToApp(EOneShareAppTarget AppTarget, const EOneShareType Type, const FOneShareData& Data, FOneGenericResultDelegate OnShareResult)
{
	
}

// 推送相关接口
// 注册推送
void UOneEngineSDKSubsystem::StartUpdatePushData(FOneStartUpdatePushDataDelegate Callback)
{
	
}

// 设置点击推送消息后的回调
void UOneEngineSDKSubsystem::SetupNotificationCallback(FOneNotificationDelegate Callback)
{
	
}

// 获取系统推送开关状态 和 应用推送开关状态
void UOneEngineSDKSubsystem::GetPushStatus(FOnePushStatusDelegate Callback)
{
	
}

// 设置应用推送开关状态
void UOneEngineSDKSubsystem::SetProviderPushState(bool bProviderPushState, FOneGenericResultDelegate Callback)
{
	
}

// 推送设置用户信息
void UOneEngineSDKSubsystem::SetPushUserInfo(const FString& ServerId, const FString& RoleId, FOneGenericResultDelegate Callback)
{
	
}

// 推送解绑用户信息
void UOneEngineSDKSubsystem::UnSetPushUserInfo(FOneGenericResultDelegate Callback)
{
	
}

// 获取推送类型列表
void UOneEngineSDKSubsystem::GetPushTypeInfoList(FOneGetPushTypeInfoListDelegate Callback)
{
	
}

// 批量设置推送类型开关状态
void UOneEngineSDKSubsystem::UpdatePushTypeList(const TArray<FOnePushTypeInfo>& PushTypeList, FOneGenericResultDelegate Callback)
{
	
}

// 夜间勿扰配置的回调
void UOneEngineSDKSubsystem::GetPushNotDisturb(FOnePushNotDisturbInfoDelegate Callback)
{
	
}

// 设置勿扰模式
void UOneEngineSDKSubsystem::UpdatePushNotDisturb(const FOnePushNotDisturbInfo& NotDisturbInfo, FOneGenericResultDelegate Callback)
{
	
}
void UOneEngineSDKSubsystem::KillProcess()
{
	
}

bool UOneEngineSDKSubsystem::ACELogin(const FString& accountId, int accountTypeCommandId, int worldId, FOneOnGetClientPacket delegate)
{
	return false;
}

bool UOneEngineSDKSubsystem::ACEClientPacketReceive(const TArray<uint8>& data)
{
	return false;
}

bool UOneEngineSDKSubsystem::ACELogout()
{
	return false;
}
void UOneEngineSDKSubsystem::GetIpInfo(const FString& Ip,FOnGetIpInfoResultDelegate Delegate)
{
	
}

/// 全球独有的 评价接口和 修改屏幕亮度接口
float UOneEngineSDKSubsystem::GetScreenBrightness()
{
	return 0.0f;
}

void UOneEngineSDKSubsystem::SetScreenBrightness(float BrightnessValue)
{
	
}
	
void UOneEngineSDKSubsystem::RecoverScreenBrightness()
{
	
}
///保持屏幕常亮
void UOneEngineSDKSubsystem::SwitchScreenPermanentBrightnessState(bool bIsTurnOn)
{
	
}
	

///评价接口
void UOneEngineSDKSubsystem::InAppRequestStoreReview(FOneGenericResultDelegate Delegate)
{
	
}

/// AppLink Ios有这个参数
void UOneEngineSDKSubsystem::RequestStoreReview(FString AppLink)
{
	
}

/// 获取渲染器配置文件地址
FString UOneEngineSDKSubsystem::GetRenderConfigFilePath()
{
	return "";
}

// 安全锁相关接口
void UOneEngineSDKSubsystem::UnlockSafeLockUsingPushNotification(const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
}

void UOneEngineSDKSubsystem::UnlockSafeLockUsingDynamicCode(const FString& DynamicCode, const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
}

void UOneEngineSDKSubsystem::StopUnlockSafeLockUsingPushNotification()
{
}

// 全球独有的打开Naver论坛
void UOneEngineSDKSubsystem::OpenCommunityByGame(EOneNaverGameType Type, const FString& Pid, bool Scheduled, FOneLoadDelegate LoadCallback, FOneUnloadDelegate UnloadCallback, FOneInGameMenuDelegate InGameMenuCallback)
{
	
}

void UOneEngineSDKSubsystem::TerminateCommunity()
{
	
}
void UOneEngineSDKSubsystem::GetProviderPushState(FOnGetPushStateDelegate Delegate)
{
	
}


void UOneEngineSDKSubsystem::SetAnalyticsCollectionEnabled(bool bEnable)
{
	
}

bool UOneEngineSDKSubsystem::IsInstalledApp(EOneShareAppTarget AppTarget)
{
	return false;
}