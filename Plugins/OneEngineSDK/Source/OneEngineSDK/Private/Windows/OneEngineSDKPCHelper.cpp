
#include "OneEngineSDKPCHelper.h"
#include "JsonObjectWrapper.h"
#include "Serialization/JsonSerializer.h"
#include "Windows/WindowsPlatformMisc.h"
#include "Async/Async.h"
#include "OneEngineSDKPCAdapter.h"
#include "Interfaces/IPluginManager.h"
#include <Misc/FileHelper.h>

DEFINE_LOG_CATEGORY(PC_OneEngineSDK_Helper);

#define CURRENT_CLASS_FUNCTION_HELPER (FString(__FUNCTION__))
#define CURRENT_LINE_HELPER  (FString::FromInt(__LINE__))
#define CURRENT_CLASS_FUNCTION_LINE_HELPER (CURRENT_CLASS_FUNCTION_HELPER + ":LNO." + CURRENT_LINE_HELPER )

#define TRACK_HELPER \
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK_Helper, Log, TEXT("%s"),*CURRENT_CLASS_FUNCTION_LINE_HELPER); \
	}\
}

#define LOG_PRINTF_HELPER(FormatString , ...)\
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK_Helper, Log, TEXT("%s: %s"), *CURRENT_CLASS_FUNCTION_LINE_HELPER, *FString::Printf(TEXT(FormatString), ##__VA_ARGS__ ) ); \
	}\
}

#define LOG_ERROR_PRINTF_HELPER(FormatString , ...) \
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK_Helper, Error, TEXT("%s: %s"), *CURRENT_CLASS_FUNCTION_LINE_HELPER, *FString::Printf(TEXT(FormatString), ##__VA_ARGS__ ) ); \
	}\
}

#define LOG_ERROR_PRINTF_ERROR_INVOKE_HELPER \
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK_Helper, Error, TEXT("%s: ERROR INVOKE"), *CURRENT_CLASS_FUNCTION_LINE_HELPER); \
	}\
}

typedef struct Function_IO_LOG_HELPER
{
public:
	Function_IO_LOG_HELPER(const FString& val)
	{
		if (OneEngineSDKPCAdapter::Get().IsDebugMode())
		{
			mVal = val;
			UE_LOG(PC_OneEngineSDK_Helper, Log, TEXT("%s start"), *mVal);
		}
	}
	~Function_IO_LOG_HELPER()
	{
		if (OneEngineSDKPCAdapter::Get().IsDebugMode())
		{
			UE_LOG(PC_OneEngineSDK_Helper, Log, TEXT("%s end"), *mVal);
		}
	}
private:
	FString mVal;

}Function_IO_LOG_HELPER;

#define LOG_FUNCTION_IO_HELPER Function_IO_LOG_HELPER a(CURRENT_CLASS_FUNCTION_LINE_HELPER)


OneEngineSDKPCHelper::OneEngineSDKPCHelper() 
	: orderId(TEXT(""))
	, userId(TEXT(""))
	, token(TEXT(""))
	, handle(nullptr)
{

}

OneEngineSDKPCHelper& OneEngineSDKPCHelper::Get()
{
	static OneEngineSDKPCHelper helper;
	return helper;
}

void* OneEngineSDKPCHelper::GetHandle()
{
	return handle;
}

void OneEngineSDKPCHelper::SetHandle(void* val)
{
	handle = val;
}

void OneEngineSDKPCHelper::SetOrderId(FString val)
{
	orderId = val;
}

FString OneEngineSDKPCHelper::GetOrderId()
{
	return orderId;
}

void OneEngineSDKPCHelper::SetUserId(FString val)
{
	userId = val;
}

FString OneEngineSDKPCHelper::GetUserId()
{
	return userId;
}

void OneEngineSDKPCHelper::SetToken(FString val)
{
	token = val;
}

FString OneEngineSDKPCHelper::GetToken()
{
	return token;
}

void OneEngineSDKPCHelper::ResetUserInfo()
{
	userId = "";
	token = "";
}

FString OneEngineSDKPCHelper::ConverServerId(const FString& ServerId)
{
	FString strServerId = ServerId;
	if (strServerId.IsEmpty())
	{
		LOG_PRINTF_HELPER("ServerId is empty");
		strServerId = "0";
	}
	return strServerId;
}

FString OneEngineSDKPCHelper::GetPluginVersion()
{
	FString AbsolutePath;
	IPluginManager& PluginManager = IPluginManager::Get();
	TSharedPtr<IPlugin> Plugin = PluginManager.FindPlugin(TEXT("OneEngineSDK"));
	if (Plugin.IsValid())
	{
		AbsolutePath = FPaths::Combine(Plugin->GetBaseDir(), Plugin->GetName() + TEXT(".uplugin"));
	}

	bool b = FPaths::FileExists(AbsolutePath);
	FString JsonStr;
	FString VersionName;
	if (FFileHelper::LoadFileToString(JsonStr, *AbsolutePath)) {
		TSharedPtr<FJsonObject> JsonObject;
		TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(JsonStr);
		if (FJsonSerializer::Deserialize(JsonReader, JsonObject)) {
			VersionName = JsonObject->GetStringField(TEXT("VersionName"));
		}
	}
	return VersionName;
}

void OneEngineSDKPCHelper::SetPreQueryCDKeyStatusDelegate(int contextId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)>& OnFinishedLambda)
{
	FScopeLock lock(&mMutexMapPreQueryCDKeyStatus);
	mMapPreQueryCDKeyStatusResultDelegate.Emplace(contextId, OnFinishedLambda);
}

void OneEngineSDKPCHelper::OnPreQueryCDKeyStatus(int code, const FString& message, const FString& result, int contextId)
{
	LOG_PRINTF_HELPER("code = %d, message = %s, result = %s, contextId = %d", code, *message, *result, contextId);
	FScopeLock lock(&mMutexMapPreQueryCDKeyStatus);
	if (mMapPreQueryCDKeyStatusResultDelegate.Contains(contextId))
	{
		auto callback = mMapPreQueryCDKeyStatusResultDelegate[contextId];
		FOneActiveQualificationInfo info;
		memset(&info, 0, sizeof(info));
		if (code == 0)
		{
			FJsonObjectWrapper Wraper;
			if (!result.IsEmpty() && Wraper.JsonObjectFromString(result) && Wraper.JsonObject.IsValid())
			{
				Wraper.JsonObject->TryGetNumberField(TEXT("deviceLogged"), info.DeviceLogged);
				Wraper.JsonObject->TryGetNumberField(TEXT("deviceTotal"), info.DeviceTotal);
				Wraper.JsonObject->TryGetNumberField(TEXT("status"), info.Status);
				Wraper.JsonObject->TryGetNumberField(TEXT("whitelist"), info.WhiteList);
			}
			else
			{
				LOG_PRINTF_HELPER("parse jsonstring failed %s", *result);
			}
		}

		AsyncTask(ENamedThreads::GameThread, [callback, code, message, info]()
			{
				if (callback)
				{
					callback(code == 0, code, message, info);
				}

			});
		mMapPreQueryCDKeyStatusResultDelegate.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}

void OneEngineSDKPCHelper::SetGetUserLocationResultDelegate(int contextId, const TFunction<void(const FOneUserLocationInfo& LocationInfo)>& OnFinishedLambda)
{
	FScopeLock lock(&mMutexMapGetUserLocationResultDelegate);
	mMapGetUserLocationResultDelegate.Emplace(contextId, OnFinishedLambda);
}

void OneEngineSDKPCHelper::OnGetUserLocationResult(const FString& result, int contextId)
{
	LOG_PRINTF_HELPER("result = %s, contextId = %d", *result, contextId);
	FScopeLock lock(&mMutexMapGetUserLocationResultDelegate);
	if (mMapGetUserLocationResultDelegate.Contains(contextId))
	{
		auto callback = mMapGetUserLocationResultDelegate[contextId];
		FOneUserLocationInfo userLocation;

		FJsonObjectWrapper Wraper;
		if (!result.IsEmpty() && Wraper.JsonObjectFromString(result) && Wraper.JsonObject.IsValid())
		{
			Wraper.JsonObject->TryGetStringField(TEXT("CountryAbbr"), userLocation.CountryAbbr);
			Wraper.JsonObject->TryGetStringField(TEXT("Country"), userLocation.Country);
			Wraper.JsonObject->TryGetStringField(TEXT("Province"), userLocation.Province);
			Wraper.JsonObject->TryGetStringField(TEXT("City"), userLocation.City);
			Wraper.JsonObject->TryGetStringField(TEXT("CountryCode"), userLocation.CountryCode);
			//Wraper.JsonObject->TryGetStringField(TEXT("CurrencyCode"), userLocation.cu);
			Wraper.JsonObject->TryGetStringField(TEXT("IP"), userLocation.IP);
		}
		else
		{
			LOG_PRINTF_HELPER("parse jsonstring failed %s", *result);
		}

		AsyncTask(ENamedThreads::GameThread, [callback, userLocation]()
			{
				if (callback)
				{
					callback(userLocation);
				}
				
			});
		mMapGetUserLocationResultDelegate.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}

void OneEngineSDKPCHelper::SetGenericResultLambda(int contextId, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda)
{
	FScopeLock lock(&mMutexMapGenericResultLambda);
	mMapGenericResultLambda.Emplace(contextId, OnFinishedLambda);
}

void OneEngineSDKPCHelper::OnGenericResultLambda(bool success, int code, const FString& msg, int contextId)
{
	LOG_PRINTF_HELPER("code = %d, message = %s, contextId = %d", code, *msg, contextId);
	FScopeLock lock(&mMutexMapGenericResultLambda);
	if (mMapGenericResultLambda.Contains(contextId))
	{
		auto callback = mMapGenericResultLambda[contextId];
		AsyncTask(ENamedThreads::GameThread, [callback, success, code, msg]()
			{
				if (callback)
				{
					callback(success, code, msg);
				}
			});
		mMapGenericResultLambda.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = % d", contextId);
	}
}

void OneEngineSDKPCHelper::SetTranslateResultDelegate(int contextId, const TFunction<void(bool bSucceed, const FString& Result, const FString& ErrorMsg)>& OnFinishedLambda)
{
	FScopeLock lock(&mMutexTranslateResultDelegate);
	mMapTranslateResultDelegate.Emplace(contextId, OnFinishedLambda);
}

void OneEngineSDKPCHelper::OnTranslateResult(int code, const FString& msg, const FString& result, int contextId)
{
	LOG_PRINTF_HELPER("code = %d, message = %s, result = %s, contextId = %d", code, *msg, *result, contextId);
	FScopeLock lock(&mMutexTranslateResultDelegate);
	if (mMapTranslateResultDelegate.Contains(contextId))
	{
		auto callback = mMapTranslateResultDelegate[contextId];
		AsyncTask(ENamedThreads::GameThread, [callback, code, msg, result]()
			{
				if (callback)
				{
					callback(code == 0, result, msg);
				}
				
			});
		mMapTranslateResultDelegate.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}

void OneEngineSDKPCHelper::SetMainLandQueryActCodeResultDelegate(int contextId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)>& OnFinishedLambda)
{
	FScopeLock lock(&mMutexMainLandQueryActCodeResultDelegate);
	mMapMainLandQueryActCodeResultDelegate.Emplace(contextId, OnFinishedLambda);
}

void OneEngineSDKPCHelper::OnMainLandQueryActCodeResult(bool success, int code, const FString& msg, int contextId)
{
	LOG_PRINTF_HELPER("success = %d, code = %d, message = %s, contextId = %d", success ? 1 : 0, code, *msg, contextId);
	FScopeLock lock(&mMutexMainLandQueryActCodeResultDelegate);
	if (mMapMainLandQueryActCodeResultDelegate.Contains(contextId))
	{
		auto callback = mMapMainLandQueryActCodeResultDelegate[contextId];
		AsyncTask(ENamedThreads::GameThread, [callback, code, msg, success]()
			{
				if (callback)
				{
					callback(code == 0, success, msg, code, msg);
				}

			});
		mMapMainLandQueryActCodeResultDelegate.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}

void OneEngineSDKPCHelper::SetUserAuthenticationResultDelegate(int contextId, UOneEngineSDKSubsystem::FOneUserAuthenticationResultDelegate Delegate)
{
	FScopeLock lock(&mMutexMapUserAuthentication);
	mMapUserAuthenticationResultDelegate.Emplace(contextId, Delegate);
}

void OneEngineSDKPCHelper::OnUserAuthenticationResult(bool success, int authResult, bool hasNetError, const FString& msg, int contextId)
{
	LOG_PRINTF_HELPER("success = %d, authResult = %d,  hasNetError = %d, message = %s, contextId = %d", success ? 1 : 0, authResult, hasNetError ? 1 : 0, *msg, contextId);
	FScopeLock lock(&mMutexMapUserAuthentication);
	if (mMapUserAuthenticationResultDelegate.Contains(contextId))
	{
		auto callback = mMapUserAuthenticationResultDelegate[contextId];
		AsyncTask(ENamedThreads::GameThread, [callback, authResult, hasNetError, msg, success]()
			{
				callback.ExecuteIfBound(success, authResult, hasNetError, msg);
			});
		mMapUserAuthenticationResultDelegate.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}

void OneEngineSDKPCHelper::OnInitResult(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate, bool success, int32 code, const FString& msg)
{
	AsyncTask(ENamedThreads::GameThread, [InitDelegate, success, code, msg]()
		{
			InitDelegate.ExecuteIfBound(success, code, msg);
		});
}

void OneEngineSDKPCHelper::OnAntiAddictionTimeout(bool bForceKick, const FString& antiInfo)
{
	LOG_PRINTF_HELPER("bForceKick : %d, antiInfo : %s", bForceKick ? 1 : 0, *antiInfo);
	FOneAntiAddictionInfo retInfo = ParseAntiInfo(antiInfo);
	FOneEngineSDKHelper::OnAntiAddictionTimeoutResultDelegate(bForceKick, retInfo);
}

void OneEngineSDKPCHelper::SetGetDeviceInfoDelegate(int contextId, const TFunction<void(const FOneDeviceInfo& DeviceInfo)>& OnFinishedLambda)
{
	FScopeLock lock(&mMutexMapGetDeviceInfo);
	mMapGetDeviceInfoResultDelegate.Emplace(contextId, OnFinishedLambda);
}

void OneEngineSDKPCHelper::OnGetDeviceInfo(int code, const FString& message, const FString& data, int contextId, const FString& deviceId, const FString& deviceSys)
{
	LOG_PRINTF_HELPER("code = %d, message = %s, data = %s, contextId = %d", code, *message, *data, contextId);
	FScopeLock lock(&mMutexMapGetDeviceInfo);
	if (mMapGetDeviceInfoResultDelegate.Contains(contextId))
	{
		auto callback = mMapGetDeviceInfoResultDelegate[contextId];
		FOneDeviceInfo info;
		info.DeviceId = deviceId;
		info.DeviceSys = deviceSys;
		if (!data.IsEmpty())
		{
			TSharedRef<TJsonReader<> > Reader = TJsonReaderFactory<>::Create(data);
			TSharedPtr<FJsonValue> jsonVaule;
			if (FJsonSerializer::Deserialize(Reader, jsonVaule))
			{
				auto dataJsonObject = jsonVaule->AsObject();
				if (dataJsonObject.IsValid())
				{
					for (auto& iterItem : dataJsonObject->Values)
					{
						FString key = iterItem.Key;
						FString value;
						if (iterItem.Value.IsValid() && iterItem.Value->TryGetString(value))
						{
							info.Ext.Add(key, value);
						}
					}
				}
			}
			else
			{
				LOG_PRINTF_HELPER("parse json struct failed = %s", *data);
			}
		}
		AsyncTask(ENamedThreads::GameThread, [callback, info]()
			{
				if (callback)
				{
					callback(info);
				}
			});
		mMapGetDeviceInfoResultDelegate.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}

void OneEngineSDKPCHelper::SetGenericResultDelegate(int contextId, UOneEngineSDKSubsystem::FOneGenericResultDelegate Delegate)
{
	FScopeLock lock(&mMutexMapGenericResultDelegate);
	mMapGenericResultDelegate.Emplace(contextId, Delegate);
}

void OneEngineSDKPCHelper::OnGenericResult(bool success, int code, const FString& msg, int contextId)
{
	LOG_PRINTF_HELPER("code = %d, message = %s, contextId = %d", code, *msg, contextId);
	FScopeLock lock(&mMutexMapGenericResultDelegate);
	if (mMapGenericResultDelegate.Contains(contextId))
	{
		auto callback = mMapGenericResultDelegate[contextId];
		AsyncTask(ENamedThreads::GameThread, [callback, success, code, msg]()
			{
				callback.ExecuteIfBound(success, code, msg);
			});
		mMapGenericResultDelegate.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}

void OneEngineSDKPCHelper::SetGetRoleListResultDelegate(int contextId, const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)>& OnFinishedLambda)
{
	FScopeLock lock(&mMutexMapGetRoleListResultDelegate);
	mMapGetRoleListResultDelegate.Emplace(contextId, OnFinishedLambda);
}

void OneEngineSDKPCHelper::OnGetRoleListResult(int code, const FString& msg, const FString& result, int contextId)
{
	TArray<FOneURCRoleInfo> arrInfo;
	LOG_PRINTF_HELPER("code =%d, msg = %s, result = %s, contextId = %d", code, *msg, *result, contextId);
	TSharedRef<TJsonReader<> > Reader = TJsonReaderFactory<>::Create(result);
	TSharedPtr<FJsonValue> jsonVaule;
	if (FJsonSerializer::Deserialize(Reader, jsonVaule))
	{
		TArray< TSharedPtr<FJsonValue> > jsonValueArray = jsonVaule->AsArray();
		if (jsonValueArray.Num())
		{
			for (auto item : jsonValueArray)
			{
				TSharedPtr<FJsonObject> JsonObject = item->AsObject();
				FOneURCRoleInfo info;
				JsonObject->TryGetStringField(TEXT("serverId"), info.ServerId);
				JsonObject->TryGetStringField(TEXT("serverName"), info.ServerName);
				JsonObject->TryGetStringField(TEXT("userId"), info.UserId);
				JsonObject->TryGetStringField(TEXT("roleId"), info.RoleId);
				JsonObject->TryGetStringField(TEXT("roleName"), info.RoleName);
				JsonObject->TryGetStringField(TEXT("lastLogin"), info.LastLoginTime);
				int64 level = 0;
				JsonObject->TryGetNumberField(TEXT("lev"), level);
				info.Level = FString::Printf(TEXT("%lld"), level);;
				int32 occupation = 0;
				JsonObject->TryGetNumberField(TEXT("occupation"), occupation);
				info.Occupation = FString::FromInt(occupation);
				int32 gender = 0;
				JsonObject->TryGetNumberField(TEXT("gender"), gender);
				info.Gender = FString::FromInt(gender);

				arrInfo.Add(info);
			}
		}
	}
	else
	{
		LOG_PRINTF_HELPER("parse json struct failed = %s", *result);
	}
	FScopeLock lock(&mMutexMapGetRoleListResultDelegate);
	if (mMapGetRoleListResultDelegate.Contains(contextId))
	{
		auto callback = mMapGetRoleListResultDelegate[contextId];
		AsyncTask(ENamedThreads::GameThread, [callback, code, msg, arrInfo]()
			{
				if (callback)
				{
					callback(code == 0, code, msg, arrInfo);
				}
			});
		mMapGetRoleListResultDelegate.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}

void OneEngineSDKPCHelper::OnActiveNoUICheckUser(int code, const FString& message, int contextId)
{
	LOG_PRINTF_HELPER("code: %d, message: %s, contextId: %d", code, *message, contextId);
	FScopeLock lock(&mMutexMapQueryActCodeResultLambda);
	if (mMapQueryActCodeResultLambda.Contains(contextId))
	{
		auto callback = mMapQueryActCodeResultLambda[contextId];
		AsyncTask(ENamedThreads::GameThread, [callback, code, message]()
			{
				if (callback)
				{
					callback(true, code != 0, message, code, message);
				}
			});
		mMapQueryActCodeResultLambda.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}

void OneEngineSDKPCHelper::OnActiveNoUIUserActivate(int code, const FString& message, int contextId)
{
	LOG_PRINTF_HELPER("code: %d, message: %s, contextId: %d", code, *message, contextId);
	FScopeLock lock(&mMutexMapExchangeActCodeResultLambda);
	if (mMapExchangeActCodeResultLambda.Contains(contextId))
	{
		auto callback = mMapExchangeActCodeResultLambda[contextId];
		AsyncTask(ENamedThreads::GameThread, [callback, code, message]()
			{
				if (callback)
				{
					callback(true, code, message);
				}
			});
		mMapExchangeActCodeResultLambda.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}

void OneEngineSDKPCHelper::SetGlobalQueryActCodeResultDelegate(const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)>& OnFinishedLambda, int contextId)
{
	FScopeLock lock(&mMutexMapQueryActCodeResultLambda);
	mMapQueryActCodeResultLambda.Emplace(contextId, OnFinishedLambda);
}

void OneEngineSDKPCHelper::SetGlobalExchangeActCodeResultDelegate(const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda, int contextId)
{
	FScopeLock lock(&mMutexMapExchangeActCodeResultLambda);
	mMapExchangeActCodeResultLambda.Emplace(contextId, OnFinishedLambda);
}

void OneEngineSDKPCHelper::SetFetchAntiAddictionInfoResultDelegate(int contextId, UOneEngineSDKSubsystem::FOneFetchAntiAddictionInfoDelegate Delegate)
{
	FScopeLock lock(&mMutexMapFetchAntiAddictionInfo);
	mMapFetchAntiAddictionInfoResultDelegate.Emplace(contextId, Delegate);
}

void OneEngineSDKPCHelper::OnFOneFetchAntiAddictionInfo(int code, const FString& result, int contextId)
{
	LOG_PRINTF_HELPER("code : %d, result : %s, contextId : %d", code, *result, contextId);
	FScopeLock lock(&mMutexMapFetchAntiAddictionInfo);
	if (mMapFetchAntiAddictionInfoResultDelegate.Contains(contextId))
	{
		FOneAntiAddictionInfo autiInfo = ParseAntiInfo(result);
		auto callback = mMapFetchAntiAddictionInfoResultDelegate[contextId];
		AsyncTask(ENamedThreads::GameThread, [callback, autiInfo]()
			{
				callback.ExecuteIfBound(autiInfo);
			});
		mMapFetchAntiAddictionInfoResultDelegate.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}

void OneEngineSDKPCHelper::SetBindResultDelegateAndBindType(UOneEngineSDKSubsystem::FOneBindResultDelegate BindDelegate, EOneEngineThirdType bindType)
{
	bindResultDelegate = BindDelegate;
	currentBindType = bindType;
}

void OneEngineSDKPCHelper::OnBindPhoneResult(int code, const FString& message, const FString& phoneNumber)
{
	auto callback = bindResultDelegate;
	AsyncTask(ENamedThreads::GameThread, [callback, code, message, phoneNumber]()
		{
			callback.ExecuteIfBound(code == 0, code, message, EOneEngineThirdType::Phone, OneEngineSDKPCAdapter::Get().GetInterface()->GetUserInfo());
		});
}

void OneEngineSDKPCHelper::OnBindWMPassportResult(int code, const FString& message, const FString& passport)
{
	auto callback = bindResultDelegate;
	AsyncTask(ENamedThreads::GameThread, [callback, code, message, passport]()
		{
			callback.ExecuteIfBound(code == 0, code, message, EOneEngineThirdType::WMPass, OneEngineSDKPCAdapter::Get().GetInterface()->GetUserInfo());
		});
}

void OneEngineSDKPCHelper::OnGlobalBindResult(const FString& userInfo, int code, const FString& message)
{
	auto callback = bindResultDelegate;
	auto bindType = currentBindType;
	int errorCode = code;
	if (errorCode == 10002)
	{
		errorCode = -10002;
	}
	AsyncTask(ENamedThreads::GameThread, [callback, errorCode, message, bindType]()
		{
			callback.ExecuteIfBound(errorCode == 0, errorCode, message,  bindType, OneEngineSDKPCAdapter::Get().GetInterface()->GetUserInfo());
		});
}

void OneEngineSDKPCHelper::SetGlobalPlatformDelegate(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const FString& Platform)>& OnFinishedLambda, int contextId)
{
	FScopeLock lock(&mMutexMapPlatformDelegateLambda);
	mMapPlatformDelegateLambda.Emplace(contextId, OnFinishedLambda);
}

void OneEngineSDKPCHelper::OnGlobalPlatform(int code, const FString& message, const FString& platform, int contextId)
{
	LOG_PRINTF_HELPER("code : %d, platform : %s, contextId : %d", code, *platform, contextId);
	FScopeLock lock(&mMutexMapPlatformDelegateLambda);
	if (mMapPlatformDelegateLambda.Contains(contextId))
	{
		auto callback = mMapPlatformDelegateLambda[contextId];
		AsyncTask(ENamedThreads::GameThread, [callback, code, message, platform]()
			{
				if (callback)
				{
					callback(code == 0, code, message, platform);
				}
			});
		mMapPlatformDelegateLambda.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}

FOneAntiAddictionInfo OneEngineSDKPCHelper::ParseAntiInfo(const FString& info)
{
	FOneAntiAddictionInfo retInfo;
	FJsonObjectWrapper Wraper;
	if (!info.IsEmpty() && Wraper.JsonObjectFromString(info))
	{
		if (Wraper.JsonObject.IsValid())
		{
			Wraper.JsonObject->TryGetStringField(TEXT("requestIp"), retInfo.RequestIp);
			TSharedPtr< FJsonObject > resultJsonObject = Wraper.JsonObject->GetObjectField(TEXT("result"));
			if (resultJsonObject.IsValid())
			{
				resultJsonObject->TryGetNumberField(TEXT("status"), retInfo.Status);
				//resultJsonObject->TryGetNumberField(TEXT("heartbeatInterval"), info.HeartbeatInterval);
				resultJsonObject->TryGetNumberField(TEXT("newHeartbeatInterval"), retInfo.HeartbeatInterval);
				resultJsonObject->TryGetNumberField(TEXT("realUser"), retInfo.Realuser);
				resultJsonObject->TryGetNumberField(TEXT("civicType"), retInfo.CivicType);
				resultJsonObject->TryGetNumberField(TEXT("age"), retInfo.Age);
				resultJsonObject->TryGetNumberField(TEXT("gender"), retInfo.Gender);
				resultJsonObject->TryGetNumberField(TEXT("accountType"), retInfo.AccountType);
				resultJsonObject->TryGetNumberField(TEXT("dayOnlineDuration"), retInfo.DayOnlineDuration);
				int appId = 0;
				resultJsonObject->TryGetNumberField(TEXT("appId"), appId);
				retInfo.AppID = FString::FromInt(appId);
				retInfo.BannedType = 0;
				resultJsonObject->TryGetStringField(TEXT("userId"), retInfo.UserId);
				resultJsonObject->TryGetNumberField(TEXT("bannedType"), retInfo.BannedType);
				resultJsonObject->TryGetStringField(TEXT("bannedReason"), retInfo.BannedReason);
				resultJsonObject->TryGetStringField(TEXT("breakNotice"), retInfo.BreakNotice);
			}
		}
	}
	else
	{
		LOG_PRINTF_HELPER("parse jsonstring failed");
	}
	return retInfo;
}

EOneEngineThirdType OneEngineSDKPCHelper::CovertGlobalLoginTypeToEngineThirdType(int LoginType)
{
	EOneEngineThirdType retType = EOneEngineThirdType::Guest;

	if (LoginType == 0)
	{
		retType = EOneEngineThirdType::Guest;
	}
	else if (LoginType == 1)
	{
		retType = EOneEngineThirdType::Facebook;
	}
	else if (LoginType == 2)
	{
		retType = EOneEngineThirdType::Google;
	}
	else if (LoginType == 3)
	{
		retType = EOneEngineThirdType::Twitter;
	}
	else if (LoginType == 4)
	{
		retType = EOneEngineThirdType::Line;
	}
	else if (LoginType == 5)
	{
		retType = EOneEngineThirdType::GooglePlay;
	}
	else if (LoginType == 6)
	{
		retType = EOneEngineThirdType::GameCenter;
	}
	else if (LoginType == 7)
	{
		retType = EOneEngineThirdType::Phone;
	}
	else if (LoginType == 8)
	{
		retType = EOneEngineThirdType::Email;
	}
	else if (LoginType == 9)
	{
		retType = EOneEngineThirdType::VK;
	}
	else if (LoginType == 10)
	{
		retType = EOneEngineThirdType::Naver;
	}
	else if (LoginType == 11)
	{
		retType = EOneEngineThirdType::Apple;
	}
	else if (LoginType == 12)
	{
		retType = EOneEngineThirdType::WeChat;
	}
	else if (LoginType == 13)
	{
		retType = EOneEngineThirdType::GuestInherit;
	}
	else if (LoginType == 14)
	{
		retType = EOneEngineThirdType::Yandex;
	}
	else if (LoginType == 15)
	{
		retType = EOneEngineThirdType::MailRu;
	}
	else if (LoginType == 16)
	{
		retType = EOneEngineThirdType::Infiplay;
	}
	else if (LoginType == 17)
	{
		retType = EOneEngineThirdType::HW;
	}
	else if (LoginType == 18)
	{
		retType = EOneEngineThirdType::ShareCode;
	}
	else if (LoginType == 19)
	{
		retType = EOneEngineThirdType::HONOR;
	}
	else if (LoginType == 22)
	{
		retType = EOneEngineThirdType::PCScanCode;
	}
	else if (LoginType == 24)
	{
		retType = EOneEngineThirdType::APJ;
	}
	else if (LoginType == 25)
	{
		retType = EOneEngineThirdType::Crunchyrool;
	}
	else if (LoginType == 50)
	{
		retType = EOneEngineThirdType::NaverCafe;
	}
	else if (LoginType == 26)
	{
		retType = EOneEngineThirdType::LinkAccount;
	}
	else if(LoginType == 23)
	{
		retType = EOneEngineThirdType::PlayStation;
	}

	return retType;
}

int OneEngineSDKPCHelper::CovertEngineThirdTypeToGlobalLoginType(EOneEngineThirdType LoginType)
{
	int nativeLoginType = -1;
	switch (LoginType)
	{
	case EOneEngineThirdType::Phone:
		nativeLoginType = 7;
		break;
	case EOneEngineThirdType::WMPass:
		nativeLoginType = -1;
		break;
	case EOneEngineThirdType::Guest:
		nativeLoginType = 0;
		break;
	case EOneEngineThirdType::Facebook:
		nativeLoginType = 1;
		break;
	case EOneEngineThirdType::Twitter:
		nativeLoginType = 3;
		break;
	case EOneEngineThirdType::Line:
		nativeLoginType = 4;
		break;
	case EOneEngineThirdType::GameCenter:
		nativeLoginType = 6;
		break;
	case EOneEngineThirdType::VK:
		nativeLoginType = 9;
		break;
	case EOneEngineThirdType::Naver:
		nativeLoginType = 10;
		break;
	case EOneEngineThirdType::Apple:
		nativeLoginType = 11;
		break;
	case EOneEngineThirdType::WeChat:
		nativeLoginType = 12;
		break;
	case EOneEngineThirdType::GuestInherit:
		nativeLoginType = 13;
		break;
	case EOneEngineThirdType::NaverCafe:
		nativeLoginType = 50;
		break;
	case EOneEngineThirdType::Yandex:
		nativeLoginType = 14;
		break;
	case EOneEngineThirdType::MailRu:
		nativeLoginType = 15;
		break;
	case EOneEngineThirdType::Infiplay:
		nativeLoginType = 16;
		break;
	case EOneEngineThirdType::ShareCode:
		nativeLoginType = 18;
		break;
	case EOneEngineThirdType::PCScanCode:
		nativeLoginType = 22;
		break;
	case EOneEngineThirdType::Email:
		nativeLoginType = 8;
		break;
	case EOneEngineThirdType::APJ:
		nativeLoginType = 24;
		break;
	case EOneEngineThirdType::Crunchyrool:
		nativeLoginType = 25;
		break;
	case EOneEngineThirdType::Google:
		nativeLoginType = 2;
		break;
	case EOneEngineThirdType::GooglePlay:
		nativeLoginType = 5;
		break;
	case EOneEngineThirdType::HW:
		nativeLoginType = 17;
		break;
	case EOneEngineThirdType::HONOR:
		nativeLoginType = 19;
		break;
	case EOneEngineThirdType::LinkAccount:
		nativeLoginType = 26;
		break;
	case EOneEngineThirdType::PlayStation:
		nativeLoginType = 23;
		break;
	default:
		break;
	}
	return nativeLoginType;
}

FOneUserInfo OneEngineSDKPCHelper::ParseGlobalUserInfo(const FString& info)
{
	FOneUserInfo userInfo;
	FJsonObjectWrapper Wraper;
	if (Wraper.JsonObjectFromString(info))
	{
		if (Wraper.JsonObject.IsValid())
		{
			Wraper.JsonObject->TryGetStringField(TEXT("username"), userInfo.UserName);
			Wraper.JsonObject->TryGetStringField(TEXT("uid"), userInfo.UserId);
			Wraper.JsonObject->TryGetStringField(TEXT("token"), userInfo.Token);
			Wraper.JsonObject->TryGetStringField(TEXT("avatar"), userInfo.Avatar);
			Wraper.JsonObject->TryGetStringField(TEXT("phoneNumber"), userInfo.Phone);
			Wraper.JsonObject->TryGetStringField(TEXT("countryCode"), userInfo.CountryCode);
			Wraper.JsonObject->TryGetStringField(TEXT("inheritCode"), userInfo.InheritCode);
			Wraper.JsonObject->TryGetBoolField(TEXT("isNewCreate"), userInfo.bIsNewCreate);
			Wraper.JsonObject->TryGetStringField(TEXT("ageCountryCode"), userInfo.AgeCountryCode);

			int isAdult = 0;
			Wraper.JsonObject->TryGetNumberField(TEXT("isAdult"), isAdult);
			userInfo.bIsAdult = isAdult != 0;

			int passwordExist = 0;
			Wraper.JsonObject->TryGetNumberField(TEXT("passwordExist"), passwordExist);
			userInfo.bPasswordExist = passwordExist != 0;
			Wraper.JsonObject->TryGetNumberField(TEXT("age"), userInfo.Age);
			int loginType = 0;
			Wraper.JsonObject->TryGetNumberField(TEXT("loginType"), loginType);
			userInfo.Type = CovertGlobalLoginTypeToEngineThirdType(loginType);
			const TArray< TSharedPtr<FJsonValue> > *thirdUsers;

			if (Wraper.JsonObject->TryGetArrayField(TEXT("thirdUsers"), thirdUsers))
			{
				if (thirdUsers->Num() > 0)
				{
					TArray<FOneUserThirdInfo> ThirdPartyUserInfoList;
					for (auto value : *thirdUsers)
					{
						FOneUserThirdInfo thirdUserInfo;
						TSharedPtr<FJsonObject> object = value->AsObject();
						if (object)
						{
							object->TryGetStringField(TEXT("uid"), thirdUserInfo.UserId);
							object->TryGetStringField(TEXT("thirdId"), thirdUserInfo.ThirdId);
							object->TryGetStringField(TEXT("username"), thirdUserInfo.UserName);
							object->TryGetStringField(TEXT("thirdEmail"), thirdUserInfo.Email);
							object->TryGetStringField(TEXT("avatar"), thirdUserInfo.Avatar);
							loginType = 0;
							object->TryGetNumberField(TEXT("thirdType"), loginType);
							thirdUserInfo.Type = CovertGlobalLoginTypeToEngineThirdType(loginType);
							ThirdPartyUserInfoList.Add(thirdUserInfo);
						}

					}
					userInfo.Thirds = ThirdPartyUserInfoList;
				}
			}
		}
	}
	else
	{
		LOG_PRINTF_HELPER("GetUserInfo parse jsonstring failed");
	}
	return userInfo;
}

void OneEngineSDKPCHelper::OnGlobalGetTokenList(int code, const FString& message, const FString& tokens, int contextId)
{
	TArray<FOneUserInfo> tokenList;

	TSharedRef<TJsonReader<> > Reader = TJsonReaderFactory<>::Create(tokens);
	TSharedPtr<FJsonValue> jsonVaule;
	if (FJsonSerializer::Deserialize(Reader, jsonVaule))
	{
		TArray< TSharedPtr<FJsonValue> > jsonValueArray = jsonVaule->AsArray();
		if (jsonValueArray.Num())
		{
			for (auto item : jsonValueArray)
			{
				TSharedPtr<FJsonObject> JsonObject = item->AsObject();
				FOneUserInfo info;

				JsonObject->TryGetStringField(TEXT("username"), info.UserName);
				JsonObject->TryGetStringField(TEXT("uid"), info.UserId);
				JsonObject->TryGetStringField(TEXT("token"), info.Token);
				JsonObject->TryGetStringField(TEXT("avatar"), info.Avatar);
				int loginType = 0;
				JsonObject->TryGetNumberField(TEXT("loginType"), loginType);
				info.Type = CovertGlobalLoginTypeToEngineThirdType(loginType);
				tokenList.Add(info);
			}
		}
	}
	else
	{
		LOG_PRINTF_HELPER("parse json struct failed");
	}

	FScopeLock lock(&mMutexMapGetTokenListLambda);
	if (mMapGetTokenListLambda.Contains(contextId))
	{
		auto callback = mMapGetTokenListLambda[contextId];
		AsyncTask(ENamedThreads::GameThread, [callback, code, message, tokenList]()
			{
				if (callback)
				{
					callback(code == 0, code, message, tokenList);
				}
			});
		mMapGetTokenListLambda.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}

void OneEngineSDKPCHelper::SetGlobalGetTokenListDelegate(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)>& OnFinishedLambda, int contextId)
{
	FScopeLock lock(&mMutexMapGetTokenListLambda);
	mMapGetTokenListLambda.Emplace(contextId, OnFinishedLambda);
}

void OneEngineSDKPCHelper::SetACEClientPacketDelegate(UOneEngineSDKSubsystem::FOneOnGetClientPacket delegate)
{
	OnGetClientPacketDelegate = delegate;
}

void OneEngineSDKPCHelper::OnGetACEClientPacket(const unsigned char* data, int len)
{
	auto callback = OnGetClientPacketDelegate;
	TArray<uint8> dataArray(data, len);
	AsyncTask(ENamedThreads::GameThread, [callback, dataArray]()
		{
			callback.ExecuteIfBound(dataArray);
		});
}

void OneEngineSDKPCHelper::SetSafeLockResultDelegate(int contextId, UOneEngineSDKSubsystem::FOneUnlockSafeLockResultDelegate Delegate)
{
	FScopeLock lock(&mMutexSafeUnlock);
	mMapSafeUnlockResultDelegate.Emplace(contextId, Delegate);
}

void OneEngineSDKPCHelper::OnSafeLockResult(EOneUnlockSafeLockResult result, int code, const FString& message, const FString& unlockToken, EOneUnlockSafeLockType lockType, int contextId)
{
	LOG_PRINTF_HELPER("code: %d, message: %s, result: %d, lockType: %d, contextId: %d", code, *message, result, lockType, contextId);
	FScopeLock lock(&mMutexSafeUnlock);
	if (mMapSafeUnlockResultDelegate.Contains(contextId))
	{
		auto callback = mMapSafeUnlockResultDelegate[contextId];
		AsyncTask(ENamedThreads::GameThread, [callback, code, result, message, unlockToken, lockType]()
			{
				callback.ExecuteIfBound(result, unlockToken, code, message, lockType);
			});
		if (lockType == EOneUnlockSafeLockType::PushNotification && result == EOneUnlockSafeLockResult::WaitingToUnlock)
		{
			return;
		}
		else
		{
			mMapSafeUnlockResultDelegate.Remove(contextId);
		}
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}

void OneEngineSDKPCHelper::SetGetIpInfoResultDelegate(UOneEngineSDKSubsystem::FOnGetIpInfoResultDelegate OnGetIpInfoDelegate, int contextId)
{
	FScopeLock lock(&mMutexGetIpInfo);
	mMapGetIpInfoDelegate.Emplace(contextId, OnGetIpInfoDelegate);
}

void OneEngineSDKPCHelper::OnGetIpInfo(int code, const FString& message, const FString& result, int contextId)
{
	LOG_PRINTF_HELPER("code:%d, message:%s, result:%s", code, *message, *result);
	FScopeLock lock(&mMutexGetIpInfo);
	if (mMapGetIpInfoDelegate.Contains(contextId))
	{
		auto callback = mMapGetIpInfoDelegate[contextId];
		AsyncTask(ENamedThreads::GameThread, [callback, code, result, message]()
			{
				FUserIpInfo info;
				FJsonObjectWrapper Wraper;
				if (!result.IsEmpty() && Wraper.JsonObjectFromString(result) && Wraper.JsonObject.IsValid())
				{
					if (OneEngineSDKPCAdapter::Get().GetEngineType() == EOneEngineSDKRegionType::Mainland)
					{
						Wraper.JsonObject->TryGetStringField(TEXT("attribution"), info.Attribution);
						Wraper.JsonObject->TryGetStringField(TEXT("countryCode"), info.CountryCode);
						Wraper.JsonObject->TryGetStringField(TEXT("cityCode"), info.CityCode);
						Wraper.JsonObject->TryGetStringField(TEXT("country"), info.Country);
						Wraper.JsonObject->TryGetStringField(TEXT("region"), info.Region);
						Wraper.JsonObject->TryGetStringField(TEXT("city"), info.City);
					}
					else
					{
						Wraper.JsonObject->TryGetStringField(TEXT("province"), info.Attribution);
						Wraper.JsonObject->TryGetStringField(TEXT("countryAbbr"), info.CountryCode);
						//Wraper.JsonObject->TryGetStringField(TEXT("countryCode"), info.CityCode);
						Wraper.JsonObject->TryGetStringField(TEXT("country"), info.Country);
						Wraper.JsonObject->TryGetStringField(TEXT("province"), info.Region);
						Wraper.JsonObject->TryGetStringField(TEXT("city"), info.City);
					}
					
				}
				else
				{
					LOG_PRINTF_HELPER("parse jsonstring failed %s", *result);
				}
				callback.ExecuteIfBound(code == 0, info, code, message);
			});
		mMapGetIpInfoDelegate.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}

void OneEngineSDKPCHelper::SetGetProductListResultDelegate(int contextId, UOneEngineSDKSubsystem::FOneProductInfoDelegate ProductResultDelegate)
{
	FScopeLock lock(&mMutexGetProductList);
	mMapGetProductListResultDelegate.Emplace(contextId, ProductResultDelegate);
}

void OneEngineSDKPCHelper::OnGetProductList(int contextId, const FString& msg, const FString& result, int code)
{
	LOG_PRINTF_HELPER("code =%d, msg = %s, contextId = %d", code, *msg, contextId);
	FScopeLock lock(&mMutexGetProductList);
	if (mMapGetProductListResultDelegate.Contains(contextId))
	{
		auto callback = mMapGetProductListResultDelegate[contextId];
		AsyncTask(ENamedThreads::GameThread, [callback, code, msg, result]()
			{
				TArray<FOneProductInfo> productInfoArray;
				if (code == 0)
				{
					FJsonObjectWrapper Wraper;
					if (!result.IsEmpty() && Wraper.JsonObjectFromString(result) && Wraper.JsonObject.IsValid())
					{
						FString origin;
						FString data;
						Wraper.JsonObject->TryGetStringField(TEXT("origin"), origin);
						Wraper.JsonObject->TryGetStringField(TEXT("data"), data);
						if (origin == "google")
						{
							TSharedRef<TJsonReader<> > Reader = TJsonReaderFactory<>::Create(data);
							TSharedPtr<FJsonValue> jsonVaule;
							if (FJsonSerializer::Deserialize(Reader, jsonVaule))
							{
								TArray< TSharedPtr<FJsonValue> > jsonValueArray = jsonVaule->AsArray();
								if (jsonValueArray.Num())
								{
									for (auto item : jsonValueArray)
									{
										TSharedPtr<FJsonObject> JsonObject = item->AsObject();
										FOneProductInfo info;
										JsonObject->TryGetStringField(TEXT("sku"), info.ProductId);
										JsonObject->TryGetStringField(TEXT("description"), info.Desc);
										JsonObject->TryGetStringField(TEXT("title"), info.Title);

										if (JsonObject->HasTypedField<EJson::Object>(TEXT("localizedPrice")))
										{
											TSharedPtr<FJsonObject> priceObj = JsonObject->GetObjectField(TEXT("localizedPrice"));
											priceObj->TryGetStringField(TEXT("priceMicros"), info.Price);
											priceObj->TryGetStringField(TEXT("formattedPrice"), info.SymbolPrice);
											priceObj->TryGetStringField(TEXT("currency"), info.Currency);
											int64 MyInt = FCString::Atoi64(*info.Price);
											if (MyInt > 0)
											{
												MyInt = MyInt / 10000;
												info.Price = FString::Format(TEXT("{0}"), { MyInt });
											}
										}
										productInfoArray.Add(info);
									}
								}
							}

						}
						else
						{
							TSharedRef<TJsonReader<> > Reader = TJsonReaderFactory<>::Create(data);
							TSharedPtr<FJsonValue> jsonVaule;
							if (FJsonSerializer::Deserialize(Reader, jsonVaule))
							{
								TArray< TSharedPtr<FJsonValue> > jsonValueArray = jsonVaule->AsArray();
								if (jsonValueArray.Num())
								{
									for (auto item : jsonValueArray)
									{
										TSharedPtr<FJsonObject> JsonObject = item->AsObject();
										FOneProductInfo info;
										JsonObject->TryGetStringField(TEXT("productId"), info.ProductId);
										JsonObject->TryGetStringField(TEXT("currency"), info.Currency);
										JsonObject->TryGetStringField(TEXT("title"), info.Title);
										int64 amount = 0;
										JsonObject->TryGetNumberField(TEXT("amount"), amount);
										info.Price = FString::Format(TEXT("{0}"), { amount });
										double price = amount * 1.0 / 100;
										info.SymbolPrice = info.Currency + FString::Printf(TEXT("%.2f"), price);
										productInfoArray.Add(info);
									}
								}
							}
						}
					}
					else
					{
						LOG_PRINTF_HELPER("parse json struct failed");
					}
				}
				callback.ExecuteIfBound(code == 0, code, productInfoArray);
			});
		mMapGetProductListResultDelegate.Remove(contextId);
	}
	else
	{
		LOG_PRINTF_HELPER("cannot find contextId = %d", contextId);
	}
}
