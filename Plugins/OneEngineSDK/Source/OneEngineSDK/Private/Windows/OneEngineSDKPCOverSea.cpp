#ifdef KONEENGINE_REGION_OVERSEA
#include "OneEngineSDKPCOverSea.h"
#include "OneEngineSDKPCHelper.h"
#include "OneEngineSDKHelper.h"
#include "JsonObjectWrapper.h"
#include "Serialization/JsonSerializer.h"
#include "Windows/WindowsPlatformMisc.h"
#include "Async/Async.h"
#include "PcGlobalSdk.h"


DEFINE_LOG_CATEGORY(PC_OneEngineSDK_OverSea);

#define CURRENT_CLASS_FUNCTION_OVERSEA (FString(__FUNCTION__))
#define CURRENT_LINE_OVERSEA  (FString::FromInt(__LINE__))
#define CURRENT_CLASS_FUNCTION_LINE_OVERSEA (CURRENT_CLASS_FUNCTION_OVERSEA + ":LNO." + CURRENT_LINE_OVERSEA )

#define TRACK_OVERSEA \
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK_OverSea, Log, TEXT("%s"),*CURRENT_CLASS_FUNCTION_LINE_OVERSEA); \
	}\
}

#define LOG_PRINTF_OVERSEA(FormatString , ...) \
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK_OverSea, Log, TEXT("%s: %s"), *CURRENT_CLASS_FUNCTION_LINE_OVERSEA, *FString::Printf(TEXT(FormatString), ##__VA_ARGS__ ) ); \
	}\
}

#define LOG_ERROR_PRINTF_OVERSEA(FormatString , ...) \
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK_OverSea, Error, TEXT("%s: %s"), *CURRENT_CLASS_FUNCTION_LINE_OVERSEA, *FString::Printf(TEXT(FormatString), ##__VA_ARGS__ ) ); \
	}\
}

#define LOG_ERROR_PRINTF_ERROR_INVOKE_OVERSEA \
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK_OverSea, Error, TEXT("%s: ERROR INVOKE"), *CURRENT_CLASS_FUNCTION_LINE_OVERSEA); \
	}\
}

typedef struct Function_IO_LOG_OVERSEA
{
public:
	Function_IO_LOG_OVERSEA(const FString& val)
	{
		if (OneEngineSDKPCAdapter::Get().IsDebugMode())
		{
			mVal = val;
			UE_LOG(PC_OneEngineSDK_OverSea, Log, TEXT("%s start"), *mVal);
		}
	}
	~Function_IO_LOG_OVERSEA()
	{
		if (OneEngineSDKPCAdapter::Get().IsDebugMode())
		{
			UE_LOG(PC_OneEngineSDK_OverSea, Log, TEXT("%s end"), *mVal);
		}
	}
private:
	FString mVal;

}Function_IO_LOG_OVERSEA;

#define LOG_FUNCTION_IO_OVERSEA Function_IO_LOG_OVERSEA a(CURRENT_CLASS_FUNCTION_LINE_OVERSEA)

bool OneEngineSDKPCOverSeaInterface::OnQuitGame(bool bForcible)
{
	LOG_PRINTF_OVERSEA("bForcible:%d", bForcible ? 1 : 0);
	FOneEngineSDKHelper::OnLogoutResultDelegate(true, 0, TEXT("success"));
	return false;
}

void OneEngineSDKPCOverSeaInterface::OnRelogin()
{
	LOG_FUNCTION_IO_OVERSEA;
	FOneEngineSDKHelper::OnLogoutResultDelegate(true, 0, TEXT("success"));
}

void OneEngineSDKPCOverSeaInterface::OnBeKickedOut()
{
	LOG_FUNCTION_IO_OVERSEA;
	FOneEngineSDKHelper::OnLogoutResultDelegate(true, 0, TEXT("success"));
}

void OneEngineSDKPCOverSeaInterface::OnEndPIE(bool val)
{
	LOG_FUNCTION_IO_OVERSEA;
	LOG_PRINTF_OVERSEA("EndPIE %d", val == true ? 1 : 0);
	if (OneEngineSDKPCHelper::Get().GetHandle() != nullptr)
	{
		HRESULT ret = PC_GLOBAL_SDK::OneSdk_UnInit(OneEngineSDKPCHelper::Get().GetHandle());
		if (ret == S_OK)
		{
			LOG_PRINTF_OVERSEA("one uninit success");
		}
		OneEngineSDKPCHelper::Get().SetHandle(nullptr);
		LOG_PRINTF_OVERSEA("EndPIE %d", val == true ? 1 : 0);
	}
}

void OneEngineSDKPCOverSeaInterface::Init(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate)
{
	LOG_FUNCTION_IO_OVERSEA;
	FString appId = TEXT("global");
	FString appKey = TEXT("global.key");
	FString ConfigPath = TEXT("global.config");
    auto handle = PC_GLOBAL_SDK::OneSdk_Union_Init(TCHAR_TO_WCHAR(*appId), TCHAR_TO_WCHAR(*appKey), TCHAR_TO_WCHAR(*ConfigPath),
        OneEngineSDKPCOverSeaInterface::OnQuitGame, OneEngineSDKPCOverSeaInterface::OnRelogin, OneEngineSDKPCOverSeaInterface::OnBeKickedOut);
    if (handle == nullptr)
    {
        LOG_PRINTF_OVERSEA("handle == nullptr");
        OneEngineSDKPCHelper::Get().OnInitResult(InitDelegate, false, 10001, TEXT("init failed"));
        if (PC_GLOBAL_SDK::LoadGlobalSDKSuccess())
        {
            LOG_PRINTF_OVERSEA("OneEngineSDK LoadLibrary Success");
        }
        else
        {
            LOG_PRINTF_OVERSEA("OneEngineSDK LoadLibrary Failed");
        }
    }
    else
    {
        LOG_PRINTF_OVERSEA("init success");
        OneEngineSDKPCHelper::Get().OnInitResult(InitDelegate, true, 0, TEXT("init success"));
        OneEngineSDKPCHelper::Get().SetHandle(handle);
		PC_GLOBAL_SDK::global_ReportSDKVersionInfo(TCHAR_TO_UTF8(*OneEngineSDKPCHelper::Get().GetPluginVersion()));
#if WITH_EDITOR
        if (!bBindEndPIE)
        {
            FEditorDelegates::EndPIE.AddRaw(this, &OneEngineSDKPCOverSeaInterface::OnEndPIE);
            bBindEndPIE = true;
        }
#endif
    }

	LOG_PRINTF_OVERSEA("OneEngineSDK Version %s", *OneEngineSDKPCHelper::Get().GetPluginVersion());
}

void OneEngineSDKPCOverSeaInterface::TrackEventRoleLoginSucceed(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
	LOG_FUNCTION_IO_OVERSEA;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	ConstructRoleInfo(jsonObject, RoleInfo);

	jsonObject->SetStringField(TEXT("ip"), Ip);
	jsonObject->SetStringField(TEXT("port"), Port);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	FString eventKey = TEXT("roleLoginSDK");
	LOG_PRINTF_OVERSEA("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_GLOBAL_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
	PC_GLOBAL_SDK::global_appflyerADTrack(TCHAR_TO_WCHAR(*eventKey), TCHAR_TO_WCHAR(*ValueJson));
}

void OneEngineSDKPCOverSeaInterface::TrackEventRoleLoginError(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port, const FString& Code, const FString& Msg)
{
	LOG_FUNCTION_IO_OVERSEA;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	ConstructRoleInfo(jsonObject, RoleInfo);

	jsonObject->SetStringField(TEXT("ip"), Ip);
	jsonObject->SetStringField(TEXT("port"), Port);
	jsonObject->SetStringField(TEXT("errorCode"), Code);
	jsonObject->SetStringField(TEXT("errorMsg"), Msg);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	FString eventKey = TEXT("roleLoginErrorSDK");
	LOG_PRINTF_OVERSEA("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_GLOBAL_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
}

void OneEngineSDKPCOverSeaInterface::TrackEventRoleLogout(const FOneRoleInfo& RoleInfo)
{
	LOG_FUNCTION_IO_OVERSEA;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	ConstructRoleInfo(jsonObject, RoleInfo);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	FString eventKey = TEXT("roleLogoutSDK");
	LOG_PRINTF_OVERSEA("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_GLOBAL_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
	PC_GLOBAL_SDK::global_appflyerADTrack(TCHAR_TO_WCHAR(*eventKey), TCHAR_TO_WCHAR(*ValueJson));
}

void OneEngineSDKPCOverSeaInterface::TrackEventRoleCreate(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
	LOG_FUNCTION_IO_OVERSEA;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	ConstructRoleInfo(jsonObject, RoleInfo);

	jsonObject->SetStringField(TEXT("ip"), Ip);
	jsonObject->SetStringField(TEXT("port"), Port);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	FString eventKey = TEXT("Create_role");
	LOG_PRINTF_OVERSEA("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_GLOBAL_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
	PC_GLOBAL_SDK::global_appflyerADTrack(TCHAR_TO_WCHAR(*eventKey), TCHAR_TO_WCHAR(*ValueJson));
}

void OneEngineSDKPCOverSeaInterface::TrackEventRoleLevelUp(const FOneRoleInfo& RoleInfo)
{
	LOG_FUNCTION_IO_OVERSEA;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	ConstructRoleInfo(jsonObject, RoleInfo);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	FString eventKey = TEXT("roleLevelUpTo") + RoleInfo.Level;
	LOG_PRINTF_OVERSEA("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_GLOBAL_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
	PC_GLOBAL_SDK::global_appflyerADTrack(TCHAR_TO_WCHAR(*eventKey), TCHAR_TO_WCHAR(*ValueJson));
}

void OneEngineSDKPCOverSeaInterface::GameResReqEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);
	FString eventKey = TEXT("gameResReqError");
	if (State == EOneResEventState::Begin)
	{
		eventKey = TEXT("gameResReqBegin");
	}
	else if (State == EOneResEventState::Success)
	{
		eventKey = TEXT("gameResReqSuccess");
	}

	jsonObject->SetStringField(TEXT("url"), Url);
	jsonObject->SetStringField(TEXT("errorCode"), ErrorCode);
	jsonObject->SetStringField(TEXT("errorMsg"), ErrorMsg);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);

	LOG_PRINTF_OVERSEA("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_GLOBAL_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
}

void OneEngineSDKPCOverSeaInterface::GameUpdateAssetEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);
	FString eventKey = TEXT("gameUpdateAssetError");
	if (State == EOneResEventState::Begin)
	{
		eventKey = TEXT("gameUpdateAssetBegin");
	}
	else if (State == EOneResEventState::Success)
	{
		eventKey = TEXT("gameUpdateAssetSuccess");
	}

	jsonObject->SetStringField(TEXT("url"), Url);
	jsonObject->SetStringField(TEXT("errorCode"), ErrorCode);
	jsonObject->SetStringField(TEXT("errorMsg"), ErrorMsg);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);

	LOG_PRINTF_OVERSEA("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_GLOBAL_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
}

void OneEngineSDKPCOverSeaInterface::GameResDecEvent(EOneResEventState State, const FString& ErrorMsg)
{
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);
	FString eventKey = TEXT("gameResDecError");
	if (State == EOneResEventState::Begin)
	{
		eventKey = TEXT("gameResDecBegin");
	}
	else if (State == EOneResEventState::Success)
	{
		eventKey = TEXT("gameResDecSuccess");
	}

	jsonObject->SetStringField(TEXT("errorMsg"), ErrorMsg);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);

	LOG_PRINTF_OVERSEA("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_GLOBAL_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
}

void OneEngineSDKPCOverSeaInterface::GameGetServerListEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);
	FString eventKey = TEXT("gameGetServerListError");
	if (State == EOneResEventState::Begin)
	{
		eventKey = TEXT("gameGetServerListBegin");
	}
	else if (State == EOneResEventState::Success)
	{
		eventKey = TEXT("gameGetServerListSuccess");
	}

	jsonObject->SetStringField(TEXT("url"), Url);
	jsonObject->SetStringField(TEXT("errorCode"), ErrorCode);
	jsonObject->SetStringField(TEXT("errorMsg"), ErrorMsg);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);

	LOG_PRINTF_OVERSEA("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_GLOBAL_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
}

void OneEngineSDKPCOverSeaInterface::TrackEvent(const FString& Name, const TMap<FString, FString>& Payload)
{
	TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
	for (const auto& Pair : Payload)
	{
		JsonObj->SetStringField(Pair.Key, Pair.Value);
	}
	FJsonObjectWrapper Wrapper;
	Wrapper.JsonObject = JsonObj;
	FString Result;
	Wrapper.JsonObjectToString(Result);

	LOG_PRINTF_OVERSEA("key:%s, hints:%s", *Name, *Result);
	PC_GLOBAL_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*Name), TCHAR_TO_UTF8(*Result), nullptr);
}

void OneEngineSDKPCOverSeaInterface::TrackEventEnterGameScene(const FString& SceneName, int32 Period, const TMap<FString, FString>& HintMap)
{
	LOG_FUNCTION_IO_OVERSEA;
	LOG_PRINTF_OVERSEA("SceneName = %s,  Period = %d, HintMap Num = %d", *SceneName, Period, HintMap.Num());
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	if (HintMap.Num() > 0)
	{
		for (auto& ele : HintMap)
		{
			jsonObject->SetStringField(ele.Key, ele.Value);
		}
	}

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	LOG_PRINTF_OVERSEA("HintMap String = %s", *ValueJson);
	PC_GLOBAL_SDK::OneSdk_TrackEventEnterGameScene(TCHAR_TO_UTF8(*SceneName), Period, TCHAR_TO_UTF8(*ValueJson));
}

void OneEngineSDKPCOverSeaInterface::TrackEventExitGameScene()
{
	LOG_FUNCTION_IO_OVERSEA;

	PC_GLOBAL_SDK::OneSdk_TrackEventExitGameScene("");
}

void OneEngineSDKPCOverSeaInterface::TrackEventAddExtraDeviceInfo(const TMap<FString, FString>& ExtraDeviceInfo)
{
	LOG_FUNCTION_IO_OVERSEA;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	if (ExtraDeviceInfo.Num() > 0)
	{
		for (auto& ele : ExtraDeviceInfo)
		{
			jsonObject->SetStringField(ele.Key, ele.Value);
		}
	}
	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	LOG_PRINTF_OVERSEA("ExtraDeviceInfo = %s", *ValueJson);
	PC_GLOBAL_SDK::OneSdk_TrackAddExtraDeviceInfo(TCHAR_TO_UTF8(*ValueJson));
}

void OneEngineSDKPCOverSeaInterface::RedeemCouponCode(const FString& CouponCode, const FString& ServerId, const FString& RoleId, const FString& RoleLevel, const FString& VipLevel, const TMap<FString, FString>& ExtraInfo, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda)
{
	LOG_FUNCTION_IO_OVERSEA;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	jsonObject->SetStringField(TEXT("roleId"), RoleId);
	jsonObject->SetStringField(TEXT("roleLevel"), RoleLevel);
	jsonObject->SetStringField(TEXT("serverId"), ServerId);
	jsonObject->SetStringField(TEXT("vipLevel"), VipLevel);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	FString extraJson = TEXT("");
	if (ExtraInfo.Num() > 0)
	{
		TSharedPtr<FJsonObject> extraObject = MakeShareable(new FJsonObject);
		bool bHasData = true;
		for (auto& item : ExtraInfo)
		{
			if (!item.Key.IsEmpty())
			{
				extraObject->SetStringField(item.Key, item.Value);
				bHasData = true;
			}
		}
		if (bHasData)
		{
			FJsonObjectWrapper extraWraper;
			extraWraper.JsonObject = extraObject;

			extraWraper.JsonObjectToString(extraJson);
		}

	}
	LOG_PRINTF_OVERSEA("roleInfo = %s, couponCode = %s, extraJson = %s", *ValueJson, *CouponCode, *extraJson);


	PC_GLOBAL_SDK::OneSdk_SetCurrentPlayerRoleInfo(TCHAR_TO_UTF8(*ValueJson));
	auto fn = [](bool success, int code, const wchar_t* msg, int contextId)
		{
			OneEngineSDKPCHelper::Get().OnGenericResultLambda(success, code, WCHAR_TO_TCHAR(msg), contextId);
		};
	int id = PC_GLOBAL_SDK::RedeemCouponCode(TCHAR_TO_UTF8(*CouponCode), TCHAR_TO_UTF8(*extraJson), fn);
	OneEngineSDKPCHelper::Get().SetGenericResultLambda(id, OnFinishedLambda);
}

void OneEngineSDKPCOverSeaInterface::FetchUserRoleInfoList(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneURCRoleInfo>& RoleList)>& OnFinishedLambda, const FString& ServerId)
{
	LOG_FUNCTION_IO_OVERSEA;
	LOG_PRINTF_OVERSEA("ServerId = %s", *ServerId);
	int id = PC_GLOBAL_SDK::OneSdk_GetExecContextId();
	auto fn = [](int code, const wchar_t* msg, const char* result, int contextId)
		{
			OneEngineSDKPCHelper::Get().OnGetRoleListResult(code, WCHAR_TO_TCHAR(msg), UTF8_TO_TCHAR(result), contextId);
		};
	PC_GLOBAL_SDK::OneSdk_QueryRoleList(TCHAR_TO_UTF8(*ServerId), fn, id);
	OneEngineSDKPCHelper::Get().SetGetRoleListResultDelegate(id, OnFinishedLambda);
}

FString OneEngineSDKPCOverSeaInterface::GetChannelId()
{
	LOG_FUNCTION_IO_OVERSEA;
	FString channelId = FString(WCHAR_TO_TCHAR(PC_GLOBAL_SDK::global_GetMediaIdStr()));
	LOG_PRINTF_OVERSEA("channelId = %s", *channelId);
	return channelId;
}

void OneEngineSDKPCOverSeaInterface::FetchAntiAddictionInfo(UOneEngineSDKSubsystem::FOneFetchAntiAddictionInfoDelegate OnFetchAntiAddictionInfo)
{
	LOG_FUNCTION_IO_OVERSEA;
	FOneAntiAddictionInfo info;
	info.Status = 0;
	AsyncTask(ENamedThreads::GameThread, [OnFetchAntiAddictionInfo, info]()
	{
		OnFetchAntiAddictionInfo.ExecuteIfBound(info);
	});
}

void OneEngineSDKPCOverSeaInterface::Login()
{
	RegisterLoginAndLogoutCallback();
	LOG_FUNCTION_IO_OVERSEA;
	PC_GLOBAL_SDK::global_LoginBySdkView();
}

void OneEngineSDKPCOverSeaInterface::Logout()
{
	LOG_FUNCTION_IO_OVERSEA;
	PC_GLOBAL_SDK::global_Logout();
	OneEngineSDKPCHelper::Get().ResetUserInfo();
}

FOneUserInfo OneEngineSDKPCOverSeaInterface::GetUserInfo()
{
	FString strInfo = WCHAR_TO_TCHAR(PC_GLOBAL_SDK::global_GetUserInfo());
	FOneUserInfo userInfo = OneEngineSDKPCHelper::Get().ParseGlobalUserInfo(strInfo);
	return userInfo;
}

void OneEngineSDKPCOverSeaInterface::Pay(const FOnePaymentInfo& PaymentInfo)
{
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	jsonObject->SetStringField(TEXT("orderId"), PaymentInfo.OrderId);
	jsonObject->SetStringField(TEXT("productId"), PaymentInfo.ProductId);
	jsonObject->SetStringField(TEXT("payUrl"), PaymentInfo.PaySuccessUrl);
	jsonObject->SetStringField(TEXT("extraInfo"), PaymentInfo.ExtInfo);
	jsonObject->SetStringField(TEXT("roleId"), PaymentInfo.RoleId);
	jsonObject->SetStringField(TEXT("serverId"), PaymentInfo.GameServerId);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);

	LOG_PRINTF_OVERSEA("pay: %s", *ValueJson);

	OneEngineSDKPCHelper::Get().SetOrderId(PaymentInfo.OrderId);
	auto fn = [](bool success, int code, const char* msg)
	{
		FString message = UTF8_TO_TCHAR(msg);
		if (code == -3 || code == -2)
		{
			code = -10002;
		}
		FOneEngineSDKHelper::OnPaymetResultDelegate(success, code, message, OneEngineSDKPCHelper::Get().GetOrderId());
	};
	HRESULT ret = PC_GLOBAL_SDK::OneSdk_DoPayWithDetailCallback(OneEngineSDKPCHelper::Get().GetHandle(), TCHAR_TO_UTF8(*ValueJson), fn);
	if (ret != S_OK)
	{
		LOG_PRINTF_OVERSEA("OneSdk_DoPayEx error = %lu ", ret);
		FOneEngineSDKHelper::OnPaymetResultDelegate(false, 10001, TEXT("exec failed"), OneEngineSDKPCHelper::Get().GetOrderId());
	}
}

void OneEngineSDKPCOverSeaInterface::TrackEventAD(const FString& Name, const TMap<FString, FString>& Payload)
{
	LOG_FUNCTION_IO_OVERSEA;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	if (Payload.Num() > 0)
	{
		for (auto& ele : Payload)
		{
			jsonObject->SetStringField(ele.Key, ele.Value);
		}
	}
	jsonObject->SetStringField(TEXT("userId"), OneEngineSDKPCHelper::Get().GetUserId());
	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	LOG_PRINTF_OVERSEA("Name: %s, Payload %s", *Name, *ValueJson);

	PC_GLOBAL_SDK::global_appflyerADTrack(TCHAR_TO_WCHAR(*Name), TCHAR_TO_WCHAR(*ValueJson));
}

bool OneEngineSDKPCOverSeaInterface::IsLoggedIn()
{
	bool isLogin = PC_GLOBAL_SDK::global_isLogin();

	LOG_PRINTF_OVERSEA("isLogin: %d", isLogin ? 1 : 0);
	return isLogin;
}

void OneEngineSDKPCOverSeaInterface::OpenUserCenter()
{
	LOG_FUNCTION_IO_OVERSEA;
	PC_GLOBAL_SDK::global_OpenUserCenterBySdkView();
}

FString OneEngineSDKPCOverSeaInterface::GetChannelMediaId()
{
	FString meadiaId = WCHAR_TO_TCHAR(PC_GLOBAL_SDK::global_GetMediaIdStr());
	LOG_PRINTF_OVERSEA("meadiaId %s", *meadiaId);
	return meadiaId;
}

void OneEngineSDKPCOverSeaInterface::DisplayCDKeyDialog(UOneEngineSDKSubsystem::FOneGenericResultDelegate OnCDKeyActivateResult, const FString& ServerId)
{
	LOG_PRINTF_OVERSEA("ServerId: %s", *ServerId);
	if (!PC_GLOBAL_SDK::global_isLogin())
	{
		LOG_PRINTF_OVERSEA(" need login first");
		OnCDKeyActivateResult.ExecuteIfBound(false, 10001, TEXT("need login first"));
		return;
	}
	FString strServerId = OneEngineSDKPCHelper::Get().ConverServerId(ServerId);

	auto fn = [](int code, const wchar_t* msg, int contextId)
	{
		OneEngineSDKPCHelper::Get().OnGenericResult(code == 0, code, WCHAR_TO_TCHAR(msg), contextId);
	};

	int id = PC_GLOBAL_SDK::global_InvokeActivateWnd(TCHAR_TO_WCHAR(*strServerId), fn);
	OneEngineSDKPCHelper::Get().SetGenericResultDelegate(id, OnCDKeyActivateResult);
}

void OneEngineSDKPCOverSeaInterface::QueryActCode(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)>& OnFinishedLambda)
{
	LOG_PRINTF_OVERSEA(" ServerId: %s", *ServerId);

	if (!PC_GLOBAL_SDK::global_isLogin())
	{
		LOG_PRINTF_OVERSEA(" need login first");
		if (OnFinishedLambda)
		{
			OnFinishedLambda(false, false, TEXT("no check"), 10001, TEXT("need login first"));
		}
		return;
	}

	FString strServerId = OneEngineSDKPCHelper::Get().ConverServerId(ServerId);

	auto fnContextId = [](int code, const wchar_t* message, const wchar_t* result, int contextId)
		{
			FString strResult = WCHAR_TO_TCHAR(result);
			FString strMessage = WCHAR_TO_TCHAR(message);
			LOG_PRINTF_OVERSEA("code:%d, message:%s, result:%s, contextId:%d", code, *strMessage, *strResult, contextId);
			OneEngineSDKPCHelper::Get().OnActiveNoUICheckUser(code, strMessage, contextId);
		};
	int contextId = PC_GLOBAL_SDK::global_checkUserIsActivateWithContextId(TCHAR_TO_WCHAR(*strServerId), fnContextId);
	LOG_PRINTF_OVERSEA("contextId:%d", contextId);
	OneEngineSDKPCHelper::Get().SetGlobalQueryActCodeResultDelegate(OnFinishedLambda, contextId);

}

void OneEngineSDKPCOverSeaInterface::ExchangeActCode(const FString& ServerId, const FString& ActCode, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda)
{
	LOG_PRINTF_OVERSEA(" ServerId: %s, ActCode: %s", *ServerId, *ActCode);

	if (!PC_GLOBAL_SDK::global_isLogin())
	{
		LOG_PRINTF_OVERSEA(" need login first");
		if (OnFinishedLambda)
		{
			OnFinishedLambda(false, 10001, TEXT("need login first"));
		}
		return;
	}

	auto fnContextId = [](int code, const wchar_t* message, const wchar_t* result, int contextId)
		{
			FString strResult = WCHAR_TO_TCHAR(result);
			FString strMessage = WCHAR_TO_TCHAR(message);
			LOG_PRINTF_OVERSEA("code:%d, message:%s, result:%s, contextId:%d", code, *strMessage, *strResult, contextId);
			OneEngineSDKPCHelper::Get().OnActiveNoUIUserActivate(code, strMessage, contextId);
		};
	int contextId = PC_GLOBAL_SDK::global_invokeUserActivateWithContextId(TCHAR_TO_WCHAR(*ActCode), fnContextId);
	LOG_PRINTF_OVERSEA("contextId:%d", contextId);
	OneEngineSDKPCHelper::Get().SetGlobalExchangeActCodeResultDelegate(OnFinishedLambda, contextId);
	
}

void OneEngineSDKPCOverSeaInterface::Bind(UOneEngineSDKSubsystem::FOneBindResultDelegate BindDelegate, EOneEngineThirdType BindType)
{
	LOG_FUNCTION_IO_OVERSEA;
	if (!PC_GLOBAL_SDK::global_isLogin())
	{
		bool success = false;
		int code = 10001;
		FString message = TEXT("need login first");
		FOneUserInfo info;
		AsyncTask(ENamedThreads::GameThread, [BindDelegate, success, code, message, BindType, info]()
			{
				BindDelegate.ExecuteIfBound(success, code, message, BindType, info);
			});
		return;
	}

	if (BindType == EOneEngineThirdType::WMPass)
	{
		bool success = false;
		int code = 10001;
		FString message = TEXT("not support bind type");
		FOneUserInfo info = GetUserInfo();
		AsyncTask(ENamedThreads::GameThread, [BindDelegate, success, code, message, BindType, info]()
		{
				BindDelegate.ExecuteIfBound(success, code, message, BindType, info);
		});
		return;
	}

	auto fn = [](const wchar_t* userInfo, int code, const wchar_t* message)
		{
			OneEngineSDKPCHelper::Get().OnGlobalBindResult(WCHAR_TO_TCHAR(userInfo), code, WCHAR_TO_TCHAR(message));
		};

	int loginType = OneEngineSDKPCHelper::Get().CovertEngineThirdTypeToGlobalLoginType(BindType);
	PC_GLOBAL_SDK::global_showBindView(fn, loginType);
	OneEngineSDKPCHelper::Get().SetBindResultDelegateAndBindType(BindDelegate, BindType);
}

void OneEngineSDKPCOverSeaInterface::GetUserLocationInfo(const TFunction<void(const FOneUserLocationInfo& LocationInfo)>& OnFinishedLambda)
{
	LOG_FUNCTION_IO_OVERSEA;
	auto fn = [](int contextId, const wchar_t* result)
	{
		OneEngineSDKPCHelper::Get().OnGetUserLocationResult(WCHAR_TO_TCHAR(result), contextId);
	};
	int contextId = PC_GLOBAL_SDK::global_GetIpLocationJson(fn);
	OneEngineSDKPCHelper::Get().SetGetUserLocationResultDelegate(contextId, OnFinishedLambda);
}

int32 OneEngineSDKPCOverSeaInterface::GetPlatformOS()
{
	int platFormOS = PC_GLOBAL_SDK::global_getOsType();
	LOG_PRINTF_OVERSEA("platformOS : %d", platFormOS);
	return platFormOS;
}

void OneEngineSDKPCOverSeaInterface::GetDeviceInfo(const TFunction<void(const FOneDeviceInfo& DeviceInfo)>& OnFinishedLambda)
{
	LOG_FUNCTION_IO_OVERSEA;
	auto callback = OnFinishedLambda;
	FOneDeviceInfo info;
	info.DeviceId = UTF8_TO_TCHAR(PC_GLOBAL_SDK::OneSdk_GetDeviceUuid());
	info.DeviceSys = UTF8_TO_TCHAR(PC_GLOBAL_SDK::OneSdk_GetDeviceSys());
	FString data = WCHAR_TO_TCHAR(PC_GLOBAL_SDK::global_getDeviceInfo());
	if (!data.IsEmpty())
	{
		TSharedRef<TJsonReader<> > Reader = TJsonReaderFactory<>::Create(data);
		TSharedPtr<FJsonValue> jsonVaule;
		if (FJsonSerializer::Deserialize(Reader, jsonVaule))
		{
			auto dataJsonObject = jsonVaule->AsObject();
			if (dataJsonObject.IsValid())
			{
				for (auto& iterItem : dataJsonObject->Values)
				{
					FString key = iterItem.Key;
					FString value;
					if (iterItem.Value.IsValid() && iterItem.Value->TryGetString(value))
					{
						info.Ext.Add(key, value);
					}
				}
			}
		}
		else
		{
			LOG_PRINTF_OVERSEA("parse json struct failed = %s", *data);
		}
	}
	AsyncTask(ENamedThreads::GameThread, [callback, info]()
		{
			if (callback)
			{
				callback(info);
			}
		});
}

void OneEngineSDKPCOverSeaInterface::GetChannelPlatformLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)>& OnFinishedLambda)
{
	auto fnContextId = [](int code, const wchar_t* message, const wchar_t* result, int contextId)
		{
			FString strResult = WCHAR_TO_TCHAR(result);
			FString strMessage = WCHAR_TO_TCHAR(message);
			LOG_PRINTF_OVERSEA("code:%d, message:%s, result:%s, contextId:%d", code, *strMessage, *strResult, contextId);
			OneEngineSDKPCHelper::Get().OnGlobalPlatform(code, strMessage, strResult, contextId);
		};
	int contextId = PC_GLOBAL_SDK::global_getPlatformWithContextId(fnContextId);
	LOG_PRINTF_OVERSEA("contextId:%d", contextId);
	OneEngineSDKPCHelper::Get().SetGlobalPlatformDelegate(OnFinishedLambda, contextId);
}

void OneEngineSDKPCOverSeaInterface::QueryUserActiveQualificationLambda(const FString& ServerId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)>& OnFinishedLambda)
{
	LOG_PRINTF_OVERSEA("ServerId = %s", *ServerId);
	auto callback = OnFinishedLambda;
	FOneActiveQualificationInfo info;
	info.DeviceLogged = 0;
	info.DeviceTotal = 0;
	info.Status = 0;
	info.WhiteList = 0;
	if (!PC_GLOBAL_SDK::global_isLogin())
	{
		bool success = false;
		int code = 10001;
		FString message = TEXT("need login first");
		AsyncTask(ENamedThreads::GameThread, [callback, success, code, message, info]()
			{
				if (callback)
				{
					callback(success, code, message, info);
				}

			});
		return;
	}
	FString strServerId = OneEngineSDKPCHelper::Get().ConverServerId(ServerId);
	AsyncTask(ENamedThreads::GameThread, [callback, info]()
		{
			if (callback)
			{
				callback(false, 10001, TEXT("no support"), info);
			}
		});
}

void OneEngineSDKPCOverSeaInterface::UnlockSafeLockUsingDynamicCode(const FString& DynamicCode, const FString& RoleName, const FString& ServerName, UOneEngineSDKSubsystem::FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
	LOG_PRINTF_OVERSEA("DynamicCode : %s", *DynamicCode);

	FString msg = "";
	int checkCode = 0;
	if (DynamicCode.IsEmpty())
	{
		checkCode = 1;
		msg = TEXT("DynamicCode is empty !");
	}
	if (checkCode != 0)
	{
		AsyncTask(ENamedThreads::GameThread, [OnUnlockSafeLockResultDelegate, checkCode, msg]()
			{
				OnUnlockSafeLockResultDelegate.ExecuteIfBound(EOneUnlockSafeLockResult::UnlockFailed, "", checkCode, msg, EOneUnlockSafeLockType::DynamicCode);
			});
		return;
	}

	auto fn = [](int code, const wchar_t* message, const wchar_t* data, int contextId)
		{
			EOneUnlockSafeLockResult unlockResult;
			if (code == 0)
			{
				unlockResult = EOneUnlockSafeLockResult::UnlockSucceeded;
			}
			else
			{
				unlockResult = EOneUnlockSafeLockResult::UnlockFailed;
			}
			FString result = WCHAR_TO_TCHAR(data);
			FString msg = WCHAR_TO_TCHAR(message);
			FString token;
			if (code == 0 && !result.IsEmpty())
			{
				FJsonObjectWrapper Wrapper;
				Wrapper.JsonObjectFromString(result);
				if (Wrapper.JsonObject.IsValid())
				{
					Wrapper.JsonObject->TryGetStringField(TEXT("unlockToken"), token);
				}
				else
				{
					code = -10001;
					msg = TEXT("parse result error");
				}
			}
			OneEngineSDKPCHelper::Get().OnSafeLockResult(unlockResult, code, msg, token, EOneUnlockSafeLockType::DynamicCode, contextId);
		};
	int contextId = PC_GLOBAL_SDK::global_SecurityLockDynamicCodeUnlock(TCHAR_TO_WCHAR(*DynamicCode), fn);
	OneEngineSDKPCHelper::Get().SetSafeLockResultDelegate(contextId, OnUnlockSafeLockResultDelegate);
}

void OneEngineSDKPCOverSeaInterface::GetIpInfo(const FString& Ip, UOneEngineSDKSubsystem::FOnGetIpInfoResultDelegate Delegate)
{
	LOG_PRINTF_OVERSEA("IP = %s", *Ip);
	if (Ip.IsEmpty())
	{
		auto callBack = Delegate;
		AsyncTask(ENamedThreads::GameThread, [callBack]()
			{
				FUserIpInfo info;
				callBack.ExecuteIfBound(false, info, 50003, TEXT("ip is empty!"));
			});
		return;
	}
	auto fnContextId = [](int code, const wchar_t* message, const wchar_t* result, int contextId)
		{
			FString strResult = WCHAR_TO_TCHAR(result);
			FString strMessage = WCHAR_TO_TCHAR(message);
			OneEngineSDKPCHelper::Get().OnGetIpInfo(code, strMessage, strResult, contextId);
		};
	int contextId = PC_GLOBAL_SDK::global_GetIpLocation(TCHAR_TO_WCHAR(*Ip), fnContextId);
	OneEngineSDKPCHelper::Get().SetGetIpInfoResultDelegate(Delegate, contextId);
	LOG_PRINTF_OVERSEA("contextId:%d", contextId);
}

FString OneEngineSDKPCOverSeaInterface::GetCurrentGameAppId()
{
	FString appId = WCHAR_TO_TCHAR(PC_GLOBAL_SDK::global_GetCurrentGameAppId());
	LOG_PRINTF_OVERSEA("CurrentGameAppId : %s", *appId);
	return appId;
}

void OneEngineSDKPCOverSeaInterface::OpenAIHelp(EOneAIHelpType Type, const FString& RoleId, const FString& ServerId, const FString& RoleName)
{
	LOG_PRINTF_OVERSEA("type: %d, RoleId: %s, ServerId: %s, RoleName: %s", (int)Type, *RoleId, *ServerId, *RoleName);
	PC_GLOBAL_SDK::global_OpenAIHelp((int)Type, TCHAR_TO_UTF8(*RoleId), TCHAR_TO_UTF8(*ServerId), TCHAR_TO_UTF8(*RoleName));
}

void OneEngineSDKPCOverSeaInterface::Translate(const FString& Text, const TFunction<void(bool bSucceed, const FString& Result, const FString& ErrorMsg)>& OnFinishedLambda)
{
	LOG_PRINTF_OVERSEA("Text: %s", *Text);
	auto fn = [](int contextId, const wchar_t* result, const wchar_t* msg, int code)
	{
		OneEngineSDKPCHelper::Get().OnTranslateResult(code, WCHAR_TO_TCHAR(msg), WCHAR_TO_TCHAR(result), contextId);
	};

	int id = PC_GLOBAL_SDK::global_TranslateBySdk(TCHAR_TO_WCHAR(*Text), fn);
	OneEngineSDKPCHelper::Get().SetTranslateResultDelegate(id, OnFinishedLambda);
}

FString OneEngineSDKPCOverSeaInterface::GetCurrentLanguage()
{
	FString ret = WCHAR_TO_TCHAR(PC_GLOBAL_SDK::global_getCurrentLang());
	LOG_PRINTF_OVERSEA("lauguage: %s", *ret);
	return ret;
}

void OneEngineSDKPCOverSeaInterface::SetLanguage(const FString& Code)
{
	LOG_PRINTF_OVERSEA("languageCode = %s ", *Code);
	PC_GLOBAL_SDK::global_SetLanguage(TCHAR_TO_WCHAR(*Code));
}

void OneEngineSDKPCOverSeaInterface::SetUpConfigAppID(const FString& AppId)
{
	LOG_PRINTF_OVERSEA("AppId = %s ", *AppId);
	PC_GLOBAL_SDK::global_setConfigAppId(TCHAR_TO_WCHAR(*AppId));
}

void OneEngineSDKPCOverSeaInterface::GetUserTokenListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)>& OnFinishedLambda)
{
	auto fnContextId = [](int code, const wchar_t* message, const wchar_t* result, int contextId)
		{
			FString strResult = WCHAR_TO_TCHAR(result);
			FString strMessage = WCHAR_TO_TCHAR(message);
			LOG_PRINTF_OVERSEA("code:%d, message:%s, result:%s, contextId:%d", code, *strMessage, *strResult, contextId);
			OneEngineSDKPCHelper::Get().OnGlobalGetTokenList(code, strMessage, strResult, contextId);
		};
	int contextId = PC_GLOBAL_SDK::global_getLocalTokenCacheWithContextId(fnContextId);
	LOG_PRINTF_OVERSEA("contextId:%d", contextId);
	OneEngineSDKPCHelper::Get().SetGlobalGetTokenListDelegate(OnFinishedLambda, contextId);
}

void OneEngineSDKPCOverSeaInterface::TokenLogin(const FString& Token, const FString& Uid, EOneEngineThirdType ThirdType)
{
	LOG_PRINTF_OVERSEA("Uid = %s type:%d ", *Uid, ThirdType);
	RegisterLoginAndLogoutCallback();
	PC_GLOBAL_SDK::global_tokenLoginByGame(TCHAR_TO_WCHAR(*Uid), TCHAR_TO_WCHAR(*Token));
}

void OneEngineSDKPCOverSeaInterface::ThirdLogin(EOneEngineThirdType ThirdType, bool bForcedLogin)
{
	LOG_PRINTF_OVERSEA("bForcedLogin = %d type:%d ", bForcedLogin ? 1 : 0, ThirdType);
	if ((ThirdType != EOneEngineThirdType::Facebook)
		&&(ThirdType != EOneEngineThirdType::HW)
		&&(ThirdType != EOneEngineThirdType::Naver)
		&&(ThirdType != EOneEngineThirdType::Apple)
		&&(ThirdType != EOneEngineThirdType::Crunchyrool)
		&& (ThirdType != EOneEngineThirdType::Google))
	{
		bool success = false;
		int code = 10001;
		FString message = TEXT("not support third type");
		FOneEngineSDKHelper::OnLoginResultDelegate(success, code, message, "");
		return;
	}
	RegisterLoginAndLogoutCallback();
	int loginType = OneEngineSDKPCHelper::Get().CovertEngineThirdTypeToGlobalLoginType(ThirdType);
	PC_GLOBAL_SDK::global_thirdLoginByGame(loginType);
}

void OneEngineSDKPCOverSeaInterface::GuestLogin()
{
	RegisterLoginAndLogoutCallback();
	LOG_FUNCTION_IO_OVERSEA;
	PC_GLOBAL_SDK::global_guestLogin();
}

void OneEngineSDKPCOverSeaInterface::GetProductList(const TArray<FString>& ProductIds, UOneEngineSDKSubsystem::FOneProductInfoDelegate ProductResultDelegate)
{
	if (ProductIds.Num() <= 0)
	{
		AsyncTask(ENamedThreads::GameThread, [ProductResultDelegate]()
			{
				TArray<FOneProductInfo> infoArray;
				ProductResultDelegate.ExecuteIfBound(false, 20001, infoArray);
			});
		return;
	}
	FString JsonOutString;
	TSharedRef<TJsonWriter<TCHAR> > Writer = TJsonWriterFactory<TCHAR>::Create(&JsonOutString);
	Writer->WriteArrayStart();
	for (FString Url : ProductIds)
	{
		Writer->WriteValue(Url);
	}
	Writer->WriteArrayEnd();
	Writer->Close();
	LOG_PRINTF_OVERSEA("ProductIdList: %s", *JsonOutString);

	auto fn = [](int code, const wchar_t* message, const wchar_t* result, int contextId)
		{
			OneEngineSDKPCHelper::Get().OnGetProductList(contextId, WCHAR_TO_TCHAR(message), WCHAR_TO_TCHAR(result), code);
		};

	int contextId = PC_GLOBAL_SDK::global_getProductList(TCHAR_TO_WCHAR(*JsonOutString), fn);
	OneEngineSDKPCHelper::Get().SetGetProductListResultDelegate(contextId, ProductResultDelegate);
}

void OneEngineSDKPCOverSeaInterface::SetAnalyticsCollectionEnabled(bool bEnable)
{
	LOG_PRINTF_OVERSEA("bEnable : %d", bEnable);
	PC_GLOBAL_SDK::global_appflyerSetCollectionEnable(bEnable);
}

void OneEngineSDKPCOverSeaInterface::RegisterLoginAndLogoutCallback()
{
	auto fnLogin = [](const wchar_t* pUID, const wchar_t* pToken, int loginType, int nErrCode, const wchar_t* pErrMessage)
		{
			if (nErrCode == 0)
			{
				LOG_PRINTF_OVERSEA("Login success uid = %s, loginType = %d", WCHAR_TO_TCHAR(pUID), loginType);
				OneEngineSDKPCHelper::Get().SetUserId(WCHAR_TO_TCHAR(pUID));
				OneEngineSDKPCHelper::Get().SetToken(WCHAR_TO_TCHAR(pToken));
				FOneEngineSDKHelper::OnLoginResultDelegate(true, nErrCode, WCHAR_TO_TCHAR(pErrMessage), WCHAR_TO_TCHAR(pUID));
			}
			else
			{
				LOG_PRINTF_OVERSEA("Login failed errorCode = %d, errorMsg = %s", nErrCode, WCHAR_TO_TCHAR(pErrMessage));
				if (nErrCode == -1)
				{
					nErrCode = -10002;
				}
				FOneEngineSDKHelper::OnLoginResultDelegate(false, nErrCode, WCHAR_TO_TCHAR(pErrMessage), "");
			}
		};
	PC_GLOBAL_SDK::global_RegisterLoginCallback(fnLogin);

	auto fnLogout = []()
		{
			LOG_PRINTF_OVERSEA("logout end");
			FOneEngineSDKHelper::OnLogoutResultDelegate(true, 0, TEXT("success"));
		};
	PC_GLOBAL_SDK::global_RegisterLogoutCallback(fnLogout);
}
#endif

