#ifdef KONEENGINE_REGION_OVERSEA

#pragma once
#include "OneEngineSDKPCAdapter.h"


DECLARE_LOG_CATEGORY_EXTERN(PC_OneEngineSDK_OverSea, Log, All);

class OneEngineSDKPCOverSeaInterface : public OneEngineSDKPCInterface
{
private:
	static bool OnQuitGame(bool bForcible);

	static void OnRelogin();

	static void OnBeKickedOut();

private:
	void OnEndPIE(bool val);

private:
	bool bBindEndPIE = false;
public:
	void Init(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate) override;

	void TrackEventRoleLoginSucceed(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port) override;

	void TrackEventRoleLoginError(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port, const FString& Code, const FString& Msg) override;

	void TrackEventRoleLogout(const FOneRoleInfo& RoleInfo) override;

	void TrackEventRoleCreate(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port) override;

	void TrackEventRoleLevelUp(const FOneRoleInfo& RoleInfo) override;

	void GameResReqEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg) override;

	void GameUpdateAssetEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg) override;

	void GameResDecEvent(EOneResEventState State, const FString& ErrorMsg) override;

	void GameGetServerListEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg) override;

	void TrackEvent(const FString& Name, const TMap<FString, FString>& Payload) override;

	void TrackEventEnterGameScene(const FString& SceneName, int32 Period, const TMap<FString, FString>& HintMap) override;

	void TrackEventExitGameScene() override;

	void TrackEventAddExtraDeviceInfo(const TMap<FString, FString>& ExtraDeviceInfo) override;

	void RedeemCouponCode(const FString& CouponCode, const FString& ServerId, const FString& RoleId, const FString& RoleLevel, const FString& VipLevel, const TMap<FString, FString>& ExtraInfo, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda) override;

	void FetchUserRoleInfoList(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)>& OnFinishedLambda, const FString& ServerId) override;

	FString GetChannelId() override;

	void FetchAntiAddictionInfo(UOneEngineSDKSubsystem::FOneFetchAntiAddictionInfoDelegate OnFetchAntiAddictionInfo) override;
public:
	virtual void Login() override;

	virtual void Logout() override;

	virtual FOneUserInfo GetUserInfo() override;

	virtual void Pay(const FOnePaymentInfo& PaymentInfo) override;

	virtual void TrackEventAD(const FString& Name, const TMap<FString, FString>& Payload) override;

	virtual bool IsLoggedIn() override;

	virtual void OpenUserCenter() override;

	virtual FString GetChannelMediaId() override;

	virtual void DisplayCDKeyDialog(UOneEngineSDKSubsystem::FOneGenericResultDelegate OnCDKeyActivateResult, const FString& ServerId) override;

	virtual void QueryActCode(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)>& OnFinishedLambda) override;

	virtual void ExchangeActCode(const FString& ServerId, const FString& ActCode, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda) override;

	virtual void Bind(UOneEngineSDKSubsystem::FOneBindResultDelegate BindDelegate, EOneEngineThirdType BindType) override;

	virtual void GetUserLocationInfo(const TFunction<void(const FOneUserLocationInfo& LocationInfo)>& OnFinishedLambda) override;

	virtual int32 GetPlatformOS() override;

	virtual void GetDeviceInfo(const TFunction<void(const FOneDeviceInfo& DeviceInfo)>& OnFinishedLambda) override;

	virtual void GetChannelPlatformLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)>& OnFinishedLambda) override;

	virtual void QueryUserActiveQualificationLambda(const FString& ServerId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)>& OnFinishedLambda) override;

	virtual void UnlockSafeLockUsingDynamicCode(const FString& DynamicCode, const FString& RoleName, const FString& ServerName, UOneEngineSDKSubsystem::FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate);

	virtual void GetIpInfo(const FString& Ip, UOneEngineSDKSubsystem::FOnGetIpInfoResultDelegate Delegate) override;

	virtual FString GetCurrentGameAppId() override;
public:
	virtual void OpenAIHelp(EOneAIHelpType Type, const FString& RoleId, const FString& ServerId, const FString& RoleName) override;

	virtual void Translate(const FString& Text, const TFunction<void(bool bSucceed, const FString& Result, const FString& ErrorMsg)>& OnFinishedLambda) override;

	virtual FString GetCurrentLanguage() override;

	virtual void SetLanguage(const FString& Code) override;

	virtual void SetUpConfigAppID(const FString& AppId) override;

	virtual void GetUserTokenListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)>& OnFinishedLambda) override;

	virtual void TokenLogin(const FString& Token, const FString& Uid, EOneEngineThirdType ThirdType) override;

	virtual void ThirdLogin(EOneEngineThirdType ThirdType, bool bForcedLogin) override;

	virtual void GuestLogin() override;

	virtual void GetProductList(const TArray<FString>& ProductIds, UOneEngineSDKSubsystem::FOneProductInfoDelegate ProductResultDelegate) override;

	virtual void SetAnalyticsCollectionEnabled(bool bEnable) override;

private:
	void RegisterLoginAndLogoutCallback();

};
#endif

