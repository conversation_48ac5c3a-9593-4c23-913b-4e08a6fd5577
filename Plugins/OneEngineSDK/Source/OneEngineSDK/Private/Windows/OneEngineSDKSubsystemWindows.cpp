#include "OneEngineSDKSubsystem.h"
#include "OneEngineSDKPCAdapter.h"

void UOneEngineSDKSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
}
void UOneEngineSDKSubsystem::Init(FOneInitDelegate InitDelegate)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->Init(InitDelegate);
}

EOneEngineSDKRegionType UOneEngineSDKSubsystem::GetRegionType()
{
	return OneEngineSDKPCAdapter::Get().GetEngineType();
}

void UOneEngineSDKSubsystem::Login()
{
	OneEngineSDKPCAdapter::Get().GetInterface()->Login();
}

void UOneEngineSDKSubsystem::GetUserTokenList(FOneFetchUserTokenListDelegate OnGetTokenListDelegate)
{
	GetUserTokenListLambda([OnGetTokenListDelegate](bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)
		{
			OnGetTokenListDelegate.ExecuteIfBound(bSucceed, Code, Msg, TokenList);
		});
}

void UOneEngineSDKSubsystem::GetUserTokenListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)> &OnFinishedLambda)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->GetUserTokenListLambda(OnFinishedLambda);
}

void UOneEngineSDKSubsystem::TokenLogin(const FString& Token, const FString& Uid, EOneEngineThirdType ThirdType)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->TokenLogin(Token, Uid, ThirdType);
}

void UOneEngineSDKSubsystem::ThirdLogin(EOneEngineThirdType ThirdType, bool bForcedLogin)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->ThirdLogin(ThirdType, bForcedLogin);
}

void UOneEngineSDKSubsystem::GuestLogin()
{
	OneEngineSDKPCAdapter::Get().GetInterface()->GuestLogin();
}

void UOneEngineSDKSubsystem::SwitchAccount()
{
	OneEngineSDKPCAdapter::Get().GetInterface()->Logout();
}

FOneUserInfo UOneEngineSDKSubsystem::GetUserInfo()
{
	FOneUserInfo UserInfo = OneEngineSDKPCAdapter::Get().GetInterface()->GetUserInfo();
	return UserInfo;
}

void UOneEngineSDKSubsystem::Pay(const FOnePaymentInfo& PaymentInfo)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->Pay(PaymentInfo);
}

// 角色事件打点
void UOneEngineSDKSubsystem::TrackEventRoleLoginSucceed(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->TrackEventRoleLoginSucceed(RoleInfo, Ip, Port);
}

void UOneEngineSDKSubsystem::TrackEventRoleLoginError(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port, const FString& Code, const FString& Msg)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->TrackEventRoleLoginError(RoleInfo, Ip, Port, Code, Msg);
}

void UOneEngineSDKSubsystem::TrackEventRoleLogout(const FOneRoleInfo& RoleInfo)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->TrackEventRoleLogout(RoleInfo);
}

void UOneEngineSDKSubsystem::TrackEventRoleCreate(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->TrackEventRoleCreate(RoleInfo, Ip, Port);
}

void UOneEngineSDKSubsystem::TrackEventRoleLevelUp(const FOneRoleInfo& RoleInfo)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->TrackEventRoleLevelUp(RoleInfo);
}

// 资源事件打点
void UOneEngineSDKSubsystem::GameResReqEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->GameResReqEvent(State, Url, ErrorCode, ErrorMsg);
}

void UOneEngineSDKSubsystem::GameUpdateAssetEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->GameUpdateAssetEvent(State, Url, ErrorCode, ErrorMsg);
}

void UOneEngineSDKSubsystem::GameResDecEvent(EOneResEventState State, const FString& ErrorMsg)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->GameResDecEvent(State, ErrorMsg);
}

void UOneEngineSDKSubsystem::GameGetServerListEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->GameGetServerListEvent(State, Url, ErrorCode, ErrorMsg);
}

// 自定义事件打点
void UOneEngineSDKSubsystem::TrackEvent(const FString& Name, const TMap<FString, FString>& Payload)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->TrackEvent(Name, Payload);
}

void UOneEngineSDKSubsystem::TrackEventAD(const FString& Name, const TMap<FString, FString>& Payload)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->TrackEventAD(Name, Payload);
}

//进入游戏场景打点
void UOneEngineSDKSubsystem::TrackEventEnterGameScene(const FString& SceneName,int32 Period,const TMap<FString,FString>& HintMap)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->TrackEventEnterGameScene(SceneName, Period, HintMap);
}

//退出游戏打点
void UOneEngineSDKSubsystem::TrackEventExitGameScene()
{
	OneEngineSDKPCAdapter::Get().GetInterface()->TrackEventExitGameScene();
}

//设备打点额外信息
void UOneEngineSDKSubsystem::TrackEventAddExtraDeviceInfo(const TMap<FString,FString>& ExtraDeviceInfo)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->TrackEventAddExtraDeviceInfo(ExtraDeviceInfo);
}

bool UOneEngineSDKSubsystem::IsLoggedIn()
{
	return OneEngineSDKPCAdapter::Get().GetInterface()->IsLoggedIn();
}

void UOneEngineSDKSubsystem::SetUpConfigAppID(const FString& AppId)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->SetUpConfigAppID(AppId);
}

FString UOneEngineSDKSubsystem::GetAppId()
{
	return OneEngineSDKPCAdapter::Get().GetInterface()->GetCurrentGameAppId();
}

void UOneEngineSDKSubsystem::EnableDebugMode(bool Enable)
{
	OneEngineSDKPCAdapter::Get().EnableDebugMode(Enable);
}

bool UOneEngineSDKSubsystem::IsDebugMode()
{
	return OneEngineSDKPCAdapter::Get().IsDebugMode();
}

void UOneEngineSDKSubsystem::OpenUserCenter()
{
	OneEngineSDKPCAdapter::Get().GetInterface()->OpenUserCenter();
}

void UOneEngineSDKSubsystem::GetQRCodeScanResult(FOneGetQRCodeScanResultDelegate OnGetQRCodeScanResultDelegate)
{
	
}

void UOneEngineSDKSubsystem::OpenComplianceOnWebView()
{
	OneEngineSDKPCAdapter::Get().GetInterface()->OpenComplianceOnWebView();
}

void UOneEngineSDKSubsystem::EnterAccountCancellation()
{
	OneEngineSDKPCAdapter::Get().GetInterface()->EnterAccountCancellation();
}

FString UOneEngineSDKSubsystem::GetChannelId()
{
	return OneEngineSDKPCAdapter::Get().GetInterface()->GetChannelId();
}

FString UOneEngineSDKSubsystem::GetChannelMediaId()
{
	return OneEngineSDKPCAdapter::Get().GetInterface()->GetChannelMediaId();
}

int32 UOneEngineSDKSubsystem::GetPlatformOS()
{
	return OneEngineSDKPCAdapter::Get().GetInterface()->GetPlatformOS();
}

void UOneEngineSDKSubsystem::GetChannelPlatform(FOneGetPlatformDelegate OnGetPlatformResultDelegate)
{
	GetChannelPlatformLambda([OnGetPlatformResultDelegate](bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)
		{
			OnGetPlatformResultDelegate.ExecuteIfBound(bSucceed, Code, Msg, Platform);
		});
}

void UOneEngineSDKSubsystem::GetChannelPlatformLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)> &OnFinishedLambda)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->GetChannelPlatformLambda(OnFinishedLambda);
}

//万能方法接口
bool UOneEngineSDKSubsystem::IsCommonFunctionSupported(const FString& FuncName)
{
	return false;
}

void UOneEngineSDKSubsystem::CallCommonFunction(const FString& FuncName, const FString& Params, FOneCommonFunctionDelegate CommonFunctionDelegate)
{
    
}


void UOneEngineSDKSubsystem::GetProductList(const TArray<FString>& ProductIds, FOneProductInfoDelegate ProductResultDelegate)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->GetProductList(ProductIds, ProductResultDelegate);
}

//获取审核开关(iOS专有接口)
bool UOneEngineSDKSubsystem::ExaminStatus()
{
	return false;
}
//是否检测BundleId(iOS专有接口)
void UOneEngineSDKSubsystem::ShouldVerifyBundleId(bool bShouldVerify)
{
	
}

void UOneEngineSDKSubsystem::GetUserLocationInfo(FOneUserLocationInfoDelegate LocationInfoDelegate)
{
	GetUserLocationInfoLambda([LocationInfoDelegate](const FOneUserLocationInfo& LocationInfo)
		{
			LocationInfoDelegate.ExecuteIfBound(LocationInfo);
		});
}

void UOneEngineSDKSubsystem::GetUserLocationInfoLambda(const TFunction<void(const FOneUserLocationInfo& LocationInfo)> &OnFinishedLambda)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->GetUserLocationInfo(OnFinishedLambda);
}

void UOneEngineSDKSubsystem::GetDeviceInfo(FOneGetDeviceInfoDelegate Delegate)
{
	GetDeviceInfoLambda([Delegate](const FOneDeviceInfo& DeviceInfo)
		{
			Delegate.ExecuteIfBound(DeviceInfo);
		});
}

void UOneEngineSDKSubsystem::GetDeviceInfoLambda(const TFunction<void(const FOneDeviceInfo& DeviceInfo)>& OnFinishedLambda)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->GetDeviceInfo(OnFinishedLambda);
}

void UOneEngineSDKSubsystem::FetchAntiAddictionInfo(FOneFetchAntiAddictionInfoDelegate OnFetchAntiAddictionInfo)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->FetchAntiAddictionInfo(OnFetchAntiAddictionInfo);
}

void UOneEngineSDKSubsystem::StartAntiAddictionNotify(const FString &ServerId, const FString &RoleId)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->StartAntiAddictionNotify(ServerId, RoleId);
}

void UOneEngineSDKSubsystem::StopAntiAddictionNotify()
{
	OneEngineSDKPCAdapter::Get().GetInterface()->StopAntiAddictionNotify();
}

void UOneEngineSDKSubsystem::SetShowDefaultActivationResultToast(bool bShow)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->SetShowDefaultActivationResultToast(bShow);
}

void UOneEngineSDKSubsystem::DisplayCDKeyDialog(FOneGenericResultDelegate OnCDKeyActivateResult,  const FString &ServerId)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->DisplayCDKeyDialog(OnCDKeyActivateResult, ServerId);
}

void UOneEngineSDKSubsystem::QueryActCode(const FString& ServerId, FOneQueryActCodeResultDelegate OnQueryActCodeResultDelegate)
{
	QueryActCodeLambda(ServerId, [OnQueryActCodeResultDelegate](bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)
		{
			OnQueryActCodeResultDelegate.ExecuteIfBound(bSucceed, bNeedActCode, ActCodePrompt, Code, ErrorMsg);
		});
}

void UOneEngineSDKSubsystem::QueryActCodeLambda(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)>& OnFinishedLambda)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->QueryActCode(ServerId, OnFinishedLambda);
}

void UOneEngineSDKSubsystem::ExchangeActCode(const FString& ServerId, const FString& ActCode, FOneGenericResultDelegate GenericResultDelegate)
{
	ExchangeActCodeLambda(ServerId, ActCode, [GenericResultDelegate](bool bSuccess, int32 Code, const FString& Msg)
		{
			GenericResultDelegate.ExecuteIfBound(bSuccess, Code, Msg);
		});
}

void UOneEngineSDKSubsystem::ExchangeActCodeLambda(const FString& ServerId, const FString& ActCode, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> &OnFinishedLambda)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->ExchangeActCode(ServerId, ActCode, OnFinishedLambda);
}

void UOneEngineSDKSubsystem::RedeemCouponCode(const FString &CouponCode,  const FString &ServerId, const FString &RoleId, const FString &RoleLevel, const FString &VipLevel, const TMap<FString, FString> &ExtraInfo, FOneGenericResultDelegate OnRedeemCouponResult)
{
	RedeemCouponCodeLambda(CouponCode, ServerId, RoleId, RoleLevel, VipLevel, ExtraInfo, [OnRedeemCouponResult](bool bSuccess, int32 Code, const FString& Msg)
		{
			OnRedeemCouponResult.ExecuteIfBound(bSuccess, Code, Msg);
		});
}

void UOneEngineSDKSubsystem::RedeemCouponCodeLambda(const FString& CouponCode, const FString& ServerId, const FString& RoleId, const FString& RoleLevel, const FString& VipLevel, const TMap<FString, FString>& ExtraInfo,  const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)> &OnFinishedLambda)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->RedeemCouponCode(CouponCode, ServerId, RoleId, RoleLevel, VipLevel, ExtraInfo, OnFinishedLambda);
}

void UOneEngineSDKSubsystem::FetchUserRoleInfoList(FOneFetchUserRoleInfoListDelegate OnFetchUserRoleListDelegate, const FString& ServerId)
{
	FetchUserRoleInfoListLambda([OnFetchUserRoleListDelegate](bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)
		{
			OnFetchUserRoleListDelegate.ExecuteIfBound(bSucceed, Code, Msg, RoleList);
		}, ServerId);
}

void UOneEngineSDKSubsystem::FetchUserRoleInfoListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)> &OnFinishedLambda, const FString& ServerId)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->FetchUserRoleInfoList(OnFinishedLambda, ServerId);
}

void UOneEngineSDKSubsystem::Bind(FOneBindResultDelegate StartBindPhoneDelegate,EOneEngineThirdType BindType)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->Bind(StartBindPhoneDelegate, BindType);
}

void UOneEngineSDKSubsystem::UserAuthentication(FOneUserAuthenticationResultDelegate OnUserAuthenticationResultDelegate)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->UserAuthentication(OnUserAuthenticationResultDelegate);
}
// 获取声明的权限 可用于展示
TArray<FOnePermissionInfo> UOneEngineSDKSubsystem::GetPermissions()
{
	TArray<FOnePermissionInfo> PermissionInfos;
	return  PermissionInfos;
}

// 检查是否权限已授权
bool UOneEngineSDKSubsystem::CheckSelfPermission(EOnePermissionType Type)
{
	return false;
}

// 申请权限
void UOneEngineSDKSubsystem::RequestPermission(EOnePermissionType Type,FOneRequestPermissionResultDelegate Delegate)
{
	
}
void UOneEngineSDKSubsystem::RequestPermission(EOnePermissionType Type,FOneRequestPermissionResultDelegate Delegate,const TArray<FString>& Tips)
{
	
}
//  关闭剪切板权限
void UOneEngineSDKSubsystem::CloseClipboardPermission()
{
	
}

// 打开应用权限设置页
void UOneEngineSDKSubsystem::OpenApplicationSetting()
{
	
}

// 打开AIHelp客服
void UOneEngineSDKSubsystem::OpenAIHelp(EOneAIHelpType Type, const FString& RoleId, const FString& ServerId, const FString& RoleName)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->OpenAIHelp(Type, RoleId, ServerId, RoleName);
}

// 打开自定义客服
void UOneEngineSDKSubsystem::OpenCustomerService(const FString& RoleID, const FString& RoleName, const FString& ServerID)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->OpenAIHelp(EOneAIHelpType::RobotChat, RoleID, ServerID, RoleName);
}

// 文本翻译
void UOneEngineSDKSubsystem::TranslateLambda(const FString& Text,const TFunction<void(bool bSucceed, const FString& Result,const FString& ErrorMsg)> &OnFinishedLambda)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->Translate(Text, OnFinishedLambda);
}

void UOneEngineSDKSubsystem::Translate(const FString& Text, FOneTranslateResultDelegate Callback)
{
	TranslateLambda(Text, [Callback](bool bSucceed, const FString& Result, const FString& ErrorMsg)
		{
			Callback.ExecuteIfBound(bSucceed, Result, ErrorMsg);
		});
}

// 获取当前设置语言
FString UOneEngineSDKSubsystem::GetCurrentLanguage()
{
	return OneEngineSDKPCAdapter::Get().GetInterface()->GetCurrentLanguage();
}

// 设置语言
void UOneEngineSDKSubsystem::SetLanguage(const FString& Code)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->SetLanguage(Code);
}

// 获取本地语言列表
TArray<FString> UOneEngineSDKSubsystem::GetSupportedLanguageCodeList()
{
	TArray<FString> LanguageCodeList;
	return LanguageCodeList;
}

// 设置屏幕方向
void UOneEngineSDKSubsystem::SetScreenOrientation(EOneScreenOrientation Orientation)
{
	
}

// 分享SDK
void UOneEngineSDKSubsystem::ShareDataToApp(EOneShareAppTarget AppTarget, const EOneShareType Type, const FOneShareData& Data, FOneGenericResultDelegate OnShareResult)
{
	
}

// 推送相关接口
// 注册推送
void UOneEngineSDKSubsystem::StartUpdatePushData(FOneStartUpdatePushDataDelegate Callback)
{
	
}

// 设置点击推送消息后的回调
void UOneEngineSDKSubsystem::SetupNotificationCallback(FOneNotificationDelegate Callback)
{
	
}

// 获取系统推送开关状态 和 应用推送开关状态
void UOneEngineSDKSubsystem::GetPushStatus(FOnePushStatusDelegate Callback)
{
	
}

// 设置应用推送开关状态
void UOneEngineSDKSubsystem::SetProviderPushState(bool bProviderPushState, FOneGenericResultDelegate Callback)
{
	
}

// 推送设置用户信息
void UOneEngineSDKSubsystem::SetPushUserInfo(const FString& ServerId, const FString& RoleId, FOneGenericResultDelegate Callback)
{
	
}

// 推送解绑用户信息
void UOneEngineSDKSubsystem::UnSetPushUserInfo(FOneGenericResultDelegate Callback)
{
	
}

// 获取推送类型列表
void UOneEngineSDKSubsystem::GetPushTypeInfoList(FOneGetPushTypeInfoListDelegate Callback)
{
	
}

// 批量设置推送类型开关状态
void UOneEngineSDKSubsystem::UpdatePushTypeList(const TArray<FOnePushTypeInfo>& PushTypeList, FOneGenericResultDelegate Callback)
{
	
}

// 夜间勿扰配置的回调
void UOneEngineSDKSubsystem::GetPushNotDisturb(FOnePushNotDisturbInfoDelegate Callback)
{
	
}

// 设置勿扰模式
void UOneEngineSDKSubsystem::UpdatePushNotDisturb(const FOnePushNotDisturbInfo& NotDisturbInfo, FOneGenericResultDelegate Callback)
{
	
}
void UOneEngineSDKSubsystem::KillProcess()
{
	
}


// 查询用户有没有激活资格
void UOneEngineSDKSubsystem::QueryUserActiveQualification(const FString& ServerId, FOneQueryUserActiveQualificationResultDelegate Callback)
{
	QueryUserActiveQualificationLambda(ServerId, [Callback](bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)
		{
			Callback.ExecuteIfBound(bSucceed, Code, ErrorMsg , QualificationInfo);
		});
}

void UOneEngineSDKSubsystem::QueryUserActiveQualificationLambda(const FString& ServerId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)> &OnFinishedLambda)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->QueryUserActiveQualificationLambda(ServerId, OnFinishedLambda);
}
	
// 激活设备 
void UOneEngineSDKSubsystem::ActivateDevice(const FString& ServerId, FOneActivateDeviceResultDelegate OneActivateDeviceResultDelegate)
{
	ActivateDeviceLambda(ServerId, [OneActivateDeviceResultDelegate](bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)
		{
			OneActivateDeviceResultDelegate.ExecuteIfBound(bSucceed, bNeedActCode, ActCodePrompt, Code, ErrorMsg);
		});
}

void UOneEngineSDKSubsystem::ActivateDeviceLambda(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)> &OnFinishedLambda)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->QueryActCode(ServerId, OnFinishedLambda);
}

bool UOneEngineSDKSubsystem::ACELogin(const FString& accountId, int accountTypeCommandId, int worldId, FOneOnGetClientPacket delegate)
{
	return OneEngineSDKPCAdapter::Get().GetInterface()->ACELogin(accountId, accountTypeCommandId, worldId, delegate);
}

bool UOneEngineSDKSubsystem::ACEClientPacketReceive(const TArray<uint8>& data)
{
	return OneEngineSDKPCAdapter::Get().GetInterface()->ACEClientPacketReceive(data);
}

bool UOneEngineSDKSubsystem::ACELogout()
{
	return OneEngineSDKPCAdapter::Get().GetInterface()->ACELogout();
}
void UOneEngineSDKSubsystem::GetIpInfo(const FString& Ip,FOnGetIpInfoResultDelegate Delegate)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->GetIpInfo(Ip, Delegate);
}

/// 全球独有的 评价接口和 修改屏幕亮度接口
float UOneEngineSDKSubsystem::GetScreenBrightness()
{
	return 0.0f;
}

void UOneEngineSDKSubsystem::SetScreenBrightness(float BrightnessValue)
{
	
}
	
void UOneEngineSDKSubsystem::RecoverScreenBrightness()
{
	
}
///保持屏幕常亮
void UOneEngineSDKSubsystem::SwitchScreenPermanentBrightnessState(bool bIsTurnOn)
{
	
}
	

///评价接口
void UOneEngineSDKSubsystem::InAppRequestStoreReview(FOneGenericResultDelegate Delegate)
{
	
}

/// AppLink Ios有这个参数
void UOneEngineSDKSubsystem::RequestStoreReview(FString AppLink)
{
	
}

/// 获取渲染器配置文件地址
FString UOneEngineSDKSubsystem::GetRenderConfigFilePath()
{
	return "";
}

// 安全锁相关接口
void UOneEngineSDKSubsystem::UnlockSafeLockUsingPushNotification(const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->UnlockSafeLockUsingPushNotification(RoleName, ServerName, OnUnlockSafeLockResultDelegate);
}

void UOneEngineSDKSubsystem::UnlockSafeLockUsingDynamicCode(const FString& DynamicCode, const FString& RoleName, const FString& ServerName, FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->UnlockSafeLockUsingDynamicCode(DynamicCode, RoleName, ServerName, OnUnlockSafeLockResultDelegate);
}

void UOneEngineSDKSubsystem::StopUnlockSafeLockUsingPushNotification()
{
	OneEngineSDKPCAdapter::Get().GetInterface()->StopUnlockSafeLockUsingPushNotification();
}

// 全球独有的打开Naver论坛
void UOneEngineSDKSubsystem::OpenCommunityByGame(EOneNaverGameType Type, const FString& Pid, bool Scheduled, FOneLoadDelegate LoadCallback, FOneUnloadDelegate UnloadCallback, FOneInGameMenuDelegate InGameMenuCallback)
{
	
}

void UOneEngineSDKSubsystem::TerminateCommunity()
{
	
}

void UOneEngineSDKSubsystem::GetProviderPushState(FOnGetPushStateDelegate Delegate)
{
	
}


void UOneEngineSDKSubsystem::SetAnalyticsCollectionEnabled(bool bEnable)
{
	OneEngineSDKPCAdapter::Get().GetInterface()->SetAnalyticsCollectionEnabled(bEnable);
}

bool UOneEngineSDKSubsystem::IsInstalledApp(EOneShareAppTarget AppTarget)
{
	return false;
}