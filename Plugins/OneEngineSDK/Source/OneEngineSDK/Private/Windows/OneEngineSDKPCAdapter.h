#pragma once
#include "OneEngineSDKSubsystem.h"
#include "Dom/JsonObject.h"
DECLARE_LOG_CATEGORY_EXTERN(PC_OneEngineSDK, Log, All);

class OneEngineSDKPCInterface;

class OneEngineSDKPCAdapter
{
private:
	OneEngineSDKPCAdapter();
	~OneEngineSDKPCAdapter();
public:
	static OneEngineSDKPCAdapter& Get();

private:
	EOneEngineSDKRegionType engineType;
	OneEngineSDKPCInterface* pInterface;

public:
	void DestroyInterface();

	void CreateSDKInterface();

	EOneEngineSDKRegionType GetEngineType();

	OneEngineSDKPCInterface* GetInterface();

private:
	bool bIsDebug;

public:
	void EnableDebugMode(bool Enable);

	bool IsDebugMode();

};

class OneEngineSDKPCInterface
{
public:
	OneEngineSDKPCInterface();
	virtual ~OneEngineSDKPCInterface();

public:
	void ConstructRoleInfo(TSharedPtr<FJsonObject>& jsonObject, const FOneRoleInfo& RoleInfo);

#pragma region Share
public:
	virtual void Init(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate);

	virtual void TrackEventRoleLoginSucceed(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port);

	virtual void TrackEventRoleLoginError(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port, const FString& Code, const FString& Msg);

	virtual void TrackEventRoleLogout(const FOneRoleInfo& RoleInfo);

	virtual void TrackEventRoleCreate(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port);

	virtual void TrackEventRoleLevelUp(const FOneRoleInfo& RoleInfo);

	virtual void GameResReqEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg);

	virtual void GameUpdateAssetEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg);

	virtual void GameResDecEvent(EOneResEventState State, const FString& ErrorMsg);

	virtual void GameGetServerListEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg);

	virtual void TrackEvent(const FString& Name, const TMap<FString, FString>& Payload);

	virtual void TrackEventEnterGameScene(const FString& SceneName, int32 Period, const TMap<FString, FString>& HintMap);

	virtual void TrackEventExitGameScene();

	virtual void TrackEventAddExtraDeviceInfo(const TMap<FString, FString>& ExtraDeviceInfo);

	virtual void RedeemCouponCode(const FString& CouponCode, const FString& ServerId, const FString& RoleId, const FString& RoleLevel, const FString& VipLevel, const TMap<FString, FString>& ExtraInfo, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda);

	virtual void FetchUserRoleInfoList(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)>& OnFinishedLambda, const FString& ServerId);

	virtual FString GetChannelId();

	virtual void FetchAntiAddictionInfo(UOneEngineSDKSubsystem::FOneFetchAntiAddictionInfoDelegate OnFetchAntiAddictionInfo);

	virtual void GetIpInfo(const FString& Ip, UOneEngineSDKSubsystem::FOnGetIpInfoResultDelegate Delegate);
	
	virtual FString GetCurrentGameAppId();
public:
	virtual void Login();

	virtual void Logout();

	virtual FOneUserInfo GetUserInfo();

	virtual void Pay(const FOnePaymentInfo& PaymentInfo);

	virtual void TrackEventAD(const FString& Name, const TMap<FString, FString>& Payload);

	virtual bool IsLoggedIn();

	virtual void OpenUserCenter();

	virtual FString GetChannelMediaId();

	virtual void DisplayCDKeyDialog(UOneEngineSDKSubsystem::FOneGenericResultDelegate OnCDKeyActivateResult, const FString& ServerId);

	virtual void QueryActCode(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)>& OnFinishedLambda);

	virtual void ExchangeActCode(const FString& ServerId, const FString& ActCode, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda);

	virtual void Bind(UOneEngineSDKSubsystem::FOneBindResultDelegate BindDelegate, EOneEngineThirdType BindType);

	virtual void GetUserLocationInfo(const TFunction<void(const FOneUserLocationInfo& LocationInfo)>& OnFinishedLambda);

	virtual int32 GetPlatformOS();

	virtual void GetDeviceInfo(const TFunction<void(const FOneDeviceInfo& DeviceInfo)>& OnFinishedLambda);

	virtual void GetChannelPlatformLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)>& OnFinishedLambda);

	virtual void QueryUserActiveQualificationLambda(const FString& ServerId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)>& OnFinishedLambda);

	virtual void UnlockSafeLockUsingDynamicCode(const FString& DynamicCode, const FString& RoleName, const FString& ServerName, UOneEngineSDKSubsystem::FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate);

#pragma endregion

#pragma region One
public:
	virtual void UserAuthentication(UOneEngineSDKSubsystem::FOneUserAuthenticationResultDelegate OnUserAuthenticationResultDelegate);

	virtual void SetShowDefaultActivationResultToast(bool bShow);

	virtual void OpenComplianceOnWebView();

	virtual void StartAntiAddictionNotify(const FString& ServerId, const FString& RoleId);

	virtual void StopAntiAddictionNotify();

	virtual void EnterAccountCancellation();

	virtual bool ACELogin(const FString& accountId, int accountTypeCommandId, int worldId, UOneEngineSDKSubsystem::FOneOnGetClientPacket delegate);

	virtual bool ACEClientPacketReceive(const TArray<uint8>& data);

	virtual bool ACELogout();

	virtual void UnlockSafeLockUsingPushNotification(const FString& RoleName, const FString& ServerName, UOneEngineSDKSubsystem::FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate);

	virtual void StopUnlockSafeLockUsingPushNotification();
#pragma endregion

#pragma region Global
public:
    virtual void OpenAIHelp(EOneAIHelpType Type, const FString& RoleId, const FString& ServerId, const FString& RoleName);

    virtual void Translate(const FString& Text, const TFunction<void(bool bSucceed, const FString& Result, const FString& ErrorMsg)>& OnFinishedLambda);

    virtual FString GetCurrentLanguage();

    virtual void SetLanguage(const FString& Code);

    virtual void SetUpConfigAppID(const FString& AppId);

    virtual void GetUserTokenListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)>& OnFinishedLambda);

    virtual void TokenLogin(const FString& Token, const FString& Uid, EOneEngineThirdType ThirdType);

    virtual void ThirdLogin(EOneEngineThirdType ThirdType, bool bForcedLogin);

    virtual void GuestLogin();

    virtual void GetProductList(const TArray<FString>& ProductIds, UOneEngineSDKSubsystem::FOneProductInfoDelegate ProductResultDelegate);

	virtual void SetAnalyticsCollectionEnabled(bool bEnable);
#pragma endregion
};
