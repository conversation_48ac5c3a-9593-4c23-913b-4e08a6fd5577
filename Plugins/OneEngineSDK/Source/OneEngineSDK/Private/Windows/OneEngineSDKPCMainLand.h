#ifdef KONEENGINE_REGION_MAINLAND

#pragma once
#include "OneEngineSDKPCAdapter.h"


DECLARE_LOG_CATEGORY_EXTERN(PC_OneEngineSDK_MainLand, Log, All);

class OneEngineSDKPCMainLandInterface : public OneEngineSDKPCInterface
{
private:
	static bool OnQuitGame(bool bForcible);

	static void OnRelogin();

	static void OnBeKickedOut();

private:
	void OnEndPIE(bool val);

private:
	bool bBindEndPIE = false;

public:
	void Init(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate) override;

	void TrackEventRoleLoginSucceed(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port) override;

	void TrackEventRoleLoginError(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port, const FString& Code, const FString& Msg) override;

	void TrackEventRoleLogout(const FOneRoleInfo& RoleInfo) override;

	void TrackEventRoleCreate(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port) override;

	void TrackEventRoleLevelUp(const FOneRoleInfo& RoleInfo) override;

	void GameResReqEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg) override;

	void GameUpdateAssetEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg) override;

	void GameResDecEvent(EOneResEventState State, const FString& ErrorMsg) override;

	void GameGetServerListEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg) override;

	void TrackEvent(const FString& Name, const TMap<FString, FString>& Payload) override;

	void TrackEventEnterGameScene(const FString& SceneName, int32 Period, const TMap<FString, FString>& HintMap) override;

	void TrackEventExitGameScene() override;

	void TrackEventAddExtraDeviceInfo(const TMap<FString, FString>& ExtraDeviceInfo) override;

	void RedeemCouponCode(const FString& CouponCode, const FString& ServerId, const FString& RoleId, const FString& RoleLevel, const FString& VipLevel, const TMap<FString, FString>& ExtraInfo, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda) override;

	void FetchUserRoleInfoList(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)>& OnFinishedLambda, const FString& ServerId) override;

	FString GetChannelId() override;

	virtual void FetchAntiAddictionInfo(UOneEngineSDKSubsystem::FOneFetchAntiAddictionInfoDelegate OnFetchAntiAddictionInfo) override;

	virtual void GetIpInfo(const FString& Ip, UOneEngineSDKSubsystem::FOnGetIpInfoResultDelegate Delegate) override;

	virtual FString GetCurrentGameAppId() override;
public:
	virtual void Login() override;

	virtual void Logout() override;

	virtual FOneUserInfo GetUserInfo()override;

	virtual void Pay(const FOnePaymentInfo& PaymentInfo) override;

	virtual void TrackEventAD(const FString& Name, const TMap<FString, FString>& Payload) override;

	virtual bool IsLoggedIn() override;

	virtual void OpenUserCenter() override;

	virtual FString GetChannelMediaId() override;

	virtual void DisplayCDKeyDialog(UOneEngineSDKSubsystem::FOneGenericResultDelegate OnCDKeyActivateResult, const FString& ServerId) override;

	virtual void QueryActCode(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)>& OnFinishedLambda) override;

	virtual void ExchangeActCode(const FString& ServerId, const FString& ActCode, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda) override;

	virtual void Bind(UOneEngineSDKSubsystem::FOneBindResultDelegate BindDelegate, EOneEngineThirdType BindType) override;

	virtual void GetUserLocationInfo(const TFunction<void(const FOneUserLocationInfo& LocationInfo)>& OnFinishedLambda) override;

	virtual int32 GetPlatformOS() override;

	virtual void GetDeviceInfo(const TFunction<void(const FOneDeviceInfo& DeviceInfo)>& OnFinishedLambda) override;

	virtual void GetChannelPlatformLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)>& OnFinishedLambda) override;

	virtual void QueryUserActiveQualificationLambda(const FString& ServerId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)>& OnFinishedLambda) override;

public:
	virtual void UserAuthentication(UOneEngineSDKSubsystem::FOneUserAuthenticationResultDelegate OnUserAuthenticationResultDelegate) override;

	virtual void SetShowDefaultActivationResultToast(bool bShow) override;

	virtual void OpenComplianceOnWebView() override;

	virtual void StartAntiAddictionNotify(const FString& ServerId, const FString& RoleId) override;

	virtual void StopAntiAddictionNotify() override;

	virtual void EnterAccountCancellation() override;

	virtual bool ACELogin(const FString& accountId, int accountTypeCommandId, int worldId, UOneEngineSDKSubsystem::FOneOnGetClientPacket delegate) override;

	virtual bool ACEClientPacketReceive(const TArray<uint8>& data) override;

	virtual bool ACELogout() override;

	virtual void UnlockSafeLockUsingPushNotification(const FString& RoleName, const FString& ServerName, UOneEngineSDKSubsystem::FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate) override;

	virtual void UnlockSafeLockUsingDynamicCode(const FString& DynamicCode, const FString& RoleName, const FString& ServerName, UOneEngineSDKSubsystem::FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate) override;

	virtual void StopUnlockSafeLockUsingPushNotification() override;
};

#endif

