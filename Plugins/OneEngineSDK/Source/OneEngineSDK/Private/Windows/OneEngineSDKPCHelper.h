#pragma once
#include "OneEngineSDKSubsystem.h"

DECLARE_LOG_CATEGORY_EXTERN(PC_OneEngineSDK_Helper, Log, All);

class OneEngineSDKPCHelper
{
private:
	FString orderId;
	FString userId;
	FString token;
	void* handle;

private:
	OneEngineSDKPCHelper();
	~OneEngineSDKPCHelper() {}
public:
	static OneEngineSDKPCHelper& Get();

public:

	void* GetHandle();
	void SetHandle(void* val);

	void SetOrderId(FString val);
	FString GetOrderId();

	void SetUserId(FString val);
	FString GetUserId();

	void SetToken(FString val);
	FString GetToken();

	void ResetUserInfo();

	FString ConverServerId(const FString& ServerId);

	FString GetPluginVersion();

private:
	FCriticalSection mMutexMapPreQueryCDKeyStatus;
	TMap<int, TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)>> mMapPreQueryCDKeyStatusResultDelegate;

public:
	void SetPreQueryCDKeyStatusDelegate(int contextId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)>& OnFinishedLambda);

	void OnPreQueryCDKeyStatus(int code, const FString& message, const FString& result, int contextId);

private:
	FCriticalSection mMutexMapGetDeviceInfo;
	TMap<int, TFunction<void(const FOneDeviceInfo& DeviceInfo)>> mMapGetDeviceInfoResultDelegate;

public:
	void SetGetDeviceInfoDelegate(int contextId, const TFunction<void(const FOneDeviceInfo& DeviceInfo)>& OnFinishedLambda);

	void OnGetDeviceInfo(int code, const FString& message, const FString& data, int contextId, const FString& deviceId, const FString& deviceSys);

private:
	FCriticalSection mMutexMapGenericResultDelegate;
	TMap<int, UOneEngineSDKSubsystem::FOneGenericResultDelegate> mMapGenericResultDelegate;

public:
	void SetGenericResultDelegate(int contextId, UOneEngineSDKSubsystem::FOneGenericResultDelegate Delegate);

	void OnGenericResult(bool success, int code, const FString& msg, int contextId);

private:
	FCriticalSection mMutexMapGetRoleListResultDelegate;
	TMap<int, TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)> > mMapGetRoleListResultDelegate;

public:
	void SetGetRoleListResultDelegate(int contextId, const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)>& OnFinishedLambda);

	void OnGetRoleListResult(int code, const FString& msg, const FString& result, int contextId);

private:
	FCriticalSection mMutexMapGetUserLocationResultDelegate;
	TMap<int, TFunction<void(const FOneUserLocationInfo& LocationInfo)>> mMapGetUserLocationResultDelegate;

public:
	void SetGetUserLocationResultDelegate(int contextId, const TFunction<void(const FOneUserLocationInfo& LocationInfo)>& OnFinishedLambda);

	void OnGetUserLocationResult(const FString& result, int contextId);

private:
	FCriticalSection mMutexMapGenericResultLambda;
	TMap<int, TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>> mMapGenericResultLambda;

public:
	void SetGenericResultLambda(int contextId, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda);

	void OnGenericResultLambda(bool success, int code, const FString& msg, int contextId);

private:
	FCriticalSection mMutexTranslateResultDelegate;
	TMap<int, TFunction<void(bool bSucceed, const FString& Result, const FString& ErrorMsg)>> mMapTranslateResultDelegate;

public:
	void SetTranslateResultDelegate(int contextId, const TFunction<void(bool bSucceed, const FString& Result, const FString& ErrorMsg)>& OnFinishedLambda);

	void OnTranslateResult(int code, const FString& msg, const FString& result, int contextId);

private:
	FCriticalSection mMutexMainLandQueryActCodeResultDelegate;
	TMap<int, TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)>> mMapMainLandQueryActCodeResultDelegate;

public:
	void SetMainLandQueryActCodeResultDelegate(int contextId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)>& OnFinishedLambda);

	void OnMainLandQueryActCodeResult(bool success, int code, const FString& msg, int contextId);

private:
	FCriticalSection mMutexMapUserAuthentication;
	TMap<int, UOneEngineSDKSubsystem::FOneUserAuthenticationResultDelegate> mMapUserAuthenticationResultDelegate;

public:
	void SetUserAuthenticationResultDelegate(int contextId, UOneEngineSDKSubsystem::FOneUserAuthenticationResultDelegate Delegate);

	void OnUserAuthenticationResult(bool success, int anthResult, bool hasNetError, const FString& msg, int contextId);

private:
	FCriticalSection mMutexMapFetchAntiAddictionInfo;
	TMap<int, UOneEngineSDKSubsystem::FOneFetchAntiAddictionInfoDelegate> mMapFetchAntiAddictionInfoResultDelegate;

public:
	void SetFetchAntiAddictionInfoResultDelegate(int contextId, UOneEngineSDKSubsystem::FOneFetchAntiAddictionInfoDelegate Delegate);

	void OnFOneFetchAntiAddictionInfo(int code, const FString& result, int contextId);

public:
	void OnInitResult(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate, bool success, int32 code, const FString& msg);

	void OnAntiAddictionTimeout(bool bForceKick, const FString& antiInfo);

public:
	void OnActiveNoUICheckUser(int code, const FString& message, int contextId);

	void OnActiveNoUIUserActivate(int code, const FString& message, int contextId);

	void SetGlobalQueryActCodeResultDelegate(const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)>& OnFinishedLambda, int contextId);

	void SetGlobalExchangeActCodeResultDelegate(const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda, int contextId);

private:
	FCriticalSection mMutexMapQueryActCodeResultLambda;
	TMap<int, TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)>> mMapQueryActCodeResultLambda;
	FCriticalSection mMutexMapExchangeActCodeResultLambda;
	TMap<int, TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>> mMapExchangeActCodeResultLambda;

public:
	void SetBindResultDelegateAndBindType(UOneEngineSDKSubsystem::FOneBindResultDelegate BindDelegate, EOneEngineThirdType bindType);

	void OnBindPhoneResult(int code, const FString& message, const FString& phoneNumber);

	void OnBindWMPassportResult(int code, const FString& message, const FString& passport);

	void OnGlobalBindResult(const FString& userInfo, int code, const FString& message);
private:
	UOneEngineSDKSubsystem::FOneBindResultDelegate bindResultDelegate;
	EOneEngineThirdType currentBindType;

public:
	void SetGlobalPlatformDelegate(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)>& OnFinishedLambda, int contextId);

	void OnGlobalPlatform(int code, const FString& message, const FString& platform, int contextId);

private:
	FCriticalSection mMutexMapPlatformDelegateLambda;
	TMap<int, TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)>> mMapPlatformDelegateLambda;

private:
	FOneAntiAddictionInfo ParseAntiInfo(const FString& info);

public:
	EOneEngineThirdType CovertGlobalLoginTypeToEngineThirdType(int LoginType);

	int CovertEngineThirdTypeToGlobalLoginType(EOneEngineThirdType LoginType);

	FOneUserInfo ParseGlobalUserInfo(const FString& info);

public:
	void OnGlobalGetTokenList(int code, const FString& message, const FString& tokens, int contextId);

	void SetGlobalGetTokenListDelegate(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)>& OnFinishedLambda, int contextId);

private:
	FCriticalSection mMutexMapGetTokenListLambda;
	TMap<int, TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)>> mMapGetTokenListLambda;

public:
	void SetACEClientPacketDelegate(UOneEngineSDKSubsystem::FOneOnGetClientPacket delegate);
	void OnGetACEClientPacket(const unsigned char* data, int len);

private:
	UOneEngineSDKSubsystem::FOneOnGetClientPacket OnGetClientPacketDelegate;

public:
	void SetSafeLockResultDelegate(int contextId, UOneEngineSDKSubsystem::FOneUnlockSafeLockResultDelegate Delegate);

	void OnSafeLockResult(EOneUnlockSafeLockResult result, int code, const FString& message, const FString& unlockToken, EOneUnlockSafeLockType lockType, int contextId);

private:
	FCriticalSection mMutexSafeUnlock;
	TMap<int, UOneEngineSDKSubsystem::FOneUnlockSafeLockResultDelegate> mMapSafeUnlockResultDelegate;

public:
	void SetGetIpInfoResultDelegate(UOneEngineSDKSubsystem::FOnGetIpInfoResultDelegate OnGetIpInfoDelegate, int contextId);

	void OnGetIpInfo(int code, const FString& message, const FString& result, int contextId);

private:
	FCriticalSection mMutexGetIpInfo;
	TMap<int, UOneEngineSDKSubsystem::FOnGetIpInfoResultDelegate> mMapGetIpInfoDelegate;

public:
	void SetGetProductListResultDelegate(int contextId, UOneEngineSDKSubsystem::FOneProductInfoDelegate ProductResultDelegate);

	void OnGetProductList(int contextId, const FString& msg, const FString& result, int code);
private:
	FCriticalSection mMutexGetProductList;
	TMap<int, UOneEngineSDKSubsystem::FOneProductInfoDelegate> mMapGetProductListResultDelegate;
};