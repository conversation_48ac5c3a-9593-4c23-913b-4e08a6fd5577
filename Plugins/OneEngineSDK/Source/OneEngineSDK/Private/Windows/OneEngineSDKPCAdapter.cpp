
#include "OneEngineSDKPCAdapter.h"

#if WITH_EDITOR
#include "Editor.h"
#endif

#include "OneEngineSDKPCHelper.h"
#include "OneEngineSDKHelper.h"
#include "JsonObjectWrapper.h"
#include "Serialization/JsonSerializer.h"
#include "Windows/WindowsPlatformMisc.h"
#include "OneEngineSDKPCOverSea.h"
#include "OneEngineSDKPCMainLand.h"
#include "Async/Async.h"
#include "HAL/FileManager.h"
#include "Misc/ConfigCacheIni.h"
const int REGION_NO_SUPPORT_CODE = 200202;
const FString REGION_NO_SUPPORT_MSG = TEXT("No Support This Region");

DEFINE_LOG_CATEGORY(PC_OneEngineSDK);

#define CURRENT_CLASS_FUNCTION (FString(__FUNCTION__))
#define CURRENT_LINE  (FString::FromInt(__LINE__))
#define CURRENT_CLASS_FUNCTION_LINE (CURRENT_CLASS_FUNCTION + ":LNO." + CURRENT_LINE )

#define TRACK \
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK, Log, TEXT("%s"), *CURRENT_CLASS_FUNCTION_LINE); \
	}\
} 
#define LOG_PRINTF(FormatString , ...)  \
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK, Log, TEXT("%s: %s"), *CURRENT_CLASS_FUNCTION_LINE, *FString::Printf(TEXT(FormatString), ##__VA_ARGS__ ) ); \
	}\
} 

#define LOG_ERROR_PRINTF(FormatString , ...) \
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK, Error, TEXT("%s: %s"), *CURRENT_CLASS_FUNCTION_LINE, *FString::Printf(TEXT(FormatString), ##__VA_ARGS__ ) ); \
	}\
} 

#define LOG_ERROR_PRINTF_ERROR_INVOKE \
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK, Error, TEXT("%s: ERROR INVOKE"), *CURRENT_CLASS_FUNCTION_LINE); \
	}\
} 


typedef struct Function_IO_LOG
{
public:
	Function_IO_LOG(const FString& val)
	{
		if (OneEngineSDKPCAdapter::Get().IsDebugMode())
		{
			mVal = val;
			UE_LOG(PC_OneEngineSDK, Log, TEXT("%s start"), *mVal);
		}
	}
	~Function_IO_LOG()
	{
		if (OneEngineSDKPCAdapter::Get().IsDebugMode())
		{
			UE_LOG(PC_OneEngineSDK, Log, TEXT("%s end"), *mVal);
		}
	}
private:
	FString mVal;

}Function_IO_LOG;

#define LOG_FUNCTION_IO Function_IO_LOG a(CURRENT_CLASS_FUNCTION_LINE)

OneEngineSDKPCAdapter::OneEngineSDKPCAdapter()
	: engineType(EOneEngineSDKRegionType::Mainland)
	, pInterface(nullptr)
	, bIsDebug(false)
{
	FString Region = TEXT("");
	GConfig->GetString(TEXT("/Script/OneEngineEditor.OneEngineSettings"), TEXT("SDKRegion"), Region, GGameIni);
	if (Region == TEXT("Mainland"))
	{
		engineType = EOneEngineSDKRegionType::Mainland;
	}
	else
	{
		engineType = EOneEngineSDKRegionType::Oversea;
	}
	CreateSDKInterface();
}

OneEngineSDKPCAdapter::~OneEngineSDKPCAdapter()
{
	DestroyInterface();
}

OneEngineSDKPCAdapter& OneEngineSDKPCAdapter::Get()
{
	static OneEngineSDKPCAdapter adapter;
	return adapter;
}

void OneEngineSDKPCAdapter::DestroyInterface()
{
	if (pInterface != nullptr)
	{
		delete pInterface;
		pInterface = nullptr;
	}
}

void OneEngineSDKPCAdapter::CreateSDKInterface()
{
#ifdef KONEENGINE_REGION_MAINLAND
	pInterface = new OneEngineSDKPCMainLandInterface();
#elif defined KONEENGINE_REGION_OVERSEA
	pInterface = new OneEngineSDKPCOverSeaInterface();
#else
	pInterface = new OneEngineSDKPCInterface();
#endif
}

EOneEngineSDKRegionType OneEngineSDKPCAdapter::GetEngineType()
{
	return engineType;
}

OneEngineSDKPCInterface* OneEngineSDKPCAdapter::GetInterface()
{
	return pInterface;
}

void OneEngineSDKPCAdapter::EnableDebugMode(bool Enable)
{
	bIsDebug = Enable;
}

bool OneEngineSDKPCAdapter::IsDebugMode()
{
	if (!UE_BUILD_SHIPPING && !bIsDebug)
	{
		bIsDebug = true;
	}
	if (!bIsDebug)
	{
		FString Path = FPaths::Combine(FPlatformProcess::BaseDir(), TEXT("SDK_DEBUG"));
		bIsDebug = IFileManager::Get().FileExists(*Path);
	}
	return bIsDebug;
}

OneEngineSDKPCInterface::OneEngineSDKPCInterface()
{

}

OneEngineSDKPCInterface::~OneEngineSDKPCInterface()
{

}

void OneEngineSDKPCInterface::Init(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	OneEngineSDKPCHelper::Get().OnInitResult(InitDelegate, false, 100101, TEXT("init failed with error config"));
}

void OneEngineSDKPCInterface::TrackEventRoleLoginSucceed(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::TrackEventRoleLoginError(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port, const FString& Code, const FString& Msg)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::TrackEventRoleLogout(const FOneRoleInfo& RoleInfo)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::TrackEventRoleCreate(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::TrackEventRoleLevelUp(const FOneRoleInfo& RoleInfo)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::ConstructRoleInfo(TSharedPtr<FJsonObject>& jsonObject, const FOneRoleInfo& RoleInfo)
{
	jsonObject->SetStringField(TEXT("roleId"), RoleInfo.RoleId);
	jsonObject->SetStringField(TEXT("roleName"), RoleInfo.RoleName);
	jsonObject->SetStringField(TEXT("vip"), RoleInfo.Vip);
	jsonObject->SetStringField(TEXT("level"), RoleInfo.Level);

	jsonObject->SetStringField(TEXT("serverId"), RoleInfo.ServerId);
	jsonObject->SetStringField(TEXT("serverName"), RoleInfo.ServerName);
	jsonObject->SetStringField(TEXT("combatValue"), RoleInfo.CombatValue);
}

void OneEngineSDKPCInterface::GameResReqEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::GameUpdateAssetEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::GameResDecEvent(EOneResEventState State, const FString& ErrorMsg)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::GameGetServerListEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::TrackEvent(const FString& Name, const TMap<FString, FString>& Payload)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::TrackEventEnterGameScene(const FString& SceneName, int32 Period, const TMap<FString, FString>& HintMap)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::TrackEventExitGameScene()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::TrackEventAddExtraDeviceInfo(const TMap<FString, FString>& ExtraDeviceInfo)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::RedeemCouponCode(const FString& CouponCode, const FString& ServerId, const FString& RoleId, const FString& RoleLevel, const FString& VipLevel, const TMap<FString, FString>& ExtraInfo, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	if (OnFinishedLambda)
	{
		OnFinishedLambda(false, REGION_NO_SUPPORT_CODE, REGION_NO_SUPPORT_MSG);
	}
}

void OneEngineSDKPCInterface::FetchUserRoleInfoList(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  TArray<FOneURCRoleInfo>& RoleList)>& OnFinishedLambda, const FString& ServerId)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	if (OnFinishedLambda)
	{
		OnFinishedLambda(false, REGION_NO_SUPPORT_CODE, REGION_NO_SUPPORT_MSG, TArray<FOneURCRoleInfo>());
	}
}

FString OneEngineSDKPCInterface::GetChannelId()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	return "";
}

void OneEngineSDKPCInterface::GetIpInfo(const FString& Ip, UOneEngineSDKSubsystem::FOnGetIpInfoResultDelegate Delegate)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	AsyncTask(ENamedThreads::GameThread, [Delegate]()
		{
			Delegate.ExecuteIfBound(false, FUserIpInfo(), REGION_NO_SUPPORT_CODE, REGION_NO_SUPPORT_MSG);
		});
}

FString OneEngineSDKPCInterface::GetCurrentGameAppId()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	return "";
}

void OneEngineSDKPCInterface::Login()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::Logout()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

FOneUserInfo OneEngineSDKPCInterface::GetUserInfo()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	return FOneUserInfo();
}

void OneEngineSDKPCInterface::Pay(const FOnePaymentInfo& PaymentInfo)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::TrackEventAD(const FString& Name, const TMap<FString, FString>& Payload)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

bool OneEngineSDKPCInterface::IsLoggedIn()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	return false;
}

void OneEngineSDKPCInterface::OpenUserCenter()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

FString OneEngineSDKPCInterface::GetChannelMediaId()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	return FString();
}

void OneEngineSDKPCInterface::DisplayCDKeyDialog(UOneEngineSDKSubsystem::FOneGenericResultDelegate OnCDKeyActivateResult, const FString& ServerId)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	AsyncTask(ENamedThreads::GameThread, [OnCDKeyActivateResult]()
	{
		OnCDKeyActivateResult.ExecuteIfBound(false, REGION_NO_SUPPORT_CODE, REGION_NO_SUPPORT_MSG);
	});
}

void OneEngineSDKPCInterface::QueryActCode(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)>& OnFinishedLambda)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	if (OnFinishedLambda)
	{
		OnFinishedLambda(false,  false, TEXT(""), REGION_NO_SUPPORT_CODE, REGION_NO_SUPPORT_MSG);
	}
}

void OneEngineSDKPCInterface::ExchangeActCode(const FString& ServerId, const FString& ActCode, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	if (OnFinishedLambda)
	{
		OnFinishedLambda(false, REGION_NO_SUPPORT_CODE, REGION_NO_SUPPORT_MSG);
	}
}

void OneEngineSDKPCInterface::Bind(UOneEngineSDKSubsystem::FOneBindResultDelegate BindDelegate, EOneEngineThirdType BindType)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	AsyncTask(ENamedThreads::GameThread, [BindDelegate]()
	{
		BindDelegate.ExecuteIfBound(false, REGION_NO_SUPPORT_CODE, REGION_NO_SUPPORT_MSG, EOneEngineThirdType::Phone, FOneUserInfo());
	});
}

void OneEngineSDKPCInterface::GetUserLocationInfo(const TFunction<void(const FOneUserLocationInfo& LocationInfo)>& OnFinishedLambda)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	if (OnFinishedLambda)
	{
		OnFinishedLambda(FOneUserLocationInfo());
	}
}

void OneEngineSDKPCInterface::UserAuthentication(UOneEngineSDKSubsystem::FOneUserAuthenticationResultDelegate OnUserAuthenticationResultDelegate)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	AsyncTask(ENamedThreads::GameThread, [OnUserAuthenticationResultDelegate]()
	{
		OnUserAuthenticationResultDelegate.ExecuteIfBound(false, -1, false, REGION_NO_SUPPORT_MSG);
	});
}

void OneEngineSDKPCInterface::SetShowDefaultActivationResultToast(bool bShow)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

int32 OneEngineSDKPCInterface::GetPlatformOS()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	return int32();
}

void OneEngineSDKPCInterface::GetDeviceInfo(const TFunction<void(const FOneDeviceInfo& DeviceInfo)>& OnFinishedLambda)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	if (OnFinishedLambda)
	{
		OnFinishedLambda(FOneDeviceInfo());
	}
}

void OneEngineSDKPCInterface::GetChannelPlatformLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)>& OnFinishedLambda)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	if (OnFinishedLambda)
	{
		OnFinishedLambda(false, REGION_NO_SUPPORT_CODE, REGION_NO_SUPPORT_MSG, TEXT(""));
	}
}

void OneEngineSDKPCInterface::OpenComplianceOnWebView()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::FetchAntiAddictionInfo(UOneEngineSDKSubsystem::FOneFetchAntiAddictionInfoDelegate OnFetchAntiAddictionInfo)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	//AsyncTask(ENamedThreads::GameThread, [OnFetchAntiAddictionInfo]()
	//{
	//	OnFetchAntiAddictionInfo.ExecuteIfBound(false, -1, false, REGION_NO_SUPPORT_MSG);
	//});
}

void OneEngineSDKPCInterface::StartAntiAddictionNotify(const FString& ServerId, const FString& RoleId)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::StopAntiAddictionNotify()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::EnterAccountCancellation()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

bool OneEngineSDKPCInterface::ACELogin(const FString& accountId, int accountTypeCommandId, int worldId, UOneEngineSDKSubsystem::FOneOnGetClientPacket delegate)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	return false;
}

bool OneEngineSDKPCInterface::ACEClientPacketReceive(const TArray<uint8>& data)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	return false;
}

bool OneEngineSDKPCInterface::ACELogout()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	return false;
}

void OneEngineSDKPCInterface::UnlockSafeLockUsingPushNotification(const FString& RoleName, const FString& ServerName, UOneEngineSDKSubsystem::FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	AsyncTask(ENamedThreads::GameThread, [OnUnlockSafeLockResultDelegate]()
	{
		OnUnlockSafeLockResultDelegate.ExecuteIfBound(EOneUnlockSafeLockResult::UnlockFailed, TEXT(""), REGION_NO_SUPPORT_CODE, REGION_NO_SUPPORT_MSG, EOneUnlockSafeLockType::PushNotification);
	});
}

void OneEngineSDKPCInterface::UnlockSafeLockUsingDynamicCode(const FString& DynamicCode, const FString& RoleName, const FString& ServerName, UOneEngineSDKSubsystem::FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	AsyncTask(ENamedThreads::GameThread, [OnUnlockSafeLockResultDelegate]()
	{
		OnUnlockSafeLockResultDelegate.ExecuteIfBound(EOneUnlockSafeLockResult::UnlockFailed, TEXT(""), REGION_NO_SUPPORT_CODE, REGION_NO_SUPPORT_MSG, EOneUnlockSafeLockType::DynamicCode);
	});
}

void OneEngineSDKPCInterface::StopUnlockSafeLockUsingPushNotification()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::OpenAIHelp(EOneAIHelpType Type, const FString& RoleId, const FString& ServerId, const FString& RoleName)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::Translate(const FString& Text, const TFunction<void(bool bSucceed, const FString& Result, const FString& ErrorMsg)>& OnFinishedLambda)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	if (OnFinishedLambda)
	{
		OnFinishedLambda(false, TEXT(""), REGION_NO_SUPPORT_MSG);
	}
}

FString OneEngineSDKPCInterface::GetCurrentLanguage()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	return FString();
}

void OneEngineSDKPCInterface::SetLanguage(const FString& Code)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::SetUpConfigAppID(const FString& AppId)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::GetUserTokenListLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneUserInfo>& TokenList)>& OnFinishedLambda)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	if (OnFinishedLambda)
	{
		OnFinishedLambda(false, REGION_NO_SUPPORT_CODE, REGION_NO_SUPPORT_MSG, TArray<FOneUserInfo>());
	}
}

void OneEngineSDKPCInterface::TokenLogin(const FString& Token, const FString& Uid, EOneEngineThirdType ThirdType)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::ThirdLogin(EOneEngineThirdType ThirdType, bool bForcedLogin)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::GuestLogin()
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}

void OneEngineSDKPCInterface::QueryUserActiveQualificationLambda(const FString& ServerId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)>& OnFinishedLambda)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	if (OnFinishedLambda)
	{
		OnFinishedLambda(false, REGION_NO_SUPPORT_CODE, REGION_NO_SUPPORT_MSG, FOneActiveQualificationInfo());
	}
}

void OneEngineSDKPCInterface::GetProductList(const TArray<FString>& ProductIds, UOneEngineSDKSubsystem::FOneProductInfoDelegate ProductResultDelegate)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
	AsyncTask(ENamedThreads::GameThread, [ProductResultDelegate]()
	{
		ProductResultDelegate.ExecuteIfBound(false, REGION_NO_SUPPORT_CODE, TArray<FOneProductInfo>());
	});
}

void OneEngineSDKPCInterface::SetAnalyticsCollectionEnabled(bool bEnable)
{
	LOG_ERROR_PRINTF_ERROR_INVOKE;
}
