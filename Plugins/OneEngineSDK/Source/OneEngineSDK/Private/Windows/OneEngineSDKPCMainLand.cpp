#ifdef KONEENGINE_REGION_MAINLAND
#include "OneEngineSDKPCMainLand.h"
#include "OneEngineSDKPCHelper.h"
#include "JsonObjectWrapper.h"
#include "Serialization/JsonSerializer.h"
#include "Windows/WindowsPlatformMisc.h"
#include "Async/Async.h"
#include "PcOneSdk.h"
#include "Misc/ConfigCacheIni.h"

DEFINE_LOG_CATEGORY(PC_OneEngineSDK_MainLand);

#define CURRENT_CLASS_FUNCTION_MAINLAND (FString(__FUNCTION__))
#define CURRENT_LINE_MAINLAND  (FString::FromInt(__LINE__))
#define CURRENT_CLASS_FUNCTION_LINE_MAINLAND (CURRENT_CLASS_FUNCTION_MAINLAND + ":LNO." + CURRENT_LINE_MAINLAND )

#define TRACK_MAINLAND \
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK_MainLand, Log, TEXT("%s"),*CURRENT_CLASS_FUNCTION_LINE_MAINLAND); \
	}\
}

#define LOG_PRINTF_MAINLAND(FormatString , ...) \
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK_MainLand, Log, TEXT("%s: %s"), *CURRENT_CLASS_FUNCTION_LINE_MAINLAND, *FString::Printf(TEXT(FormatString), ##__VA_ARGS__ ) ); \
	}\
}

#define LOG_ERROR_PRINTF_MAINLAND(FormatString , ...) \
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK_MainLand, Error, TEXT("%s: %s"), *CURRENT_CLASS_FUNCTION_LINE_MAINLAND, *FString::Printf(TEXT(FormatString), ##__VA_ARGS__ ) ); \
	}\
}

#define LOG_ERROR_PRINTF_ERROR_INVOKE_MAINLAND \
{ \
	if(OneEngineSDKPCAdapter::Get().IsDebugMode()) \
	{ \
		UE_LOG(PC_OneEngineSDK_MainLand, Error, TEXT("%s: ERROR INVOKE"), *CURRENT_CLASS_FUNCTION_LINE_MAINLAND); \
	}\
}

typedef struct Function_IO_LOG_MAINLAND
{
public:
	Function_IO_LOG_MAINLAND(const FString& val)
	{
		if (OneEngineSDKPCAdapter::Get().IsDebugMode())
		{
			mVal = val;
			UE_LOG(PC_OneEngineSDK_MainLand, Log, TEXT("%s start"), *mVal);
		}
	}
	~Function_IO_LOG_MAINLAND()
	{
		if (OneEngineSDKPCAdapter::Get().IsDebugMode())
		{
			UE_LOG(PC_OneEngineSDK_MainLand, Log, TEXT("%s end"), *mVal);
		}
	}
private:
	FString mVal;

}Function_IO_LOG_MAINLAND;

#define LOG_FUNCTION_IO_MAINLAND Function_IO_LOG_MAINLAND a(CURRENT_CLASS_FUNCTION_LINE_MAINLAND)

bool OneEngineSDKPCMainLandInterface::OnQuitGame(bool bForcible)
{
	LOG_PRINTF_MAINLAND("bForcible:%d", bForcible ? 1 : 0);
	FOneEngineSDKHelper::OnLogoutResultDelegate(true, 0, TEXT("success"));
	return false;
}

void OneEngineSDKPCMainLandInterface::OnRelogin()
{
	LOG_FUNCTION_IO_MAINLAND;
	FOneEngineSDKHelper::OnLogoutResultDelegate(true, 0, TEXT("success"));
}

void OneEngineSDKPCMainLandInterface::OnBeKickedOut()
{
	LOG_FUNCTION_IO_MAINLAND;
	FOneEngineSDKHelper::OnLogoutResultDelegate(true, 0, TEXT("success"));
}

void OneEngineSDKPCMainLandInterface::OnEndPIE(bool val)
{
	LOG_FUNCTION_IO_MAINLAND;
	LOG_PRINTF_MAINLAND("EndPIE %d", val == true ? 1 : 0);
	if (OneEngineSDKPCHelper::Get().GetHandle() != nullptr)
	{
		HRESULT ret = PC_ONE_SDK::OneSdk_UnInit(OneEngineSDKPCHelper::Get().GetHandle());
		if (ret == S_OK)
		{
			LOG_PRINTF_MAINLAND("one uninit success");
		}
		OneEngineSDKPCHelper::Get().SetHandle(nullptr);
		LOG_PRINTF_MAINLAND("EndPIE %d", val == true ? 1 : 0);
	}
}

void OneEngineSDKPCMainLandInterface::Init(UOneEngineSDKSubsystem::FOneInitDelegate InitDelegate)
{
	LOG_FUNCTION_IO_MAINLAND;
	FString appId = TEXT("");
	FString appKey = TEXT("");
	FString ConfigPath = TEXT("oneSdkConfig.config");
	GConfig->GetString(TEXT("/Script/OneEngineEditor.OneEngineSettings"), TEXT("AppID"), appId, GGameIni);
	GConfig->GetString(TEXT("/Script/OneEngineEditor.OneEngineSettings"), TEXT("OneAppKey"), appKey, GGameIni);
	LOG_PRINTF_MAINLAND(" AppId = %s", *appId);

	if (appId.IsEmpty())
	{
		OneEngineSDKPCHelper::Get().OnInitResult(InitDelegate, false, -10001, TEXT("appId Empty!"));
		return;
	}

	if (appKey.IsEmpty())
	{
		LOG_PRINTF_MAINLAND("appKey Empty!");
		appKey = TEXT("appKey");
	}
	
    auto handle = PC_ONE_SDK::OneSdk_Union_Init(TCHAR_TO_WCHAR(*appId), TCHAR_TO_WCHAR(*appKey), TCHAR_TO_WCHAR(*ConfigPath),
		OneEngineSDKPCMainLandInterface::OnQuitGame, OneEngineSDKPCMainLandInterface::OnRelogin, OneEngineSDKPCMainLandInterface::OnBeKickedOut);
    if (handle == nullptr)
    {
        LOG_PRINTF_MAINLAND("handle == nullptr");
        OneEngineSDKPCHelper::Get().OnInitResult(InitDelegate, false, 10001, TEXT("init failed"));
        PC_ONE_SDK::OneSdk_StartPlatform(nullptr);
        if (PC_ONE_SDK::LoadOneSDKSuccess())
        {
            LOG_PRINTF_MAINLAND("OneEngineSDK LoadLibrary Success");
        }
        else
        {
            LOG_PRINTF_MAINLAND("OneEngineSDK LoadLibrary Failed");
        }
    }
    else
    {
        LOG_PRINTF_MAINLAND("init success");
        OneEngineSDKPCHelper::Get().OnInitResult(InitDelegate, true, 0, TEXT("init success"));
        OneEngineSDKPCHelper::Get().SetHandle(handle);
		PC_ONE_SDK::OneSdk_ReportSDKVersionInfoEx(TCHAR_TO_UTF8(*OneEngineSDKPCHelper::Get().GetPluginVersion()));
#if WITH_EDITOR
        if (!bBindEndPIE)
        {
            FEditorDelegates::EndPIE.AddRaw(this, &OneEngineSDKPCMainLandInterface::OnEndPIE);
            bBindEndPIE = true;
        }
#endif
    }
    LOG_PRINTF_MAINLAND("OneEngineSDK Version %s", *OneEngineSDKPCHelper::Get().GetPluginVersion());
}

void OneEngineSDKPCMainLandInterface::TrackEventRoleLoginSucceed(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
	LOG_FUNCTION_IO_MAINLAND;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	ConstructRoleInfo(jsonObject, RoleInfo);

	jsonObject->SetStringField(TEXT("ip"), Ip);
	jsonObject->SetStringField(TEXT("port"), Port);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	FString eventKey = TEXT("roleLoginSDK");
	LOG_PRINTF_MAINLAND("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_ONE_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
}

void OneEngineSDKPCMainLandInterface::TrackEventRoleLoginError(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port, const FString& Code, const FString& Msg)
{
	LOG_FUNCTION_IO_MAINLAND;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	ConstructRoleInfo(jsonObject, RoleInfo);

	jsonObject->SetStringField(TEXT("ip"), Ip);
	jsonObject->SetStringField(TEXT("port"), Port);
	jsonObject->SetStringField(TEXT("errorCode"), Code);
	jsonObject->SetStringField(TEXT("errorMsg"), Msg);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	FString eventKey = TEXT("roleLoginErrorSDK");
	LOG_PRINTF_MAINLAND("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_ONE_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
}

void OneEngineSDKPCMainLandInterface::TrackEventRoleLogout(const FOneRoleInfo& RoleInfo)
{
	LOG_FUNCTION_IO_MAINLAND;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	ConstructRoleInfo(jsonObject, RoleInfo);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	FString eventKey = TEXT("roleLogoutSDK");
	LOG_PRINTF_MAINLAND("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_ONE_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
}

void OneEngineSDKPCMainLandInterface::TrackEventRoleCreate(const FOneRoleInfo& RoleInfo, const FString& Ip, const FString& Port)
{
	LOG_FUNCTION_IO_MAINLAND;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	ConstructRoleInfo(jsonObject, RoleInfo);

	jsonObject->SetStringField(TEXT("ip"), Ip);
	jsonObject->SetStringField(TEXT("port"), Port);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	FString eventKey = TEXT("Create_role");
	LOG_PRINTF_MAINLAND("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_ONE_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
}

void OneEngineSDKPCMainLandInterface::TrackEventRoleLevelUp(const FOneRoleInfo& RoleInfo)
{
	LOG_FUNCTION_IO_MAINLAND;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	ConstructRoleInfo(jsonObject, RoleInfo);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	FString eventKey = TEXT("roleLevelUpTo") + RoleInfo.Level;
	LOG_PRINTF_MAINLAND("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_ONE_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
}

void OneEngineSDKPCMainLandInterface::GameResReqEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);
	FString eventKey = TEXT("gameResReqError");
	if (State == EOneResEventState::Begin)
	{
		eventKey = TEXT("gameResReqBegin");
	}
	else if (State == EOneResEventState::Success)
	{
		eventKey = TEXT("gameResReqSuccess");
	}

	jsonObject->SetStringField(TEXT("url"), Url);
	jsonObject->SetStringField(TEXT("errorCode"), ErrorCode);
	jsonObject->SetStringField(TEXT("errorMsg"), ErrorMsg);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);

	LOG_PRINTF_MAINLAND("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_ONE_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
}

void OneEngineSDKPCMainLandInterface::GameUpdateAssetEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);
	FString eventKey = TEXT("gameUpdateAssetError");
	if (State == EOneResEventState::Begin)
	{
		eventKey = TEXT("gameUpdateAssetBegin");
	}
	else if (State == EOneResEventState::Success)
	{
		eventKey = TEXT("gameUpdateAssetSuccess");
	}

	jsonObject->SetStringField(TEXT("url"), Url);
	jsonObject->SetStringField(TEXT("errorCode"), ErrorCode);
	jsonObject->SetStringField(TEXT("errorMsg"), ErrorMsg);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);

	LOG_PRINTF_MAINLAND("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_ONE_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
}

void OneEngineSDKPCMainLandInterface::GameResDecEvent(EOneResEventState State, const FString& ErrorMsg)
{
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);
	FString eventKey = TEXT("gameResDecError");
	if (State == EOneResEventState::Begin)
	{
		eventKey = TEXT("gameResDecBegin");
	}
	else if (State == EOneResEventState::Success)
	{
		eventKey = TEXT("gameResDecSuccess");
	}

	jsonObject->SetStringField(TEXT("errorMsg"), ErrorMsg);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);

	LOG_PRINTF_MAINLAND("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_ONE_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
}

void OneEngineSDKPCMainLandInterface::GameGetServerListEvent(EOneResEventState State, const FString& Url, const FString& ErrorCode, const FString& ErrorMsg)
{
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);
	FString eventKey = TEXT("gameGetServerListError");
	if (State == EOneResEventState::Begin)
	{
		eventKey = TEXT("gameGetServerListBegin");
	}
	else if (State == EOneResEventState::Success)
	{
		eventKey = TEXT("gameGetServerListSuccess");
	}

	jsonObject->SetStringField(TEXT("url"), Url);
	jsonObject->SetStringField(TEXT("errorCode"), ErrorCode);
	jsonObject->SetStringField(TEXT("errorMsg"), ErrorMsg);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);

	LOG_PRINTF_MAINLAND("key:%s, hints:%s", *eventKey, *ValueJson);
	PC_ONE_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*eventKey), TCHAR_TO_UTF8(*ValueJson), nullptr);
}

void OneEngineSDKPCMainLandInterface::TrackEvent(const FString& Name, const TMap<FString, FString>& Payload)
{
	TSharedPtr<FJsonObject> JsonObj = MakeShareable(new FJsonObject);
	for (const auto& Pair : Payload)
	{
		JsonObj->SetStringField(Pair.Key, Pair.Value);
	}
	FJsonObjectWrapper Wrapper;
	Wrapper.JsonObject = JsonObj;
	FString Result;
	Wrapper.JsonObjectToString(Result);

	LOG_PRINTF_MAINLAND("key:%s, hints:%s", *Name, *Result);
	PC_ONE_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*Name), TCHAR_TO_UTF8(*Result), nullptr);
}

void OneEngineSDKPCMainLandInterface::TrackEventEnterGameScene(const FString& SceneName, int32 Period, const TMap<FString, FString>& HintMap)
{
	LOG_FUNCTION_IO_MAINLAND;
	LOG_PRINTF_MAINLAND("SceneName = %s,  Period = %d, HintMap Num = %d", *SceneName, Period, HintMap.Num());
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	if (HintMap.Num() > 0)
	{
		for (auto& ele : HintMap)
		{
			jsonObject->SetStringField(ele.Key, ele.Value);
		}
	}
	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	LOG_PRINTF_MAINLAND("HintMap String = %s", *ValueJson);
	PC_ONE_SDK::OneSdk_TrackEventEnterGameScene(TCHAR_TO_UTF8(*SceneName), Period, TCHAR_TO_UTF8(*ValueJson));
}

void OneEngineSDKPCMainLandInterface::TrackEventExitGameScene()
{
	LOG_FUNCTION_IO_MAINLAND;
	PC_ONE_SDK::OneSdk_TrackEventExitGameScene("");
}

void OneEngineSDKPCMainLandInterface::TrackEventAddExtraDeviceInfo(const TMap<FString, FString>& ExtraDeviceInfo)
{
	LOG_FUNCTION_IO_MAINLAND;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	if (ExtraDeviceInfo.Num() > 0)
	{
		for (auto& ele : ExtraDeviceInfo)
		{
			jsonObject->SetStringField(ele.Key, ele.Value);
		}
	}
	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	LOG_PRINTF_MAINLAND("ExtraDeviceInfo = %s", *ValueJson);
	PC_ONE_SDK::OneSdk_TrackAddExtraDeviceInfo(TCHAR_TO_UTF8(*ValueJson));
}

void OneEngineSDKPCMainLandInterface::RedeemCouponCode(const FString& CouponCode, const FString& ServerId, const FString& RoleId, const FString& RoleLevel, const FString& VipLevel, const TMap<FString, FString>& ExtraInfo, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda)
{
	LOG_FUNCTION_IO_MAINLAND;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	jsonObject->SetStringField(TEXT("roleId"), RoleId);
	jsonObject->SetStringField(TEXT("roleLevel"), RoleLevel);
	jsonObject->SetStringField(TEXT("serverId"), ServerId);
	jsonObject->SetStringField(TEXT("vipLevel"), VipLevel);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	FString extraJson = TEXT("");
	if (ExtraInfo.Num() > 0)
	{
		TSharedPtr<FJsonObject> extraObject = MakeShareable(new FJsonObject);
		bool bHasData = true;
		for (auto& item : ExtraInfo)
		{
			if (!item.Key.IsEmpty())
			{
				extraObject->SetStringField(item.Key, item.Value);
				bHasData = true;
			}
		}
		if (bHasData)
		{
			FJsonObjectWrapper extraWraper;
			extraWraper.JsonObject = extraObject;

			extraWraper.JsonObjectToString(extraJson);
		}

	}
	LOG_PRINTF_MAINLAND("roleInfo = %s, couponCode = %s, extraJson = %s", *ValueJson, *CouponCode, *extraJson);


    PC_ONE_SDK::OneSdk_SetCurrentPlayerRoleInfo(TCHAR_TO_UTF8(*ValueJson));
    auto fn = [](bool success, int code, const wchar_t* msg, int contextId)
        {
            OneEngineSDKPCHelper::Get().OnGenericResultLambda(success, code, WCHAR_TO_TCHAR(msg), contextId);
        };
    int id = PC_ONE_SDK::RedeemCouponCode(TCHAR_TO_UTF8(*CouponCode), TCHAR_TO_UTF8(*extraJson), fn);
    OneEngineSDKPCHelper::Get().SetGenericResultLambda(id, OnFinishedLambda);
}

void OneEngineSDKPCMainLandInterface::FetchUserRoleInfoList(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const TArray<FOneURCRoleInfo>& RoleList)>& OnFinishedLambda, const FString& ServerId)
{
	LOG_FUNCTION_IO_MAINLAND;
	LOG_PRINTF_MAINLAND("ServerId = %s", *ServerId);
    int id = PC_ONE_SDK::OneSdk_GetExecContextId();
    auto fn = [](int code, const wchar_t* msg, const char* result, int contextId)
        {
            OneEngineSDKPCHelper::Get().OnGetRoleListResult(code, WCHAR_TO_TCHAR(msg), UTF8_TO_TCHAR(result), contextId);
        };
    PC_ONE_SDK::OneSdk_QueryRoleList(TCHAR_TO_UTF8(*ServerId), fn, id);
    OneEngineSDKPCHelper::Get().SetGetRoleListResultDelegate(id, OnFinishedLambda);
}

FString OneEngineSDKPCMainLandInterface::GetChannelId()
{
	LOG_FUNCTION_IO_MAINLAND;
	FString channelId = FString(UTF8_TO_TCHAR(PC_ONE_SDK::OneSdk_ChannelId()));
	LOG_PRINTF_MAINLAND("channelId = %s", *channelId);
	return channelId;
}

void OneEngineSDKPCMainLandInterface::Login()
{
	LOG_FUNCTION_IO_MAINLAND;
	auto fnLoginSuccess = [](const char* lpszUID, const char* lpszToken, int platform, int channelId)
	{
		FString userId = UTF8_TO_TCHAR(lpszUID);
		FString token = UTF8_TO_TCHAR(lpszToken);
		LOG_PRINTF_MAINLAND("login success userId: %s", *userId);
		OneEngineSDKPCHelper::Get().SetToken(token);
		OneEngineSDKPCHelper::Get().SetUserId(userId);
		FOneEngineSDKHelper::OnLoginResultDelegate(true, 0, TEXT("success"), userId);
	};

	auto fnLoginCancel = []()
	{
		LOG_PRINTF_MAINLAND("login cancel");
		FOneEngineSDKHelper::OnLoginResultDelegate(false, -10002, TEXT("user login cancel"), TEXT(""));
	};

	auto fnLoginFailed = [](int code, const char* message)
	{
		LOG_PRINTF_MAINLAND("login failed");
		FOneEngineSDKHelper::OnLoginResultDelegate(false, code, FString(UTF8_TO_TCHAR(message)), "");
	};


	HRESULT ret = PC_ONE_SDK::OneSdk_DoLoginWithFailedCallback(OneEngineSDKPCHelper::Get().GetHandle(), fnLoginSuccess, fnLoginFailed, fnLoginCancel);
	if (ret != S_OK)
	{
		FOneEngineSDKHelper::OnLoginResultDelegate(false, 10001, TEXT("exec login failed"), TEXT(""));
		LOG_PRINTF_MAINLAND("OneSdk_DoLoginWithFailedCallback error = %lu", ret);
	}
}

void OneEngineSDKPCMainLandInterface::Logout()
{
	LOG_FUNCTION_IO_MAINLAND;
	HRESULT ret = PC_ONE_SDK::OneSdk_Logout(OneEngineSDKPCHelper::Get().GetHandle());
	if (ret != S_OK)
	{
		FOneEngineSDKHelper::OnLogoutResultDelegate(false, 10001, TEXT("exec logout failed"));
		LOG_PRINTF_MAINLAND("OneSdk_Logout error = %lu", ret);
	}
	else
	{
		FOneEngineSDKHelper::OnLogoutResultDelegate(true, 0, TEXT("success"));
	}
	OneEngineSDKPCHelper::Get().ResetUserInfo();
}

FOneUserInfo OneEngineSDKPCMainLandInterface::GetUserInfo()
{
	LOG_FUNCTION_IO_MAINLAND;
	FOneUserInfo userInfo;
	userInfo.Token = OneEngineSDKPCHelper::Get().GetToken();
	userInfo.UserId = OneEngineSDKPCHelper::Get().GetUserId();
	userInfo.Phone = UTF8_TO_TCHAR(PC_ONE_SDK::OneSdk_UserPhoneNumber());
	return userInfo;
}

void OneEngineSDKPCMainLandInterface::Pay(const FOnePaymentInfo& PaymentInfo)
{
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);
	if (PC_ONE_SDK::OneSdk_PlatformType() == 1) 
	{
		jsonObject->SetStringField(TEXT("appOrder"), PaymentInfo.OrderId);
		jsonObject->SetStringField(TEXT("roleId"), PaymentInfo.RoleId);
		jsonObject->SetStringField(TEXT("roleName"), PaymentInfo.RoleName);
		jsonObject->SetNumberField(TEXT("serverId"), FCString::Atoi(*PaymentInfo.GameServerId));
		jsonObject->SetNumberField(TEXT("areaId"), FCString::Atoi(*PaymentInfo.ProductId));
		jsonObject->SetNumberField(TEXT("amount"), FCString::Atoi(*PaymentInfo.Price));
		jsonObject->SetStringField(TEXT("desc"), PaymentInfo.ProductName);
		jsonObject->SetStringField(TEXT("ext"), PaymentInfo.ExtInfo);
	}
	else 
	{
		jsonObject->SetStringField(TEXT("appOrder"), PaymentInfo.OrderId);
		jsonObject->SetStringField(TEXT("roleId"), PaymentInfo.RoleId);
		jsonObject->SetStringField(TEXT("roleName"), PaymentInfo.RoleName);
		jsonObject->SetNumberField(TEXT("serverId"), FCString::Atoi(*PaymentInfo.GameServerId));
		jsonObject->SetNumberField(TEXT("areaId"), FCString::Atoi(*PaymentInfo.ProductId));
		jsonObject->SetStringField(TEXT("goodId"), PaymentInfo.ProductId);
		jsonObject->SetNumberField(TEXT("goodNum"), FCString::Atoi(*PaymentInfo.ProductCount));
		jsonObject->SetStringField(TEXT("goodInfo"), PaymentInfo.ProductName);
		jsonObject->SetNumberField(TEXT("amount"), FCString::Atoi(*PaymentInfo.Price));
		jsonObject->SetStringField(TEXT("desc"), PaymentInfo.ProductName);
		jsonObject->SetStringField(TEXT("ext"), PaymentInfo.ExtInfo);
	}

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);

	LOG_PRINTF_MAINLAND("pay: %s", *ValueJson);

	OneEngineSDKPCHelper::Get().SetOrderId(PaymentInfo.OrderId);
	auto fn = [](bool success, int code, const char* msg)
	{
		FString message = UTF8_TO_TCHAR(msg);
		if (code == -3)
		{
			code = -10002;
			message = TEXT("user cancel\terrorCode=-10002");
		}
		FOneEngineSDKHelper::OnPaymetResultDelegate(success, code, message, OneEngineSDKPCHelper::Get().GetOrderId());
	};
	HRESULT ret = PC_ONE_SDK::OneSdk_DoPayWithDetailCallback(OneEngineSDKPCHelper::Get().GetHandle(), TCHAR_TO_UTF8(*ValueJson), fn);
	if (ret != S_OK)
	{
		LOG_PRINTF_MAINLAND("OneSdk_DoPayEx error = %lu ", ret);
		FOneEngineSDKHelper::OnPaymetResultDelegate(false, 10001, TEXT("exec failed"), OneEngineSDKPCHelper::Get().GetOrderId());
	}
}

void OneEngineSDKPCMainLandInterface::TrackEventAD(const FString& Name, const TMap<FString, FString>& Payload)
{
	LOG_FUNCTION_IO_MAINLAND;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	if (Payload.Num() > 0)
	{
		for (auto& ele : Payload)
		{
			jsonObject->SetStringField(ele.Key, ele.Value);
		}
	}
	jsonObject->SetStringField(TEXT("userId"), OneEngineSDKPCHelper::Get().GetUserId());
	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	LOG_PRINTF_MAINLAND("Name: %s, Payload %s", *Name, *ValueJson);

	PC_ONE_SDK::OneSdk_wanmeiTrackEvent(TCHAR_TO_UTF8(*Name), TCHAR_TO_UTF8(*ValueJson), nullptr);
}

bool OneEngineSDKPCMainLandInterface::IsLoggedIn()
{
	bool isLogin = PC_ONE_SDK::OneSdk_PlayerLogined();

	LOG_PRINTF_MAINLAND("isLogin: %d", isLogin ? 1 : 0);
	return isLogin;
}

void OneEngineSDKPCMainLandInterface::OpenUserCenter()
{
	LOG_FUNCTION_IO_MAINLAND;
	PC_ONE_SDK::OneSdk_EnterUserCenter();
}

FString OneEngineSDKPCMainLandInterface::GetChannelMediaId()
{
	FString mediaId = FString(UTF8_TO_TCHAR(PC_ONE_SDK::OneSdk_ChannelId())) + "_" + FString(UTF8_TO_TCHAR(PC_ONE_SDK::OneSdk_SubChannelId()));
	LOG_PRINTF_MAINLAND("mediaId: %s", *mediaId);
	return mediaId;
}

void OneEngineSDKPCMainLandInterface::DisplayCDKeyDialog(UOneEngineSDKSubsystem::FOneGenericResultDelegate OnCDKeyActivateResult, const FString& ServerId)
{
	LOG_PRINTF_MAINLAND("ServerId: %s", *ServerId);
	FString strServerId = OneEngineSDKPCHelper::Get().ConverServerId(ServerId);

	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	jsonObject->SetStringField(TEXT("serverId"), strServerId);
	jsonObject->SetStringField(TEXT("uid"), OneEngineSDKPCHelper::Get().GetUserId());
	jsonObject->SetStringField(TEXT("token"), OneEngineSDKPCHelper::Get().GetToken());

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);
	PC_ONE_SDK::OneSdk_SetCurrentPlayerRoleInfo(TCHAR_TO_UTF8(*ValueJson));

	auto fn = [](bool success, int code, const wchar_t* msg, int contextId)
	{
			int errorCode = code;
			if (code == -1001)
			{
				errorCode = -10002;
			}
		OneEngineSDKPCHelper::Get().OnGenericResult(success, errorCode, WCHAR_TO_TCHAR(msg), contextId);
	};

	int id = PC_ONE_SDK::DisplayCDKeyDialog(fn);
	OneEngineSDKPCHelper::Get().SetGenericResultDelegate(id, OnCDKeyActivateResult);
}

void OneEngineSDKPCMainLandInterface::QueryActCode(const FString& ServerId, const TFunction<void(bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg)>& OnFinishedLambda)
{
	LOG_PRINTF_MAINLAND("ServerId : %s", *ServerId);
	FString strServerId = OneEngineSDKPCHelper::Get().ConverServerId(ServerId);
	auto fn = [](bool success, int code, const wchar_t* msg, int contextId)
	{
		OneEngineSDKPCHelper::Get().OnMainLandQueryActCodeResult(success, code, WCHAR_TO_TCHAR(msg), contextId);
	};

	int id = PC_ONE_SDK::QueryCDKeyCodeStatus(TCHAR_TO_UTF8(*strServerId), fn);
	OneEngineSDKPCHelper::Get().SetMainLandQueryActCodeResultDelegate(id, OnFinishedLambda);
}

void OneEngineSDKPCMainLandInterface::ExchangeActCode(const FString& ServerId, const FString& ActCode, const TFunction<void(bool bSuccess, int32 Code, const FString& Msg)>& OnFinishedLambda)
{
	LOG_PRINTF_MAINLAND("ServerId = %s, ActCode = %s", *ServerId, *ActCode);

	FString strServerId = OneEngineSDKPCHelper::Get().ConverServerId(ServerId);

	auto fn = [](bool success, int code, const wchar_t* msg, int contextId)
	{
		OneEngineSDKPCHelper::Get().OnGenericResultLambda(success, code, WCHAR_TO_TCHAR(msg), contextId);
	};

	int id = PC_ONE_SDK::ExchangeCDKeyCode(TCHAR_TO_UTF8(*ActCode), TCHAR_TO_UTF8(*strServerId), fn);
	OneEngineSDKPCHelper::Get().SetGenericResultLambda(id, OnFinishedLambda);
}

void OneEngineSDKPCMainLandInterface::Bind(UOneEngineSDKSubsystem::FOneBindResultDelegate BindDelegate, EOneEngineThirdType BindType)
{
	LOG_PRINTF_MAINLAND("BindType: %d", BindType);
	if (!PC_ONE_SDK::OneSdk_PlayerLogined())
	{
		bool success = false;
		int code = 10001;
		FString message = TEXT("need login first");
		FOneUserInfo info;
		AsyncTask(ENamedThreads::GameThread, [BindDelegate, success, code, message, BindType, info]()
			{
				BindDelegate.ExecuteIfBound(success, code, message, BindType, info);
			});
		return;
	}
	if (BindType == EOneEngineThirdType::WMPass)
	{
		FString strWMPassport = UTF8_TO_TCHAR(PC_ONE_SDK::OneSdk_GetBindWMPassport());
		if (strWMPassport.IsEmpty())
		{
			auto fn = [](int code, const char* message, const char* passport)
			{
				OneEngineSDKPCHelper::Get().OnBindWMPassportResult(code, UTF8_TO_TCHAR(message), UTF8_TO_TCHAR(passport));
			};
			PC_ONE_SDK::OneSdk_OpenBindWMPassportViewWithCallback(fn);
			OneEngineSDKPCHelper::Get().SetBindResultDelegateAndBindType(BindDelegate, BindType);
		}
		else
		{
			bool success = true;
			int code = 0;
			FString message = TEXT("success");
			FOneUserInfo info = GetUserInfo();
			AsyncTask(ENamedThreads::GameThread, [BindDelegate, success, code, message, BindType, info]()
			{
					BindDelegate.ExecuteIfBound(success, code, message, BindType, info);
			});
		}

	}
	else if (BindType == EOneEngineThirdType::Phone)
	{
		FString strPhoneNumber = UTF8_TO_TCHAR(PC_ONE_SDK::OneSdk_UserPhoneNumber());
		if (strPhoneNumber.IsEmpty())
		{
			auto fn = [](int code, const char* message, const char* phoneNumber)
			{
					OneEngineSDKPCHelper::Get().OnBindPhoneResult(code, UTF8_TO_TCHAR(message),UTF8_TO_TCHAR(phoneNumber));
			};
			PC_ONE_SDK::OneSdk_BindPhoneWithDetailCallback(fn);
			OneEngineSDKPCHelper::Get().SetBindResultDelegateAndBindType(BindDelegate, BindType);
		}
		else
		{
			bool success = true;
			int code = 0;
			FString message = TEXT("success");
			FOneUserInfo info = GetUserInfo();
			AsyncTask(ENamedThreads::GameThread, [BindDelegate, success, code, message, BindType, info]()
			{
					BindDelegate.ExecuteIfBound(success, code, message, BindType, info);
			});
		}
	}
	else
	{
		bool success = false;
		int code = 10001;
		FString message = TEXT("not support bind type");
		FOneUserInfo info;
		AsyncTask(ENamedThreads::GameThread, [BindDelegate, success, code, message, BindType, info]()
			{
				BindDelegate.ExecuteIfBound(success, code, message, BindType, info);
			});
		return;

	}
}

void OneEngineSDKPCMainLandInterface::GetUserLocationInfo(const TFunction<void(const FOneUserLocationInfo& LocationInfo)>& OnFinishedLambda)
{
	FOneUserLocationInfo userLocation;
	userLocation.IP = FString(UTF8_TO_TCHAR(PC_ONE_SDK::OneSdk_UserIp()));
	LOG_PRINTF_MAINLAND("IP = %s", *userLocation.IP);
	AsyncTask(ENamedThreads::GameThread, [OnFinishedLambda, userLocation]()
		{
			if (OnFinishedLambda)
			{
				OnFinishedLambda(userLocation);
			}
		});
}

void OneEngineSDKPCMainLandInterface::UserAuthentication(UOneEngineSDKSubsystem::FOneUserAuthenticationResultDelegate OnUserAuthenticationResultDelegate)
{
	LOG_FUNCTION_IO_MAINLAND;
	auto fn = [](bool bSuccess, const char* message, int authResult, bool hasNetError, int contextId)
	{
		OneEngineSDKPCHelper::Get().OnUserAuthenticationResult(bSuccess, authResult, hasNetError, UTF8_TO_TCHAR(message), contextId);
	};
	int contextId = PC_ONE_SDK::OneSdk_LaohuUserAuthentication(fn);
	OneEngineSDKPCHelper::Get().SetUserAuthenticationResultDelegate(contextId, OnUserAuthenticationResultDelegate);
}

void OneEngineSDKPCMainLandInterface::SetShowDefaultActivationResultToast(bool bShow)
{
	LOG_PRINTF_MAINLAND("bShow : %d", bShow ? 1 : 0);
	PC_ONE_SDK::SetShowDefaultActivationResultToast(bShow);
}

int32 OneEngineSDKPCMainLandInterface::GetPlatformOS()
{
	int platFormOS = PC_ONE_SDK::OneSdk_GetPlatformOS();
	LOG_PRINTF_MAINLAND("platformOS : %d", platFormOS);
	return platFormOS;
}

void OneEngineSDKPCMainLandInterface::GetDeviceInfo(const TFunction<void(const FOneDeviceInfo& DeviceInfo)>& OnFinishedLambda)
{
	LOG_FUNCTION_IO_MAINLAND;
	auto fn = [](int code, const char* message, const char* data, int contextId)
		{
			FString DeviceId = UTF8_TO_TCHAR(PC_ONE_SDK::OneSdk_GetDeviceUuid());
			FString DeviceSys = UTF8_TO_TCHAR(PC_ONE_SDK::OneSdk_GetDeviceSys());
			OneEngineSDKPCHelper::Get().OnGetDeviceInfo(code, UTF8_TO_TCHAR(message), UTF8_TO_TCHAR(data), contextId, DeviceId, DeviceSys);
		};
	int contextId = PC_ONE_SDK::OneSdk_GetDeviceInfo(fn);
	OneEngineSDKPCHelper::Get().SetGetDeviceInfoDelegate(contextId, OnFinishedLambda);
}

void OneEngineSDKPCMainLandInterface::GetChannelPlatformLambda(const TFunction<void(bool bSucceed, int32 Code, const FString& Msg, const  FString& Platform)>& OnFinishedLambda)
{
	FString channelPlatform = FString(WCHAR_TO_TCHAR(PC_ONE_SDK::OneSdk_ChannelName()));
	LOG_PRINTF_MAINLAND("channelPlatform : %s", *channelPlatform);
	AsyncTask(ENamedThreads::GameThread, [OnFinishedLambda, channelPlatform]()
	{
		if (OnFinishedLambda)
		{
			OnFinishedLambda(true, 0, TEXT("success"), channelPlatform);
		}
	});
}

void OneEngineSDKPCMainLandInterface::QueryUserActiveQualificationLambda(const FString& ServerId, const TFunction<void(bool bSucceed, int32 Code, const FString& ErrorMsg, const FOneActiveQualificationInfo& QualificationInfo)>& OnFinishedLambda)
{
	LOG_PRINTF_MAINLAND("ServerId = %s", *ServerId);
	auto callback = OnFinishedLambda;
	FOneActiveQualificationInfo info;
	info.DeviceLogged = 0;
	info.DeviceTotal = 0;
	info.Status = 0;
	info.WhiteList = 0;
	if (!PC_ONE_SDK::OneSdk_PlayerLogined())
	{
		bool success = false;
		int code = 10001;
		FString message = TEXT("need login first");
		AsyncTask(ENamedThreads::GameThread, [callback, success, code, message, info]()
			{
				if (callback)
				{
					callback(success, code, message, info);
				}

			});
		return;
	}
	FString strServerId = OneEngineSDKPCHelper::Get().ConverServerId(ServerId);
	auto fn = [](int code, const wchar_t* message, const wchar_t* result, int contextId)
	{
		OneEngineSDKPCHelper::Get().OnPreQueryCDKeyStatus(code, WCHAR_TO_TCHAR(message), WCHAR_TO_TCHAR(result), contextId);
	};

	int contextId = PC_ONE_SDK::PreQueryCDKeyCodeStatus(TCHAR_TO_UTF8(*strServerId), fn);
	OneEngineSDKPCHelper::Get().SetPreQueryCDKeyStatusDelegate(contextId, OnFinishedLambda);
}

void OneEngineSDKPCMainLandInterface::OpenComplianceOnWebView()
{
	LOG_FUNCTION_IO_MAINLAND;
	PC_ONE_SDK::OneSdk_OpenComplianceOnWebView();
}

void OneEngineSDKPCMainLandInterface::FetchAntiAddictionInfo(UOneEngineSDKSubsystem::FOneFetchAntiAddictionInfoDelegate OnFetchAntiAddictionInfo)
{
	LOG_FUNCTION_IO_MAINLAND;
	auto fn = [](int code, const char* result, int contextId)
	{
		OneEngineSDKPCHelper::Get().OnFOneFetchAntiAddictionInfo(code, UTF8_TO_TCHAR(result), contextId);
	};
	int id = PC_ONE_SDK::WMAntifatigueSdk_QueryAntifatiguePreLogin(fn);
	OneEngineSDKPCHelper::Get().SetFetchAntiAddictionInfoResultDelegate(id, OnFetchAntiAddictionInfo);
}

void OneEngineSDKPCMainLandInterface::StartAntiAddictionNotify(const FString& ServerId, const FString& RoleId)
{
	LOG_FUNCTION_IO_MAINLAND;
	TSharedPtr<FJsonObject> jsonObject = MakeShareable(new FJsonObject);

	jsonObject->SetStringField(TEXT("serverId"), ServerId);
	jsonObject->SetStringField(TEXT("uid"), OneEngineSDKPCHelper::Get().GetUserId());
	jsonObject->SetStringField(TEXT("roleId"), RoleId);

	FJsonObjectWrapper Wraper;
	Wraper.JsonObject = jsonObject;
	FString ValueJson;
	Wraper.JsonObjectToString(ValueJson);

	LOG_PRINTF_MAINLAND("ValueJson : %s", *ValueJson);

	auto fn = [](bool bForceKick, const char* antiAddictionInfo)
	{
		OneEngineSDKPCHelper::Get().OnAntiAddictionTimeout(bForceKick, UTF8_TO_TCHAR(antiAddictionInfo));
	};

	PC_ONE_SDK::OneSdk_SetCurrentPlayerRoleInfo(TCHAR_TO_UTF8(*ValueJson));
	PC_ONE_SDK::WMAntifatigueSdk_SetAntiAddictionTimeoutCallback(fn);
	PC_ONE_SDK::WMAntifatigueSdk_StartAntiAddictionNotify();
}

void OneEngineSDKPCMainLandInterface::StopAntiAddictionNotify()
{
	LOG_FUNCTION_IO_MAINLAND;
	PC_ONE_SDK::WMAntifatigueSdk_StopAntiAddictionNotify();
	PC_ONE_SDK::WMAntifatigueSdk_SetAntiAddictionTimeoutCallback(nullptr);
}

void OneEngineSDKPCMainLandInterface::EnterAccountCancellation()
{
	LOG_FUNCTION_IO_MAINLAND;
	PC_ONE_SDK::OneSdk_OpenCloseAccountView();
}

bool OneEngineSDKPCMainLandInterface::ACELogin(const FString& accountId, int accountTypeCommandId, int worldId, UOneEngineSDKSubsystem::FOneOnGetClientPacket delegate)
{
	LOG_PRINTF_MAINLAND("accountId: %s, accountTypeCommandId: %d, worldId: %d", *accountId, accountTypeCommandId, worldId);
	int ret = PC_ONE_SDK::OneSdk_LoginACE(TCHAR_TO_UTF8(*accountId), accountTypeCommandId, worldId);
	if (ret == 0)
	{
		auto fn = [](const unsigned char* data, int len)
			{
				OneEngineSDKPCHelper::Get().OnGetACEClientPacket(data, len);
			};
		PC_ONE_SDK::OneSdk_SetACEClientPacketListener(fn);
		OneEngineSDKPCHelper::Get().SetACEClientPacketDelegate(delegate);
	}
	return ret == 0;
}

bool OneEngineSDKPCMainLandInterface::ACEClientPacketReceive(const TArray<uint8>& data)
{
	LOG_PRINTF_MAINLAND("data : %d", data.Num());
	if (data.Num() > 0)
	{
		int ret = PC_ONE_SDK::OneSdk_OnACEClientPacketReceive(data.GetData(), data.Num());
		return ret == 0;
	}
	return false;
}

bool OneEngineSDKPCMainLandInterface::ACELogout()
{
	LOG_FUNCTION_IO_MAINLAND;
	int ret = PC_ONE_SDK::OneSdk_LogoutACE();
	return ret == 0;
}
void OneEngineSDKPCMainLandInterface::GetIpInfo(const FString& Ip, UOneEngineSDKSubsystem::FOnGetIpInfoResultDelegate Delegate)
{
	LOG_PRINTF_MAINLAND("IP = %s", *Ip);
	if (Ip.IsEmpty())
	{
		auto callBack = Delegate;
		AsyncTask(ENamedThreads::GameThread, [callBack]()
			{
				FUserIpInfo info;
				callBack.ExecuteIfBound(false, info, 50003, TEXT("ip is empty!"));
			});
		return;
	}
	auto fnContextId = [](int code, const wchar_t* message, const wchar_t* result, int contextId)
		{
			FString strResult = WCHAR_TO_TCHAR(result);
			FString strMessage = WCHAR_TO_TCHAR(message);
			OneEngineSDKPCHelper::Get().OnGetIpInfo(code, strMessage, strResult, contextId);
		};
	int contextId = PC_ONE_SDK::OneSdk_GetIPLocationInfoWithContextId(TCHAR_TO_UTF8(*Ip), fnContextId);
	OneEngineSDKPCHelper::Get().SetGetIpInfoResultDelegate(Delegate, contextId);
	LOG_PRINTF_MAINLAND("contextId:%d", contextId);
}

FString OneEngineSDKPCMainLandInterface::GetCurrentGameAppId()
{
	FString appId = WCHAR_TO_TCHAR(PC_ONE_SDK::OneSdk_GetCurrentGameAppId());
	LOG_PRINTF_MAINLAND("CurrentGameAppId : %s", *appId);
	return appId;
}

void OneEngineSDKPCMainLandInterface::UnlockSafeLockUsingPushNotification(const FString& RoleName, const FString& ServerName, UOneEngineSDKSubsystem::FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
	LOG_PRINTF_MAINLAND("RoleName : %s, ServerName : %s", *RoleName, *ServerName);

	FString msg = "";
	int checkCode = 0;
	if (RoleName.IsEmpty())
	{
		checkCode = 1;
		msg = TEXT("RoleName is empty !");
	}
	else if (ServerName.IsEmpty())
	{
		checkCode = 2;
		msg = TEXT("ServerName is empty !");
	}

	if (checkCode != 0)
	{
		AsyncTask(ENamedThreads::GameThread, [OnUnlockSafeLockResultDelegate, checkCode, msg]()
			{
				OnUnlockSafeLockResultDelegate.ExecuteIfBound(EOneUnlockSafeLockResult::UnlockFailed, "", checkCode, msg, EOneUnlockSafeLockType::PushNotification);
			});
		return;
	}

	auto fn = [](int code, int status, const char* message, const char* unlockTicket, int contextId)
	{
		EOneUnlockSafeLockResult result;
		if (code == 0 && status == 1)
		{
			result = EOneUnlockSafeLockResult::WaitingToUnlock;
		}
		else if (code == 0 && status == 2)
		{
			result = EOneUnlockSafeLockResult::UnlockSucceeded;
		}
		else
		{
			if (status == 3)
			{
				result = EOneUnlockSafeLockResult::UnlockTimedOut;
			}
			else
			{
				result = EOneUnlockSafeLockResult::UnlockFailed;
			}
		}
		OneEngineSDKPCHelper::Get().OnSafeLockResult(result, code, UTF8_TO_TCHAR(message), UTF8_TO_TCHAR(unlockTicket), EOneUnlockSafeLockType::PushNotification, contextId);
	};
	int contextId = PC_ONE_SDK::OneSdk_SecurityLockOneKeyUnlock(TCHAR_TO_UTF8(*RoleName), TCHAR_TO_UTF8(*ServerName), fn);
	OneEngineSDKPCHelper::Get().SetSafeLockResultDelegate(contextId, OnUnlockSafeLockResultDelegate);
}
void OneEngineSDKPCMainLandInterface::UnlockSafeLockUsingDynamicCode(const FString& DynamicCode, const FString& RoleName, const FString& ServerName, UOneEngineSDKSubsystem::FOneUnlockSafeLockResultDelegate OnUnlockSafeLockResultDelegate)
{
	LOG_PRINTF_MAINLAND("RoleName : %s, ServerName : %s", *RoleName, *ServerName);

	FString msg = "";
	int checkCode = 0;
	if (DynamicCode.IsEmpty())
	{
		checkCode = 1;
		msg = TEXT("DynamicCode is empty !");
	}
	else if (RoleName.IsEmpty())
	{
		checkCode = 2;
		msg = TEXT("RoleName is empty !");
	}
	else if (ServerName.IsEmpty())
	{
		checkCode = 3;
		msg = TEXT("ServerName is empty !");
	}

	if (checkCode != 0)
	{
		AsyncTask(ENamedThreads::GameThread, [OnUnlockSafeLockResultDelegate, checkCode, msg]()
			{
				OnUnlockSafeLockResultDelegate.ExecuteIfBound(EOneUnlockSafeLockResult::UnlockFailed, "", checkCode, msg, EOneUnlockSafeLockType::DynamicCode);
			});
		return;
	}

	auto fn = [](int code, const char* message, const char* unlockTicket, int contextId)
		{
			EOneUnlockSafeLockResult result;
			if (code == 0)
			{
				result = EOneUnlockSafeLockResult::UnlockSucceeded;
			}
			else
			{
				result = EOneUnlockSafeLockResult::UnlockFailed;
			}
			OneEngineSDKPCHelper::Get().OnSafeLockResult(result, code, UTF8_TO_TCHAR(message), UTF8_TO_TCHAR(unlockTicket), EOneUnlockSafeLockType::DynamicCode, contextId);
		};
	int contextId = PC_ONE_SDK::OneSdk_SecurityLockDynamicCodeUnlock(TCHAR_TO_UTF8(*RoleName), TCHAR_TO_UTF8(*ServerName), TCHAR_TO_UTF8(*DynamicCode), fn);
	OneEngineSDKPCHelper::Get().SetSafeLockResultDelegate(contextId, OnUnlockSafeLockResultDelegate);
}
void OneEngineSDKPCMainLandInterface::StopUnlockSafeLockUsingPushNotification()
{
	LOG_PRINTF_MAINLAND("StopUnlockSafeLockUsingPushNotification");
	PC_ONE_SDK::OneSdk_SecurityLockStopOneKeyUnlock();
}
#endif
