﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "Async/Async.h"
#include "Components/TextBlock.h"
#include "PSOneChangNick.h"
#include "PSOneConfirmButton.h"
#include "PSOneDelAccount.h"
#include "PSOneInputCode.h"
#include "PSOneInputPhoneNum.h"
#include "PSOneLoginSuccessToast.h"
#include "PSOneSelectAreaCode.h"
#include "PSOneShowIphoneStatus.h"
#include "PSOneUIManager.h"
#include "PSOneUserCenter.h"
#include "PSOneUserCenterAccountInfo.h"
#include "PSOneUserCenterBindManager.h"
#include "PSOneUserCenterChangePwd.h"
#include "PSOneUserCenterDeviceManager.h"
#include "PSOneUserCenterLegalTerms.h"
#include "PSOneUserCenterOther.h"
#include "Serialization/JsonReader.h"
#include "Serialization/JsonSerializer.h"

#define LOCTEXT_NAMESPACE "PSOneUIManagerUserCenter"

namespace
{
// WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_UserCenter.WBP_ONE_UserCenter'
FString UserCenterPath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_UserCenter.WBP_ONE_UserCenter_C'");
// WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_ChangeNick.WBP_ONE_ChangeNick'
FString ChangeNickPath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_ChangeNick.WBP_ONE_ChangeNick_C'");
// WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_ShowIphoneStatus.WBP_ONE_ShowIphoneStatus'
FString ShowPhoneStatusPath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_ShowIphoneStatus.WBP_ONE_ShowIphoneStatus_C'");
// WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_InputPhoneNum.WBP_ONE_InputPhoneNum'
FString InputPhoneNumPath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_InputPhoneNum.WBP_ONE_InputPhoneNum_C'");
// WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_SelectAreaCode.WBP_ONE_SelectAreaCode'
FString SelectAreaCodePath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_SelectAreaCode.WBP_ONE_SelectAreaCode_C'");
// WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_InputCode.WBP_ONE_InputCode'
FString InputCodePath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_InputCode.WBP_ONE_InputCode_C'");
// WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_ChangePWD.WBP_ONE_ChangePwd'
FString ChangePwdPath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_ChangePwd.WBP_ONE_ChangePwd_C'");
// WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_DelAccount.WBP_ONE_DelAccount'
FString DelAccountPath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_DelAccount.WBP_ONE_DelAccount_C'");
}	 // namespace

void UPSOneUIManager::ShowUserCenter()
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Attempting to show UserCenter UI..."));
	check(IsInGameThread());

	constexpr int32 UserCenterWidgetIndex = static_cast<int32>(EUIWidgetType::UserCenter);

	if (UIWidgetInstanceMap.Contains(UserCenterWidgetIndex))
	{
		const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[UserCenterWidgetIndex];
		if (UserWidget.IsValid())
		{
			UE_LOG(LogPSOneUIManager, Display, TEXT("UserCenter already exists and is valid. Binding data and returning."));
			UPSOneUserCenter* UserCenter = Cast<UPSOneUserCenter>(UserWidget.Get());
			if (UserCenter)
			{
				UserCenter->BindData(UserInfoJson);
			}
			else
			{
				UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to cast existing UserWidget to UPSOneUserCenter."));
			}
			return;
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("UserCenter found in map but pointer is invalid. Removing entry."));
			UIWidgetInstanceMap.Remove(UserCenterWidgetIndex);
		}
	}

	UE_LOG(LogPSOneUIManager, Display, TEXT("Loading UserCenter widget class from path: %s"), *UserCenterPath);
	const auto WidgetClass = LoadClass<UPSOneUserCenter>(nullptr, *UserCenterPath);
	if (WidgetClass == nullptr)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to load UserCenter widget class! Path: %s"), *UserCenterPath);
		return;
	}

	UE_LOG(LogPSOneUIManager, Display, TEXT("Creating and initializing a new UserCenter widget instance."));
	PushFocusedWidget();
	UPSOneUserCenter* UserCenter = CreateWidget<UPSOneUserCenter>(GetCurrentWorld(), WidgetClass);

	if (!UserCenter)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to create UserCenter widget instance after loading class."));
		PopFocusedWidget();
		return;
	}
	UserCenter->SetIsMainLand(bIsMainlandEnv);

	UserCenter->BindData(UserInfoJson);
	UserCenter->RefreshDeviceData(DevicesJson);
	if (!bIsMainlandEnv)
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Non-mainland environment detected. Updating bound accounts display."));
		UserCenter->UpdateBoundAccounts(BoundAccounts);
	}

	UserCenter->OnNativeVisibilityChanged.AddLambda(
		[WeakUserCenter = TWeakObjectPtr<UPSOneUserCenter>(UserCenter)](ESlateVisibility InVisibility)
		{
			if (InVisibility == ESlateVisibility::Visible)
			{
				UE_LOG(LogPSOneUIManager, Display, TEXT("UserCenter became visible. Refreshing data..."));
				if (WeakUserCenter.IsValid())
				{
					WeakUserCenter->RefreshData();
				}
				else
				{
					UE_LOG(LogPSOneUIManager, Warning,
						TEXT("UserCenter became visible, but WeakUserCenter is invalid. Cannot refresh data."));
				}
			}
		});

	UserCenter->BindAccountManagerWidget->OnBindItemCallback =
		[this, WeakUserCenter = TWeakObjectPtr<UPSOneUserCenter>(UserCenter)](const FPSOneBindItem& BindItem)
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("BindAccountManagerWidget item clicked. ID: %d, IsBind: %s"), BindItem.Id,
			BindItem.bBind ? TEXT("true") : TEXT("false"));

		if (!WeakUserCenter.IsValid())
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("Bind item callback triggered, but UserCenter is invalid."));
			return;
		}
		const FPSOneUserCenterModel& Model = WeakUserCenter->GetModel();

		if (BindItem.Id == 8)
		{
			FBindingInfo EmailBindInfo;
			EmailBindInfo.bIsEmail = true;
			EmailBindInfo.bAllowChangeBinding = true;
			if (BindItem.bBind)
			{
				UE_LOG(LogPSOneUIManager, Display, TEXT("Processing email unbind request."));
				EmailBindInfo.CurrentBinding = Model.Email;
				EmailBindInfo.FormattedBinding = Model.ShowEmail;
				EmailBindInfo.PreviousBinding = Model.Email;
				EmailBindInfo.FormattedPreviousBinding = Model.ShowEmail;
				ShowBindingStatus(WeakUserCenter.Get(), EmailBindInfo);
			}
			else
			{
				UE_LOG(LogPSOneUIManager, Display, TEXT("Processing email bind request."));
				EmailBindInfo.bIsFirstBinding = true;
				ShowBinding(WeakUserCenter.Get(), EmailBindInfo);
			}
		}
		else if (BindItem.Id == 23)
		{
			UE_LOG(LogPSOneUIManager, Display, TEXT("Processing PSN unbind request."));
			bool bAllowUnbindPSN = false;
			if (OnCheckUnbindPSN)
			{
				bAllowUnbindPSN = OnCheckUnbindPSN();
				UE_LOG(LogPSOneUIManager, Display, TEXT("OnCheckUnbindPSN callback returned: %s"),
					bAllowUnbindPSN ? TEXT("true") : TEXT("false"));
			}
			else
			{
				UE_LOG(LogPSOneUIManager, Warning, TEXT("OnCheckUnbindPSN callback is not set. Cannot check unbind permission."));
				return;
			}

			if (!bAllowUnbindPSN)
			{
				UE_LOG(LogPSOneUIManager, Display, TEXT("Unbinding PSN is not allowed by OnCheckUnbindPSN result."));
				return;
			}

			const auto WidgetClass = LoadClass<UPSOneShowIphoneStatus>(nullptr, *ShowPhoneStatusPath);
			if (!WidgetClass)
			{
				UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to load ShowPhoneStatus widget class for PSN unbind! Path: %s"),
					*ShowPhoneStatusPath);
				return;
			}

			UPSOneShowIphoneStatus* ShowBindAccountStatusWidget =
				CreateWidget<UPSOneShowIphoneStatus>(GetCurrentWorld(), WidgetClass);

			if (!ShowBindAccountStatusWidget)
			{
				UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to create ShowPhoneStatus widget instance for PSN unbind."));
				return;
			}

			TWeakObjectPtr<UPSOneShowIphoneStatus> WeakShowBindAccountStatusWidget =
				TWeakObjectPtr<UPSOneShowIphoneStatus>(ShowBindAccountStatusWidget);

			// autocorrect-disable
			ShowBindAccountStatusWidget->TitleTextBlock->SetText(LOCTEXT("UnbindPSNTitle", "解绑 PlayStation™Network 账号"));
			ShowBindAccountStatusWidget->BindDescTextBlock->SetText(
				LOCTEXT("UnbindPSNDescription", "当前账号已绑定 PlayStation™Network 账号，您可以使用此账号进行登录。确认解绑吗？"));
			// autocorrect-enable
			ShowBindAccountStatusWidget->SetBindText(TEXT(""));
			ShowBindAccountStatusWidget->HideUnbind = true;
			ShowBindAccountStatusWidget->ActionButton->Title = LOCTEXT("ConfirmUnlinkButton", "确认解绑");

			ShowBindAccountStatusWidget->OnBackspace = [this, WeakShowBindAccountStatusWidget]()
			{
				UE_LOG(LogPSOneUIManager, Display, TEXT("PSN unbind confirmation - Backspace/Cancel pressed."));
				AsyncTask(ENamedThreads::GameThread,
					[this, WeakShowBindAccountStatusWidget]()
					{
						RemoveWidgetFromViewport(WeakShowBindAccountStatusWidget);
						PopHiddenWidgetsUntilUserCenter();
					});
			};

			ShowBindAccountStatusWidget->OnClicked = [this, WeakShowBindAccountStatusWidget]()
			{
				UE_LOG(LogPSOneUIManager, Display,
					TEXT("PSN unbind confirmation - Confirm button clicked. Initiating unbind process."));
				if (OnUnbindPSN)
				{
					// 二次确认
					if (OnShowConfirmDialog)
					{
						FText localText =LOCTEXT("UnbindPSNTitle", "解绑 PlayStation™Network 账号");
						FString message = localText.ToString();
						OnShowConfirmDialog(message, [this, WeakShowBindAccountStatusWidget](bool bOk)
						{
							if (bOk)
							{
								OnUnbindPSN(
									[this, WeakShowBindAccountStatusWidget](bool bSuccess)
								{
									if (!bSuccess)
									{
										UE_LOG(LogPSOneUIManager, Error, TEXT("OnUnbindPSN callback reported failure."));
										return;
									}

									UE_LOG(LogPSOneUIManager, Display, TEXT("PSN unbind successful. Hiding UserCenter and logging out."));
									AsyncTask(ENamedThreads::GameThread,
										[this, WeakShowBindAccountStatusWidget]()
									{
										RemoveWidgetFromViewport(WeakShowBindAccountStatusWidget);
										HideUserCenter();
									});
								});
							}
						});
					}
					else {
						OnUnbindPSN(
							[this, WeakShowBindAccountStatusWidget](bool bSuccess)
						{
							if (!bSuccess)
							{
								UE_LOG(LogPSOneUIManager, Error, TEXT("OnUnbindPSN callback reported failure."));
								return;
							}

							UE_LOG(LogPSOneUIManager, Display, TEXT("PSN unbind successful. Hiding UserCenter and logging out."));
							AsyncTask(ENamedThreads::GameThread,
								[this, WeakShowBindAccountStatusWidget]()
								{
									RemoveWidgetFromViewport(WeakShowBindAccountStatusWidget);
									HideUserCenter();
								});
						});
					}


				}
				else
				{
					UE_LOG(LogPSOneUIManager, Warning, TEXT("OnUnbindPSN callback is not set. Cannot initiate unbind process."));
				}
			};

			AddWidgetToViewport(ShowBindAccountStatusWidget);
			ShowBindAccountStatusWidget->SetKeyboardFocus();
			PushHiddenWidget(WeakUserCenter.Get());
			UE_LOG(LogPSOneUIManager, Display, TEXT("Showing PSN unbind confirmation UI. UserCenter is hidden."));
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("Unhandled BindItem ID in OnBindItemCallback: %d"), BindItem.Id);
		}
	};

	// 监听账号信息点击事件
	UserCenter->AccountInfoWidget->OnClickCallback = [this, WeakUserCenter = TWeakObjectPtr<UPSOneUserCenter>(UserCenter)](
														 EPSOneUserCenterAccountInfoClickType ClickType)
	{
		if (!WeakUserCenter.IsValid())
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("AccountInfoWidget callback triggered, but UserCenter is invalid."));
			return;
		}
		const FPSOneUserCenterModel& Model = WeakUserCenter->GetModel();

		switch (ClickType)
		{
			// 点击了头像
			case EPSOneUserCenterAccountInfoClickType::Avatar:
				UE_LOG(LogPSOneUIManager, Display, TEXT("Avatar clicked. No action defined."));
				break;
			// 点击了昵称
			case EPSOneUserCenterAccountInfoClickType::Nick:
			{
				UE_LOG(LogPSOneUIManager, Display, TEXT("Nickname clicked. Showing change nickname UI."));
				ShowNicknameChange();
			}
			break;
			// 点击了手机号
			case EPSOneUserCenterAccountInfoClickType::Mobile:
			{
				UE_LOG(LogPSOneUIManager, Display, TEXT("Mobile clicked. Preparing phone binding info..."));
				FBindingInfo PhoneBindInfo;
				PhoneBindInfo.bIsEmail = false;
				PhoneBindInfo.bIsFirstBinding = Model.CellPhone.IsEmpty();
				PhoneBindInfo.bAllowChangeBinding = !PhoneBindInfo.bIsFirstBinding;
				PhoneBindInfo.CurrentBinding = Model.CellPhone;
				PhoneBindInfo.FormattedBinding = Model.ShowCellPhone;
				PhoneBindInfo.PreviousBinding = Model.CellPhone;
				PhoneBindInfo.FormattedPreviousBinding = Model.ShowCellPhone;

				if (PhoneBindInfo.bIsFirstBinding)
				{
					UE_LOG(LogPSOneUIManager, Display, TEXT("Phone not bound. Showing phone binding UI."));
					ShowBinding(WeakUserCenter.Get(), PhoneBindInfo);
				}
				else
				{
					UE_LOG(LogPSOneUIManager, Display, TEXT("Phone already bound. Showing phone binding status UI."));
					ShowBindingStatus(WeakUserCenter.Get(), PhoneBindInfo);
				}
			}
			break;
			// 点击了邮箱
			case EPSOneUserCenterAccountInfoClickType::Email:
				UE_LOG(LogPSOneUIManager, Display, TEXT("Email clicked. Preparing email binding info..."));
				break;

			case EPSOneUserCenterAccountInfoClickType::Cancellation:
			{
				UE_LOG(LogPSOneUIManager, Display, TEXT("Account Cancellation button clicked. Triggering callback."));
				if (OnOpenAccountCancellation)
				{
					OnOpenAccountCancellation();
				}
				else
				{
					UE_LOG(LogPSOneUIManager, Warning,
						TEXT("OnOpenAccountCancellation callback is not set. Cannot open cancellation process."));
				}
			}
			break;

			case EPSOneUserCenterAccountInfoClickType::ChangePassword:
			{
				UE_LOG(LogPSOneUIManager, Display, TEXT("Change Password button clicked. Showing password change UI."));
				FBindingInfo PhoneBindInfo;
				PhoneBindInfo.CurrentBinding = Model.CellPhone;
				PhoneBindInfo.FormattedBinding = Model.ShowCellPhone;
				ShowPwdChange(WeakUserCenter.Get(), Model.HavePwd, PhoneBindInfo);
			}
			break;

			default:
				break;
		}
	};

	// 监听设备管理点击事件
	UserCenter->DeviceManagerWidget->OnOfflineDeviceCallback =
		[this, WeakUserCenter = TWeakObjectPtr<UPSOneUserCenter>(UserCenter)](const FOneOnlineDevice& DeviceInfo)
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Offline device requested. DeviceID: %s"), *DeviceInfo.DeviceID);
		if (OnOfflineDevice)
		{
			OnOfflineDevice(DeviceInfo.DeviceID);
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("OnOfflineDevice callback is not set. Cannot process offline device request."));
		}
	};

	// 监听法律条款点击事件
	UserCenter->LegalTermsWidget->OnClickOpenLegalTerms = [this]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Legal Terms clicked. Triggering URL open callback."));
		if (OnOpenLegalTermsURL)
		{
			OnOpenLegalTermsURL();
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("OnOpenLegalTermsURL callback is not set. Cannot open legal terms URL."));
		}
	};

	// 监听用户中心 Tab 切换事件
	UserCenter->OnTabIndexChanged = [this, WeakUserCenter = TWeakObjectPtr<UPSOneUserCenter>(UserCenter)](int32 Index)
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("UserCenter Tab index changed to: %d"), Index);
		if (OnUserCenterTabIndexChanged)
		{
			OnUserCenterTabIndexChanged(Index);
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("OnUserCenterTabIndexChanged callback is not set. Cannot notify tab change."));
		}
	};

	// 监听切换账号事件
	UserCenter->OnSwitchAccount = [this]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Switch Account button clicked. Hiding UserCenter and initiating logout."));
		AsyncTask(ENamedThreads::GameThread,
			[this]()
			{
				HideUserCenter();
				if (OnLogout)
				{
					OnLogout();
				}
				else
				{
					UE_LOG(LogPSOneUIManager, Warning,
						TEXT("Switch Account requested, but OnLogout callback is not set. Cannot perform logout."));
				}
			});
	};

	UserCenter->OtherWidget->OnClickDeleteAccount = [this, WeakUserCenter = TWeakObjectPtr<UPSOneUserCenter>(UserCenter)]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Delete Account button clicked (from OtherWidget). Showing confirmation UI."));

		if (!WeakUserCenter.IsValid())
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("Delete Account clicked, but UserCenter is invalid."));
			return;
		}

		const auto WidgetClass = LoadClass<UPSOneDelAccount>(nullptr, *DelAccountPath);
		if (!WidgetClass)
		{
			UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to load Delete Account widget class! Path: %s"), *DelAccountPath);
			return;
		}

		UPSOneDelAccount* DelAccountWidget = CreateWidget<UPSOneDelAccount>(GetCurrentWorld(), WidgetClass);
		if (!DelAccountWidget)
		{
			UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to create Delete Account widget instance."));
			return;
		}

		TWeakObjectPtr<UPSOneDelAccount> WeakDelAccountWidget = TWeakObjectPtr<UPSOneDelAccount>(DelAccountWidget);

		DelAccountWidget->OnConfirm = [this, WeakDelAccountWidget]()
		{
			UE_LOG(LogPSOneUIManager, Display,
				TEXT("Delete Account confirmation - Confirm button clicked. Initiating delete process."));
			if (OnApplyDeleteAccount)
			{
				OnApplyDeleteAccount(
					[this, WeakDelAccountWidget](bool Success)
					{
						if (!Success)
						{
							UE_LOG(LogPSOneUIManager, Error, TEXT("OnApplyDeleteAccount callback reported failure."));
							return;
						}

						UE_LOG(LogPSOneUIManager, Display, TEXT("Account deletion successful. Hiding UserCenter and logging out."));
						AsyncTask(ENamedThreads::GameThread,
							[this, WeakDelAccountWidget]()
							{
								RemoveWidgetFromViewport(WeakDelAccountWidget);
								HideUserCenter();
							});
					});
			}
			else
			{
				UE_LOG(
					LogPSOneUIManager, Warning, TEXT("OnApplyDeleteAccount callback is not set. Cannot initiate delete process."));
			}
		};

		DelAccountWidget->OnClickCloseButton = [this, WeakDelAccountWidget]()
		{
			UE_LOG(LogPSOneUIManager, Display, TEXT("Delete Account confirmation - Close button clicked."));
			AsyncTask(ENamedThreads::GameThread,
				[this, WeakDelAccountWidget]()
				{
					RemoveWidgetFromViewport(WeakDelAccountWidget);
					PopHiddenWidgetsUntilUserCenter();
				});
		};

		AddWidgetToViewport(DelAccountWidget);
		DelAccountWidget->SetKeyboardFocus();
		PushHiddenWidget(WeakUserCenter.Get());
		UE_LOG(LogPSOneUIManager, Display, TEXT("Showing Delete Account confirmation UI. UserCenter is hidden."));
	};

	// 监听关闭按钮点击事件
	UserCenter->OnClickCloseButton = [this]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("UserCenter Close button clicked. Hiding UserCenter asynchronously."));
		AsyncTask(ENamedThreads::GameThread, [this]() { HideUserCenter(); });
	};

	UE_LOG(LogPSOneUIManager, Display, TEXT("Adding UserCenter widget to viewport and finalizing setup."));
	AddWidgetToViewport(UserCenter);

	BroadcastWidgetVisibilityChange(EUIWidgetType::UserCenter, true);
	UserCenter->SetKeyboardFocus();
	UIWidgetInstanceMap.Add(UserCenterWidgetIndex, UserCenter);

	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowUserCenter function completed successfully."));
}

void UPSOneUIManager::HideUserCenter()
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Attempting to hide UserCenter UI..."));
	check(IsInGameThread());

	constexpr int32 UserCenterWidgetIndex = static_cast<int32>(EUIWidgetType::UserCenter);

	if (UIWidgetInstanceMap.Contains(UserCenterWidgetIndex))
	{
		const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[UserCenterWidgetIndex];
		if (UserWidget.IsValid())
		{
			UE_LOG(LogPSOneUIManager, Display, TEXT("Found valid UserCenter instance. Removing from viewport and map."));
			RemoveWidgetFromViewport(UserWidget);
			UIWidgetInstanceMap.Remove(UserCenterWidgetIndex);
			BroadcastWidgetVisibilityChange(EUIWidgetType::UserCenter, false);
			PopFocusedWidget();
			UE_LOG(LogPSOneUIManager, Display, TEXT("HideUserCenter completed successfully."));
			return;
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning,
				TEXT("UserCenter found in map but pointer is invalid during HideUserCenter. Removing entry."));
			UIWidgetInstanceMap.Remove(UserCenterWidgetIndex);
		}
	}
	else
	{
		UE_LOG(LogPSOneUIManager, Warning,
			TEXT("HideUserCenter called, but UserCenter widget was not found in the map. It might be already hidden or never "
				 "shown."));
	}
	PopFocusedWidget();
	UE_LOG(LogPSOneUIManager, Display, TEXT("HideUserCenter finished (widget might not have been found or was invalid)."));
}

bool UPSOneUIManager::ValidateUserCenterWidget(UPSOneUserCenter** OutUserCenter)
{
	if (OutUserCenter)
	{
		*OutUserCenter = nullptr;
	}
	else
	{
		UE_LOG(LogPSOneUIManager, Warning,
			TEXT("ValidateUserCenterWidget called with null OutUserCenter pointer. Cannot return widget."));
		return false;
	}

	constexpr int32 UserCenterWidgetIndex = static_cast<int32>(EUIWidgetType::UserCenter);

	if (!UIWidgetInstanceMap.Contains(UserCenterWidgetIndex))
	{
		UE_LOG(LogPSOneUIManager, Warning, TEXT("Validation failed: UserCenter widget key not found in UIWidgetInstanceMap."));
		return false;
	}

	const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[UserCenterWidgetIndex];
	if (!UserWidget.IsValid())
	{
		UE_LOG(LogPSOneUIManager, Warning, TEXT("Validation failed: UserCenter widget pointer in map is invalid."));
		return false;
	}

	UPSOneUserCenter* UserCenter = Cast<UPSOneUserCenter>(UserWidget.Get());
	if (UserCenter == nullptr)
	{
		UE_LOG(LogPSOneUIManager, Error,
			TEXT("Validation failed: Failed to cast valid UserWidget pointer to UPSOneUserCenter. Type mismatch?"));
		return false;
	}

	*OutUserCenter = UserCenter;
	UE_LOG(LogPSOneUIManager, Verbose, TEXT("UserCenter widget validation successful."));
	return true;
}

// 显示昵称修改页面
void UPSOneUIManager::ShowNicknameChange()
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Attempting to show Nickname Change UI..."));
	UPSOneUserCenter* UserCenter = nullptr;
	// 验证用户中心控件是否有效
	if (!ValidateUserCenterWidget(&UserCenter))
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to validate UserCenter widget. Cannot show Nickname Change UI."));
		return; // 用户中心无效，无法继续
	}

	// 加载修改昵称控件类
	UE_LOG(LogPSOneUIManager, Display, TEXT("Loading Change Nickname widget class from path: %s"), *ChangeNickPath);
	const auto WidgetClass = LoadClass<UPSOneChangNick>(nullptr, *ChangeNickPath);
	if (WidgetClass == nullptr)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to load Change Nickname widget class! Path: %s"), *ChangeNickPath);
		// !!!: 加载失败，应提示用户
		return;
	}

	// 创建修改昵称控件实例
	UPSOneChangNick* ChangeNickWidget = CreateWidget<UPSOneChangNick>(GetCurrentWorld(), WidgetClass);
	if (!ChangeNickWidget)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to create Change Nickname widget instance."));
		// !!!: 创建失败，应提示用户
		return;
	}

	// 添加到视口并设置焦点
	AddWidgetToViewport(ChangeNickWidget);
	ChangeNickWidget->SetKeyboardFocus();

	// 隐藏用户中心
	PushHiddenWidget(UserCenter);
	UE_LOG(LogPSOneUIManager, Display, TEXT("Showing Change Nickname UI. UserCenter is hidden."));

	// 绑定确认按钮回调
	ChangeNickWidget->OnConfirm = [this, WeakWidget = TWeakObjectPtr<UPSOneChangNick>(ChangeNickWidget)](const FString& NickName)
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Nickname Change confirmation - Confirm button clicked. New nickname: %s"), *NickName);
		if (OnNicknameChange)
		{
			// 调用外部处理函数修改昵称
			OnNicknameChange(NickName, WeakWidget);
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("OnNicknameChange callback is not set. Cannot process nickname change."));
			// !!!: 回调未设置，需要告知用户或禁用确认按钮
			// Consider showing an error message to the user
		}
	};

	// 绑定返回按钮回调
	ChangeNickWidget->OnBackspace = [this, WeakWidget = TWeakObjectPtr<UPSOneChangNick>(ChangeNickWidget)]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Nickname Change UI - Backspace/Cancel pressed."));
		AsyncTask(ENamedThreads::GameThread,
			[this, WeakWidget]()
			{
				RemoveWidgetFromViewport(WeakWidget); // 移除当前界面
				PopHiddenWidgetAndShow(); // 弹出并显示上一个隐藏的界面 (用户中心)
			});
	};

	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowNicknameChange function completed."));
}

// 显示绑定状态界面 (手机或邮箱)
// 根据 BindingDetails.bAllowChangeBinding 决定显示"确定"、"更换绑定手机"还是"确认解绑"
void UPSOneUIManager::ShowBindingStatus(UUserWidget* WidgetToHide, struct FBindingInfo& BindingDetails)
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Attempting to show Binding Status UI (Type: %s)..."), BindingDetails.bIsEmail ? TEXT("Email") : TEXT("Phone"));
	check(WidgetToHide); // 确保传入了需要隐藏的控件

	// 加载状态显示控件类
	UE_LOG(LogPSOneUIManager, Display, TEXT("Loading Binding Status widget class from path: %s"), *ShowPhoneStatusPath);
	const auto WidgetClass = LoadClass<UPSOneShowIphoneStatus>(nullptr, *ShowPhoneStatusPath);
	if (!WidgetClass)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to load Binding Status (UPSOneShowIphoneStatus) widget class! Path: %s"), *ShowPhoneStatusPath);
		// !!!: 加载失败，应提示用户
		return;
	}

	// 创建状态显示控件实例
	UPSOneShowIphoneStatus* ShowBindAccountStatusWidget = CreateWidget<UPSOneShowIphoneStatus>(GetCurrentWorld(), WidgetClass);
	if (!ShowBindAccountStatusWidget)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to create Binding Status widget instance."));
		// !!!: 创建失败，应提示用户
		return;
	}

	TWeakObjectPtr<UPSOneShowIphoneStatus> WeakShowBindAccountStatusWidget =
		TWeakObjectPtr<UPSOneShowIphoneStatus>(ShowBindAccountStatusWidget);

	// 根据是邮箱还是手机设置标题和描述
	if (BindingDetails.bIsEmail)
	{
		ShowBindAccountStatusWidget->TitleTextBlock->SetText(LOCTEXT("LinkEmailTitle", "绑定邮箱"));
		ShowBindAccountStatusWidget->BindDescTextBlock->SetText(
			LOCTEXT("LinkkEmailDesc", "当前账号已绑定邮箱，您可以使用此邮箱作为登录账号："));
	}
	else
	{
		ShowBindAccountStatusWidget->TitleTextBlock->SetText(LOCTEXT("LinkPhoneNumberTitle", "绑定手机号"));
		ShowBindAccountStatusWidget->BindDescTextBlock->SetText(
			LOCTEXT("LinkPhoneNumberDesc", "当前账号已绑定了手机，您可以使用此手机号作为登录账号："));
	}

	// 设置显示的绑定信息 (脱敏后的手机号或邮箱)
	UE_LOG(LogPSOneUIManager, Display, TEXT("Setting binding display text: %s"), *BindingDetails.FormattedBinding);
	ShowBindAccountStatusWidget->SetBindText(BindingDetails.FormattedBinding);

	// 根据类型和是否允许更改，配置按钮行为
	if (BindingDetails.bIsEmail)
	{
		// 邮箱逻辑：
		// 隐藏左下角的独立"解绑"按钮 (如果控件有的话)，统一使用主动作按钮
		ShowBindAccountStatusWidget->HideUnbind = true; // !!!: 确认 UPSOneShowIphoneStatus 控件是否有独立解绑按钮以及 HideUnbind 的确切作用
		if (BindingDetails.bAllowChangeBinding)
		{
			// 允许解绑 -> 主按钮显示 "确认解绑"
			ShowBindAccountStatusWidget->ActionButton->Title = LOCTEXT("ConfirmUnlink", "确认解绑");
			UE_LOG(LogPSOneUIManager, Display, TEXT("Email binding status: Allow unbind. Action button set to 'ConfirmUnlink'."));
		}
		else
		{
			// 不允许解绑 (例如刚绑定成功后) -> 主按钮显示 "确定" (仅用于关闭此状态界面)
			ShowBindAccountStatusWidget->ActionButton->Title = LOCTEXT("Confirm", "确定");
			UE_LOG(LogPSOneUIManager, Display, TEXT("Email binding status: Disallow unbind. Action button set to 'Confirm'."));
		}
	}
	else // 手机逻辑
	{
		if (BindingDetails.bAllowChangeBinding)
		{
			// 允许换绑的情况下，也允许解绑
			ShowBindAccountStatusWidget->HideUnbind = false;
			ShowBindAccountStatusWidget->ActionButton->Title = LOCTEXT("ChangeLinkedPhone", "更换绑定手机");
			UE_LOG(LogPSOneUIManager, Display, TEXT("Phone binding status: Allow change binding. Action button set to 'ChangeLinkedPhone'"));
		}
		else
		{
			// 不允许换绑时，隐藏解绑按钮，只显示确定按钮
			ShowBindAccountStatusWidget->HideUnbind = true;
			ShowBindAccountStatusWidget->ActionButton->Title = LOCTEXT("Confirm", "确定");
			UE_LOG(LogPSOneUIManager, Display, TEXT("Phone binding status: Disallow change binding. Action button set to 'Confirm'"));
		}
	}

	// 主按钮点击事件处理
	ShowBindAccountStatusWidget->OnClicked = [this, WeakShowBindAccountStatusWidget, BindingDetails]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("UPSOneShowIphoneStatus OnClicked lambda triggered"));
		if (BindingDetails.bIsEmail)
		{
			if (BindingDetails.bAllowChangeBinding)
			{
				// 点击了 确认解绑 按钮，解绑邮箱。
				// 不需要验证码，直接解绑。
				if (OnUnbindEmail)
				{
					UE_LOG(LogPSOneUIManager, Display, TEXT("Executing email unbinding operation"));

					// 二次确认
					if (OnShowConfirmDialog)
					{
						FText localText = LOCTEXT("ConfirmUnlink", "确认解绑");
						FString message = localText.ToString();
						OnShowConfirmDialog(message, [this, WeakShowBindAccountStatusWidget, BindingDetails](bool bOk)
						{
							if (bOk)
							{
								OnUnbindEmail(BindingDetails.CurrentBinding, WeakShowBindAccountStatusWidget,
									[this, WeakShowBindAccountStatusWidget](bool Success, const FString& JsonStr, int Code, const FString& Err)
								{
									UE_LOG(LogPSOneUIManager, Display,
										TEXT("Email unbinding response - Success: %s, Response: %s, Error Code: %d, Error Message: %s"),
										Success ? TEXT("true") : TEXT("false"), *JsonStr, Code, *Err);

									if (!Success)
									{
										UE_LOG(LogPSOneUIManager, Error, TEXT("Email unbinding failed: %s"), *Err);
										return;
									}

									RemoveWidgetFromViewport(WeakShowBindAccountStatusWidget);
									PopHiddenWidgetsUntilUserCenter();
								});
							}
						});
					}
					else {
						OnUnbindEmail(BindingDetails.CurrentBinding, WeakShowBindAccountStatusWidget,
							[this, WeakShowBindAccountStatusWidget](bool Success, const FString& JsonStr, int Code, const FString& Err)
						{
							UE_LOG(LogPSOneUIManager, Display,
								TEXT("Email unbinding response - Success: %s, Response: %s, Error Code: %d, Error Message: %s"),
								Success ? TEXT("true") : TEXT("false"), *JsonStr, Code, *Err);

							if (!Success)
							{
								UE_LOG(LogPSOneUIManager, Error, TEXT("Email unbinding failed: %s"), *Err);
								return;
							}

							RemoveWidgetFromViewport(WeakShowBindAccountStatusWidget);
							PopHiddenWidgetsUntilUserCenter();
						});
					}
				}
				else
				{
					UE_LOG(LogPSOneUIManager, Error, TEXT("OnUnbindEmail callback not bound, cannot execute email unbinding"));
					// !!!: 回调未绑定时缺少用户提示机制
				}
			}
			else
			{
				// 直接返回用户中心
				UE_LOG(LogPSOneUIManager, Display, TEXT("Email binding status: Confirm button clicked, returning to user center"));
				AsyncTask(ENamedThreads::GameThread,
					[this, WeakShowBindAccountStatusWidget]()
					{
						RemoveWidgetFromViewport(WeakShowBindAccountStatusWidget);
						PopHiddenWidgetsUntilUserCenter();
					});
				return;
			}
		}
		else
		{
			// 手机绑定相关逻辑
			// 如果不允许换绑，点击确认按钮返回到用户中心
			if (!BindingDetails.bAllowChangeBinding)
			{
				UE_LOG(LogPSOneUIManager, Display, TEXT("Phone binding status: Change binding not allowed, returning to user center"));
				AsyncTask(ENamedThreads::GameThread,
					[this, WeakShowBindAccountStatusWidget]()
					{
						RemoveWidgetFromViewport(WeakShowBindAccountStatusWidget);
						PopHiddenWidgetsUntilUserCenter();
					});
				return;
			}

			// 检查验证码发送回调是否已绑定
			if (!OnSendPhoneVerificationCode)
			{
				UE_LOG(LogPSOneUIManager, Error, TEXT("Verification code sending callback (OnSendPhoneVerificationCode) not bound!"));
				// !!!: 回调未绑定时缺少用户提示机制
				return;
			}

			UE_LOG(LogPSOneUIManager, Display, TEXT("Requesting verification code for previous phone number..."));
			// 请求验证码
			FText Title = LOCTEXT("LinkPhoneNumber", "绑定手机号");
			OnSendPhoneVerificationCode(0,		   // 旧手机不需要传递 AreaCodeId
				BindingDetails.PreviousBinding,	   // 旧手机
				EVerificationCodeType::Change,	   // 修改手机号
				false,							   // 不需要校验就旧手机的有效性
				[this, WeakShowBindAccountStatusWidget, BindingDetails, Title](
					bool Success, const FString& ResponseStr, int ErrCode, const FString& ErrMsg)
				{
					UE_LOG(LogPSOneUIManager, Display,
						TEXT("Verification code request response - Success: %s, Response: %s, Error Code: %d, Error Message: %s"),
						Success ? TEXT("true") : TEXT("false"), *ResponseStr, ErrCode, *ErrMsg);

					// 如果请求验证码失败，则返回
					if (!Success)
					{
						// !!!: 验证码请求失败时缺少用户提示机制
						UE_LOG(LogPSOneUIManager, Error, TEXT("Verification code request failed: %s"), *ErrMsg);
						return;
					}

					// 显示验证码输入窗口
					UE_LOG(LogPSOneUIManager, Display, TEXT("Showing verification code input widget..."));
					ShowVerificationCodeInput(
						WeakShowBindAccountStatusWidget.Get(),		// 绑定状态窗口
						Title.ToString(),							// 标题
						BindingDetails.FormattedPreviousBinding,	// 旧手机
						[this, WeakShowBindAccountStatusWidget, BindingDetails]()
						{
							// 重新发送验证码，回调函数
							UE_LOG(LogPSOneUIManager, Display, TEXT("Resending verification code..."));
							OnSendPhoneVerificationCode(
								0, BindingDetails.PreviousBinding, EVerificationCodeType::Change, false, nullptr);
						},
						[this, WeakShowBindAccountStatusWidget, BindingDetails](
							UUserWidget* InputVerificationCodeWidget,	 // 验证码输入框
							const FString& VerificationCode				 // 验证码
						)
						{
							// 校验验证码，回调函数
							UE_LOG(LogPSOneUIManager, Display, TEXT("Validating verification code: %s"), *VerificationCode);

							// 检查回调是否已绑定
							if (!OnValidateVerificationCodeAndBindPhone)
							{
								UE_LOG(LogPSOneUIManager, Error, TEXT("Verification code validation callback (OnValidateVerificationCodeAndBindPhone) not bound"));
								// !!!: 回调未绑定时缺少用户提示机制
								return;
							}

							// 校验验证码，绑定手机
							OnValidateVerificationCodeAndBindPhone(0,	 // 不需要传递 AreaCodeId
								BindingDetails.PreviousBinding,			 // 手机
								VerificationCode,						 // 验证码
								false,									 // 是否 需要绑定手机
								TEXT(""),								 // 旧手机
								[this, InputVerificationCodeWidget, BindingDetails](
									bool Success, const FString& ResponseStr, int ErrCode, const FString& ErrMsg)
								{
									UE_LOG(LogPSOneUIManager, Display,
										TEXT("Verification code validation result - Success: %s, Response: %s, Error Code: %d, "
												 "Error Message: %s"),
										Success ? TEXT("true") : TEXT("false"), *ResponseStr, ErrCode, *ErrMsg);

									if (!Success)
									{
										// !!!: 验证码验证失败时缺少用户提示机制
										UE_LOG(LogPSOneUIManager, Error, TEXT("Verification code validation failed: %s"), *ErrMsg);
										return;
									}

									UE_LOG(LogPSOneUIManager, Display, TEXT("Previous phone verified successfully, showing new phone binding page"));

									FBindingInfo CopyBindingDetails = BindingDetails;
									CopyBindingDetails.bIsFirstBinding = true;		   // 是否是第一次绑定
									CopyBindingDetails.bAllowChangeBinding = false;	   // 是否允许换绑
									ShowBinding(InputVerificationCodeWidget, CopyBindingDetails);
								});
						});
				});
		}
	};

	// 返回按钮点击事件处理
	ShowBindAccountStatusWidget->OnBackspace = [this, WeakShowBindAccountStatusWidget]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Back button clicked, removing widget and popping hidden widget"));
		AsyncTask(ENamedThreads::GameThread,
			[this, WeakShowBindAccountStatusWidget]()
			{
				RemoveWidgetFromViewport(WeakShowBindAccountStatusWidget);
				PopHiddenWidgetsUntilUserCenter();
			});
	};

	// 解绑按钮点击事件处理（仅手机绑定时显示）
	ShowBindAccountStatusWidget->OnUnbind = [this, WeakShowBindAccountStatusWidget, BindingDetails]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Unbind button clicked"));
		// 检查是否允许解绑
		if (!BindingDetails.bAllowChangeBinding)
		{
			UE_LOG(LogPSOneUIManager, Display, TEXT("Change/unbind phone not allowed, no unbinding operation needed"));
			return;
		}

		// 检查验证码发送回调是否已绑定
		if (!OnSendPhoneVerificationCode)
		{
			UE_LOG(LogPSOneUIManager, Error, TEXT("Verification code sending callback (OnSendPhoneVerificationCode) not bound!"));

			return;
		}

		UE_LOG(LogPSOneUIManager, Display, TEXT("Requesting verification code for current phone number..."));
		FText Title = LOCTEXT("UnlinkPhoneNumber", "解绑手机号");
		OnSendPhoneVerificationCode(0,			 // 不需要传递 AreaCodeId
			BindingDetails.CurrentBinding,		 // 当前手机
			EVerificationCodeType::Unbinding,	 // 解绑手机号
			false,
			[this, WeakShowBindAccountStatusWidget, BindingDetails, Title](
				bool Success, const FString& ResponseStr, int ErrCode, const FString& ErrMsg)
			{
				UE_LOG(LogPSOneUIManager, Display,
					TEXT("Verification code request response - Success: %s, Response: %s, Error Code: %d, Error Message: %s"),
					Success ? TEXT("true") : TEXT("false"), *ResponseStr, ErrCode, *ErrMsg);

				if (!Success)
				{
					// !!!: 验证码请求失败时缺少用户提示机制
					UE_LOG(LogPSOneUIManager, Error, TEXT("Verification code request failed: %s"), *ErrMsg);
					return;
				}

				UE_LOG(LogPSOneUIManager, Display, TEXT("Showing verification code input widget..."));
				ShowVerificationCodeInput(
					WeakShowBindAccountStatusWidget.Get(), Title.ToString(), BindingDetails.FormattedPreviousBinding,
					[this, WeakShowBindAccountStatusWidget, BindingDetails]()
					{
						// 重新发送验证码回调
						UE_LOG(LogPSOneUIManager, Display, TEXT("Resending verification code..."));
						OnSendPhoneVerificationCode(
							0, BindingDetails.PreviousBinding, EVerificationCodeType::Unbinding, false, nullptr);
						// !!!: 重新发送验证码时没有错误处理回调
					},
					[this, WeakShowBindAccountStatusWidget, BindingDetails](
						UUserWidget* InputVerificationCodeWidget, const FString& VerificationCode)
					{
						// 验证码验证回调
						UE_LOG(LogPSOneUIManager, Display, TEXT("Validating verification code: %s"), *VerificationCode);

						// 检查解绑回调是否已绑定
						if (OnUnbindPhoneNumber)
						{
							// 这里，外部退出登录，所以不需要回调
							OnUnbindPhoneNumber(VerificationCode, InputVerificationCodeWidget, nullptr);
						}
						else
						{
							UE_LOG(LogPSOneUIManager, Error, TEXT("Phone unbinding callback (OnUnbindPhoneNumber) not bound"));
							// !!!: 回调未绑定时缺少用户提示机制
						}
					});
			});
	};

	// --- 显示界面并处理 UI 逻辑 ---
	UE_LOG(LogPSOneUIManager, Display, TEXT("Adding Binding Status widget to viewport and setting keyboard focus..."));
	AddWidgetToViewport(ShowBindAccountStatusWidget); // 将控件添加到视口
	ShowBindAccountStatusWidget->SetKeyboardFocus();  // 设置键盘焦点，以便响应输入或按钮事件
	PushHiddenWidget(WidgetToHide);					  // 将调用此函数的上层界面隐藏并入栈

	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowBindingStatus function completed"));
}

void UPSOneUIManager::ShowBinding(UUserWidget* WidgetToHide, struct FBindingInfo& BindingDetails)
{
	// 显示绑定界面，支持邮箱绑定和手机号绑定，根据 BindingDetails 中的 bIsEmail 字段区分
	UE_LOG(LogPSOneUIManager, Display, TEXT("Starting ShowBinding function... Type: %s"), BindingDetails.bIsEmail ? TEXT("Email") : TEXT("Phone"));

	// 加载输入控件类
	const auto WidgetClass = LoadClass<UPSOneInputPhoneNum>(nullptr, *InputPhoneNumPath);
	if (WidgetClass == nullptr)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to load UPSOneInputPhoneNum widget class! Path: %s"), *InputPhoneNumPath);
		return;
	}

	// 创建输入控件实例
	UPSOneInputPhoneNum* InputAccountWidget = CreateWidget<UPSOneInputPhoneNum>(GetCurrentWorld(), WidgetClass);
	// 创建弱指针引用避免循环引用
	TWeakObjectPtr<UPSOneInputPhoneNum> WeakInputAccountWidget = TWeakObjectPtr<UPSOneInputPhoneNum>(InputAccountWidget);

	// 设置输入类型（邮箱或手机号）
	InputAccountWidget->InputType = BindingDetails.bIsEmail ? EPSOneInputPhoneNumType::Email : EPSOneInputPhoneNumType::PhoneNum;

	// 设置区号点击事件
	InputAccountWidget->OnClickAreaCode = [this, WeakInputAccountWidget]()
	{
		// 显示国家/区号选择界面
		UE_LOG(LogPSOneUIManager, Display, TEXT("OnClickAreaCode triggered. Showing country code selection..."));
		ShowCountryCodeSelection(WeakInputAccountWidget.Get(), CountryCodeJson,
			[WeakInputAccountWidget](int AreaCodeId, int AreaCode, const FString& AreaName)
			{
				// 区号选择回调
				UE_LOG(LogPSOneUIManager, Display, TEXT("Area code selected: ID: %d, Code: %d, Name: %s"), AreaCodeId, AreaCode,
					*AreaName);

				// !!!: 此处应检查 WeakInputAccountWidget 是否有效
				if (!WeakInputAccountWidget.IsValid())
				{
					UE_LOG(LogPSOneUIManager, Error, TEXT("Area code selection callback failed - input widget is invalid"));
					return;
				}

				WeakInputAccountWidget->SetAreaCodeId(AreaCodeId);
				WeakInputAccountWidget->SetAreaCode(AreaCode);

				WeakInputAccountWidget->AreaCodeButton->SetTitle(FText::FromString(FString::Printf(TEXT("%d"), AreaCode)));
				UE_LOG(LogPSOneUIManager, Display, TEXT("Area code button updated with new code: %d"), AreaCode);
			});
	};

	// 设置下一步点击事件
	InputAccountWidget->OnClickNext = [this, WeakInputAccountWidget, BindingDetails]()
	{
		// !!!: 此处应检查 WeakInputAccountWidget 是否有效
		if (!WeakInputAccountWidget.IsValid())
		{
			UE_LOG(LogPSOneUIManager, Error, TEXT("Next button callback failed - input widget is invalid"));
			return;
		}

		// 邮箱绑定和手机号绑定分支处理
		if (BindingDetails.bIsEmail)
		{
			// 邮箱绑定流程
			UE_LOG(LogPSOneUIManager, Display, TEXT("OnClickNext triggered. Input is Email"));
			if (!OnSendEmailVerificationCode)
			{
				UE_LOG(LogPSOneUIManager, Error, TEXT("OnSendEmailVerificationCode is not bound!"));
				// !!!: 应向用户显示错误提示
				return;
			}

			FString Email = WeakInputAccountWidget->GetPhoneNumber();
			if (Email.IsEmpty())
			{
				UE_LOG(LogPSOneUIManager, Warning, TEXT("Email address is empty, cannot send verification code"));
				// !!!: 应向用户显示输入错误提示
				return;
			}

			UE_LOG(LogPSOneUIManager, Display, TEXT("Email: %s"), *Email);

			// 复制绑定信息用于回调
			FBindingInfo CopyBindingDetails = BindingDetails;
			CopyBindingDetails.CurrentBinding = Email;
			CopyBindingDetails.FormattedBinding = Email;

			// 发送邮箱验证码
			OnSendEmailVerificationCode(Email, EVerificationCodeType::Binding, WeakInputAccountWidget.Get(),
				[this, WeakInputAccountWidget, CopyBindingDetails](
					bool Success, const FString& JsonStr, int Code, const FString& Err)
				{
					check(IsInGameThread());
					if (!Success)
					{
						UE_LOG(LogPSOneUIManager, Error, TEXT("Email verification code request failed. Error code: %d, Error message: %s"), Code, *Err);
						// !!!: 应向用户显示错误信息
						return;
					}

					UE_LOG(LogPSOneUIManager, Display, TEXT("Verification code request successful. Response: %s"), *JsonStr);

					// 显示验证码输入界面
					FText Title = LOCTEXT("LinkEmail", "绑定邮箱");
					ShowVerificationCodeInput(
						WeakInputAccountWidget.Get(), Title.ToString(), CopyBindingDetails.CurrentBinding,
						[this, WeakInputAccountWidget, CopyBindingDetails]()
						{
							// 重新发送验证码回调
							UE_LOG(LogPSOneUIManager, Display, TEXT("Resending verification code..."));

							// !!!: 此处应检查 WeakInputAccountWidget 和 OnSendEmailVerificationCode 是否有效
							if (!OnSendEmailVerificationCode)
							{
								UE_LOG(LogPSOneUIManager, Error, TEXT("Email verification code resend callback is not bound"));
								return;
							}

							OnSendEmailVerificationCode(CopyBindingDetails.CurrentBinding, EVerificationCodeType::Binding,
								WeakInputAccountWidget.Get(), nullptr);
						},
						[this, WeakInputAccountWidget, CopyBindingDetails](
							UUserWidget* InputVerificationCodeWidget, const FString& VerificationCode)
						{
							// 验证码输入完成回调
							UE_LOG(LogPSOneUIManager, Display, TEXT("Verifying entered code: %s"), *VerificationCode);
							if (VerificationCode.IsEmpty())
							{
								UE_LOG(LogPSOneUIManager, Warning, TEXT("Verification code is empty, cannot verify"));
								// !!!: 应向用户显示验证码输入错误提示
								return;
							}

							if (OnBindEmail)
							{
								// 验证邮箱验证码并绑定
								OnBindEmail(CopyBindingDetails.CurrentBinding, VerificationCode, WeakInputAccountWidget.Get(),
									[this, InputVerificationCodeWidget, CopyBindingDetails](
										bool Success, const FString& JsonStr, int Code, const FString& Err)
									{
										check(IsInGameThread());
										if (!Success)
										{
											UE_LOG(LogPSOneUIManager, Error, TEXT("Email binding failed. Error code: %d, Error message: %s"), Code, *Err);
											// !!!: 应向用户显示错误信息
											return;
										}

										UE_LOG(LogPSOneUIManager, Display, TEXT("Binding email successful. Response: %s"), *JsonStr);

										// 复制并更新绑定信息
										FBindingInfo CopyBindingDetails1 = CopyBindingDetails;
										CopyBindingDetails1.bAllowChangeBinding = false;

										// 显示绑定状态
										ShowBindingStatus(InputVerificationCodeWidget, CopyBindingDetails1);
									});
							}
							else
							{
								UE_LOG(LogPSOneUIManager, Error, TEXT("OnBindEmail is not bound!"));
								// !!!: 应向用户显示错误提示
							}
						});
				});
		}
		else
		{
			// 手机号绑定流程
			UE_LOG(LogPSOneUIManager, Display, TEXT("Starting phone binding process"));

			// 根据是否已有绑定来确定验证码类型
			EVerificationCodeType CodeType =
				BindingDetails.PreviousBinding.Len() == 0 ? EVerificationCodeType::Binding : EVerificationCodeType::Change;

			UE_LOG(LogPSOneUIManager, Display, TEXT("OnClickNext triggered. Verification Code Purpose: %s"),
				CodeType == EVerificationCodeType::Binding ? TEXT("Binding") : TEXT("Change"));

			if (!OnSendPhoneVerificationCode)
			{
				UE_LOG(LogPSOneUIManager, Error, TEXT("RequestVerificationCode is not bound!"));
				// !!!: 应向用户显示错误提示
				return;
			}

			// 获取手机号信息
			FBindingInfo CopyBindingDetails = BindingDetails;
			CopyBindingDetails.CountryCodeId = WeakInputAccountWidget->GetAreaCodeId();
			CopyBindingDetails.CurrentBinding = WeakInputAccountWidget->GetPhoneNumber();

			if (CopyBindingDetails.CurrentBinding.IsEmpty())
			{
				UE_LOG(LogPSOneUIManager, Warning, TEXT("Phone number is empty, cannot send verification code"));
				// !!!: 应向用户显示输入错误提示
				return;
			}

			UE_LOG(LogPSOneUIManager, Display, TEXT("Requesting verification code. Area Code ID: %d, Phone Number: %s"),
				CopyBindingDetails.CountryCodeId, *CopyBindingDetails.CurrentBinding);

			// 发送手机验证码
			OnSendPhoneVerificationCode(CopyBindingDetails.CountryCodeId, CopyBindingDetails.CurrentBinding, CodeType, true,
				[this, WeakInputAccountWidget, CopyBindingDetails, CodeType](
					bool Success, const FString& ResponseStr, int ErrCode, const FString& ErrMsg)
				{
					check(IsInGameThread());
					if (!Success)
					{
						UE_LOG(LogPSOneUIManager, Error, TEXT("Phone verification code request failed. Error code: %d, Error message: %s"), ErrCode, *ErrMsg);
						// !!!: 应向用户显示错误信息
						return;
					}

					// 显示验证码输入界面
					FText Title = LOCTEXT("LinkPhoneNumber", "绑定手机号");
					UE_LOG(LogPSOneUIManager, Display, TEXT("Verification code request successful. Response: %s"), *ResponseStr);

					ShowVerificationCodeInput(
						WeakInputAccountWidget.Get(), Title.ToString(), TEXT(""),
						[this, WeakInputAccountWidget, CodeType, CopyBindingDetails]()
						{
							// 重新发送验证码回调
							UE_LOG(LogPSOneUIManager, Display, TEXT("Resending verification code..."));

							// !!!: 此处应检查 WeakInputAccountWidget 和 OnSendPhoneVerificationCode 是否有效
							if (!OnSendPhoneVerificationCode)
							{
								UE_LOG(LogPSOneUIManager, Error, TEXT("Phone verification code resend callback is not bound"));
								return;
							}

							OnSendPhoneVerificationCode(
								CopyBindingDetails.CountryCodeId, CopyBindingDetails.CurrentBinding, CodeType, false, nullptr);
						},
						[this, WeakInputAccountWidget, CodeType, CopyBindingDetails](
							UUserWidget* InputVerificationCodeWidget, const FString& VerificationCode)
						{
							// 验证码输入完成回调
							UE_LOG(LogPSOneUIManager, Display, TEXT("Verifying entered code: %s"), *VerificationCode);

							if (VerificationCode.IsEmpty())
							{
								UE_LOG(LogPSOneUIManager, Warning, TEXT("Verification code is empty, cannot verify"));
								// !!!: 应向用户显示验证码输入错误提示
								return;
							}

							if (OnValidateVerificationCodeAndBindPhone)
							{
								// 验证手机验证码并绑定
								OnValidateVerificationCodeAndBindPhone(CopyBindingDetails.CountryCodeId,
									CopyBindingDetails.CurrentBinding, VerificationCode,
									true,	 // 绑定手机
									CopyBindingDetails.PreviousBinding,
									[this, InputVerificationCodeWidget, CodeType, CopyBindingDetails](
										bool Success, const FString& ResponseStr, int ErrCode, const FString& ErrMsg)
									{
										UE_LOG(LogPSOneUIManager, Display,
											TEXT("Verification code validation result - Success: %s, Response: %s, Error Code: %d, "
												 "Error Message: %s"),
											Success ? TEXT("True") : TEXT("False"), *ResponseStr, ErrCode, *ErrMsg);
										if (!Success)
										{
											return;
										}

										UE_LOG(LogPSOneUIManager, Display, TEXT("Verification successful. Processing response..."));

										// 复制并更新绑定信息
										FBindingInfo CopyBindingDetails1 = CopyBindingDetails;
										FString ShowCellphone;

										// 解析返回的 JSON 以获取格式化的手机号
										TSharedPtr<FJsonObject> JsonObject;
										TSharedRef<TJsonReader<TCHAR>> JsonReader = TJsonReaderFactory<TCHAR>::Create(ResponseStr);
										if (FJsonSerializer::Deserialize(JsonReader, JsonObject) && JsonObject.IsValid())
										{
											ShowCellphone = JsonObject->GetStringField(TEXT("showCellphone"));
											CopyBindingDetails1.FormattedBinding = ShowCellphone;
											UE_LOG(LogPSOneUIManager, Display, TEXT("Formatted phone number: %s"), *ShowCellphone);
										}

										// 如果没有获取到格式化的手机号，则使用原始手机号
										if (ShowCellphone.IsEmpty())
										{
											ShowCellphone = CopyBindingDetails1.CurrentBinding;
											UE_LOG(LogPSOneUIManager, Warning, TEXT("Using current phone number as formatted number"));
										}

										UE_LOG(LogPSOneUIManager, Display, TEXT("Showing phone binding status..."));
										CopyBindingDetails1.bAllowChangeBinding = false;

										// 显示绑定状态
										ShowBindingStatus(InputVerificationCodeWidget, CopyBindingDetails1);
									});
							}
							else
							{
								UE_LOG(LogPSOneUIManager, Error, TEXT("VerifyVerificationCode is not bound!"));
								// !!!: 应向用户显示错误提示
							}
						});
				});
		}
	};

	// 设置返回按钮点击事件
	InputAccountWidget->OnBackspace = [this, WeakWidget = TWeakObjectPtr<UPSOneInputPhoneNum>(InputAccountWidget)]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("UPSOneInputPhoneNum OnBackspace triggered. Removing widget from viewport..."));
		AsyncTask(ENamedThreads::GameThread,
			[this, WeakWidget]()
			{

				if (!WeakWidget.IsValid())
				{
					UE_LOG(LogPSOneUIManager, Warning, TEXT("Backspace operation failed - widget is invalid"));
					return;
				}

				RemoveWidgetFromViewport(WeakWidget);
				PopHiddenWidgetAndShow();
			});
	};

	// 将控件添加到视口并设置键盘焦点
	UE_LOG(LogPSOneUIManager, Display, TEXT("Adding widget to viewport and setting keyboard focus..."));
	AddWidgetToViewport(InputAccountWidget);
	InputAccountWidget->SetKeyboardFocus();
	PushHiddenWidget(WidgetToHide);

	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowBinding function completed."));
}

void UPSOneUIManager::ShowCountryCodeSelection(UUserWidget* WidgetToHide, const FString& AreaCodeJsonString,
	TFunction<void(int AreaCodeId, int AreaCode, const FString& Area)> OnAreaCodeSelected)
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Displaying country code selection UI..."));
	UE_LOG(LogPSOneUIManager, Display, TEXT("Attempting to load UPSOneSelectAreaCode widget class from path: %s"), *SelectAreaCodePath);

	// 加载选择区号的 Widget 类
	const auto WidgetClass = LoadClass<UPSOneSelectAreaCode>(nullptr, *SelectAreaCodePath);
	if (WidgetClass == nullptr)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to load UPSOneSelectAreaCode widget class from path: %s!"), *SelectAreaCodePath);
		return;
	}

	// !!!: GetCurrentWorld() 可能返回 nullptr
	UWorld* CurrentWorld = GetCurrentWorld();
	if (!CurrentWorld)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Cannot create widget: CurrentWorld is null."));
		return;
	}
	UPSOneSelectAreaCode* SelectAreaCodeWidget = CreateWidget<UPSOneSelectAreaCode>(CurrentWorld, WidgetClass);
	if (!SelectAreaCodeWidget)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to create UPSOneSelectAreaCode widget instance."));
		return;
	}

	// 绑定区号选择后的回调函数
	SelectAreaCodeWidget->OnClicked = [this, WeakWidget = TWeakObjectPtr<UPSOneSelectAreaCode>(SelectAreaCodeWidget),
										  OnAreaCodeSelected](const FPSOneAreaCode& AreaCode)
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Country code selected - ID: %d, Code: %d, Area: %s"), AreaCode.Id, AreaCode.Code, *AreaCode.Area);
		if (OnAreaCodeSelected)
		{
			OnAreaCodeSelected(AreaCode.Id, AreaCode.Code, AreaCode.Area);
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("OnAreaCodeSelected callback is not bound."));
		}

		// 异步移除 Widget 并显示上一个隐藏的 Widget
		AsyncTask(ENamedThreads::GameThread, [this, WeakWidget]()
		{
			if (WeakWidget.IsValid())
			{
				UE_LOG(LogPSOneUIManager, Display, TEXT("Removing country code selection widget from viewport asynchronously..."));
				RemoveWidgetFromViewport(WeakWidget);
				PopHiddenWidgetAndShow();
			}
			else
			{
				UE_LOG(LogPSOneUIManager, Warning, TEXT("SelectAreaCodeWidget became invalid before removal task executed."));
			}
		});
	};

	// 绑定返回按钮的回调函数
	SelectAreaCodeWidget->OnBackspace = [this, WeakWidget = TWeakObjectPtr<UPSOneSelectAreaCode>(SelectAreaCodeWidget)]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("UPSOneSelectAreaCode Backspace pressed. Removing widget from viewport..."));
		// 异步移除 Widget 并显示上一个隐藏的 Widget
		AsyncTask(ENamedThreads::GameThread, [this, WeakWidget]()
		{
			if (WeakWidget.IsValid())
			{
				RemoveWidgetFromViewport(WeakWidget);
				PopHiddenWidgetAndShow();
			}
			else
			{
				UE_LOG(LogPSOneUIManager, Warning, TEXT("SelectAreaCodeWidget became invalid before backspace task executed."));
			}
		});
	};

	TArray<FPSOneAreaCode> AreaCodes;
	// !!!: AreaCodeJsonString 可能为空或不是有效的 JSON 格式
	if (!AreaCodeJsonString.IsEmpty())
	{
		TSharedPtr<FJsonObject> JsonObject;
		TSharedRef<TJsonReader<TCHAR>> JsonReader = TJsonReaderFactory<TCHAR>::Create(AreaCodeJsonString);

		UE_LOG(LogPSOneUIManager, Display, TEXT("Parsing country code JSON data..."));
		if (FJsonSerializer::Deserialize(JsonReader, JsonObject) && JsonObject.IsValid())
		{
			const TArray<TSharedPtr<FJsonValue>>* JsonArrayPtr;
			if (JsonObject->TryGetArrayField(TEXT("areaCodes"), JsonArrayPtr))
			{
				const TArray<TSharedPtr<FJsonValue>>& JsonArray = *JsonArrayPtr;
				UE_LOG(LogPSOneUIManager, Display, TEXT("Found %d area codes in JSON data."), JsonArray.Num());
				for (const auto& JsonValue : JsonArray)
				{
					const TSharedPtr<FJsonObject> JsonObj = JsonValue->AsObject();
					if (JsonObj.IsValid())
					{
						FString Area;
						int32 Code = 0;
						int32 Id = 0;
						// 进行更安全的字段获取
						if (JsonObj->TryGetStringField(TEXT("areaName"), Area) &&
							JsonObj->TryGetNumberField(TEXT("areaCode"), Code) &&
							JsonObj->TryGetNumberField(TEXT("areaCodeId"), Id))
						{
							AreaCodes.Add(FPSOneAreaCode{Id, Code, Area});
						}
						else
						{
							UE_LOG(LogPSOneUIManager, Warning, TEXT("Skipping area code entry due to missing or invalid fields in JSON object."));
						}
					}
					else
					{
						UE_LOG(LogPSOneUIManager, Warning, TEXT("Found non-object value in areaCodes array. Skipping."));
					}
				}
			}
			else
			{
				UE_LOG(LogPSOneUIManager, Warning, TEXT("JSON data does not contain 'areaCodes' array field."));
			}
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to parse JSON country code data. Input string might be invalid."));
		}
	}
	else
	{
		UE_LOG(LogPSOneUIManager, Warning, TEXT("AreaCodeJsonString is empty. No country codes will be parsed from JSON."));
	}


	// 如果解析失败或 JSON 为空，并且全局国家代码缓存也为空，则使用默认区号
	if (CountryCodeJson.IsEmpty())
	{
		UE_LOG(LogPSOneUIManager, Warning, TEXT("No area codes available from JSON or cache. Using default area code for China (ID: 1, Code: 86)."));
		AreaCodes.Add(FPSOneAreaCode{1, 86, TEXT("中国大陆")}); // 使用 TEXT() 宏
	}


	UE_LOG(LogPSOneUIManager, Display, TEXT("Binding %d area codes to the selection widget."), AreaCodes.Num());
	SelectAreaCodeWidget->BindData(AreaCodes);

	UE_LOG(LogPSOneUIManager, Display, TEXT("Adding country code selection widget to viewport and setting keyboard focus..."));
	AddWidgetToViewport(SelectAreaCodeWidget);
	SelectAreaCodeWidget->SetKeyboardFocus();

	// 隐藏传入的 Widget
	PushHiddenWidget(WidgetToHide);

	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowCountryCodeSelection process completed successfully."));
}

void UPSOneUIManager::ShowVerificationCodeInput(UUserWidget* WidgetToHide, const FString& Title, const FString& PhoneOrEMail,
	TFunction<void()> OnSendCode, TFunction<void(UUserWidget* CurrentWidget, const FString& Code)> OnCodeEntered)
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Displaying verification code input UI..."));
	UE_LOG(LogPSOneUIManager, Display, TEXT("Attempting to load UPSOneInputCode widget class from path: %s"), *InputCodePath);

	// 加载验证码输入 Widget 类
	const auto WidgetClass = LoadClass<UPSOneInputCode>(nullptr, *InputCodePath);
	if (WidgetClass == nullptr)
	{
			UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to load UPSOneInputCode widget class from path: %s!"), *InputCodePath);
			return;
	}

	// !!!: GetCurrentWorld() 可能返回 nullptr
	UWorld* CurrentWorld = GetCurrentWorld();
	if (!CurrentWorld)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Cannot create widget: CurrentWorld is null."));
		return;
	}
	UPSOneInputCode* InputCodeWidget = CreateWidget<UPSOneInputCode>(CurrentWorld, WidgetClass);
	if (!InputCodeWidget)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to create UPSOneInputCode widget instance."));
		return;
	}

	UE_LOG(LogPSOneUIManager, Display, TEXT("Configuring InputCodeWidget: Title='%s', Target='%s'."), *Title, *PhoneOrEMail);
	InputCodeWidget->SetTitleText(Title);
	InputCodeWidget->SetTargetText(PhoneOrEMail); // 显示目标手机号或邮箱

	// 绑定发送验证码请求的回调
	InputCodeWidget->OnSendCode = [OnSendCode]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("UPSOneInputCode 'Send Code' requested by user."));
		if (OnSendCode)
		{
			OnSendCode();
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("OnSendCode callback is not bound. Cannot send verification code."));
		}
	};

	// 绑定验证码输入完成的回调
	InputCodeWidget->OnClicked = [this, WeakWidget = TWeakObjectPtr<UPSOneInputCode>(InputCodeWidget), OnCodeEntered](
									 const FString& Code)
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("UPSOneInputCode confirmed with code input (length: %d)."), Code.Len());
		if (OnCodeEntered)
		{
			// 传递 Widget 实例和输入的验证码
			OnCodeEntered(WeakWidget.Get(), Code); // 允许调用者处理 Widget (例如显示错误信息)
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("OnCodeEntered callback is not bound. Code input cannot be processed."));
			// 如果没有回调处理，考虑是否自动关闭窗口
			// AsyncTask(ENamedThreads::GameThread, [this, WeakWidget]() { ... });
		}
		// 注意：验证码输入后的窗口关闭逻辑通常由 OnCodeEntered 回调的实现者决定
	};

	// 绑定返回按钮的回调
	InputCodeWidget->OnBackspace = [this, WeakWidget = TWeakObjectPtr<UPSOneInputCode>(InputCodeWidget)]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("UPSOneInputCode Backspace pressed. Removing widget from viewport..."));
		// 异步移除 Widget 并显示上一个隐藏的 Widget
		AsyncTask(ENamedThreads::GameThread, [this, WeakWidget]()
		{
			if (WeakWidget.IsValid())
			{
				RemoveWidgetFromViewport(WeakWidget);
				PopHiddenWidgetAndShow();
			}
			else
			{
				UE_LOG(LogPSOneUIManager, Warning, TEXT("InputCodeWidget became invalid before backspace task executed."));
			}
		});
	};

	UE_LOG(LogPSOneUIManager, Display, TEXT("Adding verification code input widget to viewport and setting keyboard focus..."));
	AddWidgetToViewport(InputCodeWidget);
	InputCodeWidget->SetKeyboardFocus();

	// 隐藏传入的 Widget
	PushHiddenWidget(WidgetToHide);

	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowVerificationCodeInput process completed successfully."));
}

void UPSOneUIManager::ShowPwdChange(UUserWidget* WidgetToHide, bool HavePwd, struct FBindingInfo& BindingDetails)
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Displaying password change/set UI (HavePwd: %s)..."), HavePwd ? TEXT("true") : TEXT("false"));
	const auto WidgetClass = LoadClass<UPSOneUserCenterChangePwd>(nullptr, *ChangePwdPath);
	if (WidgetClass == nullptr)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to load UPSOneUserCenterChangePwd widget class! Path: %s"), *ChangePwdPath);
		return;
	}

	// !!!: GetCurrentWorld() 可能返回 nullptr
	UWorld* CurrentWorld = GetCurrentWorld();
	if (!CurrentWorld)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Cannot create widget: CurrentWorld is null."));
		return;
	}
	UE_LOG(LogPSOneUIManager, Display, TEXT("Creating UPSOneUserCenterChangePwd widget instance..."));
	UPSOneUserCenterChangePwd* ChangePasswordWidget = CreateWidget<UPSOneUserCenterChangePwd>(CurrentWorld, WidgetClass);
	if (!ChangePasswordWidget)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to create UPSOneUserCenterChangePwd widget instance."));
		return;
	}

	// 根据是否有密码设置不同的标题
	FText Title = HavePwd ? LOCTEXT("ChangeLoginPassword", "修改登录密码") : LOCTEXT("SetLoginPassword", "设置登录密码");
	ChangePasswordWidget->TitleText = Title;
	// 设置需要修改密码的账号信息（通常是手机号）
	ChangePasswordWidget->SetPhoneNumber(BindingDetails.FormattedBinding); // FormattedBinding 应该是脱敏后的号码

	// 绑定发送验证码请求的回调
	ChangePasswordWidget->OnSendCodeRequest = [this, BindingDetails]()
	{
		// 使用脱敏后的号码记录日志
		UE_LOG(LogPSOneUIManager, Display, TEXT("Password change UI requests sending verification code for account: %s"),
			*BindingDetails.FormattedBinding);
		if (!OnSendPhoneVerificationCode)
		{
			UE_LOG(LogPSOneUIManager, Error, TEXT("OnSendPhoneVerificationCode delegate is not bound! Cannot request verification code."));
			// !!!: 应该在 UI 上给用户反馈发送失败
			return;
		}
		// 调用外部逻辑发送验证码，使用原始绑定信息 CurrentBinding
		OnSendPhoneVerificationCode(0, BindingDetails.CurrentBinding, EVerificationCodeType::RestPwd, false, nullptr);
	};

	// 绑定确认修改/设置密码的回调
	ChangePasswordWidget->OnClickConfirmButton = [this,
													 WeakWidget = TWeakObjectPtr<UPSOneUserCenterChangePwd>(ChangePasswordWidget),
													 HavePwd](const FString& Code, const FString& NewPwd, const FString& ConfirmPwd)
	{
		UE_LOG(LogPSOneUIManager, Display,
			TEXT("Password change UI Confirm button clicked. Code length: %d, NewPwd length: %d, ConfirmPwd length: %d"), Code.Len(), NewPwd.Len(), ConfirmPwd.Len());
		// !!!: 可以在这里添加基础的密码复杂度校验日志，或者由 Widget 内部处理
		if (NewPwd.IsEmpty() || ConfirmPwd.IsEmpty())
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("New password or confirmation password is empty."));
			// UI 应该已经做了校验，但这里可以加一层日志
		}
		if (NewPwd != ConfirmPwd)
		{
		    UE_LOG(LogPSOneUIManager, Warning, TEXT("New password and confirmation password do not match."));
			// UI 应该已经做了校验
		}

		if (OnChangePassword)
		{
			// 调用外部逻辑处理密码修改/设置
			OnChangePassword(HavePwd, Code, NewPwd, ConfirmPwd, WeakWidget);
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("OnChangePassword callback is not set. Cannot process password change."));
			// !!!: 应该在 UI 上给用户反馈操作失败
		}
		// 注意：操作成功后的窗口关闭通常由 OnChangePassword 回调的实现者处理
	};

	// 绑定关闭/返回按钮的回调
	ChangePasswordWidget->OnClickCloseButton =
		[this, WeakWidget = TWeakObjectPtr<UPSOneUserCenterChangePwd>(ChangePasswordWidget)]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Password change UI Close/Back button pressed. Removing widget from viewport..."));
		// 异步移除 Widget 并显示上一个隐藏的 Widget
		AsyncTask(ENamedThreads::GameThread, [this, WeakWidget]()
		{
			if (WeakWidget.IsValid())
			{
				RemoveWidgetFromViewport(WeakWidget);
				PopHiddenWidgetAndShow();
			}
			else
			{
				UE_LOG(LogPSOneUIManager, Warning, TEXT("ChangePasswordWidget became invalid before close task executed."));
			}
		});
	};

	UE_LOG(LogPSOneUIManager, Display, TEXT("Adding password change/set widget to viewport and setting keyboard focus..."));
	AddWidgetToViewport(ChangePasswordWidget);
	ChangePasswordWidget->SetKeyboardFocus();

	// 隐藏传入的 Widget
	PushHiddenWidget(WidgetToHide);
	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowPwdChange process completed successfully."));
}

// 更新设备列表信息
void UPSOneUIManager::UpdateDevices(const FString& InputDeviceJson)
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Received request to update device list data."));
	// !!!: InputDeviceJson 可能为空或格式错误，ValidateUserCenterWidget 之前的解析和使用应更健壮
	DevicesJson = InputDeviceJson; // 缓存原始 JSON 数据

	UPSOneUserCenter* UserCenter = nullptr;
	if (!ValidateUserCenterWidget(&UserCenter))
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Cannot update devices: UserCenter widget is not valid or not found."));
		return;
	}
	// 调用 UserCenter Widget 刷新设备列表显示
	UserCenter->RefreshDeviceData(InputDeviceJson);
	UE_LOG(LogPSOneUIManager, Display, TEXT("Device list data passed to UserCenter widget for refresh."));
}

// 更新用户信息
void UPSOneUIManager::UpdateUserInfo(const FString& UserInfo)
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Received request to update user information."));
	// !!!: UserInfo 可能为空或格式错误，ValidateUserCenterWidget 之前的解析和使用应更健壮
	UserInfoJson = UserInfo; // 缓存原始 JSON 数据

	UPSOneUserCenter* UserCenter = nullptr;
	if (!ValidateUserCenterWidget(&UserCenter))
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Cannot update user info: UserCenter widget is not valid or not found."));
		return;
	}
	UE_LOG(LogPSOneUIManager, Display, TEXT("Binding new user data to UserCenter widget..."));
	// 调用 UserCenter Widget 绑定新的用户数据
	UserCenter->BindData(UserInfo);
	UserCenter->RefreshData();
	UE_LOG(LogPSOneUIManager, Display, TEXT("User information updated in UserCenter widget."));
}

// 更新已绑定的第三方账号信息
void UPSOneUIManager::UpdateBoundAccounts(const TMap<int32, bool>& Accounts)
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Received request to update bound accounts status. Account count: %d"), Accounts.Num());
	BoundAccounts = Accounts; // 更新缓存的绑定状态

	UPSOneUserCenter* UserCenter = nullptr;
	if (!ValidateUserCenterWidget(&UserCenter))
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Cannot update bound accounts: UserCenter widget is not valid or not found."));
		return;
	}

	// 调用 UserCenter Widget 更新绑定账号的显示状态
	UserCenter->UpdateBoundAccounts(BoundAccounts);
	UE_LOG(LogPSOneUIManager, Display, TEXT("Bound accounts status updated in UserCenter widget."));
}

// 更新国家/地区代码的 JSON 缓存
void UPSOneUIManager::UpdateCountryCode(const FString& JsonStr)
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Updating cached country code JSON string."));
	// !!!: JsonStr 可能为空或格式错误，使用时需要校验
	CountryCodeJson = JsonStr;
}

// 从隐藏堆栈中弹出 Widget，直到找到用户中心 Widget
void UPSOneUIManager::PopHiddenWidgetsUntilUserCenter()
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Attempting to pop hidden widgets until UserCenter is reached and shown."));
	UPSOneUserCenter* UserCenter = nullptr;
	// 首先验证 UserCenter Widget 是否存在且有效
	if (!ValidateUserCenterWidget(&UserCenter))
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Cannot pop until UserCenter: UserCenter widget is not valid or not found. Aborting pop sequence."));
		// !!!: 这种情况可能意味着 UI 状态异常，是否需要重置或采取其他措施？
		return;
	}
	// 调用通用的弹出方法，将 UserCenter 作为目标
	RemoveHiddenWidgetsUntil(UserCenter);
	UE_LOG(LogPSOneUIManager, Display, TEXT("Popping hidden widgets until UserCenter completed (if UserCenter was hidden)."));
}

// 设置指定 UserWidget 的可见性 (特殊处理 UserCenter 的内容框)
void UPSOneUIManager::SetUserWidgetVisibility(TWeakObjectPtr<UUserWidget> Widget, ESlateVisibility Visibility)
{
	if (!Widget.IsValid())
	{
		UE_LOG(LogPSOneUIManager, Warning, TEXT("Attempted to set visibility for an invalid or destroyed widget pointer."));
		return;
	}

	// 尝试将 Widget 转换为用户中心类型
	if (auto UserCenter = Cast<UPSOneUserCenter>(Widget.Get()))
	{
		// 如果是用户中心，则控制其内部内容框的可见性，而不是整个 Widget
		UE_LOG(LogPSOneUIManager, Verbose, TEXT("Setting UserCenter's *content* visibility to %s."), *UEnum::GetValueAsString(Visibility));
		UserCenter->SetContentBoxVisibility(Visibility);
	}
	else
	{
		// 对于其他类型的 Widget，直接设置其根可见性
		UE_LOG(LogPSOneUIManager, Verbose, TEXT("Setting visibility of widget '%s' to %s."), *Widget->GetName(), *UEnum::GetValueAsString(Visibility));
		Widget->SetVisibility(Visibility);
	}
}

// 从隐藏堆栈顶部弹出一个 Widget 并使其可见、获得焦点
// 主要用于返回上一级页面的场景
TWeakObjectPtr<UUserWidget> UPSOneUIManager::PopHiddenWidgetAndShow()
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Attempting to pop and show the top widget from the hidden stack."));

	if (HiddenUIWidgetList.Num() == 0)
	{
		UE_LOG(LogPSOneUIManager, Warning, TEXT("Hidden widget stack is empty. Cannot pop. No previous UI to show."));
		// !!!: 这种情况可能表示 UI 流程异常，或者已经回到了最底层
		return nullptr;
	}

	// 从堆栈顶部弹出一个弱指针
	TWeakObjectPtr<UUserWidget> Widget = HiddenUIWidgetList.Pop();

	if (Widget.IsValid())
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Popped widget '%s' is valid. Setting visibility to Visible and focusing."), *Widget->GetName());
		// 设置 Widget 可见
		SetUserWidgetVisibility(Widget, ESlateVisibility::Visible);
		// 设置键盘焦点，使其可交互
		Widget->SetKeyboardFocus();
		UE_LOG(LogPSOneUIManager, Display, TEXT("Widget '%s' popped and shown successfully. New hidden stack size: %d"), *Widget->GetName(), HiddenUIWidgetList.Num());
	}
	else
	{
		UE_LOG(LogPSOneUIManager, Warning, TEXT("Popped widget from hidden stack was invalid or already destroyed. New hidden stack size: %d"), HiddenUIWidgetList.Num());
		// !!!: 堆栈中存在无效指针，可能需要清理或检查 PushHiddenWidget 的逻辑
		// 可以考虑如果弹出的是无效指针，是否继续弹出直到找到有效的，或者直接返回 null
	}
	return Widget; // 返回弹出的 Widget (可能是有效或无效的)
}

#undef LOCTEXT_NAMESPACE
