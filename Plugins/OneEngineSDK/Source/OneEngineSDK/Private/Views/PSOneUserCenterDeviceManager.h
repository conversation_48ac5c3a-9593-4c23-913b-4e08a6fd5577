﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PSOneFocusUserWidget.h"
#include "PSOneUserCenterDeviceManager.generated.h"

USTRUCT()
struct FOneOnlineDevice
{
	GENERATED_BODY()
	bool bIsCurrentDevice {false};
	int OSType {0};
	FString DeviceName;
	FString DeviceID;
};

/**
 *
 */
UCLASS()
class ONEENGINESDK_API UPSOneUserCenterDeviceManager : public UPSOneFocusUserWidget
{
	GENERATED_BODY()

public:
	UPROPERTY(meta=(BindWidget))
	class UTextBlock* TitleBlock;

	UPROPERTY(meta=(BindWidget))
	class UScrollBox* ScrollBox;

	bool bIsMainLand {false};

	void SetOnlineDevices(const TArray<FOneOnlineDevice>& Devices);

	TFunction<void(const FOneOnlineDevice&)> OnOfflineDeviceCallback;

	TFunction<void(TArray<UWidget*>)> OnSetupDeviceItemCallback;
protected:

	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;

	virtual void NativeOnRemovedFromFocusPath(const FFocusEvent& InFocusEvent) override;
	int SelectedIndex{-1};
};
