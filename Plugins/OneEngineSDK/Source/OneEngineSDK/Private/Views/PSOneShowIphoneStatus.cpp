// Fill out your copyright notice in the Description page of Project Settings.


#include "Views/PSOneShowIphoneStatus.h"

#include "PSOneConfirmButton.h"
#include "PSUserWidgetSettings.h"
#include "Components/HorizontalBox.h"
#include "Components/Image.h"
#include "Components/ScaleBox.h"
#include "Components/TextBlock.h"

void UPSOneShowIphoneStatus::SetBindText(const FString& Num)
{
	BindTextBlock->SetText(FText::FromString(Num));
}

bool UPSOneShowIphoneStatus::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	// 固定导航
	UE_LOG(LogTemp, Log, TEXT("Setting ActionButton's focus navigation always points to itself"));
	ActionButton->SetNavigationRuleExplicit(EUINavigation::Up, ActionButton);
	ActionButton->SetNavigationRuleExplicit(EUINavigation::Down, ActionButton);
	ActionButton->SetNavigationRuleExplicit(EUINavigation::Left, ActionButton);
	ActionButton->SetNavigationRuleExplicit(EUINavigation::Right, ActionButton);
	
	ActionButton->OnClickCallback = [this]()
	{
		if (OnClicked)
		{
			OnClicked();
		}
	};
	return true;
}

void UPSOneShowIphoneStatus::NativePreConstruct()
{
	Super::NativePreConstruct();
	UPSUserWidgetSettings* Settings = UPSUserWidgetSettings::Get();
	ScaleBox->SetUserSpecifiedScale(Settings->GetUIScaleRatio());
	{
		FSlateBrush Brush;
		UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSSquareTexture);
		UnbindIcon->SetBrush(Brush);
	}
	if (Settings->bEnterButtonAssignCircle)
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			EnterIcon->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			BackspaceIcon->SetBrush(Brush);
		}
	}
	else
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			EnterIcon->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			BackspaceIcon->SetBrush(Brush);
		}
	}
	if (HideUnbind)
	{
		UnbindBox->SetVisibility(ESlateVisibility::Collapsed);
	}
	else
	{
		UnbindBox->SetVisibility(ESlateVisibility::Visible);
	}
}

FReply UPSOneShowIphoneStatus::NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent)
{
	FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
	ActionButton->SetKeyboardFocus();
	return Reply;
}

FReply UPSOneShowIphoneStatus::NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent)
{
	if (UPSUserWidgetSettings::IsClickedEnterKey(InKeyEvent))
	{
		if (OnClicked)
		{
			OnClicked();
		}
	}
	else if (UPSUserWidgetSettings::IsClickedBackKey(InKeyEvent))
	{
		if (OnBackspace)
		{
			OnBackspace();
		}
	}
	else if (InKeyEvent.GetKey() == EKeys::Gamepad_FaceButton_Left)
	{
		if (HideUnbind == false)
		{
			if (OnUnbind)
			{
				OnUnbind();
			}
		}
	}
	return FReply::Handled();
}
