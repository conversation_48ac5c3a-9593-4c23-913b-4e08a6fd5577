﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PSOneFocusUserWidget.h"
#include "PSOneUserCenterIDInfo.generated.h"


USTRUCT()
struct FPSOneIDInfoStruct
{
	GENERATED_BODY()

	UPROPERTY()
	FText Name;

	UPROPERTY()
	FText ID;
};

UCLASS()
class ONEENGINESDK_API UPSOneUserCenterIDInfo : public UPSOneFocusUserWidget
{
	GENERATED_BODY()

public:
	// AvatarCell
	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterRightCellSubtitle_NF* NameCell;

	// NickCell
	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterRightCellSubtitle_NF* IDCell;

	void BindData(const FPSOneIDInfoStruct& InIDInfo);

protected:
	virtual bool NativeSupportsKeyboardFocus() const override;
};
