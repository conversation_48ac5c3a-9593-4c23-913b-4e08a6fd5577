﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "Blueprint/UserWidget.h"
#include "PSOneFocusUserWidget.generated.h"

UINTERFACE(MinimalAPI)
class UPSOneSaveFocusWidgetInterface : public UInterface
{
	GENERATED_BODY()
};

class ONEENGINESDK_API IPSOneSaveFocusWidgetInterface
{
	GENERATED_BODY()

public:
	virtual void SaveFocusWidget(UWidget* Widget) = 0;
};

UCLASS()
class ONEENGINESDK_API UPSOneFocusUserWidget : public UUserWidget
{
	GENERATED_BODY()
};
