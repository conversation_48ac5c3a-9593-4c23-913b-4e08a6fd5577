﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "PSOneDelAccount.h"
#include "PSOneFocusNavigator.h"
#include "PSUserWidgetSettings.h"
#include "PSOneConfirmButton.h"
#include "PSOneTextFieldBase.h"

#include "Components/Image.h"
#include "Components/ScaleBox.h"
#include "Components/TextBlock.h"

bool UPSOneDelAccount::Initialize()
{
    if (!Super::Initialize())
    {
        return false;
    }

    FocusNavigator = MakeShareable(new FPSOneFocusNavigator());
    FocusNavigator->AddFocusableWidget(InputTextField);
 
	InputTextField->OnTextChanged = [this](const FText &InText) {
		if (InText.EqualTo(FText::FromString("DELETE")) || InText.EqualTo(FText::FromString("delete")))
		{
			// Container ConfirmButton
			// 获取最后一个焦点
			auto LastWidget = FocusNavigator->FocusableWidgets.Last();
			if (LastWidget.Get() != ConfirmButton)
			{
				FocusNavigator->AddFocusableWidget(ConfirmButton);
				FocusNavigator->SetFocusLoop();
			} 
		} else {
			auto LastWidget = FocusNavigator->FocusableWidgets.Last();
			if (LastWidget.Get() == ConfirmButton)
			{
				FocusNavigator->FocusableWidgets.Remove(LastWidget);
				FocusNavigator->SetFocusLoop();
			}
		}
	};

    ConfirmButton->OnClickCallback = [this]()
    {
        if (OnConfirm)
        {
            OnConfirm();
        }
    };
    return true;
}

void UPSOneDelAccount::NativePreConstruct()
{
    Super::NativePreConstruct();
    UPSUserWidgetSettings *Settings = UPSUserWidgetSettings::Get();
	ScaleBox->SetUserSpecifiedScale(Settings->GetUIScaleRatio());

    if (Settings->bEnterButtonAssignCircle)
    {
        {
            FSlateBrush Brush;
            UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
            EnterIcon->SetBrush(Brush);
        }
        {
            FSlateBrush Brush;
            UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
            BackspaceIcon->SetBrush(Brush);
        }
    }
    else
    {
        {
            FSlateBrush Brush;
            UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
            EnterIcon->SetBrush(Brush);
        }
        {
            FSlateBrush Brush;
            UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
            BackspaceIcon->SetBrush(Brush);
        }
    }
}

FReply UPSOneDelAccount::NativeOnFocusReceived(const FGeometry &InGeometry, const FFocusEvent &InFocusEvent)
{
    FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
    FocusNavigator->OnFocus();
    return Reply;
}

FReply UPSOneDelAccount::NativeOnKeyDown(const FGeometry &InGeometry, const FKeyEvent &InKeyEvent)
{

    if (FocusNavigator->HandleKeyEvent(InKeyEvent))
    {
        return FReply::Handled();
    }
    else if (UPSUserWidgetSettings::IsClickedBackKey(InKeyEvent))
    {
        if (OnClickCloseButton)
        {
            OnClickCloseButton();
        }
    }
    return FReply::Handled();
}
