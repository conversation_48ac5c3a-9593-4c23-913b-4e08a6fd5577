// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Views/PSOneFocusUserWidget.h"
#include "PSOneInputPhoneNum.generated.h"


// 枚举
UENUM(BlueprintType)
enum class EPSOneInputPhoneNumType : uint8
{
	PhoneNum,
	Email
};




/**
 * 用于绑定手机号或邮箱的输入框0
 */
UCLASS()
class UPSOneInputPhoneNum : public UPSOneFocusUserWidget
{
	GENERATED_BODY()

public:

	UPROPERTY(meta=(BindWidget))
	class UScaleBox* ScaleBox;
	
	// 标题
	UPROPERTY(meta=(BindWidget))
	class UTextBlock* TitleTextBlock;

	UPROPERTY(meta=(BindWidget))
	class UPSOneButtonBase* AreaCodeButton;

	UPROPERTY(meta=(BindWidget))
	class UPSOneTextFieldBase* PhoneNumTextField;

	UPROPERTY(meta=(BindWidget))
	class UPSOneConfirmButton* ActionButton;

	// enter icon
	UPROPERTY(meta=(BindWidget))
	class UImage* EnterIcon;

	// backspace icon
	UPROPERTY(meta=(BindWidget))
	class UImage* BackspaceIcon;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EPSOneInputPhoneNumType InputType = EPSOneInputPhoneNumType::PhoneNum;

	// Select Area Code
	TFunction<void()> OnClickAreaCode;

	// 点击事件
	TFunction<void()> OnClickNext;

	// 点击事件
	TFunction<void()> OnBackspace;

	void SetAreaCodeId(int InId);
	int GetAreaCodeId() const;
	void SetAreaCode(int InCode);
	int GetAreaCode() const;
	FString GetPhoneNumber() const;
	virtual bool Initialize() override;
protected:
	virtual void NativePreConstruct() override;
	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;
	virtual FReply NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent) override;

private:
	int SelectAreaCodeId = 1;
	int SelectAreaCodeCode = 86;

	TSharedPtr<class FPSOneFocusNavigator> FocusNavigator;
	TSharedPtr<class FPSOneFocusNavigator> FocusNavigator1;
};
