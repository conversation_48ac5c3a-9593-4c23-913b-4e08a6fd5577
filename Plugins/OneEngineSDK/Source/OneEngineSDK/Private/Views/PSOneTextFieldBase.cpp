﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "PSOneTextFieldBase.h"

#include "PSUserWidgetSettings.h"
#include "Components/Border.h"
#include "Components/EditableText.h"
#include "Components/TextBlock.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Engine/Texture2D.h"
#include "TimerManager.h"

FString UPSOneTextFieldBase::GetContent() const
{
	return TextField->GetText().ToString();
}

void UPSOneTextFieldBase::SetContent(const FString &Text)
{
	TextField->SetText(FText::FromString(Text));
	UpdateHintTextVisibility(TextField->GetText());
}

void UPSOneTextFieldBase::SetHintText(const FText &Text)
{
	HintText = Text;
	HintTextBlock->SetText(HintText);
	UpdateHintTextVisibility(TextField->GetText());
}

bool UPSOneTextFieldBase::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	TextField->OnTextChanged.AddDynamic(this, &UPSOneTextFieldBase::InternalOnTextChanged);
	TextField->OnTextCommitted.AddDynamic(this, &UPSOneTextFieldBase::InternalOnTextCommitted);

	return true;
}

void UPSOneTextFieldBase::NativePreConstruct()
{
	Super::NativePreConstruct();
	TextField->SetClearKeyboardFocusOnCommit(false);

	TextField->SetText(DefaultText);
	TextField->SetIsPassword(bIsPassword);
	TextField->KeyboardType = VirtualKeyboardType;

	HintTextBlock->SetText(HintText);
	UpdateHintTextVisibility(TextField->GetText());
}

void UPSOneTextFieldBase::UpdateBorderBrush(UTexture2D *Texture)
{
	if (!Texture)
	{
		UE_LOG(LogTemp, Warning, TEXT("Texture is null in %s"), *GetName());
		return;
	}

	FSlateBrush Brush;
	Brush.SetResourceObject(Texture);
	Brush.ImageSize = FVector2D(Texture->GetSizeX(), Texture->GetSizeY());
	Brush.DrawAs = ESlateBrushDrawType::Box;
	Brush.Margin = FMargin(0.5f);
	BgBorder->SetBrush(Brush);
}

void UPSOneTextFieldBase::UpdateHintTextVisibility(const FText &Text)
{
	const bool bIsEmpty = Text.IsEmpty();
	HintTextBlock->SetVisibility(bIsEmpty ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Hidden);
}

void UPSOneTextFieldBase::NativeOnAddedToFocusPath(const FFocusEvent &InFocusEvent)
{
	Super::NativeOnAddedToFocusPath(InFocusEvent);
	if (bIsFocus)
		return;

	bIsFocus = true;
	if (OnFocusSwitch)
		OnFocusSwitch(this, bIsFocus);
	// 让内部 TextField 获取焦点
	// TextField->SetKeyboardFocus();
	UpdateBorderBrush(FocusTexture);
}

void UPSOneTextFieldBase::NativeOnRemovedFromFocusPath(const FFocusEvent &InFocusEvent)
{
	Super::NativeOnRemovedFromFocusPath(InFocusEvent);
	bIsFocus = false;
	if (OnFocusSwitch)
		OnFocusSwitch(this, bIsFocus);
	UpdateBorderBrush(NormalTexture);
}

FReply UPSOneTextFieldBase::NativeOnFocusReceived(const FGeometry &InGeometry, const FFocusEvent &InFocusEvent)
{
	FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
	return Reply;
}

FReply UPSOneTextFieldBase::NativeOnKeyDown(const FGeometry &InGeometry, const FKeyEvent &InKeyEvent)
{
	FReply Reply = Super::NativeOnKeyDown(InGeometry, InKeyEvent);
	if (UPSUserWidgetSettings::IsClickedEnterKey(InKeyEvent) && !TextField->HasKeyboardFocus())
	{
		TextField->SetKeyboardFocus();
	}
	return Reply;
}

void UPSOneTextFieldBase::InternalOnTextChanged(const FText &Text)
{
	UpdateHintTextVisibility(Text);
	if (OnTextChanged)
		OnTextChanged(Text);

	
	if (NextTickOnTextChanged)
	{
		GetWorld()->GetTimerManager().SetTimerForNextTick([this, Text]()
		{
			NextTickOnTextChanged(Text);
		});
	}
}

void UPSOneTextFieldBase::InternalOnTextCommitted(const FText &Text, ETextCommit::Type CommitType)
{
	UpdateHintTextVisibility(Text);
	if (OnTextCommitted)
		OnTextCommitted(Text);
	
	// 当触发焦点导航时，应该让其他组件获取焦点，
	// 而不应该再次获取焦点了
	// SetKeyboardFocus();
}

void UPSOneTextFieldBase::DelayFocus()
{
	TextField->SetKeyboardFocus();
}
