﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PSOneFocusUserWidget.h"
#include "PSOneUserAgreementPrompt.generated.h"

/**
 *
 */
UCLASS()
class ONEENGINESDK_API UPSOneUserAgreementPrompt : public UPSOneFocusUserWidget
{
	GENERATED_BODY()

public:
	UPROPERTY(meta=(BindWidget))
	class UScaleBox* ScaleBox;
	
	UPROPERTY(meta=(bindWidget))
	class UBackgroundBlur* BackgroundBlur;

	UPROPERTY(meta=(BindWidget))
	class UImage* AgreeImage;

	UPROPERTY(meta=(BindWidget))
	class UImage* DisagreeImage;

	UPROPERTY(meta=(BindWidget))
	class USizeBox* BgImageSizeBox;	

	UPROPERTY(meta=(BindWidget))
	class UImage* BgImage;

	UPROPERTY(meta=(BindWidget))
	class UTextBlock* TitleBlock;

	UPROPERTY(meta=(BindWidget))
	class UTextBlock* DescBlock;

	UPROPERTY(meta=(BindWidget))
	class UTextBlock* DescBlock_1;

	UPROPERTY(meta=(BindWidget))
	class UImage* Left1Image;

	UPROPERTY(meta=(BindWidget))
	class UImage* Right1Image;

	UPROPERTY(meta=(BindWidget))
	class UImage* TriangleImage;

	UPROPERTY(meta=(BindWidget))
	class UTextBlock* TitleBlock_2;

	UPROPERTY(meta=(BindWidget))
	class UTextBlock* DescBlock_2;

	UPROPERTY(meta=(BindWidget))
	class UPSOneButtonBase* BindAccountButton;

	UPROPERTY(meta=(BindWidget))
	class UPSOneButtonBase* CreateAccountButton;

	UPROPERTY(meta=(BindWidget))
	class UPSOneButtonBase* LoginWithPwrdButton;

	UPROPERTY(meta=(BindWidget))
	class UWidgetSwitcher* Switcher;

	UPROPERTY(meta=(BindWidget))
	class UHorizontalBox* ChildProtectionBox;

	UPROPERTY(EditAnywhere)
	bool bIsMainland{true};

	UPROPERTY(EditAnywhere)
	int32 StartIndex{0};

	UPROPERTY(EditAnywhere)
	int32 LoginOption;

	UPROPERTY(EditAnywhere)
	bool bShowBgImage{false};


	TFunction<void()> OnBindAccountCallback;

	TFunction<void()> OnCreateAccountCallback;

	TFunction<void()> OnLoginWithPwrdCallback;

	// call on game thread
	TFunction<void()> OnOpenUserAgreementCallback;

	TFunction<void()> OnOpenUserPrivacyCallback;

	TFunction<void()> OnOpenChildPrivacyCallback;

	// read and agree
	TFunction<void()> OnAgreeCallback;

	// call on game thread
	TFunction<void()> OnCloseCallback;

	void SwitchToPreparePage();

	virtual bool Initialize() override;

protected:

	virtual void NativePreConstruct() override;
	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;
	virtual void NativeOnFocusLost(const FFocusEvent& InFocusEvent) override;
	virtual FReply NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent) override;

private:
	class UPSOneButtonBase* GetBestFocusButton();

	TSharedPtr<class FPSOneFocusNavigator> FocusNavigator;
};
