﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "PSOneButtonBase.generated.h"

/**
 *
 */
UCLASS()
class ONEENGINESDK_API UPSOneButtonBase : public UUserWidget
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere)
	FText Title;

	//对齐方式
	UPROPERTY(EditAnywhere)
	TEnumAsByte<EHorizontalAlignment> Alignment = EHorizontalAlignment::HAlign_Left;

	UPROPERTY(meta=(BindWidgetOptional))
	class UHorizontalBox* TextHorizontalBox;

	UPROPERTY(EditAnywhere)
	int32 TextSize = 18.f;

	UPROPERTY(meta=(BindWidget))
	class UBorder* BgBorder;

	UPROPERTY(meta=(BindWidget))
	class UButton* Button;

	UPROPERTY(meta=(BindWidget))
	class UTextBlock* TextBlock;

	UPROPERTY(EditAnywhere)
	class UTexture2D* SelectedTexture;

	// normal
	UPROPERTY(EditAnywhere)
	class UTexture2D* NormalTexture;

	// onFocus
	UPROPERTY(EditAnywhere)
	class UTexture2D* FocusTexture;

	UPROPERTY(EditAnywhere)
	bool bIsSelected = false;

	UFUNCTION(BlueprintCallable)
	void SetTitle(const FText& InTitle);

	bool bIsFocus;

	void SetSelected(bool bSelected);
	// UUserWidget interface
	// button click callback
	TFunction<void()> OnClickCallback;

	TFunction<void(UUserWidget* Widget, bool bIsFocus)> OnFocusSwitch;

public:
	virtual bool Initialize() override;

protected:
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void NativeDestruct() override;

protected:
	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;
	virtual void NativeOnAddedToFocusPath(const FFocusEvent& InFocusEvent) override;
	virtual void NativeOnRemovedFromFocusPath(const FFocusEvent& InFocusEvent) override;

	virtual void ButtonDidClick();
	virtual void OnFocusSwitched(bool Focus);

private:
	// Callback
	UFUNCTION()
	void OnClick();

	// 辅助函数：设置边框纹理
	void SetBorderTexture(UBorder* Border, UTexture2D* Texture);
};
