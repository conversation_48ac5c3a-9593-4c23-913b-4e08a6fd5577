// Fill out your copyright notice in the Description page of Project Settings.


#include "Views/PSOneUserCenterRightCellSubtitle.h"

#include "PSOneFocusUserWidget.h"
#include "PSUserWidgetSettings.h"
#include "Components/Button.h"
#include "Components/Image.h"
#include "Components/SizeBox.h"
#include "Components/TextBlock.h"

void UPSOneUserCenterRightCellSubtitle::SetSubTitle(const FText& InSubTitle)
{
	SubTitle = InSubTitle;
	SubTextBlock->SetText(SubTitle);
}

void UPSOneUserCenterRightCellSubtitle::NativePreConstruct()
{
	Super::NativePreConstruct();
	if (Icon)
	{
		FSlateBrush Brush;
		UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Icon);
		IconImage->SetBrush(Brush);
		// IconImage->SetVisibility(ESlateVisibility::Visible);
		ImageBox->SetVisibility(ESlateVisibility::Visible);
	}
	else
	{
		ImageBox->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (HiddenArrow)
	{
		ArrowImage->SetVisibility(ESlateVisibility::Collapsed);
	}
	else
	{
		ArrowImage->SetVisibility(ESlateVisibility::Visible);
	}
	SubTextBlock->SetText(SubTitle);
	SubTextBlock->SetColorAndOpacity(SubTitleColor);
}

void UPSOneUserCenterRightCellSubtitle::NativeConstruct()
{
	Super::NativeConstruct();
}

void UPSOneUserCenterRightCellSubtitle::OnFocusSwitched(bool Focus)
{
	Super::OnFocusSwitched(Focus);
	if (!Focus)
	{
		return;
	}
	UObject* OuterObject = GetOuter();
	while (OuterObject)
	{
		if (OuterObject->IsA(UUserWidget::StaticClass()))
		{
			IPSOneSaveFocusWidgetInterface* SaveFocusWidget = Cast<IPSOneSaveFocusWidgetInterface>(OuterObject);
			if (SaveFocusWidget)
			{
				SaveFocusWidget->SaveFocusWidget(this);
				break;
			}
		}

		OuterObject = OuterObject->GetOuter();
	}
}
