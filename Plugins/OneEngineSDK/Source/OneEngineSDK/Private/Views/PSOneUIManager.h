﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "Components/SlateWrapperTypes.h"
#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Runtime/Launch/Resources/Version.h"

#if (ENGINE_MAJOR_VERSION == 5 && ENGINE_MINOR_VERSION >= 2)
#include "Engine/TimerHandle.h"
#else
#include "Engine/EngineTypes.h"
#endif

#include "PSOneUIManager.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogPSOneUIManager, Log, All);

/**
 * @class UPSOneUIManager
 * @brief 管理 PS One UI 相关的功能，包括各种 UI 界面的显示和隐藏，以及相关操作的处理
 */
UCLASS(Blueprintable, BlueprintType)
class ONEENGINESDK_API UPSOneUIManager : public UObject
{
	GENERATED_BODY()

public:
	/** 验证码类型 */
	enum class EVerificationCodeType : uint8
	{
		General = 0,
		Binding = 1,	  // 绑定
		Change = 2,		  // 修改
		Unbinding = 3,	  // 解绑
		RestPwd = 4,	  // 修改密码
	};

	/** 定义可管理的 UI 控件类型 */
	enum class EUIWidgetType : uint8
	{
		None = 0,
		UserAgreement = 1,
		Login = 2,
		RealName = 3,
		UserCenter = 4,
		DelTips = 5,

		Loading = 101,
		Toast = 102,
	};

	/** 绑定信息结构体 */
	struct FBindingInfo
	{
		// 电话还是邮箱
		bool bIsEmail = false;
		// 是否是首次绑定
		bool bIsFirstBinding = false;
		// 是否允许更换绑定
		bool bAllowChangeBinding = false;

		// 当前绑定
		FString CurrentBinding;
		// 当前绑定格式化
		FString FormattedBinding;

		// 之前绑定
		FString PreviousBinding;
		// 之前绑定格式化
		FString FormattedPreviousBinding;

		// 国家代码 ID
		int CountryCodeId = 0;

		// 国家代码
		int CountryCode = 0;
		// 国家名称
		FString CountryName;
	};

	/** 单例实例获取函数 */
	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	static UPSOneUIManager* Get();

	/** UI 控件管理 */
	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	void ShowLoading();

	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	void HideLoading();

	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	void ShowToast(const FText& Text, float Duration = 3.0f, bool HandleFocusWidget = false);

	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	void ShowLoginSuccessHint(const FText& Text);

	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	void ShowUserAgreement(bool bHasAgreed, int32 LoginOption, const FString& Title = TEXT(""), const FString& Content = TEXT(""));

	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	void HideUserAgreement();

	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	void SwitchToPreparePage();

	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	void ShowLogin(bool bIsMainland = false);

	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	void HideLogin();

	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	void ShowRealName();

	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	void HideRealName();

	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	void ShowUserCenter();

	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	void HideUserCenter();

	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	void ShowDelTips(const FString& Tips, const FString &Uid, const FString &Token);

	UFUNCTION(BlueprintCallable, Category = "PSOneUIManager")
	void HideDelTips();

	void UpdateBoundAccounts(const TMap<int32, bool>& Accounts);

	/** 环境设置与查询 */
	void SetMainlandEnv(bool bIsMainlandChina)
	{
		bIsMainlandEnv = bIsMainlandChina;
	}
	bool IsMainlandEnv() const
	{
		return bIsMainlandEnv;
	}

	/** 二维码管理 */
	void SetQrCodeValidity(bool bIsValid);
	void SetQrCodeImageData(const TArray<uint8>& QRCodeImageBinary);

	/** 设备与用户信息管理 */
	void UpdateDevices(const FString& DeviceJson);
	void UpdateUserInfo(const FString& UserInfo);
	

	void UpdateCountryCode(const FString& JsonStr);

	FString GetCountryCode() const
	{
		return CountryCodeJson;
	}

	/** 隐藏与显示 UI 控件 */
	void PopHiddenWidgetsUntilUserCenter();

	static void RemoveWidgetFromViewport(TWeakObjectPtr<class UUserWidget> Widget);
	static void AddWidgetToViewport(
		TWeakObjectPtr<class UUserWidget> Widget, ESlateVisibility Visibility = ESlateVisibility::Visible);

	/** 回调函数定义 */
	TFunction<void()> OnAccountBinding;
	TFunction<void()> OnPsnLogin;
	TFunction<void()> OnPwrdLogin;
	TFunction<void()> OnOpenUserAgreementURL;
	TFunction<void()> OnOpenPrivacyPolicyURL;
	TFunction<void()> OnOpenChildPrivacyPolicyURL;
	TFunction<void()> OnOpenLegalTermsURL;
	TFunction<void()> OnAgreementAccepted;

	TFunction<void(const FString& NickName, TWeakObjectPtr<class UUserWidget> CurrentWidget)> OnNicknameChange;

	TFunction<void(const FString& Url)> OnOpenWebView;

	TFunction<void(const FString& Username, const FString& Password)> OnLoginWithPwd;

	TFunction<void(const FString& FullName, const FString& IdentificationNumber)> OnLoginWithIdentityVerification;

	TFunction<void(const FString& PhoneNumber)> OnSendLoginVerificationCode;

	TFunction<void(const FString& PhoneNumber, const FString& VerificationCode)> OnLoginWithVerificationCode;

	TFunction<void(bool HavePwd, const FString& VerificationCode, const FString& NewPassword, const FString& ConfirmNewPassword,
		TWeakObjectPtr<class UUserWidget> CurrentWidget)>
		OnChangePassword;

	TFunction<void()> OnQRCodeActivated;
	TFunction<void()> OnQRCodeCanceled;
	TFunction<void()> OnLogout;

	TFunction<void(EUIWidgetType WidgetType, bool Visibility)> OnUIWidgetVisibilityChanged;

	TFunction<void(const FString& EventName, const TMap<FString, FString>& EventData)> OnTrackEvent;

	TFunction<void(int AreaCodeId,																		// 国家/地区区号 ID
		const FString& PhoneNumber,																		// 手机号码
		EVerificationCodeType VerifyType,																// 验证码类型
		bool NeedCheckPhoneNumber,																		// 是否需要校验手机号有效性
		TFunction<void(bool Success, const FString& JsonStr, int Code, const FString& Err)> Callback	// 请求结果回调函数
		)>
		OnSendPhoneVerificationCode;

	TFunction<void(int AreaCodeId,																		  // 国家/地区区号 ID
		const FString& PhoneNumber,																		  // 手机号码
		const FString& VerificationCode,																  // 验证码
		bool bNeedBind,																					  // 是否需要绑定手机号
		const FString& OldPhoneNumber,																	  // 旧手机号（换绑时使用）
		TFunction<void(bool Success, const FString& JsonStr, int Code, const FString& Err)> Callback)>	  // 验证结果回调函数
		OnValidateVerificationCodeAndBindPhone;

	// 发送邮箱验证码
	TFunction<void(const FString& Email, EVerificationCodeType VerifyType, TWeakObjectPtr<class UUserWidget> CurrentWidget,
		TFunction<void(bool Success, const FString& JsonStr, int Code, const FString& Err)> Callback)>
		OnSendEmailVerificationCode;

	// 解绑邮箱
	TFunction<void(const FString& Email, TWeakObjectPtr<class UUserWidget> CurrentWidget,
		TFunction<void(bool Success, const FString& JsonStr, int Code, const FString& Err)> Callback)>
		OnUnbindEmail;
	// 绑定邮箱
	TFunction<void(const FString& Email, const FString& VerificationCode, TWeakObjectPtr<class UUserWidget> CurrentWidget,
		TFunction<void(bool Success, const FString& JsonStr, int Code, const FString& Err)> Callback)>
		OnBindEmail;

	// 打开账号注销页面
	TFunction<void()> OnOpenAccountCancellation;

	// 用户中心标签页变化
	TFunction<void(int TabIndex)> OnUserCenterTabIndexChanged;

	// 离线设备
	TFunction<void(const FString& DeviceId)> OnOfflineDevice;

	// 检查是否允许解绑 PSN，返回值为 bool
	TFunction<bool()> OnCheckUnbindPSN;

	// 解绑 PSN
	TFunction<void(TFunction<void(bool Success)> Callback)> OnUnbindPSN;

	// 解绑手机号
	TFunction<void(const FString& AuthCode, TWeakObjectPtr<class UUserWidget> CurrentWidget,
		TFunction<void(bool Success, const FString& JsonStr, int Code, const FString& Err)> Callback)>
		OnUnbindPhoneNumber;

	// 申请删除账号
	TFunction<void(TFunction<void(bool Success)> Callback)> OnApplyDeleteAccount;

	// 点击删除账号按钮回调，
	TFunction<void(const FString &Uid,const FString &Token,TFunction<void(bool bShouldClose)> Callback)> OnClickDeleteAccount;

	// 点击恢复账号按钮回调
	TFunction<void(const FString &Uid,TFunction<void(bool bShouldClose)> Callback)> OnClickRestoreAccount;

	// 二次确认弹窗
	TFunction<void(const FString& Message, TFunction<void(bool bOk)> Callback)> OnShowConfirmDialog;

private:
	static UWorld* GetCurrentWorld();
	/** UI 控件实例与管理 */
	TMap<int32, TWeakObjectPtr<class UUserWidget>> UIWidgetInstanceMap;
	TArray<TWeakObjectPtr<class UUserWidget>> HiddenUIWidgetList;
	void PushHiddenWidget(TWeakObjectPtr<class UUserWidget> Widget);
	TWeakObjectPtr<class UUserWidget> PopHiddenWidgetAndShow();
	void RemoveHiddenWidgetsUntil(TWeakObjectPtr<class UUserWidget> Widget);
	void SetUserWidgetVisibility(TWeakObjectPtr<class UUserWidget> Widget, ESlateVisibility Visibility);

	/** 焦点栈管理 */
	TArray<TSharedPtr<SWidget>> FocusWidgetStack;
	FCriticalSection FocusStackMutex;
	TSharedPtr<SWidget> PushFocusedWidget();
	TSharedPtr<SWidget> PopFocusedWidget(TSharedPtr<SWidget> Widget = nullptr);

	/** UI 定时器管理 */
	TMap<int32, FTimerHandle> UIWidgetTimerMap;
	FThreadSafeCounter TimerIDGenerator;

	/** 广播 UI 可见性变化 */
	void BroadcastWidgetVisibilityChange(EUIWidgetType WidgetType, bool Visibility);

	/** 打开 Web 视图 */
	void OpenWebView(const FString& URL) const;

	/** 用户中心相关操作 */
	bool ValidateUserCenterWidget(class UPSOneUserCenter** OutUserCenter);
	void ShowNicknameChange();

	void ShowBindingStatus(class UUserWidget* WidgetToHide, struct FBindingInfo& BindingDetails);

	void ShowBinding(class UUserWidget* WidgetToHide, struct FBindingInfo& BindingDetails);

	void ShowCountryCodeSelection(class UUserWidget* WidgetToHide, const FString& AreaCodeJson,
		TFunction<void(int AreaCodeId, int AreaCode, const FString& Area)> OnAreaCodeSelected);

	void ShowVerificationCodeInput(class UUserWidget* WidgetToHide, const FString& Title, const FString& PhoneOrEMail,
		TFunction<void()> OnSendCode, TFunction<void(class UUserWidget* CurrentWidget, const FString& Code)> OnCodeEntered);

	void ShowPwdChange(class UUserWidget* WidgetToHide, bool HavePwd, struct FBindingInfo& BindingDetails);

	// 打开焦点调试信息
	void EnableFocusDebugInfo(bool bEnable);

	/** 私有成员变量 */
	FString DevicesJson;
	FString UserInfoJson;
	FString CountryCodeJson;

	bool bIsMainlandEnv = true;
	bool bIsQrCodeValid = false;

	/** 已绑定的账号列表 */
	TMap<int32, bool> BoundAccounts;
};
