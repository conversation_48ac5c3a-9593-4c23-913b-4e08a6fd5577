// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PSOneFocusUserWidget.h"
#include "Blueprint/UserWidget.h"
#include "PSOneRealNameVerification.generated.h"

/**
 *
 */
UCLASS()
class UPSOneRealNameVerification : public UPSOneFocusUserWidget
{
	GENERATED_BODY()

public:

	UPROPERTY(meta=(BindWidget))
	class UScaleBox* ScaleBox;

	UPROPERTY(meta=(bindWidget))
	class UBackgroundBlur* BackgroundBlur;

	UPROPERTY(meta=(BindWidget))
	class UPSOneTextFieldBase* NameTextField;

	UPROPERTY(meta=(BindWidget))
	class UPSOneTextFieldBase* IDTextField;

	UPROPERTY(meta=(BindWidget))
	class UPSOneConfirmButton* SubmitButton;

	// 确认 图标
	UPROPERTY(meta=(BindWidget))
	class UImage* EnterIcon;

	// 返回 图标
	UPROPERTY(meta=(BindWidget))
	class UImage* BackspaceIcon;

	TFunction<void(const FString& Name, const FString& ID)> OnSubmit;

	// 关闭
	TFunction<void()> OnClickCloseButton;

	virtual bool Initialize() override;

protected:
	virtual void NativePreConstruct() override;
	virtual FReply NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent) override;
	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;

	TSharedPtr<class FPSOneFocusNavigator> FocusNavigator;
};
