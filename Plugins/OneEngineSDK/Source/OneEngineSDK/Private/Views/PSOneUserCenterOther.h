﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PSOneFocusUserWidget.h"
#include "PSOneUserCenterOther.generated.h"

/**
 * 
 */
UCLASS()
class ONEENGINESDK_API UPSOneUserCenterOther : public UPSOneFocusUserWidget
{
	GENERATED_BODY()

public:
	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterRightCellSubtitle* DeleteAccountCell;

	TFunction<void()> OnClickDeleteAccount;
	
	virtual bool Initialize() override;
protected:
	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;
};
