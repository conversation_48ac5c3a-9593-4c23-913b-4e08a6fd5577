// Fill out your copyright notice in the Description page of Project Settings.

#include "Views/PSOneRealNameVerification.h"
#include "PSOneFocusNavigator.h"
#include "PSOneConfirmButton.h"
#include "PSOneTextFieldBase.h"
#include "PSUserWidgetSettings.h"
#include "Components/BackgroundBlur.h"
#include "Components/Image.h"
#include "Components/ScaleBox.h"

bool UPSOneRealNameVerification::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	FocusNavigator = MakeShareable(new FPSOneFocusNavigator());

	FocusNavigator->AddFocusableWidget(NameTextField);
	FocusNavigator->AddFocusableWidget(IDTextField);
	FocusNavigator->AddFocusableWidget(SubmitButton);
	FocusNavigator->SetFocusLoop();
	
	SubmitButton->OnClickCallback = [this]()
	{
		if (OnSubmit)
		{
			const FString Name = NameTextField->GetContent();
			const FString ID = IDTextField->GetContent();
			OnSubmit(Name, ID);
		}
	};
	return true;
}

void UPSOneRealNameVerification::NativePreConstruct()
{
	Super::NativePreConstruct();
	UPSUserWidgetSettings *Settings = UPSUserWidgetSettings::Get();
	ScaleBox->SetUserSpecifiedScale(Settings->GetUIScaleRatio());
	if (Settings->bEnableBlurBackground)
	{
		BackgroundBlur->SetVisibility(ESlateVisibility::Visible);
		BackgroundBlur->SetBlurStrength(Settings->BlurBackgroundStrength);
	}
	else
	{
		BackgroundBlur->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (Settings->bEnterButtonAssignCircle)
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			EnterIcon->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			BackspaceIcon->SetBrush(Brush);
		}
	}
	else
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			EnterIcon->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			BackspaceIcon->SetBrush(Brush);
		}
	}
}

FReply UPSOneRealNameVerification::NativeOnKeyDown(const FGeometry &InGeometry, const FKeyEvent &InKeyEvent)
{
	if (FocusNavigator->HandleKeyEvent(InKeyEvent))
	{
		return FReply::Handled();
	}
	else if (UPSUserWidgetSettings::IsClickedBackKey(InKeyEvent))
	{
		if (OnClickCloseButton)
		{
			OnClickCloseButton();
		}
	}
	return FReply::Handled();
}

FReply UPSOneRealNameVerification::NativeOnFocusReceived(const FGeometry &InGeometry, const FFocusEvent &InFocusEvent)
{
	FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
	FocusNavigator->OnFocus();
	return Reply;
}
