﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "PSOneScaleWidget.generated.h"

/**
 * 
 */
UCLASS()
class ONEENGINESDK_API UPSOneScaleWidget : public UUserWidget
{
	GENERATED_BODY()
public:
	UPROPERTY(meta=(BindWidget))
	class UScaleBox* ScaleBox;

protected:
	virtual void NativePreConstruct() override;
};
