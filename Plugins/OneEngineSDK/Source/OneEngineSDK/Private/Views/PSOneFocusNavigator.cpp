﻿#include "PSOneFocusNavigator.h"
#include "Blueprint/UserWidget.h"
#include "PSOneTextFieldBase.h"

FPSOneFocusNavigator::FPSOneFocusNavigator()
	: CurrentFocusIndex(-1)
	, NavigationOrientation(ENavigationOrientation::Vertical)
	, bHasFocus(false)
	, bAutoCleanInvalidReferences(true)
{
}

FPSOneFocusNavigator::~FPSOneFocusNavigator()
{
	ParentNavigator = nullptr;
	ChildrenNavigator = nullptr;
	FocusableWidgets.Empty();
}

void FPSOneFocusNavigator::SetEnableDebugLog(bool bEnable)
{
	bEnableDebugLog = bEnable;
}

void FPSOneFocusNavigator::SetNavigationOrientation(ENavigationOrientation Orientation)
{
	NavigationOrientation = Orientation;
}

ENavigationOrientation FPSOneFocusNavigator::GetNavigationOrientation() const
{
	return NavigationOrientation;
}

void FPSOneFocusNavigator::AddFocusableWidget(UUserWidget* Widget)
{
	if (Widget)
	{
		// 检查是否已添加该控件，避免重复
		for (const TWeakObjectPtr<UUserWidget>& ExistingWidget : FocusableWidgets)
		{
			if (ExistingWidget.Get() == Widget)
			{
				LogInfo(TEXT("Widget already added to focusable widgets."));
				return;
			}
		}
		UPSOneTextFieldBase* TextField = Cast<UPSOneTextFieldBase>(Widget);
		if (TextField)
		{
			TextField->NextTickOnTextChanged = [this](const FText& Text)
			{
				UE_LOG(LogTemp, Warning, TEXT("TextField NextTickOnTextChanged, Text: %s"), *Text.ToString());
				OnFocus();
			};
		}

		FocusableWidgets.Add(Widget);
		LogInfo(FString::Printf(TEXT("Added focusable widget, total count: %d"), FocusableWidgets.Num()));
	}
}

void FPSOneFocusNavigator::ReplaceFocusableWidgetByIndex(int32 Index, UUserWidget* NewWidget)
{
	if (NewWidget && Index >= 0 && Index < FocusableWidgets.Num())
	{
		FocusableWidgets[Index] = NewWidget;
		LogInfo(FString::Printf(TEXT("Replaced focusable widget at index: %d"), Index));
		if (CurrentFocusIndex == Index)
		{
			// 如果当前焦点在被替换的控件上，重新设置焦点
			SetFocusByIndex(Index);
		}
	}
}

bool FPSOneFocusNavigator::SetFocusByIndex(int32 Index)
{
	CleanInvalidReferences();

	if (FocusableWidgets.Num() == 0)
	{
		LogInfo(TEXT("Cannot set focus - No focusable widgets available"));
		return false;
	}

	if (!FocusableWidgets.IsValidIndex(Index) || !FocusableWidgets[Index].IsValid())
	{
		LogInfo(FString::Printf(TEXT("Failed to set focus to index: %d - Invalid index or widget"), Index));
		return false;
	}

	const int32 OldIndex = CurrentFocusIndex;
	CurrentFocusIndex = Index;

	// 计算实际移动方向，考虑循环导航
	bool bForward;
	if (OldIndex == -1)
	{
		// 初次设置焦点，默认向前
		bForward = true;
	}
	else if (CurrentFocusIndex == OldIndex + 1 ||
	         (CurrentFocusIndex == 0 && OldIndex == FocusableWidgets.Num() - 1))
	{
		// 向后移动一位或从末尾循环到开头
		bForward = true;
	}
	else
	{
		// 其他情况视为向前移动
		bForward = false;
	}

	// 获取焦点的控件
	UUserWidget* FocusWidget = FocusableWidgets[CurrentFocusIndex].Get();
	if (FocusWidget)
	{
		// 检查控件是否可见和启用
		if (!IsWidgetFocusable(FocusWidget))
		{
			LogInfo(FString::Printf(TEXT("Widget at index %d is not focusable (invisible or disabled)"), Index));
			// 尝试找到下一个可用控件
			if (!FindAndSetNextFocusableWidget(bForward ? EFocusMoveDirection::Right : EFocusMoveDirection::Left))
			{
				// 如果找不到可用控件，恢复原来的焦点
				CurrentFocusIndex = OldIndex;
				LogInfo(TEXT("No focusable widgets available, reverting to previous index"));
				return false;
			}
			return true;
		}

		FocusWidget->SetKeyboardFocus();
		LogInfo(FString::Printf(TEXT("Focus set to widget at index: %d, %s"), CurrentFocusIndex, *FocusWidget->GetName()));

		// 设置此导航器为活动导航器
		SetAsActiveFocusNavigator();
	}

	// 调用焦点改变回调
	HandleFocusChanged(OldIndex, CurrentFocusIndex);
	return true;
}

bool FPSOneFocusNavigator::MoveFocus(EFocusMoveDirection Direction)
{
	// 添加方向日志
	FString DirectionStr;
	switch (Direction)
	{
		case EFocusMoveDirection::Up:
			DirectionStr = TEXT("Up");
			break;
		case EFocusMoveDirection::Down:
			DirectionStr = TEXT("Down");
			break;
		case EFocusMoveDirection::Left:
			DirectionStr = TEXT("Left");
			break;
		case EFocusMoveDirection::Right:
			DirectionStr = TEXT("Right");
			break;
		default:
			DirectionStr = TEXT("Unknown");
			break;
	}
	// LogInfo(FString::Printf(TEXT("MoveFocus called with direction: %s"), *DirectionStr));

	// 获取当前拥有焦点的导航器
	FPSOneFocusNavigator* ActiveNav = GetCurrentFocusNavigator();
	if (!ActiveNav)
	{
		LogInfo(TEXT("No active navigator found. Using this navigator as active."));
		ActiveNav = this;
		// 设置此导航器为活动导航器
		SetAsActiveFocusNavigator();
	}
	// 如果当前导航器不是活动导航器，则将操作委托给活动导航器
	if (ActiveNav != this)
	{
		LogInfo(FString::Printf(TEXT("Delegating move focus to active navigator: %s"),
			ActiveNav ? TEXT("Valid") : TEXT("Null")));
		return ActiveNav->MoveFocus(Direction);
	}
	// 以下是当前导航器正在处理焦点移动的情况
	// 检查是否需要在父子级之间导航
	if (NavigationOrientation == ENavigationOrientation::Horizontal)
	{
		if (Direction == EFocusMoveDirection::Up)
		{
			if (TryNavigateToParent())
			{
				// LogInfo(TEXT("Navigated to parent using UP direction"));
				return true;
			}
		}
		else if (Direction == EFocusMoveDirection::Down)
		{
			if (TryNavigateToChild())
			{
				// LogInfo(TEXT("Navigated to children using DOWN direction"));
				return true;
			}
		}
	}
	else if (NavigationOrientation == ENavigationOrientation::Vertical)
	{
		if (Direction == EFocusMoveDirection::Left)
		{
			if (TryNavigateToParent())
			{
				// LogInfo(TEXT("Navigated to parent using LEFT direction"));
				return true;
			}
		}
		else if (Direction == EFocusMoveDirection::Right)
		{
			if (TryNavigateToChild())
			{
				// LogInfo(TEXT("Navigated to children using RIGHT direction"));
				return true;
			}
		}
	}

	if (FocusableWidgets.Num() == 0 || CurrentFocusIndex == -1)
	{
		LogInfo(TEXT("Cannot move focus - No focusable widgets or current focus is invalid"));
		return false;
	}

	// 正常的焦点移动
	int32 NewIndex = CurrentFocusIndex;

	// 根据方向计算下一个焦点索引
	switch (Direction)
	{
		case EFocusMoveDirection::Up:
			if (NavigationOrientation == ENavigationOrientation::Vertical)
			{
				NewIndex--;
				// LogInfo(TEXT("Moving focus UP"));
			}
			break;
		case EFocusMoveDirection::Down:
			if (NavigationOrientation == ENavigationOrientation::Vertical)
			{
				NewIndex++;
				// LogInfo(TEXT("Moving focus DOWN"));
			}
			break;
		case EFocusMoveDirection::Left:
			if (NavigationOrientation == ENavigationOrientation::Horizontal)
			{
				NewIndex--;
				// LogInfo(TEXT("Moving focus LEFT"));
			}
			break;
		case EFocusMoveDirection::Right:
			if (NavigationOrientation == ENavigationOrientation::Horizontal)
			{
				NewIndex++;
				// LogInfo(TEXT("Moving focus RIGHT"));
			}
			break;
	}
	// 循环焦点导航
	if (NewIndex < 0)
	{
		NewIndex = FocusableWidgets.Num() - 1;
		// LogInfo(TEXT("Wrapping to last item"));
	}
	else if (NewIndex >= FocusableWidgets.Num())
	{
		NewIndex = 0;
		// LogInfo(TEXT("Wrapping to first item"));
	}

	// 尝试设置焦点
	if (!SetFocusByIndex(NewIndex))
	{
		// 如果设置焦点失败，尝试找到下一个可用控件
		return FindAndSetNextFocusableWidget(Direction);
	}
	return true;
}

UUserWidget* FPSOneFocusNavigator::GetCurrentFocusWidget() const
{
	if (FocusableWidgets.IsValidIndex(CurrentFocusIndex) && FocusableWidgets[CurrentFocusIndex].IsValid())
	{
		return FocusableWidgets[CurrentFocusIndex].Get();
	}

	return nullptr;
}

int32 FPSOneFocusNavigator::GetCurrentFocusIndex() const
{
	return CurrentFocusIndex;
}

FPSOneFocusNavigator* FPSOneFocusNavigator::GetParent()
{
	return ParentNavigator;
}

void FPSOneFocusNavigator::SetParent(FPSOneFocusNavigator* InParent)
{
	// 检查循环引用
	if (InParent == this)
	{
		LogInfo(TEXT("Error: Cannot set self as parent"));
		return;
	}

	// 检查是否会形成循环引用
	FPSOneFocusNavigator* ParentCheck = InParent;
	while (ParentCheck)
	{
		if (ParentCheck == this)
		{
			LogInfo(TEXT("Error: Circular reference detected. Cannot set parent."));
			return;
		}
		ParentCheck = ParentCheck->GetParent();
	}

	ParentNavigator = InParent;
	LogInfo(FString::Printf(TEXT("Parent navigator set: %s"),
		InParent ? TEXT("Valid") : TEXT("Null")));
}

FPSOneFocusNavigator* FPSOneFocusNavigator::GetChildren()
{
	return ChildrenNavigator;
}

void FPSOneFocusNavigator::SetChildren(FPSOneFocusNavigator* InChild)
{
	// 检查循环引用
	if (InChild == this)
	{
		LogInfo(TEXT("Error: Cannot set self as child"));
		return;
	}

	// 检查是否会形成循环引用
	FPSOneFocusNavigator* ChildCheck = InChild;
	while (ChildCheck)
	{
		if (ChildCheck == this)
		{
			LogInfo(TEXT("Error: Circular reference detected. Cannot set child."));
			return;
		}
		ChildCheck = ChildCheck->GetChildren();
	}

	// 如果已经有子级，先清除其父级引用
	if (ChildrenNavigator)
	{
		ChildrenNavigator->ParentNavigator = nullptr;
	}

	ChildrenNavigator = InChild;
	LogInfo(FString::Printf(TEXT("Child navigator set: %s"),
		InChild ? TEXT("Valid") : TEXT("Null")));

	// 设置子级导航器的父级为当前导航器
	if (ChildrenNavigator)
	{
		ChildrenNavigator->ParentNavigator = this;
	}
}

bool FPSOneFocusNavigator::TryNavigateToChild()
{
	if (ChildrenNavigator != nullptr)
	{
		ChildrenNavigator->CleanInvalidReferences();

		if (ChildrenNavigator->FocusableWidgets.Num() > 0)
		{
			LogInfo(TEXT("Navigating to children"));
			bHasFocus = false;

			int32 ChildFocusIndex = ChildrenNavigator->GetCurrentFocusIndex();
			if (ChildFocusIndex == -1 || !ChildrenNavigator->FocusableWidgets.IsValidIndex(ChildFocusIndex) ||
			    !ChildrenNavigator->FocusableWidgets[ChildFocusIndex].IsValid())
			{
				// 子导航器没有有效的焦点，设置为第一个可用控件
				ChildFocusIndex = 0;
			}

			LogInfo(FString::Printf(TEXT("Child focus index: %d"), ChildFocusIndex));
			bool Success = ChildrenNavigator->SetFocusByIndex(ChildFocusIndex);

			// 如果设置失败，尝试找到下一个可用控件
			if (!Success)
			{
				Success = ChildrenNavigator->FindAndSetNextFocusableWidget();
			}

			if (Success)
			{
				ChildrenNavigator->SetAsActiveFocusNavigator();
				return true;
			}
		}
		else
		{
			LogInfo(TEXT("Cannot navigate to child - Child navigator has no widgets"));
		}
	}
	else
	{
		LogInfo(TEXT("Cannot navigate to child - Child navigator invalid"));
	}

	return false;
}

bool FPSOneFocusNavigator::TryNavigateToParent()
{
	if (ParentNavigator != nullptr)
	{
		// 清理父导航器中的无效引用
		ParentNavigator->CleanInvalidReferences();

		int32 ParentFocusIndex = ParentNavigator->GetCurrentFocusIndex();
		if (ParentFocusIndex == -1 || !ParentNavigator->FocusableWidgets.IsValidIndex(ParentFocusIndex) ||
		    !ParentNavigator->FocusableWidgets[ParentFocusIndex].IsValid())
		{
			// 父导航器没有有效的焦点，设置为第一个可用控件
			ParentFocusIndex = 0;
		}

		LogInfo(FString::Printf(TEXT("Navigating to parent at index: %d"), ParentFocusIndex));
		// 当前导航器失去焦点
		bHasFocus = false;

		// 设置父级导航器为活动导航器
		bool Success = ParentNavigator->SetFocusByIndex(ParentFocusIndex);

		// 如果设置失败，尝试找到下一个可用控件
		if (!Success)
		{
			Success = ParentNavigator->FindAndSetNextFocusableWidget();
		}

		if (Success)
		{
			ParentNavigator->SetAsActiveFocusNavigator();
			return true;
		}
	}
	LogInfo(TEXT("Cannot navigate to parent - Parent navigator invalid or has no widgets"));
	return false;
}

FPSOneFocusNavigator* FPSOneFocusNavigator::GetCurrentFocusNavigator()
{
	// 从根导航器开始查找拥有焦点的导航器
	FPSOneFocusNavigator* RootNavigator = this;
	while (RootNavigator->ParentNavigator != nullptr)
	{
		RootNavigator = RootNavigator->ParentNavigator;
	}
	// 从根导航器开始查找拥有焦点的导航器
	FPSOneFocusNavigator* FocusedNavigator = RootNavigator->FindCurrentFocusNavigator();
	if (FocusedNavigator)
	{
		return FocusedNavigator;
	}
	LogInfo(TEXT("No active navigator found, using current navigator"));
	return this;
}

FPSOneFocusNavigator* FPSOneFocusNavigator::FindCurrentFocusNavigator()
{
	// 如果此导航器拥有焦点，则返回 this
	if (bHasFocus)
	{
		LogInfo(TEXT("Found active focus in self"));
		return this;
	}
	// 遍历子级导航器
	if (ChildrenNavigator != nullptr)
	{
		FPSOneFocusNavigator* FoundNavigator = ChildrenNavigator->FindCurrentFocusNavigator();
		if (FoundNavigator)
		{
			LogInfo(TEXT("Found active focus in children"));
			return FoundNavigator;
		}
	}
	LogInfo(TEXT("No active focus found in children or self"));
	return nullptr;
}

void FPSOneFocusNavigator::SetAsActiveFocusNavigator()
{
	// 从根导航器开始，清除所有导航器的焦点状态
	FPSOneFocusNavigator* RootNavigator = this;
	while (RootNavigator->ParentNavigator != nullptr)
	{
		RootNavigator = RootNavigator->ParentNavigator;
	}

	// 递归清除所有导航器的焦点状态
	ClearFocusStatus(RootNavigator);
	// 设置此导航器为拥有焦点
	bHasFocus = true;
}

bool FPSOneFocusNavigator::HasFocus()
{
	return bHasFocus;
}

void FPSOneFocusNavigator::HandleFocusChanged(int32 OldIndex, int32 NewIndex)
{
	// 调用 C++回调
	if (OnFocusChanged.IsBound())
	{
		OnFocusChanged.Execute(OldIndex, NewIndex);
	}
	LogInfo(FString::Printf(TEXT("Focus changed from index %d to %d"), OldIndex, NewIndex));
}

void FPSOneFocusNavigator::HandleEnter()
{
	// 调用 C++回调
	if (OnEnterCallback.IsBound())
	{
		OnEnterCallback.Execute();
	}
	LogInfo(TEXT("Enter action executed"));
}

void FPSOneFocusNavigator::HandleBack()
{
	// 调用 C++回调
	if (OnBackCallback.IsBound())
	{
		OnBackCallback.Execute();
	}
	LogInfo(TEXT("Back action executed"));
}

void FPSOneFocusNavigator::LogInfo(const FString& Message) const
{
	// 输出日志
	if (bEnableDebugLog)
	{
		UE_LOG(LogTemp, Display, TEXT("[FocusNavigator] %s"), *Message);
	}
}

void FPSOneFocusNavigator::ClearFocusStatus(FPSOneFocusNavigator* Navigator)
{
	if (!Navigator)
	{
		return;
	}

	Navigator->bHasFocus = false;
	// 递归清除子级导航器的焦点状态
	if (Navigator->ChildrenNavigator != nullptr)
	{
		ClearFocusStatus(Navigator->ChildrenNavigator);
	}
}

void FPSOneFocusNavigator::CleanInvalidReferences()
{
	if (!bAutoCleanInvalidReferences)
	{
		return;
	}

	bool bRemovedAny = false;
	for (int32 i = FocusableWidgets.Num() - 1; i >= 0; --i)
	{
		if (!FocusableWidgets[i].IsValid())
		{
			// 记录当前删除的索引，以便调整当前焦点索引
			int32 RemovedIndex = i;
			FocusableWidgets.RemoveAt(i);
			LogInfo(FString::Printf(TEXT("Cleaned invalid widget reference at index %d"), i));
			bRemovedAny = true;

			// 调整当前焦点索引
			if (CurrentFocusIndex == RemovedIndex)
			{
				// 当前焦点控件被移除，需要重新设置焦点
				CurrentFocusIndex = -1;
			}
			else if (CurrentFocusIndex > RemovedIndex)
			{
				// 如果移除的索引小于当前焦点索引，需要调整当前焦点索引
				CurrentFocusIndex--;
			}
		}
	}

	// 如果有控件被移除且当前没有焦点，尝试设置新的焦点
	if (bRemovedAny && CurrentFocusIndex == -1 && FocusableWidgets.Num() > 0)
	{
		SetFocusByIndex(0);
	}
}

bool FPSOneFocusNavigator::IsWidgetFocusable(UUserWidget* Widget) const
{
	if (!Widget)
	{
		return false;
	}

	// 检查控件是否可见和启用
	return Widget->IsVisible() && Widget->GetIsEnabled();
}

bool FPSOneFocusNavigator::FindAndSetNextFocusableWidget(EFocusMoveDirection Direction)
{
	if (FocusableWidgets.Num() == 0)
	{
		return false;
	}

	// 从当前焦点开始，向指定方向查找下一个可用控件
	int32 StartIndex = (CurrentFocusIndex >= 0) ? CurrentFocusIndex : 0;
	int32 CurrentIndex = StartIndex;
	bool bForward = (Direction == EFocusMoveDirection::Down || Direction == EFocusMoveDirection::Right);

	// 尝试找到下一个可用控件
	do
	{
		CurrentIndex = bForward
			               ? (CurrentIndex + 1) % FocusableWidgets.Num()
			               : (CurrentIndex - 1 + FocusableWidgets.Num()) % FocusableWidgets.Num();

		if (FocusableWidgets.IsValidIndex(CurrentIndex) && FocusableWidgets[CurrentIndex].IsValid())
		{
			UUserWidget* Widget = FocusableWidgets[CurrentIndex].Get();
			if (IsWidgetFocusable(Widget))
			{
				return SetFocusByIndex(CurrentIndex);
			}
		}
	} while (CurrentIndex != StartIndex);

	// 没有找到可用控件
	LogInfo(TEXT("No focusable widgets found after search"));
	return false;
}

void FPSOneFocusNavigator::SetAutoCleanInvalidReferences(bool bEnable)
{
	bAutoCleanInvalidReferences = bEnable;
}

bool FPSOneFocusNavigator::OnFocus()
{
	LogInfo(TEXT("OnFocus called"));

	CleanInvalidReferences();

	if (FocusableWidgets.Num() == 0)
	{
		LogInfo(TEXT("Cannot focus - No focusable widgets available"));
		return false;
	}

	// 当前导航器应该成为活动导航器
	SetAsActiveFocusNavigator();

	if (CurrentFocusIndex == -1)
	{
		// 首次获得焦点，选择第一个控件
		LogInfo(TEXT("First focus, selecting first widget"));
		for (int32 i = 0; i < FocusableWidgets.Num(); ++i)
		{
			if (FocusableWidgets[i].IsValid() && IsWidgetFocusable(FocusableWidgets[i].Get()))
			{
				return SetFocusByIndex(i);
			}
		}

		LogInfo(TEXT("No focusable widgets found"));
		return false;
	}
	else
	{
		// 恢复之前的焦点
		LogInfo(FString::Printf(TEXT("Restoring previous focus at index: %d"), CurrentFocusIndex));

		// 检查当前焦点是否有效
		if (FocusableWidgets.IsValidIndex(CurrentFocusIndex) &&
		    FocusableWidgets[CurrentFocusIndex].IsValid() &&
		    IsWidgetFocusable(FocusableWidgets[CurrentFocusIndex].Get()))
		{
			return SetFocusByIndex(CurrentFocusIndex);
		}
		else
		{
			// 当前焦点无效，尝试找到下一个可用控件
			LogInfo(TEXT("Current focus invalid, searching for next focusable widget"));
			return FindAndSetNextFocusableWidget();
		}
	}
}

bool FPSOneFocusNavigator::HandleKeyEvent(const FKeyEvent& InKeyEvent)
{
	FKey Key = InKeyEvent.GetKey();

	// 处理方向键导航
	if (Key == EKeys::Up || Key == EKeys::Gamepad_DPad_Up || Key == EKeys::Gamepad_LeftStick_Up)
	{
		MoveFocus(EFocusMoveDirection::Up);
		return true;
	}

	if (Key == EKeys::Down || Key == EKeys::Gamepad_DPad_Down || Key == EKeys::Gamepad_LeftStick_Down)
	{
		MoveFocus(EFocusMoveDirection::Down);
		return true;
	}

	if (Key == EKeys::Left || Key == EKeys::Gamepad_DPad_Left || Key == EKeys::Gamepad_LeftStick_Left)
	{
		MoveFocus(EFocusMoveDirection::Left);
		return true;
	}

	if (Key == EKeys::Right || Key == EKeys::Gamepad_DPad_Right || Key == EKeys::Gamepad_LeftStick_Right)
	{
		MoveFocus(EFocusMoveDirection::Right);
		return true;
	}

	return false;
}

void FPSOneFocusNavigator::SetFocusLoop()
{
	if (FocusableWidgets.Num() <= 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("SetFocusLoop: No focusable widgets"));
		return;
	}

	if (FocusableWidgets.Num() == 1)
	{
		UE_LOG(LogTemp, Warning, TEXT("Only one focusable widget, disabling focus loop"));
		UUserWidget* FirstFocus = FocusableWidgets[0].Get();

		if (NavigationOrientation == ENavigationOrientation::Vertical)
		{
			// 上下导航固定
			FirstFocus->SetNavigationRuleExplicit(EUINavigation::Up, FirstFocus);
			FirstFocus->SetNavigationRuleExplicit(EUINavigation::Down, FirstFocus);
		}
		else
		{
			// 左右导航固定
			FirstFocus->SetNavigationRuleExplicit(EUINavigation::Left, FirstFocus);
			FirstFocus->SetNavigationRuleExplicit(EUINavigation::Right, FirstFocus);
		}
		return;
	}

	int32 StartIndex = 0;
	int32 EndIndex = FocusableWidgets.Num() - 1;
	// 查找第一个可用焦点
	while (StartIndex <= EndIndex)
	{
		if (IsWidgetFocusable(FocusableWidgets[StartIndex].Get()))
		{
			break;
		}
		StartIndex++;
	}

	// 获取最后一个可用焦点
	while (EndIndex >= StartIndex)
	{
		if (IsWidgetFocusable(FocusableWidgets[EndIndex].Get()))
		{
			break;
		}
		EndIndex--;
	}
	UE_LOG(LogTemp, Warning, TEXT("StartIndex: %d, EndIndex: %d"), StartIndex, EndIndex);

	if (StartIndex == EndIndex)
	{
		UE_LOG(LogTemp, Warning, TEXT("SetFocusLoop: Only one focusable widget"));
		UUserWidget* FirstFocus = FocusableWidgets[StartIndex].Get();
		if (NavigationOrientation == ENavigationOrientation::Vertical)
		{
			// 上下导航固定
			UE_LOG(LogTemp, Warning, TEXT("SetFocusLoop: Vertical"))
			FirstFocus->SetNavigationRuleExplicit(EUINavigation::Up, FirstFocus);
			FirstFocus->SetNavigationRuleExplicit(EUINavigation::Down, FirstFocus);
		}
		else
		{
			// 左右导航固定
			UE_LOG(LogTemp, Warning, TEXT("SetFocusLoop: Horizontal"))
			FirstFocus->SetNavigationRuleExplicit(EUINavigation::Left, FirstFocus);
			FirstFocus->SetNavigationRuleExplicit(EUINavigation::Right, FirstFocus);
		}
		return;
	}

	if (StartIndex < EndIndex)
	{
		UUserWidget* FirstFocus = FocusableWidgets[StartIndex].Get();
		UUserWidget* LastFocus = FocusableWidgets[EndIndex].Get();
		if (NavigationOrientation == ENavigationOrientation::Vertical)
		{
			// 配置方向焦点
			UE_LOG(LogTemp, Warning, TEXT("SetFocusLoop: Vertical"))
			FirstFocus->SetNavigationRuleExplicit(EUINavigation::Up, LastFocus);
			LastFocus->SetNavigationRuleExplicit(EUINavigation::Down, FirstFocus);
		}
		else
		{
			// 配置方向焦点
			UE_LOG(LogTemp, Warning, TEXT("SetFocusLoop: Horizontal"))
			FirstFocus->SetNavigationRuleExplicit(EUINavigation::Left, LastFocus);
			LastFocus->SetNavigationRuleExplicit(EUINavigation::Right, FirstFocus);
		}
	}
}