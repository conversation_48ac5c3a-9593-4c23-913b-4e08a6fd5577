﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "PSOneAvatarImage.h"

#include "PSUserWidgetSettings.h"
#include "Blueprint/AsyncTaskDownloadImage.h"
#include "Components/Image.h"

// 初始化静态成员变量
TMap<FString, UTexture2DDynamic*> UPSOneAvatarImage::TextureCache;


// 从内存缓存中获取纹理
bool UPSOneAvatarImage::GetTextureFromCache(const FString& URL, UTexture2DDynamic*& OutTexture)
{
	if (TextureCache.Contains(URL))
	{
		OutTexture = TextureCache[URL];
		return true;
	}
	return false;
}

// 添加纹理到内存缓存
void UPSOneAvatarImage::AddTextureToCache(const FString& URL, UTexture2DDynamic* Texture)
{
	if (!URL.IsEmpty() && Texture)
	{
		TextureCache.Add(URL, Texture);
		UE_LOG(LogTemp, Log, TEXT("Added texture to memory cache for URL: %s"), *URL);
	}
}

void UPSOneAvatarImage::NativePreConstruct()
{
	Super::NativePreConstruct();
	if (Image)
	{
		UpdateImage(Image);
	}
}

void UPSOneAvatarImage::HandleTextureDownloaded(UTexture2DDynamic* Texture)
{
	if (!Texture)
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to download image from URL: %s"), *ImageURL);
		ImageView->SetBrush(FSlateBrush());
		return;
	}
	UE_LOG(LogTemp, Log, TEXT("Success Download Image From URL: %s"), *ImageURL);
	AddTextureToCache(ImageURL, Texture);
	ImageView->SetBrushFromTextureDynamic(Texture);
}

void UPSOneAvatarImage::UpdateImage(UTexture2D* Texture)
{
	if (Texture)
	{
		if (Image != Texture)
		{
			Image = Texture;
		}

		FSlateBrush Brush;
		UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Texture);
		ImageView->SetBrush(Brush);
	}
}

void UPSOneAvatarImage::SetImageURL(const FString& URL)
{
	if (URL.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("URL is empty, can't load image"));
		ImageView->SetBrush(FSlateBrush());
		return;
	}

	// 首先检查内存缓存
	UTexture2DDynamic* CachedTexture = nullptr;
	if (GetTextureFromCache(URL, CachedTexture))
	{
		UE_LOG(LogTemp, Log, TEXT("Loading image from memory cache for URL: %s"), *URL);
		ImageView->SetBrushFromTextureDynamic(CachedTexture);
		ImageURL = URL;
	}

	// 即使有缓存，也在后台发起下载请求以更新缓存
	UE_LOG(LogTemp, Log, TEXT("Background downloading image for cache update: %s"), *URL);
	DownloadImageTask = UAsyncTaskDownloadImage::DownloadImage(URL);
	DownloadImageTask->OnSuccess.AddDynamic(this, &UPSOneAvatarImage::HandleTextureDownloaded);
	DownloadImageTask->OnFail.AddDynamic(this, &UPSOneAvatarImage::HandleTextureDownloaded);
}
