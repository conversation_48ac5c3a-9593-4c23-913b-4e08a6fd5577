﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "PSOneAvatarImage.generated.h"

/**
 *
 */
UCLASS()
class ONEENGINESDK_API UPSOneAvatarImage : public UUserWidget
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere)
	class UTexture2D* Image;

	UPROPERTY(meta=(BindWidget))
	class UImage* ImageView;

	UFUNCTION(BlueprintCallable)
	void UpdateImage(class UTexture2D* Texture);

	UFUNCTION(BlueprintCallable)
	void SetImageURL(const FString& URL);

protected:
	FString ImageURL;
	virtual void NativePreConstruct() override;

private:
	UFUNCTION()
	void HandleTextureDownloaded(UTexture2DDynamic* Texture);

	UPROPERTY()
	class UAsyncTaskDownloadImage* DownloadImageTask;

	// 内存缓存相关
	static TMap<FString, UTexture2DDynamic*> TextureCache;
	static bool GetTextureFromCache(const FString& URL, UTexture2DDynamic*& OutTexture);
	static void AddTextureToCache(const FString& URL, UTexture2DDynamic* Texture);
};
