﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PSOneFocusUserWidget.h"
#include "PSOneDelAccount.generated.h"

/**
 *
 */
UCLASS()
class ONEENGINESDK_API UPSOneDelAccount : public UPSOneFocusUserWidget
{
	GENERATED_BODY()
public:

	UPROPERTY(meta=(BindWidget))
	class UScaleBox* ScaleBox;
	
	UPROPERTY(meta = (BindWidget))
	class UTextBlock *TitleTextBlock;

	UPROPERTY(meta = (BindWidget))
	class UTextBlock *DescTextBlock;

	// InputTextField
	UPROPERTY(meta = (BindWidget))
	class UPSOneTextFieldBase *InputTextField;

	// ConfirmButton
	UPROPERTY(meta = (BindWidget))
	class UPSOneConfirmButton *ConfirmButton;

	// enter icon
	UPROPERTY(meta = (BindWidget))
	class UImage *EnterIcon;

	// backspace icon
	UPROPERTY(meta = (BindWidget))
	class UImage *BackspaceIcon;

	virtual bool Initialize() override;

	// 点击事件
	TFunction<void()> OnClickCloseButton;

	// 点击事件
	TFunction<void()> OnConfirm;

protected:
	virtual void NativePreConstruct() override;
	virtual FReply NativeOnFocusReceived(const FGeometry &InGeometry, const FFocusEvent &InFocusEvent) override;
	virtual FReply NativeOnKeyDown(const FGeometry &InGeometry, const FKeyEvent &InKeyEvent) override;

private:
	TSharedPtr<class FPSOneFocusNavigator> FocusNavigator;
};
