﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PSOneFocusUserWidget.h"
#include "PSOneUserCenterChangePwd.generated.h"

/**
 *
 */
UCLASS()
class ONEENGINESDK_API UPSOneUserCenterChangePwd : public UPSOneFocusUserWidget
{
	GENERATED_BODY()

public:

	UPROPERTY(meta=(BindWidget))
	class UScaleBox* ScaleBox;
	
	UPROPERTY(EditAnywhere)
	FText TitleText;

	// Title
	UPROPERTY(meta=(BindWidget))
	class UTextBlock* TitleBlock;

	// 验证码发送提示
	UPROPERTY(meta=(BindWidget))
	class UTextBlock* SendCodeTip;

	// 验证码输入框
	UPROPERTY(meta=(BindWidget))
	class UPSOneTextFieldBase* CodeInputTextField;

	// 设置新密码提示
	UPROPERTY(meta=(BindWidget))
	class UTextBlock* SetNewPwdTip;

	// 新密码输入框
	UPROPERTY(meta=(BindWidget))
	class UPSOneTextFieldBase* NewPwdTextField;;

	// 确认新密码提示
	UPROPERTY(meta=(BindWidget))
	class UPSOneTextFieldBase* ConfirmPwdTextField;;

	// 确认按钮
	UPROPERTY(meta=(BindWidget))
	class UPSOneButtonBase* ConfirmButton;

	// enter icon
	UPROPERTY(meta=(BindWidget))
	class UImage* EnterIcon;

	// backspace icon
	UPROPERTY(meta=(BindWidget))
	class UImage* BackspaceIcon;

	void SetPhoneNumber(const FString& PhoneNumber);

	// 发送验证码请求
	TFunction<void()> OnSendCodeRequest;
	// 点击确认按钮，验证码、新密码、确认密码
	TFunction<void(const FString& Code, const FString& NewPwd, const FString& ConfirmPwd)> OnClickConfirmButton;
	// 关闭按钮
	TFunction<void()> OnClickCloseButton;

	virtual bool Initialize() override;

protected:
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;
	virtual FReply NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent) override;

private:
	FString TargetPhoneNumber;
	// 定时器，用于验证码倒计时
	FTimerHandle TimerHandle;
	int32 CountDownTime = 60;
	void StartCountDown();
	void StopCountDown();

	TSharedPtr<class FPSOneFocusNavigator> FocusNavigator;
};
