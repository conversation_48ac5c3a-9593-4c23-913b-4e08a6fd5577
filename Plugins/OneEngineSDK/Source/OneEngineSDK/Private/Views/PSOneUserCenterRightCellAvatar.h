﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PSOneButtonBase.h"
#include "PSOneUserCenterRightCellAvatar.generated.h"

/**
 * 
 */
UCLASS()
class ONEENGINESDK_API UPSOneUserCenterRightCellAvatar : public UPSOneButtonBase
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere)
	class UTexture2D* ImageIcon;

	UPROPERTY(meta=(BindWidget))
	class UPSOneAvatarImage* AvatarImage;

	
	void UpdateAvatarImage(class UTexture2D* InTexture);

	void SetAvatarImageURL(const FString& URL);

protected:
	virtual void NativePreConstruct() override;
	virtual void OnFocusSwitched(bool Focus) override;
};
