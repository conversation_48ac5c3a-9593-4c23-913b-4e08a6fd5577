﻿#pragma once

#include "CoreMinimal.h"
#include "PSOneButtonBase.h"
#include "PSOneUserCenterRightCellAccountBind.generated.h"


UCLASS()
class ONEENGINESDK_API UPSOneUserCenterRightCellAccountBind: public UPSOneButtonBase
{
	GENERATED_BODY()
public:

	UPROPERTY(EditAnywhere)
	bool bIsBind;

	UPROPERTY(EditAnywhere)
	class UTexture2D* UnBindIcon;

	UPROPERTY(EditAnywhere)
	class UTexture2D* BindIcon;

	UPROPERTY(EditAnywhere)
	class UTexture2D* Icon;
	

	UPROPERTY(meta=(BindWidget))
	class UImage* IconImage;
	
	UPROPERTY(meta=(BindWidget))
	class UImage* BindImage;
	

	void UpdateBindIcon(bool bBind);


	virtual void NativePreConstruct() override;
	
};
