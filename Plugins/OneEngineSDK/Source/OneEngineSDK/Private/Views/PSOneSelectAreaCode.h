// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Views/PSOneFocusUserWidget.h"
#include "PSOneSelectAreaCode.generated.h"

USTRUCT(BlueprintType)
struct FPSOneAreaCode
{
	GENERATED_BODY()
	FPSOneAreaCode()
	{
		Id = 0;
		Code = 0;
		Area = TEXT("");
	}

	FPSOneAreaCode(int InId, int InCode, const FString& InArea)
	{
		Id = InId;
		Code = InCode;
		Area = InArea;
	}

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int32 Id;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int32 Code;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Area;
};


/**
 *
 */
UCLASS()
class UPSOneSelectAreaCode : public UPSOneFocusUserWidget
{
	GENERATED_BODY()

public:

	UPROPERTY(meta=(BindWidget))
	class UScaleBox* ScaleBox;
	
	// enter icon
	UPROPERTY(meta=(BindWidget))
	class UImage* EnterIcon;

	// backspace icon
	UPROPERTY(meta=(BindWidget))
	class UImage* BackspaceIcon;


	// 显示的区域
	UPROPERTY(meta=(BindWidget))
	class UScrollBox* ScrollBox;

	// 点击事件
	TFunction<void(const FPSOneAreaCode& AreaCode)> OnClicked;

	// 点击事件
	TFunction<void()> OnBackspace;

	// bind data
	UFUNCTION(BlueprintCallable)
	void BindData(const TArray<FPSOneAreaCode>& InAreaCodes);

	virtual bool Initialize() override;

protected:
	virtual void NativePreConstruct() override;
	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;
	virtual FReply NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent) override;

private:
	TArray<FPSOneAreaCode> AreaCodes;
	TSharedPtr<class FPSOneFocusNavigator> FocusNavigator;
};
