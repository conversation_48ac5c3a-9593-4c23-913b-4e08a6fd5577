﻿#pragma once
#include "CoreMinimal.h"
#include "Engine/DataAsset.h"
#include "PSUserWidgetSettings.generated.h"

UCLASS(BlueprintType)
class UPSUserWidgetSettings : public UDataAsset
{
	GENERATED_BODY()

public:
	UPSUserWidgetSettings();

	UFUNCTION(BlueprintPure, DisplayName="PSUserWidgetSettings")
	static UPSUserWidgetSettings* Get();

	// UI Z 轴顺序
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int32 ZOrder = 1000;
	
	// 是否启用模糊背景
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bEnableBlurBackground = true;

	// 模糊背景强度
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float BlurBackgroundStrength = 1.6f;

	// 使用自定义字体
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	class UFont* CustomFont;
	
	// 应该要被废弃了
	UPROPERTY(EditAnywhere)
	UTexture2D* AgreementPromptBackgroundTexture;

	UPROPERTY(BlueprintReadOnly)
	bool bEnterButtonAssignCircle = false;

	UPROPERTY(BlueprintReadOnly)
	UTexture2D* PSCircleTexture;

	UPROPERTY(BlueprintReadOnly)
	UTexture2D* PSCrossTexture;

	UPROPERTY(BlueprintReadOnly)
	UTexture2D* PSSquareTexture;

	UPROPERTY(BlueprintReadOnly)
	UTexture2D* PSTriangleTexture;

	UPROPERTY(BlueprintReadOnly)
	UTexture2D* PSL1Texture;

	UPROPERTY(BlueprintReadOnly)
	UTexture2D* PSR1Texture;

	UPROPERTY(BlueprintReadOnly)
	UTexture2D* PSR2Texture;

	bool bIsIwPlay {false};

	float GetUIScaleRatio();

	static void SetupBrushWithTexture2D_DrawImage(FSlateBrush& Brush, UTexture2D* Texture);
	static void SetupBrushWithTexture2D_DrawBox(FSlateBrush& Brush, UTexture2D* Texture);
	static void SetupBrushWithTexture2D(FSlateBrush& Brush, UTexture2D* Texture, ESlateBrushDrawType::Type DrawType = ESlateBrushDrawType::Box, float Margin = 0.5f);
	static bool IsClickedEnterKey(const FKeyEvent& InKeyEvent);
	static bool IsClickedBackKey(const FKeyEvent& InKeyEvent);


// 	static UTexture2DDynamic* LoadImageToTexture2DDy(const FString& ImagePath);
// 	static void SaveImageFromTexture2DDy(UTexture2DDynamic* InDyTex, const FString& SavePath);
// 	
// private:
// 	static bool LoadImageToData(const FString& ImagePath, TArray64<uint8>& OutData, int32& Width, int32& Height);
// 	static bool SaveImageFromRawData(TArray64<uint8>& RawData, const FString& SavePath, const int32& Width,
// 									 const int32& Height);

};
