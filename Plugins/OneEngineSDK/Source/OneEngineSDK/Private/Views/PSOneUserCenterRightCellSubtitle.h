// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Views/PSOneButtonBase.h"
#include "PSOneUserCenterRightCellSubtitle.generated.h"

/**
 *  目前用于个人信息展示
 */
UCLASS()
class UPSOneUserCenterRightCellSubtitle : public UPSOneButtonBase
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere)
	class UTexture2D* Icon;

	UPROPERTY(meta=(BindWidget))
	class UImage* IconImage;

	UPROPERTY(meta=(BindWidget))
	class USizeBox* ImageBox;

	UPROPERTY(EditAnywhere)
	FText SubTitle;

	// SubTitle Color, 2B2B2BFF
	UPROPERTY(EditAnywhere)
	FLinearColor SubTitleColor{0.024158f, 0.024158f, 0.024158f, 1.0f};
	
	UPROPERTY(EditAnywhere)
	bool HiddenArrow{false};

	UPROPERTY(meta=(BindWidget))
	class UTextBlock* SubTextBlock;

	UPROPERTY(meta=(BindWidget))
	class UImage* ArrowImage;

	void SetSubTitle(const FText& InSubTitle);

protected:
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void OnFocusSwitched(bool Focus) override;
};
