﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "PSOneLoginSuccessToast.generated.h"

/**
 * 
 */
UCLASS()
class ONEENGINESDK_API UPSOneLoginSuccessToast : public UUserWidget
{
	GENERATED_BODY()

public:
	UPROPERTY(meta=(BindWidget))
	class UImage* Icon;

	UPROPERTY(meta=(BindWidget))
	class USizeBox* ImageSizeBox;

	UPROPERTY(meta=(BindWidget))
	class UTextBlock* TextBlock;

	void SetMessage(const FText& Message);
	
	void HiddenIcon(bool bHidden);
};
