﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PSOneFocusUserWidget.h"
#include "PSUserWidgetSettings.h"
#include "PSOneUserCenter.generated.h"

// 声明 Log 分类

DECLARE_LOG_CATEGORY_EXTERN(LogPSOneUserCenter, Log, All);


USTRUCT()
struct FPSOneUserCenterModel
{
	GENERATED_BODY()

	UPROPERTY()
	FString UserName;
	UPROPERTY()
	FString NickName;
	UPROPERTY()
	FString CellPhone;
	UPROPERTY()
	FString ShowCellPhone;
	UPROPERTY()
	FString ShowEmail;
	UPROPERTY()
	FString HeadImg;
	UPROPERTY()
	FString RealName;
	UPROPERTY()
	FString IDNumber;
	UPROPERTY()
	FString Email;
	UPROPERTY()
	FString UserID;
	UPROPERTY()
	FString OneID;
	UPROPERTY()
	FString Token;
	UPROPERTY()
	bool NewUser = false;
	UPROPERTY()
	FString UserIP;
	UPROPERTY()
	bool HavePwd = false;

	void BindData(const FString& JsonString);

	void ResetData();
};


UCLASS()
class ONEENGINESDK_API UPSOneUserCenter : public UPSOneFocusUserWidget
{
	GENERATED_BODY()

public:

	UPROPERTY(meta=(BindWidget))
	class UScaleBox* ScaleBox;

	UPROPERTY(meta=(bindWidget))
	class UBackgroundBlur* BackgroundBlur;

	UPROPERTY(meta=(bindWidget))
	class UVerticalBox* ContentBox;

	// 头像
	UPROPERTY(meta=(BindWidget))
	class UPSOneAvatarImage* AvatarImage;


	// Id
	UPROPERTY(meta=(BindWidget))
	class UTextBlock* MainIdText;


	UPROPERTY(meta=(BindWidget))
	class UTextBlock* NickTextBlock;

	// 用户名
	UPROPERTY(meta=(BindWidget))
	class UTextBlock* NickText;

	UPROPERTY(meta=(BindWidget))
	class UTextBlock* UIDTextBlock;

	// UID
	UPROPERTY(meta=(BindWidget))
	class UTextBlock* UIDText;

	// 绑定账号 cell
	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterLeftCell* BindAccountTabCell;

	// 账号信息 cell
	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterLeftCell* AccountTabCell;

	// 实名信息
	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterLeftCell* RealNameTabCell;

	// 设备管理
	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterLeftCell* DevicesTabCell;

	// 法律条款
	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterLeftCell* LawTabCell;


	// 其他
	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterLeftCell* OtherTabCell;

	// tabSwitcher
	UPROPERTY(meta=(BindWidget))
	class UWidgetSwitcher* TabSwitcher;

	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterBindManager* BindAccountManagerWidget;

	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterAccountInfo* AccountInfoWidget;

	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterIDInfo* IDInfoWidget;

	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterDeviceManager* DeviceManagerWidget;

	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterLegalTerms* LegalTermsWidget;

	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterOther* OtherWidget;


	// enter icon
	UPROPERTY(meta=(BindWidget))
	class UImage* EnterIcon;

	// backspace icon
	UPROPERTY(meta=(BindWidget))
	class UImage* BackspaceIcon;
	
	// 关闭按钮
	TFunction<void(int TabIndex)> OnTabIndexChanged;

	//切换账号
	TFunction<void()> OnSwitchAccount;

	// 关闭按钮
	TFunction<void()> OnClickCloseButton;

	void SetContentBoxVisibility(ESlateVisibility InVisibility);

	UFUNCTION(BlueprintCallable)
	void BindData(const FString& JsonString);

	UFUNCTION(BlueprintCallable)
	void RefreshData();
	void UpdateAvatar();
	void UpdateDisplayName();

	UFUNCTION(BlueprintCallable)
	void RefreshDeviceData(const FString& JsonString);

	UFUNCTION(BlueprintCallable)
	void UpdateBoundAccounts(const TMap<int32, bool>& Accounts);

	const FPSOneUserCenterModel& GetModel() const { return Model; }

	virtual bool Initialize() override;

	void SetupTabCallbacks();
	void UpdateTabSelection(int32 TabIndex);
	void SwitchToTabWidget(TSubclassOf<UUserWidget> WidgetClass);

	void SetIsMainLand(bool bIsMainLand);

	class FPSOneFocusNavigator* GetFocusNavigator() const { return FocusNavigator.Get(); }

	UPROPERTY(BlueprintReadWrite, EditAnywhere)

	bool bIsMainLand{false};

protected:
	FPSOneUserCenterModel Model;

	FString DevicesJsonString;

	virtual void NativePreConstruct() override;
	virtual void NativeDestruct() override;
	void SetupBackgroundBlur(UPSUserWidgetSettings* Settings);
	void SetupButtonIcons(UPSUserWidgetSettings* Settings);
	virtual void NativeConstruct() override;

	void RefreshDeviceList();
	void UpdateTabPageData();

	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;
	virtual FReply NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent) override;


private:
	TSharedPtr<class FPSOneFocusNavigator> FocusNavigator;
};
