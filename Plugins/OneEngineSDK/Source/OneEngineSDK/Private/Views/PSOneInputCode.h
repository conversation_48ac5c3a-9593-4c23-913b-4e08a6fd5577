// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Views/PSOneFocusUserWidget.h"
#include "PSOneInputCode.generated.h"

/**
 *
 */
UCLASS()
class UPSOneInputCode : public UPSOneFocusUserWidget
{
	GENERATED_BODY()

public:

	UPROPERTY(meta=(BindWidget))
	class UScaleBox* ScaleBox;
	
	UPROPERTY(meta=(BindWidget))
	class UTextBlock* TitleTextBlock;

	void SetTitleText(const FString& Text);


	UPROPERTY(meta=(BindWidget))
	class UPSOneTextFieldBase* CodeTextField;


	UPROPERTY(meta=(BindWidget))
	class UPSOneConfirmButton* ActionButton;


	UPROPERTY(meta=(BindWidget))
	class UTextBlock* TargetTextBlock;


	UPROPERTY(meta=(BindWidget))
	class UHorizontalBox* BottomHorizontalBox;

	void SetTargetText(const FString& Text);

	UPROPERTY(meta=(BindWidget))
	class UImage* EnterIcon;

	// enter icon
	UPROPERTY(meta=(BindWidget))
	class UImage* SendCodeIcon;

	// backspace icon
	UPROPERTY(meta=(BindWidget))
	class UImage* BackspaceIcon;


	TFunction<void()> OnSendCode;

	TFunction<void(const FString& Code)> OnClicked;

	TFunction<void()> OnBackspace;

	virtual bool Initialize() override;

protected:
	FString TargetText;
	virtual void NativePreConstruct() override;
	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;
	virtual FReply NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent) override;

	TSharedPtr<class FPSOneFocusNavigator> FocusNavigator;
};
