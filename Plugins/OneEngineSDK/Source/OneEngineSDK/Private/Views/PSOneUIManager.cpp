﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "PSOneUIManager.h"

#include "Async/Async.h"
#include "Components/TextBlock.h"
#include "PSOneToast.h"
#include "PSUserWidgetSettings.h"
#include "Slate/SObjectWidget.h"
#include "TimerManager.h"
#include "Engine/Engine.h"
#include "Engine/GameViewportClient.h"
#include "Framework/Application/SlateApplication.h"


#if WITH_EDITOR
#include "Editor.h"
#endif

#define LOCTEXT_NAMESPACE "PSOneUIManager"

DEFINE_LOG_CATEGORY(LogPSOneUIManager);

namespace
{
// WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_Loading.WBP_ONE_Loading'
FString LoadingPath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_Loading.WBP_ONE_Loading_C'");
// WidgetBlueprint'/OneEngineSDK/Widget/WBP_One_Toast.WBP_ONE_Toast'
FString ToastPath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_Toast.WBP_ONE_Toast_C'");

}	 // namespace

UPSOneUIManager* UPSOneUIManager::Get()
{
	static UPSOneUIManager* Instance = nullptr;
	if (Instance == nullptr)
	{
		Instance = NewObject<UPSOneUIManager>();
		Instance->AddToRoot();
#if UE_BUILD_DEBUG || UE_BUILD_DEVELOPMENT
		Instance->EnableFocusDebugInfo(true);
#endif
	}
	return Instance;
}

void UPSOneUIManager::ShowLoading()
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Attempting to show Loading widget."));
	check(IsInGameThread());
	constexpr int32 LoadingWidgetIndex = static_cast<int32>(EUIWidgetType::Loading);

	if (UIWidgetInstanceMap.Contains(LoadingWidgetIndex))
	{
		const TWeakObjectPtr<UUserWidget> ExistingWidget = UIWidgetInstanceMap[LoadingWidgetIndex];
		if (ExistingWidget.IsValid())
		{
			UE_LOG(LogPSOneUIManager, Display, TEXT("Loading widget is already visible."));
			return;
		}

		UE_LOG(LogPSOneUIManager, Warning, TEXT("Found an invalid reference for the Loading widget. Removing it."));
		UIWidgetInstanceMap.Remove(LoadingWidgetIndex);
	}

	const TSubclassOf<UUserWidget> WidgetClass = LoadClass<UUserWidget>(nullptr, *LoadingPath);
	if (!WidgetClass)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to load Loading widget class from path: %s"), *LoadingPath);
		return;
	}

	UWorld* World = GetCurrentWorld();
	if (!World)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to get current world. Cannot create Loading widget."));
		return;
	}

	UUserWidget* Widget = CreateWidget<UUserWidget>(World, WidgetClass);
	if (!Widget)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to create Loading widget instance."));
		return;
	}

	PushFocusedWidget();
	FSlateApplication::Get().ClearKeyboardFocus();

	AddWidgetToViewport(Widget);
	UIWidgetInstanceMap.Add(LoadingWidgetIndex, Widget);
	UE_LOG(LogPSOneUIManager, Display, TEXT("Loading widget shown successfully."));
}

void UPSOneUIManager::HideLoading()
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Attempting to hide Loading widget."));
	check(IsInGameThread());
	constexpr int32 LoadingWidgetIndex = static_cast<int32>(EUIWidgetType::Loading);

	if (UIWidgetInstanceMap.Contains(LoadingWidgetIndex))
	{
		const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[LoadingWidgetIndex];
		UIWidgetInstanceMap.Remove(LoadingWidgetIndex);
		if (UserWidget.IsValid())
		{
			RemoveWidgetFromViewport(UserWidget);
			PopFocusedWidget();
			UE_LOG(LogPSOneUIManager, Display, TEXT("Loading widget hidden successfully."));
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("Loading widget reference was invalid before removal."));
			PopFocusedWidget();
		}
		return;
	}

	UE_LOG(LogPSOneUIManager, Display, TEXT("No Loading widget instance found to hide."));
}

void UPSOneUIManager::ShowToast(const FText& Text, const float Duration, bool HandleFocusWidget)
{
	check(IsInGameThread());
	const auto WidgetClass = LoadClass<UPSOneToast>(nullptr, *ToastPath);
	if (WidgetClass == nullptr)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to load Toast widget class from path: %s"), *ToastPath);
		return;
	}

	UWorld* World = GetCurrentWorld();
	if (!World)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to get current world. Cannot create Toast widget."));
		return;
	}

	UPSOneToast* Toast = CreateWidget<UPSOneToast>(World, WidgetClass);
	if (!Toast)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to create Toast widget instance."));
		return;
	}
	Toast->SetMessage(Text);
	AddWidgetToViewport(Toast);

	TSharedPtr<SWidget> FocusedWidget = nullptr;
	if (HandleFocusWidget)
	{
		FocusedWidget = PushFocusedWidget();
		FSlateApplication::Get().ClearKeyboardFocus();
	}

	TWeakObjectPtr<UUserWidget> WeakToast = Toast;
	int32 TimeId = TimerIDGenerator.Increment();
	auto& TimeHandle = UIWidgetTimerMap.FindOrAdd(TimeId);
	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowToast: '%s', ID: %d. Auto-hide after %.2f seconds."), *Text.ToString(), TimeId,
		Duration);

	World->GetTimerManager().SetTimer(
		TimeHandle,
		[this, TimeId, WeakToast, Text, FocusedWidget, HandleFocusWidget]()
		{
			if (WeakToast.IsValid())
			{
				UE_LOG(LogPSOneUIManager, Display, TEXT("HideToast: '%s', ID: %d. Removing now."), *Text.ToString(), TimeId);
				RemoveWidgetFromViewport(WeakToast);
			}
			else
			{
				UE_LOG(LogPSOneUIManager, Warning, TEXT("HideToast: ID: %d. Widget was already invalid."), TimeId);
			}
			UIWidgetTimerMap.Remove(TimeId);
			if (HandleFocusWidget)
			{
				PopFocusedWidget(FocusedWidget);
			}
		},
		Duration, false);
}

void UPSOneUIManager::PushHiddenWidget(TWeakObjectPtr<UUserWidget> Widget)
{
	if (!Widget.IsValid())
	{
		UE_LOG(LogPSOneUIManager, Warning, TEXT("Attempted to push an invalid widget to the hidden stack. Aborting."));
		return;
	}

	if (HiddenUIWidgetList.Num() > 0 && HiddenUIWidgetList.Top() == Widget)
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Widget is already at the top of the hidden stack. No action needed."));
		return;
	}

	HiddenUIWidgetList.Add(Widget);
	SetUserWidgetVisibility(Widget, ESlateVisibility::Collapsed);
	UE_LOG(LogPSOneUIManager, Display, TEXT("Widget pushed to hidden stack. Stack size: %d"), HiddenUIWidgetList.Num());
}

void UPSOneUIManager::RemoveHiddenWidgetsUntil(TWeakObjectPtr<UUserWidget> TargetWidget)
{
	if (HiddenUIWidgetList.Num() == 0)
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Hidden stack is empty. No widgets to remove."));
		return;
	}
	if (!TargetWidget.IsValid())
	{
		UE_LOG(LogPSOneUIManager, Warning, TEXT("Target widget for removal sequence is invalid. Aborting."));
		return;
	}

	UE_LOG(LogPSOneUIManager, Display, TEXT("Starting removal of hidden widgets until target is reached. Initial stack size: %d"),
		HiddenUIWidgetList.Num());

	int32 InitialCount = HiddenUIWidgetList.Num();
	int32 RemovedCount = 0;

	while (HiddenUIWidgetList.Num() > 0)
	{
		TWeakObjectPtr<UUserWidget> TopWidget = HiddenUIWidgetList.Pop();

		if (!TopWidget.IsValid())
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("Encountered an invalid widget in the hidden stack during removal. Skipping."));
			continue;
		}

		if (TopWidget == TargetWidget)
		{
			UE_LOG(LogPSOneUIManager, Display,
				TEXT("Target widget found. Setting it visible and focusing. Stopping removal sequence."));
			SetUserWidgetVisibility(TopWidget, ESlateVisibility::Visible);
			TopWidget->SetKeyboardFocus();
			RemovedCount = InitialCount - HiddenUIWidgetList.Num() - 1;
			break;
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Verbose, TEXT("Removing non-target widget from hidden stack and viewport."));
			RemoveWidgetFromViewport(TopWidget);
		}
	}

	UE_LOG(LogPSOneUIManager, Display,
		TEXT("Hidden widget removal sequence finished. Widgets removed (excluding target): %d. Final stack size: %d"), RemovedCount,
		HiddenUIWidgetList.Num());
}

void UPSOneUIManager::AddWidgetToViewport(TWeakObjectPtr<UUserWidget> Widget, ESlateVisibility Visibility)
{
	if (Widget.IsValid())
	{
		int32 ZOrder = UPSUserWidgetSettings::Get()->ZOrder;
		UE_LOG(LogPSOneUIManager, Verbose, TEXT("Adding widget to viewport with ZOrder: %d, Visibility: %s"), ZOrder,
			*UEnum::GetValueAsString(Visibility));
		Widget->AddToViewport(ZOrder);
		Widget->SetVisibility(Visibility);
	}
	else
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to add widget to viewport: Widget reference is invalid!"));
	}
}

void UPSOneUIManager::RemoveWidgetFromViewport(TWeakObjectPtr<UUserWidget> Widget)
{
	if (Widget.IsValid())
	{
		UE_LOG(LogPSOneUIManager, Verbose, TEXT("Removing widget from viewport."));

#if (ENGINE_MAJOR_VERSION == 5 && ENGINE_MINOR_VERSION >= 1)
		Widget->RemoveFromParent();
#else
		Widget->RemoveFromViewport();
#endif
	}
	else
	{
		UE_LOG(LogPSOneUIManager, Warning, TEXT("Attempted to remove an invalid widget reference from viewport."));
	}
}

TSharedPtr<SWidget> UPSOneUIManager::PushFocusedWidget()
{
	const TSharedPtr<SWidget> CurrentWidget = FSlateApplication::Get().GetKeyboardFocusedWidget();
	if (!CurrentWidget.IsValid())
	{
		UE_LOG(LogPSOneUIManager, Warning, TEXT("No valid keyboard focused widget found when trying to push focus state."));
		return nullptr;
	}

	FScopeLock Lock(&FocusStackMutex);

	if (FocusWidgetStack.Num() > 0)
	{
		const auto TopWidget = FocusWidgetStack.Top();
		if (CurrentWidget == TopWidget)
		{
			UE_LOG(
				LogPSOneUIManager, Verbose, TEXT("Current focused widget is already the top of the focus stack. No push needed."));
			return CurrentWidget;
		}
	}

	FocusWidgetStack.Push(CurrentWidget);
	UE_LOG(LogPSOneUIManager, Verbose, TEXT("Focused widget pushed to stack. New stack size: %d"), FocusWidgetStack.Num());
	return CurrentWidget;
}

TSharedPtr<SWidget> UPSOneUIManager::PopFocusedWidget(TSharedPtr<SWidget> Widget)
{
	FScopeLock Lock(&FocusStackMutex);

	if (FocusWidgetStack.Num() > 0)
	{
		if (Widget.IsValid())
		{
			if (FocusWidgetStack.Top() == Widget)
			{
				FocusWidgetStack.Pop();
				FSlateApplication::Get().SetKeyboardFocus(Widget);
				UE_LOG(LogPSOneUIManager, Verbose,
					TEXT("Popping specific widget from focus stack and setting focus back to it. Stack size: %d"),
					FocusWidgetStack.Num());
				return Widget;
			}
			else
			{
				UE_LOG(LogPSOneUIManager, Warning,
					TEXT("Specified widget to pop is not the top of the focus stack. No pop action taken."));
				return nullptr;
			}
		}
		else
		{
			auto WidgetToFocus = FocusWidgetStack.Pop();
			if (WidgetToFocus.IsValid())
			{
				UE_LOG(LogPSOneUIManager, Verbose,
					TEXT("Popping top widget from focus stack and restoring focus to it. Stack size: %d"), FocusWidgetStack.Num());
				FSlateApplication::Get().SetKeyboardFocus(WidgetToFocus);
				return WidgetToFocus;
			}
			else
			{
				UE_LOG(LogPSOneUIManager, Warning, TEXT("Popped widget from focus stack was invalid. Cannot restore focus."));
				return nullptr;
			}
		}
	}
	else
	{
		UE_LOG(LogPSOneUIManager, Warning, TEXT("Focus stack is empty. Cannot pop focus state."));
		return nullptr;
	}
}

void UPSOneUIManager::BroadcastWidgetVisibilityChange(EUIWidgetType WidgetType, bool Visibility)
{
	if (OnUIWidgetVisibilityChanged)
	{
		UE_LOG(LogPSOneUIManager, Verbose, TEXT("Broadcasting visibility change: WidgetType %d, Visibility %s"),
			static_cast<int32>(WidgetType), Visibility ? TEXT("true") : TEXT("false"));

		OnUIWidgetVisibilityChanged(WidgetType, Visibility);

		// 一种临时的解决方案，用于解决显示的页面被游戏抢去焦点的问题
		const int32 Index = static_cast<int32>(WidgetType);
		if (UIWidgetInstanceMap.Contains(Index))
		{
			const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[Index];
			if (UserWidget.IsValid() && Visibility)
			{
				if (const auto World = GetCurrentWorld())
				{
					FTimerHandle TimerHandle;
					World->GetTimerManager().SetTimer(
						TimerHandle,
						[UserWidget]()
						{
							if (!UserWidget.IsValid())
								return;

							TSharedPtr<SWidget> MySlateWidget = UserWidget->GetCachedWidget();
							if (!MySlateWidget.IsValid())
								return;

							auto CurrentFocusedWidget = FSlateApplication::Get().GetKeyboardFocusedWidget();

							if (CurrentFocusedWidget.IsValid())
							{
								FWidgetPath WidgetPath;
								if (FSlateApplication::Get().FindPathToWidget(CurrentFocusedWidget.ToSharedRef(), WidgetPath))
								{
#if (ENGINE_MAJOR_VERSION == 5 && ENGINE_MINOR_VERSION >= 0)
									if (WidgetPath.ContainsWidget(MySlateWidget.Get()))
#else
									if (WidgetPath.ContainsWidget(MySlateWidget.ToSharedRef()))
#endif
									{
										UE_LOG(LogPSOneUIManager, Verbose,
											TEXT("Widget is already in the focus path after delay. No focus change needed."));
										return;
									}
								}
							}

							UE_LOG(LogPSOneUIManager, Verbose, TEXT("Setting keyboard focus to widget after delay."));
							UserWidget->SetKeyboardFocus();
						},
						1.0f, false);
				}
				else
				{
					UE_LOG(LogPSOneUIManager, Warning, TEXT("Cannot get world to set delayed focus timer."));
				}
			}
		}
	}
	else
	{
		UE_LOG(LogPSOneUIManager, Warning,
			TEXT("OnUIWidgetVisibilityChanged delegate is not bound. Cannot broadcast visibility change."));
	}
}

void UPSOneUIManager::OpenWebView(const FString& URL) const
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Requesting to open web view for URL: %s"), *URL);
	if (OnOpenWebView)
	{
		OnOpenWebView(URL);
	}
	else
	{
		UE_LOG(LogPSOneUIManager, Warning, TEXT("OnOpenWebView delegate is not bound. Cannot open web view."));
	}
}

UWorld* UPSOneUIManager::GetCurrentWorld()
{
	UWorld* World = nullptr;
#if WITH_EDITOR
	if (GIsEditor)
	{
		if (GPlayInEditorID == -1)
		{
			const FWorldContext* WorldContext = GEditor->GetPIEWorldContext(1);
			if (WorldContext == nullptr)
			{
				if (const UGameViewportClient* Viewport = GEngine->GameViewport)
				{
					World = Viewport->GetWorld();
				}
			}
			else
			{
				World = WorldContext->World();
			}
		}
		else
		{
			const FWorldContext* WorldContext = GEditor->GetPIEWorldContext(GPlayInEditorID);
			if (WorldContext == nullptr)
			{
				return nullptr;
			}
			World = WorldContext->World();
		}
	}
	else
	{
		World = GEngine->GetCurrentPlayWorld(nullptr);
	}
#else
	World = GEngine->GetCurrentPlayWorld(nullptr);
#endif
	return World;
}

void UPSOneUIManager::EnableFocusDebugInfo(bool bEnable)
{
	static FDelegateHandle Handle;
	auto& SlateApplication = FSlateApplication::Get();
	auto& FocusChangingEvent = SlateApplication.OnFocusChanging();
	if (bEnable)
	{
		if (!Handle.IsValid())
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("Adding focus changing callback..."));
			auto AddLambdaCallback = [](const FFocusEvent& FocusEvent, const FWeakWidgetPath& OldFocusedWidgetPath,
										 const TSharedPtr<SWidget>& OldFocusedWidget, const FWidgetPath& NewFocusedWidgetPath,
										 const TSharedPtr<SWidget>& NewFocusedWidget)
			{
				switch (FocusEvent.GetCause())
				{
					case EFocusCause::Navigation:
					case EFocusCause::SetDirectly:
						if (NewFocusedWidget.IsValid())
						{
							// 获取类名
							FString WidgetClassName = NewFocusedWidget->GetTypeAsString();

							// 检查是否是 UMG 部件
							FString UMGTypeName = TEXT("None");
							if (WidgetClassName.StartsWith(TEXT("SObjectWidget")))
							{
								// 尝试转换为 SObjectWidget
								TSharedPtr<SObjectWidget> ObjectWidget = StaticCastSharedPtr<SObjectWidget>(NewFocusedWidget);
								if (ObjectWidget.IsValid())
								{
									const UUserWidget* UserWidget = ObjectWidget->GetWidgetObject();
									if (UserWidget)
									{
										UMGTypeName = UserWidget->GetClass()->GetName();
									}
								}
							}

							// 构建 UI 路径
							FString UIPath;
							if (NewFocusedWidgetPath.IsValid())
							{
								for (int32 i = 0; i < NewFocusedWidgetPath.Widgets.Num(); ++i)
								{
									const FArrangedWidget& ArrangedWidget = NewFocusedWidgetPath.Widgets[i];
									if (!UIPath.IsEmpty())
									{
										UIPath += TEXT(" > ");
									}
									UIPath += ArrangedWidget.Widget->GetTypeAsString();

									// 如果是 UMG 控件，尝试添加 UMG 类名
									if (ArrangedWidget.Widget->GetTypeAsString().StartsWith(TEXT("SObjectWidget")))
									{
										// ArrangedWidget.Widget 为 TSharedRef 类型
										// 转换为 TSharedPtr<SObjectWidget>
										TSharedPtr<SWidget> WidgetPtr = ArrangedWidget.Widget;
										TSharedPtr<SObjectWidget> PathObjectWidget = StaticCastSharedPtr<SObjectWidget>(WidgetPtr);
										if (PathObjectWidget.IsValid())
										{
											UUserWidget* PathUserWidget = PathObjectWidget->GetWidgetObject();
											if (PathUserWidget)
											{
												UIPath += FString::Printf(TEXT("(%s)"), *PathUserWidget->GetClass()->GetName());
											}
										}
									}
								}
							}

							// 简化路径 - 如果太长，可以只保留最后几级
							const int32 MaxPathDepth = 4;	 // 显示路径的最后几级
							TArray<FString> PathSegments;
							UIPath.ParseIntoArray(PathSegments, TEXT(" > "), true);

							FString SimplifiedPath;
							if (PathSegments.Num() > MaxPathDepth)
							{
								SimplifiedPath = TEXT("... > ");
								for (int32 i = PathSegments.Num() - MaxPathDepth; i < PathSegments.Num(); ++i)
								{
									if (i > PathSegments.Num() - MaxPathDepth)
									{
										SimplifiedPath += TEXT(" > ");
									}
									SimplifiedPath += PathSegments[i];
								}
							}
							else
							{
								SimplifiedPath = UIPath;
							}

							const TCHAR* CauseStr = (FocusEvent.GetCause() == EFocusCause::SetDirectly)
														? TEXT("[被设置焦点]")
														: TEXT("[通过导航设置焦点]");

							if (UMGTypeName != TEXT("None"))
							{
								UE_LOG(LogPSOneUIManager, Warning, TEXT("%s: 类名：%s, UMG 类型：%s\n路径：%s"), CauseStr,
									*WidgetClassName, *UMGTypeName, *SimplifiedPath);
							}
							else
							{
								// ONE_LOG_PRINTF("%s: 类名：%s\n路径：%s", CauseStr, *WidgetClassName, *SimplifiedPath);
								UE_LOG(LogPSOneUIManager, Warning, TEXT("%s: 类名：%s\n路径：%s"), CauseStr, *WidgetClassName,
									*SimplifiedPath);
							}
						}
						break;
					case EFocusCause::Cleared:
						UE_LOG(LogPSOneUIManager, Warning, TEXT("[焦点被清除]"));
						break;
					default:
						break;
				}
			};

			Handle = FocusChangingEvent.AddLambda(AddLambdaCallback);
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("Focus changing callback already added."));
		}
	}
	else if (Handle.IsValid())
	{
		UE_LOG(LogPSOneUIManager, Warning, TEXT("Removing focus changing callback..."));
		FocusChangingEvent.Remove(Handle);
		Handle.Reset();
	}
}

#undef LOCTEXT_NAMESPACE
