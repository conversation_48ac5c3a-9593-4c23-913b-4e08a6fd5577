﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "PSOneChangNick.h"
#include "PSOneFocusNavigator.h"
#include "PSOneConfirmButton.h"
#include "PSOneTextFieldBase.h"
#include "PSUserWidgetSettings.h"
#include "Components/Image.h"
#include "Components/ScaleBox.h"

bool UPSOneChangNick::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	FocusNavigator = MakeShareable(new FPSOneFocusNavigator());
	FocusNavigator->SetNavigationOrientation(ENavigationOrientation::Vertical);
	FocusNavigator->AddFocusableWidget(NameTextField);
	FocusNavigator->AddFocusableWidget(ConfirmButton);
	FocusNavigator->SetFocusLoop();

	ConfirmButton->OnClickCallback = [this]()
	{
		if (OnConfirm)
		{
			const FString &NickName = NameTextField->GetContent();
			OnConfirm(NickName);
		}
	};
	return true;
}

void UPSOneChangNick::NativePreConstruct()
{
	Super::NativePreConstruct();

	UPSUserWidgetSettings *Settings = UPSUserWidgetSettings::Get();
	ScaleBox->SetUserSpecifiedScale(Settings->GetUIScaleRatio());

	if (Settings->bEnterButtonAssignCircle)
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			EnterIcon->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			BackspaceIcon->SetBrush(Brush);
		}
	}
	else
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			EnterIcon->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			BackspaceIcon->SetBrush(Brush);
		}
	}
}

void UPSOneChangNick::NativeConstruct()
{
	Super::NativeConstruct();
}

FReply UPSOneChangNick::NativeOnFocusReceived(const FGeometry &InGeometry, const FFocusEvent &InFocusEvent)
{
	FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);

	FocusNavigator->OnFocus();
	return Reply;
}

FReply UPSOneChangNick::NativeOnKeyDown(const FGeometry &InGeometry, const FKeyEvent &InKeyEvent)
{
	if (FocusNavigator->HandleKeyEvent(InKeyEvent))
	{
		return FReply::Handled();
	}
	else if (UPSUserWidgetSettings::IsClickedBackKey(InKeyEvent))
	{
		if (OnBackspace)
		{
			OnBackspace();
		}
	}
	return FReply::Handled();
}
