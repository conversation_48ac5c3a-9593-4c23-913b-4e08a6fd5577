﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "Async/Async.h"
#include "Components/TextBlock.h"
#include "PSOneDelTips.h"
#include "PSOneLogin.h"
#include "PSOneLoginSuccessToast.h"
#include "PSOneRealNameVerification.h"
#include "PSOneUIManager.h"
#include "PSUserWidgetSettings.h"
#include "TimerManager.h"

#define LOCTEXT_NAMESPACE "PSOneUIManagerLogin"

namespace
{
// 各个 UI Widget 资源的路径
// 登录界面 Widget 蓝图路径
// WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_Login.WBP_ONE_Login'
FString LoginPath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_Login.WBP_ONE_Login_C'");
// 实名认证界面 Widget 蓝图路径
// WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_RealNameVerification.WBP_ONE_RealNameVerification'
FString RealNameVerificationPath =
	TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_RealNameVerification.WBP_ONE_RealNameVerification_C'");
// 登录成功提示 Widget 蓝图路径
// WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_LoginSuccessToast.WBP_ONE_LoginSuccessToast'
FString LoginSuccessToastPath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_LoginSuccessToast.WBP_ONE_LoginSuccessToast_C'");
// 账号删除提示 Widget 蓝图路径
// WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_DelTips.WBP_ONE_DelTips'
FString DelTipsPath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_DelTips.WBP_ONE_DelTips_C'");

}	 // namespace

// 显示登录成功提示 (Toast)
void UPSOneUIManager::ShowLoginSuccessHint(const FText& Text)
{
	check(IsInGameThread());	// 必须在游戏线程调用

	// 加载登录成功提示的 Widget 类
	const auto WidgetClass = LoadClass<UPSOneLoginSuccessToast>(nullptr, *LoginSuccessToastPath);
	if (WidgetClass == nullptr)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("ShowLoginSuccessHint: Failed to load LoginSuccessToast widget class from path: %s"),
			*LoginSuccessToastPath);
		return;
	}

	// 提示显示时长
	constexpr float Duration = 3.0f;

	// 创建 Widget 实例
	UPSOneLoginSuccessToast* Toast = CreateWidget<UPSOneLoginSuccessToast>(GetCurrentWorld(), WidgetClass);
	// FIXME: CreateWidget 理论上也可能失败，虽然概率很低。可以考虑添加检查。
	if (!Toast)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("ShowLoginSuccessHint: Failed to create LoginSuccessToast widget instance."));
		return;
	}

	// 根据环境隐藏/显示图标
	Toast->HiddenIcon(!IsMainlandEnv());
	// 设置提示信息
	Toast->SetMessage(Text);
	// 添加到视口
	AddWidgetToViewport(Toast);

	// 使用弱指针，防止 Widget 被销毁后仍然访问
	TWeakObjectPtr<UUserWidget> Weak = Toast;
	int32 TimeId = TimerIDGenerator.Increment();
	auto& TimeHandle = UIWidgetTimerMap.FindOrAdd(TimeId);	  // 管理定时器句柄

	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowLoginSuccessHint: Showing hint '%s' (ID: %d). Will remove after %.1f seconds."),
		*Text.ToString(), TimeId, Duration);

	// 设置定时器，在指定时间后移除提示
	GetCurrentWorld()->GetTimerManager().SetTimer(
		TimeHandle,
		[this, TimeId, Weak, Text, Duration]()	  // Lambda 捕获必要的变量
		{
			if (Weak.IsValid())	   // 检查 Widget 是否仍然有效
			{
				UE_LOG(LogPSOneUIManager, Display,
					TEXT("ShowLoginSuccessHint: Timer finished for hint '%s' (ID: %d). Removing widget."), *Text.ToString(),
					TimeId);
				RemoveWidgetFromViewport(Weak);	   // 从视口移除
			}
			else
			{
				UE_LOG(LogPSOneUIManager, Warning,
					TEXT("ShowLoginSuccessHint: Timer finished for hint '%s' (ID: %d), but widget was already destroyed."),
					*Text.ToString(), TimeId);
			}
			UIWidgetTimerMap.Remove(TimeId);	// 从映射中移除定时器句柄
		},
		Duration,	 // 延迟时间
		false);		 // 非循环定时器
}

// 显示登录界面
void UPSOneUIManager::ShowLogin(bool bIsMainland)
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Entering ShowLogin function. IsMainland: %s"),
		bIsMainland ? TEXT("true") : TEXT("false"));
	check(IsInGameThread());	// 必须在游戏线程调用

	constexpr int32 LoginWidgetIndex = static_cast<int32>(EUIWidgetType::Login);

	// 检查登录界面是否已经存在且有效
	if (UIWidgetInstanceMap.Contains(LoginWidgetIndex))
	{
		const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[LoginWidgetIndex];
		if (UserWidget.IsValid())
		{
			UE_LOG(LogPSOneUIManager, Display,
				TEXT("ShowLogin: Login widget already exists and is valid. Aborting creation. Path: %s"), *LoginPath);
			// FIXME: 或许应该将现有窗口置于顶层？
			return;	   // 如果已存在，直接返回
		}
		else
		{
			// 存在于映射中但指针无效，说明之前的 Widget 可能已被销毁，需要移除旧条目
			UE_LOG(LogPSOneUIManager, Warning,
				TEXT("ShowLogin: Found an invalid Login widget pointer in UIWidgetInstanceMap. Removing stale entry and creating a "
					 "new one."));
			UIWidgetInstanceMap.Remove(LoginWidgetIndex);	 // 移除无效条目
		}
	}

	// 加载登录界面的 Widget 类
	const auto WidgetClass = LoadClass<UPSOneLogin>(nullptr, *LoginPath);
	if (WidgetClass == nullptr)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("ShowLogin: Failed to load login widget class! Path: %s"), *LoginPath);
		return;
	}

	// 保存当前拥有焦点的 Widget，以便后续恢复
	PushFocusedWidget();

	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowLogin: Creating new Login widget instance..."));

	// 创建登录 Widget 实例
	UPSOneLogin* Login = CreateWidget<UPSOneLogin>(GetCurrentWorld(), WidgetClass);
	Login->bIsIwPlay = UPSUserWidgetSettings::Get()->bIsIwPlay;
	// FIXME: CreateWidget 理论上也可能失败，虽然概率很低。可以考虑添加检查。
	if (!Login)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("ShowLogin: Failed to create Login widget instance."));
		PopFocusedWidget();	   // 创建失败，恢复之前的焦点
		return;
	}

	// 使用弱指针，主要用于回调中安全地访问 Login Widget
	auto WeakLogin = TWeakObjectPtr<UPSOneLogin>(Login);

	// 设置是否为大陆环境（影响 UI 显示）
	Login->bIsMainLand = bIsMainland;

	// 绑定登录按钮点击事件的回调
	Login->OnClickLoginButton = [this, WeakLogin](
									const FString& Account, const FString& Password, bool bIsPasswordLogin, bool bIsAgreePolicy)
	{
		// 检查 Login Widget 是否仍然有效
		if (!WeakLogin.IsValid())
		{
			UE_LOG(LogPSOneUIManager, Error, TEXT("ShowLogin: OnClickLoginButton callback invoked, but Login widget is invalid."));
			return;
		}

		FString LastFourChars = Account.Right(4);	 // 日志中隐藏部分账号信息
		UE_LOG(LogPSOneUIManager, Display,
			TEXT("ShowLogin: OnClickLoginButton triggered. Account ends with: ****%s, IsPasswordLogin: %s, IsAgreePolicy: %s"),
			*LastFourChars, bIsPasswordLogin ? TEXT("true") : TEXT("false"), bIsAgreePolicy ? TEXT("true") : TEXT("false"));

		// 检查用户是否同意了隐私政策
		if (!bIsAgreePolicy)
		{
			UE_LOG(LogPSOneUIManager, Display, TEXT("ShowLogin: User has not agreed to the policy. Showing reminder toast."));
			// 弹出提示要求用户阅读并同意
			const auto ToastText = LOCTEXT("ReadAndAgreeTerms", "请阅读并同意协议");
			constexpr float Duration = 3.0f;	// 使用常量定义持续时间
			ShowToast(ToastText, Duration, true);
			return;
		}

		// 根据登录方式调用不同的外部回调
		if (bIsPasswordLogin)
		{
			UE_LOG(LogPSOneUIManager, Display, TEXT("ShowLogin: Attempting login with password..."));
			if (OnLoginWithPwd)
			{
				OnLoginWithPwd(Account, Password);	  // 调用密码登录回调
			}
			else
			{
				// FIXME: 如果回调未设置，是否应该给用户反馈？或者禁止点击？
				UE_LOG(LogPSOneUIManager, Warning,
					TEXT("ShowLogin: OnLoginWithPwd callback is not set. Password login action will not be performed."));
			}
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Display, TEXT("ShowLogin: Attempting login with verification code..."));
			if (OnLoginWithVerificationCode)
			{
				OnLoginWithVerificationCode(Account, Password);	   // 调用验证码登录回调
			}
			else
			{
				// FIXME: 如果回调未设置，是否应该给用户反馈？或者禁止点击？
				UE_LOG(LogPSOneUIManager, Warning,
					TEXT("ShowLogin: OnLoginWithVerificationCode callback is not set. Verification code login action will not be "
						 "performed."));
			}
		}
	};

	// 绑定打开用户协议链接的回调
	Login->OpenUserAgreement = [this]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("ShowLogin: OpenUserAgreement triggered."));
		if (OnOpenUserAgreementURL)
		{
			OnOpenUserAgreementURL();	 // 调用打开用户协议的回调
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning,
				TEXT("ShowLogin: OnOpenUserAgreementURL callback is not set. Cannot open user agreement URL."));
		}
	};

	// 绑定打开隐私政策链接的回调
	Login->OpenPrivacyPolicy = [this]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("ShowLogin: OpenPrivacyPolicy triggered."));
		if (OnOpenPrivacyPolicyURL)
		{
			OnOpenPrivacyPolicyURL();	 // 调用打开隐私政策的回调
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning,
				TEXT("ShowLogin: OnOpenPrivacyPolicyURL callback is not set. Cannot open privacy policy URL."));
		}
	};

	// 绑定刷新二维码的回调
	Login->RefreshQrCode = [this]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("ShowLogin: RefreshQrCode triggered. Current QRCode validity: %s"),
			bIsQrCodeValid ? TEXT("Valid") : TEXT("Invalid"));

		// 如果二维码仍然有效，则无需刷新
		if (bIsQrCodeValid)
		{
			UE_LOG(LogPSOneUIManager, Display, TEXT("ShowLogin: QRCode is still valid, skipping refresh request."));
			return;
		}

		// 调用外部回调请求新的二维码
		if (OnQRCodeActivated)
		{
			UE_LOG(LogPSOneUIManager, Display, TEXT("ShowLogin: Requesting QR code refresh via OnQRCodeActivated callback..."));
			OnQRCodeActivated();
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("ShowLogin: OnQRCodeActivated callback is not set. Cannot refresh QR code."));
		}
	};

	// 绑定发送验证码按钮点击事件的回调
	Login->OnClickSendCodeButton = [this](const FString& Account)
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("ShowLogin: OnClickSendCodeButton triggered for account ending with: ****%s"),
			*Account.Right(4));
		if (OnSendLoginVerificationCode)
		{
			OnSendLoginVerificationCode(Account);	 // 调用发送验证码的回调
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning,
				TEXT("ShowLogin: OnSendLoginVerificationCode callback is not set. Cannot send verification code."));
		}
	};

	// 绑定关闭按钮点击事件的回调
	Login->OnClickCloseButton = [this]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("ShowLogin: OnClickCloseButton triggered. Hiding Login widget asynchronously..."));
		// 使用异步任务确保在游戏线程执行 HideLogin
		AsyncTask(ENamedThreads::GameThread, [this]() { HideLogin(); });
	};

	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowLogin: Adding Login widget to viewport and setting focus..."));
	AddWidgetToViewport(Login);										// 添加到视口
	BroadcastWidgetVisibilityChange(EUIWidgetType::Login, true);	// 广播 Widget 可见性变化事件
	Login->SetKeyboardFocus();										// 设置键盘焦点到该 Widget

	// 显示登录界面后，立即尝试激活/刷新一次二维码
	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowLogin: Activating initial QR code..."));
	if (OnQRCodeActivated)
	{
		UE_LOG(LogPSOneUIManager, Verbose, TEXT("ShowLogin: Calling OnQRCodeActivated for initial QR code."));
		OnQRCodeActivated();
	}
	else
	{
		UE_LOG(LogPSOneUIManager, Warning,
			TEXT("ShowLogin: OnQRCodeActivated callback is not set. Initial QR code activation skipped."));
	}

	// 将 Widget 实例添加到映射中进行管理
	UIWidgetInstanceMap.Add(LoginWidgetIndex, Login);
	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowLogin: Function completed successfully. Login widget is now visible."));
}

// 隐藏登录界面
void UPSOneUIManager::HideLogin()
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Entering HideLogin function..."));
	check(IsInGameThread());	// 必须在游戏线程调用

	// 如果是大陆环境，关闭登录界面时需要撤销二维码
	if (IsMainlandEnv())
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("HideLogin: Mainland environment detected. Canceling QR code..."));
		if (OnQRCodeCanceled)
		{
			OnQRCodeCanceled();	   // 调用撤销二维码的回调
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("HideLogin: OnQRCodeCanceled callback is not set. Cannot cancel QR code."));
		}
	}
	else
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("HideLogin: Non-mainland environment. Skipping QR code cancellation."));
	}

	constexpr int32 LoginWidgetIndex = static_cast<int32>(EUIWidgetType::Login);

	// 查找并移除登录界面 Widget
	if (UIWidgetInstanceMap.Contains(LoginWidgetIndex))
	{
		const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[LoginWidgetIndex];
		if (UserWidget.IsValid())	 // 确保 Widget 仍然有效
		{
			UE_LOG(LogPSOneUIManager, Display, TEXT("HideLogin: Found valid Login widget. Removing from viewport and map..."));
			RemoveWidgetFromViewport(UserWidget);							 // 从视口移除
			UIWidgetInstanceMap.Remove(LoginWidgetIndex);					 // 从映射中移除
			BroadcastWidgetVisibilityChange(EUIWidgetType::Login, false);	 // 广播 Widget 可见性变化事件
			PopFocusedWidget();												 // 恢复之前获得焦点的 Widget
			UE_LOG(LogPSOneUIManager, Display, TEXT("HideLogin: Login widget removed successfully."));
			return;
		}
		else
		{
			// 如果指针无效，说明 Widget 可能已被意外销毁
			UE_LOG(LogPSOneUIManager, Warning,
				TEXT("HideLogin: Found Login widget entry in map, but the pointer is invalid. Removing stale entry."));
			UIWidgetInstanceMap.Remove(LoginWidgetIndex);	 // 只移除无效条目
															 // 不需要 PopFocusedWidget，因为可能没有 Push 过或者焦点状态未知
		}
	}
	else	// 映射中不存在
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("HideLogin: No login widget instance found in the map. Nothing to hide."));
	}
	UE_LOG(LogPSOneUIManager, Display, TEXT("Exiting HideLogin function."));
}

// 设置二维码的有效性状态，并更新 UI
void UPSOneUIManager::SetQrCodeValidity(bool bIsValid)
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Entering SetQrCodeValidity function. Setting validity to: %s"),
		bIsValid ? TEXT("Valid") : TEXT("Invalid"));
	check(IsInGameThread());	// 必须在游戏线程调用

	bIsQrCodeValid = bIsValid;	  // 更新内部状态变量

	constexpr int32 LoginWidgetIndex = static_cast<int32>(EUIWidgetType::Login);

	// 检查登录界面是否存在
	if (!UIWidgetInstanceMap.Contains(LoginWidgetIndex))
	{
		UE_LOG(LogPSOneUIManager, Warning,
			TEXT("SetQrCodeValidity: Login widget not found in UIWidgetInstanceMap. Cannot update QR code validity UI."));
		return;
	}

	const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[LoginWidgetIndex];
	if (!UserWidget.IsValid())
	{
		UE_LOG(LogPSOneUIManager, Warning,
			TEXT("SetQrCodeValidity: Login widget pointer is invalid. Cannot update QR code validity UI."));
		// FIXME: 指针无效但仍在 Map 中，是否应该在此处移除？
		// UIWidgetInstanceMap.Remove(LoginWidgetIndex);
		return;
	}

	// 尝试将 UserWidget 转换为 UPSOneLogin
	UPSOneLogin* LoginWidget = Cast<UPSOneLogin>(UserWidget.Get());
	if (!LoginWidget)
	{
		UE_LOG(LogPSOneUIManager, Error,
			TEXT("SetQrCodeValidity: Failed to cast UserWidget to UPSOneLogin. Widget type might be incorrect or corrupted. "
				 "Address: %p"),
			UserWidget.Get());
		return;
	}

	// 调用 Login Widget 内部函数更新 UI 状态
	LoginWidget->SetQrCodeValid(bIsValid);
	UE_LOG(LogPSOneUIManager, Display, TEXT("SetQrCodeValidity: QR code validity UI updated successfully."));
}

// 设置二维码图片数据，并更新 UI
void UPSOneUIManager::SetQrCodeImageData(const TArray<uint8>& QRCodeImageBinary)
{
	UE_LOG(LogPSOneUIManager, Verbose, TEXT("Entering SetQrCodeImageData function. Image data size: %d bytes"),
		QRCodeImageBinary.Num());
	check(IsInGameThread());	// 必须在游戏线程调用

	constexpr int32 LoginWidgetIndex = static_cast<int32>(EUIWidgetType::Login);

	// 检查登录界面是否存在
	if (!UIWidgetInstanceMap.Contains(LoginWidgetIndex))
	{
		UE_LOG(LogPSOneUIManager, Warning,
			TEXT("SetQrCodeImageData: Login widget not found in UIWidgetInstanceMap. Cannot set QR code image data."));
		return;
	}

	const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[LoginWidgetIndex];
	if (!UserWidget.IsValid())
	{
		UE_LOG(LogPSOneUIManager, Warning,
			TEXT("SetQrCodeImageData: Login widget pointer is invalid. Cannot set QR code image data."));
		// FIXME: 指针无效但仍在 Map 中，是否应该在此处移除？
		// UIWidgetInstanceMap.Remove(LoginWidgetIndex);
		return;
	}

	// 尝试将 UserWidget 转换为 UPSOneLogin
	UPSOneLogin* LoginWidget = Cast<UPSOneLogin>(UserWidget.Get());
	if (!LoginWidget)
	{
		UE_LOG(LogPSOneUIManager, Error,
			TEXT("SetQrCodeImageData: Failed to cast UserWidget to UPSOneLogin. Widget type might be incorrect or corrupted. "
				 "Address: %p"),
			UserWidget.Get());
		return;
	}

	// 调用 Login Widget 内部函数设置图片数据
	LoginWidget->SetQrCodeImage(QRCodeImageBinary);
	UE_LOG(LogPSOneUIManager, Verbose, TEXT("SetQrCodeImageData: QR code image data set successfully in Login widget."));
}

// 显示实名认证界面
void UPSOneUIManager::ShowRealName()
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Entering ShowRealName function..."));
	check(IsInGameThread());	// 必须在游戏线程调用

	constexpr int32 RealNameWidgetIndex = static_cast<int32>(EUIWidgetType::RealName);

	// 检查实名认证界面是否已经存在且有效
	if (UIWidgetInstanceMap.Contains(RealNameWidgetIndex))
	{
		const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[RealNameWidgetIndex];
		if (UserWidget.IsValid())
		{
			UE_LOG(
				LogPSOneUIManager, Display, TEXT("ShowRealName: RealName widget already exists and is valid. Aborting creation."));
			// FIXME: 或许应该将现有窗口置于顶层？
			return;	   // 如果已存在，直接返回
		}
		else
		{
			// 存在于映射中但指针无效，移除旧条目
			UE_LOG(LogPSOneUIManager, Warning,
				TEXT("ShowRealName: Found an invalid RealName widget pointer in UIWidgetInstanceMap. Removing stale entry and "
					 "creating a new one."));
			UIWidgetInstanceMap.Remove(RealNameWidgetIndex);
		}
	}

	UE_LOG(
		LogPSOneUIManager, Display, TEXT("ShowRealName: Loading RealName widget class from path: %s"), *RealNameVerificationPath);
	// 加载实名认证界面的 Widget 类
	const auto WidgetClass = LoadClass<UPSOneRealNameVerification>(nullptr, *RealNameVerificationPath);
	if (WidgetClass == nullptr)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("ShowRealName: Failed to load real name widget class! Path: %s"),
			*RealNameVerificationPath);
		return;
	}

	// 保存当前焦点
	PushFocusedWidget();

	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowRealName: Creating new RealName widget instance..."));
	// 创建实名认证 Widget 实例
	UPSOneRealNameVerification* RealName = CreateWidget<UPSOneRealNameVerification>(GetCurrentWorld(), WidgetClass);
	// FIXME: CreateWidget 理论上也可能失败
	if (!RealName)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("ShowRealName: Failed to create RealName widget instance."));
		PopFocusedWidget();	   // 创建失败，恢复焦点
		return;
	}

	// 绑定提交按钮的回调
	RealName->OnSubmit = [this](const FString& Name, const FString& ID)
	{
		// FIXME: 应该对 Name 和 ID 做基本的校验吗？（例如非空）
		UE_LOG(LogPSOneUIManager, Display, TEXT("ShowRealName: OnSubmit triggered. Name and ID received."));
		if (OnLoginWithIdentityVerification)
		{
			OnLoginWithIdentityVerification(Name, ID);	  // 调用外部身份验证回调
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning,
				TEXT("ShowRealName: OnLoginWithIdentityVerification callback is not set. Identity verification cannot proceed."));
		}
	};

	// 绑定关闭按钮的回调
	RealName->OnClickCloseButton = [this]()
	{
		UE_LOG(LogPSOneUIManager, Display,
			TEXT("ShowRealName: OnClickCloseButton triggered. Hiding RealName widget asynchronously..."));
		// 异步隐藏界面
		AsyncTask(ENamedThreads::GameThread, [this]() { HideRealName(); });
	};

	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowRealName: Adding RealName widget to viewport and setting focus..."));
	AddWidgetToViewport(RealName);									   // 添加到视口
	BroadcastWidgetVisibilityChange(EUIWidgetType::RealName, true);	   // 广播可见性变化
	RealName->SetKeyboardFocus();									   // 设置焦点
	UIWidgetInstanceMap.Add(RealNameWidgetIndex, RealName);			   // 添加到管理映射
	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowRealName: Function completed successfully. RealName widget is now visible."));
}

// 隐藏实名认证界面
void UPSOneUIManager::HideRealName()
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Entering HideRealName function..."));
	check(IsInGameThread());	// 必须在游戏线程调用

	constexpr int32 RealNameWidgetIndex = static_cast<int32>(EUIWidgetType::RealName);

	// 查找并移除实名认证界面 Widget
	if (UIWidgetInstanceMap.Contains(RealNameWidgetIndex))
	{
		const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[RealNameWidgetIndex];
		if (UserWidget.IsValid())
		{
			UE_LOG(
				LogPSOneUIManager, Display, TEXT("HideRealName: Found valid RealName widget. Removing from viewport and map..."));
			RemoveWidgetFromViewport(UserWidget);								// 从视口移除
			UIWidgetInstanceMap.Remove(RealNameWidgetIndex);					// 从映射移除
			BroadcastWidgetVisibilityChange(EUIWidgetType::RealName, false);	// 广播可见性变化
			PopFocusedWidget();													// 恢复焦点
			UE_LOG(LogPSOneUIManager, Display, TEXT("HideRealName: RealName widget removed successfully."));
			return;
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning,
				TEXT("HideRealName: Found RealName widget entry in map, but the pointer is invalid. Removing stale entry."));
			UIWidgetInstanceMap.Remove(RealNameWidgetIndex);	// 只移除无效条目
		}
	}
	else
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("HideRealName: No RealName widget instance found in the map. Nothing to hide."));
	}
	UE_LOG(LogPSOneUIManager, Display, TEXT("Exiting HideRealName function."));
}

// 显示账号删除风险提示界面
void UPSOneUIManager::ShowDelTips(const FString& Tips, const FString& Uid, const FString& Token)
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Entering ShowDelTips function with Tips: %s"), *Tips);
	check(IsInGameThread());	// 必须在游戏线程调用

	constexpr int32 DelTipsWidgetIndex = static_cast<int32>(EUIWidgetType::DelTips);

	// 检查删除提示界面是否已经存在且有效
	if (UIWidgetInstanceMap.Contains(DelTipsWidgetIndex))
	{
		const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[DelTipsWidgetIndex];
		if (UserWidget.IsValid())
		{
			UE_LOG(LogPSOneUIManager, Display, TEXT("ShowDelTips: DelTips widget already exists and is valid. Aborting creation."));
			// FIXME: 是否需要更新已存在窗口的 Tips 内容？
			// Cast<UPSOneDelTips>(UserWidget.Get())->SetDescText(Tips); ?
			return;	   // 如果已存在，直接返回
		}
		else
		{
			// 存在于映射中但指针无效，移除旧条目
			UE_LOG(LogPSOneUIManager, Warning,
				TEXT("ShowDelTips: Found an invalid DelTips widget pointer in UIWidgetInstanceMap. Removing stale entry and "
					 "creating a new one."));
			UIWidgetInstanceMap.Remove(DelTipsWidgetIndex);
		}
	}

	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowDelTips: Loading DelTips widget class from path: %s"), *DelTipsPath);
	// 加载删除提示界面的 Widget 类
	const auto WidgetClass = LoadClass<UPSOneDelTips>(nullptr, *DelTipsPath);
	if (WidgetClass == nullptr)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("ShowDelTips: Failed to load del tips widget class! Path: %s"), *DelTipsPath);
		return;
	}

	// 保存当前焦点
	PushFocusedWidget();

	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowDelTips: Creating new DelTips widget instance..."));
	// 创建删除提示 Widget 实例
	UPSOneDelTips* DelTips = CreateWidget<UPSOneDelTips>(GetCurrentWorld(), WidgetClass);
	// FIXME: CreateWidget 理论上也可能失败
	if (!DelTips)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("ShowDelTips: Failed to create DelTips widget instance."));
		PopFocusedWidget();	   // 创建失败，恢复焦点
		return;
	}

	// 使用弱指针，用于在回调中安全访问 Widget
	TWeakObjectPtr<UPSOneDelTips> WeakDelTips = TWeakObjectPtr<UPSOneDelTips>(DelTips);

	DelTips->Uid = Uid;
	DelTips->Token = Token;

	// 设置提示文字内容
	DelTips->SetDescText(Tips);

	// 绑定"恢复账号"按钮的回调
	DelTips->OnRestore = [this, WeakDelTips]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("ShowDelTips: OnRestore triggered."));
		if (OnClickRestoreAccount)
		{
			// 调用外部恢复账号的回调，并传入一个用于处理结果的 Lambda
			OnClickRestoreAccount(WeakDelTips->Uid,
				[this, WeakDelTips](bool bShouldClose)	  // 这个内部 Lambda 在恢复操作完成后被调用
				{
					if (!WeakDelTips.IsValid())	   // 再次检查 Widget 有效性
					{
						UE_LOG(LogPSOneUIManager, Warning,
							TEXT("ShowDelTips: Restore callback executed, but DelTips widget is no longer valid."));
						return;
					}

					if (bShouldClose)
					{
						AsyncTask(ENamedThreads::GameThread,
							[this, WeakDelTips]()
							{
								HideDelTips();	  // 应该调用这个来统一处理
							});
					}
				});
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning,
				TEXT("ShowDelTips: OnClickRestoreAccount callback is not set. Cannot restore account."));
		}
	};

	// 绑定"确认删除"按钮的回调
	DelTips->OnDelete = [this, WeakDelTips]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("ShowDelTips: OnDelete triggered."));
		if (OnClickDeleteAccount)
		{
			// 调用外部删除账号的回调，并传入一个用于处理结果的 Lambda
			OnClickDeleteAccount(WeakDelTips->Uid, WeakDelTips->Token,
				[this, WeakDelTips](bool bShouldClose)	  // 这个内部 Lambda 在删除操作完成后被调用
				{
					if (!WeakDelTips.IsValid())	   // 再次检查 Widget 有效性
					{
						UE_LOG(LogPSOneUIManager, Warning,
							TEXT("ShowDelTips: Delete callback executed, but DelTips widget is no longer valid."));
						return;
					}

					if (bShouldClose)
					{
						AsyncTask(ENamedThreads::GameThread,
							[this, WeakDelTips]()
							{
								HideDelTips();	  // 应该调用这个来统一处理
							});
					}

				});
		}
		else
		{
			UE_LOG(
				LogPSOneUIManager, Warning, TEXT("ShowDelTips: OnClickDeleteAccount callback is not set. Cannot delete account."));
		}
	};

	// 绑定关闭按钮的回调
	DelTips->OnClickCloseButton = [this]()
	{
		UE_LOG(
			LogPSOneUIManager, Display, TEXT("ShowDelTips: OnClickCloseButton triggered. Hiding DelTips widget asynchronously..."));
		// 异步隐藏界面
		AsyncTask(ENamedThreads::GameThread, [this]() { HideDelTips(); });
	};

	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowDelTips: Adding DelTips widget to viewport and setting focus..."));
	AddWidgetToViewport(DelTips);									  // 添加到视口
	BroadcastWidgetVisibilityChange(EUIWidgetType::DelTips, true);	  // 广播可见性变化
	DelTips->SetKeyboardFocus();									  // 设置焦点
	UIWidgetInstanceMap.Add(DelTipsWidgetIndex, DelTips);			  // 添加到管理映射
	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowDelTips: Function completed successfully. DelTips widget is now visible."));
}

// 隐藏账号删除风险提示界面
void UPSOneUIManager::HideDelTips()
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Entering HideDelTips function..."));
	check(IsInGameThread());	// 必须在游戏线程调用

	constexpr int32 DelTipsWidgetIndex = static_cast<int32>(EUIWidgetType::DelTips);

	// 查找并移除删除提示界面 Widget
	if (UIWidgetInstanceMap.Contains(DelTipsWidgetIndex))
	{
		const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[DelTipsWidgetIndex];
		if (UserWidget.IsValid())
		{
			UE_LOG(LogPSOneUIManager, Display, TEXT("HideDelTips: Found valid DelTips widget. Removing from viewport and map..."));
			RemoveWidgetFromViewport(UserWidget);							   // 从视口移除
			UIWidgetInstanceMap.Remove(DelTipsWidgetIndex);					   // 从映射移除
			BroadcastWidgetVisibilityChange(EUIWidgetType::DelTips, false);	   // 广播可见性变化
			PopFocusedWidget();												   // 恢复焦点
			UE_LOG(LogPSOneUIManager, Display, TEXT("HideDelTips: DelTips widget removed successfully."));
			return;
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning,
				TEXT("HideDelTips: Found DelTips widget entry in map, but the pointer is invalid. Removing stale entry."));
			UIWidgetInstanceMap.Remove(DelTipsWidgetIndex);	   // 只移除无效条目
		}
	}
	else
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("HideDelTips: No DelTips widget instance found in the map. Nothing to hide."));
	}
	UE_LOG(LogPSOneUIManager, Display, TEXT("Exiting HideDelTips function."));
}

#undef LOCTEXT_NAMESPACE
