﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "PSOneUserCenterDeviceManager.h"
#include "PSOneUserCenterRightCellSubtitle.h"
#include "Components/Image.h"
#include "Components/TextBlock.h"
#include "Components/ScrollBox.h"

#define LOCTEXT_NAMESPACE "PSOneUserCenterDeviceManager"

namespace
{
	FString DeviceInfoCellPath = "WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_UserCenterRightCellSubtitle.WBP_ONE_UserCenterRightCellSubtitle_C'";
}

void UPSOneUserCenterDeviceManager::SetOnlineDevices(const TArray<FOneOnlineDevice>& Devices)
{
	// 已在 2 台设备登录
	TitleBlock->SetText(FText::Format(LOCTEXT("LoggedInDevices", "已在{0}台设备登录"), FText::AsNumber(Devices.Num())));
	ScrollBox->ClearChildren();

	auto WidgetClass = LoadClass<UPSOneUserCenterRightCellSubtitle>(nullptr, *DeviceInfoCellPath);
	if (!WidgetClass)
	{
		UE_LOG(LogTemp, Error, TEXT("UPSOneSelectAreaCode::BindData: Failed to load widget class %s"), *DeviceInfoCellPath);
		return;
	}
	UWorld*		   World = GetWorld();
	static FString OtherDeviceIcon = TEXT("Texture2D'/OneEngineSDK/Texture/T_Platform_Other.T_Platform_Other'");
	static FString PSDeviceIcon = TEXT("Texture2D'/OneEngineSDK/Texture/T_Platform_PS.T_Platform_PS'");
	int			   i = 0;
	// 大陆的是 16
	int PsType = 16;
	if (!bIsMainLand)
	{
		PsType = 15;
	}
	for (const auto& Device : Devices)
	{
		// 循环加入
		auto Widget = CreateWidget<UPSOneUserCenterRightCellSubtitle>(World, WidgetClass);

		if (Device.OSType == PsType)
		{
			// PS
			Widget->Icon = LoadObject<UTexture2D>(nullptr, *PSDeviceIcon);
		}
		else
		{
			// Other
			Widget->Icon = LoadObject<UTexture2D>(nullptr, *OtherDeviceIcon);
		}
		Widget->SetTitle(FText::FromString(Device.DeviceName));
		Widget->HiddenArrow = true;
		
		if (Device.bIsCurrentDevice)
		{
			FText CurrentDevice = LOCTEXT("CurrentDevice", "本机");
			Widget->SetSubTitle(CurrentDevice);
		}
		else
		{
			// 强制下线
			FText ForceOffline = LOCTEXT("ForceOffline", "强制下线");
			Widget->SetSubTitle(ForceOffline);
			// rgba(254, 58, 58, 1)
			Widget->SubTitleColor = FLinearColor(254.0 / 255.0f, 58.0 / 255.0f, 58.0 / 255.0f, 1.0f);
			
			Widget->OnClickCallback = [this, i, Device]() {
				SelectedIndex = i;
				if (OnOfflineDeviceCallback)
				{
					OnOfflineDeviceCallback(Device);
				}
			};
		}
		i++;
		ScrollBox->AddChild(Widget);
	}

	if (Devices.Num() > 0)
	{
		TArray<UWidget*> Children = ScrollBox->GetAllChildren();
		if (OnSetupDeviceItemCallback)
		{
			OnSetupDeviceItemCallback(Children);
		}
	}
}

FReply UPSOneUserCenterDeviceManager::NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent)
{
	FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
	return Reply;
}

void UPSOneUserCenterDeviceManager::NativeOnRemovedFromFocusPath(const FFocusEvent& InFocusEvent)
{
	Super::NativeOnRemovedFromFocusPath(InFocusEvent);
	SelectedIndex = -1;
}

#undef LOCTEXT_NAMESPACE
