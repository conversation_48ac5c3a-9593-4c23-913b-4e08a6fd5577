﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "PSOneUserCenterBindManager.h"
#include  "PSOneUserCenterRightCellAccountBind.h"
#include "Components/ScrollBox.h"

#define LOCTEXT_NAMESPACE "PSOneUserCenterBindManager"
namespace
{
	// 纹理路径
	FString EmailIcon = TEXT("Texture2D'/OneEngineSDK/Texture/T_UC_Email.T_UC_Email'");
	// PSN

	FString PSNIcon = TEXT("Texture2D'/OneEngineSDK/Texture/T_Platform_PS.T_Platform_PS'");

	// Cell 路径
	FString CellPath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_UserCenterRightCellAccountBind.WBP_ONE_UserCenterRightCellAccountBind_C'");
}



void UPSOneUserCenterBindManager::SetBindItems(const TArray<FPSOneBindItem>& Items)
{
	ScrollBox->ClearChildren();
	auto WidgetClass = LoadClass<UPSOneUserCenterRightCellAccountBind>(nullptr, *CellPath);
	if (!WidgetClass)
	{
		UE_LOG(LogTemp, Error, TEXT("UPSOneUserCenterBindManager::SetBindItems: Failed to load widget class %s"), *CellPath);
		return;
	}
	UWorld* World = GetWorld();
	int i = 0;
	for (const auto& Item : Items)
	{
		auto Widget = CreateWidget<UPSOneUserCenterRightCellAccountBind>(World, WidgetClass);
		if (Item.Id == 8)
		{
			// 邮箱
			Widget->Icon = LoadObject<UTexture2D>(nullptr, *EmailIcon);
			Widget->SetTitle(LOCTEXT("Email", "邮箱"));
		} else if (Item.Id == 23)
		{
			Widget->Icon = LoadObject<UTexture2D>(nullptr, *PSNIcon);
			Widget->SetTitle(LOCTEXT("PSN", "登录 PlayStation™Network"));
		} else
		{
			continue;
		}
		Widget->UpdateBindIcon(Item.bBind);
		Widget->OnClickCallback = [this, i, Item]()
		{
			if (OnBindItemCallback)
			{
				OnBindItemCallback(Item);
			}
		};
		i++;
		ScrollBox->AddChild(Widget);
	}

	TArray<UWidget*> Children = ScrollBox->GetAllChildren();
	if (Children.Num() > 0)
	{
		if (OnSetupBindItemCallback)
		{
			OnSetupBindItemCallback(Children);
		}
	}
}
#undef LOCTEXT_NAMESPACE
