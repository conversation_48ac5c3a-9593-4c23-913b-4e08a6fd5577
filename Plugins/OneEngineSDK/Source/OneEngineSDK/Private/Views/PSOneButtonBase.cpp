﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "PSOneButtonBase.h"

#include "PSUserWidgetSettings.h"
#include "Components/Border.h"
#include "Components/TextBlock.h"
#include "Components/Button.h"
#include "Components/ButtonSlot.h"
#include "Engine/Font.h"
#include "Components/HorizontalBox.h"
#include "Engine/Texture2D.h"

void UPSOneButtonBase::SetTitle(const FText &InTitle)
{
	Title = InTitle;
	TextBlock->SetText(Title);
}

void UPSOneButtonBase::SetSelected(bool bSelected)
{
	bIsSelected = bSelected;

	UTexture2D* TextureToUse = bSelected ? FocusTexture : NormalTexture;
	SetBorderTexture(BgBorder, TextureToUse);
}

bool UPSOneButtonBase::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	// 添加点击事件
	Button->OnClicked.AddDynamic(this, &UPSOneButtonBase::OnClick);
	return true;
}

void UPSOneButtonBase::NativePreConstruct()
{
	Super::NativePreConstruct();

	if (UPSUserWidgetSettings::Get()->CustomFont)
	{
		FSlateFontInfo Info = FSlateFontInfo(UPSUserWidgetSettings::Get()->CustomFont, TextSize);
		TextBlock->SetFont(Info);
	}

	SetTitle(Title);
	
	SetSelected(bIsSelected);

	if (TextHorizontalBox)
	{
		UE_LOG(LogTemp, Warning, TEXT("Cast TextHorizontalBox to UButtonSlot"));
		if (UButtonSlot* TextHorizontalBoxSlot = Cast<UButtonSlot>(TextHorizontalBox->Slot))
		{
			TextHorizontalBoxSlot->SetHorizontalAlignment(Alignment);
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("TextHorizontalBoxSlot is not a UButtonSlot"));
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("TextHorizontalBox is null"));
	}
	
}

void UPSOneButtonBase::NativeConstruct()
{
	Super::NativeConstruct();
}

void UPSOneButtonBase::NativeDestruct()
{
	Super::NativeDestruct();
	Button->OnClicked.RemoveDynamic(this, &UPSOneButtonBase::OnClick);
}

FReply UPSOneButtonBase::NativeOnFocusReceived(const FGeometry &InGeometry, const FFocusEvent &InFocusEvent)
{
	FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
	Button->SetKeyboardFocus();
	return Reply;
}

void UPSOneButtonBase::OnClick()
{
	ButtonDidClick();
}

void UPSOneButtonBase::NativeOnAddedToFocusPath(const FFocusEvent &InFocusEvent)
{
	Super::NativeOnAddedToFocusPath(InFocusEvent);

	if (bIsFocus)
	{
		return;
	}

	bIsFocus = true;

	if (OnFocusSwitch)
	{
		OnFocusSwitch(this, bIsFocus);
	}

	OnFocusSwitched(true);
	SetBorderTexture(BgBorder, FocusTexture);
}

void UPSOneButtonBase::NativeOnRemovedFromFocusPath(const FFocusEvent &InFocusEvent)
{
	Super::NativeOnRemovedFromFocusPath(InFocusEvent);
	bIsFocus = false;

	if (OnFocusSwitch)
	{
		OnFocusSwitch(this, bIsFocus);
	}

	OnFocusSwitched(false);

	UTexture2D* TextureToUse = bIsSelected ? SelectedTexture : NormalTexture;
	SetBorderTexture(BgBorder, TextureToUse);
}

void UPSOneButtonBase::ButtonDidClick()
{
	if (OnClickCallback)
	{
		OnClickCallback();
	}
}

void UPSOneButtonBase::OnFocusSwitched(bool Focus)
{
}

void UPSOneButtonBase::SetBorderTexture(UBorder* Border, UTexture2D* Texture)
{
	if (!Border || !Texture)
	{
		if (!Texture)
		{
			UE_LOG(LogTemp, Warning, TEXT("Texture is null for border %s"), Border ? *Border->GetName() : TEXT("null"));
		}
		return;
	}

	FSlateBrush Brush;
	Brush.SetResourceObject(Texture);
	Brush.ImageSize = FVector2D(Texture->GetSizeX(), Texture->GetSizeY());
	Brush.DrawAs = ESlateBrushDrawType::Box;
	Brush.Margin = FMargin(0.5f);
	Border->SetBrush(Brush);
}
