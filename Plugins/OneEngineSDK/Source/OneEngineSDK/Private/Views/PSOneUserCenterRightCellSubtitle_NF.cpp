﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "PSOneUserCenterRightCellSubtitle_NF.h"

#include "Components/Image.h"
#include "Components/TextBlock.h"

void UPSOneUserCenterRightCellSubtitle_NF::SetSubTitle(const FText& InSubTitle)
{
	SubTitle = InSubTitle;
	SubTextBlock->SetText(SubTitle);
}

void UPSOneUserCenterRightCellSubtitle_NF::NativePreConstruct()
{
	Super::NativePreConstruct();

	if (HiddenArrow)
	{
		ArrowImage->SetVisibility(ESlateVisibility::Collapsed);
	}
	else
	{
		ArrowImage->SetVisibility(ESlateVisibility::Visible);
	}
	
	TextBlock->SetText(Title);
	SubTextBlock->SetText(SubTitle);
}

void UPSOneUserCenterRightCellSubtitle_NF::NativeConstruct()
{
	Super::NativeConstruct();
}
