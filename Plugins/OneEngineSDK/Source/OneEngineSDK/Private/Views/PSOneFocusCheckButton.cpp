// Fill out your copyright notice in the Description page of Project Settings.

#include "Views/PSOneFocusCheckButton.h"

#include "Components/Image.h"

void UPSOneFocusCheckButton::ButtonDidClick()
{
    bIsChecked = !bIsChecked;
    UpdateCheckBoxImage();
    Super::ButtonDidClick();
}

void UPSOneFocusCheckButton::UpdateCheckBoxImage()
{
    if (bIsChecked)
    {
        if (CheckedTexture)
        {
            CheckBoxImage->SetBrushFromTexture(CheckedTexture);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("No Checked Image"));
        }
    }
    else
    {
        if (UncheckedImage)
        {
            CheckBoxImage->SetBrushFromTexture(UncheckedImage);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("No Unchecked Image"));
        }
    }
}
