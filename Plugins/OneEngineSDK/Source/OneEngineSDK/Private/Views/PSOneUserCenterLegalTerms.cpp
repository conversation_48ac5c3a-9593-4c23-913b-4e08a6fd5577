﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "PSOneUserCenterLegalTerms.h"

#include "PSOneUserCenterRightCellSubtitle.h"

bool UPSOneUserCenterLegalTerms::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	LegalCell->OnClickCallback = [this]()
	{
		if (OnClickOpenLegalTerms)
		{
			OnClickOpenLegalTerms();
		}
	};
	return true;
}


FReply UPSOneUserCenterLegalTerms::NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent)
{
	FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
	return Reply;
}
