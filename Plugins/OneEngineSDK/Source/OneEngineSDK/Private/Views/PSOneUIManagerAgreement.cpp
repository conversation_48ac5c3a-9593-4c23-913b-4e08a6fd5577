﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "Async/Async.h"
#include "Components/TextBlock.h"
#include "PSOneUIManager.h"
#include "PSOneUserAgreementPrompt.h"

#define LOCTEXT_NAMESPACE "PSOneUIManagerAgreement"

namespace
{
// 用户协议提示框 Widget 蓝图路径
FString UserAgreementPromptPath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_AgreementPrompt.WBP_ONE_AgreementPrompt_C'");
}	 // namespace

// 显示用户协议界面
void UPSOneUIManager::ShowUserAgreement(bool bHasAgreed, int32 LoginOption, const FString& Title, const FString& Content)
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Attempting to show User Agreement. HasAgreed: %s, LoginOption: %d"),
		bHasAgreed ? TEXT("True") : TEXT("False"), LoginOption);
	check(IsInGameThread());	// 确保在游戏线程中执行

	// 获取用户协议 Widget 的索引
	constexpr int32 UserAgreementWidgetIndex = static_cast<int32>(EUIWidgetType::UserAgreement);

	// 检查用户协议 Widget 是否已存在
	if (UIWidgetInstanceMap.Contains(UserAgreementWidgetIndex))
	{
		const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[UserAgreementWidgetIndex];
		if (UserWidget.IsValid())
		{
			UE_LOG(LogPSOneUIManager, Display, TEXT("User Agreement widget already exists."));
			UPSOneUserAgreementPrompt* UserAgreementPrompt = Cast<UPSOneUserAgreementPrompt>(UserWidget.Get());
			if (UserAgreementPrompt)	// 检查类型转换是否成功
			{
				if (bHasAgreed)
				{
					// 如果用户已同意，切换到准备页面
					UE_LOG(LogPSOneUIManager, Display,
						TEXT("Switching existing User Agreement widget to prepare page because user has already agreed."));
					UserAgreementPrompt->SwitchToPreparePage();
				}
				else
				{
					// FIXME: 注意这里 - 如果 Widget 已显示但用户未同意，当前逻辑可能不完整，是否需要重置或显示特定状态？
					UE_LOG(LogPSOneUIManager, Warning,
						TEXT("User Agreement widget is shown, but user hasn't agreed. Current state might be unexpected."));
				}
			}
			else
			{
				// 如果类型转换失败，记录错误并移除无效实例
				UE_LOG(LogPSOneUIManager, Error,
					TEXT("Failed to cast existing UserWidget to UPSOneUserAgreementPrompt. Removing invalid entry."));
				UIWidgetInstanceMap.Remove(UserAgreementWidgetIndex);
				// FIXME: 注意这里 - 是否需要通知其他系统或尝试重新创建 Widget？
			}
			return;	   // Widget 已存在，直接返回
		}
		else
		{
			// 如果 Widget 指针无效，从映射中移除
			UE_LOG(LogPSOneUIManager, Warning, TEXT("Found an invalid User Agreement widget entry in the map. Removing it."));
			UIWidgetInstanceMap.Remove(UserAgreementWidgetIndex);
		}
	}
	else
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("No existing User Agreement widget found. Proceeding to create a new one."));
	}

	// 加载用户协议 Widget 类
	UE_LOG(LogPSOneUIManager, Display, TEXT("Loading User Agreement widget class from path: %s"), *UserAgreementPromptPath);
	const TSubclassOf<UUserWidget> WidgetClass = LoadClass<UPSOneUserAgreementPrompt>(nullptr, *UserAgreementPromptPath);
	if (WidgetClass == nullptr)
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to load User Agreement widget class! Path: %s"), *UserAgreementPromptPath);
		// FIXME: 注意这里 - 加载失败，应该如何处理？是否需要回退或通知用户？
		return;	   // 加载失败，无法继续
	}

	// 保存当前焦点 Widget，以便稍后恢复
	PushFocusedWidget();

	// 创建新的用户协议 Widget 实例
	UE_LOG(LogPSOneUIManager, Display, TEXT("Creating a new User Agreement widget instance."));
	UPSOneUserAgreementPrompt* UserAgreementPrompt = CreateWidget<UPSOneUserAgreementPrompt>(GetCurrentWorld(), WidgetClass);
	if (!UserAgreementPrompt)	 // 检查 Widget 是否成功创建
	{
		UE_LOG(LogPSOneUIManager, Error, TEXT("Failed to create User Agreement widget instance!"));
		PopFocusedWidget();	   // 恢复之前的焦点 Widget
		// FIXME: 注意这里 - Widget 创建失败，应该如何处理？
		return;
	}

	// 设置 Widget 属性
	UserAgreementPrompt->bIsMainland = IsMainlandEnv();		 // 是否为大陆环境
	UserAgreementPrompt->StartIndex = bHasAgreed ? 1 : 0;	 // 根据是否已同意设置初始页面索引 (0: 同意页面，1: 准备页面)
	UserAgreementPrompt->LoginOption = LoginOption;			 // 设置登录选项

	if (!Title.IsEmpty())
	{
		UserAgreementPrompt->TitleBlock->SetText(FText::FromString(Title));
	}

	if (!Content.IsEmpty())
	{
		UserAgreementPrompt->DescBlock->SetText(FText::FromString(Content));
	}

	// 绑定回调函数
	auto WeakWidget = TWeakObjectPtr<UPSOneUserAgreementPrompt>(UserAgreementPrompt);	 // 使用弱指针防止循环引用

	// 用户同意协议的回调
	UserAgreementPrompt->OnAgreeCallback = [this, WeakWidget]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("User Agreement accepted. Executing OnAgreementAccepted callback."));
		if (OnAgreementAccepted)
		{
			OnAgreementAccepted();
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("OnAgreementAccepted callback is not bound."));
		}
	};

	// 用户点击"绑定账号"的回调
	UserAgreementPrompt->OnBindAccountCallback = [this]()
	{
		UE_LOG(LogPSOneUIManager, Display,
			TEXT("Bind Account button clicked. Hiding User Agreement and executing OnAccountBinding callback."));
		// 使用 AsyncTask 确保在游戏线程执行 UI 操作
		AsyncTask(ENamedThreads::GameThread,
			[this]()
			{
				HideUserAgreement();	// 隐藏协议界面
				if (OnAccountBinding)
				{
					OnAccountBinding();
				}
				else
				{
					UE_LOG(LogPSOneUIManager, Warning, TEXT("OnAccountBinding callback is not bound."));
				}
			});
	};

	// 用户点击"创建账号"的回调
	UserAgreementPrompt->OnCreateAccountCallback = [this, WeakWidget]()
	{
		UE_LOG(LogPSOneUIManager, Display,
			TEXT("Create Account button clicked. Hiding User Agreement and executing OnPsnLogin callback."));
		AsyncTask(ENamedThreads::GameThread,
			[this]()
			{
				HideUserAgreement();	// 隐藏协议界面
				if (OnPsnLogin)
				{
					OnPsnLogin();
				}
				else
				{
					UE_LOG(LogPSOneUIManager, Warning, TEXT("OnPsnLogin callback is not bound."));
				}
			});
	};

	// 用户点击"使用完美通行证登录"的回调
	UserAgreementPrompt->OnLoginWithPwrdCallback = [this, WeakWidget]()
	{
		UE_LOG(LogPSOneUIManager, Display,
			TEXT("Login with PWRD button clicked. Hiding User Agreement and executing OnPwrdLogin callback."));
		AsyncTask(ENamedThreads::GameThread,
			[this]()
			{
				HideUserAgreement();	// 隐藏协议界面
				if (OnPwrdLogin)
				{
					OnPwrdLogin();
				}
				else
				{
					UE_LOG(LogPSOneUIManager, Warning, TEXT("OnPwrdLogin callback is not bound."));
				}
			});
	};

	// 用户点击打开"用户协议"链接的回调
	UserAgreementPrompt->OnOpenUserAgreementCallback = [this]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Open User Agreement link clicked. Executing OnOpenUserAgreementURL callback."));
		if (OnOpenUserAgreementURL)
		{
			OnOpenUserAgreementURL();
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("OnOpenUserAgreementURL callback is not bound."));
		}
	};

	// 用户点击打开"隐私政策"链接的回调
	UserAgreementPrompt->OnOpenUserPrivacyCallback = [this]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("Open Privacy Policy link clicked. Executing OnOpenPrivacyPolicyURL callback."));
		if (OnOpenPrivacyPolicyURL)
		{
			OnOpenPrivacyPolicyURL();
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("OnOpenPrivacyPolicyURL callback is not bound."));
		}
	};

	// 用户点击打开"儿童隐私政策"链接的回调
	UserAgreementPrompt->OnOpenChildPrivacyCallback = [this]()
	{
		UE_LOG(LogPSOneUIManager, Display,
			TEXT("Open Child Privacy Policy link clicked. Executing OnOpenChildPrivacyPolicyURL callback."));
		if (OnOpenChildPrivacyPolicyURL)
		{
			OnOpenChildPrivacyPolicyURL();
		}
		else
		{
			UE_LOG(LogPSOneUIManager, Warning, TEXT("OnOpenChildPrivacyPolicyURL callback is not bound."));
		}
	};

	// 用户关闭协议界面的回调
	UserAgreementPrompt->OnCloseCallback = [this]()
	{
		UE_LOG(LogPSOneUIManager, Display, TEXT("User Agreement closed by user. Hiding the widget."));
		// 使用 AsyncTask 确保在游戏线程执行 UI 操作
		AsyncTask(ENamedThreads::GameThread, [this]() { HideUserAgreement(); });
	};

	// 将 Widget 添加到视口并设置焦点
	UE_LOG(LogPSOneUIManager, Display, TEXT("Adding User Agreement widget to viewport and setting focus."));
	AddWidgetToViewport(UserAgreementPrompt);								// 添加到视口
	BroadcastWidgetVisibilityChange(EUIWidgetType::UserAgreement, true);	// 广播可见性变化事件
	UserAgreementPrompt->SetKeyboardFocus();								// 设置键盘焦点
	// 将 Widget 实例添加到映射中管理
	UIWidgetInstanceMap.Add(UserAgreementWidgetIndex, UserAgreementPrompt);
	UE_LOG(LogPSOneUIManager, Display, TEXT("ShowUserAgreement function completed successfully."));
}

// 切换用户协议界面到准备页面（通常在同意后调用）
void UPSOneUIManager::SwitchToPreparePage()
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Attempting to switch User Agreement widget to Prepare Page..."));
	check(IsInGameThread());	// 确保在游戏线程中执行

	constexpr int32 UserAgreementWidgetIndex = static_cast<int32>(EUIWidgetType::UserAgreement);
	// 检查 Widget 是否存在于映射中
	if (UIWidgetInstanceMap.Contains(UserAgreementWidgetIndex))
	{
		const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[UserAgreementWidgetIndex];
		if (UserWidget.IsValid())
		{
			// 尝试将 Widget 转换为正确的类型
			UPSOneUserAgreementPrompt* UserAgreementPrompt = Cast<UPSOneUserAgreementPrompt>(UserWidget.Get());
			if (UserAgreementPrompt)
			{
				// 调用 Widget 内部的切换页面函数
				UE_LOG(LogPSOneUIManager, Display, TEXT("Calling SwitchToPreparePage on the User Agreement widget instance."));
				UserAgreementPrompt->SwitchToPreparePage();
				UE_LOG(LogPSOneUIManager, Display, TEXT("SwitchToPreparePage function completed successfully."));
				return;	   // 成功切换，返回
			}
			else
			{
				// 类型转换失败
				UE_LOG(LogPSOneUIManager, Error,
					TEXT("Failed to cast UserWidget to UPSOneUserAgreementPrompt during SwitchToPreparePage. Widget might be of "
						 "wrong type or corrupted."));
			}
		}
		else
		{
			// Widget 指针无效
			UE_LOG(LogPSOneUIManager, Warning,
				TEXT("User Agreement widget pointer is invalid during SwitchToPreparePage. Cannot switch page."));
			// 移除无效条目
			UIWidgetInstanceMap.Remove(UserAgreementWidgetIndex);
		}
	}
	else
	{
		// Widget 未显示
		UE_LOG(LogPSOneUIManager, Warning, TEXT("User Agreement widget is not currently shown. Cannot switch to prepare page."));
	}
	UE_LOG(LogPSOneUIManager, Display,
		TEXT("SwitchToPreparePage function finished, but the page was not switched (widget not found or invalid)."));
}

// 隐藏用户协议界面
void UPSOneUIManager::HideUserAgreement()
{
	UE_LOG(LogPSOneUIManager, Display, TEXT("Attempting to hide User Agreement widget..."));
	check(IsInGameThread());	// 确保在游戏线程中执行

	constexpr int32 UserAgreementWidgetIndex = static_cast<int32>(EUIWidgetType::UserAgreement);
	// 检查 Widget 是否存在于映射中
	if (UIWidgetInstanceMap.Contains(UserAgreementWidgetIndex))
	{
		const TWeakObjectPtr<UUserWidget> UserWidget = UIWidgetInstanceMap[UserAgreementWidgetIndex];
		if (UserWidget.IsValid())
		{
			// 从视口移除 Widget
			UE_LOG(LogPSOneUIManager, Display, TEXT("Removing User Agreement widget from viewport."));
			RemoveWidgetFromViewport(UserWidget);
		}
		else
		{
			// Widget 指针无效，但也需要从映射中移除
			UE_LOG(LogPSOneUIManager, Warning,
				TEXT("User Agreement widget pointer was invalid, but removing the entry from the map anyway."));
		}
		// 从映射中移除 Widget 实例
		UIWidgetInstanceMap.Remove(UserAgreementWidgetIndex);
		// 广播可见性变化事件
		BroadcastWidgetVisibilityChange(EUIWidgetType::UserAgreement, false);
		// 恢复之前的焦点 Widget
		PopFocusedWidget();
		UE_LOG(LogPSOneUIManager, Display, TEXT("User Agreement widget hidden successfully."));
		return;	   // 成功隐藏，返回
	}

	// 如果 Widget 本来就不存在
	UE_LOG(LogPSOneUIManager, Display, TEXT("No User Agreement widget was found to hide."));
}

#undef LOCTEXT_NAMESPACE
