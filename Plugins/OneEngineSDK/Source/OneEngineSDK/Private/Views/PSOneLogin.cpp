﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "PSOneLogin.h"

#include "Components/BackgroundBlur.h"
#include "Components/EditableText.h"
#include "Components/HorizontalBox.h"
#include "Components/HorizontalBoxSlot.h"
#include "Components/Image.h"
#include "Components/SizeBox.h"
#include "Components/TextBlock.h"
#include "Components/VerticalBox.h"
#include "Components/WidgetSwitcher.h"
#include "ImageUtils.h"
#include "Misc/FileHelper.h"
#include "PSOneConfirmButton.h"
#include "PSOneFocusCheckButton.h"
#include "PSOneFocusNavigator.h"
#include "PSOneTextFieldBase.h"
#include "PSOneTextFieldVerificationCode.h"
#include "PSUserWidgetSettings.h"
#include "Components/CanvasPanelSlot.h"
#include "Components/ScaleBox.h"
#include "Engine/Texture2D.h"

#define LOCTEXT_NAMESPACE "PSOneLogin"

// 常量定义，避免魔法数字
namespace LoginConstants
{
// 大陆版登录框内边距
const FMargin MAINLAND_LOGIN_BOX_PADDING(40.0, 0, 20.0, 0);
// 海外版登录框内边距
const FMargin GLOBAL_LOGIN_BOX_PADDING(40.0, 0, 40.0, 0);
}	 // namespace LoginConstants

void UPSOneLogin::NativePreConstruct()
{
	Super::NativePreConstruct();

	// 初始化 UI 布局和纹理
	InitializeUILayout();
	InitializeUITextures();
}

/**
 * 初始化 UI 布局
 * 根据是否为大陆版设置不同的布局和提示文本
 */
void UPSOneLogin::InitializeUILayout()
{
	// 设置背景模糊效果
	ConfigureBackgroundBlur();
	
	UHorizontalBoxSlot* LoginVerticalBoxSlot = Cast<UHorizontalBoxSlot>(LoginVerticalBox->Slot);
	// 获取画布面板插槽
	UCanvasPanelSlot* CanvasPanelSlot = Cast<UCanvasPanelSlot>(ContentVerticalBox->Slot);
	const float TopPadding = CanvasPanelSlot->GetPosition().Y;
	
	if (bIsMainLand)
	{
		// 大陆版，显示账号登录以及二维码登录
		ContentSizeBox->SetWidthOverride(MainLandContentWidth);
		CanvasPanelSlot->SetSize(FVector2D(MainLandContentWidth, CanvasPanelSlot->GetSize().Y));
		CanvasPanelSlot->SetPosition(FVector2D(-(MainLandContentWidth * 0.5), TopPadding));
		
		VerticalSplitLine->SetVisibility(ESlateVisibility::Visible);
		QRCodeVerticalBox->SetVisibility(ESlateVisibility::Visible);

		if (LoginVerticalBoxSlot)
		{
			LoginVerticalBoxSlot->SetPadding(LoginConstants::MAINLAND_LOGIN_BOX_PADDING);
		}

		// 设置大陆版的提示文本
		AccountField->SetHintText(LOCTEXT("MobileAccountHint", "请输入手机号"));
		AccountField->VirtualKeyboardType = EVirtualKeyboardType::Number;
		VerificationCodeField->SetHintText(LOCTEXT("MobileVerificationCodeHint", "请输入短信验证码"));
	}
	else
	{
		// 海外版，只显示账号登录
		ContentSizeBox->SetWidthOverride(GlobalContentWidth);
		CanvasPanelSlot->SetSize(FVector2D(GlobalContentWidth, CanvasPanelSlot->GetSize().Y));
		CanvasPanelSlot->SetPosition(FVector2D(-(GlobalContentWidth * 0.5), TopPadding));
		
		VerticalSplitLine->SetVisibility(ESlateVisibility::Collapsed);
		QRCodeVerticalBox->SetVisibility(ESlateVisibility::Collapsed);

		if (LoginVerticalBoxSlot)
		{
			LoginVerticalBoxSlot->SetPadding(LoginConstants::GLOBAL_LOGIN_BOX_PADDING);
		}

		if (bIsSupportEmailPasswordLogin)
		{
			EmailSwitchLoginHorizontalBox->SetVisibility(ESlateVisibility::Visible);
		}
		else
		{
			EmailSwitchLoginHorizontalBox->SetVisibility(ESlateVisibility::Collapsed);
		}
		// 设置海外版的提示文本
		AccountField->SetHintText(LOCTEXT("EmailAccountHint", "请输入邮箱"));
		AccountField->VirtualKeyboardType = EVirtualKeyboardType::Email;
		VerificationCodeField->SetHintText(LOCTEXT("EmailVerificationCodeHint", "请输入邮箱验证码"));
	}


	
	
	// 根据当前登录方式更新 UI
	UpdateLoginType(bIsLoginWithPassword, false);


}

/**
 * 配置背景模糊效果
 * 根据用户设置决定是否启用背景模糊
 */
void UPSOneLogin::ConfigureBackgroundBlur()
{
	UPSUserWidgetSettings* Settings = UPSUserWidgetSettings::Get();
	ScaleBox->SetUserSpecifiedScale(Settings->GetUIScaleRatio());

	if (Settings->bEnableBlurBackground)
	{
		BackgroundBlur->SetVisibility(ESlateVisibility::Visible);
		BackgroundBlur->SetBlurStrength(Settings->BlurBackgroundStrength);
	}
	else
	{
		BackgroundBlur->SetVisibility(ESlateVisibility::Collapsed);
	}
}

/**
 * 初始化 UI 纹理
 * 设置各个图标的纹理
 */
void UPSOneLogin::InitializeUITextures()
{
	UPSUserWidgetSettings* Settings = UPSUserWidgetSettings::Get();

	

	// 设置各个图标的纹理
	SetBrushTexture(SendCodeIcon, Settings->PSSquareTexture);
	SetBrushTexture(AccountLoginIcon, Settings->PSTriangleTexture);
	SetBrushTexture(CodeLoginIcon, Settings->PSTriangleTexture);

	if (bIsIwPlay)
	{
		SetBrushTexture(LogoIcon,IwPlayLogo);
	}
	else
	{
		SetBrushTexture(LogoIcon, PwrdLogo);
	}

	// 根据设置配置按钮图标
	ConfigureButtonIcons(Settings);
}

/**
 * 配置按钮图标
 * 根据是否将确认按钮分配为圆形，设置不同的按钮图标
 */
void UPSOneLogin::ConfigureButtonIcons(UPSUserWidgetSettings* Settings)
{
	if (Settings->bEnterButtonAssignCircle)
	{
		// 确认按钮为圆形，取消按钮为叉形
		SetBrushTexture(EnterIcon, Settings->PSCircleTexture);
		SetBrushTexture(BackspaceIcon, Settings->PSCrossTexture);
	}
	else
	{
		// 确认按钮为叉形，取消按钮为圆形
		SetBrushTexture(EnterIcon, Settings->PSCrossTexture);
		SetBrushTexture(BackspaceIcon, Settings->PSCircleTexture);
	}
}

/**
 * 设置图像的纹理
 * @param TargetImage 目标图像控件
 * @param Texture 要设置的纹理
 */
void UPSOneLogin::SetBrushTexture(UImage* TargetImage, UTexture2D* Texture)
{
	if (!TargetImage || !Texture)
	{
		return;
	}

	FSlateBrush Brush;
	UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Texture);
	TargetImage->SetBrush(Brush);
}

void UPSOneLogin::NativeConstruct()
{
	Super::NativeConstruct();

	// 更新登录类型和焦点
	UpdateLoginType(bIsLoginWithPassword, false);
	FocusNavigator->OnFocus();
}

void UPSOneLogin::NativeDestruct()
{
	Super::NativeDestruct();
}

FReply UPSOneLogin::NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent)
{
	FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);

	UE_LOG(LogTemp, Display, TEXT("Focus Received: %s"), *GetNameSafe(this));
	FocusNavigator->OnFocus();
	return Reply;
}

void UPSOneLogin::NativeOnFocusLost(const FFocusEvent& InFocusEvent)
{
	Super::NativeOnFocusLost(InFocusEvent);
}

/**
 * 处理按键事件
 * 根据不同的按键执行相应的操作
 */
FReply UPSOneLogin::NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent)
{
	const FKey PressedKey = InKeyEvent.GetKey();

	// 三角形按钮，切换登录方式
	if (PressedKey == EKeys::Gamepad_FaceButton_Top)
	{
		if (!bIsMainLand)
		{
			// 海外版，支持邮箱密码登录，根据是否支持邮箱密码登录切换登录方式
			if (bIsSupportEmailPasswordLogin)
			{
				HandleSwitchLoginType();
			}
		}
		else
		{
			HandleSwitchLoginType();
		}
	}
	// 方形按钮，发送验证码
	else if (PressedKey == EKeys::Gamepad_FaceButton_Left)
	{
		HandleSendCodeButton();
	}
	// L1 按钮，查看用户协议
	else if (PressedKey == EKeys::Gamepad_LeftShoulder)
	{
		HandleUserAgreement();
	}
	// R1 按钮，查看隐私政策
	else if (PressedKey == EKeys::Gamepad_RightShoulder)
	{
		HandlePrivacyPolicy();
	}
	// R2 按钮，刷新二维码
	else if (PressedKey == EKeys::Gamepad_RightTrigger)
	{
		HandleRefreshQRCode();
	}
	// 返回键，关闭登录界面
	else if (UPSUserWidgetSettings::IsClickedBackKey(InKeyEvent))
	{
		HandleCloseLogin();
	}
	// 其他按键，交给导航器处理
	else if (FocusNavigator->HandleKeyEvent(InKeyEvent))
	{
		return FReply::Handled();
	}

	return FReply::Handled();
}

/**
 * 处理切换登录类型
 * 在密码登录和验证码登录之间切换
 */
void UPSOneLogin::HandleSwitchLoginType()
{
	UE_LOG(LogTemp, Display, TEXT("Switch Login Type: %s"), bIsLoginWithPassword ? TEXT("Password") : TEXT("SMS"));
	bool UpdateFocus = PasswordField->HasKeyboardFocus() || VerificationCodeField->HasKeyboardFocus();
	UpdateLoginType(!bIsLoginWithPassword, UpdateFocus);
}

/**
 * 处理发送验证码
 * 仅在验证码登录模式下可用
 */
void UPSOneLogin::HandleSendCodeButton()
{
	if (!bIsLoginWithPassword)
	{
		UE_LOG(LogTemp, Display, TEXT("Send Code ..."));
		const FString Account = AccountField->GetContent();
		if (OnClickSendCodeButton)
		{
			OnClickSendCodeButton(Account);
		}
	}
	else
	{
		UE_LOG(LogTemp, Display, TEXT("Password Login, No Send Code"));
	}
}

/**
 * 处理查看用户协议
 */
void UPSOneLogin::HandleUserAgreement()
{
	UE_LOG(LogTemp, Display, TEXT("Open User Agreement ..."));
	if (OpenUserAgreement)
	{
		OpenUserAgreement();
	}
}

/**
 * 处理查看隐私政策
 */
void UPSOneLogin::HandlePrivacyPolicy()
{
	UE_LOG(LogTemp, Display, TEXT("Open Privacy Policy ..."));
	if (OpenPrivacyPolicy)
	{
		OpenPrivacyPolicy();
	}
}

/**
 * 处理刷新二维码
 */
void UPSOneLogin::HandleRefreshQRCode()
{
	UE_LOG(LogTemp, Display, TEXT("Refresh QR Code ..."));
	if (RefreshQrCode)
	{
		RefreshQrCode();
	}
}

/**
 * 处理关闭登录界面
 */
void UPSOneLogin::HandleCloseLogin()
{
	UE_LOG(LogTemp, Display, TEXT("Close Login ..."));
	if (OnClickCloseButton)
	{
		OnClickCloseButton();
	}
}

/**
 * 更新登录类型
 * @param bPasswordLogin 是否为密码登录
 * @param bFocusUpdate 是否更新焦点
 */
void UPSOneLogin::UpdateLoginType(bool bPasswordLogin, bool bFocusUpdate)
{
	this->bIsLoginWithPassword = bPasswordLogin;

	// 更新登录标题和切换按钮文本
	UpdateLoginTypeText();

	// 设置活动的控件，0 是验证码登录，1 是密码登录
	PasswordSwitcher->SetActiveWidgetIndex(bIsLoginWithPassword ? 1 : 0);
	LoginTypeSwitcher->SetActiveWidgetIndex(bIsLoginWithPassword ? 1 : 0);

	// 清除验证码字段
	if (!bIsLoginWithPassword)
	{
		VerificationCodeField->SetContent(TEXT(""));
	}

	// 更新焦点导航器
	FocusNavigator->ReplaceFocusableWidgetByIndex(1, bIsLoginWithPassword ? PasswordField : VerificationCodeField);
}

/**
 * 更新登录类型文本
 * 根据登录类型和版本显示不同的文本
 */
void UPSOneLogin::UpdateLoginTypeText()
{
	if (bIsMainLand)
	{
		if (bIsLoginWithPassword)
		{
			// 大陆版密码登录文本
			LoginTypeTextBlock->SetText(LOCTEXT("MobilePasswordLogin", "手机密码登录"));
			SwitchLoginTypeTextBlock->SetText(LOCTEXT("MobileCodeLogin", "短信验证码登录"));
		}
		else
		{
			// 大陆版验证码登录文本
			LoginTypeTextBlock->SetText(LOCTEXT("MobileCodeLogin", "短信验证码登录"));
			SwitchLoginTypeTextBlock->SetText(LOCTEXT("MobilePasswordLogin", "手机密码登录"));
		}
	}
	else
	{
		if (bIsLoginWithPassword)
		{
			// 海外版密码登录文本
			LoginTypeTextBlock->SetText(LOCTEXT("EmailPasswordLogin", "邮箱密码登录"));
			SwitchLoginTypeTextBlock->SetText(LOCTEXT("EmailCodeLogin", "邮箱验证码登录"));
		}
		else
		{
			// 海外版验证码登录文本
			LoginTypeTextBlock->SetText(LOCTEXT("EmailCodeLogin", "邮箱验证码登录"));
			SwitchLoginTypeTextBlock->SetText(LOCTEXT("EmailPasswordLogin", "邮箱密码登录"));
		}
	}
}

/**
 * 设置二维码的有效状态
 * @param bValid 是否有效
 */
void UPSOneLogin::SetQrCodeValid(bool bValid)
{
	RefreshQRCodeImage->SetVisibility(bValid ? ESlateVisibility::Hidden : ESlateVisibility::Visible);
}

/**
 * 设置二维码图像
 * @param InQrCodeImageBinary 二维码图像二进制数据
 */
void UPSOneLogin::SetQrCodeImage(const TArray<uint8>& InQrCodeImageBinary)
{
	// 将二进制数据导入为纹理
	UTexture2D* TQrCode = FImageUtils::ImportBufferAsTexture2D(InQrCodeImageBinary);
	if (!TQrCode)
	{
		UE_LOG(LogTemp, Error, TEXT("QRCode is invalid"));
		return;
	}

	UE_LOG(LogTemp, Display, TEXT("QRCode is valid, X is %d, Y is %d"), TQrCode->GetSizeX(), TQrCode->GetSizeY());

	// 设置二维码图像
	FSlateBrush QrCodeBrush;
	UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(QrCodeBrush, TQrCode);
	QRCodeImage->SetBrush(QrCodeBrush);
	SetQrCodeValid(true);

	// Just for debug
#if ENGINE_SUPPORT_SONY
#if PLATFORM_PS4 || PLATFORM_PS5
	FString Tmppath = "/download0/qrc.png";
	FFileHelper::SaveArrayToFile(InQrCodeImageBinary, *Tmppath);
#endif
#endif
}

/**
 * 初始化控件
 * 设置导航器、回调和焦点
 */
bool UPSOneLogin::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	// 创建焦点导航器
	FocusNavigator = MakeShareable(new FPSOneFocusNavigator());
	FocusNavigator->SetNavigationOrientation(ENavigationOrientation::Vertical);

	// 设置控件回调和焦点导航
	SetupWidgetCallbacks();
	SetupFocusNavigation();

	PwrdLogo  = LoadObject<UTexture2D>(nullptr, TEXT("Texture2D'/OneEngineSDK/Texture/T_Login_Logo.T_Login_Logo'"));
	IwPlayLogo = LoadObject<UTexture2D>(nullptr, TEXT("Texture2D'/OneEngineSDK/Texture/T_Login_Iwplay.T_Login_Iwplay'"));

	return true;
}

/**
 * 设置控件回调
 * 为各个控件设置焦点切换和点击回调
 */
void UPSOneLogin::SetupWidgetCallbacks()
{
	// 设置登录按钮点击事件
	LoginButton->OnClickCallback = [this]()
	{
		// 获取账号信息
		const FString Account = AccountField->GetContent();
		// 根据登录类型获取密码或验证码
		const FString Password = bIsLoginWithPassword ? PasswordField->GetContent() : VerificationCodeField->GetContent();
		// 获取是否同意协议
		const bool AgreePolicy = AgreePolicyCheckButton->IsChecked();

		// 触发登录回调
		if (OnClickLoginButton)
		{
			OnClickLoginButton(Account, Password, bIsLoginWithPassword, AgreePolicy);
		}
	};
}

/**
 * 设置焦点导航
 * 将控件添加到焦点导航器
 */
void UPSOneLogin::SetupFocusNavigation()
{
	// 添加可导航的控件
	FocusNavigator->AddFocusableWidget(AccountField);
	FocusNavigator->AddFocusableWidget(VerificationCodeField);	  // 可以替换
	FocusNavigator->AddFocusableWidget(AgreePolicyCheckButton);
	FocusNavigator->AddFocusableWidget(LoginButton);
	FocusNavigator->SetFocusLoop();
}

#undef LOCTEXT_NAMESPACE
