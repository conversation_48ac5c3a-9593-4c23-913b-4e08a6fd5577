﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PSOneFocusUserWidget.h"
#include "PSOneDelTips.generated.h"

/**
 *
 */
UCLASS()
class ONEENGINESDK_API UPSOneDelTips : public UPSOneFocusUserWidget
{
	GENERATED_BODY()
public:

	UPROPERTY(meta=(BindWidget))
	class UScaleBox* ScaleBox;

	UPROPERTY(meta=(BindWidget))
	class UBackgroundBlur* BackgroundBlur;

	UPROPERTY(meta = (BindWidget))
	class UTextBlock *TitleTextBlock;

	UPROPERTY(meta = (BindWidget))
	class UTextBlock *DescTextBlock;

	// 恢复按钮
	UPROPERTY(meta = (BindWidget))
	class UPSOneConfirmButton *RestoreButton;

	// 删除按钮
	UPROPERTY(meta = (BindWidget))
	class UPSOneButtonBase *DeleteButton;

	// enter icon
	UPROPERTY(meta = (BindWidget))
	class UImage *EnterIcon;

	// backspace icon
	UPROPERTY(meta = (BindWidget))
	class UImage *BackspaceIcon;
	virtual bool Initialize() override;

	// 点击事件
	TFunction<void()> OnRestore;

	// 点击事件
	TFunction<void()> OnDelete;

	// 点击事件
	TFunction<void()> OnClickCloseButton;

	void SetTitleText(const FString &Text);

	void SetDescText(const FString &Text);

	// userinfo
	FString Uid;
	FString Token;

protected:
	virtual void NativePreConstruct() override;
	virtual FReply NativeOnFocusReceived(const FGeometry &InGeometry, const FFocusEvent &InFocusEvent) override;
	virtual FReply NativeOnKeyDown(const FGeometry &InGeometry, const FKeyEvent &InKeyEvent) override;

private:
	TSharedPtr<class FPSOneFocusNavigator> FocusNavigator;
};
