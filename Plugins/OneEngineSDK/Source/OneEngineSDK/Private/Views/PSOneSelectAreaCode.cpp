// Fill out your copyright notice in the Description page of Project Settings.

#include "Views/PSOneSelectAreaCode.h"
#include "PSOneFocusNavigator.h"
#include "PSOneUserCenterRightCellSubtitle.h"
#include "PSUserWidgetSettings.h"
#include "Components/Image.h"
#include "Components/ScaleBox.h"
#include "Components/ScrollBox.h"

namespace
{
	// WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_UserCenterRightCellSubtitle.WBP_ONE_UserCenterRightCellSubtitle'
	FString AreaCodeCellPath = TEXT("WidgetBlueprint'/OneEngineSDK/Widget/WBP_ONE_UserCenterRightCellSubtitle.WBP_ONE_UserCenterRightCellSubtitle_C'");
}

bool UPSOneSelectAreaCode::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	FocusNavigator = MakeShareable(new FPSOneFocusNavigator());
	return true;
}

void UPSOneSelectAreaCode::BindData(const TArray<FPSOneAreaCode> &InAreaCodes)
{
	AreaCodes = InAreaCodes;
	FocusNavigator->FocusableWidgets.Empty();
	// Refresh UI
	ScrollBox->ClearChildren();
	auto WidgetClass = LoadClass<UPSOneUserCenterRightCellSubtitle>(nullptr, *AreaCodeCellPath);
	if (!WidgetClass)
	{
		UE_LOG(LogTemp, Error, TEXT("UPSOneSelectAreaCode::BindData: Failed to load widget class %s"), *AreaCodeCellPath);
		return;
	}
	UWorld *World = GetWorld();
	for (const FPSOneAreaCode &AreaCode : AreaCodes)
	{
		auto Widget = CreateWidget<UPSOneUserCenterRightCellSubtitle>(World, WidgetClass);
		Widget->Icon = nullptr;
		Widget->TextSize = 12;
		Widget->HiddenArrow = true;
		Widget->Title = FText::FromString(FString::Printf(TEXT("%d"), AreaCode.Code));
		Widget->SubTitle = FText::FromString(AreaCode.Area);
		FPSOneAreaCode CopyAreaCode = AreaCode;
		Widget->OnClickCallback = [this, CopyAreaCode]()
		{
			if (OnClicked)
			{
				OnClicked(CopyAreaCode);
			}
		};
		FocusNavigator->AddFocusableWidget(Widget);
		ScrollBox->AddChild(Widget);
	}
	FocusNavigator->SetFocusLoop();
	FocusNavigator->OnFocus();
}

void UPSOneSelectAreaCode::NativePreConstruct()
{
	Super::NativePreConstruct();
	AreaCodes.Empty();
	UPSUserWidgetSettings *Settings = UPSUserWidgetSettings::Get();
	ScaleBox->SetUserSpecifiedScale(Settings->GetUIScaleRatio());
	if (Settings->bEnterButtonAssignCircle)
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			EnterIcon->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			BackspaceIcon->SetBrush(Brush);
		}
	}
	else
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			EnterIcon->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			BackspaceIcon->SetBrush(Brush);
		}
	}
}

FReply UPSOneSelectAreaCode::NativeOnFocusReceived(const FGeometry &InGeometry, const FFocusEvent &InFocusEvent)
{
	FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
	FocusNavigator->OnFocus();
	return Reply;
}

FReply UPSOneSelectAreaCode::NativeOnKeyDown(const FGeometry &InGeometry, const FKeyEvent &InKeyEvent)
{
	if (FocusNavigator->HandleKeyEvent(InKeyEvent))
	{
		return FReply::Handled();
	}
	else if (UPSUserWidgetSettings::IsClickedBackKey(InKeyEvent))
	{
		if (OnBackspace)
		{
			OnBackspace();
		}
	}
	return FReply::Handled();
}
