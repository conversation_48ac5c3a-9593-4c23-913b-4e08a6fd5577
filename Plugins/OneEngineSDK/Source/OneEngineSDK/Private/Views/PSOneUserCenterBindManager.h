// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Views/PSOneFocusUserWidget.h"
#include "PSOneUserCenterBindManager.generated.h"


USTRUCT()
struct FPSOneBindItem
{
	GENERATED_BODY()
	int Id;
	bool bBind;
};

/**
 * 
 */
UCLASS()
class UPSOneUserCenterBindManager : public UPSOneFocusUserWidget
{
	GENERATED_BODY()

public:
	UPROPERTY(meta=(BindWidget))
	class UTextBlock* TitleBlock;

	UPROPERTY(meta=(BindWidget))
	class UScrollBox* ScrollBox;

	void SetBindItems(const TArray<FPSOneBindItem>& Items);

	TFunction<void(const FPSOneBindItem&)> OnBindItemCallback;

	TFunction<void(TArray<UWidget*>)>OnSetupBindItemCallback; 
};
