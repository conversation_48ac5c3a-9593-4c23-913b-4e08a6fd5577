// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Views/PSOneButtonBase.h"
#include "PSOneFocusCheckButton.generated.h"

/**
 *
 */
UCLASS()
class UPSOneFocusCheckButton : public UPSOneButtonBase
{
	GENERATED_BODY()
public:

	UPROPERTY(EditAnywhere)
	class UTexture2D* CheckedTexture;

	UPROPERTY(EditAnywhere)
	class UTexture2D* UncheckedImage;

	UPROPERTY(meta = (BindWidget))
	class UImage *CheckBoxImage;

	bool IsChecked() const { return bIsChecked; }
protected:
	bool bIsChecked = false;

	virtual void ButtonDidClick() override;

	void UpdateCheckBoxImage();
};
