// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Views/PSOneFocusUserWidget.h"
#include "PSOneShowIphoneStatus.generated.h"

/**
 *
 */
UCLASS()
class UPSOneShowIphoneStatus : public UPSOneFocusUserWidget
{
	GENERATED_BODY()

public:

	UPROPERTY(meta=(BindWidget))
	class UScaleBox* ScaleBox;

	// 显示的号码
	UPROPERTY(meta=(BindWidget))
	class UTextBlock* TitleTextBlock;

	// 绑定描述
	UPROPERTY(meta=(BindWidget))
	class UTextBlock* BindDescTextBlock;

	// 绑定正文
	UPROPERTY(meta=(BindWidget))
	class UTextBlock* BindTextBlock;

	UPROPERTY(meta=(BindWidget))
	class UPSOneConfirmButton* ActionButton;

	// enter icon
	UPROPERTY(meta=(BindWidget))
	class UImage* EnterIcon;

	// backspace icon
	UPROPERTY(meta=(BindWidget))
	class UImage* BackspaceIcon;

	UPROPERTY(meta=(BindWidget))
	class UHorizontalBox* UnbindBox;

	UPROPERTY(meta=(BindWidget))
	class UImage* UnbindIcon;

	UPROPERTY(EditAnywhere)
	bool HideUnbind = false;

	void SetBindText(const FString& Text);

	// 点击事件
	TFunction<void()> OnClicked;

	// 点击事件
	TFunction<void()> OnBackspace;

	// 手机解绑
	TFunction<void()> OnUnbind;

	virtual bool Initialize() override;
protected:
	virtual void NativePreConstruct() override;
	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;
	virtual FReply NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent) override;
};
