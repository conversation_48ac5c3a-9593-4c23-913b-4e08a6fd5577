﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "PSOneTextFieldBase.generated.h"

UCLASS()
class ONEENGINESDK_API UPSOneTextFieldBase : public UUserWidget
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere)
	bool bIsPassword{false};

	UPROPERTY(EditAnywhere)
	TEnumAsByte<EVirtualKeyboardType::Type> VirtualKeyboardType{EVirtualKeyboardType::Default};

	UPROPERTY(EditAnywhere)
	FText DefaultText;

	UPROPERTY(EditAnywhere)
	FText HintText;

	UPROPERTY(meta=(BindWidget))
	class UBorder* BgBorder;

	UPROPERTY(meta=(BindWidget))
	class UEditableText* TextField;

	UPROPERTY(meta=(BindWidget))
	class UTextBlock* HintTextBlock;

	// normal
	UPROPERTY(EditAnywhere)
	class UTexture2D* NormalTexture;

	// onFocus
	UPROPERTY(EditAnywhere)
	class UTexture2D* FocusTexture;

	// UTextBlock callback
	TFunction<void(const FText& Text)> OnTextChanged;

	TFunction<void(const FText& Text)> OnTextCommitted;

	//NextTickOnTextCommitted
	TFunction<void(const FText& Text)> NextTickOnTextChanged;


	TFunction<void(UUserWidget* Widget, bool bIsFocus)> OnFocusSwitch;

	// Get text
	FString GetContent() const;

	void SetContent(const FString& Text);

	// Set hint text
	void SetHintText(const FText& Text);

	virtual bool Initialize() override;

protected:
	virtual void NativePreConstruct() override;
	void UpdateBorderBrush(UTexture2D* Texture);
	void UpdateHintTextVisibility(const FText& Text);
	// Override the focus path event
	virtual void NativeOnAddedToFocusPath(const FFocusEvent& InFocusEvent) override;
	virtual void NativeOnRemovedFromFocusPath(const FFocusEvent& InFocusEvent) override;
	// Override the focus event
	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;
	virtual FReply NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent) override;

private:
	UFUNCTION()
	void InternalOnTextChanged(const FText& Text);

	UFUNCTION()
	void InternalOnTextCommitted(const FText& Text, ETextCommit::Type CommitType);

	UFUNCTION()
	void DelayFocus();

	bool bIsFocus{false};
};
