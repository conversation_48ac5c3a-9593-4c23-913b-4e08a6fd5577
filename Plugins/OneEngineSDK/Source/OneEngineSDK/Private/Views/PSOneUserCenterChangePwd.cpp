﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "PSOneUserCenterChangePwd.h"
#include "PSOneFocusNavigator.h"
#include "PSOneButtonBase.h"
#include "PSOneTextFieldBase.h"
#include "PSUserWidgetSettings.h"
#include "Components/Image.h"
#include "Components/ScaleBox.h"
#include "Components/TextBlock.h"
#include "TimerManager.h"

#define LOCTEXT_NAMESPACE "PSOneUserCenterChangePwd"

void UPSOneUserCenterChangePwd::SetPhoneNumber(const FString &PhoneNumber)
{
	TargetPhoneNumber = PhoneNumber;
}

bool UPSOneUserCenterChangePwd::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	FocusNavigator = MakeShareable(new FPSOneFocusNavigator());

	// 验证码输入框
	FocusNavigator->AddFocusableWidget(CodeInputTextField);

	// 新密码输入框
	FocusNavigator->AddFocusableWidget(NewPwdTextField);

	// 确认新密码
	FocusNavigator->AddFocusableWidget(ConfirmPwdTextField);

	// 确认按钮
	FocusNavigator->AddFocusableWidget(ConfirmButton);

	FocusNavigator->SetFocusLoop();
	

	ConfirmButton->OnClickCallback = [this]()
	{
		if (OnClickConfirmButton)
		{
			OnClickConfirmButton(CodeInputTextField->GetContent(), NewPwdTextField->GetContent(), ConfirmPwdTextField->GetContent());
		}
	};

	return true;
}

void UPSOneUserCenterChangePwd::NativePreConstruct()
{
	Super::NativePreConstruct();
	UPSUserWidgetSettings *Settings = UPSUserWidgetSettings::Get();
	ScaleBox->SetUserSpecifiedScale(Settings->GetUIScaleRatio());
	
	if (Settings->bEnterButtonAssignCircle)
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			EnterIcon->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			BackspaceIcon->SetBrush(Brush);
		}
	}
	else
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			EnterIcon->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			BackspaceIcon->SetBrush(Brush);
		}
	}

	TitleBlock->SetText(TitleText);
	SendCodeTip->SetText(
		FText::Format(LOCTEXT("VerificationCodeSent", "验证码发送至{0}，请输入验证码"), FText::FromString(TargetPhoneNumber)));
	SetNewPwdTip->SetText(FText::Format(LOCTEXT("SetNewPassword", "设置账号{0}的新密码"), FText::FromString(TargetPhoneNumber)));
}

void UPSOneUserCenterChangePwd::NativeConstruct()
{
	Super::NativeConstruct();

	CodeInputTextField->SetKeyboardFocus();
	FTimerHandle DelaySendCodeTimerHandle;
	GetWorld()->GetTimerManager().SetTimer(DelaySendCodeTimerHandle, [this]()
										   {
		if (OnSendCodeRequest)
		{
			OnSendCodeRequest();
		} }, 0.2f, false);
}

FReply UPSOneUserCenterChangePwd::NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent)
{
	FReply Reply =  Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
	FocusNavigator->OnFocus();
	return Reply;
}

FReply UPSOneUserCenterChangePwd::NativeOnKeyDown(const FGeometry &InGeometry, const FKeyEvent &InKeyEvent)
{
	if (FocusNavigator->HandleKeyEvent(InKeyEvent))
	{
		return FReply::Handled();
	}
	else if (InKeyEvent.GetKey() == EKeys::Gamepad_RightShoulder)
	{
		if (OnSendCodeRequest)
		{
			OnSendCodeRequest();
		}
	}
	else if (UPSUserWidgetSettings::IsClickedBackKey(InKeyEvent))
	{
		if (OnClickCloseButton)
		{
			OnClickCloseButton();
		}
	}
	return FReply::Handled();
}

void UPSOneUserCenterChangePwd::StartCountDown()
{
}

void UPSOneUserCenterChangePwd::StopCountDown()
{
}

#undef LOCTEXT_NAMESPACE
