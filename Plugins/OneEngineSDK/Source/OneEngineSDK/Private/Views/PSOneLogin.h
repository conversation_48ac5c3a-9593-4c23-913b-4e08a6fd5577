﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PSOneFocusUserWidget.h"
#include "PSOneLogin.generated.h"

/**
 * 登录界面控件
 * 支持大陆版和海外版两种模式
 * 支持密码登录和验证码登录两种方式
 * 大陆版还支持二维码登录
 */
UCLASS()
class ONEENGINESDK_API UPSOneLogin : public UPSOneFocusUserWidget
{
	GENERATED_BODY()

public:

	UPROPERTY(meta=(BindWidget))
	class UScaleBox* ScaleBox;
	
	// 背景模糊组件
	UPROPERTY(meta=(bindWidget))
	class UBackgroundBlur* BackgroundBlur;
	
	// 内容包裹器，用于垂直布局内容
	UPROPERTY(meta=(BindWidget))
	class UVerticalBox* ContentVerticalBox;
	
	// 内容尺寸盒，根据版本设置不同的宽度 (860 for mainland, 680 for global)
	UPROPERTY(meta=(BindWidget))
	class USizeBox* ContentSizeBox;

	// 登录垂直框，包含登录相关控件
	UPROPERTY(meta=(BindWidget))
	class UVerticalBox* LoginVerticalBox;

	// 垂直分割线，分隔账号登录和二维码登录
	UPROPERTY(meta=(BindWidget))
	class UImage* VerticalSplitLine;

	// 二维码垂直框，包含二维码相关控件
	UPROPERTY(meta=(BindWidget))
	class UVerticalBox* QRCodeVerticalBox;

	// logo 图标
	UPROPERTY(meta=(BindWidget))
	class UImage* LogoIcon;

	// 登录类型文本，显示当前登录方式
	UPROPERTY(meta=(BindWidget))
	class UTextBlock* LoginTypeTextBlock;

	// 账号输入框
	UPROPERTY(meta=(BindWidget))
	class UPSOneTextFieldBase* AccountField;

	// 密码切换器，用于切换密码和验证码输入框
	UPROPERTY(meta=(BindWidget))
	class UWidgetSwitcher* PasswordSwitcher;

	// 密码输入框
	UPROPERTY(meta=(BindWidget))
	class UPSOneTextFieldBase* PasswordField;

	// 验证码输入框
	UPROPERTY(meta=(BindWidget))
	class UPSOneTextFieldVerificationCode* VerificationCodeField;

	// 阅读并同意协议复选框
	UPROPERTY(meta=(BindWidget))
	class UPSOneFocusCheckButton* AgreePolicyCheckButton;

	// 登录按钮
	UPROPERTY(meta=(BindWidget))
	class UPSOneConfirmButton* LoginButton;

	// 二维码图像
	UPROPERTY(meta= (BindWidget))
	class UImage* QRCodeImage;

	// 刷新二维码图像
	UPROPERTY(meta= (BindWidget))
	class UImage* RefreshQRCodeImage;

	// 登录类型切换器
	UPROPERTY(meta=(BindWidget))
	class UWidgetSwitcher* LoginTypeSwitcher;

	// 发送验证码图标（方形按钮）
	UPROPERTY(meta=(BindWidget))
	class UImage* SendCodeIcon;

	// 验证码登录图标（三角形按钮）
	UPROPERTY(meta=(BindWidget))
	class UImage* CodeLoginIcon;

	// 账号登录图标（三角形按钮）
	UPROPERTY(meta=(BindWidget))
	class UImage* AccountLoginIcon;

	// 邮箱密码登录水平框
	UPROPERTY(meta=(BindWidget))
	class UHorizontalBox* EmailSwitchLoginHorizontalBox;

	// 切换登录方式文本
	UPROPERTY(meta=(BindWidget))
	class UTextBlock* SwitchLoginTypeTextBlock;

	// 确认图标（圆形或叉形按钮）
	UPROPERTY(meta=(BindWidget))
	class UImage* EnterIcon;

	// 返回图标（圆形或叉形按钮）
	UPROPERTY(meta=(BindWidget))
	class UImage* BackspaceIcon;

	// 登录点击事件回调函数
	TFunction<void(const FString& Account, const FString& Password, bool bIsPasswordLogin,bool bIsAgreePolicy)> OnClickLoginButton;

	// 查看用户协议回调函数
	TFunction<void()> OpenUserAgreement;

	// 查看隐私保护政策回调函数
	TFunction<void()> OpenPrivacyPolicy;

	// 刷新二维码回调函数
	TFunction<void()> RefreshQrCode;

	// 发送验证码回调函数
	TFunction<void(const FString& Account)> OnClickSendCodeButton;

	// 关闭登录界面回调函数
	TFunction<void()> OnClickCloseButton;

	/**
	 * 设置二维码的有效状态
	 * @param bValid 是否有效，有效时隐藏刷新图标，无效时显示刷新图标
	 */
	void SetQrCodeValid(bool bValid);

	/**
	 * 设置二维码图像
	 * @param InQrCodeImageBinary 二维码图像的二进制数据
	 */
	void SetQrCodeImage(const TArray<uint8>& InQrCodeImageBinary);

	/**
	 * 初始化控件
	 * 创建焦点导航器，设置回调和焦点
	 */
	virtual bool Initialize() override;

	// 是否为大陆版，影响登录界面的布局和提示文本
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bIsMainLand{false};

	// 大陆版内容宽度
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float MainLandContentWidth{980};

	// 海外版内容宽度
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float GlobalContentWidth{680};

	// 是否使用密码登录，false 表示使用验证码登录
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bIsLoginWithPassword{false};

	// 支持邮箱密码登录
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bIsSupportEmailPasswordLogin{true};


	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bIsIwPlay {false};

protected:
	// UI 生命周期方法
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void NativeDestruct() override;
	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;
	virtual void NativeOnFocusLost(const FFocusEvent& InFocusEvent) override;

	/**
	 * 处理按键事件
	 * 根据不同的按键执行相应的操作
	 */
	virtual FReply NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent) override;

	/**
	 * 更新登录类型
	 * @param bPasswordLogin 是否为密码登录
	 * @param bFocusUpdate 是否更新焦点
	 */
	virtual void UpdateLoginType(bool bPasswordLogin, bool bFocusUpdate = true);

	// UI 初始化相关方法

	/**
	 * 初始化UI布局
	 * 根据是否为大陆版设置不同的布局和提示文本
	 */
	void InitializeUILayout();

	/**
	 * 初始化UI纹理
	 * 设置各个图标的纹理
	 */
	void InitializeUITextures();

	/**
	 * 配置背景模糊效果
	 * 根据用户设置决定是否启用背景模糊
	 */
	void ConfigureBackgroundBlur();

	/**
	 * 配置按钮图标
	 * 根据是否将确认按钮分配为圆形，设置不同的按钮图标
	 */
	void ConfigureButtonIcons(class UPSUserWidgetSettings* Settings);

	/**
	 * 设置图像的纹理
	 * @param TargetImage 目标图像控件
	 * @param Texture 要设置的纹理
	 */
	void SetBrushTexture(UImage* TargetImage, UTexture2D* Texture);

	/**
	 * 更新登录类型文本
	 * 根据登录类型和版本显示不同的文本
	 */
	void UpdateLoginTypeText();

	// 按键处理相关方法

	/**
	 * 处理切换登录类型
	 * 在密码登录和验证码登录之间切换
	 */
	void HandleSwitchLoginType();

	/**
	 * 处理发送验证码
	 * 仅在验证码登录模式下可用
	 */
	void HandleSendCodeButton();

	/**
	 * 处理查看用户协议
	 */
	void HandleUserAgreement();

	/**
	 * 处理查看隐私政策
	 */
	void HandlePrivacyPolicy();

	/**
	 * 处理刷新二维码
	 */
	void HandleRefreshQRCode();

	/**
	 * 处理关闭登录界面
	 */
	void HandleCloseLogin();

	// 控件初始化和焦点管理

	/**
	 * 设置控件回调
	 * 为各个控件设置焦点切换和点击回调
	 */
	void SetupWidgetCallbacks();

	/**
	 * 设置焦点导航
	 * 将控件添加到焦点导航器
	 */
	void SetupFocusNavigation();

	UPROPERTY(BlueprintReadOnly)
	UTexture2D* PwrdLogo;

	UPROPERTY(BlueprintReadOnly)
	UTexture2D* IwPlayLogo;

private:
	// 焦点导航器，管理控件之间的焦点切换
	TSharedPtr<class FPSOneFocusNavigator> FocusNavigator;
};
