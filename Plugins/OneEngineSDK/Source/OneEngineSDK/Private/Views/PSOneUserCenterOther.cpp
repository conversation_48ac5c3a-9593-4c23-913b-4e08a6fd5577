﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "PSOneUserCenterOther.h"
#include "PSOneUserCenterRightCellSubtitle.h"


bool UPSOneUserCenterOther::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	DeleteAccountCell->OnClickCallback = [this]()
	{
		if (OnClickDeleteAccount)
		{
			OnClickDeleteAccount();
		}
	};
	return true;
}

FReply UPSOneUserCenterOther::NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent)
{
	FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
	return Reply;
}
