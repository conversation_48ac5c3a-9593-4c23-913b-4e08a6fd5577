﻿#pragma once

#include "CoreMinimal.h"

// 定义焦点移动方向枚举
enum class EFocusMoveDirection : uint8
{
	Up,
	Down,
	Left,
	Right
};

// 定义导航方向枚举
enum class ENavigationOrientation : uint8
{
	Vertical,    // 垂直导航，上下键用于父子级导航
	Horizontal,  // 水平导航，左右键用于父子级导航
};


// 焦点变化回调函数
DECLARE_DELEGATE_TwoParams(FFocusChangedDelegate, int32 /*OldIndex*/, int32 /*NewIndex*/);

// Enter 操作回调函数
DECLARE_DELEGATE(FEnterDelegate);

// Back 操作回调函数
DECLARE_DELEGATE(FBackDelegate);

class FPSOneFocusNavigator
{
public:
	FPSOneFocusNavigator();
	virtual  ~FPSOneFocusNavigator();

	// 设置是否启用调试日志
	void SetEnableDebugLog(bool bEnable);

	// 焦点控件集合管理
	TArray<TWeakObjectPtr<class UUserWidget>> FocusableWidgets;

	// 设置导航方向
	void SetNavigationOrientation(ENavigationOrientation Orientation);

	// 获取导航方向
	ENavigationOrientation GetNavigationOrientation() const;

	// 添加可接受焦点的控件
	void AddFocusableWidget(UUserWidget* Widget);

	// 替换可接受焦点的控件
	void ReplaceFocusableWidgetByIndex(int32 Index, UUserWidget* NewWidget);

	// 根据索引设置焦点
	bool SetFocusByIndex(int32 Index);

	// 根据方向移动焦点
	bool MoveFocus(EFocusMoveDirection Direction);

	// 获取当前焦点控件
	UUserWidget* GetCurrentFocusWidget() const;

	// 获取当前焦点索引
	int32 GetCurrentFocusIndex() const;

	// 父级导航管理
	FPSOneFocusNavigator* GetParent();

	// 设置父级导航
	void SetParent(FPSOneFocusNavigator* Parent);

	// 子级导航管理
	FPSOneFocusNavigator* GetChildren();

	// 设置子级导航
	void SetChildren(FPSOneFocusNavigator* Children);

	// 尝试从当前导航器切换到子级导航器
	bool  TryNavigateToChild();

	// 尝试从当前导航器切换到父级导航器
	bool  TryNavigateToParent();

	// 获取当前拥有焦点的导航器
	FPSOneFocusNavigator* GetCurrentFocusNavigator();

	// 查找当前拥有焦点的导航器
	FPSOneFocusNavigator* FindCurrentFocusNavigator();

	// 设置此导航器为活动状态
	void SetAsActiveFocusNavigator();

	// 检查此导航器是否拥有焦点
	bool HasFocus();

	// 清理无效引用
	void CleanInvalidReferences();

	// 检查控件是否可以接收焦点（可见且启用）
	bool IsWidgetFocusable(UUserWidget* Widget) const;

	// 查找并设置下一个可用控件
	bool FindAndSetNextFocusableWidget(EFocusMoveDirection Direction = EFocusMoveDirection::Right);

	// 设置是否自动清理无效引用
	void SetAutoCleanInvalidReferences(bool bEnable);

	// 当导航器获得焦点时调用，处理焦点恢复或首次聚焦
	bool OnFocus();

	// 焦点改变事件 - C++回调
	FFocusChangedDelegate OnFocusChanged;
	// Enter 事件 - C++回调
	FEnterDelegate OnEnterCallback;
	// Back 事件 - C++回调
	FBackDelegate OnBackCallback;

    bool HandleKeyEvent(const FKeyEvent& Event);

	// 设置焦点循环
	void SetFocusLoop();
	
protected:

	// 当前焦点索引
	int32 CurrentFocusIndex;

	// 导航方向
	ENavigationOrientation NavigationOrientation;

	// 父级导航器
	FPSOneFocusNavigator* ParentNavigator{nullptr};

	// 子级导航器
	FPSOneFocusNavigator* ChildrenNavigator{nullptr};

	// 标记此导航器是否拥有焦点
	bool bHasFocus;

	// 是否自动清理无效引用
	bool bAutoCleanInvalidReferences;

	// 是否启用调试日志
	bool bEnableDebugLog {false};

	// 内部处理焦点变化
	virtual void HandleFocusChanged(int32 OldIndex, int32 NewIndex);

	// 内部处理 Enter 操作
	virtual void HandleEnter();

	// 内部处理 Back 操作
	virtual void HandleBack();

	// 日志辅助函数
	void LogInfo(const FString& Message) const;

	// 递归清除所有导航器的焦点状态
	void ClearFocusStatus(FPSOneFocusNavigator* Navigator);
};
