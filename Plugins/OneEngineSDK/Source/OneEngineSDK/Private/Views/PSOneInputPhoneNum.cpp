﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "Views/PSOneInputPhoneNum.h"
#include "PSOneFocusNavigator.h"
#include "PSOneConfirmButton.h"
#include "PSOneTextFieldBase.h"
#include "PSUserWidgetSettings.h"
#include "Components/Button.h"
#include "Components/EditableText.h"
#include "Components/Image.h"
#include "Components/ScaleBox.h"
#include "Components/TextBlock.h"

#define LOCTEXT_NAMESPACE "PSOneInputPhoneNum"

void UPSOneInputPhoneNum::SetAreaCodeId(int InId)
{
	SelectAreaCodeId = InId;
}

int UPSOneInputPhoneNum::GetAreaCodeId() const
{
	return SelectAreaCodeId;
}

void UPSOneInputPhoneNum::SetAreaCode(int InCode)
{
	SelectAreaCodeCode = InCode;
}

int UPSOneInputPhoneNum::GetAreaCode() const
{
	return SelectAreaCodeCode;
}

FString UPSOneInputPhoneNum::GetPhoneNumber() const
{
	return PhoneNumTextField->GetContent();
}

bool UPSOneInputPhoneNum::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}

	// 创建子导航器
	FocusNavigator = MakeShareable(new FPSOneFocusNavigator());
	FocusNavigator->SetNavigationOrientation(ENavigationOrientation::Horizontal);
	FocusNavigator->AddFocusableWidget(AreaCodeButton);
	FocusNavigator->AddFocusableWidget(PhoneNumTextField);
	FocusNavigator->SetFocusLoop();

	FocusNavigator1 = MakeShareable(new FPSOneFocusNavigator());
	FocusNavigator1->SetNavigationOrientation(ENavigationOrientation::Horizontal);
	FocusNavigator1->AddFocusableWidget(ActionButton);
	FocusNavigator1->SetFocusLoop();

	// 设置子导航器，以便支持上下导航
	FocusNavigator->SetChildren(FocusNavigator1.Get());
	AreaCodeButton->OnClickCallback = [this]()
	{
		AreaCodeButton->bIsFocus = false;
		if (OnClickAreaCode)
		{
			OnClickAreaCode();
		}
	};
	
	ActionButton->OnClickCallback = [this]()
	{
		if (OnClickNext)
		{
			OnClickNext();
		}
	};
	return true;
}

void UPSOneInputPhoneNum::NativePreConstruct()
{
	Super::NativePreConstruct();
	if (InputType == EPSOneInputPhoneNumType::PhoneNum)
	{
		TitleTextBlock->SetText(LOCTEXT("BindPhone", "绑定手机"));
		PhoneNumTextField->SetHintText(LOCTEXT("PhoneNumHint", "请输入手机号"));
		AreaCodeButton->SetVisibility(ESlateVisibility::Visible);
		AreaCodeButton->SetTitle(FText::FromString(FString::Printf(TEXT("%d"), SelectAreaCodeCode)));
	}
	else
	{
		TitleTextBlock->SetText(LOCTEXT("BindEmail", "绑定邮箱"));
		AreaCodeButton->SetVisibility(ESlateVisibility::Collapsed);
		PhoneNumTextField->SetHintText(LOCTEXT("EmailHint", "请输入邮箱"));
	}
	UPSUserWidgetSettings *Settings = UPSUserWidgetSettings::Get();
	ScaleBox->SetUserSpecifiedScale(Settings->GetUIScaleRatio());

	if (Settings->bEnterButtonAssignCircle)
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			EnterIcon->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			BackspaceIcon->SetBrush(Brush);
		}
	}
	else
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			EnterIcon->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			BackspaceIcon->SetBrush(Brush);
		}
	}
}

FReply UPSOneInputPhoneNum::NativeOnFocusReceived(const FGeometry &InGeometry, const FFocusEvent &InFocusEvent)
{
	UE_LOG(LogTemp, Display, TEXT("UPSOneInputPhoneNum::NativeOnFocusReceived"));
	FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
	FocusNavigator->GetCurrentFocusNavigator()->OnFocus();
	return Reply;
}

FReply UPSOneInputPhoneNum::NativeOnKeyDown(const FGeometry &InGeometry, const FKeyEvent &InKeyEvent)
{
	if (FocusNavigator->HandleKeyEvent(InKeyEvent))
	{
		return FReply::Handled();
	}
	else if (UPSUserWidgetSettings::IsClickedBackKey(InKeyEvent))
	{
		if (OnBackspace)
		{
			OnBackspace();
		}
	}
	return FReply::Handled();
}

#undef LOCTEXT_NAMESPACE
