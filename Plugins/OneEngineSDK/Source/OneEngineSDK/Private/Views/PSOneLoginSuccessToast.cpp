﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "PSOneLoginSuccessToast.h"

#include "Components/SizeBox.h"
#include "Components/TextBlock.h"

void UPSOneLoginSuccessToast::SetMessage(const FText& Message)
{
	TextBlock->SetText(Message);
}

void UPSOneLoginSuccessToast::HiddenIcon(bool bHidden)
{
	ImageSizeBox->SetVisibility(bHidden ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
}
