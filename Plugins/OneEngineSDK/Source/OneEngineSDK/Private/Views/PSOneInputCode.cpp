// Fill out your copyright notice in the Description page of Project Settings.

#include "Views/PSOneInputCode.h"
#include "PSOneFocusNavigator.h"
#include "PSOneConfirmButton.h"
#include "PSOneTextFieldBase.h"
#include "PSUserWidgetSettings.h"
#include "Components/HorizontalBox.h"
#include "Components/Image.h"
#include "Components/ScaleBox.h"
#include "Components/TextBlock.h"

void UPSOneInputCode::SetTitleText(const FString &Text)
{
	TitleTextBlock->SetText(FText::FromString(Text));
}

void UPSOneInputCode::SetTargetText(const FString &Text)
{
	TargetText = Text;
	TargetTextBlock->SetText(FText::FromString(Text));
}

bool UPSOneInputCode::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	FocusNavigator = MakeShareable(new FPSOneFocusNavigator());

	FocusNavigator->AddFocusableWidget(CodeTextField);
	FocusNavigator->AddFocusableWidget(ActionButton);
	
	FocusNavigator->SetFocusLoop();
	
	ActionButton->OnClickCallback = [this]()
	{
		if (OnClicked)
		{
			const FString Code = CodeTextField->GetContent();
			OnClicked(Code);
		}
	};
	return true;
}

void UPSOneInputCode::NativePreConstruct()
{
	Super::NativePreConstruct();
	UPSUserWidgetSettings *Settings = UPSUserWidgetSettings::Get();
	ScaleBox->SetUserSpecifiedScale(Settings->GetUIScaleRatio());

	if (TargetText.IsEmpty())
	{
		BottomHorizontalBox->SetVisibility(ESlateVisibility::Collapsed);
	}
	else
	{
		BottomHorizontalBox->SetVisibility(ESlateVisibility::Visible);
	}

	{
		FSlateBrush Brush;
		UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSSquareTexture);
		SendCodeIcon->SetBrush(Brush);
	}

	if (Settings->bEnterButtonAssignCircle)
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			EnterIcon->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			BackspaceIcon->SetBrush(Brush);
		}
	}
	else
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			EnterIcon->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			BackspaceIcon->SetBrush(Brush);
		}
	}
}

FReply UPSOneInputCode::NativeOnFocusReceived(const FGeometry &InGeometry, const FFocusEvent &InFocusEvent)
{
	FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
	FocusNavigator->OnFocus();
	return Reply;
}

FReply UPSOneInputCode::NativeOnKeyDown(const FGeometry &InGeometry, const FKeyEvent &InKeyEvent)
{
	if (FocusNavigator->HandleKeyEvent(InKeyEvent))
	{
		return FReply::Handled();
	}
	else if (InKeyEvent.GetKey() == EKeys::Gamepad_FaceButton_Left)
	{
		if (OnSendCode)
		{
			OnSendCode();
		}
	}
	else if (UPSUserWidgetSettings::IsClickedBackKey(InKeyEvent))
	{
		if (OnBackspace)
		{
			OnBackspace();
		}
	}
	return FReply::Handled();
}
