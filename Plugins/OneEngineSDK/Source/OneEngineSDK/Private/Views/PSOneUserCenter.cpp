﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "PSOneUserCenter.h"

#include "PSOneAvatarImage.h"
#include "PSOneUserCenterAccountInfo.h"
#include "PSOneUserCenterBindManager.h"
#include "PSOneUserCenterDeviceManager.h"
#include "PSOneUserCenterIDInfo.h"
#include "PSOneUserCenterLeftCell.h"
#include "PSOneUserCenterLegalTerms.h"
#include "PSOneUserCenterOther.h"
#include "PSUserWidgetSettings.h"
#include "Components/BackgroundBlur.h"
#include "Components/Image.h"
#include "Components/TextBlock.h"
#include "Components/VerticalBox.h"
#include "Components/WidgetSwitcher.h"
#include "Serialization/JsonSerializer.h"

#include "PSOneUserCenterRightCellAvatar.h"
#include "PSOneUserCenterRightCellSubtitle.h"
#include "PSOneFocusNavigator.h"
#include "Components/ScaleBox.h"

#define LOCTEXT_NAMESPACE "PSOneUserCenter"

DEFINE_LOG_CATEGORY(LogPSOneUserCenter);

namespace
{
	TMap<int32, TSharedPtr<FPSOneFocusNavigator>> TabWidgetMap;

	void SetTabWidgetFocusNavigator(int32 TabIndex, TSharedPtr<FPSOneFocusNavigator> FocusNavigator)
	{
		TabWidgetMap.Add(TabIndex, FocusNavigator);
	}

	TSharedPtr<FPSOneFocusNavigator> GetTabWidgetFocusNavigator(int32 TabIndex)
	{
		return TabWidgetMap.FindRef(TabIndex);
	}

	bool IsTabWidgetFocusNavigatorExist(int32 TabIndex)
	{
		return TabWidgetMap.Contains(TabIndex);
	}

	void ClearTabWidgetFocusNavigator(int32 TabIndex)
	{
		TabWidgetMap.Remove(TabIndex);
	}

	void ClearAllTabWidgetFocusNavigator()
	{
		TabWidgetMap.Empty();
	}
} // namespace

/**
 * 从 JSON 字符串中解析并绑定用户中心模型数据
 *
 * @param JsonString 包含用户数据的 JSON 字符串
 */
void FPSOneUserCenterModel::BindData(const FString& JsonString)
{
	// 重置数据
	ResetData();

	// 解析 Json 字符串
	TSharedPtr<FJsonObject>		   JsonObject;
	TSharedRef<TJsonReader<TCHAR>> JsonReader = TJsonReaderFactory<TCHAR>::Create(JsonString);
	if (!FJsonSerializer::Deserialize(JsonReader, JsonObject) || !JsonObject.IsValid())
	{
		UE_LOG(LogPSOneUserCenter, Error, TEXT("Failed to parse JSON string: %s"), *JsonString);
		return;
	}

	// 解析 Json 对象中的各个字段
	JsonObject->TryGetStringField(TEXT("username"), UserName);
	JsonObject->TryGetStringField(TEXT("nickname"), NickName);
	JsonObject->TryGetStringField(TEXT("cellphone"), CellPhone);
	JsonObject->TryGetStringField(TEXT("showCellphone"), ShowCellPhone);
	JsonObject->TryGetStringField(TEXT("showEmail"), ShowEmail);
	JsonObject->TryGetStringField(TEXT("headImg"), HeadImg);
	JsonObject->TryGetStringField(TEXT("realName"), RealName);
	JsonObject->TryGetStringField(TEXT("idNumber"), IDNumber);
	JsonObject->TryGetStringField(TEXT("email"), Email);
	JsonObject->TryGetStringField(TEXT("userId"), UserID);
	JsonObject->TryGetStringField(TEXT("oneId"), OneID);
	JsonObject->TryGetStringField(TEXT("token"), Token);
	JsonObject->TryGetBoolField(TEXT("newUser"), NewUser);
	JsonObject->TryGetStringField(TEXT("userIp"), UserIP);
	JsonObject->TryGetBoolField(TEXT("havePwd"), HavePwd);
}

/**
 * 重置用户中心模型数据为默认空值
 */
void FPSOneUserCenterModel::ResetData()
{
	UserName.Empty();
	NickName.Empty();
	CellPhone.Empty();
	ShowCellPhone.Empty();
	ShowEmail.Empty();
	HeadImg.Empty();
	RealName.Empty();
	IDNumber.Empty();
	Email.Empty();
	UserID.Empty();
	OneID.Empty();
	Token.Empty();
	NewUser = false;
	UserIP.Empty();
	HavePwd = false;
}

/**
 * 设置内容盒子的可见性
 *
 * @param InVisibility 要设置的可见性状态
 */
void UPSOneUserCenter::SetContentBoxVisibility(ESlateVisibility InVisibility)
{
	if (ContentBox)
	{
		ContentBox->SetVisibility(InVisibility);
	}
}

/**
 * 绑定用户中心数据
 *
 * @param JsonString 包含用户数据的 JSON 字符串
 */
void UPSOneUserCenter::BindData(const FString& JsonString)
{
	Model.BindData(JsonString);
}

/**
 * 初始化用户中心 UI 控件和回调函数
 *
 * @return 初始化是否成功
 */
bool UPSOneUserCenter::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	FocusNavigator = MakeShareable(new FPSOneFocusNavigator());
	FocusNavigator->SetNavigationOrientation(ENavigationOrientation::Vertical);

	// 全球 Index 0 => 绑定账号
	if (!bIsMainLand)
	{
		int32							 Index = 0;
		TSharedPtr<FPSOneFocusNavigator> ChildFocusNavigator = MakeShareable(new FPSOneFocusNavigator());
		ChildFocusNavigator->SetNavigationOrientation(ENavigationOrientation::Vertical);
		UE_LOG(LogPSOneUserCenter, Log, TEXT("Create new FocusNavigator, Index: %d"), Index);
		SetTabWidgetFocusNavigator(Index, ChildFocusNavigator);
		BindAccountManagerWidget->OnSetupBindItemCallback = [this, ChildFocusNavigator](TArray<UWidget*> Children) {
			ChildFocusNavigator->FocusableWidgets.Empty();
			if (Children.Num() == 0)
			{
				return;
			}
			UE_LOG(LogPSOneUserCenter, Log, TEXT("OnSetupBindItemCallback, Children.Num: %d"), Children.Num());
			for (UWidget* Child : Children)
			{
				if (Child)
				{
					UUserWidget* UserWidget = Cast<UUserWidget>(Child);
					ChildFocusNavigator->AddFocusableWidget(UserWidget);
				}
			}
			ChildFocusNavigator->SetFocusLoop();
		};
	}

	{
		// 设备管理
		int32							 Index = 3;
		TSharedPtr<FPSOneFocusNavigator> ChildFocusNavigator = MakeShareable(new FPSOneFocusNavigator());
		ChildFocusNavigator->SetNavigationOrientation(ENavigationOrientation::Vertical);
		UE_LOG(LogPSOneUserCenter, Log, TEXT("Create new FocusNavigator, Index: %d"), Index);
		SetTabWidgetFocusNavigator(Index, ChildFocusNavigator);
		DeviceManagerWidget->OnSetupDeviceItemCallback = [this, ChildFocusNavigator](TArray<UWidget*> Children) {
			ChildFocusNavigator->FocusableWidgets.Empty();

			if (Children.Num() == 0)
			{
				return;
			}

			UE_LOG(LogPSOneUserCenter, Log, TEXT("OnSetupDeviceItemCallback, Children.Num: %d"), Children.Num());

			for (UWidget* Child : Children)
			{

				if (Child)
				{
					UUserWidget* UserWidget = Cast<UUserWidget>(Child);
					ChildFocusNavigator->AddFocusableWidget(UserWidget);
				}
			}

			ChildFocusNavigator->SetFocusLoop();
			
			if (ChildFocusNavigator->HasFocus())
			{
				ChildFocusNavigator->OnFocus();
			}
		};
	}

	// 设置 tab 标签的回调函数
	SetupTabCallbacks();
	return true;
}

/**
 * 设置标签页的点击回调函数
 * 每个标签页点击后会更新选中状态并切换对应内容
 */
void UPSOneUserCenter::SetupTabCallbacks()
{
	UE_LOG(LogPSOneUserCenter, Log, TEXT("SetupTabCallbacks"));
	// 定义标签页点击回调函数设置
	auto SetupTabCallback = [this](UPSOneUserCenterLeftCell* TabCell, int32 TabIndex, TSubclassOf<UUserWidget> WidgetClass) {
		if (!TabCell)
			return;

		// 设置点击回调，负责更新 UI 状态和切换到对应页面
		TabCell->OnClickCallback = [this, TabIndex, WidgetClass]() {
			// 更新标签页选中状态
			UpdateTabSelection(TabIndex);
			// 切换到对应页面
			SwitchToTabWidget(WidgetClass);
		};
	};
	FocusNavigator->AddFocusableWidget(BindAccountTabCell);
	FocusNavigator->AddFocusableWidget(AccountTabCell);
	FocusNavigator->AddFocusableWidget(RealNameTabCell);
	FocusNavigator->AddFocusableWidget(DevicesTabCell);
	FocusNavigator->AddFocusableWidget(LawTabCell);
	FocusNavigator->AddFocusableWidget(OtherTabCell);

	FocusNavigator->SetFocusLoop();

	// 大陆 -》账号管理，实名信息，设备管理，法律条款
	SetupTabCallback(BindAccountTabCell, 0, UPSOneUserCenterBindManager::StaticClass());
	SetupTabCallback(AccountTabCell, 1, UPSOneUserCenterAccountInfo::StaticClass());
	SetupTabCallback(RealNameTabCell, 2, UPSOneUserCenterIDInfo::StaticClass());
	SetupTabCallback(DevicesTabCell, 3, UPSOneUserCenterDeviceManager::StaticClass());
	SetupTabCallback(LawTabCell, 4, UPSOneUserCenterLegalTerms::StaticClass());
	SetupTabCallback(OtherTabCell, 5, UPSOneUserCenterOther::StaticClass());
}

/**
 * 更新标签页的选中状态
 *
 * @param TabIndex 当前选中的标签页索引
 */
void UPSOneUserCenter::UpdateTabSelection(int32 TabIndex)
{
	UE_LOG(LogPSOneUserCenter, Log, TEXT("UpdateTabSelection, TabIndex: %d"), TabIndex);

	// 根据索引设置各个标签页的选中状态
	if (BindAccountTabCell)
		BindAccountTabCell->SetSelected(TabIndex == 0);
	if (AccountTabCell)
		AccountTabCell->SetSelected(TabIndex == 1);
	if (RealNameTabCell)
		RealNameTabCell->SetSelected(TabIndex == 2);
	if (DevicesTabCell)
		DevicesTabCell->SetSelected(TabIndex == 3);
	if (LawTabCell)
		LawTabCell->SetSelected(TabIndex == 4);
	if (OtherTabCell)
		OtherTabCell->SetSelected(TabIndex == 5);

	// 触发标签页切换回调
	if (OnTabIndexChanged)
	{
		OnTabIndexChanged(TabIndex);
	}
}

/**
 * 切换到指定类型的标签页内容
 *
 * @param WidgetClass 要切换到的页面小部件类
 */
void UPSOneUserCenter::SwitchToTabWidget(TSubclassOf<UUserWidget> WidgetClass)
{
	if (!TabSwitcher)
		return;

	// 查找对应类型的小部件并激活
	const auto AllChildren = TabSwitcher->GetAllChildren();
	for (auto Child : AllChildren)
	{
		if (Child && Child->IsA(WidgetClass))
		{
			const int32 Index = TabSwitcher->GetChildIndex(Child);
			TabSwitcher->SetActiveWidgetIndex(Index);
			UpdateTabPageData();

			TSharedPtr<FPSOneFocusNavigator> ChildFocusNavigator = nullptr;
			const bool						 bIsExist = IsTabWidgetFocusNavigatorExist(Index);
			if (bIsExist)
			{
				UE_LOG(LogPSOneUserCenter, Log, TEXT("SwitchToTabWidget, Index: %d, WidgetClass: %s, ****** Get exist FocusNavigator"), Index, *WidgetClass->GetName());
				ChildFocusNavigator = GetTabWidgetFocusNavigator(Index);
			}
			else
			{
				UE_LOG(LogPSOneUserCenter, Log, TEXT("SwitchToTabWidget, Index: %d, WidgetClass: %s, ****** Create new FocusNavigator"), Index, *WidgetClass->GetName());
				ChildFocusNavigator = MakeShareable(new FPSOneFocusNavigator());
				ChildFocusNavigator->SetNavigationOrientation(ENavigationOrientation::Vertical);
				UE_LOG(LogPSOneUserCenter, Log, TEXT("Create new FocusNavigator, Index: %d"), Index);
				SetTabWidgetFocusNavigator(Index, ChildFocusNavigator);
			}

			FocusNavigator->SetChildren(ChildFocusNavigator.Get());

			if (WidgetClass == UPSOneUserCenterAccountInfo::StaticClass())
			{
				// 为账号信息页面添加可聚焦控件
				if (AccountInfoWidget && !bIsExist)
				{
					// 说明是第一次加载，需要添加控件
					UPSOneUserCenterAccountInfo* AccountInfo = Cast<UPSOneUserCenterAccountInfo>(Child);
					ChildFocusNavigator->AddFocusableWidget(AccountInfo->AvatarCell);
					ChildFocusNavigator->AddFocusableWidget(AccountInfo->NickCell);
					ChildFocusNavigator->AddFocusableWidget(AccountInfo->MobileCell);
					ChildFocusNavigator->AddFocusableWidget(AccountInfo->EmailCell);
					ChildFocusNavigator->AddFocusableWidget(AccountInfo->CancellationCell);
					ChildFocusNavigator->AddFocusableWidget(AccountInfo->ChangePasswordCell);
					ChildFocusNavigator->SetFocusLoop();
					
				}
			}
			else if (WidgetClass == UPSOneUserCenterDeviceManager::StaticClass())
			{
				// 为设备管理页面添加可聚焦控件
				if (DeviceManagerWidget)
				{
					// 等等 Child 加载完成，再添加控件
				}
			}
			else if (WidgetClass == UPSOneUserCenterLegalTerms::StaticClass())
			{
				// 为法律条款页面添加可聚焦控件
				if (LegalTermsWidget && !bIsExist)
				{
					UPSOneUserCenterLegalTerms* LegalTerms = Cast<UPSOneUserCenterLegalTerms>(Child);
					ChildFocusNavigator->AddFocusableWidget(LegalTerms->LegalCell);
					ChildFocusNavigator->SetFocusLoop();
				}
			}
			else if (WidgetClass == UPSOneUserCenterBindManager::StaticClass())
			{
				// 为绑定管理页面添加可聚焦控件
				if (BindAccountManagerWidget && !bIsExist)
				{
				}
			}
			else if (WidgetClass == UPSOneUserCenterOther::StaticClass())
			{
				// 为其他页面添加可聚焦控件
				if (OtherWidget && !bIsExist)
				{
					UPSOneUserCenterOther* Other = Cast<UPSOneUserCenterOther>(Child);
					ChildFocusNavigator->AddFocusableWidget(Other->DeleteAccountCell);
					ChildFocusNavigator->SetFocusLoop();
				}
			}

			break;
		}
	}
}

void UPSOneUserCenter::SetIsMainLand(bool bInIsMainLand)
{
	bIsMainLand = bInIsMainLand;
	DeviceManagerWidget->bIsMainLand = bInIsMainLand;
}

/**
 * 预构造函数，用于设置 UI 的初始状态
 * 在 UI 被构造前调用，用于配置 UI 元素
 */
void UPSOneUserCenter::NativePreConstruct()
{
	Super::NativePreConstruct();

	UPSUserWidgetSettings* Settings = UPSUserWidgetSettings::Get();
	ScaleBox->SetUserSpecifiedScale(Settings->GetUIScaleRatio());

	SetupBackgroundBlur(Settings);
	SetupButtonIcons(Settings);

	if (bIsMainLand)
	{
		UE_LOG(LogPSOneUserCenter, Display, TEXT("*** Set IsMainLand = true ***"));
		NickTextBlock->SetVisibility(ESlateVisibility::Visible);
		NickText->SetVisibility(ESlateVisibility::Visible);
		UIDTextBlock->SetText(LOCTEXT("UserIdentity", "用户编号"));

		BindAccountTabCell->SetVisibility(ESlateVisibility::Collapsed);
		AccountTabCell->SetVisibility(ESlateVisibility::Visible);
		RealNameTabCell->SetVisibility(ESlateVisibility::Visible);
		OtherTabCell->SetVisibility(ESlateVisibility::Collapsed);

		// 默认选中第一个标签页
		if (TabSwitcher)
		{
			UpdateTabSelection(1);
			SwitchToTabWidget(UPSOneUserCenterAccountInfo::StaticClass());
		}
	}
	else
	{
		UE_LOG(LogPSOneUserCenter, Display, TEXT("*** Set IsMainLand = false ***"));
		// 隐藏
		NickTextBlock->SetVisibility(ESlateVisibility::Collapsed);
		NickText->SetVisibility(ESlateVisibility::Collapsed);
		// 用户编号
		UIDTextBlock->SetText(LOCTEXT("UID", "UID"));

		BindAccountTabCell->SetVisibility(ESlateVisibility::Visible);
		AccountTabCell->SetVisibility(ESlateVisibility::Collapsed);
		RealNameTabCell->SetVisibility(ESlateVisibility::Collapsed);
		OtherTabCell->SetVisibility(ESlateVisibility::Visible);

		if (TabSwitcher)
		{
			UpdateTabSelection(0);
			SwitchToTabWidget(UPSOneUserCenterBindManager::StaticClass());
		}
	}
}

void UPSOneUserCenter::NativeDestruct()
{
	Super::NativeDestruct();
	ClearAllTabWidgetFocusNavigator();
}

/**
 * 设置背景模糊效果
 *
 * @param Settings 用户小部件设置
 */
void UPSOneUserCenter::SetupBackgroundBlur(UPSUserWidgetSettings* Settings)
{
	if (!BackgroundBlur || !Settings)
		return;

	// 根据设置决定是否启用背景模糊效果
	if (Settings->bEnableBlurBackground)
	{
		BackgroundBlur->SetVisibility(ESlateVisibility::Visible);
		BackgroundBlur->SetBlurStrength(Settings->BlurBackgroundStrength);
	}
	else
	{
		BackgroundBlur->SetVisibility(ESlateVisibility::Collapsed);
	}
}

/**
 * 设置按钮图标
 * 根据配置决定按钮使用圆形还是叉形图标
 *
 * @param Settings 用户小部件设置
 */
void UPSOneUserCenter::SetupButtonIcons(UPSUserWidgetSettings* Settings)
{
	if (!EnterIcon || !BackspaceIcon || !Settings)
		return;

	FSlateBrush EnterBrush, BackspaceBrush;

	// 根据设置配置进入按钮和返回按钮的图标
	if (Settings->bEnterButtonAssignCircle)
	{
		UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(EnterBrush, Settings->PSCircleTexture);
		UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(BackspaceBrush, Settings->PSCrossTexture);
	}
	else
	{
		UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(EnterBrush, Settings->PSCrossTexture);
		UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(BackspaceBrush, Settings->PSCircleTexture);
	}

	EnterIcon->SetBrush(EnterBrush);
	BackspaceIcon->SetBrush(BackspaceBrush);
}

/**
 * 小部件构造后调用
 * 进行初始化操作并加载数据
 */
void UPSOneUserCenter::NativeConstruct()
{
	Super::NativeConstruct();

	// 默认激活第一个标签页
	if (TabSwitcher)
	{
		if (bIsMainLand)
		{
			UpdateTabSelection(1);
			SwitchToTabWidget(UPSOneUserCenterAccountInfo::StaticClass());
		}
		else
		{
			UpdateTabSelection(0);
			SwitchToTabWidget(UPSOneUserCenterBindManager::StaticClass());
		}
	}
	FocusNavigator->OnFocus();
	// 刷新数据显示
	RefreshData();
}

/**
 * 刷新设备列表数据
 * 解析设备 JSON 数据并更新 UI
 */
void UPSOneUserCenter::RefreshDeviceList()
{
	if (!DeviceManagerWidget)
	{
		UE_LOG(LogPSOneUserCenter, Error, TEXT("DeviceManagerWidget is nullptr"));
		return;
	}

	// 解析设备列表 JSON 数据
	TArray<FOneOnlineDevice> Devices;

	if (DevicesJsonString.IsEmpty())
	{
		// 如果 JSON 字符串为空，直接更新空设备列表
		DeviceManagerWidget->SetOnlineDevices(Devices);
		return;
	}

	// 解析 JSON
	TSharedPtr<FJsonObject>	  JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(DevicesJsonString);

	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		UE_LOG(LogPSOneUserCenter, Error, TEXT("Parse DevicesJsonString failed: %s"), *DevicesJsonString);
		DeviceManagerWidget->SetOnlineDevices(Devices);
		return;
	}

	// 获取设备数组
	const TArray<TSharedPtr<FJsonValue>>* DevicesJsonArray;
	if (!JsonObject->TryGetArrayField(TEXT("devices"), DevicesJsonArray))
	{
		UE_LOG(LogPSOneUserCenter, Error, TEXT("Failed to get DevicesJsonArray"));
		DeviceManagerWidget->SetOnlineDevices(Devices);
		return;
	}

	// 遍历解析每个设备信息
	bool bHasCurrentDevice = false;
	for (const auto& Device : *DevicesJsonArray)
	{
		if (!Device.IsValid())
			continue;

		TSharedPtr<FJsonObject> DeviceJsonObject = Device->AsObject();
		if (!DeviceJsonObject.IsValid())
			continue;

		FOneOnlineDevice OneOnlineDevice;

		// 解析设备信息
		DeviceJsonObject->TryGetNumberField(TEXT("osType"), OneOnlineDevice.OSType);
		DeviceJsonObject->TryGetStringField(TEXT("deviceName"), OneOnlineDevice.DeviceName);

		// 获取设备 ID，优先使用 deviceId，如果为空则尝试使用 ndid
		if (!DeviceJsonObject->TryGetStringField(TEXT("deviceId"), OneOnlineDevice.DeviceID) || OneOnlineDevice.DeviceID.IsEmpty())
		{
			if (!DeviceJsonObject->TryGetStringField(TEXT("ndid"), OneOnlineDevice.DeviceID) || OneOnlineDevice.DeviceID.IsEmpty())
			{
				UE_LOG(LogPSOneUserCenter, Error, TEXT("DeviceID is empty, deviceName: %s,  not add to devices list"), *OneOnlineDevice.DeviceName);
				continue;
			}
		}

		// 检查是否为当前设备
		DeviceJsonObject->TryGetBoolField(TEXT("isSelf"), OneOnlineDevice.bIsCurrentDevice);
		if (OneOnlineDevice.bIsCurrentDevice)
		{
			bHasCurrentDevice = true;
		}

		Devices.Add(OneOnlineDevice);
	}

	// 如果没有标记当前设备，则将第一个设备设为当前设备
	if (!bHasCurrentDevice && Devices.Num() > 0)
	{
		UE_LOG(LogPSOneUserCenter, Warning, TEXT("No current device found, set first device as current"));
		Devices[0].bIsCurrentDevice = true;
	}

	// 更新设备列表显示
	DeviceManagerWidget->SetOnlineDevices(Devices);
}

/**
 * 刷新用户中心所有数据
 * 更新头像、显示名称和标签页数据
 */
void UPSOneUserCenter::RefreshData()
{
	// 过滤掉空用户 ID
	if (Model.UserID.IsEmpty())
	{
		return;
	}

	UpdateAvatar();
	UpdateDisplayName();
	UpdateTabPageData();
}

/**
 * 更新用户头像
 * 如果有有效的头像 URL，设置到头像组件
 */
void UPSOneUserCenter::UpdateAvatar()
{
	if (AvatarImage && !Model.HeadImg.IsEmpty() && Model.HeadImg.StartsWith(TEXT("http")))
	{
		AvatarImage->SetImageURL(Model.HeadImg);
	}
}

/**
 * 更新用户显示名称
 * 优先使用手机号，其次使用邮箱，最后使用用户 ID
 */
void UPSOneUserCenter::UpdateDisplayName()
{
	if (!MainIdText || !NickText || !UIDText)
		return;

	FString DisplayName;

	if (bIsMainLand)
	{
		// 确定显示的主标识，按优先级：手机号 > 邮箱 > 用户 ID
		DisplayName = Model.ShowCellPhone;
		if (DisplayName.IsEmpty())
		{
			DisplayName = Model.ShowEmail;
			if (DisplayName.IsEmpty())
			{
				DisplayName = Model.UserID;
			}
		}
	}
	else
	{
		// 海外
		DisplayName = Model.UserName;
		if (DisplayName.IsEmpty())
		{
			DisplayName = Model.UserID;
		}
	}

	// 设置用户信息文本
	MainIdText->SetText(FText::FromString(DisplayName));
	NickText->SetText(FText::FromString(Model.NickName));
	UIDText->SetText(FText::FromString(Model.UserID));
}

/**
 * 刷新设备数据
 *
 * @param JsonString 包含设备信息的 JSON 字符串
 */
void UPSOneUserCenter::RefreshDeviceData(const FString& JsonString)
{
	DevicesJsonString = JsonString;
	RefreshDeviceList();
}

/**
 * 更新绑定账号信息
 * 将传入的账号绑定状态映射转换为绑定项数组，并更新到绑定账号管理组件
 *
 * @param Accounts 账号类型与绑定状态的映射表
 */
void UPSOneUserCenter::UpdateBoundAccounts(const TMap<int32, bool>& Accounts)
{
	// 确保绑定账号管理组件有效
	if (!BindAccountManagerWidget)
	{
		return;
	}

	// 将映射表转换为绑定项数组
	TArray<FPSOneBindItem> BindItems;
	BindItems.Reserve(Accounts.Num()); // 预分配内存以提高性能

	for (const auto& Pair : Accounts)
	{
		BindItems.Add(FPSOneBindItem{ Pair.Key, Pair.Value });
	}
	// 更新绑定账号管理组件
	BindAccountManagerWidget->SetBindItems(BindItems);

	//
}

/**
 * 更新当前标签页内容数据
 * 根据当前活动标签页类型更新相应数据
 */
void UPSOneUserCenter::UpdateTabPageData()
{
	if (!TabSwitcher)
		return;

	// 获取当前活动的标签页小部件
	UWidget* Widget = TabSwitcher->GetActiveWidget();
	if (!Widget)
	{
		return;
	}

	// 根据小部件类型更新不同页面数据
	if (Widget->IsA(UPSOneUserCenterAccountInfo::StaticClass()) && AccountInfoWidget)
	{
		// 账户信息页面
		FPSOneAccountInfoStruct AccountInfo;
		AccountInfo.ImageURL = Model.HeadImg;
		AccountInfo.Nick = FText::FromString(Model.NickName);
		AccountInfo.Mobile = FText::FromString(Model.ShowCellPhone);
		AccountInfo.Email = FText::FromString(Model.ShowEmail);
		AccountInfo.bHasPwd = Model.HavePwd;
		AccountInfoWidget->BindData(AccountInfo);
	}
	else if (Widget->IsA(UPSOneUserCenterIDInfo::StaticClass()) && IDInfoWidget)
	{
		// 实名认证信息页面
		FPSOneIDInfoStruct IDInfo;
		IDInfo.Name = FText::FromString(Model.RealName);
		IDInfo.ID = FText::FromString(Model.IDNumber);
		IDInfoWidget->BindData(IDInfo);
	}
	else if (Widget->IsA(UPSOneUserCenterDeviceManager::StaticClass()))
	{
		// 设备管理页面
		RefreshDeviceList();
	}
	// 法律条款页面不需要特别处理
}

/**
 * 接收焦点时的处理
 * 设置适当的标签页和内容焦点
 *
 * @param InGeometry 几何信息
 * @param InFocusEvent 焦点事件
 * @return 焦点处理回复
 */
FReply UPSOneUserCenter::NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent)
{
	// 调用基类实现以处理默认行为
	return Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
}

/**
 * 处理按键事件
 *
 * @param InGeometry 几何信息
 * @param InKeyEvent 按键事件
 * @return 按键处理回复
 */
FReply UPSOneUserCenter::NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent)
{
	// 先让 FocusNavigator 处理导航键
	UE_LOG(LogPSOneUserCenter, Log, TEXT("UPSOneUserCenter NativeOnKeyDown !!!"));
	if (FocusNavigator->HandleKeyEvent(InKeyEvent))
	{
		return FReply::Handled();
	}

	// 处理返回键
	if (UPSUserWidgetSettings::IsClickedBackKey(InKeyEvent))
	{
		if (OnClickCloseButton)
		{
			OnClickCloseButton();
		}
		return FReply::Handled();
	}

	// 处理左肩键切换账户
	if (InKeyEvent.GetKey() == EKeys::Gamepad_LeftShoulder)
	{
		if (OnSwitchAccount)
		{
			OnSwitchAccount();
		}
		return FReply::Handled();
	}

	return FReply::Handled();
}

#undef LOCTEXT_NAMESPACE
