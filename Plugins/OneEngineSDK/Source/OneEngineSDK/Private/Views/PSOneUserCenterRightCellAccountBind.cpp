﻿#include "PSOneUserCenterRightCellAccountBind.h"

#include "PSUserWidgetSettings.h"
#include "Components/Image.h"


void UPSOneUserCenterRightCellAccountBind::NativePreConstruct()
{
	Super::NativePreConstruct();

	if (Icon)
	{
		FSlateBrush Brush;
		UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Icon);
		IconImage->SetBrush(Brush);
	}

	
	
	UpdateBindIcon(bIsBind);
}


void UPSOneUserCenterRightCellAccountBind::UpdateBindIcon(bool bBind)
{
	bIsBind = bBind;
	if (bBind)
	{
		if (BindIcon) 
		{
			// 如果是绑定状态，显示解绑图标
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, UnBindIcon);
			BindImage->SetBrush(Brush);
		}
	} else
	{
		if (UnBindIcon)
		{
			// 如果是解绑状态，显示绑定图标
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, BindIcon);
			BindImage->SetBrush(Brush);
		}
	}
}
