﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "PSOneUserAgreementPrompt.h"

#include "PSOneButtonBase.h"
#include "PSUserWidgetSettings.h"
#include "Components/BackgroundBlur.h"
#include "Components/HorizontalBox.h"
#include "Components/Image.h"
#include "Components/SizeBox.h"
#include "Components/WidgetSwitcher.h"
#include "PSOneFocusNavigator.h"
#include "Components/ScaleBox.h"
#include "Runtime/Launch/Resources/Version.h"

void UPSOneUserAgreementPrompt::SwitchToPreparePage()
{
	const int Count = Switcher->GetNumWidgets();
	if (Count == 0)
	{
		UE_LOG(LogTemp, Display, TEXT("SwitchToPreparePage Switcher is empty"));
		return;
		;
	}
	StartIndex = Count - 1;
	UE_LOG(LogTemp, Display, TEXT("SwitchToPage %d"), StartIndex);
	Switcher->SetActiveWidgetIndex(StartIndex);
	if (StartIndex > 0)
	{
		FocusNavigator->OnFocus();
	}
}

void UPSOneUserAgreementPrompt::NativePreConstruct()
{
	Super::NativePreConstruct();

	// 获取全局设置
	UPSUserWidgetSettings* Settings = UPSUserWidgetSettings::Get();
	ScaleBox->SetUserSpecifiedScale(Settings->GetUIScaleRatio());

	// 是否显示背景图片
	if (bShowBgImage)
	{
		BgImageSizeBox->SetVisibility(ESlateVisibility::Visible);
	}
	else
	{
		BgImageSizeBox->SetVisibility(ESlateVisibility::Collapsed);
	}
	
	if (Settings->bEnableBlurBackground)
	{
		// 背景模糊
		BackgroundBlur->SetVisibility(ESlateVisibility::Visible);
		BackgroundBlur->SetBlurStrength(Settings->BlurBackgroundStrength);
	}
	else
	{
		BackgroundBlur->SetVisibility(ESlateVisibility::Collapsed);
	}

	if (bIsMainland)
	{
		// 如果是大陆版，显示儿童保护提示
		ChildProtectionBox->SetVisibility(ESlateVisibility::Visible);
	}
	else
	{
		ChildProtectionBox->SetVisibility(ESlateVisibility::Collapsed);
	}

	// PS4 按钮
	if (Settings->bEnterButtonAssignCircle)
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			AgreeImage->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			DisagreeImage->SetBrush(Brush);
		}
	}
	else
	{
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
			AgreeImage->SetBrush(Brush);
		}
		{
			FSlateBrush Brush;
			UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
			DisagreeImage->SetBrush(Brush);
		}
	}

	{
		// 绘制背景图片
		FSlateBrush Brush;
		UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->AgreementPromptBackgroundTexture);
		BgImage->SetBrush(Brush);
	}

	{
		// 绘制 PS L1 按钮
		FSlateBrush Brush;
		UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSL1Texture);
		Left1Image->SetBrush(Brush);
	}
	{
		// 绘制 PS R1 按钮
		FSlateBrush Brush;
		UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSR1Texture);
		Right1Image->SetBrush(Brush);
	}
	{
		// 绘制 PS Triangle 按钮
		FSlateBrush Brush;
		UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSTriangleTexture);
		TriangleImage->SetBrush(Brush);
	}

	Switcher->SetActiveWidgetIndex(StartIndex);

	if (StartIndex > 0)
	{
		// BindAccountButton->SetKeyboardFocus();
		FocusNavigator->OnFocus();
	}

	// 登录方式
	if (LoginOption > 0)
	{
		// 只显示指定的登录方式
		BindAccountButton->SetVisibility((LoginOption & 1) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		CreateAccountButton->SetVisibility((LoginOption & (1 << 1)) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		if (bIsMainland)
		{
			LoginWithPwrdButton->SetVisibility((LoginOption & (1 << 2)) ? ESlateVisibility::Visible : ESlateVisibility::Collapsed);
		}
		else
		{
			// 海外版，没有密码登录
			LoginWithPwrdButton->SetVisibility(ESlateVisibility::Collapsed);
		}
	}
	else
	{
		// 显示所有登录方式
		BindAccountButton->SetVisibility(ESlateVisibility::Visible);
		CreateAccountButton->SetVisibility(ESlateVisibility::Visible);
		if (bIsMainland)
		{
			// 大陆版，显示密码登录
			LoginWithPwrdButton->SetVisibility(ESlateVisibility::Visible);
		}
		else
		{
			// 海外版，没有密码登录
			LoginWithPwrdButton->SetVisibility(ESlateVisibility::Collapsed);
		}
	}

	// Todo: 处理导航
	TArray<UPSOneButtonBase*> VisibleButtons;
	if (BindAccountButton->GetVisibility() == ESlateVisibility::Visible)
	{
		VisibleButtons.Add(BindAccountButton);
	}
	if (CreateAccountButton->GetVisibility() == ESlateVisibility::Visible)
	{
		VisibleButtons.Add(CreateAccountButton);
	}
	if (LoginWithPwrdButton->GetVisibility() == ESlateVisibility::Visible)
	{
		VisibleButtons.Add(LoginWithPwrdButton);
	}

	int32 NumVisibleButtons = VisibleButtons.Num();
	if (NumVisibleButtons >= 2)
	{
		UPSOneButtonBase* FirstButton = VisibleButtons[0];
		UPSOneButtonBase* LastButton = VisibleButtons[NumVisibleButtons - 1];
		FirstButton->SetNavigationRuleExplicit(EUINavigation::Up, LastButton);
		LastButton->SetNavigationRuleExplicit(EUINavigation::Down, FirstButton);
	}
}

bool UPSOneUserAgreementPrompt::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	FocusNavigator = MakeShareable(new FPSOneFocusNavigator());
	FocusNavigator->SetNavigationOrientation(ENavigationOrientation::Vertical);
	
	BindAccountButton->OnClickCallback = [this]() {
		if (OnBindAccountCallback)
		{
			OnBindAccountCallback();
		}
	};

	CreateAccountButton->OnClickCallback = [this]() {
		if (OnCreateAccountCallback)
		{
			OnCreateAccountCallback();
		}
	};

	LoginWithPwrdButton->OnClickCallback = [this]() {
		if (OnLoginWithPwrdCallback)
		{
			OnLoginWithPwrdCallback();
		}
	};

	FocusNavigator->AddFocusableWidget(BindAccountButton);
	FocusNavigator->AddFocusableWidget(CreateAccountButton);
	FocusNavigator->AddFocusableWidget(LoginWithPwrdButton);
	FocusNavigator->SetFocusLoop();
	return true;
}

FReply UPSOneUserAgreementPrompt::NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent)
{
	FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
#if (ENGINE_MAJOR_VERSION == 5 && ENGINE_MINOR_VERSION >= 2)
	int32  Index = Switcher->GetActiveWidgetIndex();
#else
	int32  Index = Switcher->ActiveWidgetIndex;
#endif
	if (Index == 1)
	{
		FocusNavigator->OnFocus();
	}
	return Reply;
}

void UPSOneUserAgreementPrompt::NativeOnFocusLost(const FFocusEvent& InFocusEvent)
{
	Super::NativeOnFocusLost(InFocusEvent);
}

FReply UPSOneUserAgreementPrompt::NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent)
{
#if (ENGINE_MAJOR_VERSION == 5 && ENGINE_MINOR_VERSION >= 2)
	int32  Index = Switcher->GetActiveWidgetIndex();
#else
	int32  Index = Switcher->ActiveWidgetIndex;
#endif
	if (Index == 0)
	{
		// 第一页，L1 查看用户协议，R1 查看个人信息保护政策，三角形 查看儿童个人信息保护指引，
		if (InKeyEvent.GetKey() == EKeys::Gamepad_LeftShoulder)
		{
			if (OnOpenUserAgreementCallback)
			{
				OnOpenUserAgreementCallback();
			}
		}

		else if (InKeyEvent.GetKey() == EKeys::Gamepad_RightShoulder)
		{
			if (OnOpenUserPrivacyCallback)
			{
				OnOpenUserPrivacyCallback();
			}
		}

		else if (InKeyEvent.GetKey() == EKeys::Gamepad_FaceButton_Top)
		{
			if (!bIsMainland)
			{
				return FReply::Handled();
			}
			if (OnOpenChildPrivacyCallback)
			{
				OnOpenChildPrivacyCallback();
			}
		}

		else if (UPSUserWidgetSettings::IsClickedEnterKey(InKeyEvent))
		{
			if (OnAgreeCallback)
			{
				OnAgreeCallback();
			}
		}
		else if (UPSUserWidgetSettings::IsClickedBackKey(InKeyEvent))
		{
			if (OnCloseCallback)
			{
				OnCloseCallback();
			};
		}
	}
	else
	{
		if (FocusNavigator->HandleKeyEvent(InKeyEvent))
		{
			return FReply::Handled();
		}

		if (UPSUserWidgetSettings::IsClickedBackKey(InKeyEvent))
		{
			if (OnCloseCallback)
			{
				OnCloseCallback();
			}
		}
	}
	return FReply::Handled();
}

UPSOneButtonBase* UPSOneUserAgreementPrompt::GetBestFocusButton()
{
	if (LoginOption == 0 || (LoginOption & 1))
	{
		return BindAccountButton;
	}
	if (LoginOption & 2)
	{
		return CreateAccountButton;
	}
	if (LoginOption & 4)
	{
		return LoginWithPwrdButton;
	}
	return BindAccountButton;
}
