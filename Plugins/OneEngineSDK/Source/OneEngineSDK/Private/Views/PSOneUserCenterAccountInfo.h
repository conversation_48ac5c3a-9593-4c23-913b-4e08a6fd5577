// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PSOneFocusUserWidget.h"
#include "PSOneUserCenterAccountInfo.generated.h"

// 点击事件类型枚举

enum class EPSOneUserCenterAccountInfoClickType: uint8
{
	Avatar,
	Nick,
	Mobile,
	Email,
	Cancellation,
	ChangePassword,
};

USTRUCT()
struct FPSOneAccountInfoStruct
{
	GENERATED_BODY()

	UPROPERTY()
	FString ImageURL;

	UPROPERTY()
	FText Nick;

	UPROPERTY()
	FText Mobile;

	UPROPERTY()
	FText Email;

	UPROPERTY()
	bool bHasPwd{false};
};


UCLASS()
class ONEENGINESDK_API UPSOneUserCenterAccountInfo : public UPSOneFocusUserWidget, public IPSOneSaveFocusWidgetInterface
{
	GENERATED_BODY()

public:
	// AvatarCell
	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterRightCellAvatar* AvatarCell;
	// NickCell
	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterRightCellSubtitle* NickCell;
	// MobileCell
	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterRightCellSubtitle* MobileCell;
	// EmailCell
	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterRightCellSubtitle* EmailCell;
	// CancellationCell
	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterRightCellSubtitle* CancellationCell;
	//  ChangePasswordCell
	UPROPERTY(meta=(BindWidget))
	class UPSOneUserCenterRightCellSubtitle* ChangePasswordCell;

	void BindData(const FPSOneAccountInfoStruct& InData);

	TFunction<void(EPSOneUserCenterAccountInfoClickType)> OnClickCallback;

	virtual void SaveFocusWidget(UWidget* Widget) override;

	virtual bool Initialize() override;
	
protected:
	FPSOneAccountInfoStruct AccountInfo;
	virtual void NativePreConstruct() override;
	void UpdateCellVisibilityStatus();
	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;
};
