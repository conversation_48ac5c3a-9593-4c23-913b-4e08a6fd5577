﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "PSOneUserCenterRightCellSubtitle_NF.generated.h"

/**
 * 
 */
UCLASS()
class ONEENGINESDK_API UPSOneUserCenterRightCellSubtitle_NF : public UUserWidget
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere)
	FText Title;

	UPROPERTY(meta=(BindWidget))
	class UTextBlock* TextBlock;

	UPROPERTY(meta=(BindWidget))
	class UBorder* BgBorder;
	
	UPROPERTY(EditAnywhere)
	class UTexture2D* NormalTexture;
	
	UPROPERTY(EditAnywhere)
	FText SubTitle;

	UPROPERTY(meta=(BindWidget))
	class UTextBlock* SubTextBlock;

	UPROPERTY(EditAnywhere)
	bool HiddenArrow{false};

	UPROPERTY(meta=(BindWidget))
	class UImage* ArrowImage;
	
	void SetSubTitle(const FText& InSubTitle);

protected:
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
};
