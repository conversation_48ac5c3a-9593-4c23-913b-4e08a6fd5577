﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "PSOneUserCenterRightCellAvatar.h"
#include "PSOneFocusUserWidget.h"
#include "PSOneAvatarImage.h"

void UPSOneUserCenterRightCellAvatar::UpdateAvatarImage(UTexture2D* InTexture)
{
	if (ImageIcon)
	{
		if (ImageIcon != InTexture)
		{
			ImageIcon = InTexture;
		}
		AvatarImage->UpdateImage(InTexture);
	}
}

void UPSOneUserCenterRightCellAvatar::SetAvatarImageURL(const FString& URL)
{
	AvatarImage->SetImageURL(URL);
}

void UPSOneUserCenterRightCellAvatar::NativePreConstruct()
{
	Super::NativePreConstruct();
	if (ImageIcon)
	{
		UpdateAvatarImage(ImageIcon);
	}
}

void UPSOneUserCenterRightCellAvatar::OnFocusSwitched(bool Focus)
{
	Super::OnFocusSwitched(Focus);
	if (!Focus)
	{
		return;
	}
	UObject* OuterObject = GetOuter();
	while (OuterObject)
	{
		if (OuterObject->IsA(UUserWidget::StaticClass()))
		{
			IPSOneSaveFocusWidgetInterface* SaveFocusWidget = Cast<IPSOneSaveFocusWidgetInterface>(OuterObject);
			if (SaveFocusWidget)
			{
				SaveFocusWidget->SaveFocusWidget(this);
				break;
			}
		}

		OuterObject = OuterObject->GetOuter();
	}
}
