﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "PSOneDelTips.h"
#include "PSOneFocusNavigator.h"
#include "PSUserWidgetSettings.h"
#include "Components/Image.h"
#include "Components/TextBlock.h"
#include "PSOneConfirmButton.h"
#include "PSOneButtonBase.h"
#include "Components/BackgroundBlur.h"
#include "Components/ScaleBox.h"

bool UPSOneDelTips::Initialize()
{
    if (!Super::Initialize())
    {
        return false;
    }
    // 添加焦点导航

    FocusNavigator = MakeShareable(new FPSOneFocusNavigator());
    FocusNavigator->AddFocusableWidget(RestoreButton);
    FocusNavigator->AddFocusableWidget(DeleteButton);
	FocusNavigator->SetFocusLoop();

    RestoreButton->OnClickCallback = [this]()
    {
        if (OnRestore)
        {
            OnRestore();
        }
    };

    DeleteButton->OnClickCallback = [this]()
    {
        if (OnDelete)
        {
            OnDelete();
        }
    };

    return true;
}

void UPSOneDelTips::SetTitleText(const FString &Text)
{
    TitleTextBlock->SetText(FText::FromString(Text));
}

void UPSOneDelTips::SetDescText(const FString &Text)
{
    DescTextBlock->SetText(FText::FromString(Text));
}

void UPSOneDelTips::NativePreConstruct()
{
    Super::NativePreConstruct();
    UPSUserWidgetSettings *Settings = UPSUserWidgetSettings::Get();
	ScaleBox->SetUserSpecifiedScale(Settings->GetUIScaleRatio());

	if (Settings->bEnableBlurBackground)
	{
		BackgroundBlur->SetVisibility(ESlateVisibility::Visible);
		BackgroundBlur->SetBlurStrength(Settings->BlurBackgroundStrength);
	}
	else
	{
		BackgroundBlur->SetVisibility(ESlateVisibility::Collapsed);
	}
	
    if (Settings->bEnterButtonAssignCircle)
    {
        {
            FSlateBrush Brush;
            UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
            EnterIcon->SetBrush(Brush);
        }
        {
            FSlateBrush Brush;
            UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
            BackspaceIcon->SetBrush(Brush);
        }
    }
    else
    {
        {
            FSlateBrush Brush;
            UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCrossTexture);
            EnterIcon->SetBrush(Brush);
        }
        {
            FSlateBrush Brush;
            UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(Brush, Settings->PSCircleTexture);
            BackspaceIcon->SetBrush(Brush);
        }
    }
}

FReply UPSOneDelTips::NativeOnFocusReceived(const FGeometry &InGeometry, const FFocusEvent &InFocusEvent)
{
    FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
    FocusNavigator->OnFocus();
    return Reply;
}

FReply UPSOneDelTips::NativeOnKeyDown(const FGeometry &InGeometry, const FKeyEvent &InKeyEvent)
{
    if (FocusNavigator->HandleKeyEvent(InKeyEvent))
    {
        return FReply::Handled();
    }
    else if (UPSUserWidgetSettings::IsClickedBackKey(InKeyEvent))
    {
        if (OnClickCloseButton)
        {
            OnClickCloseButton();
        }
    }
    return FReply::Handled();
}
