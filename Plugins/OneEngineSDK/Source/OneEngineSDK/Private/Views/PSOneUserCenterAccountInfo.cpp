﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "PSOneUserCenterAccountInfo.h"

#include "PSOneUserCenterRightCellAvatar.h"
#include "PSOneUserCenterRightCellSubtitle.h"

#define LOCTEXT_NAMESPACE "PSOneUserCenterAccountInfo"

void UPSOneUserCenterAccountInfo::BindData(const FPSOneAccountInfoStruct& InData)
{
	AccountInfo = InData;
	AvatarCell->SetAvatarImageURL(InData.ImageURL);
	NickCell->SetSubTitle(InData.Nick);
	MobileCell->SetSubTitle(InData.Mobile);
	UpdateCellVisibilityStatus();
}

void UPSOneUserCenterAccountInfo::SaveFocusWidget(UWidget* Widget)
{
	
}

bool UPSOneUserCenterAccountInfo::Initialize()
{
	if (!Super::Initialize())
	{
		return false;
	}
	AvatarCell->OnClickCallback = [this]()
	{
		if (OnClickCallback)
		{
			OnClickCallback(EPSOneUserCenterAccountInfoClickType::Avatar);
		}
	};
	NickCell->OnClickCallback = [this]()
	{
		if (OnClickCallback)
		{
			OnClickCallback(EPSOneUserCenterAccountInfoClickType::Nick);
		}
	};
	MobileCell->OnClickCallback = [this]()
	{
		if (OnClickCallback)
		{
			OnClickCallback(EPSOneUserCenterAccountInfoClickType::Mobile);
		}
	};
	EmailCell->OnClickCallback = [this]()
	{
		if (OnClickCallback)
		{
			OnClickCallback(EPSOneUserCenterAccountInfoClickType::Email);
		}
	};
	CancellationCell->OnClickCallback = [this]()
	{
		if (OnClickCallback)
		{
			OnClickCallback(EPSOneUserCenterAccountInfoClickType::Cancellation);
		}
	};
	ChangePasswordCell->OnClickCallback = [this]()
	{
		if (OnClickCallback)
		{
			OnClickCallback(EPSOneUserCenterAccountInfoClickType::ChangePassword);
		}
	};

	return true;
}

void UPSOneUserCenterAccountInfo::NativePreConstruct()
{
	Super::NativePreConstruct();
	UpdateCellVisibilityStatus();
}

void UPSOneUserCenterAccountInfo::UpdateCellVisibilityStatus()
{
	// 账号注销 隐藏
	CancellationCell->SetVisibility(ESlateVisibility::Collapsed);
	
	if (AccountInfo.Email.IsEmpty())
	{
		EmailCell->SetVisibility(ESlateVisibility::Collapsed);
	}
	else
	{
		EmailCell->SetSubTitle(AccountInfo.Email);
		EmailCell->SetVisibility(ESlateVisibility::Visible);
	}

	if (AccountInfo.Mobile.IsEmpty())
	{
		ChangePasswordCell->SetVisibility(ESlateVisibility::Collapsed);
	}
	else
	{
		FText PwdCellText = AccountInfo.bHasPwd ? LOCTEXT("ChangeLoginPassword", "修改登录密码") : LOCTEXT("SetLoginPassword", "设置登录密码");
		ChangePasswordCell->SetTitle(PwdCellText);
		ChangePasswordCell->SetVisibility(ESlateVisibility::Visible);
	}
}

FReply UPSOneUserCenterAccountInfo::NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent)
{
	FReply Reply = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);
	return Reply;
}

#undef LOCTEXT_NAMESPACE
