// Fill out your copyright notice in the Description page of Project Settings.


#include "Views/PSOneUserCenterLeftCell.h"
#include "Engine/Texture2D.h"
#include "Components/Image.h"

void UPSOneUserCenterLeftCell::NativePreConstruct()
{
	Super::NativePreConstruct();
	if (ImageIcon != nullptr)
	{
		FSlateBrush Brush;
		Brush.SetResourceObject(ImageIcon);
		Brush.ImageSize = FVector2D(ImageIcon->GetSizeX(), ImageIcon->GetSizeY());
		Brush.DrawAs = ESlateBrushDrawType::Image;
		Image->SetBrush(Brush);
	}
}

void UPSOneUserCenterLeftCell::ButtonDidClick()
{
	Super::ButtonDidClick();
}
