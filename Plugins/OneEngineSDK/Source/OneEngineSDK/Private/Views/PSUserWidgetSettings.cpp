﻿#include "PSUserWidgetSettings.h"
#include "ImageUtils.h"
#include "IImageWrapper.h"
#include "IImageWrapperModule.h"
#include "Engine/Texture2DDynamic.h"
#include "Engine/Font.h"
#include "Engine/UserInterfaceSettings.h"
#include "Misc/FileHelper.h"

#if ENGINE_SUPPORT_SONY
#if PLATFORM_PS4
	#include <system_service.h>
#endif
#endif

namespace
{
void GetImageFormatFromPath(const FString& Path, EImageFormat& ImageFormat)
{
	const FString FileExtension = FPaths::GetExtension(Path, false);
	ImageFormat = EImageFormat::Invalid;
	if (FileExtension.Equals(TEXT("jpg"), ESearchCase::IgnoreCase) || FileExtension.Equals(
		    TEXT("jpeg"), ESearchCase::IgnoreCase))
	{
		ImageFormat = EImageFormat::JPEG;
	}
	else if (FileExtension.Equals(TEXT("png"), ESearchCase::IgnoreCase))
	{
		ImageFormat = EImageFormat::PNG;
	}
}
UWorld* GetCurrentWorld()
{
	UWorld* World = nullptr;
#if WITH_EDITOR
	if (GIsEditor)
	{
		if (GPlayInEditorID == -1)
		{
			const FWorldContext* WorldContext = GEditor->GetPIEWorldContext(1);
			if (WorldContext == nullptr)
			{
				if (const UGameViewportClient* Viewport = GEngine->GameViewport)
				{
					World = Viewport->GetWorld();
				}
			}
			else
			{
				World = WorldContext->World();
			}
		}
		else
		{
			const FWorldContext* WorldContext = GEditor->GetPIEWorldContext(GPlayInEditorID);
			if (WorldContext == nullptr)
			{
				return nullptr;
			}
			World = WorldContext->World();
		}
	}
	else
	{
		World = GEngine->GetCurrentPlayWorld(nullptr);
	}
#else
	auto WorldContext = GEngine->GetWorldContextFromGameViewport(GEngine->GameViewport);
	if (WorldContext)
	{
		World = WorldContext->World();
	}
#endif
	return World;
}


FVector2D GetViewportSize()
{
	FVector2D ViewportSize(1920, 1080); // Default value

	// 首先检查 GEngine 和 GameViewport 是否有效
	if (!GEngine || !GEngine->GameViewport)
	{
		// 如果基本指针无效，返回默认值
		return ViewportSize;
	}

	// 尝试从 ViewportFrame 获取视口
	if (GEngine->GameViewport->ViewportFrame != nullptr)
	{
		FViewport* Viewport = GEngine->GameViewport->ViewportFrame->GetViewport();
		// 检查 GetViewport() 是否返回有效指针
		if (Viewport)
		{
			ViewportSize = Viewport->GetSizeXY();
			// 如果 GetSizeXY 返回无效大小，则回退
			if (ViewportSize.X <= 0 || ViewportSize.Y <= 0)
			{
				FVector2D ViewSize;
				GEngine->GameViewport->GetViewportSize(ViewSize);
				// 仅当备用方法提供有效大小时才使用它
				if (ViewSize.X > 0 && ViewSize.Y > 0) {
					 ViewportSize = ViewSize;
				} else {
					// 两种方法都失败，保持默认值 1920x1080
					ViewportSize = FVector2D(1920, 1080);
				}
			}
		}
		else
		{
			// GetViewport() 返回 null，尝试直接从 GameViewport 获取大小
			FVector2D ViewSize;
			GEngine->GameViewport->GetViewportSize(ViewSize);
			// 仅当此方法提供有效大小时才使用它
			if (ViewSize.X > 0 && ViewSize.Y > 0) {
				ViewportSize = ViewSize;
			}
			// 否则保持默认值 (1920, 1080)
		}
	}
	else
	{
		// ViewportFrame 为 null，直接从 GameViewport 获取大小
		FVector2D ViewSize;
		GEngine->GameViewport->GetViewportSize(ViewSize);
		// 仅当此方法提供有效大小时才使用它
		if (ViewSize.X > 0 && ViewSize.Y > 0) {
			ViewportSize = ViewSize;
		}
		// 否则保持默认值 (1920, 1080)
	}

	// 最终健全性检查，确保大小不为零或负数
	if (ViewportSize.X <= 0 || ViewportSize.Y <= 0)
	{
		// 如果最终得到无效大小，返回默认值。
		ViewportSize = FVector2D(1920, 1080);
	}

	return ViewportSize;
}

float GetUIScaler()
{
	FVector2D ViewportSize = GetViewportSize();
	int32 X = FGenericPlatformMath::FloorToInt(ViewportSize.X);
	int32 Y = FGenericPlatformMath::FloorToInt(ViewportSize.Y);
	float CurrentDPIScale = GetDefault<UUserInterfaceSettings>(UUserInterfaceSettings::StaticClass())->GetDPIScaleBasedOnSize(FIntPoint(X, Y));
	return ViewportSize.Y / 1080.f / CurrentDPIScale;
}

}

UPSUserWidgetSettings::UPSUserWidgetSettings()
{
	bool bResourceLoaded = false;
#if ENGINE_SUPPORT_SONY
#if PLATFORM_PS4
	int value = 0;
	sceSystemServiceParamGetInt(SCE_SYSTEM_SERVICE_PARAM_ID_ENTER_BUTTON_ASSIGN, &value);
	if (value == SCE_SYSTEM_PARAM_ENTER_BUTTON_ASSIGN_CIRCLE)
	{
		bEnterButtonAssignCircle = true;
	}

	PSCircleTexture = LoadObject<UTexture2D>(nullptr, TEXT("Texture2D'/OneEngineSDK/PS4Images/PlayStationCircleButton.PlayStationCircleButton'"));
	PSCrossTexture = LoadObject<UTexture2D>(nullptr, TEXT("Texture2D'/OneEngineSDK/PS4Images/PlayStationCrossButton.PlayStationCrossButton'"));
	PSSquareTexture = LoadObject<UTexture2D>(nullptr, TEXT("Texture2D'/OneEngineSDK/PS4Images/PlayStationSquareButton.PlayStationSquareButton'"));
	PSTriangleTexture = LoadObject<UTexture2D>(nullptr, TEXT("Texture2D'/OneEngineSDK/PS4Images/PlayStationTriangleButton.PlayStationTriangleButton'"));

	bResourceLoaded = true;

#elif PLATFORM_PS5

	PSCircleTexture = LoadObject<UTexture2D>(nullptr, TEXT("Texture2D'/OneEngineSDK/PS5Images/PlayStationCircleButton.PlayStationCircleButton'"));
	PSCrossTexture = LoadObject<UTexture2D>(nullptr, TEXT("Texture2D'/OneEngineSDK/PS5Images/PlayStationCrossButton.PlayStationCrossButton'"));
	PSSquareTexture = LoadObject<UTexture2D>(nullptr, TEXT("Texture2D'/OneEngineSDK/PS5Images/PlayStationSquareButton.PlayStationSquareButton'"));
	PSTriangleTexture = LoadObject<UTexture2D>(nullptr, TEXT("Texture2D'/OneEngineSDK/PS5Images/PlayStationTriangleButton.PlayStationTriangleButton'"));
	bResourceLoaded = true;

#endif
#endif

	if (!bResourceLoaded)
	{
		// load default
		PSCircleTexture = LoadObject<UTexture2D>(nullptr,
			TEXT("Texture2D'/OneEngineSDK/PS5Images/PlayStationCircleButton.PlayStationCircleButton'"));
		PSCrossTexture = LoadObject<UTexture2D>(nullptr,
			TEXT("Texture2D'/OneEngineSDK/PS5Images/PlayStationCrossButton.PlayStationCrossButton'"));
		PSSquareTexture = LoadObject<UTexture2D>(nullptr,
			TEXT("Texture2D'/OneEngineSDK/PS5Images/PlayStationSquareButton.PlayStationSquareButton'"));
		PSTriangleTexture = LoadObject<UTexture2D>(nullptr,
			TEXT("Texture2D'/OneEngineSDK/PS5Images/PlayStationTriangleButton.PlayStationTriangleButton'"));
	}

	PSL1Texture = LoadObject<UTexture2D>(nullptr, TEXT("Texture2D'/OneEngineSDK/Texture/T_Gamepad_L1.T_Gamepad_L1'"));
	PSR1Texture = LoadObject<UTexture2D>(nullptr, TEXT("Texture2D'/OneEngineSDK/Texture/T_Gamepad_R1.T_Gamepad_R1'"));
	PSR2Texture = LoadObject<UTexture2D>(nullptr, TEXT("Texture2D'/OneEngineSDK/Texture/T_Gamepad_R2.T_Gamepad_R2'"));

	// Default
	AgreementPromptBackgroundTexture = LoadObject<UTexture2D>(nullptr,
		TEXT("Texture2D'/OneEngineSDK/Texture/T_ProcotolHint_BG.T_ProcotolHint_BG'"));
}

UPSUserWidgetSettings* UPSUserWidgetSettings::Get()
{
	static UPSUserWidgetSettings* Settings = nullptr;

	if (!Settings)
	{
		Settings = LoadObject<UPSUserWidgetSettings>(nullptr,
			TEXT("PSUserWidgetSettings'/OneEngineSDK/DA_WidgetSettings.DA_WidgetSettings'"));
		
		Settings->AddToRoot();
		
		UFont* Font = LoadObject<UFont>(nullptr,TEXT("Font'/OneEngineSDK/Fonts/ONE_Font.ONE_Font'"));
		if (Font)
		{
			Settings->CustomFont = Font;
		}
	}
	return Settings;
}

float UPSUserWidgetSettings::GetUIScaleRatio()
{
	return GetUIScaler();
}

void UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawImage(FSlateBrush& Brush, UTexture2D* Texture)
{
	UPSUserWidgetSettings::SetupBrushWithTexture2D(Brush, Texture, ESlateBrushDrawType::Image);
}

void UPSUserWidgetSettings::SetupBrushWithTexture2D_DrawBox(FSlateBrush& Brush, UTexture2D* Texture)
{
	UPSUserWidgetSettings::SetupBrushWithTexture2D(Brush, Texture, ESlateBrushDrawType::Box);
}

void UPSUserWidgetSettings::SetupBrushWithTexture2D(FSlateBrush& Brush, UTexture2D* Texture, ESlateBrushDrawType::Type DrawType,
	float Margin)
{
	Brush.SetResourceObject(Texture);
	Brush.ImageSize = FVector2D(Texture->GetSizeX(), Texture->GetSizeY());
	Brush.DrawAs = DrawType;
	Brush.Margin = FMargin(Margin);
}

bool UPSUserWidgetSettings::IsClickedEnterKey(const FKeyEvent& InKeyEvent)
{
	FKey EnterKey;
	if (InKeyEvent.GetKey().IsGamepadKey())
	{
		if (UPSUserWidgetSettings::Get()->bEnterButtonAssignCircle)
		{
			EnterKey = EKeys::Gamepad_FaceButton_Right;
		}
		else
		{
			EnterKey = EKeys::Gamepad_FaceButton_Bottom;
		}
	}
	else
	{
		EnterKey = EKeys::Enter;
	}
	return InKeyEvent.GetKey() == EnterKey;
}

bool UPSUserWidgetSettings::IsClickedBackKey(const FKeyEvent& InKeyEvent)
{
	FKey BackKey;
	if (InKeyEvent.GetKey().IsGamepadKey())
	{
		if (UPSUserWidgetSettings::Get()->bEnterButtonAssignCircle)
		{
			BackKey = EKeys::Gamepad_FaceButton_Bottom;
		}
		else
		{
			BackKey = EKeys::Gamepad_FaceButton_Right;
		}
	}
	else
	{
		BackKey = EKeys::Escape;
	}
	return InKeyEvent.GetKey() == BackKey;
}

//
// UTexture2DDynamic* UPSUserWidgetSettings::LoadImageToTexture2DDy(const FString& ImagePath)
// {
// 	TArray64<uint8>* OutData = new TArray64<uint8>(); //跟格式无关的颜色数据
// 	int32 Width = 0;
// 	int32 Height = 0;
// 	if (LoadImageToData(ImagePath, *OutData, Width, Height))
// 	{
// 		UTexture2DDynamic* InDyTexture = UTexture2DDynamic::Create(Width, Height);
// 		if (InDyTexture && OutData)
// 		{
// 			//如果为 false，表示单独使用 Alpha 通道作为遮罩
// 			InDyTexture->SRGB = true;
// 			//为纹理创建一个新的资源，并且更新对改资源的所有缓存引用
// 			InDyTexture->UpdateResource();
// 			FTexture2DDynamicResource* Texture2DDyRes = static_cast<FTexture2DDynamicResource*>(InDyTexture->
// 				Resource);
// 			ENQUEUE_RENDER_COMMAND(FWriterRawDataToDyTexture2D)
// 			([Texture2DDyRes, OutData](FRHICommandListImmediate& RHICommandList) mutable
// 				{
// 					//给 Texture2DDyRes 赋值
// 					check(IsInRenderingThread());
// 					FRHITexture2D* RHITexture2D = Texture2DDyRes->GetTexture2DRHI();
// 					const int64 CurrentWidth = RHITexture2D->GetSizeX();
// 					const int64 CurrentHeight = RHITexture2D->GetSizeY();
// 					//给动态贴图赋值
// 					uint32 DestStride = 0; //目标步长
// 					//加锁
// 					uint8* DestData = static_cast<uint8*>(RHILockTexture2D(
// 						RHITexture2D, 0, RLM_WriteOnly, DestStride, false, false));
// 					for (int64 y = 0; y < CurrentHeight; ++y)
// 					{
// 						const int64 DestPtrStride = y * DestStride;
// 						uint8* DestPtr = &DestData[DestPtrStride];
// 						uint8* StrData = OutData->GetData();
// 						const int64 SrcPtrStride = y * CurrentWidth;
// 						const FColor* SrcPtr = &(reinterpret_cast<FColor*>(StrData))[SrcPtrStride];
// 						for (int64 x = 0; x < CurrentWidth; ++x)
// 						{
// 							*DestPtr++ = SrcPtr->B;
// 							*DestPtr++ = SrcPtr->G;
// 							*DestPtr++ = SrcPtr->R;
// 							*DestPtr++ = SrcPtr->A;
// 							SrcPtr++;
// 						}
// 					}
// 					RHIUnlockTexture2D(RHITexture2D, 0, false, false);
// 					if (OutData)
// 					{
// 						delete OutData;
// 						OutData = nullptr;
// 					}
// 				}
// 			);
// 			return InDyTexture;
// 		}
// 	}
// 	if (OutData)
// 	{
// 		delete OutData;
// 		OutData = nullptr;
// 	}
// 	return nullptr;
// }
//
// void UPSUserWidgetSettings::SaveImageFromTexture2DDy(UTexture2DDynamic* InDyTex, const FString& SavePath)
// {
// 	TArray64<uint8>* RawData = new TArray64<uint8>();
// 	FTexture2DDynamicResource* TextureResource = static_cast<FTexture2DDynamicResource*>(InDyTex->Resource);
// 	ENQUEUE_RENDER_COMMAND(FWriteRawDataToTexture)(
// 		[TextureResource, RawData, SavePath](FRHICommandListImmediate& RHICmdList) mutable
// 		{
// 			check(IsInRenderingThread());
// 			FRHITexture2D* TextureRHI = TextureResource->GetTexture2DRHI();
// 			int32 Width = TextureRHI->GetSizeX();
// 			int32 Height = TextureRHI->GetSizeY();
// 			uint32 DestStride = 0;
// 			const uint8* DestData = static_cast<uint8*>(RHILockTexture2D(TextureRHI, 0, RLM_ReadOnly, DestStride, false,
// 			                                                             false));
// 			RawData->SetNum(Width * Height * 4);
// 			for (int32 y = 0; y < Height; y++)
// 			{
// 				const uint8* DestPtr = &DestData[(static_cast<int64>(Height) - 1 - y) * DestStride];
// 				FColor* SrcPtr = &(reinterpret_cast<FColor*>(RawData->GetData()))[(static_cast<int64>(Height) - 1 - y) *
// 					Width];
// 				for (int32 x = 0; x < Width; x++)
// 				{
// 					SrcPtr->B = *DestPtr++;
// 					SrcPtr->G = *DestPtr++;
// 					SrcPtr->R = *DestPtr++;
// 					SrcPtr->A = *DestPtr++;
// 					SrcPtr++;
// 				}
// 			}
// 			SaveImageFromRawData(*RawData, SavePath, Width, Height);
// 			RHIUnlockTexture2D(TextureRHI, 0, false, false);
// 			delete RawData;
// 		});
// }
//
// bool UPSUserWidgetSettings::LoadImageToData(const FString& ImagePath, TArray64<uint8>& OutData, int32& Width, int32& Height)
// {
// 	//取出图片的二进制数据
// 	TArray<uint8> ImageResultData;
// 	if (!FFileHelper::LoadFileToArray(ImageResultData, *ImagePath))
// 	{
// 		return false;
// 	}
//
// 	//解析图片格式
// 	EImageFormat ImageFormat = EImageFormat::Invalid;
// 	GetImageFormatFromPath(ImagePath, ImageFormat);
//
// 	//加载图片处理模块
// 	IImageWrapperModule& ImageWrapperModule = FModuleManager::LoadModuleChecked<IImageWrapperModule>("ImageWrapper");
// 	//根据不同的图片格式创建不同的图片处理类的具体实例
// 	const TSharedPtr<IImageWrapper> ImageWrapperPtr = ImageWrapperModule.CreateImageWrapper(ImageFormat);
// 	if (ImageWrapperPtr.IsValid() && ImageWrapperPtr->SetCompressed(ImageResultData.GetData(),
// 	                                                                ImageResultData.GetAllocatedSize()))
// 	{
// 		//OutData 与格式无关的颜色数据
// 		ImageWrapperPtr->GetRaw(ERGBFormat::BGRA, 8, OutData);
// 		Width = ImageWrapperPtr->GetWidth();
// 		Height = ImageWrapperPtr->GetHeight();
// 		return true;
// 	}
// 	return false;
// }
//
// bool UPSUserWidgetSettings::SaveImageFromRawData(TArray64<uint8>& OutData, const FString& SavePath, const int32& Width, const int32& Height)
// {
// 	EImageFormat ImageFormat;
// 	GetImageFormatFromPath(SavePath, ImageFormat);
// 	IImageWrapperModule& ImageWrapperModule = FModuleManager::LoadModuleChecked<IImageWrapperModule>("ImageWrapper");
// 	TSharedPtr<IImageWrapper> ImageWrapperPtr = ImageWrapperModule.CreateImageWrapper(ImageFormat);
// 	if (ImageWrapperPtr.IsValid() && ImageWrapperPtr->SetRaw(OutData.GetData(), OutData.GetAllocatedSize(), Width,
// 	                                                         Height, ERGBFormat::BGRA, 8))
// 	{
// 		const TArray64<uint8> CompressedData = ImageWrapperPtr->GetCompressed(100);
// 		FFileHelper::SaveArrayToFile(CompressedData, *SavePath);
// 		return true;
// 	}
// 	return false;
// }
