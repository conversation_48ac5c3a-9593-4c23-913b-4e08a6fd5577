﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PSOneFocusUserWidget.h"
#include "PSOneChangNick.generated.h"

/**
 * 
 */
UCLASS()
class ONEENGINESDK_API UPSOneChangNick : public UPSOneFocusUserWidget
{
	GENERATED_BODY()

public:
	UPROPERTY(meta=(BindWidget))
	class UScaleBox* ScaleBox;

	UPROPERTY(meta=(BindWidget))
	class UPSOneTextFieldBase* NameTextField;

	// enter icon
	UPROPERTY(meta=(BindWidget))
	class UImage* EnterIcon;

	// backspace icon
	UPROPERTY(meta=(BindWidget))
	class UImage* BackspaceIcon;


	UPROPERTY(meta=(BindWidget))
	class UPSOneConfirmButton* ConfirmButton;
	
	// 点击事件
	TFunction<void(const FString& Nick)> OnConfirm;

	// 点击事件
	TFunction<void()> OnBackspace;

	virtual bool Initialize() override;

protected:
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;
	virtual FReply NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent) override;

	TSharedPtr<class FPSOneFocusNavigator> FocusNavigator;
};
