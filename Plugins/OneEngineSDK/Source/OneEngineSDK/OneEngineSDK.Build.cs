// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;
using System;
using System.IO;
using System.Reflection;

public class OneEngineSDK : ModuleRules
{
	public OneEngineSDK(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicIncludePaths.AddRange(
			new string[]
			{
				// ... add public include paths required here ...
			}
		);


		PrivateIncludePaths.AddRange(
			new string[]
			{
				// ... add other private include paths required here ...
			}
		);
		


		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
				"UMG",
				"RHI", // for PS4 and PS5
				"RenderCore", // for PS4 and PS5
				// ... add other public dependencies that you statically link with here ...
			}
		);

		// 获取 SDK 所选地区
		ConfigHierarchy Ini = ConfigCache.ReadHierarchy(ConfigHierarchyType.Game, Target.ProjectFile != null ? Target.ProjectFile.Directory : null, Target.Platform);
		string newSectionName = "/Script/OneEngineEditor.OneEngineSettings";
		string SDKRegion;
		string SDKRegionModule = "Engine";
		Ini.GetString(newSectionName, "SDKRegion", out SDKRegion);

		if (Target.Platform == UnrealTargetPlatform.Mac || Target.Platform == UnrealTargetPlatform.IOS)
		{
			// iOS、Mac 根据后台下载 ini 配置文件，决定地区
			string RegionType;
			Ini.GetString("/Script/OneEngineSDK", "Region", out RegionType);
			if (RegionType == "1")
			{
				SDKRegion = "Mainland";
			}
			else if (RegionType == "2")
			{
				SDKRegion = "Oversea";
			}
			else
			{
				// iOS 没读取到下载的配置，按未知地区处理，不加载对应地区 module
				SDKRegionModule = "Engine";
			}
		}

		if (SDKRegion == "Mainland")
		{
			System.Console.WriteLine(">>>> OneEngine Region Is Mainland");
			PublicDefinitions.Add("KONEENGINE_REGION_MAINLAND=1");
			SDKRegionModule = "OneEngineMainlandLibrary";
		}
		else if (SDKRegion == "Oversea")
		{
			System.Console.WriteLine(">>>> OneEngine Region Is Oversea");
			PublicDefinitions.Add("KONEENGINE_REGION_OVERSEA=1");
			SDKRegionModule = "OneEngineOverseaLibrary";
		}
		else
		{
			System.Console.WriteLine(">>>> OneEngine Region Is Unknown");
		}

		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				// ... add private dependencies that you statically link with here ...
				"CoreUObject",
				"Engine",
				"Slate",
				"SlateCore",
				"InputCore",
				"ApplicationCore",
				"Json",
				"JsonUtilities",
				"ImageWrapper",
				"Projects",
				"OneEngineCoreLibrary",
				SDKRegionModule
			}
		);

		if (Target.Platform == UnrealTargetPlatform.Win64)
		{
            PublicDependencyModuleNames.AddRange(new string[] { "Projects" });
        }

		if (Target.Type == TargetRules.TargetType.Editor)
		{
			PublicDependencyModuleNames.AddRange(new string[] { "UnrealEd" });
		}

		var ContainsSony = 0;
		var ValidPlatforms = UnrealTargetPlatform.GetValidPlatforms();
		foreach (var targetPlatform in ValidPlatforms)
		{
			switch (targetPlatform.ToString())
			{
				case "PS4":
					ContainsSony |= 1;
					break;
				case "PS5":
					ContainsSony |= 2;
					break;
			}
		}

		if (ContainsSony == 3)
		{
			Console.WriteLine(">>> Engine support Sony feature");
			PublicDefinitions.Add("ENGINE_SUPPORT_SONY=1");
		}
		else
		{
			Console.WriteLine(">>> Engine not support Sony feature");
			PublicDefinitions.Add("ENGINE_SUPPORT_SONY=0");
		}

		// 添加 PS4，PS5 第三方库依赖模块
		if (Target.Platform.ToString() == "PS4" || Target.Platform.ToString() == "PS5")
		{
			PrivateDependencyModuleNames.Add("DependLibrariesLibrary");
		}

		DynamicallyLoadedModuleNames.AddRange(
			new string[]
			{
				// ... add any modules that your module loads dynamically here ...
			}
		);

		if (Target.Platform.ToString() == "OpenHarmony")
		{
			PrivateDependencyModuleNames.AddRange(
				new string[]
				{
					"aki_jsbind"
				}
			);
		}
	}
}
