#include "OneEngineEditor.h"
#include "Developer/Settings/Public/ISettingsModule.h"
#include "Developer/Settings/Public/ISettingsSection.h"
#include "Settings/ProjectPackagingSettings.h"

#define LOCTEXT_NAMESPACE "FOneEngineEditorModule"

void FOneEngineEditorModule::StartupModule()
{
	ISettingsModule* SettingsModule = FModuleManager::GetModulePtr<ISettingsModule>("Settings");
	if (SettingsModule)
	{
		UOneEngineSettings* OneEngineSettings = GetMutableDefault<UOneEngineSettings>();
		ISettingsSectionPtr SettingsSection = SettingsModule->RegisterSettings("Project", "Plugins", "OneEngine",
			LOCTEXT("OneEngineSettingsName", "OneEngine"),
			LOCTEXT("OneEngineSettingsDescription", "Settings for OneEngineSDK Plugin"),OneEngineSettings
		);
		if (SettingsSection.IsValid())
		{
			SettingsSection->OnModified().BindRaw(this, &FOneEngineEditorModule::HandleSettingsModified);
		}
	}
}

void FOneEngineEditorModule::ShutdownModule()
{
	ISettingsModule* SettingsModule = FModuleManager::GetModulePtr<ISettingsModule>("Settings");
	if (SettingsModule)
	{
		SettingsModule->UnregisterSettings("Project", "Plugins", "OneEngineSDK");
	}
}

bool FOneEngineEditorModule::HandleSettingsModified()
{
	UOneEngineSettings* settings = GetMutableDefault<UOneEngineSettings>();
	UProjectPackagingSettings* PackagingSettings = GetMutableDefault<UProjectPackagingSettings>();
	

#if ENGINE_MAJOR_VERSION >= 5 && ((ENGINE_MINOR_VERSION == 3 && ENGINE_PATCH_VERSION >= 2) || ENGINE_MINOR_VERSION > 3)
	settings->TryUpdateDefaultConfigFile();
#else
	settings->UpdateDefaultConfigFile();
#endif
	PackagingSettings->SaveConfig();

#if ENGINE_MAJOR_VERSION >= 5 && ((ENGINE_MINOR_VERSION == 3 && ENGINE_PATCH_VERSION >= 2) || ENGINE_MINOR_VERSION > 3)
	PackagingSettings->TryUpdateDefaultConfigFile();
#else
	PackagingSettings->UpdateDefaultConfigFile();
#endif

	return true;
}

bool FOneEngineEditorModule::OnSave()
{
	return false;
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FOneEngineEditorModule, OneEngineEditor)
