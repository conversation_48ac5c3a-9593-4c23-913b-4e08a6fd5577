// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Engine/DeveloperSettings.h"
#include "OneEngineSettings.generated.h"

UENUM(BlueprintType)
enum class ESDKRegionConfig : uint8
{
	Mainland,
	Oversea
};

UCLASS(config = Game, defaultconfig)
class ONEENGINEEDITOR_API UOneEngineSettings : public UObject
{
	GENERATED_BODY()

public:
	UPROPERTY(Config,EditAnywhere, Category = "OneEngineSDK")
	ESDKRegionConfig SDKRegion = ESDKRegionConfig::Mainland;
	
	UPROPERTY(Config, EditAnywhere, Category = "OneEngineSDK")
	FString AppID;
    
    UPROPERTY(Config,EditAnywhere, Category = "OneEngineSDK", meta = (ToolTip = "配置文件版本号，需和Dev配置一致"))
	FString  Env;
    
	UPROPERTY(Config, EditAnywhere, Category = "OneEngineSDK", meta = (EditCondition = "SDKRegion == ESDKRegionConfig::Mainland"))
	FString OneAppKey;

	UPROPERTY(Config, EditAnywhere, Category = "PS",
	meta = (DisplayName = "PSMainlandConfigData", EditCondition = "SDKRegion == ESDKRegionConfig::Mainland"))
	FString PSMainlandConfigData;

	UPROPERTY(Config, EditAnywhere, Category = "PS",
		meta = (DisplayName = "PSOverseaConfigData", EditCondition = "SDKRegion == ESDKRegionConfig::Oversea"))
	FString PSOverseaConfigData;

	UPROPERTY(Config, EditAnywhere, Category = "HarmonyOS",
		meta = (DisplayName = "LaohuConfig", EditCondition = "SDKRegion == ESDKRegionConfig::Mainland"))
	FString HarmonyOSConfigFilePath;

	UPROPERTY(Config, EditAnywhere, Category = "Android",meta = (DisplayName = "是否启用启动页背景图",  EditCondition = "SDKRegion == ESDKRegionConfig::Mainland"))
	bool bShowSplashBackGroundImage;

	UPROPERTY(Config, EditAnywhere, Category = "Android",meta = (DisplayName = "启动背景图显示时间 单位ms,默认500ms" , EditCondition = "SDKRegion == ESDKRegionConfig::Mainland"))
	int64 SplashShowTime = 1200;
	
};
