import nativeRender from "libNativeGlue.so";
import { OneSDKAPI } from '@wpsdk/onesdk/src/main/ets/OneSDKAPI';
import { OneSDKPurchaseInfo } from '@wpsdk/onesdk/src/main/ets/open/OneSDKPurchaseInfo';
import { TrackEventRoleInfo } from '@wpsdk/onesdk/src/main/ets/open/TrackEventRoleInfo';
import {
    TrackEventResGetServerListStatus,
    TrackEventResReqStatus,
    TrackEventResResDecStatus,
    TrackEventResUpdateAssetStatus
} from '@wpsdk/onesdk/src/main/ets/open/TrackEventResStatus';
import PushAgent from '@wpsdk/push/src/main/ets/PushAgent';
import { FatigueUserInfo } from '@wpsdk/onesdk/src/main/ets/fatigue/open/FatigueUserInfo';
import { ArrayList, JSON, util } from '@kit.ArkTS';
import { UserRoleInfo } from '@wpsdk/onesdk/src/main/ets/gcode/open/UserRoleInfo';
import { abilityAccessCtrl, common, Context, Permissions, Want } from "@kit.AbilityKit";
import { PushMsg } from "@wpsdk/push/src/main/ets/data/PushMsg";
import { PushTypeInfo } from "@wpsdk/push/src/main/ets/data/PushTypeInfo";
import { PushNotDisturbInfo } from "@wpsdk/push/src/main/ets/data/PushNotDisturbInfo";
import { BusinessError } from "@kit.BasicServicesKit";
import { bundleManager } from '@kit.AbilityKit'
import { notificationManager } from "@kit.NotificationKit";
import { OneShareAPI } from "@wpsdk/share/src/main/ets/open/OneShareAPI";
import { ShareAction } from "@wpsdk/share/src/main/ets/open/ShareAction";
import { IShareCallback } from "@wpsdk/share/src/main/ets/open/IShareCallback";
import { TextBuilder } from "@wpsdk/share/src/main/ets/builder/TextBuilder";
import { ThirdPlatform } from "@wpsdk/share/src/main/ets/open/ShareType";
import { ImageBuilder } from "@wpsdk/share/src/main/ets/builder/ImageBuilder";
import { fileUri } from "@kit.CoreFileKit";


export class OneGlobalSDKBridge {
    static bindMainThreadAki() {
        //主线程绑定 在主线程中执行
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.init", OneGlobalSDKBridge.init);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.setLoginListener", OneGlobalSDKBridge.setLoginListener);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.login", OneGlobalSDKBridge.login);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.logout", OneGlobalSDKBridge.logout);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.hasLogin", OneGlobalSDKBridge.hasLogin);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getChannelId", OneGlobalSDKBridge.getChannelId);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getUserInfo", OneGlobalSDKBridge.getUserInfo);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.pay", OneGlobalSDKBridge.pay);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.openCompliance", OneGlobalSDKBridge.openCompliance);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getChannelMediaId", OneGlobalSDKBridge.getChannelMediaId);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getPlatformOs", OneGlobalSDKBridge.getPlatformOs);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getPlatformChannel", OneGlobalSDKBridge.getPlatformChannel);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getDeviceInfo", OneGlobalSDKBridge.getDeviceInfo);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getDeviceId", OneGlobalSDKBridge.getDeviceId);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getDeviceModel", OneGlobalSDKBridge.getDeviceModel);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getDeviceSys", OneGlobalSDKBridge.getDeviceSys);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.wanmeiTrackEvent", OneGlobalSDKBridge.wanmeiTrackEvent);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.trackEventRoleCreate", OneGlobalSDKBridge.trackEventRoleCreate);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.trackEventRoleLogin", OneGlobalSDKBridge.trackEventRoleLogin);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.trackEventRoleLoginError", OneGlobalSDKBridge.trackEventRoleLoginError);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.trackEventRoleLogout", OneGlobalSDKBridge.trackEventRoleLogout);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.trackEventRoleLevelUp", OneGlobalSDKBridge.trackEventRoleLevelUp);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.wanmeiGameResReqEvent", OneGlobalSDKBridge.wanmeiGameResReqEvent);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.wanmeiGameUpdateAssetEvent", OneGlobalSDKBridge.wanmeiGameUpdateAssetEvent);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.wanmeiGameResDecEvent", OneGlobalSDKBridge.wanmeiGameResDecEvent);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.wanmeiGameGetServerListEvent", OneGlobalSDKBridge.wanmeiGameGetServerListEvent);

        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.initPush", OneGlobalSDKBridge.initPush);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.setPushMsgListener", OneGlobalSDKBridge.setPushMsgListener);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getPushStatus", OneGlobalSDKBridge.getPushStatus);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.changePushState", OneGlobalSDKBridge.changePushState);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.bindUserId", OneGlobalSDKBridge.bindUserId);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.unbindUserId", OneGlobalSDKBridge.unbindUserId);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getPushTypeList", OneGlobalSDKBridge.getPushTypeList);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.updatePushType", OneGlobalSDKBridge.updatePushType);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getPushNotDisturbInfo", OneGlobalSDKBridge.getPushNotDisturbInfo);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.updateNotDisturbInfo", OneGlobalSDKBridge.updateNotDisturbInfo);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getDeviceMap", OneGlobalSDKBridge.getDeviceMap);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.killProcess", OneGlobalSDKBridge.killProcess);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.setLoginListener", OneGlobalSDKBridge.setLoginListener);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.openUserCenter", OneGlobalSDKBridge.openUserCenter);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getSubChannelId", OneGlobalSDKBridge.getSubChannelId);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.fetchAntiAddictionInfo", OneGlobalSDKBridge.fetchAntiAddictionInfo);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getAntiAddictionInfo", OneGlobalSDKBridge.getAntiAddictionInfo);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.startAntiAddictionNotify", OneGlobalSDKBridge.startAntiAddictionNotify);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.stopAntiAddictionNotify", OneGlobalSDKBridge.stopAntiAddictionNotify);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.onForeground", OneGlobalSDKBridge.onForeground);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.onBackground", OneGlobalSDKBridge.onBackground);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.setRoleInfo", OneGlobalSDKBridge.setRoleInfo);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getDeviceRam", OneGlobalSDKBridge.getDeviceRam);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getUserIp", OneGlobalSDKBridge.getUserIp);

        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.verifyRedeemCode", OneGlobalSDKBridge.verifyRedeemCode);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getUserRoleInfoList", OneGlobalSDKBridge.getUserRoleInfoList);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.launchPCScanLogin", OneGlobalSDKBridge.launchPCScanLogin);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.setDebug", OneGlobalSDKBridge.setDebug);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.isDebug", OneGlobalSDKBridge.isDebug);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.trackEnterGameScene", OneGlobalSDKBridge.trackEnterGameScene);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.trackEventExitGameScene", OneGlobalSDKBridge.trackEventExitGameScene);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.trackEventAddExtraDeviceInfo", OneGlobalSDKBridge.trackEventAddExtraDeviceInfo);

        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.startBindPhone", OneGlobalSDKBridge.startBindPhone);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.examinStatus", OneGlobalSDKBridge.examinStatus);


        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.reqPermissionsFromUser", OneGlobalSDKBridge.reqPermissionsFromUser);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.checkPermissionsFromUser", OneGlobalSDKBridge.checkPermissionsFromUser);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.openPermissionsSetting", OneGlobalSDKBridge.openPermissionsSetting);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.openSystemPermissionSetting", OneGlobalSDKBridge.openSystemPermissionSetting);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.share", OneGlobalSDKBridge.share);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.isInstalledApp", OneGlobalSDKBridge.isInstalledApp);
        nativeRender.JSBind.bindFunction("OneGlobalSDKBridge.getAppId", OneGlobalSDKBridge.getAppId);

    }

    static bindWorkerThreadAki() {
        //worker线程绑定 在worker线程中执行

    }

    static init(appId: number, appKey: string, callback: (success: boolean, code: number, msg: string) => void) {
        if (appId == 0 || appKey == null || appKey == undefined) {
            //读取config 文件
            try {
                let context: Context = globalThis["abilityContext"]
                let data = context.resourceManager.getRawFileContentSync("harmony_config")
                if (data && data.length > 0) {
                    let base64 = new util.Base64Helper()
                    let configData = util.TextDecoder.create().decodeToString(data)
                    let configJson = base64.decodeSync(configData)
                    let config = util.TextDecoder.create().decodeToString(configJson)
                    if (config) {
                        let json = JSON.parse(config)
                        if (json && json.hasOwnProperty("one_app_id") && json.hasOwnProperty("one_app_key")) {
                            appId = Number(json["one_app_id"])
                            appKey = json["one_app_key"]
                        }
                    }
                }
            } catch (e) {
                console.log("get harmony_config content failed by exception")
            }
        }
        OneSDKAPI.init(appId, appKey, {
            success: function (): void {
                callback(true, 0, "")
            },
            fail: function (errCode: number, errMsg: string): void {
                callback(false, errCode, errMsg)
            }
        })
    }

    static setLoginListener(loginCallback?: (success: boolean, isCancel: boolean, code: number, msg: string) => void, logoutCallback?: (success: boolean, code: number, msg: string) => void) {
        OneSDKAPI.setLoginCallback({
            success: function (): void {
                loginCallback?.(true, false, 0, "");
            },
            fail: function (errCode: number, errMsg: string): void {
                loginCallback?.(false, false, errCode, errMsg);
            },
            cancel: function (errCode: number, errMsg: string): void {
                loginCallback?.(false, true, errCode, errMsg);
            }
        }, {
            success: function (): void {
                logoutCallback?.(true, 0, "")
            },
            fail: function (errCode: number, errMsg: string): void {
                logoutCallback?.(false, errCode, errMsg)
            }
        })
    }

    static login() {
        OneSDKAPI.login()
    }

    private static roleInfo: Record<string, string> = {};

    static setRoleInfo(roleJson: string) {
        try {
            // JsonObj->SetStringField("roleId", Info.roleId);
            // JsonObj->SetStringField("roleName", Info.roleName);
            // JsonObj->SetStringField("serverId", Info.serverId);
            // JsonObj->SetStringField("serverName", Info.serverName);
            // JsonObj->SetStringField("vip", Info.vip);
            // JsonObj->SetStringField("lv", Info.lv);
            let role = JSON.parse(roleJson) as Record<string, string>
            Object.assign(OneGlobalSDKBridge.roleInfo, role);
        } catch (e) {
            console.error("setRoleInfo parse json failed")
        }
    }

    static pay(orderInfo: string, callback?: (success: boolean, isCancel: boolean, code: number, msg: string, orderId: string) => void) {
        let json = JSON.parse(orderInfo);
        //  orderId: string //订单号
        //   productId: string //商品id
        //   productName: string //商品名字
        //   productPrice: number //商品价格
        //   // productCount: string //商品数量
        //   serverId: string //游戏区服id
        //   // serverName: string //游戏区服名称
        //   roleId: string //角色id
        //   roleName: string //角色名称
        //   ext: string //游戏需要one服务器给游戏服务器透传的字段
        let order = new OneSDKPurchaseInfo();
        order.orderId = json["orderId"]
        order.productId = json["productId"]
        order.productName = json["productName"]
        order.productPrice = json["productPrice"]
        order.serverId = json["serverId"]
        // order.serverName=json["serverName"]
        order.roleId = json["roleId"]
        order.roleName = json["roleName"]
        order.ext = json["ext"]
        OneSDKAPI.purchase(order, {
            success: function (orderId: string): void {
                if (callback) {
                    callback(true, false, 0, "", orderId);
                }
            },
            fail: function (orderId: string, errCode: number, errMsg: string): void {
                if (callback) {
                    callback(false, false, errCode, errMsg, orderId);
                }
            },
            cancel: function (orderId: string, errCode: number, errMsg: string): void {
                if (callback) {
                    callback(false, true, errCode, errMsg, orderId);
                }
            }
        })
    }

    static logout() {
        OneSDKAPI.logout();
    }

    static hasLogin(): boolean {
        return OneSDKAPI.hasLogin()
    }

    static getUserInfo(): string {
        let json = {
            "uid": OneSDKAPI.getOneSDKUserId(),
            "token": OneSDKAPI.getOneSDKUserToken()
        }
        return JSON.stringify(json)
    }

    static getChannelId(): string {
        return OneSDKAPI.getChannelId().toString();
    }

    static openCompliance() {
        OneSDKAPI.openLegalTermsPage();
    }

    static getChannelMediaId(): string {
        return OneSDKAPI.getChannelMediaId()
    }

    static getPlatformOs(): number {
        return OneSDKAPI.getPlatformOS();
    }

    static getPlatformChannel(): string {
        return OneSDKAPI.getChannelPlatform();
    }

    static getDeviceInfo(): string {
        return JSON.stringify({
            "deviceID": OneSDKAPI.getUDID(),
            "deviceSys": OneSDKAPI.getDeviceSys(),
            "ext": {
                "dmd": OneSDKAPI.getDeviceModel(),
                "mmr": OneSDKAPI.getDeviceRAM(),
            }
        })
    }


    /**
     * 游戏自定义打点
     */
    static wanmeiTrackEvent(event: string, paramMap: object) {
        let params = new Map<string, string>(Object.entries(paramMap))
        OneSDKAPI.wanmeiTrackEvent(event, params)
    }


    /**
     * 角色创建
     */
    static trackEventRoleCreate(roleInfo: {
        roleId: string, roleName: string, serverId: string, serverName: string,
        vipLevel: string, roleLevel: string, ip?: string, port?: string
    }) {
        OneSDKAPI.trackEventRoleCreate(roleInfo)
    }

    /**
     * 角色登录成功
     */
    static trackEventRoleLogin(roleInfo: {
        roleId: string, roleName: string, serverId: string, serverName: string,
        vipLevel: string, roleLevel: string, ip?: string, port?: string
    }) {
        OneSDKAPI.trackEventRoleLogin(roleInfo)

    }

    /**
     * 角色登录失败
     */
    static trackEventRoleLoginError(roleInfo: {
        roleId: string, roleName: string, serverId: string, serverName: string,
        vipLevel: string, roleLevel: string, ip?: string, port?: string
    }) {
        OneSDKAPI.trackEventRoleLoginError(roleInfo)
    }

    /**
     * 角色登出
     */
    static trackEventRoleLogout(roleInfo: {
        roleId: string, roleName: string, serverId: string, serverName: string,
        vipLevel: string, roleLevel: string, ip?: string, port?: string
    }) {
        OneSDKAPI.trackEventRoleLogout(roleInfo)
    }

    /**
     * 角色升级
     */
    static trackEventRoleLevelUp(roleInfo: {
        roleId: string, roleName: string, serverId: string, serverName: string,
        vipLevel: string, roleLevel: string, ip?: string, port?: string
    }) {
        OneSDKAPI.trackEventRoleLevelUp(roleInfo)
    }

    /**
     * 资源版本核对方法
     */
    static wanmeiGameResReqEvent(status: string, url: string, errorCode?: string, errorMsg?: string) {
        if (TrackEventResReqStatus.BEGIN === status) {
            OneSDKAPI.wanmeiGameResReqEvent(TrackEventResReqStatus.BEGIN, url)
        } else if (TrackEventResReqStatus.SUCCESS === status) {
            OneSDKAPI.wanmeiGameResReqEvent(TrackEventResReqStatus.SUCCESS, url)
        } else if (TrackEventResReqStatus.ERROR === status) {
            OneSDKAPI.wanmeiGameResReqEvent(TrackEventResReqStatus.ERROR, url, errorCode, errorMsg)
        }
    }

    /**
     * 完美统计资源下载方法
     */
    static wanmeiGameUpdateAssetEvent(status: string, url: string, errorCode?: string, errorMsg?: string) {

        if (TrackEventResUpdateAssetStatus.BEGIN === status) {
            OneSDKAPI.wanmeiGameUpdateAssetEvent(TrackEventResUpdateAssetStatus.BEGIN, url)
        } else if (TrackEventResUpdateAssetStatus.SUCCESS === status) {
            OneSDKAPI.wanmeiGameUpdateAssetEvent(TrackEventResUpdateAssetStatus.SUCCESS, url)
        } else if (TrackEventResUpdateAssetStatus.ERROR === status) {
            OneSDKAPI.wanmeiGameUpdateAssetEvent(TrackEventResUpdateAssetStatus.ERROR, url, errorCode, errorMsg)
        }
    }

    /**
     * 完美统计解压缩方法
     */
    static wanmeiGameResDecEvent(status: string, errorMsg?: string) {

        if (TrackEventResResDecStatus.BEGIN === status) {
            OneSDKAPI.wanmeiGameResDecEvent(TrackEventResResDecStatus.BEGIN)
        } else if (TrackEventResResDecStatus.SUCCESS === status) {
            OneSDKAPI.wanmeiGameResDecEvent(TrackEventResResDecStatus.SUCCESS)
        } else if (TrackEventResResDecStatus.ERROR === status) {
            OneSDKAPI.wanmeiGameResDecEvent(TrackEventResResDecStatus.ERROR, errorMsg)
        }
    }

    /**
     * 完美统计服务器列表方法
     */
    static wanmeiGameGetServerListEvent(status: string, url: string, errorCode?: string, errorMsg?: string) {
        if (TrackEventResGetServerListStatus.BEGIN === status) {
            OneSDKAPI.wanmeiGameGetServerListEvent(TrackEventResGetServerListStatus.BEGIN, url)
        } else if (TrackEventResGetServerListStatus.SUCCESS === status) {
            OneSDKAPI.wanmeiGameGetServerListEvent(TrackEventResGetServerListStatus.SUCCESS, url)
        } else if (TrackEventResGetServerListStatus.ERROR === status) {
            OneSDKAPI.wanmeiGameGetServerListEvent(TrackEventResGetServerListStatus.ERROR, url, errorCode, errorMsg)
        }
    }

    static openExitGameConfirmDialog() {
        OneSDKAPI.openExitGameConfirmDialog()
    }

    static onForeground() {
        let context = globalThis["abilityContext"]
        if (context) {
            OneSDKAPI.onForeground(context);
        }
    }

    static onBackground() {
        OneSDKAPI.onBackground()
    }

    static getDeviceId(): string {
        return OneSDKAPI.getUDID()
    }

    static getUserIp(): string {
        return OneSDKAPI.getUserIp()
    }

    static getDeviceModel(): string {
        return OneSDKAPI.getDeviceModel()
    }

    static getDeviceSys(): string {
        return OneSDKAPI.getDeviceSys();
    }

    static getDeviceRam(): string {
        return OneSDKAPI.getDeviceRAM()
    }

    static isNewUser(): boolean {
        return OneSDKAPI.isNewUser()
    }

    static openUserCenter() {
        return OneSDKAPI.openUserCenter()
    }

    static getSubChannelId(): number {
        return OneSDKAPI.getSubChannelId();
    }

    /**
     * 异步获取实时联网防沉迷信息
     */
    static fetchAntiAddictionInfo(callback: (result: string) => void) {
        OneSDKAPI.fetchAntiAddictionInfo({
            getFatigueUserInfo: function (fatigueUserInfo: FatigueUserInfo): void {
                callback?.(JSON.stringify(fatigueUserInfo ?? {}))
            }
        })
    }

    /**
     * 同步获取本地缓存防沉迷信息
     */
    static getAntiAddictionInfo(): string {
        return JSON.stringify(OneSDKAPI.getAntiAddictionInfo() ?? {})
    }

    /**
     * 开启防沉迷监控
     */
    static startAntiAddictionNotify(roleId: string, serverId: string, callback?: (fatigueUserInfo: string) => void) {
        if (!roleId) {
            roleId = OneGlobalSDKBridge.roleInfo.roleId
        }
        if (!serverId) {
            serverId = OneGlobalSDKBridge.roleInfo.serverId
        }
        OneSDKAPI.startAntiAddictionNotify(roleId, serverId, {
            continueGame: function (fatigueUserInfo: FatigueUserInfo): void {
            },
            forbidGame: function (fatigueUserInfo: FatigueUserInfo): void {
                callback?.(JSON.stringify(fatigueUserInfo ?? {}))
            }
        })
    }

    /**
     * 关闭防沉迷监控
     */
    static stopAntiAddictionNotify() {
        OneSDKAPI.stopAntiAddictionNotify()
    }

    static getDeviceMap(): string {
        return JSON.stringify({
            "deviceID": OneSDKAPI.getUDID(),
            "deviceSys": OneSDKAPI.getDeviceSys(),
            "deviceMode": OneSDKAPI.getDeviceModel(),
            "deviceRamSize": OneSDKAPI.getDeviceRAM()
        })
    }


    static initPush(oneAppId: number, appKey: string, callback?: (success: boolean, deviceToken: string, code: number, msg: string) => void) {
        let context = globalThis["abilityContext"]
        PushAgent.init(context, oneAppId.toString(), appKey, {
            onSuccess: function (): void {
                PushAgent.registerPush({
                    onSuccess: function (deviceId: string, deviceToken: string): void {
                        if (callback) {
                            callback(true, deviceToken, 0, "");
                        }
                    },
                    onFail: function (code: number, msg: string): void {
                        if (callback) {
                            callback(false, "", code, msg);
                        }
                    }
                })
            },
            onFail: function (code: number, msg: string): void {
                if (callback) {
                    callback(false, "", code, msg);
                }
            }
        });
    }

    static setPushMsgListener(listener?: (pushMsg: string) => void) {
        PushAgent.setMsgClickListener((message: PushMsg) => {
            if (listener) {
                listener(JSON.stringify(message))
            }
        })
    }

    static getPushStatus(): string {
        return JSON.stringify(PushAgent.getPushStatus())
    }

    static changePushState(isOpen: boolean, callback?: (success: boolean, code: number, msg: string) => void) {
        PushAgent.changePushState(isOpen, {
            onSuccess: function (): void {
                if (callback) {
                    callback(true, 0, "");
                }
            },
            onFail: function (code: number, msg: string): void {
                if (callback) {
                    callback(false, code, msg);
                }
            }
        })
    }

    static bindUserId(roleId: string, serverId: string, callback?: (success: boolean, code: number, msg: string) => void) {
        PushAgent.bindUserId(OneSDKAPI.getOneSDKUserId(), roleId, serverId, {
            onSuccess: function (): void {
                if (callback) {
                    callback(true, 0, "");
                }
            },
            onFail: function (code: number, msg: string): void {
                if (callback) {
                    callback(false, code, msg);
                }
            }
        })
    }

    static unbindUserId(callback?: (success: boolean, code: number, msg: string) => void) {
        PushAgent.unbindUserId({
            onSuccess: function (): void {
                if (callback) {
                    callback(true, 0, "");
                }
            },
            onFail: function (code: number, msg: string): void {
                if (callback) {
                    callback(false, code, msg);
                }
            }
        })
    }

    static getPushTypeList(callback?: (success: boolean, code: number, msg: string, result: string) => void) {
        PushAgent.getPushTypeList({
            onSuccess: function (result: PushTypeInfo[]): void {
                if (callback) {
                    callback(true, 0, "", JSON.stringify(result))
                }
            },
            onFail: function (code: number, msg: string): void {
                if (callback) {
                    callback(false, code, msg, "");
                }
            }
        })
    }

    static updatePushType(typeList: string, callback?: (success: boolean, code: number, msg: string) => void) {
        let typeArray: PushTypeInfo[] = JSON.parse(typeList) as PushTypeInfo[]
        PushAgent.updatePushTypeList(typeArray, {
            onSuccess: function (): void {
                if (callback) {
                    callback(true, 0, "");
                }
            },
            onFail: function (code: number, msg: string): void {
                if (callback) {
                    callback(false, code, msg);
                }
            }
        })
    }

    static getPushNotDisturbInfo(callback?: (success: boolean, code: number, msg: string, result: string) => void) {
        PushAgent.getPushNotDisturbInfo({
            onSuccess: function (result: PushNotDisturbInfo): void {
                if (callback) {
                    callback(true, 0, "", JSON.stringify(result))
                }
            },
            onFail: function (code: number, msg: string): void {
                if (callback) {
                    callback(false, code, msg, "");
                }
            }
        })
    }

    static updateNotDisturbInfo(info: string, callback?: (success: boolean, code: number, msg: string) => void) {
        try {
            let disturbInfo = JSON.parse(info) as PushNotDisturbInfo;
            PushAgent.updatePushNotDisturbInfo(disturbInfo, {
                onSuccess: function (): void {
                    if (callback) {
                        callback(true, 0, "");
                    }
                },
                onFail: function (code: number, msg: string): void {
                    if (callback) {
                        callback(false, code, msg);
                    }
                }
            })
        } catch (e) {
            console.log("updateNotDisturbInfo failed by exception");
        }

    }

    static killProcess() {
        let context = globalThis["abilityContext"]
        if (context) {
            context.getApplicationContext()?.killAllProcesses()
        }
    }

    static verifyRedeemCode(redeemCode: string, ext: object, callback?: (success: boolean, code: number, msg: string) => void) {
        let extra = new Map<string, string>(Object.entries(ext))
        OneSDKAPI.verifyRedeemCode(redeemCode, OneGlobalSDKBridge.roleInfo["serverId"], OneGlobalSDKBridge.roleInfo["roleId"], OneGlobalSDKBridge.roleInfo["lv"], OneGlobalSDKBridge.roleInfo["vip"],
            extra, {
                onSuccess() {
                    callback?.(true, 0, "")
                }
                , onFail(code: number, msg: string) {
                    callback?.(false, code, msg)
                }
            })
    }

    static getUserRoleInfoList(serverId: string, callback?: (success: boolean, result: string, code: number, msg: string) => void) {
        OneSDKAPI.getUserRoleInfoList({
            onSuccess(infoList: ArrayList<UserRoleInfo>) {
                callback?.(true, JSON.stringify(infoList ?? []), 0, "")
            }
            , onFail(code: number, msg: string) {
                callback?.(false, "[]", code, msg)
            }
        }, serverId)
    }

    static launchPCScanLogin(callback?: (codeType: string, codeLink: string) => void) {
        OneSDKAPI.getQrCodeScanResult({
            onScanResult: function (codeType: string, codeLink: string): void {
                callback?.(codeType, codeLink)
            }
        })
    }

    static setDebug(isDebug: boolean) {
        OneSDKAPI.setDebug(isDebug)
        OneShareAPI.setDebug(isDebug)
    }

    static isDebug(): boolean {
        return OneSDKAPI.isDebug()
    }

    static trackEnterGameScene(scene: string, period: number, hint: Map<string, string>) {
        let params: Map<string, string> = new Map()
        if (hint) {
            params = new Map(Object.entries(hint));
        }
        OneSDKAPI.trackEventEnterGameScene(scene, period, params)
    }

    static trackEventExitGameScene() {
        OneSDKAPI.trackEventExitGameScene()
    }


    static trackEventAddExtraDeviceInfo(extraDevice: Map<string, string>) {
        let params: Map<string, string> = new Map()
        if (extraDevice) {
            params = new Map(Object.entries(extraDevice));
        }
        OneSDKAPI.trackEventAddExtraDeviceInfo(params)
    }

    static startBindPhone(callback?: (success: boolean, code: number, msg: string) => void) {
        OneSDKAPI.startBindPhone({
            onSuccess: function (showCellphone: string): void {
                callback?.(true, 0, showCellphone)
            },
            onFail: function (code: number, message: string): void {
                callback?.(false, code, message)
            },
            onCancel: function (): void {
                callback?.(false, -1, "cancel")
            }
        })
    }

    static examinStatus(): boolean {
        let status = OneSDKAPI.examinStatus()
        if(status == undefined){
            return false
        }
        return status
    }

    static onAbilityCreateOrNewWant(want: Want) {
        PushAgent.onAbilityCreateOrNewWant(want)
        let context = globalThis["abilityContext"] as common.UIAbilityContext
        OneShareAPI.doShareAsResult(want,context)
    }

    //请求权限
    static reqPermissionsFromUser(permissionType: number, callback?: (granted: boolean, type: number) => void) {
        if(permissionType ==1 || permissionType ==2){
            callback?.(true,permissionType)
        }else if (permissionType == 12) {
            let context = globalThis["abilityContext"] as common.UIAbilityContext
            notificationManager.requestEnableNotification(context).then(() => {
                callback(notificationManager.isNotificationEnabledSync(), permissionType)
            }).catch((error) => {
                callback(notificationManager.isNotificationEnabledSync(), permissionType)
            })
        } else {
            let permissions: Array<Permissions> = []
            let permission = OneGlobalSDKBridge.convertType(permissionType)
            if (!permission) {
                callback?.(false, permissionType)
                return
            }
            permissions.push(permission)
            let context = globalThis["abilityContext"] as common.Context
            let atManager: abilityAccessCtrl.AtManager = abilityAccessCtrl.createAtManager();
            atManager.requestPermissionsFromUser(context, permissions).then((data) => {
                callback(data.authResults[0] == 0, permissionType) //0表示成功
            }).catch((err: BusinessError) => {
                console.error(`Failed to request permissions from user. Code is ${err.code}, message is ${err.message}`);
                callback(false, permissionType)
            })
        }
    }

    static checkPermissionsFromUser(permissionType: number): boolean {
        try {
            if(permissionType ==12){
                return notificationManager.isNotificationEnabledSync();
            }
            if(permissionType ==1 || permissionType ==2){
                return true
            }
            let permissions = OneGlobalSDKBridge.convertType(permissionType)
            if(!permissions){
                return false;
            }
            let atManager: abilityAccessCtrl.AtManager = abilityAccessCtrl.createAtManager();
            let grantStatus: abilityAccessCtrl.GrantStatus =
                atManager.checkAccessTokenSync(OneGlobalSDKBridge.GetTokenId(), permissions as Permissions);
            return grantStatus == abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED;
        } catch (e) {
            console.error(`Failed to check permission from user. err is ${e}, permissionString is "${permissionType}"`)
            return false;
        }
    }

    static openPermissionsSetting(permissionType: number, callback?: (granted: boolean, type: number) => void): void {
        let permissions: Array<Permissions> = []
        let context = globalThis["abilityContext"]
        let atManager: abilityAccessCtrl.AtManager = abilityAccessCtrl.createAtManager();
        atManager.requestPermissionOnSetting(context, permissions).then((data: Array<abilityAccessCtrl.GrantStatus>) => {
            callback(data[0] == abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED, permissionType) //0表示成功
        }).catch((err: BusinessError) => {
            console.error(`Failed to open permission setting dialog. Code is ${err.code}, message is ${err.message}`);
            callback(false, permissionType) //0表示成功
        });
    }

    // 跳转到系统设置中的应用权限页面
    static openSystemPermissionSetting() {
        let context = globalThis["abilityContext"]
        context.startAbility({
            bundleName: 'com.huawei.hmos.settings', //设置应用bundleName
            abilityName: 'com.huawei.hmos.settings.MainAbility', //设置应用abilityName
            uri: "application_info_entry", //通知管理页面
            parameters: {
                pushParams: context.abilityInfo.bundleName
            }
        })
    }

    private static GetTokenId(): number {
        // 获取应用程序的accessTokenI
        try {
            let bundleInfo: bundleManager.BundleInfo = bundleManager.getBundleInfoForSelfSync(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION);
            let appInfo: bundleManager.ApplicationInfo = bundleInfo.appInfo;
            return appInfo.accessTokenId;
        } catch (error) {
            const err: BusinessError = error as BusinessError;
            console.error(`Failed to get bundle info for self. Code is ${err.code}, message is ${err.message}`);
        }
        return 0;
    }

    private static convertType(type: number): Permissions|undefined {
        switch (type) {
            case 0: // 	Clipboard = 0,//剪贴板
                return "ohos.permission.READ_PASTEBOARD"
            case 1: // 	ReadExternalStorage,//读取存储卡
                return
            case 2: // 	WriteExternalStorage,//写入存储卡
                return
            case 3: // 	Camera,//相机
                return 'ohos.permission.CAMERA'
            case 4: // 	RecordAudio,//麦克风音频
                return 'ohos.permission.MICROPHONE'
            case 5: // 	CoarseLocation,//网络定位
                return 'ohos.permission.APPROXIMATELY_LOCATION'
            case 6: // 	FineLocation,//GPS定位
                return 'ohos.permission.LOCATION'
            case 7: // 	CallPhone,//打电话
            case 8: // 	ReadContacts,//读取通讯录
                return 'ohos.permission.READ_CONTACTS'
            case 9: // 	ReadSms,//读取短信
            case 10: // ReadCalendar,//读取日历
            case 11: // BodySensors,//传感器
                return
            case 12: // Notification,//通知权限
                return
            case 13: // ATTTrack,//广告追踪权限
                return 'ohos.permission.APP_TRACKING_CONSENT'

        }
        return
    }

    public static share(target: number, shareType: number, shareData: string, callback?: (success: boolean, code: number, msg: string) => void) {
        let context = globalThis["abilityContext"] as common.UIAbilityContext
        let shareCallback: IShareCallback = {
            onShareSucceed: (platform: number): void => {
                callback?.(true,0,"")
            },
            onShareCancelled: (platform: number): void => {
                callback?.(false,-10002,"")
            },
            onShareFailed: (platform: number, msg: string): void => {
                callback?.(false,-2,msg)
            }
        }
        let shareInfo = JSON.parse(shareData) as {title:string, content:string,localImagePath: string}
        if(!shareInfo.content){
            shareInfo.content = shareInfo.title
        }
        OneShareAPI.init(context, {
            success: () => {
                if(target != 4){
                    callback?.(false,-1,"not support this app to share")
                    return
                }
                if(shareType == 0){
                    //图片
                    OneShareAPI.share(ShareAction.newAction(ImageBuilder, ThirdPlatform.SINA, shareCallback)
                        .withImagePath(fileUri.getUriFromPath(shareInfo.localImagePath))
                        .setText(shareInfo.content).build(), context)
                }else if(shareType == 2){
                    //文本
                    OneShareAPI.share(ShareAction.newAction(TextBuilder, ThirdPlatform.SINA, shareCallback)
                        .setText(shareInfo.content).build(), context)
                }else {
                    callback?.(false,-1,"not support this share type")
                }

            },
            fail: function (errCode: number, errMsg: string): any {
                callback?.(false,errCode,errMsg)
            }
        })
        //Image,//图片
        // 	WebPage,//网页
        // 	Text,//文本
        // 	ImageSnapShot,//图片instagram 快拍
        //Obj->SetStringField(TEXT("title"), Data.Title);
        // 	Obj->SetStringField(TEXT("content"), Data.Content);
        // 	Obj->SetStringField(TEXT("localImagePath"), Data.LocalImagePath);
    }

    public static isInstalledApp(target: number):boolean{
        return false;
    }
    
    public static getAppId():string{
        return OneSDKAPI.getAppId()+"";
    }
}