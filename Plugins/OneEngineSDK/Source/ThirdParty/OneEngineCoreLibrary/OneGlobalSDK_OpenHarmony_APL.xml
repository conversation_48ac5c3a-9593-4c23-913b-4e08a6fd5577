﻿<?xml version="1.0" encoding="utf-8"?>
<root>

    <resourceCopies>
        <log text="OneGlobal_APL_OpenHarmony resourceCopies start"/>

        <copyFile src="$S(PluginDir)/OpenHarmony/bridge/OneGlobalSDKBridge.ts"
                  dst="$S(BuildDir)/entry/src/main/ets/ue/bridge/OneGlobalSDKBridge.ts"/>
        <copyFile src="$S(PluginDir)/OpenHarmony/bridge/hvigor-config.json5"
                  dst="$S(BuildDir)/hvigor/hvigor-config.json5"/>
        <copyFile src="$S(PluginDir)/OpenHarmony/bridge/hvigorfile.ts"
                  dst="$S(BuildDir)/entry/hvigorfile.ts"/>


        <copyFile src="$S(PluginDir)/OpenHarmony/bridge/.ohpmrc" dst="$S(BuildDir)/.ohpmrc"/>
        <copyFile src="$S(PluginDir)/OpenHarmony/bridge/.npmrc" dst="$S(BuildDir)/.npmrc"/>
        <copyFile src="$S(PluginDir)/../../../OneEngineSDK.uplugin" dst="$S(BuildDir)/OneEngineSDK.uplugin"/>

        <copyFile src="$S(BuildDir)/../../Config/DefaultGame.ini" dst="$S(BuildDir)/DefaultGame.ini" />
        <!--  YH 有加密功能 Config下的会加密，Config2的不加密，有的话会覆盖加密的  -->
        <copyFile src="$S(BuildDir)/../../Config2/DefaultGame.ini" dst="$S(BuildDir)/DefaultGame.ini" />

        <setStringFromProperty result="ConfigPath" ini="Game" section="/Script/OneEngineEditor.OneEngineSettings" property="HarmonyOSConfigFilePath" default=""/>
        <setString result="PathResult" value="$S(ConfigPath)"/>
        <log text="OneGlobal LaohuConfigPath: $S(PathResult)"/>
        <setBoolContains result="hasDelimiter" source="$S(PathResult)" find="\"/>

        <while condition="hasDelimiter">
            <setIntFindString result="LastIndex" source="$S(PathResult)" find="\"/>
            <setBoolIsEqual result="has" arg1="$I(LastIndex)" arg2="-1"/>
            <log text="OneGlobal LaohuConfigPath:LastIndex $I(LastIndex)"/>
            <if condition = "has">
                <false>
                    <log text="OneGlobal LaohuConfigPath 000 : $S(PathResult)"/>
                    <setIntLength result="resultLength" source="$S(PathResult)"/>
                    <setIntSubtract result="subLength" arg1="$I(resultLength)" arg2="$I(LastIndex)"/>
                    <setIntFindString result="LastIndex" source="$S(PathResult)" find="\"/>
                    <setIntAdd result="LastIndex" arg1="$I(LastIndex)" arg2="1"/>
                    <log text="OneGlobal LaohuConfigPath resultLength $I(resultLength) subLength: $I(subLength)"/>
                    <setStringSubstring result="PathResult" source="$S(PathResult)" start="$I(LastIndex)" length="$I(subLength)"/>
                    <setBoolContains result="hasDelimiter" source="$S(PathResult)" find="\"/>
                    <log text="OneGlobal LaohuConfigPath PathResult 11: $S(PathResult)"/>
                    <if condition="hasDelimiter">
                        <true>
                            <continue/>
                        </true>
                    </if>
                </false>
            </if>
        </while>

        <copyDir src="$S(BuildDir)/../../Content/OneSDKConfig/HarmonyOS" dst="$S(BuildDir)/entry/src/main/resources/rawfile"/>

        <copyFile src="$S(ConfigPath)" dst="$S(BuildDir)/entry/src/main/resources/rawfile/$S(PathResult)"/>



        <log text="OneGlobal_APL_OpenHarmony resourceCopies end"/>
    </resourceCopies>

    <init>
        <log text=" OneGlobal HarmonyOS  APL  init "/>
    </init>

    <entryAbilityImportAdditions>
        <insert>
            import { OneGlobalSDKBridge } from '../ue/bridge/OneGlobalSDKBridge';
        </insert>
    </entryAbilityImportAdditions>

    <entryAbilityOnCreateAdditions>
        <insert>
            OneGlobalSDKBridge.bindMainThreadAki();
            OneGlobalSDKBridge.onAbilityCreateOrNewWant(want);
        </insert>
    </entryAbilityOnCreateAdditions>

    <AkiJSBindImportAdditions>
        <insert>
            import { OneGlobalSDKBridge } from '../ue/bridge/OneGlobalSDKBridge';
        </insert>
    </AkiJSBindImportAdditions>
    <AkiJSBindUeWorkerAdditions>
        <insert>
            OneGlobalSDKBridge.bindWorkerThreadAki();
        </insert>
    </AkiJSBindUeWorkerAdditions>

    <addDependenciesInEntryOHPackage>
        <insertNewline/>
        <insertPair key="@wpsdk/onesdk"  value ="4.268.0"/>
        <insertNewline/>
        <insertPair key="@wpsdk/push"  value ="1.0.1"/>
        <insertNewline/>
        <insertPair key="@wpsdk/share"  value ="1.6.2"/>
    </addDependenciesInEntryOHPackage>

    <entryAbilityClassAdditions>
        <insert>

        </insert>
    </entryAbilityClassAdditions>

    <entryAbilityExternalClassAdditions>
        <insert>
        </insert>
    </entryAbilityExternalClassAdditions>
    <entryAbilityonForegroundAdditions>
        <insert>
            OneGlobalSDKBridge.onForeground()
        </insert>
    </entryAbilityonForegroundAdditions>

    <entryAbilityonBackgroundAdditions>
        <insert>
            OneGlobalSDKBridge.onBackground()
        </insert>
    </entryAbilityonBackgroundAdditions>

    <entryAbilityClassAdditions>
        <insert>
            onNewWant(want: Want, launchParam: AbilityConstant.LaunchParam): void {
            OneGlobalSDKBridge.onAbilityCreateOrNewWant(want);
            }
        </insert>
    </entryAbilityClassAdditions>



    <indexImportAdditions>
        <insert>
            import { OneGlobalSDKBridge } from '../ue/bridge/OneGlobalSDKBridge';
            import { OneSDKInit } from '@wpsdk/onesdk';
            import { OneShareInit } from '@wpsdk/share';
        </insert>
    </indexImportAdditions>
    <indexBuildStackAdditions>
        <insert>
            OneSDKInit()
            OneShareInit()
        </insert>
    </indexBuildStackAdditions>
    <indexonBackPressAdditions>
        <insert>
            OneGlobalSDKBridge.openExitGameConfirmDialog()
        </insert>
    </indexonBackPressAdditions>

</root>
