﻿<?xml version="1.0" encoding="utf-8"?>
<root >
  <prebuildCopies>
        <log text="Copying onesdk_library directory to staging before build $S(EngineDir)"/>
    <!--    <copyDir src="$S(PluginDir)/Android/one_global_library" dst="$S(BuildDir)/JavaLibs/one_global_library" />-->
    <copyFile src="$S(BuildDir)/../../../Config/DefaultGame.ini" dst="$S(BuildDir)/gradle/DefaultGame.ini" />
    <!--  YH 有加密功能 Config下的会加密，Config2的不加密，有的话会覆盖加密的  -->
    <copyFile src="$S(BuildDir)/../../../Config2/DefaultGame.ini" dst="$S(BuildDir)/gradle/DefaultGame.ini" />
    <copyFile src="$S(PluginDir)/../../../OneEngineSDK.uplugin" dst="$S(BuildDir)/gradle/app/OneEngineSDK.uplugin"/>
  </prebuildCopies>

  <init>
  </init>

  <baseBuildGradleAdditions>
    <insert>
      allprojects {
      repositories {
      maven {
      url 'http://nexus.sys.wanmei.com/repository/maven-public/'
      allowInsecureProtocol true
      }
      }
      }
    </insert>
  </baseBuildGradleAdditions>

  <buildscriptGradleAdditions>
    <insert>
      repositories {
        maven {
        url 'http://nexus.sys.wanmei.com/repository/maven-public/'
        allowInsecureProtocol true
        }
      }

      dependencies {
        classpath 'com.wpsdk.onesdk:engine-plugin:1.3.11'
      }
    </insert>
  </buildscriptGradleAdditions>


  <buildGradleAdditions>
    <insert>
      apply plugin: 'com.wpsdk.onesdk.engine-pack'
      
      configurations.all {
       resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
      }
      
      //默认是SNAPSHOT 版本，sdkPackage 脚本在归档时去掉SNAPSHOT
      dependencies {
        api ("com.wpsdk.ue:one-global-bridge:${getPluginVersion()}-SNAPSHOT")
        api 'com.one.networksdk:wpsdk-onenetsdk:*******'
      }
      
    </insert>
  </buildGradleAdditions>
  <buildGradleAdditions>
    <insert>
      allprojects {
      def classMap = [
      'android.support.annotation': 'androidx.annotation',
      'android.support.v4.app.ActivityCompat': 'androidx.core.app.ActivityCompat',
      'android.support.v4.app.NotificationCompat': 'androidx.core.app.NotificationCompat',
      'android.support.v4.app.NotificationManagerCompat': 'androidx.core.app.NotificationManagerCompat',
      'android.support.v4.content.ContextCompat': 'androidx.core.content.ContextCompat',
      'android.support.v4.content.FileProvider': 'androidx.core.content.FileProvider',
      'android.support.v13.app.FragmentCompat': 'androidx.legacy.app.FragmentCompat',
      'android.arch.lifecycle': 'androidx.lifecycle',
      'android.arch.lifecycle.Lifecycle': 'androidx.lifecycle.Lifecycle',
      'android.arch.lifecycle.LifecycleObserver': 'androidx.lifecycle.LifecycleObserver',
      'android.arch.lifecycle.OnLifecycleEvent': 'androidx.lifecycle.OnLifecycleEvent',
      'android.arch.lifecycle.ProcessLifecycleOwner': 'androidx.lifecycle.ProcessLifecycleOwner',
      ]

      afterEvaluate { project ->
      project.rootProject.projectDir.traverse(type: groovy.io.FileType.FILES, nameFilter: ~/.*\.java$/) { f ->
      classMap.each { entry ->
      if (f.getText('UTF-8').contains(entry.key)) {
      println "Change ${entry.key} to ${entry.value} in file ${f}"
      ant.replace(file: f, token: entry.key, value: entry.value)
      }
      }
      }
      }
      }
    </insert>
  </buildGradleAdditions>
  <gameApplicationImportAdditions>
    <insert>
      import android.content.Context;
      import com.wpsdk.one.global.OneGlobalSDK;
    </insert>
  </gameApplicationImportAdditions>
  <gameActivityImportAdditions>
    <insert>
      import com.epicgames.ue.OneSDKNativeActivity;
    </insert>
  </gameActivityImportAdditions>
  <gameActivitySuperClass>
    <insert>OneSDKNativeActivity</insert>
  </gameActivitySuperClass>
  <gameApplicationOnCreateAdditions>
    <insert>
      OneGlobalSDK.getDefault().onApplicationOnCreate(this);
    </insert>
  </gameApplicationOnCreateAdditions>


  <gameActivityImportAdditions>
    <insert>
      import com.wpsdk.one.global.OneGlobalSDK;
      import com.wpsdk.one.global.bridge.OneGlobalBridge;
    </insert>
  </gameActivityImportAdditions>

  <gameActivityOnCreateAdditions>
    <setBoolNot result="notDistribution" source="$B(Distribution)"/>
    <log text="UE Game distribution  : $B(Distribution)"/>
    <if condition="notDistribution">
      <true>
        <insert>
          OneGlobalBridge.getDefault().setDebug(true);
        </insert>
      </true>
    </if>
  </gameActivityOnCreateAdditions>
  
  
  <proguardAdditions>
    <insert>
      -dontwarn com.onevcat.uniwebview.**
      -dontwarn com.tencent.**
      -dontwarn com.wpsdk.android.common.**
      -dontwarn kotlinx.**
      -dontwarn kotlin.**
      -dontwarn org.codehaus.**
      -dontwarn  org.conscrypt.**
      -dontwarn org.jetbrains.**
      -dontwarn com.google.gson.**
      -dontwarn com.huawei.**
      -dontwarn com.wpsdk.global.**
      -dontwarn com.wpsdk.gateway.core.**
      -dontwarn okhttp3.**
      -keep class com.wpsdk.dfga.sdk.**{*;}
      -keep class pwc.ptc.PwcPtcCfg{*;}
      -keep class com.epicgames.unreal.GameActivity$* { *; }
      -keep class com.epicgames.unreal.** { *; }
      -keep class com.epicgames.ue4.** { *; }
      -keepattributes Signature
      -keepattributes RuntimeVisibleAnnotations,AnnotationDefault

      -if class com.google.gson.reflect.TypeToken
      -keep,allowobfuscation class com.google.gson.reflect.TypeToken
      -keep,allowobfuscation class * extends com.google.gson.reflect.TypeToken
      -keep,allowobfuscation,allowoptimization @com.google.gson.annotations.JsonAdapter class *

      -keep class com.one.networksdk.**{*;}
      -keep class com.wpsdkwpsdk.**{*;}
      -keep class com.wpwpsdk.**{*;}
      -keep class com.wpsdk.**{*;}
      -keep class com.wpsdk.gateway.core.bean.**{*;}
      -keep class ru.rustore.sdk.**{*;}
      -keep class com.samsung.android.sdk.**{*;}
      -keep class com.wpsdk.global.**{*;}
      -keep class com.hihonor.iap.sdk.**{*;}
      -keep class com.huawei.hms.iap.**{*;}
      -keep class com.taptap.**{*;}
      -keep class com.xsolla.android.**{*;}
      -keep class com.flexionmobile.sdk.**{*;}
    </insert>
  </proguardAdditions>
</root>
