// Fill out your copyright notice in the Description page of Project Settings.

using System.IO;
using UnrealBuildTool;
#if UE_5_0_OR_LATER
using EpicGames.Core;
#else
using Tools.DotNETCommon;
#endif

public class OneEngineCoreLibrary : ModuleRules
{
	public OneEngineCoreLibrary(ReadOnlyTargetRules Target) : base(Target)
	{
		Type = ModuleType.External;

		if (Target.Platform == UnrealTargetPlatform.Win64)
		{

        }
        else if (Target.Platform == UnrealTargetPlatform.Mac)
        {
        	//增加是否安装oneEngineSDK macOS Libraries判断 
        	string MacNativeSDKPath = Path.Combine(PluginDirectory, "../MacNativeSDK");
            if(Directory.Exists(MacNativeSDKPath))
            {
	            PublicIncludePaths.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WPOneEngineBridge.framework/Headers"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WPOneEngineBridge.framework"));
	            PublicDefinitions.Add("INSTALL_ONE_ENGINE_MAC_LIBRARY=1");

	        }
        }
        else if (Target.Platform == UnrealTargetPlatform.Linux)
		{
			
		}
        else if (Target.Platform == UnrealTargetPlatform.Android)
        {

	        string PluginPath = Utils.MakePathRelativeTo(ModuleDirectory, Target.RelativeEnginePath);
	        AdditionalPropertiesForReceipt.Add("AndroidPlugin", Path.Combine(PluginPath, "OneGlobalSDK_UPL.xml"));
	        
			#if UE_5_0_OR_LATER
							
			#else
				        AdditionalPropertiesForReceipt.Add("AndroidPlugin", Path.Combine(PluginPath, "Android_Patch_UPL.xml"));
			#endif
        }
		else if (Target.Platform.ToString() == "OpenHarmony")
		{
			AdditionalPropertiesForReceipt.Add("OpenHarmonyPlugin", Path.Combine(ModuleDirectory, "OneGlobalSDK_OpenHarmony_APL.xml"));
		}
        else if (Target.Platform == UnrealTargetPlatform.IOS)
        {
            // Framework=Start
			PublicAdditionalFrameworks.Add(new Framework("WPOneEngineBridge", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WPOneEngineBridge.embeddedframework.zip")));
			// Framework=End
			
			// 遍历UPL目录下的配置文件
			string SDKConfigDirPath = Path.Combine(PluginDirectory, "../iOSNativeSDK/Additional");
			if(Directory.Exists(SDKConfigDirPath))
			{
				var LocaleFolders = Directory.GetFileSystemEntries(SDKConfigDirPath, "*");
				foreach (var SubFilePath in LocaleFolders)
				{
					// 判断FolderName是否已.xml为后缀
					if (Path.GetExtension(SubFilePath) == ".xml")
					{
						// 添加到Receipt中
						AdditionalPropertiesForReceipt.Add("IOSPlugin", SubFilePath);
					}
				}
			}

			
            PublicFrameworks.AddRange(
            new string[]{
                "CoreFoundation",
            }
            );
            
            PublicWeakFrameworks.AddRange(
            new string[]{
                "Foundation", "UIKit"
            }
            );
            
            PublicSystemLibraries.AddRange(
            new string[]{
                "curses", "iconv", "resolv", "z", "sqlite3"
            }
            );
        }
	}
}
