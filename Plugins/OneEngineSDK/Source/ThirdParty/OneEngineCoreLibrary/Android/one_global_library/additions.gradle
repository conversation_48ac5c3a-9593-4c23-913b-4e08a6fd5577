
apply plugin: 'com.android.library'
apply plugin: 'com.wpsdk.onesdk.engine-pack'
android {
    compileSdkVersion COMPILE_SDK_VERSION.toInteger()
    defaultConfig {
        minSdkVersion MIN_SDK_VERSION.toInteger()
        targetSdkVersion TARGET_SDK_VERSION.toInteger()
        versionCode STORE_VERSION.toInteger()
        versionName VERSION_DISPLAY_NAME
    }
    sourceSets.main {
        assets.srcDir "assets"
        jniLibs.srcDir "libs"
    }
}
dependencies {
    api fileTree(dir: 'libs', include: ['*.jar', "*.aar"])
}
configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}
String SnapshotValue = System.getenv("IsSnapshot")
String bridge_version =getPluginVersion()
if (SnapshotValue != null) {
    boolean isSnap = Boolean.valueOf(SnapshotValue)
    if (isSnap) {
        dependencies {
           api ("com.wpsdk.ue:one-global-bridge:${bridge_version}-SNAPSHOT") {changing = true }
        }
    } else {
        dependencies {
            api 'com.wpsdk.ue:one-global-bridge:' + bridge_version
        }
    }
} else {
    dependencies {
        api ("com.wpsdk.ue:one-global-bridge:${bridge_version}-SNAPSHOT") {changing = true }
    }
}

def getPluginVersion() {
    try {
        File file = file("./../app/OneEngineSDK.uplugin")
        println("plugin path :" + file.absolutePath)
        groovy.json.JsonSlurper slurper = new groovy.json.JsonSlurper()
        def obj = slurper.parseText(file.text)
        return obj.VersionName
    } catch (Exception e) {
        return ""
    }
}
