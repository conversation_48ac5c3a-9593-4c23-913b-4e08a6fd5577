﻿<?xml version="1.0" encoding="utf-8"?>
<root >
    <gradleProperties>
        <setStringFromProperty result="SDKRegion" ini="Game" section="/Script/OneEngineEditor.OneEngineSettings" property="SDKRegion"/>
        <setBoolIsEqual result="bMainland" arg1="$S(SDKRegion)" arg2="Mainland"/>
        <log text="get SDKRegion = $S(SDKRegion)"/>
        <if condition="bMainland">
            <true>
                <insert>
                    ANDROID_TOOLS_BUILD_GRADLE_VERSION=com.android.tools.build:gradle:4.0.2
                </insert>
            </true>
            <false>
                <insert>
                    ANDROID_TOOLS_BUILD_GRADLE_VERSION=com.android.tools.build:gradle:7.4.2
                </insert>
            </false>
        </if>
    </gradleProperties>
</root>