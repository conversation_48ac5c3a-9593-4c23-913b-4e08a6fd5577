<?xml version="1.0" encoding="utf-8"?>
<root>
    <init>
        <setStringFromProperty result="sWeixin" ini="Game" section="/Script/OneEngineSDK" property="Weixin" default=""/>
        <log text="$S(sWeixin)"/>
        <setStringFromProperty result="sWeibo" ini="Game" section="/Script/OneEngineSDK" property="Weibo" default=""/>
        <log text="$S(sWeibo)"/>
        <setStringFromProperty result="sQQ" ini="Game" section="/Script/OneEngineSDK" property="QQ" default=""/>
        <log text="$S(sQQ)"/>
        <setStringFromProperty result="sBilibili" ini="Game" section="/Script/OneEngineSDK" property="BilibiliAppId" default=""/>
        <log text="$S(sBilibili)"/>
        <setStringFromProperty result="sTapTap" ini="Game" section="/Script/OneEngineSDK" property="TapTap" default=""/>
        <log text="$S(sTapTap)"/>
        <setStringFromProperty result="sWPAppID" ini="Game" section="/Script/OneEngineSDK" property="Wanmei" default=""/>
        <setStringFromProperty result="sWPAppID1" ini="Game" section="/Script/OneEngineSDK" property="Wanmei1" default=""/>
        <log text="OneSDK adding requirement and permission to plist..."/>
    </init>

    <iosPListUpdates>
        <loopElements tag="$">
            <setStringFromTag result="CurrentTag" tag="$"/>
            <setBoolIsEqual   result="isRootDictionary" arg1="$S(CurrentTag)" arg2="dict"/>
            
            <if condition="isRootDictionary">
                <true>
<!--  =============================== 处理CFBundleURLTypes =============================== -->
                    <!--  查找CFBundleURLTypes，判断是否存在  -->
                    <setBool result="bCFBundleURLTypesFound" value="false"/>
                    <setBool result="bCFBundleURLTypesSearchNextElement" value="false"/>
                    
                    <loopElements tag="$">
                        <setStringFromTagText result="TagIteratorValue" tag="$"/>
                        <setBoolIsEqual result="isCFBundleURLTypes" arg1="$S(TagIteratorValue)" arg2="CFBundleURLTypes"/>
                        
                        <if condition="isCFBundleURLTypes">
                          <true>
                            <setBool result="bCFBundleURLTypesFound" value="true"/>
                          </true>
                        </if>
                    </loopElements>
                    
                    <!-- 如果没有CFBundleURLTypes，则创建 -->
                     <if condition="bCFBundleURLTypesFound">
                       <false>
                         <addElements tag="$" once="true">
                             <key>CFBundleURLTypes</key>
                             <array>
                             </array>
                         </addElements>
                       </false>
                     </if>
                      
                    <!-- 添加三方平台urlscheme  -->
                    <loopElements tag="$">
                        <setStringFromTagText result="TagIteratorValue" tag="$"/>
                        <setBoolIsEqual result="isCFBundleURLTypes" arg1="$S(TagIteratorValue)" arg2="CFBundleURLTypes"/>
                        
                        <if condition="bCFBundleURLTypesSearchNextElement">
                            <true>
                                <setBool result="bCFBundleURLTypesSearchNextElement" value="false"/>
                                <!-- 添加wechat urlscheme-->
                                <setBoolIsEqual result="isSetURLTypeWechatNull" arg1="$S(sWeixin)" arg2=""/>

                                <if condition="isSetURLTypeWechatNull">
                                  <false>
                                      <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                      <!--  添加CFBundleTypeRole、CFBundleURLName  -->
                                      <addElements tag="$dCFBundleURLTypesDict">
                                          <key>CFBundleTypeRole</key>
                                          <string>Editor</string>
                                          <key>CFBundleURLName</key>
                                          <string>weixin</string>
                                      </addElements>
                                      
                                      <!--  添加 CFBundleURLSchemes  -->
                                      <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                      <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>

                                      <setElement result="dCFBundleURLSchemesRootArray" value="array"/>

                                      <setElement result="dCFBundleURLSchemesAppId" value="string" text="$S(sWeixin)"/>
                                      <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId"/>

                                      <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>

                                      <addElement tag="$" name="dCFBundleURLTypesDict"/>
                                  </false>
                                </if>

                                <!--  添加Weibo urlscheme  -->
                                <setBoolIsEqual result="isSetURLTypesWeiboNull" arg1="$S(sWeibo)" arg2=""/>

                                <if condition="isSetURLTypesWeiboNull">
                                  <false>
                                      <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                      <!-- 添加CFBundleTypeRole、CFBundleURLName  -->
                                      <addElements tag="$dCFBundleURLTypesDict">
                                          <key>CFBundleTypeRole</key>
                                          <string>Editor</string>
                                          <key>CFBundleURLName</key>
                                          <string>weibo</string>
                                      </addElements>
                                      
                                      <!--  添加 CFBundleURLSchemes  -->
                                      <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                      <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>

                                      <setElement result="dCFBundleURLSchemesRootArray" value="array"/>

                                      <setElement result="dCFBundleURLSchemesAppId" value="string" text="$S(sWeibo)"/>
                                      <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId"/>

                                      <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>

                                      <addElement tag="$" name="dCFBundleURLTypesDict"/>
                                  </false>
                                </if>
                                
                                <!--  添加QQ urlscheme  -->
                                <setBoolIsEqual result="isSetURLTypesQQNull" arg1="$S(sQQ)" arg2=""/>

                                <if condition="isSetURLTypesQQNull">
                                  <false>
                                      <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                      <!--  添加CFBundleTypeRole、CFBundleURLName  -->
                                      <addElements tag="$dCFBundleURLTypesDict">
                                          <key>CFBundleTypeRole</key>
                                          <string>Editor</string>
                                          <key>CFBundleURLName</key>
                                          <string>qq</string>
                                      </addElements>
                                      
                                      <!--  添加 CFBundleURLSchemes  -->
                                      <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                      <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>

                                      <setElement result="dCFBundleURLSchemesRootArray" value="array"/>

                                      <setElement result="dCFBundleURLSchemesAppId" value="string" text="$S(sQQ)"/>
                                      <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId"/>

                                      <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>

                                      <addElement tag="$" name="dCFBundleURLTypesDict"/>
                                  </false>
                                </if>

                                <!-- 添加BiliBili urlscheme-->
                                <setBoolIsEqual result="isSetURLTypeBilibiliNull" arg1="$S(sBilibili)" arg2=""/>

                                <if condition="isSetURLTypeBilibiliNull">
                                  <false>
                                      <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                      <!--  添加CFBundleTypeRole、CFBundleURLName  -->
                                      <addElements tag="$dCFBundleURLTypesDict">
                                          <key>CFBundleTypeRole</key>
                                          <string>Editor</string>
                                          <key>CFBundleURLName</key>
                                          <string>bilibili</string>
                                      </addElements>
                                      
                                      <!--  添加 CFBundleURLSchemes  -->
                                      <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                      <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>

                                      <setElement result="dCFBundleURLSchemesRootArray" value="array"/>

                                      <setElement result="dCFBundleURLSchemesAppId" value="string" text="$S(sBilibili)"/>
                                      <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId"/>

                                      <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>

                                      <addElement tag="$" name="dCFBundleURLTypesDict"/>
                                  </false>
                                </if>
                                
                                <!--  添加TapTap urlscheme  -->
                               <setBoolIsEqual result="isSetURLTypesTapTapNull" arg1="$S(sTapTap)" arg2=""/>

                               <if condition="isSetURLTypesTapTapNull">
                                 <false>
                                     <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                     <!-- 添加CFBundleTypeRole、CFBundleURLName  -->
                                     <addElements tag="$dCFBundleURLTypesDict">
                                         <key>CFBundleTypeRole</key>
                                         <string>Editor</string>
                                         <key>CFBundleURLName</key>
                                         <string>taptap</string>
                                     </addElements>
                                     
                                     <!--  添加 CFBundleURLSchemes  -->
                                     <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                     <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>

                                     <setElement result="dCFBundleURLSchemesRootArray" value="array"/>

                                     <setElement result="dCFBundleURLSchemesAppId" value="string" text="$S(sTapTap)"/>
                                     <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId"/>

                                     <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>

                                     <addElement tag="$" name="dCFBundleURLTypesDict"/>
                                 </false>
                               </if>

                                <!--  添加wp urlscheme(扩展使用)  -->
                                <setBoolIsEqual result="isSetURLTypesWPNull" arg1="$S(sWPAppID)" arg2=""/>

                                <if condition="isSetURLTypesWPNull">
                                    <false>
                                        <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                        <!-- 添加CFBundleTypeRole、CFBundleURLName  -->
                                        <addElements tag="$dCFBundleURLTypesDict">
                                            <key>CFBundleTypeRole</key>
                                            <string>Editor</string>
                                            <key>CFBundleURLName</key>
                                            <string>wp</string>
                                        </addElements>

                                        <!--  添加 CFBundleURLSchemes  -->
                                        <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>
                                        <setElement result="dCFBundleURLSchemesRootArray" value="array"/>

                                        <setElement result="dCFBundleURLSchemesAppId" value="string" text="$S(sWPAppID)"/>
                                        <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId"/>

                                        <!--  添加wanmei1  -->
                                        <setBoolIsEqual result="isSetURLTypesWanme1" arg1="$S(sWPAppID1)" arg2=""/>
                                        <if condition="isSetURLTypesWanme1">
                                            <false>
                                                <setElement result="dCFBundleURLSchemesAppId2" value="string" text="$S(sWPAppID1)"/>
                                                <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId2"/>
                                            </false>
                                        </if>
                                        
                                        <!--  添加结束符 CFBundleURLSchemes  -->
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>
                                        <addElement tag="$" name="dCFBundleURLTypesDict"/>
                                    </false>
                                </if>
                            </true>
                        </if>
                        <if condition="isCFBundleURLTypes">
                            <true>
                                <setBool result="bCFBundleURLTypesSearchNextElement" value="true"/>
                            </true>
                        </if>
                    </loopElements>
<!--  =============================== 处理LSApplicationQueriesSchemes =============================== -->
                    <!--  判断LSApplicationQueriesSchemes是否存在 -->
                    <setBool result="bApplicationQueriesSchemesFound" value="false"/>
                    <setBool result="bApplicationQueriesSchemesNextElement" value="false"/>
                    <loopElements tag="$">
                        <setStringFromTagText result="TagIteratorValue" tag="$"/>
                        <setBoolIsEqual result="isApplicationQueriesSchemes" arg1="$S(TagIteratorValue)" arg2="LSApplicationQueriesSchemes"/>
                        <if condition="isApplicationQueriesSchemes">
                          <true>
                            <setBool result="bApplicationQueriesSchemesFound" value="true"/>
                          </true>
                        </if>
                    </loopElements>
                    
                    <!-- 如果没有LSApplicationQueriesSchemes，则创建 -->
                     <if condition="bApplicationQueriesSchemesFound">
                       <false>
                         <addElements tag="$" once="true">
                             <key>LSApplicationQueriesSchemes</key>
                             <array>
                             </array>
                         </addElements>
                       </false>
                     </if>
                     <!-- 添加三方平台LSApplicationQueriesSchemes-->
                     <loopElements tag="$">
                         <setStringFromTagText result="TagIteratorValue" tag="$"/>
                         <setBoolIsEqual result="isApplicationQueriesSchemes" arg1="$S(TagIteratorValue)" arg2="LSApplicationQueriesSchemes"/>
                         <if condition="bApplicationQueriesSchemesNextElement">
                             <true>
                                 <setBool result="bApplicationQueriesSchemesNextElement" value="false"/>
                                 <addElements tag="$">
                                     <string>weixin</string>
                                     <string>wechat</string>
                                     <string>weixinULAPI</string>
                                     <string>weixinURLParamsAPI</string>
                                     <string>sinaweibo</string>
                                     <string>sinaweibohd</string>
                                     <string>weibosdk</string>
                                     <string>weibosdk2.5</string>
                                     <string>weibosdk3.3</string>
                                     <string>mqq</string>
                                     <string>tim</string>
                                     <string>mqqapi</string>
                                     <string>mqqwpa</string>
                                     <string>mqqOpensdkSSoLogin</string>
                                     <string>mqqopensdkapiV2</string>
                                     <string>mqqopensdkapiV3</string>
                                     <string>mqqopensdkapiV4</string>
                                     <string>wtloginmqq2</string>
                                     <string>wtloginmqq</string>
                                     <string>mqzone</string>
                                     <string>mqzoneopensdk</string>
                                     <string>mqzoneopensdkapi</string>
                                     <string>mqzoneopensdkapi19</string>
                                     <string>mqzoneopensdkapiV2</string>
                                     <string>mqqopensdknopasteboardios16</string>
                                     <string>mqqopensdknopasteboard</string>
                                     <string>bilibili</string>
                                     <string>bilibili.following</string>
                                     <string>tapiosdk</string>
                                     <string>tapsdk</string>
                                 </addElements>
                             </true>
                         </if>
                         <if condition="isApplicationQueriesSchemes">
                             <true>
                                 <setBool result="bApplicationQueriesSchemesNextElement" value="true"/>
                             </true>
                         </if>
                     </loopElements>
                     
<!--  =============================== 处理NSAppTransportSecurity ===============================  -->
                    <!--  查找NSAppTransportSecurity，判断是否存在  -->
                    <setBool result="bAllowsArbitraryLoadsFound" value="false"/>
                    <setBool result="bNSExceptionDomainsFound" value="false"/>
                 
                    <loopElements tag="$">
                        <setStringFromTagText result="TagIteratorValue" tag="$"/>
                        <setBoolIsEqual result="isAllowsArbitraryLoads" arg1="$S(TagIteratorValue)" arg2="NSAllowsArbitraryLoads"/>
                        <setBoolIsEqual result="isExceptionDomains" arg1="$S(TagIteratorValue)" arg2="NSExceptionDomains"/>
                        <if condition="isAllowsArbitraryLoads">
                          <true>
                              <setBool result="bAllowsArbitraryLoadsFound" value="true"/>
                          </true>
                        </if>
                        
                        <if condition="isExceptionDomains">
                          <true>
                              <setBool result="bNSExceptionDomainsFound" value="true"/>
                          </true>
                        </if>
                    </loopElements>
                    
                    <!-- 判断是否已经设置了AllowsArbitraryLoads -->
                    <!-- 没找到AllowsArbitraryLoads 或者找到了AllowsArbitraryLoads，但没找到NSExceptionDomains，都算AllowsArbitraryLoads=true，不处理-->
                    <setBool result="bNeedAddAppTransportSecurity" value="false"/>
                    <setBool result="bNeedAppendExceptionDomains" value="false"/>
                    <if condition="bAllowsArbitraryLoadsFound">
                      <true>
                          <if condition="bNSExceptionDomainsFound">
                              <true>
                                  <!-- 找到了AllowsArbitraryLoads，也找到NSExceptionDomains， 按AllowsArbitraryLoads=false处理，需添加ExceptionDomains处理-->
                                  <setBool result="bNeedAppendExceptionDomains" value="true"/>
                              </true>
                              <false>
                              <!--  找到了AllowsArbitraryLoads，但没找到NSExceptionDomains， 按已添加了AllowsArbitraryLoads=true处理，不需要单独添加ExceptionDomains-->
                              </false>
                          </if>
                      </true>
                      <false>
                          <if condition="bNSExceptionDomainsFound">
                              <true>
                                  <!--  没找到AllowsArbitraryLoads，但找到NSExceptionDomains， 按需添加ExceptionDomains处理-->
                                  <setBool result="bNeedAppendExceptionDomains" value="true"/>
                              </true>
                              <false>
                                  <!--  没找到AllowsArbitraryLoads，也没找到NSExceptionDomains， 按需添加NSAppTransportSecurity处理-->
                                  <setBool result="bNeedAddAppTransportSecurity" value="true"/>
                              </false>
                          </if>
                      </false>
                    </if>
                    <!-- 判断是否需要添加NSAppTransportSecurity -->
                    <if condition="bNeedAddAppTransportSecurity">
                        <true>
                            <addElements tag="$">
                                <key>NSAppTransportSecurity</key>
                                <dict>
                                    <key>NSExceptionDomains</key>
                                    <dict>
                                        <key>cmpassport.com</key>
                                        <dict>
                                            <key>NSExceptionMinimumTLSVersion</key>
                                            <string>TLSv1.0</string>
                                            <key>NSIncludesSubdomains</key>
                                            <true/>
                                            <key>NSThirdPartyExceptionAllowsInsecureHTTPLoads</key>
                                            <true/>
                                            <key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
                                            <false/>
                                        </dict>
                                    </dict>
                                </dict>
                            </addElements>
                        </true>
                    </if>
                    
                    <!-- 判断是否需要在已有的NSExceptionDomains key中追加 -->
                    <if condition="bNeedAppendExceptionDomains">
                        <!-- 已有NSExceptionDomains，需要找到该key，并追加  -->
                        <true>
                            <setBool result="bExceptionDomainsSearchNextElement" value="false"/>
                            <loopElements tag="$">
                                <setStringFromTagText result="TagIteratorValue" tag="$"/>
                                <setBoolIsEqual result="isExceptionDomains" arg1="$S(TagIteratorValue)" arg2="NSExceptionDomains"/>
                                
                                <if condition="bExceptionDomainsSearchNextElement">
                                    <true>
                                        <setBool result="bExceptionDomainsSearchNextElement" value="false"/>
                                        <addElements tag="$">
                                            <key>cmpassport.com</key>
                                            <dict>
                                                <key>NSExceptionMinimumTLSVersion</key>
                                                <string>TLSv1.0</string>
                                                <key>NSIncludesSubdomains</key>
                                                <true/>
                                                <key>NSThirdPartyExceptionAllowsInsecureHTTPLoads</key>
                                                <true/>
                                                <key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
                                                <false/>
                                            </dict>
                                        </addElements>
                                    </true>
                                </if>
                                <if condition="isExceptionDomains">
                                    <true>
                                        <setBool result="bExceptionDomainsSearchNextElement" value="true"/>
                                    </true>
                                </if>
                            </loopElements>
                        </true>
                    </if>
                </true>
            </if>
        </loopElements>
    </iosPListUpdates>
</root>
