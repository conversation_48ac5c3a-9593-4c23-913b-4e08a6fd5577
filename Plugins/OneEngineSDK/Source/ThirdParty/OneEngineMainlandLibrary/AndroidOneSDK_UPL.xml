<?xml version="1.0" encoding="utf-8"?>
<root xmlns:android="http://schemas.android.com/apk/res/android">
  <init>
    <log text="Android Permission Plugin Init"/>
    <!-- check if using Gradle -->
    <setBoolFromProperty result="bGradle" ini="Engine" section="/Script/AndroidRuntimeSettings.AndroidRuntimeSettings" property="bEnableGradle" default="false"/>
  </init>
  
  <resourceCopies>
    <!--  老虎换肤功能   -->
    <copyDir src="$S(BuildDir)/../../../Content/Skins/Android" dst="$S(BuildDir)/assets/skins" />
  </resourceCopies>
  
  <prebuildCopies>
    <log text="Copying onesdk_library directory to staging before build"/>
    <!--    <copyDir src="$S(PluginDir)/Android/onesdk_library" dst="$S(BuildDir)/JavaLibs/onesdk_library" />-->
  
  </prebuildCopies>

  <gradleProperties>
    <insert>

    </insert>
  </gradleProperties>

  <buildGradleAdditions>
    <insert>
    </insert>
  </buildGradleAdditions>
  <baseBuildGradleAdditions>
    <insert>
      allprojects {
        repositories {
          maven {
            url "http://nexus.sys.wanmei.com/repository/maven-public/"
            allowInsecureProtocol true
          }
        }
      }
    </insert>
  </baseBuildGradleAdditions>
  <buildGradleAdditions>
    <insert>
      dependencies {
        api 'com.wpsdk.onesdk:onesdk-native:5.127.0'
        api 'com.pwrd.oneshare:pwrd-onesharesdk:1.7.2'
        api 'com.wpsdk.push:pusdsdk:3.10.0'
      }
    </insert>
  </buildGradleAdditions>


  <androidManifestUpdates>

    <!-- 修改游戏的启动activity为LaunchActivity-->
    <loopElements tag="activity">
      <setStringFromAttribute result="activityName" tag="$" name="android:name"/>
      <setBoolIsEqual result="bLauncher" arg1="$S(activityName)" arg2="com.epicgames.ue4.SplashActivity"/>
      <if condition="bLauncher">
        <true>
          <removeElement tag="$"/>
        </true>
      </if>
      <setBoolIsEqual result="bUE5Launcher" arg1="$S(activityName)" arg2="com.epicgames.unreal.SplashActivity"/>
      <if condition="bUE5Launcher">
        <true>
          <removeElement tag="$"/>
        </true>
      </if>
    </loopElements>


    <setBoolFromProperty result="ShowSplashBackGroundImage" ini="Game" section="/Script/OneEngineEditor.OneEngineSettings" property="bShowSplashBackGroundImage" default="false"/>
    <setIntFromProperty  result="SplashShowTime" ini="Game" section="/Script/OneEngineEditor.OneEngineSettings" property="SplashShowTime" default="500"/>
    <log text="ShowSplashBackGroundImage $B(ShowSplashBackGroundImage)"/>
    <if condition="ShowSplashBackGroundImage">
      <true>
        <addElements  tag="application">
          <meta-data android:name="com.epicgames.ue.LaunchActivity.showTime" />
          <meta-data android:name="com.epicgames.ue.LaunchActivity.bShowSplashBackGroundImage" android:value="true" />

        </addElements>
        <loopElements tag="meta-data">
          <setStringFromAttribute result="MetaName" tag="$" name="android:name"/>
          <setBoolIsEqual result="bSplashShowTime" arg1="$S(MetaName)" arg2="com.epicgames.ue.LaunchActivity.showTime"/>
          <if condition="bSplashShowTime">
            <true>
              <addAttribute tag="$" name="android:value" value="$I(SplashShowTime)"/>
            </true>
          </if>
        </loopElements>
        <loopElements tag="activity">
          <setStringFromAttribute result="activityName" tag="$" name="android:name"/>
          <setBoolIsEqual result="bGameActivity" arg1="$S(activityName)" arg2="com.epicgames.ue4.GameActivity"/>
          <if condition="bGameActivity">
            <true>
              <removeAttribute  tag="$" name="android:theme"/>
              <addAttribute tag="$" name="android:theme" value="@style/UE4BaseTheme"/>
            </true>
          </if>
          <setBoolIsEqual result="bUE5bGameActivity" arg1="$S(activityName)" arg2="com.epicgames.unreal.GameActivity"/>
          <if condition="bUE5bGameActivity">
            <true>
              <removeAttribute  tag="$" name="android:theme"/>
              <addAttribute tag="$" name="android:theme" value="@style/UnrealBaseTheme"/>
            </true>
          </if>
        </loopElements>
      </true>
    </if>
    <addElements tag="application">
      <activity android:name="com.epicgames.ue.LaunchActivity" 
                android:configChanges="screenSize|orientation|keyboard|navigation|layoutDirection" 
                android:launchMode="standard" 
                android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen" 
                android:screenOrientation="sensorLandscape">
        <intent-filter>
          <action android:name="android.intent.action.MAIN" />
          <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
      </activity>
      <provider
              android:name="com.pwrd.oneshare.provider.OneShareProvider"
              android:authorities="${applicationId}.onesharefileprovider"
              android:exported="false"
              android:grantUriPermissions="true">
        <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/one_share_file_paths" />
      </provider>
      <provider
              xmlns:tools="http://schemas.android.com/tools"
              tools:replace="android:authorities"
              android:name="com.sina.weibo.sdk.content.FileProvider"
              android:authorities="${applicationId}.fileprovider"
              android:exported="false"
              android:grantUriPermissions="true">
        <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths" />
      </provider>
    </addElements>
    
  </androidManifestUpdates>

  <!-- 需要将OneSDKApplication指定为游戏application的父类 -->
  <gameApplicationSuperClass>
    <insert>
      OneSDKApplication
    </insert>
  </gameApplicationSuperClass>

  <gameApplicationImportAdditions>
    <insert>
      import android.content.Context;
      import com.pwrd.onesdk.onesdkcommon.onesdkapplication.OneSDKApplication;
    </insert>
  </gameApplicationImportAdditions>

  <!-- 防沉迷生命周期添加 -->
  <gameActivityOnStartAdditions>
    <insert>

    </insert>
  </gameActivityOnStartAdditions>

  <gameActivityOnStopAdditions>
    <insert>

    </insert>
  </gameActivityOnStopAdditions>

  <gameApplicationOnCreateAdditions>
    <insert>
    </insert>
  </gameApplicationOnCreateAdditions>




  <!-- Activity OnCreate -->
  <gameActivityOnCreateAdditions>
    <insert>
      NativeCalls.AllowJavaBackButtonEvent(true);
    </insert>
  </gameActivityOnCreateAdditions>

  <proguardAdditions>
    <insert>
      -keep public class com.pwrd.**{*;}
      -keep public class com.wanmei.**{*;}
      -keep public class android.support.**{*;}
      -keep class android.support.** { *; }
      -keep class android.arch.lifecycle.** { *; }
      -keep class android.arch.core.** { *; }

      -dontwarn com.pwrd.**
      -dontwarn com.wanmei.**
      -dontwarn com.google.gson.**
      -dontwarn com.huawei.**
      -dontwarn com.wpsdk.global.**
      -dontwarn okhttp3.**
      
      
      -keepclasseswithmembernames class * { native &lt;methods&gt;; }
      #防沉迷
      -keep class com.pwrd.fatigue.** {*;}
      -keep class com.pwrd.userterm.** {*;}
      -keep class com.pwrd.onefunction.** {*;}
      -keep class com.wpsdk.zxing.** {*;}
      -keep class com.wpsdk.journeyapps.** {*;}
    </insert>
  </proguardAdditions>

  

</root>


