apply plugin: 'com.android.library'
apply plugin: 'com.wpsdk.onesdk.engine-pack'

android {
    compileSdkVersion COMPILE_SDK_VERSION.toInteger()
    defaultConfig {
        minSdkVersion MIN_SDK_VERSION.toInteger()
        targetSdkVersion TARGET_SDK_VERSION.toInteger()
        versionCode STORE_VERSION.toInteger()
        versionName VERSION_DISPLAY_NAME
    }
    sourceSets.main {
        assets.srcDir "assets"
        jniLibs.srcDir "libs"
    }
}
dependencies {
    api fileTree(dir: 'libs', include: ['*.jar', "*.aar"])
    api 'com.wpsdk.onesdk:onesdk-native:5.75.1'
}