// Copyright Epic Games, Inc. All Rights Reserved.
using System.IO;
using UnrealBuildTool;
#if UE_5_0_OR_LATER
using EpicGames.Core;
#else
using Tools.DotNETCommon;
#endif


public class OneEngineMainlandLibrary : ModuleRules
{
    public OneEngineMainlandLibrary(ReadOnlyTargetRules Target) : base(Target)
    {
        Type = ModuleType.External;

        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            string projcetFullPath = Target.ProjectFile.ToString();
            string dllBinaryPath = Path.Combine(Path.GetDirectoryName(projcetFullPath), "Binaries/Win64/WPOneSDK_64.dll");
            string moudleDllPath = Path.Combine(ModuleDirectory, "Windows/lib/x64/WPOneSDK_64.dll");

            if (File.Exists(dllBinaryPath))
            {
                FileInfo fileInfoBinaryPath = new FileInfo(dllBinaryPath);
                FileInfo fileInfoModulePath = new FileInfo(moudleDllPath);
                if (fileInfoBinaryPath.LastWriteTime != fileInfoModulePath.LastWriteTime)
                {
                    File.Delete(dllBinaryPath);
                }
            }

            RuntimeDependencies.Add("$(ProjectDir)/Binaries/Win64/WPOneSDK_64.dll", moudleDllPath);
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Windows/include"));
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Windows/lib/x64", "WPOneSDK_64.lib"));
        }
        else if (Target.Platform == UnrealTargetPlatform.Mac)
        {
        	//增加是否安装oneEngineSDK macOS Libraries判断 
        	string MacNativeSDKPath = Path.Combine(PluginDirectory, "../MacNativeSDK");
            if(Directory.Exists(MacNativeSDKPath))
            {
            	PublicIncludePaths.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMXAppKit.framework/Headers"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WPOneEngineBridgeLaohu_MacOS.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMOneLHIAP.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMLaohuMacSDK.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMAFNetworking.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMDevice.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMMacRoute.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMNetworkDiagnose.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WPAnalysisSDK.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMXAppKit.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMCategories.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMFatigueManageSDK.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMMasonry.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMRedeemCode.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMSDWebImage.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMUActCode.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMURedeemCode.framework"));
	            PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WPInfoSyncSDK.framework"));
	            
	            if(Target.Type != TargetType.Editor) 
	            {
		            //Editor添加资源会报以下错误
		            //ERROR: Building would modify the following engine files: Engine/Binaries/Mac/UE4Editor.app/Contents/Resources/WMLaohuMacSDK.bundle
		            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../MacNativeSDK/Bundles/WMLaohuMacSDK.bundle")));
		            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../MacNativeSDK/Bundles/WPAnaConfig.plist")));
		            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../MacNativeSDK/Bundles/WMActCodeMacOSResources.bundle")));
		            // AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../MacNativeSDK/Config/OneSDKMacConfigFile.config")));
		            // 遍历Config目录下的配置文件
		            string SDKConfigDirPath = Path.Combine(PluginDirectory, "../MacNativeSDK/Config");
		            if(Directory.Exists(SDKConfigDirPath))
		            {
			            var LocaleFolders = Directory.GetFileSystemEntries(SDKConfigDirPath, "*");
			            foreach (var FolderName in LocaleFolders)
			            {
				            AdditionalBundleResources.Add(new BundleResource(FolderName));
			            }
		            }
	            }
            }
            
			PublicFrameworks.AddRange(
            new string[]
				{
					"Security",
					"Cocoa",
					"Foundation",
					"SystemConfiguration",
					"CoreTelephony",
					"CoreGraphics",
					"CoreText",
					"AdSupport",
					"StoreKit",
					"WebKit",
					"Accelerate",
					"CoreWLAN",
                    "LocalAuthentication",
                    "Metal",
                    "StoreKit"
				}
			);

			PublicWeakFrameworks.AddRange(
				new string[]
				{
					"CoreLocation",
					"QuartzCore",
					"AuthenticationServices"
				}
			);

			PublicSystemLibraries.Add("c++");
			PublicSystemLibraries.Add("resolv");
			PublicSystemLibraries.Add("sqlite3");
			PublicSystemLibraries.Add("z");
        }
        else if (Target.Platform == UnrealTargetPlatform.Android)
        {
            string PluginPath = Utils.MakePathRelativeTo(ModuleDirectory, Target.RelativeEnginePath);
            AdditionalPropertiesForReceipt.Add("AndroidPlugin", Path.Combine(PluginPath, "AndroidOneSDK_UPL.xml"));
        }
        else if (Target.Platform == UnrealTargetPlatform.IOS)
        {
            // Framework=Start
            string WeixinAppID;
            string WeiboAppID;
            string QQAppID;
            string BiliBiliAppID;
            string TapTapAppID;
            DirectoryReference MyProjectDir = DirectoryReference.FromFile(Target.ProjectFile);
            ConfigHierarchy Ini = ConfigCache.ReadHierarchy(ConfigHierarchyType.Game, MyProjectDir, UnrealTargetPlatform.IOS);
            Ini.GetString("/Script/OneEngineSDK", "Weixin", out WeixinAppID);
            Ini.GetString("/Script/OneEngineSDK", "Weibo", out WeiboAppID);
            Ini.GetString("/Script/OneEngineSDK", "QQ", out QQAppID);
            Ini.GetString("/Script/OneEngineSDK", "BilibiliAppId", out BiliBiliAppID);
            Ini.GetString("/Script/OneEngineSDK", "TapTap", out TapTapAppID);

			PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libfascore.a"));
			PublicAdditionalFrameworks.Add(new Framework("WPOneEngineBridgeLaohu", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WPOneEngineBridgeLaohu.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMFatigueManageSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMFatigueManageSDK.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("LHSocialCore", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/LHSocialCore.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("TYRZUISDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/TYRZUISDK.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMFMDB", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMFMDB.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMLaohuSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMLaohuSDK.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMLaohuShareSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMLaohuShareSDK.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMNetworkDiagnose", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMNetworkDiagnose.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMOneLHIAP", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMOneLHIAP.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WPAdSupport", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WPAdSupport.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WPAnalysisSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WPAnalysisSDK.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMTOMLSerialization", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMTOMLSerialization.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("AutoShowTerms", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/AutoShowTerms.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMTransitionAnimator", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMTransitionAnimator.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMUActCode", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMUActCode.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMURedeemCode", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMURedeemCode.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMRedeemCode", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMRedeemCode.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMAFNetworking", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMAFNetworking.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMCategories", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMCategories.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMDevice", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMDevice.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMMBProgressHUD", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMMBProgressHUD.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMMasonry", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMMasonry.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("ncnn", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/ncnn.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("opencv2", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/opencv2.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("openmp", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/openmp.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMFasCore", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMFasCore.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMFasSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMFasSDK.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WPCustomUITheme", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WPCustomUITheme.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WMCollectInfoSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMCollectInfoSDK.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WPAppPrivacy", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WPAppPrivacy.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WPPushSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WPPushSDK.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WPInfoSyncSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WPInfoSyncSDK.embeddedframework.zip")));
			
			if (!string.IsNullOrEmpty(WeixinAppID))
            {
            	PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialWechat.a"));
            	PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libWeChatSDK.a"));
            }
            if (!string.IsNullOrEmpty(WeiboAppID))
            {
            	PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialWeibo.a"));
            	PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libWeiboSDK.a"));
            	AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/WeiboSDK.bundle")));			
            }
            if (!string.IsNullOrEmpty(QQAppID))
            {
            	PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialQQ.a"));
            	PublicAdditionalFrameworks.Add(new Framework("TencentOpenAPI", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/TencentOpenAPI.embeddedframework.zip")));
            }
            if (!string.IsNullOrEmpty(BiliBiliAppID))
            {
            	PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialBilibili.a"));
            	PublicAdditionalFrameworks.Add(new Framework("BiliFollowing", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/BiliFollowing.embeddedframework.zip")));
            }
            if (!string.IsNullOrEmpty(TapTapAppID))
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialTapTap.a"));
                PublicAdditionalFrameworks.Add(new Framework("LeanCloudObjc", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/LeanCloudObjc.embeddedframework.zip")));
                PublicAdditionalFrameworks.Add(new Framework("TapBootstrapSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/TapBootstrapSDK.embeddedframework.zip")));
                PublicAdditionalFrameworks.Add(new Framework("TapCommonSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/TapCommonSDK.embeddedframework.zip")));
                PublicAdditionalFrameworks.Add(new Framework("TapLoginSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/TapLoginSDK.embeddedframework.zip")));
                PublicAdditionalFrameworks.Add(new Framework("LibProtocolBuffers", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/LibProtocolBuffers.embeddedframework.zip")));
            }
            
            // 遍历Config目录下的配置文件
            string SDKConfigDirPath = Path.Combine(PluginDirectory, "../iOSNativeSDK/Config");
            if(Directory.Exists(SDKConfigDirPath))
            {
                var LocaleFolders = Directory.GetFileSystemEntries(SDKConfigDirPath, "*");
                foreach (var FolderName in LocaleFolders)
                {
                    AdditionalBundleResources.Add(new BundleResource(FolderName));
                }
            }
            
			AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/TYRZResource.bundle")));
            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/WmComPlatform_Resources_3.bundle")));
            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/SocialResources.bundle")));
            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/WMActCodeResources.bundle")));
            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/WPAnaConfig.plist")));
            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/fascore.bundle")));
            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/WMFasSDK.bundle")));
            
			// Framework=End
            PublicFrameworks.AddRange(
                new string[]{
                    "AdSupport", "AVFoundation", "CoreFoundation", "CoreGraphics", "AppTrackingTransparency",
                    "CoreMedia", "CoreText", "MessageUI", "NetworkExtension", "Photos",
                    "StoreKit", "SystemConfiguration", "WebKit", "QuartzCore", "AdServices","CoreMotion","LocalAuthentication"
                }
            );

            PublicWeakFrameworks.AddRange(
                new string[]{
                    "AuthenticationServices", "MobileCoreServices",
                    "CoreTelephony", "Foundation", "Security", "UIKit"
                }
                          );
              
              PublicSystemLibraries.AddRange(
                  new string[]{
                      "curses", "iconv", "resolv", "z", "sqlite3"
                  }
              );
            AdditionalPropertiesForReceipt.Add("IOSPlugin", Path.Combine(ModuleDirectory, "IOSOneSDK_UPL.xml"));
        }
    }
}
