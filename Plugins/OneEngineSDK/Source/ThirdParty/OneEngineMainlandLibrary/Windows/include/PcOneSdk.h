#ifndef __PC_ONE_SDK_H__
#define __PC_ONE_SDK_H__

#if PLATFORM_WINDOWS
#include "Windows/WindowsHWrapper.h"
#else
#include <windows.h>
#endif

#ifdef WPONESDK_EXPORTS
#define PGP_ONESDK_API extern "C" __declspec(dllexport)
#else
#define PGP_ONESDK_API extern "C" __declspec(dllimport)
#endif

namespace PC_ONE_SDK {

#define CC_ERROR_UNKNOWN					0xFFFFFFFFL

#define CC_ERR_NO_ERROR						0xE0000000L

#define CC_ERR_HANDLE_INVALID				0xE0000001L

#define CC_ERR_PARAM_INVALID				0xE0000002L

#define CC_ERR_MEMORY_INSUFFICIENT			0xE0000003L

#define CC_ERR_CLIENT_NOT_LAUNCHED			0xE0000004L

#define CC_ERR_CLIENT_INTERNAL				0xE0000005L

#define CC_ERR_CREATEMAPPING_FAILURE		0xE0000006L

#define CC_ERR_OPENMAPPING_FAILURE			0xE0000007L

#define CC_ERR_MAPPINGFILE_FAILURE			0xE0000008L

#define CC_ERR_WAIT_TIMEOUT					0xE0000009L

#define CC_ERR_FINDWINDOW_FIALURE			0xE000000AL

#define CC_ERR_ACCOUNTMISMATCH				0xE000000BL

#define CC_ERR_NETFAILURE					0xE000000CL

#define CC_ERR_OVERLAY_NOTLOADED			0xE000000DL

#define CC_ERR_OVERLAY_APINOTFOUND			0xE000000EL

#define CC_ERR_REGISTRY_NOT_EXIST			0xE000000FL

#define CC_ERR_CLIENT_NOT_INSTALLED			0xE0000010L

#define CC_ERR_CLIENT_IS_UPDATING			0xE0000011L

#define CC_ERR_CLIENT_IS_INSTALLING			0xE0000012L

#define CC_ERR_OVERLAY_GOTOURL_FAILURE		0xE0000013L

#define CC_ERR_FILE_INVALID                 0xE0000014L

#define CC_ERR_NOTSTARTFROM_ARC             0xE0000015L

#define CC_ERR_OPENMUTEX_FAILURE            0xE0000020L

#define CC_ERR_CANNOTQUERY_LOGININFO        0xE0000021L

#define CC_ERR_STARTPROCESS_FAILED          0xE0000022L

#define CC_ERR_PLATFORMGUIDE_NOTFOUND       0xE0000023L

#define CC_ERR_PLATFORM_NOTINSTALLED        0xE0000024L

#define CC_ERR_PLATFORM_INSTALLSKIPBYUSER   0xE0000025L

#define CC_ERR_PLATFORMGUIDE_INVALID        0xE0000026L

#define CC_ERR_PLATFORMNOTRUNNING           0xE0000027L


	typedef void(*queryAntifatiguePreLoginInfoCallback)(int code, const char* result, int contextId);

	typedef void(*antiAddictionTimeoutCallback)(bool bForceKick, const char* antiAddictionInfo);

	typedef void(*cdkeyVerifyFinishedCallback)(bool success, int code, const wchar_t* msg, int contextId);

	typedef void(*redeemcodeVerifyFinishedCallback)(bool success, int code, const wchar_t* msg, int contextId);

	typedef bool(__cdecl* OnQuitGame)(__in bool bForcible);

	typedef void(__cdecl* OnRelogin)();

	typedef void(__cdecl* OnBeKickedOut)();

	typedef void(__cdecl* OnLoginSuccess)(__in const char* lpszUID, __in const char* lpszToken, __in int platform, __in int channelId);

	typedef void(__cdecl* OnLoginCancel)();

	typedef void(*verifyPhoneFinished)(int code, const char* messsge);

	typedef void(*queryRoleListCallback)(int code, const wchar_t* msg, const char* result, int contextId);

	typedef void(*queryAntifatiguePreLoginCallback)(int code, const wchar_t* msg, const char* result, int contextId);

	typedef void(*netqueryCallback)(bool bSuccess, const wchar_t* msg, const char* result, int contextId);

	typedef void* PGPONESDK_HANDLE;

	typedef void(*LaohuUserAuthenticationCallback)(bool bSuccess, const char* message, int authResult, bool hasNetError, int contextId);

	typedef void(*GetDeviceInfoCallback)(int code, const char* message, const char* data, int contextId);

	typedef void(__cdecl* OnPayDetailCallback)(bool success, int code, const char* message);

	typedef void(__cdecl* OnLoginFailed)(int code, const char* message);

	typedef void(*bindWMPassportCallback)(int code, const char* message, const char* data);

	typedef void(*bindPhoneCallback)(int code, const char* message, const char* phoneNumber);

	typedef void(*preQueryFinishedCallback)(int code, const wchar_t* msg, const wchar_t* result, int contextId);

	typedef void(*SecurityLockOneKeyCodeUnlockCallback)(int code, int status, const char* message, const char* unlockToken, int contextId);

	typedef void(*SecurityLockDynamicCodeUnlockCallback)(int code, const char* message, const char* unlockToken, int contextId);

	typedef void(*OnGetIPLocationInfo)(int code, const char* message, const char* result);

	typedef void(*OnACEClientPacket)(const unsigned char* data, int len);

	PGP_ONESDK_API HRESULT OneSdk_DoPayWithDetailCallback(const PGPONESDK_HANDLE hSdk, const char* lpszPayParams, OnPayDetailCallback detailCallback = NULL);

	PGP_ONESDK_API int OneSdk_LaohuUserAuthentication(LaohuUserAuthenticationCallback callback);

	PGP_ONESDK_API void OneSdk_TrackEventEnterGameScene(const char* scene, int period, const char* hints);

	PGP_ONESDK_API void OneSdk_TrackEventExitGameScene(const char* hints = "");

	PGP_ONESDK_API void OneSdk_TrackAddExtraDeviceInfo(const char* extraDeviceInfo);

	PGP_ONESDK_API bool LoadOneSDKSuccess();

	PGP_ONESDK_API int WMAntifatigueSdk_QueryAntifatiguePreLogin(queryAntifatiguePreLoginInfoCallback callback);

	PGP_ONESDK_API const char* WMAntifatigueSdk_GetAntiAddictionInfo();

	PGP_ONESDK_API void WMAntifatigueSdk_StartAntiAddictionNotify();

	PGP_ONESDK_API void WMAntifatigueSdk_StopAntiAddictionNotify();

	PGP_ONESDK_API void WMAntifatigueSdk_SetAntiAddictionTimeoutCallback(antiAddictionTimeoutCallback callback);

	PGP_ONESDK_API void SetShowDefaultActivationResultToast(bool show);

	PGP_ONESDK_API int DisplayCDKeyDialog(cdkeyVerifyFinishedCallback callback);

	PGP_ONESDK_API int QueryCDKeyCodeStatus(const char* serverId, cdkeyVerifyFinishedCallback callback);

	PGP_ONESDK_API int ExchangeCDKeyCode(const char* code, const char* serverId, cdkeyVerifyFinishedCallback callback);

	PGP_ONESDK_API int RedeemCouponCode(const char* code, const char* extraInfo, redeemcodeVerifyFinishedCallback callbck);

	PGP_ONESDK_API PGPONESDK_HANDLE OneSdk_Union_Init(const wchar_t* appId, const wchar_t* appKey, const wchar_t* oneConfigFilePath, OnQuitGame quitListener, OnRelogin changeUserListener, OnBeKickedOut kickoutListener = 0);

	PGP_ONESDK_API HRESULT OneSdk_UnInit(const PGPONESDK_HANDLE hSdk);

	PGP_ONESDK_API const char* OneSdk_GetDeviceUuid();

	PGP_ONESDK_API HRESULT OneSdk_DoLoginWithFailedCallback(const PGPONESDK_HANDLE hSdk, OnLoginSuccess loginSuccessCallback, OnLoginFailed loginFailedCallback, OnLoginCancel loginCancelCallback);

	PGP_ONESDK_API HRESULT OneSdk_Logout(const PGPONESDK_HANDLE hSdk);

	PGP_ONESDK_API bool OneSdk_PlayerLogined();

	PGP_ONESDK_API void OneSdk_EnterUserCenter();

	PGP_ONESDK_API void OneSdk_OpenComplianceOnWebView();

	PGP_ONESDK_API bool OneSdk_OpenBindWMPassportView();

	PGP_ONESDK_API bool OneSdk_OpenCloseAccountView();

	PGP_ONESDK_API bool OneSdk_VerifyPhone(verifyPhoneFinished callback);

	PGP_ONESDK_API const char* OneSdk_GetBindWMPassport();

	PGP_ONESDK_API const char* OneSdk_UserIp();

	PGP_ONESDK_API const char* OneSdk_ChannelId();

	PGP_ONESDK_API const wchar_t* OneSdk_ChannelName();

	PGP_ONESDK_API const char* OneSdk_SubChannelId();

	PGP_ONESDK_API const char* OneSdk_UserPhoneNumber();

	PGP_ONESDK_API const char* OneSdk_ChannelUid();

	PGP_ONESDK_API const char* OneSdk_ChannelToken();

	PGP_ONESDK_API const char* OneSdk_GetDeviceSys();

	PGP_ONESDK_API const char* OneSdk_GetDeviceRAM();

	PGP_ONESDK_API const char* OneSdk_GetDeviceModel();

	PGP_ONESDK_API int OneSdk_GetPlatformOS();

	PGP_ONESDK_API const char* OneSdk_SdkVersion();

	PGP_ONESDK_API void OneSdk_SetCurrentPlayerRoleInfo(const char* playerRoleInfos);

	PGP_ONESDK_API HRESULT OneSdk_StartPlatform(const wchar_t* pszCmd = NULL);

	PGP_ONESDK_API void  OneSdk_wanmeiTrackEvent(const char* eventKey, const char* eventHints, const char* eventVal);

	PGP_ONESDK_API void OneSdk_QueryRoleList(const char* serverId, queryRoleListCallback callback, int contextId);

	PGP_ONESDK_API int OneSdk_GetDeviceInfo(GetDeviceInfoCallback callback);

	PGP_ONESDK_API int OneSdk_GetSdkType();

	PGP_ONESDK_API int OneSdk_GetExecContextId();

	PGP_ONESDK_API int OneSdk_PlatformType();

	PGP_ONESDK_API void OneSdk_customTrackEvent(int taskId, const char* eventKey, const char* eventVal, const char* eventHints);

	PGP_ONESDK_API bool OneSdk_OpenBindWMPassportViewWithCallback(bindWMPassportCallback callback);

	PGP_ONESDK_API bool OneSdk_BindPhoneWithDetailCallback(bindPhoneCallback callback);

	PGP_ONESDK_API int PreQueryCDKeyCodeStatus(const char* serverId, preQueryFinishedCallback callback);

	PGP_ONESDK_API int OneSdk_SecurityLockOneKeyUnlock(const char* roleName, const char* serverName, SecurityLockOneKeyCodeUnlockCallback callback);

	PGP_ONESDK_API void OneSdk_SecurityLockStopOneKeyUnlock();

	PGP_ONESDK_API int OneSdk_SecurityLockDynamicCodeUnlock(const char* roleName, const char* serverName, const char* dynamicCode, SecurityLockDynamicCodeUnlockCallback callback);

	PGP_ONESDK_API void OneSdk_GetIPLocationInfo(OnGetIPLocationInfo callback);

	PGP_ONESDK_API void OneSdk_SetACEClientPacketListener(OnACEClientPacket listener);

	PGP_ONESDK_API int OneSdk_LoginACE(const char* accountId, int accountTypeCommandId, int worldId);

	PGP_ONESDK_API int OneSdk_OnACEClientPacketReceive(const unsigned char* data, int len);

	PGP_ONESDK_API int OneSdk_LogoutACE();

	PGP_ONESDK_API void OneSdk_GetIPLocationInfoEx(const char* ip, OnGetIPLocationInfo callback);

	PGP_ONESDK_API void OneSdk_ReportSDKVersionInfoEx(const char* sdkVersion);

	typedef void(__cdecl* OnOneSdkCommonCallback)(int code, const wchar_t* message, const wchar_t* result, int contextId);
	PGP_ONESDK_API int OneSdk_GetIPLocationInfoWithContextId(const char* ip, OnOneSdkCommonCallback callback);

	PGP_ONESDK_API const wchar_t* OneSdk_GetCurrentGameAppId();
}

#endif

