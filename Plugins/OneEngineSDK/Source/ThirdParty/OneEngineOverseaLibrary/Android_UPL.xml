<?xml version="1.0" encoding="utf-8"?>
<root xmlns:android="http://schemas.android.com/apk/res/android">
  <init>
    <log text="Android_UPL.xml Init"/>
    <setStringFromProperty result="PackageName" ini="Engine" section="/Script/AndroidRuntimeSettings.AndroidRuntimeSettings" property="PackageName"/>
    <setStringFromProperty result="YandexId" ini="Game" section="/Script/OneEngineEditor.OneEngineSettings" property="YandexAppKey"/>
  </init>
  <!-- 华为 渠道包配置  因为 google 商店 可能不允许 带有华为相关的包，所以华为 目前需要单独出一个包 -->
  <resourceCopies>
    <log text="Copying globalsdk_library directory to staging before build"/>
    <!--    <copyDir src="$S(PluginDir)/Android/globalsdk_library" dst="$S(BuildDir)/JavaLibs/globalsdk_library" />-->
    <copyDir src="$S(PluginDir)/Android/wxapi" dst="$S(BuildDir)/src/com/wpsdk/global/wxapi" />
    <copyDir src="$S(PluginDir)/Android/resources" dst="$S(BuildDir)/gradle/app/src/main/resources/wpsdk/sdk" />

  </resourceCopies>


  <buildscriptGradleAdditions>
    <insert>
      dependencies {
      classpath 'com.google.gms:google-services:4.4.2'
      classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
      classpath 'com.android.tools:r8:8.5.35'
      }
    </insert>
  </buildscriptGradleAdditions>

  <baseBuildGradleAdditions>
    <insert>
      allprojects {
        repositories {
          mavenCentral()
          maven {
          url "http://nexus.sys.wanmei.com/repository/maven-public/"
          allowInsecureProtocol true
         }
        maven {
          url 'http://developer.huawei.com/repo/'
          allowInsecureProtocol true
        }
        maven {
            url 'https://artifact.bytedance.com/repository/AwemeOpenSDK'
            allowInsecureProtocol true
          }
        }
      }
    </insert>
  </baseBuildGradleAdditions>

  <buildGradleAdditions>
    <insert>
      dependencies {
        api ('com.wpsdk.global:wpsdk-globalsdk:3.66.0') {
          //UE自动添加obb了，如果不exclude会报错
          exclude group: 'com.google.obb', module: 'obbliscense'
        }
        api 'com.wpsdk.permission:wpsdk-permission-mainland:3.0.3'
        
    
        compileOnly "com.yandex.android:authsdk:2.5.1"
        compileOnly "com.huawei.hms:iap:6.10.0.300"
        compileOnly "com.huawei.hms:hwid:6.9.0.301"
        compileOnly "com.xsolla.android:store:2.3.0"
        compileOnly "com.xsolla.android:payments:1.0.4"
        compileOnly "com.xsolla.android:inventory:2.0.2"
        compileOnly "com.samsung.android:iap-helper:6.1.0"
        compileOnly "com.tiktok.open.sdk:tiktok-open-sdk-core:2.0.1"
        compileOnly "com.tiktok.open.sdk:tiktok-open-sdk-share:2.0.1"
        compileOnly "com.hihonor.sdk:opensdk:*********"
        compileOnly "ru.rustore.sdk:billingclient:8.0.0"
        compileOnly "com.taptap.android.payment:iap:4.3.12"
        compileOnly "com.taptap.android.payment:base:4.3.12"
        compileOnly "com.taptap.android.payment:stripe:4.3.12"
        compileOnly "com.taptap.android.payment:braintree:4.3.12"
        compileOnly "com.taptap.android.payment:alipay:4.3.12"
        compileOnly "com.navercorp.nng:naver-game:1.3.6"

      }
     
      android {
        compileOptions {
          sourceCompatibility JavaVersion.VERSION_1_8
          targetCompatibility JavaVersion.VERSION_1_8
        }
      }
      apply plugin: 'com.google.gms.google-services'
      apply plugin: 'com.google.firebase.crashlytics'
    </insert>

  </buildGradleAdditions>

  <gradleProperties>
    <setStringFromProperty result="AAPT2" ini="Game" section="/Script/OneEngineEditor.OneEngineSettings" property="AAPT2"/>
    <log text="Get aapt2 path: $S(AAPT2)"/>
    <setBoolIsEqual result="AAPT2Empty" arg1="$S(AAPT2)" arg2=""/>
    <if condition="AAPT2Empty">
      <true>
        <setStringFromEnvVar result="aapt" value="%android.aapt2FromMavenOverride%"/>
        <log text="Get aapt111 path: $S(aapt)"/>
        <insertValue  value='android.aapt2FromMavenOverride=$S(aapt)'/>
      </true>
      <false>
        <insertValue  value='android.aapt2FromMavenOverride=$S(AAPT2)'/>
      </false>
    </if>
  </gradleProperties>

  <androidManifestUpdates>
    <addAttribute tag="application" name="android:theme" value="@style/GlobalSDKTheme.AppCompat.Game"/>

    <addElements tag="application">
      <receiver android:name="com.appsflyer.MultipleInstallBroadcastReceiver" android:exported="true">
        <intent-filter>
          <action android:name="com.android.vending.INSTALL_REFERRER" />
        </intent-filter>
      </receiver>
      <activity
              android:name="net.openid.appauth.RedirectUriReceiverActivity"
              android:exported="true"
              xmlns:tools="http://schemas.android.com/tools"
              tools:node="replace">
        <intent-filter>
          <action android:name="android.intent.action.VIEW" />

          <category android:name="android.intent.category.DEFAULT" />
          <category android:name="android.intent.category.BROWSABLE" />
          <!--替换成游戏包名-->
          <data android:scheme="${applicationId}" />
        </intent-filter>
      </activity>
    </addElements>


    <!-- TODO: 根据ini中的属性选择配置微信相关内容 -->
    <!-- todo name属性需要设置为 applicationId(包名) + '.wxapi.WXEntryActivity'-->
    <!-- weiXin login/share config -->
    <addElements tag="application">
      <activity-alias android:name="WxEntryActivity" android:exported="true" android:label="WXLogin" android:launchMode="singleTask" android:theme="@android:style/Theme.Translucent.NoTitleBar" android:targetActivity="com.wpsdk.global.wxapi.WXEntryActivity" />

        <activity
            android:name="com.wpsdk.global.wxapi.WXEntryActivity"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
    </addElements>

    <loopElements tag="activity-alias">
      <setStringFromAttribute result="activityName" tag="$" name="android:name"/>
      <setBoolIsEqual result="bWxEntryActivity" arg1="$S(activityName)" arg2="WxEntryActivity"/>
      <if condition="bWxEntryActivity">
        <true>
          <addAttribute tag="$" name="android:name" value="$S(PackageName).wxapi.WXEntryActivity"/>
        </true>
      </if>
    </loopElements>

    <!-- weiXin pay config-->
    <addElements tag="application">
      <activity-alias android:name="WXPayEntryActivity" android:exported="true" android:label="WXPay" android:launchMode="singleTask" android:theme="@android:style/Theme.Translucent.NoTitleBar" android:targetActivity="com.wpsdk.global.wxapi.WXPayEntryActivity" />

       <activity
            android:name="com.wpsdk.global.wxapi.WXPayEntryActivity"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
    </addElements>

    <loopElements tag="activity-alias">
      <setStringFromAttribute result="activityName" tag="$" name="android:name"/>
      <setBoolIsEqual result="bWxPayEntryActivity" arg1="$S(activityName)" arg2="WXPayEntryActivity"/>
      <if condition="bWxPayEntryActivity">
        <true>
          <addAttribute tag="$" name="android:name" value="$S(PackageName).wxapi.WXPayEntryActivity"/>
        </true>
      </if>
    </loopElements>


    <addPermission android:name="com.android.vending.CHECK_LICENSE"/>


  </androidManifestUpdates>

  <gameApplicationImportAdditions>
    <insert>

    </insert>
  </gameApplicationImportAdditions>

  <gameApplicationOnCreateAdditions>
    <insert>

    </insert>
  </gameApplicationOnCreateAdditions>
  <proguardAdditions>
    <insert>
      -keepattributes Signature
      -keepattributes RuntimeVisibleAnnotations,AnnotationDefault

      -if class com.google.gson.reflect.TypeToken
      -keep,allowobfuscation class com.google.gson.reflect.TypeToken
      -keep,allowobfuscation class * extends com.google.gson.reflect.TypeToken
      -keep,allowobfuscation,allowoptimization @com.google.gson.annotations.JsonAdapter class *
    </insert>
  </proguardAdditions>


</root>


