package com.wpsdk.global.wxapi;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;

import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.wpsdk.global.base.rxbus.RxBus;

/**
 * @Author: HePing
 * @Date: 2019/9/12
 * @Description: 微信登录/分享回调处理Activity;
 * <p>
 * 需保证类全路径名为： applicationId(包名) + wxapi.WXEntryActivity
 */
public class WXEntryActivity extends Activity implements IWXAPIEventHandler {
    private static final String TAG = "--WXEntryActivity--";

    IWXAPI mIWXAPI;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        //如果没回调onResp，八成是这句没有写
        mIWXAPI = WXAPIFactory.createWXAPI(this,  ResourceUtil.getResString(this,"wechat_app_id"), true);
        mIWXAPI.handleIntent(getIntent(), this);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        mIWXAPI.handleIntent(intent, this);
    }


    // 微信发送请求到第三方应用时，会回调到该方法
    @Override
    public void onReq(BaseReq req) {
    }

    // 第三方应用发送到微信的请求处理后的响应结果，会回调到该方法
    //app发送消息给微信，处理返回消息的回调
    @Override
    public void onResp(BaseResp resp) {
        RxBus.getInstance().post(resp);
        finish();
    }
}