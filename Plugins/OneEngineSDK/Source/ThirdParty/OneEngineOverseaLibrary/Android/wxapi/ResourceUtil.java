package com.wpsdk.global.wxapi;

import android.content.Context;

public class ResourceUtil {
    public static int getResId(Context context,String name,String type){
        return  context.getResources().getIdentifier(name,type,context.getPackageName());
    }
    public static String getResString(Context context,String name){
        return context.getResources().getString(getResId(context,name,"string"));
    }
}
