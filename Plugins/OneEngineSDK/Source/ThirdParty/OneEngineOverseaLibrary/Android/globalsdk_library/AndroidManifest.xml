<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools" package="com.wpsdk.global">
    <application>
        <activity android:name="com.wpsdk.global.wxapi.WXEntryActivity" android:exported="true" android:label="WXLogin" android:launchMode="singleTask" android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <activity android:name="com.wpsdk.global.wxapi.WXPayEntryActivity" android:exported="true" android:label="WXPay" android:launchMode="singleTask" android:theme="@android:style/Theme.Translucent.NoTitleBar" />

         <!--todo: AppAuth.替换包名-->
        <activity
            android:name="net.openid.appauth.RedirectUriReceiverActivity"
            android:exported="true"
            tools:node="replace">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!--替换成游戏包名-->
                <data android:scheme="${applicationId}" />
            </intent-filter>
        </activity>
    </application>

</manifest>
