buildscript {
    println(" it is buildScript ")
    extensions.add("tiktokVersion","2.0.1")
    extensions.add("yandexVersion","2.5.1")
}
apply plugin: 'com.android.library'
apply plugin: 'com.wpsdk.onesdk.engine-pack'
android {
	compileSdkVersion  COMPILE_SDK_VERSION.toInteger()
	defaultConfig {
		minSdkVersion  MIN_SDK_VERSION.toInteger()
		targetSdkVersion  TARGET_SDK_VERSION.toInteger()
		versionCode  STORE_VERSION.toInteger()
		versionName  VERSION_DISPLAY_NAME
	}
	repositories {
		flatDir {
			dirs 'libs'
		}
	}
	 sourceSets.main {
        assets.srcDir "assets"
        jniLibs.srcDir "libs"
     }
}
dependencies {
	api fileTree(dir: 'libs', include: ['*.jar','*.aar'])
}
dependencies {
    api ('com.wpsdk.global:wpsdk-globalsdk:2.90.0') {
      //UE自动添加obb了，如果不exclude会报错
      exclude group: 'com.google.obb', module: 'obbliscense'
    }
}
