using System.IO;
using UnrealBuildTool;
#if UE_5_0_OR_LATER
using EpicGames.Core;
#else
using Tools.DotNETCommon;
#endif


public class OneEngineOverseaLibrary : ModuleRules
{
    public OneEngineOverseaLibrary(ReadOnlyTargetRules Target) : base(Target)
    {
        Type = ModuleType.External;

        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            string projcetFullPath = Target.ProjectFile.ToString();
            string dllBinaryPath = Path.Combine(Path.GetDirectoryName(projcetFullPath), "Binaries/Win64/WPGlobalSDK_64.dll");
            string moudleDllPath = Path.Combine(ModuleDirectory, "Windows/lib/x64/WPGlobalSDK_64.dll");

            if (File.Exists(dllBinaryPath))
            {
                FileInfo fileInfoBinaryPath = new FileInfo(dllBinaryPath);
                FileInfo fileInfoModulePath = new FileInfo(moudleDllPath);
                if (fileInfoBinaryPath.LastWriteTime != fileInfoModulePath.LastWriteTime)
                {
                    File.Delete(dllBinaryPath);
                }
            }

            RuntimeDependencies.Add("$(ProjectDir)/Binaries/Win64/WPGlobalSDK_64.dll", moudleDllPath);
            PublicIncludePaths.Add(Path.Combine(ModuleDirectory, "Windows/include"));
            PublicAdditionalLibraries.Add(Path.Combine(ModuleDirectory, "Windows/lib/x64", "WPGlobalSDK_64.lib"));
        }
        else if (Target.Platform == UnrealTargetPlatform.Mac)
        { 
            //增加是否安装oneEngineSDK macOS Libraries判断 
            string MacNativeSDKPath = Path.Combine(PluginDirectory, "../MacNativeSDK");
            if(Directory.Exists(MacNativeSDKPath))
            {
                PublicIncludePaths.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMXAppKit.framework/Headers"));
    			
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Libs/libHWComADAppsflyer.a"));
                
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WPOneEngineBridgeOversea_MacOS.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/GlobalSDK_MacOS.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMMasonry.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/PWModel.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMAFNetworking.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMCategories.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMFMDB.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMMacRoute.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMNetworkDiagnose.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WPAnalysisSDK.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMSDWebImage.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMZipUtilities.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMDevice.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMWebViewJavascriptBridge.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMSoloIAP.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WMXAppKit.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/HWComPlatformADCore.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/AppsFlyerLib.framework"));
                PublicFrameworks.Add(Path.Combine(PluginDirectory, "../MacNativeSDK/Frameworks/WPInfoSyncSDK.framework"));
                
                if(Target.Type != TargetType.Editor) 
                {
    	            //Editor添加资源会报以下错误
    	            //ERROR: Building would modify the following engine files: Engine/Binaries/Mac/UE4Editor.app/Contents/Resources/GlobalResources.bundle
    	            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../MacNativeSDK/Bundles/GlobalResources.bundle")));
    	            
                    // 遍历Config目录下的配置文件
                    string SDKConfigDirPath = Path.Combine(PluginDirectory, "../MacNativeSDK/Config");
                    if(Directory.Exists(SDKConfigDirPath))
                    {
                        var LocaleFolders = Directory.GetFileSystemEntries(SDKConfigDirPath, "*");
                        foreach (var FolderName in LocaleFolders)
                        {
                            AdditionalBundleResources.Add(new BundleResource(FolderName));
                        }
                    }
                }
            }
            PublicFrameworks.AddRange(
                new string[]{
                    "CoreWLAN",
                    "Metal",
                    "LocalAuthentication", 
                    "Accelerate", 
                    "AVFoundation", 
                    "Security", 
                    "AdSupport", 
                    "QuartzCore", 
                    "SystemConfiguration",
                    "WebKit", 
                    "AdServices", 
                    "AppKit", 
                    "CoreVideo",
                    "StoreKit"
                }
            );

            // depencies of system weak framework
            PublicWeakFrameworks.AddRange(
                new string[]{
                    // none
                    "AuthenticationServices"
                }
            );

            // depencies of system library "resolv.9", 
            PublicSystemLibraries.AddRange(
                new string[]{
                    "c++", "z", "sqlite3", "resolv"
                }
            );
        }
        else if (Target.Platform == UnrealTargetPlatform.Android)
        {
            PrivateDependencyModuleNames.AddRange(new string[] { "Launch" });
            string PluginPath = Utils.MakePathRelativeTo(ModuleDirectory, Target.RelativeEnginePath);
            AdditionalPropertiesForReceipt.Add("AndroidPlugin", Path.Combine(PluginPath, "Android_UPL.xml"));
#if UE_5_0_OR_LATER
            AdditionalPropertiesForReceipt.Add("AndroidPlugin", Path.Combine(PluginPath, "Android_UE5_Oversea_UPL.xml"));
#endif
        }
        else if (Target.Platform == UnrealTargetPlatform.IOS)
        {
            string FacebookAppID;
            string VkAppId;
            string LineChannelID;
            string YandexAppKey;
            string WeixinAppID;
            string TwitterConsumerKey;
            string NaverSchemeUrl;
            string TikTokAppID;
            bool bEnableInstagram;
            bool bEnableInfiplay;
            bool bEnableMailRu;
            bool bEnableGameCenter;
            bool bEnableCrunchyroll;
            bool bEnableAdjust;
            bool bEnableSteam;
            bool bEnablePS;
            
            DirectoryReference MyProjectDir = DirectoryReference.FromFile(Target.ProjectFile);
            ConfigHierarchy Ini = ConfigCache.ReadHierarchy(ConfigHierarchyType.Game, MyProjectDir, UnrealTargetPlatform.IOS);
            Ini.GetString("/Script/OneEngineSDK", "FacebookAppID", out FacebookAppID);
            Ini.GetString("/Script/OneEngineSDK", "VkAppId", out VkAppId);
            Ini.GetString("/Script/OneEngineSDK", "LineChannelID", out LineChannelID);
            Ini.GetString("/Script/OneEngineSDK", "YandexAppKey", out YandexAppKey);
            Ini.GetString("/Script/OneEngineSDK", "Weixin", out WeixinAppID);
            Ini.GetString("/Script/OneEngineSDK", "TwitterConsumerKey", out TwitterConsumerKey);
            Ini.GetString("/Script/OneEngineSDK", "NaverSchemeUrl", out NaverSchemeUrl);
            Ini.GetString("/Script/OneEngineSDK", "TikTokAppID", out TikTokAppID);
            Ini.GetBool("/Script/OneEngineSDK", "IsEnableInstagram", out bEnableInstagram);
            Ini.GetBool("/Script/OneEngineSDK", "IsEnableInfiplay", out bEnableInfiplay);
            Ini.GetBool("/Script/OneEngineSDK", "IsEnableMailRu", out bEnableMailRu);
            Ini.GetBool("/Script/OneEngineSDK", "IsEnableGameCenter", out bEnableGameCenter);
            Ini.GetBool("/Script/OneEngineSDK", "IsEnableCrunchyroll", out bEnableCrunchyroll);
            Ini.GetBool("/Script/OneEngineSDK", "IsEnableAdjust", out bEnableAdjust);
            Ini.GetBool("/Script/OneEngineSDK", "IsEnableSteam", out bEnableSteam);
            Ini.GetBool("/Script/OneEngineSDK", "IsEnablePS", out bEnablePS);
            
            // Framework=Start
            PublicAdditionalFrameworks.Add(new Framework("WPOneEngineBridgeOversea", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WPOneEngineBridgeOversea.embeddedframework.zip")));
            
            if (!string.IsNullOrEmpty(FacebookAppID))
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialFacebook.a"));
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libHWComADFacebook.a"));
                PublicAdditionalFrameworks.Add(new Framework("FBAEMKit", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FBAEMKit.embeddedframework.zip")));
                PublicAdditionalFrameworks.Add(new Framework("FBSDKCoreKit", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FBSDKCoreKit.embeddedframework.zip")));
                PublicAdditionalFrameworks.Add(new Framework("FBSDKCoreKit_Basics", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FBSDKCoreKit_Basics.embeddedframework.zip")));
                PublicAdditionalFrameworks.Add(new Framework("FBSDKGamingServicesKit", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FBSDKGamingServicesKit.embeddedframework.zip")));
                PublicAdditionalFrameworks.Add(new Framework("FBSDKLoginKit", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FBSDKLoginKit.embeddedframework.zip")));
                PublicAdditionalFrameworks.Add(new Framework("FBSDKShareKit", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FBSDKShareKit.embeddedframework.zip")));
            }
            if (!string.IsNullOrEmpty(VkAppId))
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialVK.a"));
                PublicAdditionalFrameworks.Add(new Framework("VKSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/VKSDK.embeddedframework.zip")));
                AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/VKSdkResources.bundle")));
            }
            if (bEnableInstagram)
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialInstagram.a"));
            }
            if (!string.IsNullOrEmpty(LineChannelID))
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialLine.a"));
                PublicAdditionalFrameworks.Add(new Framework("LineSDKObjC", Path.GetFullPath(Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/LineSDKObjC.framework.zip")), null, true));
            }
            if (!string.IsNullOrEmpty(YandexAppKey))
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialYandex.a"));
                PublicAdditionalFrameworks.Add(new Framework("WMYandexSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMYandexSDK.embeddedframework.zip")));
            }
            if (!string.IsNullOrEmpty(WeixinAppID))
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialWechat.a"));
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libWeChatSDK.a"));
            }
            if (bEnableInfiplay)
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialInfiplay.a"));
            }
            if (bEnableMailRu)
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialMailRu.a"));
            }
            if (!string.IsNullOrEmpty(TwitterConsumerKey))
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialTwitter.a"));
            }
            if (!string.IsNullOrEmpty(NaverSchemeUrl))
            {
                PublicAdditionalFrameworks.Add(new Framework("NNGSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/NNGSDK.embeddedframework.zip")));
                PublicAdditionalFrameworks.Add(new Framework("NaverThirdPartyLogin", Path.GetFullPath(Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/NaverThirdPartyLogin.framework.zip")), null, true));
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialNaver.a"));
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialNaverCafe.a"));
                AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/NNGSDK.bundle")));
                AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/NaverAuth.bundle")));
            }
            if (!string.IsNullOrEmpty(TikTokAppID))
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialTikTok.a"));
                PublicAdditionalFrameworks.Add(new Framework("TikTokOpenSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/TikTokOpenSDK.embeddedframework.zip")));
            }
            if (bEnableCrunchyroll)
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialCrunchyroll.a"));
            }
            if (bEnableAdjust) 
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libHWComADAdjust.a"));
                PublicAdditionalFrameworks.Add(new Framework("AdjustSdk", Path.GetFullPath(Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/AdjustSdk.framework.zip")), null, true));
                PublicAdditionalFrameworks.Add(new Framework("AdjustSigSdk", Path.GetFullPath(Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/AdjustSigSdk.framework.zip")), null, true));
            }
            if (bEnableGameCenter)
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialGameCenter.a"));
            }
            if (bEnableSteam)
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialSteam.a"));
            }
            if (bEnablePS)
            {
                PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialPlayStation.a"));
            }
            PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialSystem.a"));
            PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libHWComADAppsflyer.a"));
            PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libHWComADFirebase.a"));
            PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialApple.a"));
            PublicAdditionalLibraries.Add(Path.Combine(PluginDirectory, "../iOSNativeSDK/Libs/libLHSocialGoogle.a"));
            PublicAdditionalFrameworks.Add(new Framework("AppsFlyerLib", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/AppsFlyerLib.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("AIHelpSupportSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/AIHelpSupportSDK.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("HWComCustomService", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/HWComCustomService.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("HWComCSAIHelp", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/HWComCSAIHelp.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("AppAuth", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/AppAuth.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("FirebaseAnalytics", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FirebaseAnalytics.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("FirebaseCoreInternal", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FirebaseCoreInternal.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("FirebaseCore", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FirebaseCore.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("FirebaseCrashlytics", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FirebaseCrashlytics.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("FirebaseInstallations", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FirebaseInstallations.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("FirebaseCoreExtension", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FirebaseCoreExtension.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("FirebaseRemoteConfigInterop", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FirebaseRemoteConfigInterop.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("FirebaseSessions", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FirebaseSessions.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("FirebaseMessaging", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FirebaseMessaging.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("GlobalSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/GlobalSDK.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("GoogleAppMeasurement", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/GoogleAppMeasurement.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("GoogleAppMeasurementIdentitySupport", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/GoogleAppMeasurementIdentitySupport.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("GoogleDataTransport", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/GoogleDataTransport.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("GoogleSignIn", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/GoogleSignIn.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("GoogleUtilities", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/GoogleUtilities.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("GTMAppAuth", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/GTMAppAuth.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("GTMSessionFetcher", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/GTMSessionFetcher.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("HWComPlatformADCore", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/HWComPlatformADCore.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("HWComPlatformPushCore", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/HWComPlatformPushCore.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("LHSocialCore", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/LHSocialCore.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("nanopb", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/nanopb.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMAFNetworking", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMAFNetworking.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMCache", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMCache.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMCategories", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMCategories.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMCollectInfoSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMCollectInfoSDK.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMDevice", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMDevice.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMDPPhotoPicker", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMDPPhotoPicker.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMFMDB", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMFMDB.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMImageProcessor", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMImageProcessor.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMLogger", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMLogger.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMMasonry", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMMasonry.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMMBProgressHUD", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMMBProgressHUD.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMNetworkDiagnose", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMNetworkDiagnose.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMRedeemCode", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMRedeemCode.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMSDWebImage", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMSDWebImage.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMSoloIAP", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMSoloIAP.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMWebViewJavascriptBridge", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMWebViewJavascriptBridge.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WPAnalysisSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WPAnalysisSDK.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WMZipUtilities", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WMZipUtilities.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("FBLPromises", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/FBLPromises.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("Promises", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/Promises.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("WPEngineExtension", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WPEngineExtension.embeddedframework.zip")));
			PublicAdditionalFrameworks.Add(new Framework("GoogleAdsOnDeviceConversion", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/GoogleAdsOnDeviceConversion.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("WPInfoSyncSDK", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/WPInfoSyncSDK.embeddedframework.zip")));
            PublicAdditionalFrameworks.Add(new Framework("AppCheckCore", Path.Combine(PluginDirectory, "../iOSNativeSDK/Frameworks/AppCheckCore.embeddedframework.zip")));
            
            // 遍历Config目录下的配置文件
            string SDKConfigDirPath = Path.Combine(PluginDirectory, "../iOSNativeSDK/Config");
            if(Directory.Exists(SDKConfigDirPath))
            {
                var LocaleFolders = Directory.GetFileSystemEntries(SDKConfigDirPath, "*");
                foreach (var FolderName in LocaleFolders)
                {
                    AdditionalBundleResources.Add(new BundleResource(FolderName));
                }
            }
            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/WMDPPhotoPicker.bundle")));
            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/AIHelpSupportSDK.bundle")));
            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/GlobalResources.bundle")));
            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/GoogleSignIn.bundle")));
            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/SocialResources.bundle")));
            AdditionalBundleResources.Add(new BundleResource(Path.Combine(PluginDirectory, "../iOSNativeSDK/Resources/PrivacyInfo.xcprivacy")));
            
            // Framework=End
            PublicFrameworks.AddRange(
                new string[]{
                    "AdSupport",
                    "Security",
                    "StoreKit",
                    "LocalAuthentication",
                    "Accelerate",
                    "CoreTelephony",
                    "SystemConfiguration",
                    "NetworkExtension",
                    "AVFoundation",
                    "SafariServices",
                    "WebKit",
                    "Photos",
                    "PhotosUI",
                    "MapKit",
                    "CoreLocation",
                    "Social",
                    "Accounts"
                }
            );

            PublicWeakFrameworks.AddRange(
                new string[]{
                    "AuthenticationServices",
                    "MobileCoreServices",
                    "UIKit",
                    "AppTrackingTransparency",
                    "AdServices",
                    "SwiftUI"
                }
            );

            PublicSystemLibraries.AddRange(
                new string[]{
                    "c++", "resolv.9", "z", "sqlite3"
                }
            );
            AdditionalPropertiesForReceipt.Add("IOSPlugin", Path.Combine(ModuleDirectory, "IOS_UPL.xml"));
        }
    }
}
