#ifndef __PC_GLOBAL_SDK_H__
#define __PC_GLOBAL_SDK_H__

#if PLATFORM_WINDOWS
#include "Windows/WindowsHWrapper.h"
#else
#include <windows.h>
#endif

#ifdef WPGLOBALSDK_EXPORTS
#define PGP_GLOBAL_API extern "C" __declspec(dllexport)
#else
#define PGP_GLOBAL_API extern "C" __declspec(dllimport)
#endif


namespace PC_GLOBAL_SDK
{
#define CC_ERROR_UNKNOWN					0xFFFFFFFFL

#define CC_ERR_NO_ERROR						0xE0000000L

#define CC_ERR_HANDLE_INVALID				0xE0000001L

#define CC_ERR_PARAM_INVALID				0xE0000002L

#define CC_ERR_MEMORY_INSUFFICIENT			0xE0000003L

#define CC_ERR_CLIENT_NOT_LAUNCHED			0xE0000004L

#define CC_ERR_CLIENT_INTERNAL				0xE0000005L

#define CC_ERR_CREATEMAPPING_FAILURE		0xE0000006L

#define CC_ERR_OPENMAPPING_FAILURE			0xE0000007L

#define CC_ERR_MAPPINGFILE_FAILURE			0xE0000008L

#define CC_ERR_WAIT_TIMEOUT					0xE0000009L

#define CC_ERR_FINDWINDOW_FIALURE			0xE000000AL

#define CC_ERR_ACCOUNTMISMATCH				0xE000000BL

#define CC_ERR_NETFAILURE					0xE000000CL

#define CC_ERR_OVERLAY_NOTLOADED			0xE000000DL

#define CC_ERR_OVERLAY_APINOTFOUND			0xE000000EL

#define CC_ERR_REGISTRY_NOT_EXIST			0xE000000FL

#define CC_ERR_CLIENT_NOT_INSTALLED			0xE0000010L

#define CC_ERR_CLIENT_IS_UPDATING			0xE0000011L

#define CC_ERR_CLIENT_IS_INSTALLING			0xE0000012L

#define CC_ERR_OVERLAY_GOTOURL_FAILURE		0xE0000013L

#define CC_ERR_FILE_INVALID                 0xE0000014L

#define CC_ERR_NOTSTARTFROM_ARC             0xE0000015L

#define CC_ERR_OPENMUTEX_FAILURE            0xE0000020L

#define CC_ERR_CANNOTQUERY_LOGININFO        0xE0000021L

#define CC_ERR_STARTPROCESS_FAILED          0xE0000022L

#define CC_ERR_PLATFORMGUIDE_NOTFOUND       0xE0000023L

#define CC_ERR_PLATFORM_NOTINSTALLED        0xE0000024L

#define CC_ERR_PLATFORM_INSTALLSKIPBYUSER   0xE0000025L

#define CC_ERR_PLATFORMGUIDE_INVALID        0xE0000026L

#define CC_ERR_PLATFORMNOTRUNNING           0xE0000027L

	typedef void(*redeemcodeVerifyFinishedCallback)(bool success, int code, const wchar_t* msg, int contextId);

	typedef bool(__cdecl* OnQuitGame)(__in bool bForcible);

	typedef void(__cdecl* OnRelogin)();

	typedef void(__cdecl* OnBeKickedOut)();

	typedef void(*queryRoleListCallback)(int code, const wchar_t* msg, const char* result, int contextId);

	typedef void(*netqueryCallback)(bool bSuccess, const wchar_t* msg, const char* result, int contextId);

	typedef void* PGPONESDK_HANDLE;

	typedef void(__cdecl* pOnGlobalLoginResult)(const wchar_t* pUID, const wchar_t* pToken, int loginType, int nErrCode, const wchar_t* pErrMessage);

	typedef void(__cdecl* pOnGlobalLogoutResult)();

	typedef void(__cdecl* pOnGlobalTranslateResult)(int contextId, const wchar_t* pTargetStr, const wchar_t* pErrMessage, int nErrCode);

	typedef void(__cdecl* pOnGlobalIpLocationResult)(int connectId, const wchar_t* pTargetJson);

	typedef void(__cdecl* pOnGlobalGetAnnounsCallback)(int connectId, const wchar_t* pTargetJson, int errCode, const wchar_t* errMsg);

	typedef void(__cdecl* pOnGlobalInvokeActivateCallback)(int iErrCode, const wchar_t* message, int connectId);

	typedef void(__cdecl* pFnOnGlobalCheckUserActivate)(int responseCode, const wchar_t* message);

	typedef void(__cdecl* pFnOnGlobalUserActivate)(int responseCode, const wchar_t* message);

	typedef void(__cdecl* OnPayDetailCallback)(bool success, int code, const char* message);

	typedef void(__cdecl* pFnOnGlobalGetLocalTokenCache)(const wchar_t* tokens);

	typedef void(__cdecl* pFnOnGlobalGetUserInfoCallback)(const wchar_t* userInfos, int errNumber, const wchar_t* errMsg);

	typedef void(__cdecl* pFnOnGlobalGetPlatformCallback)(const wchar_t* platform);

	typedef void(__cdecl* OnGetProductListCallback)(int code, const char* message, int contextId);

	PGP_GLOBAL_API int OneSdk_GetProductList(const char* productIDs, OnGetProductListCallback callback);

	PGP_GLOBAL_API HRESULT OneSdk_DoPayWithDetailCallback(const PGPONESDK_HANDLE hSdk, const char* lpszPayParams, OnPayDetailCallback detailCallback = NULL);

	PGP_GLOBAL_API void OneSdk_TrackEventEnterGameScene(const char* scene, int period, const char* hints);

	PGP_GLOBAL_API void OneSdk_TrackEventExitGameScene(const char* hints = "");

	PGP_GLOBAL_API void OneSdk_TrackAddExtraDeviceInfo(const char* extraDeviceInfo);

	PGP_GLOBAL_API bool LoadGlobalSDKSuccess();

	PGP_GLOBAL_API int RedeemCouponCode(const char* code, const char* extraInfo, redeemcodeVerifyFinishedCallback callbck);

	PGP_GLOBAL_API PGPONESDK_HANDLE OneSdk_Union_Init(const wchar_t* appId, const wchar_t* appKey, const wchar_t* oneConfigFilePath, OnQuitGame quitListener, OnRelogin changeUserListener, OnBeKickedOut kickoutListener = 0);

	PGP_GLOBAL_API HRESULT OneSdk_UnInit(const PGPONESDK_HANDLE hSdk);

	PGP_GLOBAL_API const char* OneSdk_GetDeviceUuid();

	PGP_GLOBAL_API const char* OneSdk_GetDeviceSys();

	PGP_GLOBAL_API void  OneSdk_wanmeiTrackEvent(const char* eventKey, const char* eventHints, const char* eventVal);

	PGP_GLOBAL_API void OneSdk_QueryRoleList(const char* serverId, queryRoleListCallback callback, int contextId);

	PGP_GLOBAL_API void OneSdk_SetCurrentPlayerRoleInfo(const char* playerRoleInfos);

	PGP_GLOBAL_API void global_RegisterLoginCallback(pOnGlobalLoginResult loginResultCallback);

	PGP_GLOBAL_API void global_RegisterLogoutCallback(pOnGlobalLogoutResult logoutResultCallback);

	PGP_GLOBAL_API void global_LoginBySdkView();

	PGP_GLOBAL_API void global_UISwitchAccount();

	PGP_GLOBAL_API void global_OpenUserCenterBySdkView();

	PGP_GLOBAL_API void global_Logout();

	PGP_GLOBAL_API const wchar_t* global_GetUserInfo();

	PGP_GLOBAL_API void global_SetLanguage(const wchar_t* language);

	PGP_GLOBAL_API int global_TranslateBySdk(const wchar_t* text, pOnGlobalTranslateResult translateResult);

	PGP_GLOBAL_API int global_TranslateBySdkWithTargetLanguage(const wchar_t* text, const wchar_t* targetLanguage, pOnGlobalTranslateResult translateResult);

	PGP_GLOBAL_API const wchar_t* global_getDeviceInfo();

	PGP_GLOBAL_API void global_OpenAIHelp(int type, const char* roleID, const char* serverID, const char* roleName);

	PGP_GLOBAL_API int global_GetIpLocationJson(pOnGlobalIpLocationResult callbackJson);

	PGP_GLOBAL_API const wchar_t* global_GetMediaIdStr();

	PGP_GLOBAL_API int global_getAnnounsBeforeLogin(const wchar_t* showType, pOnGlobalGetAnnounsCallback callback);

	PGP_GLOBAL_API int global_getAnnounsAfterLogin(const wchar_t* showType, const wchar_t* roleId, const wchar_t* serverId, pOnGlobalGetAnnounsCallback callback);

	PGP_GLOBAL_API int global_InvokeActivateWnd(const wchar_t* serverId, pOnGlobalInvokeActivateCallback callback);

	PGP_GLOBAL_API bool global_isLogin();

	PGP_GLOBAL_API void global_checkUserIsActivate(const wchar_t* serverId, pFnOnGlobalCheckUserActivate fn);

	PGP_GLOBAL_API void global_invokeUserActivate(const wchar_t* activateCode, pFnOnGlobalUserActivate fn);

	PGP_GLOBAL_API const char* global_openWebViewWithUrl(const char* url, const char* jsonConfig);

	PGP_GLOBAL_API const char* OneSdk_ADId();

	PGP_GLOBAL_API const wchar_t* global_getCurrentLang();

	PGP_GLOBAL_API int OneSdk_GetSdkType();

	PGP_GLOBAL_API int OneSdk_GetExecContextId();

	PGP_GLOBAL_API void global_appflyerADTrack(const wchar_t* eventName, const wchar_t* eventParams);

	PGP_GLOBAL_API void global_setConfigAppId(const wchar_t* appId);

	PGP_GLOBAL_API void OneSdk_customTrackEvent(int taskId, const char* eventKey, const char* eventVal, const char* eventHints);

	PGP_GLOBAL_API void global_getLocalTokenCache(pFnOnGlobalGetLocalTokenCache fnCallback);

	PGP_GLOBAL_API void global_showBindView(pFnOnGlobalGetUserInfoCallback fnCallback, int loginType);

	PGP_GLOBAL_API void global_tokenLoginByGame(const wchar_t* userId, const wchar_t* userToken);

	PGP_GLOBAL_API void global_thirdLoginByGame(int loginType);

	PGP_GLOBAL_API int global_getOsType();

	PGP_GLOBAL_API void global_guestLogin();

	PGP_GLOBAL_API void global_getPlatform(pFnOnGlobalGetPlatformCallback fnCallback);

	PGP_GLOBAL_API void global_appflyerSetCustomData(const wchar_t* customData);

	PGP_GLOBAL_API void global_appflyerSetCollectionEnable(bool enable);

	PGP_GLOBAL_API void global_setDelUserTip(const wchar_t* delUserTip);

	PGP_GLOBAL_API void global_disableUsercenterFunc(int funcEnum);

	PGP_GLOBAL_API void global_ReportSDKVersionInfo(const char* sdkVersion);

	typedef void(__cdecl* pFnOnGlobalGetProductListCallback)(int code, const wchar_t* message, const wchar_t* data, int contextId);

	PGP_GLOBAL_API int global_getProductList(const wchar_t* productIds, pFnOnGlobalGetProductListCallback callback);

	typedef void(__cdecl* pFnOnGlobalSecurityLockDynamicCodeUnlockCallback)(int code, const wchar_t* message, const wchar_t* unlockToken, int contextId);

	PGP_GLOBAL_API int global_SecurityLockDynamicCodeUnlock(const wchar_t* dynamicCode, pFnOnGlobalSecurityLockDynamicCodeUnlockCallback callback);

	typedef void(__cdecl* pFnOnGlobalCommonCallback)(int code, const wchar_t* message, const wchar_t* result, int contextId);

	PGP_GLOBAL_API int global_checkUserIsActivateWithContextId(const wchar_t* serverId, pFnOnGlobalCommonCallback fn);

	PGP_GLOBAL_API int global_invokeUserActivateWithContextId(const wchar_t* activateCode, pFnOnGlobalCommonCallback fn);

	PGP_GLOBAL_API int global_getLocalTokenCacheWithContextId(pFnOnGlobalCommonCallback fnCallback);

	PGP_GLOBAL_API int global_getPlatformWithContextId(pFnOnGlobalCommonCallback fnCallback);

	PGP_GLOBAL_API int global_GetIpLocation(const wchar_t* ip, pFnOnGlobalCommonCallback fnCallback);

	PGP_GLOBAL_API const wchar_t* global_GetCurrentGameAppId();
}


#endif
