<?xml version="1.0" encoding="utf-8"?>
<root>
    <init>
        <setStringFromProperty result="sBundleIdentifier" ini="Engine" section="/Script/IOSRuntimeSettings.IOSRuntimeSettings" property="BundleIdentifier" default=""/>
        <setStringFromProperty result="sFacebookAppID" ini="Game" section="/Script/OneEngineSDK" property="FacebookAppID" default=""/>
        <setStringFromProperty result="sFacebookClientToken" ini="Game" section="/Script/OneEngineSDK" property="FacebookClientToken" default=""/>
        <setStringFromProperty result="sFacebookDisplayName" ini="Game" section="/Script/OneEngineSDK" property="FacebookDisplayName" default=""/>
        <setStringFromProperty result="sREVERSED_CLIENT_ID" ini="Game" section="/Script/OneEngineSDK" property="REVERSED_CLIENT_ID" default=""/>
        <setStringFromProperty result="sVkAppId" ini="Game" section="/Script/OneEngineSDK" property="VkAppId" default=""/>
        <setStringFromProperty result="sLineChannelID" ini="Game" section="/Script/OneEngineSDK" property="LineChannelID" default=""/>
        <setStringFromProperty result="sNaver" ini="Game" section="/Script/OneEngineSDK" property="NaverSchemeUrl" default=""/>
        <setStringFromProperty result="sTwitterConsumerKey" ini="Game" section="/Script/OneEngineSDK" property="TwitterConsumerKey" default=""/>
        <setStringFromProperty result="sTikTokAppID" ini="Game" section="/Script/OneEngineSDK" property="TikTokAppID" default=""/>
        <setStringFromProperty result="sWechatAppid" ini="Game" section="/Script/OneEngineSDK" property="Weixin" default=""/>
        <setStringFromProperty result="sYandexAppKey" ini="Game" section="/Script/OneEngineSDK" property="YandexAppKey" default=""/>
        <setStringFromProperty result="sWPAppID" ini="Game" section="/Script/OneEngineSDK" property="Wanmei" default=""/>
        <setStringFromProperty result="sWPAppID1" ini="Game" section="/Script/OneEngineSDK" property="Wanmei1" default=""/>
        <setStringFromProperty result="sWPAppID2" ini="Game" section="/Script/OneEngineSDK" property="Wanmei2" default=""/>
        
        <log text="GlobalSDK adding requirement and permission to plist..."/>
    </init>
    <iosPListUpdates>
        <loopElements tag="$">
            <setStringFromTag result="CurrentTag" tag="$"/>
            <setBoolIsEqual   result="isRootDictionary" arg1="$S(CurrentTag)" arg2="dict"/>

            <if condition="isRootDictionary">
                <true>
                    <!--  =============================== 处理CFBundleURLTypes =============================== -->
                    <!--  查找CFBundleURLTypes，判断是否存在  -->
                    <setBool result="bCFBundleURLTypesFound" value="false"/>
                    <setBool result="bCFBundleURLTypesSearchNextElement" value="false"/>

                    <!--  因Naver必须要处在CFBundleURLTypes第一位，全球版本默认不支持追加，所以不对当前plist查找CFBundleURLTypes，统一重新创建  -->
                    <!--                    <loopElements tag="$">-->
                    <!--                        <setStringFromTagText result="TagIteratorValue" tag="$"/>-->
                    <!--                        <setBoolIsEqual result="isCFBundleURLTypes" arg1="$S(TagIteratorValue)" arg2="CFBundleURLTypes"/>-->
                    <!--                        -->
                    <!--                        <if condition="isCFBundleURLTypes">-->
                    <!--                            <true>-->
                    <!--                                <setBool result="bCFBundleURLTypesFound" value="true"/>-->
                    <!--                            </true>-->
                    <!--                        </if>-->
                    <!--                    </loopElements>-->

                    <!-- 如果没有CFBundleURLTypes，则创建 -->
                    <if condition="bCFBundleURLTypesFound">
                        <false>
                            <addElements tag="$" once="true">
                                <key>CFBundleURLTypes</key>
                                <array>
                                </array>
                            </addElements>
                        </false>
                    </if>

                    <!-- 添加三方平台urlscheme  -->
                    <loopElements tag="$">
                        <setStringFromTagText result="TagIteratorValue" tag="$"/>
                        <setBoolIsEqual result="isCFBundleURLTypes" arg1="$S(TagIteratorValue)" arg2="CFBundleURLTypes"/>

                        <if condition="bCFBundleURLTypesSearchNextElement">
                            <true>
                                <setBool result="bCFBundleURLTypesSearchNextElement" value="false"/>

                                <!--  添加naver urlscheme(必须放第一个)  -->
                                <setBoolIsEqual result="isSetURLTypesNaverNull" arg1="$S(sNaver)" arg2=""/>
                                <if condition="isSetURLTypesNaverNull">
                                    <false>
                                        <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                        <!-- 添加CFBundleTypeRole、CFBundleURLName  -->
                                        <addElements tag="$dCFBundleURLTypesDict">
                                            <key>CFBundleTypeRole</key>
                                            <string>Editor</string>
                                            <key>CFBundleURLName</key>
                                            <string>naver</string>
                                        </addElements>
                                        <!--  添加 CFBundleURLSchemes  -->
                                        <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>
                                        <setElement result="dCFBundleURLSchemesRootArray" value="array"/>
                                        <setElement result="dCFBundleURLSchemesAppId" value="string" text="$S(sNaver)"/>
                                        <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>
                                        <addElement tag="$" name="dCFBundleURLTypesDict"/>
                                    </false>
                                </if>

                                <!--  添加Facebook urlscheme  -->
                                <setBoolIsEqual result="isSetURLTypesFacebookNull" arg1="$S(sFacebookAppID)" arg2=""/>
                                <if condition="isSetURLTypesFacebookNull">
                                    <false>
                                        <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                        <!-- 添加CFBundleTypeRole、CFBundleURLName  -->
                                        <addElements tag="$dCFBundleURLTypesDict">
                                            <key>CFBundleTypeRole</key>
                                            <string>Editor</string>
                                            <key>CFBundleURLName</key>
                                            <string>facebook</string>
                                        </addElements>
                                        <!--  添加 CFBundleURLSchemes  -->
                                        <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>
                                        <setElement result="dCFBundleURLSchemesRootArray" value="array"/>
                                        <setElement result="dCFBundleURLSchemesAppId" value="string" text="fb$S(sFacebookAppID)"/>
                                        <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>
                                        <addElement tag="$" name="dCFBundleURLTypesDict"/>
                                    </false>
                                </if>

                                <!--  添加google urlscheme  -->
                                <setBoolIsEqual result="isSetURLTypesGoogleNull" arg1="$S(sREVERSED_CLIENT_ID)" arg2=""/>
                                <if condition="isSetURLTypesGoogleNull">
                                    <false>
                                        <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                        <!--  添加CFBundleTypeRole、CFBundleURLName  -->
                                        <addElements tag="$dCFBundleURLTypesDict">
                                            <key>CFBundleTypeRole</key>
                                            <string>Editor</string>
                                            <key>CFBundleURLName</key>
                                            <string>google</string>
                                        </addElements>
                                        <!--  添加 CFBundleURLSchemes  -->
                                        <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>
                                        <setElement result="dCFBundleURLSchemesRootArray" value="array"/>
                                        <setElement result="dCFBundleURLSchemesAppId" value="string" text="$S(sREVERSED_CLIENT_ID)"/>
                                        <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>
                                        <addElement tag="$" name="dCFBundleURLTypesDict"/>
                                    </false>
                                </if>

                                <!-- 添加Line urlscheme-->
                                <setBoolIsEqual result="isSetURLTypeLineNull" arg1="$S(sLineChannelID)" arg2=""/>
                                <if condition="isSetURLTypeLineNull">
                                    <false>
                                        <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                        <!--  添加CFBundleTypeRole、CFBundleURLName  -->
                                        <addElements tag="$dCFBundleURLTypesDict">
                                            <key>CFBundleTypeRole</key>
                                            <string>Editor</string>
                                            <key>CFBundleURLName</key>
                                            <string>line</string>
                                        </addElements>
                                        <!--  添加 CFBundleURLSchemes  -->
                                        <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>
                                        <setElement result="dCFBundleURLSchemesRootArray" value="array"/>
                                        <setElement result="dCFBundleURLSchemesAppId" value="string" text="line3rdp.$S(sBundleIdentifier)"/>
                                        <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>
                                        <addElement tag="$" name="dCFBundleURLTypesDict"/>
                                    </false>
                                </if>

                                <!--  添加VkAppId urlscheme  -->
                                <setBoolIsEqual result="isSetURLTypesVKNull" arg1="$S(sVkAppId)" arg2=""/>
                                <if condition="isSetURLTypesVKNull">
                                    <false>
                                        <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                        <!-- 添加CFBundleTypeRole、CFBundleURLName  -->
                                        <addElements tag="$dCFBundleURLTypesDict">
                                            <key>CFBundleTypeRole</key>
                                            <string>Editor</string>
                                            <key>CFBundleURLName</key>
                                            <string>vk</string>
                                        </addElements>
                                        <!--  添加 CFBundleURLSchemes  -->
                                        <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>
                                        <setElement result="dCFBundleURLSchemesRootArray" value="array"/>
                                        <setElement result="dCFBundleURLSchemesAppId" value="string" text="vk$S(sVkAppId)"/>
                                        <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>
                                        <addElement tag="$" name="dCFBundleURLTypesDict"/>
                                    </false>
                                </if>

                                <!--  添加wechat urlscheme  -->
                                <setBoolIsEqual result="isSetURLTypesWechatNull" arg1="$S(sWechatAppid)" arg2=""/>
                                <if condition="isSetURLTypesWechatNull">
                                    <false>
                                        <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                        <!-- 添加CFBundleTypeRole、CFBundleURLName  -->
                                        <addElements tag="$dCFBundleURLTypesDict">
                                            <key>CFBundleTypeRole</key>
                                            <string>Editor</string>
                                            <key>CFBundleURLName</key>
                                            <string>wechat</string>
                                        </addElements>
                                        <!--  添加 CFBundleURLSchemes  -->
                                        <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>
                                        <setElement result="dCFBundleURLSchemesRootArray" value="array"/>
                                        <setElement result="dCFBundleURLSchemesAppId" value="string" text="$S(sWechatAppid)"/>
                                        <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>
                                        <addElement tag="$" name="dCFBundleURLTypesDict"/>
                                    </false>
                                </if>

                                <!--  添加twitter urlscheme  -->
                                <setBoolIsEqual result="isSetURLTypesTwitterNull" arg1="$S(sTwitterConsumerKey)" arg2=""/>
                                <if condition="isSetURLTypesTwitterNull">
                                    <false>
                                        <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                        <!-- 添加CFBundleTypeRole、CFBundleURLName  -->
                                        <addElements tag="$dCFBundleURLTypesDict">
                                            <key>CFBundleTypeRole</key>
                                            <string>Editor</string>
                                            <key>CFBundleURLName</key>
                                            <string>twitter</string>
                                        </addElements>
                                        <!--  添加 CFBundleURLSchemes  -->
                                        <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>
                                        <setElement result="dCFBundleURLSchemesRootArray" value="array"/>
                                        <setElement result="dCFBundleURLSchemesAppId" value="string" text="twitterkit-$S(sTwitterConsumerKey)"/>
                                        <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>
                                        <addElement tag="$" name="dCFBundleURLTypesDict"/>
                                    </false>
                                </if>

                                <!--  添加tiktok urlscheme  -->
                                <setBoolIsEqual result="isSetURLTypesTiktokNull" arg1="$S(sTikTokAppID)" arg2=""/>
                                <if condition="isSetURLTypesTiktokNull">
                                    <false>
                                        <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                        <!-- 添加CFBundleTypeRole、CFBundleURLName  -->
                                        <addElements tag="$dCFBundleURLTypesDict">
                                            <key>CFBundleTypeRole</key>
                                            <string>Editor</string>
                                            <key>CFBundleURLName</key>
                                            <string>tiktok</string>
                                        </addElements>
                                        <!--  添加 CFBundleURLSchemes  -->
                                        <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>
                                        <setElement result="dCFBundleURLSchemesRootArray" value="array"/>
                                        <setElement result="dCFBundleURLSchemesAppId" value="string" text="$S(sTikTokAppID)"/>
                                        <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>
                                        <addElement tag="$" name="dCFBundleURLTypesDict"/>
                                    </false>
                                </if>

                                <!--  添加yandex urlscheme  -->
                                <setBoolIsEqual result="isSetURLTypesYandexNull" arg1="$S(sYandexAppKey)" arg2=""/>
                                <if condition="isSetURLTypesYandexNull">
                                    <false>
                                        <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                        <!-- 添加CFBundleTypeRole、CFBundleURLName  -->
                                        <addElements tag="$dCFBundleURLTypesDict">
                                            <key>CFBundleTypeRole</key>
                                            <string>Editor</string>
                                            <key>CFBundleURLName</key>
                                            <string>Yandex</string>
                                        </addElements>
                                        <!--  添加 CFBundleURLSchemes  -->
                                        <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>
                                        <setElement result="dCFBundleURLSchemesRootArray" value="array"/>
                                        <setElement result="dCFBundleURLSchemesAppId" value="string" text="yx$S(sYandexAppKey)"/>
                                        <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId"/>
                                        <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>
                                        <addElement tag="$" name="dCFBundleURLTypesDict"/>
                                    </false>
                                </if>

                                <!--  添加wanmei urlscheme  -->
                                <setElement result="dCFBundleURLTypesDict" value="dict"/>
                                <!-- 添加CFBundleTypeRole、CFBundleURLName  -->
                                <addElements tag="$dCFBundleURLTypesDict">
                                    <key>CFBundleTypeRole</key>
                                    <string>Editor</string>
                                    <key>CFBundleURLName</key>
                                    <string>wp</string>
                                </addElements>
                                <!--  添加 CFBundleURLSchemes  -->
                                <setElement result="dCFBundleURLSchemes" value="key" text="CFBundleURLSchemes"/>
                                <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemes"/>
                                <setElement result="dCFBundleURLSchemesRootArray" value="array"/>
                                <!--  添加 wp+facebook  -->
                                <setElement result="dCFBundleURLSchemesAppId1" value="string" text="$S(sWPAppID)"/>
                                <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId1"/>
                                <!--  添加wanmei1  -->
                                <setBoolIsEqual result="isSetURLTypesWanme1" arg1="$S(sWPAppID1)" arg2=""/>
                                <if condition="isSetURLTypesWanme1">
                                    <false>
                                        <setElement result="dCFBundleURLSchemesAppId2" value="string" text="$S(sWPAppID1)"/>
                                        <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId2"/>
                                    </false>
                                </if>
                                <!--  添加wanmei2（扩展使用）  -->
                                <setBoolIsEqual result="isSetURLTypesWanme2" arg1="$S(sWPAppID2)" arg2=""/>
                                <if condition="isSetURLTypesWanme2">
                                    <false>
                                        <setElement result="dCFBundleURLSchemesAppId3" value="string" text="$S(sWPAppID2)"/>
                                        <addElement tag="$dCFBundleURLSchemesRootArray" name="dCFBundleURLSchemesAppId3"/>
                                    </false>
                                </if>
                                <!--  添加结束符 CFBundleURLSchemes  -->
                                <addElement tag="$dCFBundleURLTypesDict" name="dCFBundleURLSchemesRootArray"/>
                                <addElement tag="$" name="dCFBundleURLTypesDict"/>
                            </true>
                        </if>
                        <if condition="isCFBundleURLTypes">
                            <true>
                                <setBool result="bCFBundleURLTypesSearchNextElement" value="true"/>
                            </true>
                        </if>
                    </loopElements>
                    <!--  =============================== 处理LSApplicationQueriesSchemes =============================== -->
                    <!--  判断LSApplicationQueriesSchemes是否存在 -->
                    <setBool result="bApplicationQueriesSchemesFound" value="false"/>
                    <setBool result="bApplicationQueriesSchemesNextElement" value="false"/>
                    <loopElements tag="$">
                        <setStringFromTagText result="TagIteratorValue" tag="$"/>
                        <setBoolIsEqual result="isApplicationQueriesSchemes" arg1="$S(TagIteratorValue)" arg2="LSApplicationQueriesSchemes"/>
                        <if condition="isApplicationQueriesSchemes">
                            <true>
                                <setBool result="bApplicationQueriesSchemesFound" value="true"/>
                            </true>
                        </if>
                    </loopElements>

                    <!-- 如果没有LSApplicationQueriesSchemes，则创建 -->
                    <if condition="bApplicationQueriesSchemesFound">
                        <false>
                            <addElements tag="$" once="true">
                                <key>LSApplicationQueriesSchemes</key>
                                <array>
                                </array>
                            </addElements>
                        </false>
                    </if>
                    <!-- 添加三方平台LSApplicationQueriesSchemes-->
                    <loopElements tag="$">
                        <setStringFromTagText result="TagIteratorValue" tag="$"/>
                        <setBoolIsEqual result="isApplicationQueriesSchemes" arg1="$S(TagIteratorValue)" arg2="LSApplicationQueriesSchemes"/>
                        <if condition="bApplicationQueriesSchemesNextElement">
                            <true>
                                <setBool result="bApplicationQueriesSchemesNextElement" value="false"/>
                                <addElements tag="$">
                                    <string>naversearchthirdlogin</string>
                                    <string>naversearchapp</string>
                                    <string>navercafe</string>
                                    <string>fbapi</string>
                                    <string>fb-messenger-share-api</string>
                                    <string>fbauth2</string>
                                    <string>fbshareextension</string>
                                    <string>instagram</string>
                                    <string>instagram-stories</string>
                                    <string>vk</string>
                                    <string>vk-share</string>
                                    <string>vkauthorize</string>
                                    <string>weixinULAPI</string>
                                    <string>weixin</string>
                                    <string>wechat</string>
                                    <string>line</string>
                                    <string>lineauth2</string>
                                    <string>twitter</string>
                                    <string>twitterauth</string>
                                    <string>yandexauth</string>
                                    <string>yandexauth2</string>
                                    <string>tiktokopensdk</string>
                                    <string>tiktoksharesdk</string>
                                    <string>snssdk1180</string>
                                    <string>snssdk1233</string>
                                </addElements>
                            </true>
                        </if>
                        <if condition="isApplicationQueriesSchemes">
                            <true>
                                <setBool result="bApplicationQueriesSchemesNextElement" value="true"/>
                            </true>
                        </if>
                    </loopElements>
                </true>
            </if>
        </loopElements>
        <!-- 添加Info.plist中其他值-->
        <addElements tag="dict" once="true">
            <key>NSAdvertisingAttributionReportEndpoint</key>
            <string>https://appsflyer-skadnetwork.com</string>
        </addElements>
        <!--  Info.plist添加 facebook 参数  -->
        <setBoolIsEqual result="isSetURLTypesFacebookNull" arg1="$S(sFacebookAppID)" arg2=""/>
        <if condition="isSetURLTypesFacebookNull">
            <false>
                <addElements tag="dict" once="true">
                    <key>FacebookAppID</key>
                </addElements>
                <setElement result="eFacebookAppID" value="string" text="$S(sFacebookAppID)"/>
                <addElement tag="dict" once="true" name="eFacebookAppID"/>

                <addElements tag="dict" once="true">
                    <key>FacebookClientToken</key>
                </addElements>
                <setElement result="eFacebookClientToken" value="string" text="$S(sFacebookClientToken)"/>
                <addElement tag="dict" once="true" name="eFacebookClientToken"/>

                <addElements tag="dict" once="true">
                    <key>FacebookDisplayName</key>
                </addElements>
                <setElement result="eFacebookDisplayName" value="string" text="$S(sFacebookDisplayName)"/>
                <addElement tag="dict" once="true" name="eFacebookDisplayName"/>
            </false>
        </if>
        <!--  Info.plist添加 Line 参数  -->
        <setBoolIsEqual result="isSetURLTypeLineNull" arg1="$S(sLineChannelID)" arg2=""/>
        <if condition="isSetURLTypeLineNull">
            <false>
                <addElements tag="dict" once="true">
                    <key>LineSDKConfig</key>
                </addElements>
                <setElement result="eLineChannelID" xml="&lt;dict&gt;&lt;key&gt;ChannelID&lt;/key&gt;&lt;string&gt;$S(sLineChannelID)&lt;/string&gt;&lt;/dict&gt;"/>
                <addElement tag="dict" once="true" name="eLineChannelID"/>
            </false>
        </if>
        <!--  Info.plist添加 TikTok 参数  -->
        <setBoolIsEqual result="isSetURLTypesTiktokNull" arg1="$S(sTikTokAppID)" arg2=""/>
        <if condition="isSetURLTypesTiktokNull">
            <false>
                <addElements tag="dict" once="true">
                    <key>TikTokAppID</key>
                </addElements>
                <setElement result="sTikTokAppID" value="string" text="$S(sTikTokAppID)"/>
                <addElement tag="dict" once="true" name="sTikTokAppID"/>
            </false>
        </if>
    </iosPListUpdates>
</root>
