[CommonSettings]
;源路径,存放源文件的位置
SourcePath = Plugins/OneEngineSDK/Content/Localization/OneEngineSDK
;目标路径,生成的本地化文件将存放在这里
DestinationPath = Plugins/OneEngineSDK/Content/Localization/OneEngineSDK
;清单文件名
ManifestName = OneEngineSDK.manifest
;存档文件名
ArchiveName = OneEngineSDK.archive
;可移植对象文件名
PortableObjectName = OneEngineSDK.po
;资源文件名
ResourceName = OneEngineSDK.locres
;原生文化/语言
NativeCulture = zh-Hans
;需要生成的文化/语言列表
CulturesToGenerate = de
CulturesToGenerate = en
CulturesToGenerate = es
CulturesToGenerate = fr
CulturesToGenerate = id
CulturesToGenerate = ja
CulturesToGenerate = ko
CulturesToGenerate = pt
CulturesToGenerate = ru
CulturesToGenerate = th
CulturesToGenerate = zh
CulturesToGenerate = zh-CN
CulturesToGenerate = zh-Hans
CulturesToGenerate = zh-Hant

;从源代码中收集文本
[GatherTextStep0]
CommandletClass = GatherTextFromSource
; 搜索目录路径
SearchDirectoryPaths = Plugins/OneEngineSDK/Source/OneEngineSDK
FileNameFilters = *.cpp
FileNameFilters = *.h
FileNameFilters = *.c
FileNameFilters = *.inl
FileNameFilters = *.mm
FileNameFilters = *.ini
; 是否从仅编辑器数据中收集文本
ShouldGatherFromEditorOnlyData = false

;从资产中收集文本
[GatherTextStep1]
CommandletClass = GatherTextFromAssets
IncludePathFilters = Plugins/OneEngineSDK/Content/*
PackageFileNameFilters = *.umap
PackageFileNameFilters = *.uasset
; 是否修复损坏的文本
bFixBroken = false
; 是否从仅编辑器数据中收集文本
ShouldGatherFromEditorOnlyData = false

;生成收集清单
[GatherTextStep2]
CommandletClass = GenerateGatherManifest

;生成收集存档
[GatherTextStep3]
CommandletClass = GenerateGatherArchive

[GatherTextStep4]
CommandletClass=InternationalizationExport
bExportLoc=true
ShouldPersistCommentsOnExport=false
ShouldAddSourceLocationsAsComments=true

;生成文本本地化报告
[GatherTextStep5]
CommandletClass = GenerateTextLocalizationReport
bWordCountReport = true
WordCountReportName = OneEngineSDK.csv
bConflictReport = true
ConflictReportName = OneEngineSDK_Conflicts.txt


