[CommonSettings]
;源路径,存放源文件的位置
SourcePath = Plugins/OneEngineSDK/Content/Localization/OneEngineSDK
;目标路径,生成的本地化文件将存放在这里
DestinationPath = Plugins/OneEngineSDK/Content/Localization/OneEngineSDK
;清单文件名
ManifestName = OneEngineSDK.manifest
;存档文件名
ArchiveName = OneEngineSDK.archive
;可移植对象文件名
PortableObjectName = OneEngineSDK.po
;资源文件名
ResourceName = OneEngineSDK.locres
;原生文化/语言
NativeCulture = zh-Hans
;需要生成的文化/语言列表
CulturesToGenerate = de
CulturesToGenerate = en
CulturesToGenerate = es
CulturesToGenerate = fr
CulturesToGenerate = id
CulturesToGenerate = ja
CulturesToGenerate = ko
CulturesToGenerate = pt
CulturesToGenerate = ru
CulturesToGenerate = th
CulturesToGenerate = zh
CulturesToGenerate = zh-CN
CulturesToGenerate = zh-Hans
CulturesToGenerate = zh-Hant

;Gather text from source code
[GatherTextStep0]
CommandletClass=GenerateTextLocalizationReport
bWordCountReport=true
WordCountReportName=OneEngineSDK.csv
