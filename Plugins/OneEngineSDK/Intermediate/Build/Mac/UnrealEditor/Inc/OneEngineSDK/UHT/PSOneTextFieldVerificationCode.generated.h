// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSOneTextFieldVerificationCode.h"

#ifdef ONEENGINESDK_PSOneTextFieldVerificationCode_generated_h
#error "PSOneTextFieldVerificationCode.generated.h already included, missing '#pragma once' in PSOneTextFieldVerificationCode.h"
#endif
#define ONEENGINESDK_PSOneTextFieldVerificationCode_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UPSOneTextFieldVerificationCode ******************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldVerificationCode_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldVerificationCode_h_15_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneTextFieldVerificationCode(); \
	friend struct Z_Construct_UClass_UPSOneTextFieldVerificationCode_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldVerificationCode_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSOneTextFieldVerificationCode, UPSOneTextFieldBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSOneTextFieldVerificationCode_NoRegister) \
	DECLARE_SERIALIZER(UPSOneTextFieldVerificationCode)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldVerificationCode_h_15_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneTextFieldVerificationCode(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSOneTextFieldVerificationCode(UPSOneTextFieldVerificationCode&&) = delete; \
	UPSOneTextFieldVerificationCode(const UPSOneTextFieldVerificationCode&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneTextFieldVerificationCode); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneTextFieldVerificationCode); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneTextFieldVerificationCode) \
	NO_API virtual ~UPSOneTextFieldVerificationCode();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldVerificationCode_h_12_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldVerificationCode_h_15_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldVerificationCode_h_15_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldVerificationCode_h_15_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSOneTextFieldVerificationCode;

// ********** End Class UPSOneTextFieldVerificationCode ********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldVerificationCode_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
