// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneLogin.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneLogin() {}

// ********** Begin Cross Module References ********************************************************
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneConfirmButton_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusCheckButton_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneLogin();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneLogin_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldBase_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldVerificationCode_NoRegister();
UMG_API UClass* Z_Construct_UClass_UBackgroundBlur_NoRegister();
UMG_API UClass* Z_Construct_UClass_UHorizontalBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_USizeBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
UMG_API UClass* Z_Construct_UClass_UVerticalBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UWidgetSwitcher_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneLogin **************************************************************
void UPSOneLogin::StaticRegisterNativesUPSOneLogin()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneLogin;
UClass* UPSOneLogin::GetPrivateStaticClass()
{
	using TClass = UPSOneLogin;
	if (!Z_Registration_Info_UClass_UPSOneLogin.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneLogin"),
			Z_Registration_Info_UClass_UPSOneLogin.InnerSingleton,
			StaticRegisterNativesUPSOneLogin,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneLogin.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneLogin_NoRegister()
{
	return UPSOneLogin::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneLogin_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * \xe7\x99\xbb\xe5\xbd\x95\xe7\x95\x8c\xe9\x9d\xa2\xe6\x8e\xa7\xe4\xbb\xb6\n * \xe6\x94\xaf\xe6\x8c\x81\xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xe5\x92\x8c\xe6\xb5\xb7\xe5\xa4\x96\xe7\x89\x88\xe4\xb8\xa4\xe7\xa7\x8d\xe6\xa8\xa1\xe5\xbc\x8f\n * \xe6\x94\xaf\xe6\x8c\x81\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe5\x92\x8c\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe4\xb8\xa4\xe7\xa7\x8d\xe6\x96\xb9\xe5\xbc\x8f\n * \xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xe8\xbf\x98\xe6\x94\xaf\xe6\x8c\x81\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\n */" },
#endif
		{ "IncludePath", "Views/PSOneLogin.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x99\xbb\xe5\xbd\x95\xe7\x95\x8c\xe9\x9d\xa2\xe6\x8e\xa7\xe4\xbb\xb6\n\xe6\x94\xaf\xe6\x8c\x81\xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xe5\x92\x8c\xe6\xb5\xb7\xe5\xa4\x96\xe7\x89\x88\xe4\xb8\xa4\xe7\xa7\x8d\xe6\xa8\xa1\xe5\xbc\x8f\n\xe6\x94\xaf\xe6\x8c\x81\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe5\x92\x8c\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe4\xb8\xa4\xe7\xa7\x8d\xe6\x96\xb9\xe5\xbc\x8f\n\xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xe8\xbf\x98\xe6\x94\xaf\xe6\x8c\x81\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackgroundBlur_MetaData[] = {
		{ "bindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\x83\x8c\xe6\x99\xaf\xe6\xa8\xa1\xe7\xb3\x8a\xe7\xbb\x84\xe4\xbb\xb6\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\x83\x8c\xe6\x99\xaf\xe6\xa8\xa1\xe7\xb3\x8a\xe7\xbb\x84\xe4\xbb\xb6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ContentVerticalBox_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x86\x85\xe5\xae\xb9\xe5\x8c\x85\xe8\xa3\xb9\xe5\x99\xa8\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe5\x9e\x82\xe7\x9b\xb4\xe5\xb8\x83\xe5\xb1\x80\xe5\x86\x85\xe5\xae\xb9\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x86\x85\xe5\xae\xb9\xe5\x8c\x85\xe8\xa3\xb9\xe5\x99\xa8\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe5\x9e\x82\xe7\x9b\xb4\xe5\xb8\x83\xe5\xb1\x80\xe5\x86\x85\xe5\xae\xb9" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ContentSizeBox_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x86\x85\xe5\xae\xb9\xe5\xb0\xba\xe5\xaf\xb8\xe7\x9b\x92\xef\xbc\x8c\xe6\xa0\xb9\xe6\x8d\xae\xe7\x89\x88\xe6\x9c\xac\xe8\xae\xbe\xe7\xbd\xae\xe4\xb8\x8d\xe5\x90\x8c\xe7\x9a\x84\xe5\xae\xbd\xe5\xba\xa6 (860 for mainland, 680 for global)\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x86\x85\xe5\xae\xb9\xe5\xb0\xba\xe5\xaf\xb8\xe7\x9b\x92\xef\xbc\x8c\xe6\xa0\xb9\xe6\x8d\xae\xe7\x89\x88\xe6\x9c\xac\xe8\xae\xbe\xe7\xbd\xae\xe4\xb8\x8d\xe5\x90\x8c\xe7\x9a\x84\xe5\xae\xbd\xe5\xba\xa6 (860 for mainland, 680 for global)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoginVerticalBox_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x99\xbb\xe5\xbd\x95\xe5\x9e\x82\xe7\x9b\xb4\xe6\xa1\x86\xef\xbc\x8c\xe5\x8c\x85\xe5\x90\xab\xe7\x99\xbb\xe5\xbd\x95\xe7\x9b\xb8\xe5\x85\xb3\xe6\x8e\xa7\xe4\xbb\xb6\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x99\xbb\xe5\xbd\x95\xe5\x9e\x82\xe7\x9b\xb4\xe6\xa1\x86\xef\xbc\x8c\xe5\x8c\x85\xe5\x90\xab\xe7\x99\xbb\xe5\xbd\x95\xe7\x9b\xb8\xe5\x85\xb3\xe6\x8e\xa7\xe4\xbb\xb6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VerticalSplitLine_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x9e\x82\xe7\x9b\xb4\xe5\x88\x86\xe5\x89\xb2\xe7\xba\xbf\xef\xbc\x8c\xe5\x88\x86\xe9\x9a\x94\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x99\xbb\xe5\xbd\x95\xe5\x92\x8c\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x9e\x82\xe7\x9b\xb4\xe5\x88\x86\xe5\x89\xb2\xe7\xba\xbf\xef\xbc\x8c\xe5\x88\x86\xe9\x9a\x94\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x99\xbb\xe5\xbd\x95\xe5\x92\x8c\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QRCodeVerticalBox_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe5\x9e\x82\xe7\x9b\xb4\xe6\xa1\x86\xef\xbc\x8c\xe5\x8c\x85\xe5\x90\xab\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe7\x9b\xb8\xe5\x85\xb3\xe6\x8e\xa7\xe4\xbb\xb6\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe5\x9e\x82\xe7\x9b\xb4\xe6\xa1\x86\xef\xbc\x8c\xe5\x8c\x85\xe5\x90\xab\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe7\x9b\xb8\xe5\x85\xb3\xe6\x8e\xa7\xe4\xbb\xb6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LogoIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// logo \xe5\x9b\xbe\xe6\xa0\x87\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "logo \xe5\x9b\xbe\xe6\xa0\x87" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoginTypeTextBlock_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x99\xbb\xe5\xbd\x95\xe7\xb1\xbb\xe5\x9e\x8b\xe6\x96\x87\xe6\x9c\xac\xef\xbc\x8c\xe6\x98\xbe\xe7\xa4\xba\xe5\xbd\x93\xe5\x89\x8d\xe7\x99\xbb\xe5\xbd\x95\xe6\x96\xb9\xe5\xbc\x8f\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x99\xbb\xe5\xbd\x95\xe7\xb1\xbb\xe5\x9e\x8b\xe6\x96\x87\xe6\x9c\xac\xef\xbc\x8c\xe6\x98\xbe\xe7\xa4\xba\xe5\xbd\x93\xe5\x89\x8d\xe7\x99\xbb\xe5\xbd\x95\xe6\x96\xb9\xe5\xbc\x8f" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccountField_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xb4\xa6\xe5\x8f\xb7\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xb4\xa6\xe5\x8f\xb7\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PasswordSwitcher_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xaf\x86\xe7\xa0\x81\xe5\x88\x87\xe6\x8d\xa2\xe5\x99\xa8\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe5\x88\x87\xe6\x8d\xa2\xe5\xaf\x86\xe7\xa0\x81\xe5\x92\x8c\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xaf\x86\xe7\xa0\x81\xe5\x88\x87\xe6\x8d\xa2\xe5\x99\xa8\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe5\x88\x87\xe6\x8d\xa2\xe5\xaf\x86\xe7\xa0\x81\xe5\x92\x8c\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PasswordField_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xaf\x86\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xaf\x86\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VerificationCodeField_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AgreePolicyCheckButton_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe9\x98\x85\xe8\xaf\xbb\xe5\xb9\xb6\xe5\x90\x8c\xe6\x84\x8f\xe5\x8d\x8f\xe8\xae\xae\xe5\xa4\x8d\xe9\x80\x89\xe6\xa1\x86\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe9\x98\x85\xe8\xaf\xbb\xe5\xb9\xb6\xe5\x90\x8c\xe6\x84\x8f\xe5\x8d\x8f\xe8\xae\xae\xe5\xa4\x8d\xe9\x80\x89\xe6\xa1\x86" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoginButton_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x99\xbb\xe5\xbd\x95\xe6\x8c\x89\xe9\x92\xae\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x99\xbb\xe5\xbd\x95\xe6\x8c\x89\xe9\x92\xae" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QRCodeImage_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe5\x9b\xbe\xe5\x83\x8f\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe5\x9b\xbe\xe5\x83\x8f" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RefreshQRCodeImage_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x88\xb7\xe6\x96\xb0\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe5\x9b\xbe\xe5\x83\x8f\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x88\xb7\xe6\x96\xb0\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe5\x9b\xbe\xe5\x83\x8f" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoginTypeSwitcher_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x99\xbb\xe5\xbd\x95\xe7\xb1\xbb\xe5\x9e\x8b\xe5\x88\x87\xe6\x8d\xa2\xe5\x99\xa8\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x99\xbb\xe5\xbd\x95\xe7\xb1\xbb\xe5\x9e\x8b\xe5\x88\x87\xe6\x8d\xa2\xe5\x99\xa8" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SendCodeIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x8f\x91\xe9\x80\x81\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe6\x96\xb9\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x8f\x91\xe9\x80\x81\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe6\x96\xb9\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CodeLoginIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe4\xb8\x89\xe8\xa7\x92\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe4\xb8\x89\xe8\xa7\x92\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccountLoginIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xb4\xa6\xe5\x8f\xb7\xe7\x99\xbb\xe5\xbd\x95\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe4\xb8\x89\xe8\xa7\x92\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x99\xbb\xe5\xbd\x95\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe4\xb8\x89\xe8\xa7\x92\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmailSwitchLoginHorizontalBox_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe9\x82\xae\xe7\xae\xb1\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe6\xb0\xb4\xe5\xb9\xb3\xe6\xa1\x86\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe9\x82\xae\xe7\xae\xb1\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe6\xb0\xb4\xe5\xb9\xb3\xe6\xa1\x86" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SwitchLoginTypeTextBlock_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x88\x87\xe6\x8d\xa2\xe7\x99\xbb\xe5\xbd\x95\xe6\x96\xb9\xe5\xbc\x8f\xe6\x96\x87\xe6\x9c\xac\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x88\x87\xe6\x8d\xa2\xe7\x99\xbb\xe5\xbd\x95\xe6\x96\xb9\xe5\xbc\x8f\xe6\x96\x87\xe6\x9c\xac" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\xa1\xae\xe8\xae\xa4\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe5\x9c\x86\xe5\xbd\xa2\xe6\x88\x96\xe5\x8f\x89\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\xa1\xae\xe8\xae\xa4\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe5\x9c\x86\xe5\xbd\xa2\xe6\x88\x96\xe5\x8f\x89\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xbf\x94\xe5\x9b\x9e\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe5\x9c\x86\xe5\xbd\xa2\xe6\x88\x96\xe5\x8f\x89\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xbf\x94\xe5\x9b\x9e\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe5\x9c\x86\xe5\xbd\xa2\xe6\x88\x96\xe5\x8f\x89\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsMainLand_MetaData[] = {
		{ "Category", "PSOneLogin" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba\xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xef\xbc\x8c\xe5\xbd\xb1\xe5\x93\x8d\xe7\x99\xbb\xe5\xbd\x95\xe7\x95\x8c\xe9\x9d\xa2\xe7\x9a\x84\xe5\xb8\x83\xe5\xb1\x80\xe5\x92\x8c\xe6\x8f\x90\xe7\xa4\xba\xe6\x96\x87\xe6\x9c\xac\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba\xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xef\xbc\x8c\xe5\xbd\xb1\xe5\x93\x8d\xe7\x99\xbb\xe5\xbd\x95\xe7\x95\x8c\xe9\x9d\xa2\xe7\x9a\x84\xe5\xb8\x83\xe5\xb1\x80\xe5\x92\x8c\xe6\x8f\x90\xe7\xa4\xba\xe6\x96\x87\xe6\x9c\xac" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MainLandContentWidth_MetaData[] = {
		{ "Category", "PSOneLogin" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xe5\x86\x85\xe5\xae\xb9\xe5\xae\xbd\xe5\xba\xa6\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xe5\x86\x85\xe5\xae\xb9\xe5\xae\xbd\xe5\xba\xa6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalContentWidth_MetaData[] = {
		{ "Category", "PSOneLogin" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\xb5\xb7\xe5\xa4\x96\xe7\x89\x88\xe5\x86\x85\xe5\xae\xb9\xe5\xae\xbd\xe5\xba\xa6\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\xb5\xb7\xe5\xa4\x96\xe7\x89\x88\xe5\x86\x85\xe5\xae\xb9\xe5\xae\xbd\xe5\xba\xa6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsLoginWithPassword_MetaData[] = {
		{ "Category", "PSOneLogin" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe4\xbd\xbf\xe7\x94\xa8\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xef\xbc\x8c""false \xe8\xa1\xa8\xe7\xa4\xba\xe4\xbd\xbf\xe7\x94\xa8\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe4\xbd\xbf\xe7\x94\xa8\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xef\xbc\x8c""false \xe8\xa1\xa8\xe7\xa4\xba\xe4\xbd\xbf\xe7\x94\xa8\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsSupportEmailPasswordLogin_MetaData[] = {
		{ "Category", "PSOneLogin" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x94\xaf\xe6\x8c\x81\xe9\x82\xae\xe7\xae\xb1\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x94\xaf\xe6\x8c\x81\xe9\x82\xae\xe7\xae\xb1\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsIwPlay_MetaData[] = {
		{ "Category", "PSOneLogin" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PwrdLogo_MetaData[] = {
		{ "Category", "PSOneLogin" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IwPlayLogo_MetaData[] = {
		{ "Category", "PSOneLogin" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BackgroundBlur;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ContentVerticalBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ContentSizeBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LoginVerticalBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VerticalSplitLine;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_QRCodeVerticalBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LogoIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LoginTypeTextBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AccountField;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PasswordSwitcher;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PasswordField;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VerificationCodeField;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AgreePolicyCheckButton;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LoginButton;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_QRCodeImage;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RefreshQRCodeImage;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LoginTypeSwitcher;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SendCodeIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CodeLoginIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AccountLoginIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EmailSwitchLoginHorizontalBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SwitchLoginTypeTextBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
	static void NewProp_bIsMainLand_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMainLand;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MainLandContentWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalContentWidth;
	static void NewProp_bIsLoginWithPassword_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsLoginWithPassword;
	static void NewProp_bIsSupportEmailPasswordLogin_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsSupportEmailPasswordLogin;
	static void NewProp_bIsIwPlay_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsIwPlay;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PwrdLogo;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IwPlayLogo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneLogin>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleBox_MetaData), NewProp_ScaleBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_BackgroundBlur = { "BackgroundBlur", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, BackgroundBlur), Z_Construct_UClass_UBackgroundBlur_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackgroundBlur_MetaData), NewProp_BackgroundBlur_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ContentVerticalBox = { "ContentVerticalBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, ContentVerticalBox), Z_Construct_UClass_UVerticalBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ContentVerticalBox_MetaData), NewProp_ContentVerticalBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ContentSizeBox = { "ContentSizeBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, ContentSizeBox), Z_Construct_UClass_USizeBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ContentSizeBox_MetaData), NewProp_ContentSizeBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginVerticalBox = { "LoginVerticalBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, LoginVerticalBox), Z_Construct_UClass_UVerticalBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoginVerticalBox_MetaData), NewProp_LoginVerticalBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_VerticalSplitLine = { "VerticalSplitLine", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, VerticalSplitLine), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VerticalSplitLine_MetaData), NewProp_VerticalSplitLine_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_QRCodeVerticalBox = { "QRCodeVerticalBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, QRCodeVerticalBox), Z_Construct_UClass_UVerticalBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QRCodeVerticalBox_MetaData), NewProp_QRCodeVerticalBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LogoIcon = { "LogoIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, LogoIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LogoIcon_MetaData), NewProp_LogoIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginTypeTextBlock = { "LoginTypeTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, LoginTypeTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoginTypeTextBlock_MetaData), NewProp_LoginTypeTextBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AccountField = { "AccountField", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, AccountField), Z_Construct_UClass_UPSOneTextFieldBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccountField_MetaData), NewProp_AccountField_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PasswordSwitcher = { "PasswordSwitcher", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, PasswordSwitcher), Z_Construct_UClass_UWidgetSwitcher_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PasswordSwitcher_MetaData), NewProp_PasswordSwitcher_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PasswordField = { "PasswordField", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, PasswordField), Z_Construct_UClass_UPSOneTextFieldBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PasswordField_MetaData), NewProp_PasswordField_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_VerificationCodeField = { "VerificationCodeField", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, VerificationCodeField), Z_Construct_UClass_UPSOneTextFieldVerificationCode_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VerificationCodeField_MetaData), NewProp_VerificationCodeField_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AgreePolicyCheckButton = { "AgreePolicyCheckButton", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, AgreePolicyCheckButton), Z_Construct_UClass_UPSOneFocusCheckButton_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AgreePolicyCheckButton_MetaData), NewProp_AgreePolicyCheckButton_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginButton = { "LoginButton", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, LoginButton), Z_Construct_UClass_UPSOneConfirmButton_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoginButton_MetaData), NewProp_LoginButton_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_QRCodeImage = { "QRCodeImage", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, QRCodeImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QRCodeImage_MetaData), NewProp_QRCodeImage_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_RefreshQRCodeImage = { "RefreshQRCodeImage", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, RefreshQRCodeImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RefreshQRCodeImage_MetaData), NewProp_RefreshQRCodeImage_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginTypeSwitcher = { "LoginTypeSwitcher", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, LoginTypeSwitcher), Z_Construct_UClass_UWidgetSwitcher_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoginTypeSwitcher_MetaData), NewProp_LoginTypeSwitcher_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_SendCodeIcon = { "SendCodeIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, SendCodeIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SendCodeIcon_MetaData), NewProp_SendCodeIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_CodeLoginIcon = { "CodeLoginIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, CodeLoginIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CodeLoginIcon_MetaData), NewProp_CodeLoginIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AccountLoginIcon = { "AccountLoginIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, AccountLoginIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccountLoginIcon_MetaData), NewProp_AccountLoginIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_EmailSwitchLoginHorizontalBox = { "EmailSwitchLoginHorizontalBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, EmailSwitchLoginHorizontalBox), Z_Construct_UClass_UHorizontalBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmailSwitchLoginHorizontalBox_MetaData), NewProp_EmailSwitchLoginHorizontalBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_SwitchLoginTypeTextBlock = { "SwitchLoginTypeTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, SwitchLoginTypeTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SwitchLoginTypeTextBlock_MetaData), NewProp_SwitchLoginTypeTextBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnterIcon_MetaData), NewProp_EnterIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackspaceIcon_MetaData), NewProp_BackspaceIcon_MetaData) };
void Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsMainLand_SetBit(void* Obj)
{
	((UPSOneLogin*)Obj)->bIsMainLand = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsMainLand = { "bIsMainLand", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPSOneLogin), &Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsMainLand_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsMainLand_MetaData), NewProp_bIsMainLand_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_MainLandContentWidth = { "MainLandContentWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, MainLandContentWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MainLandContentWidth_MetaData), NewProp_MainLandContentWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_GlobalContentWidth = { "GlobalContentWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, GlobalContentWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalContentWidth_MetaData), NewProp_GlobalContentWidth_MetaData) };
void Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsLoginWithPassword_SetBit(void* Obj)
{
	((UPSOneLogin*)Obj)->bIsLoginWithPassword = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsLoginWithPassword = { "bIsLoginWithPassword", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPSOneLogin), &Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsLoginWithPassword_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsLoginWithPassword_MetaData), NewProp_bIsLoginWithPassword_MetaData) };
void Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsSupportEmailPasswordLogin_SetBit(void* Obj)
{
	((UPSOneLogin*)Obj)->bIsSupportEmailPasswordLogin = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsSupportEmailPasswordLogin = { "bIsSupportEmailPasswordLogin", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPSOneLogin), &Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsSupportEmailPasswordLogin_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsSupportEmailPasswordLogin_MetaData), NewProp_bIsSupportEmailPasswordLogin_MetaData) };
void Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsIwPlay_SetBit(void* Obj)
{
	((UPSOneLogin*)Obj)->bIsIwPlay = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsIwPlay = { "bIsIwPlay", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPSOneLogin), &Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsIwPlay_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsIwPlay_MetaData), NewProp_bIsIwPlay_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PwrdLogo = { "PwrdLogo", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, PwrdLogo), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PwrdLogo_MetaData), NewProp_PwrdLogo_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_IwPlayLogo = { "IwPlayLogo", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLogin, IwPlayLogo), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IwPlayLogo_MetaData), NewProp_IwPlayLogo_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneLogin_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ScaleBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_BackgroundBlur,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ContentVerticalBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ContentSizeBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginVerticalBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_VerticalSplitLine,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_QRCodeVerticalBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LogoIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginTypeTextBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AccountField,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PasswordSwitcher,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PasswordField,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_VerificationCodeField,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AgreePolicyCheckButton,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginButton,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_QRCodeImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_RefreshQRCodeImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginTypeSwitcher,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_SendCodeIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_CodeLoginIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AccountLoginIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_EmailSwitchLoginHorizontalBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_SwitchLoginTypeTextBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_EnterIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_BackspaceIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsMainLand,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_MainLandContentWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_GlobalContentWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsLoginWithPassword,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsSupportEmailPasswordLogin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsIwPlay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PwrdLogo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_IwPlayLogo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneLogin_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneLogin_Statics::ClassParams = {
	&UPSOneLogin::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneLogin_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneLogin_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneLogin()
{
	if (!Z_Registration_Info_UClass_UPSOneLogin.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneLogin.OuterSingleton, Z_Construct_UClass_UPSOneLogin_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneLogin.OuterSingleton;
}
UPSOneLogin::UPSOneLogin(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneLogin);
UPSOneLogin::~UPSOneLogin() {}
// ********** End Class UPSOneLogin ****************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneLogin_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneLogin, UPSOneLogin::StaticClass, TEXT("UPSOneLogin"), &Z_Registration_Info_UClass_UPSOneLogin, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneLogin), 642347751U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneLogin_h__Script_OneEngineSDK_1951249902(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneLogin_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneLogin_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
