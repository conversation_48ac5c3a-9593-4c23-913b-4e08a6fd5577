// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSOneInputPhoneNum.h"

#ifdef ONEENGINESDK_PSOneInputPhoneNum_generated_h
#error "PSOneInputPhoneNum.generated.h already included, missing '#pragma once' in PSOneInputPhoneNum.h"
#endif
#define ONEENGINESDK_PSOneInputPhoneNum_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UPSOneInputPhoneNum ******************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneInputPhoneNum_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneInputPhoneNum(); \
	friend struct Z_Construct_UClass_UPSOneInputPhoneNum_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneInputPhoneNum_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSOneInputPhoneNum, UPSOneFocusUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSOneInputPhoneNum_NoRegister) \
	DECLARE_SERIALIZER(UPSOneInputPhoneNum)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneInputPhoneNum(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSOneInputPhoneNum(UPSOneInputPhoneNum&&) = delete; \
	UPSOneInputPhoneNum(const UPSOneInputPhoneNum&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneInputPhoneNum); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneInputPhoneNum); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneInputPhoneNum) \
	NO_API virtual ~UPSOneInputPhoneNum();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_24_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSOneInputPhoneNum;

// ********** End Class UPSOneInputPhoneNum ********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h

// ********** Begin Enum EPSOneInputPhoneNumType ***************************************************
#define FOREACH_ENUM_EPSONEINPUTPHONENUMTYPE(op) \
	op(EPSOneInputPhoneNumType::PhoneNum) \
	op(EPSOneInputPhoneNumType::Email) 

enum class EPSOneInputPhoneNumType : uint8;
template<> struct TIsUEnumClass<EPSOneInputPhoneNumType> { enum { Value = true }; };
template<> ONEENGINESDK_API UEnum* StaticEnum<EPSOneInputPhoneNumType>();
// ********** End Enum EPSOneInputPhoneNumType *****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
