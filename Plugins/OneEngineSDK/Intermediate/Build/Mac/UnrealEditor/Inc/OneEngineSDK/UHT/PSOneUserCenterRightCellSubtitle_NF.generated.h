// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSOneUserCenterRightCellSubtitle_NF.h"

#ifdef ONEENGINESDK_PSOneUserCenterRightCellSubtitle_NF_generated_h
#error "PSOneUserCenterRightCellSubtitle_NF.generated.h already included, missing '#pragma once' in PSOneUserCenterRightCellSubtitle_NF.h"
#endif
#define ONEENGINESDK_PSOneUserCenterRightCellSubtitle_NF_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UPSOneUserCenterRightCellSubtitle_NF *************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellSubtitle_NF_h_15_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneUserCenterRightCellSubtitle_NF(); \
	friend struct Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSOneUserCenterRightCellSubtitle_NF, UUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_NoRegister) \
	DECLARE_SERIALIZER(UPSOneUserCenterRightCellSubtitle_NF)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellSubtitle_NF_h_15_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneUserCenterRightCellSubtitle_NF(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSOneUserCenterRightCellSubtitle_NF(UPSOneUserCenterRightCellSubtitle_NF&&) = delete; \
	UPSOneUserCenterRightCellSubtitle_NF(const UPSOneUserCenterRightCellSubtitle_NF&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneUserCenterRightCellSubtitle_NF); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneUserCenterRightCellSubtitle_NF); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneUserCenterRightCellSubtitle_NF) \
	NO_API virtual ~UPSOneUserCenterRightCellSubtitle_NF();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellSubtitle_NF_h_12_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellSubtitle_NF_h_15_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellSubtitle_NF_h_15_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellSubtitle_NF_h_15_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSOneUserCenterRightCellSubtitle_NF;

// ********** End Class UPSOneUserCenterRightCellSubtitle_NF ***************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellSubtitle_NF_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
