// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSOneUserCenterIDInfo.h"

#ifdef ONEENGINESDK_PSOneUserCenterIDInfo_generated_h
#error "PSOneUserCenterIDInfo.generated.h already included, missing '#pragma once' in PSOneUserCenterIDInfo.h"
#endif
#define ONEENGINESDK_PSOneUserCenterIDInfo_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FPSOneIDInfoStruct ************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterIDInfo_h_13_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FPSOneIDInfoStruct;
// ********** End ScriptStruct FPSOneIDInfoStruct **************************************************

// ********** Begin Class UPSOneUserCenterIDInfo ***************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterIDInfo_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterIDInfo_h_25_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneUserCenterIDInfo(); \
	friend struct Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterIDInfo_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSOneUserCenterIDInfo, UPSOneFocusUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSOneUserCenterIDInfo_NoRegister) \
	DECLARE_SERIALIZER(UPSOneUserCenterIDInfo)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterIDInfo_h_25_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneUserCenterIDInfo(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSOneUserCenterIDInfo(UPSOneUserCenterIDInfo&&) = delete; \
	UPSOneUserCenterIDInfo(const UPSOneUserCenterIDInfo&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneUserCenterIDInfo); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneUserCenterIDInfo); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneUserCenterIDInfo) \
	NO_API virtual ~UPSOneUserCenterIDInfo();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterIDInfo_h_22_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterIDInfo_h_25_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterIDInfo_h_25_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterIDInfo_h_25_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSOneUserCenterIDInfo;

// ********** End Class UPSOneUserCenterIDInfo *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterIDInfo_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
