// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneUserCenterLeftCell.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneUserCenterLeftCell() {}

// ********** Begin Cross Module References ********************************************************
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterLeftCell();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneUserCenterLeftCell *************************************************
void UPSOneUserCenterLeftCell::StaticRegisterNativesUPSOneUserCenterLeftCell()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneUserCenterLeftCell;
UClass* UPSOneUserCenterLeftCell::GetPrivateStaticClass()
{
	using TClass = UPSOneUserCenterLeftCell;
	if (!Z_Registration_Info_UClass_UPSOneUserCenterLeftCell.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneUserCenterLeftCell"),
			Z_Registration_Info_UClass_UPSOneUserCenterLeftCell.InnerSingleton,
			StaticRegisterNativesUPSOneUserCenterLeftCell,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterLeftCell.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister()
{
	return UPSOneUserCenterLeftCell::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * \n */" },
#endif
		{ "IncludePath", "Views/PSOneUserCenterLeftCell.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterLeftCell.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImageIcon_MetaData[] = {
		{ "Category", "PSOneUserCenterLeftCell" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterLeftCell.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Image_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterLeftCell.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ImageIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Image;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterLeftCell>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics::NewProp_ImageIcon = { "ImageIcon", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterLeftCell, ImageIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImageIcon_MetaData), NewProp_ImageIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics::NewProp_Image = { "Image", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterLeftCell, Image), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Image_MetaData), NewProp_Image_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics::NewProp_ImageIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics::NewProp_Image,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneButtonBase,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics::ClassParams = {
	&UPSOneUserCenterLeftCell::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics::PropPointers),
	0,
	0x00A010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneUserCenterLeftCell()
{
	if (!Z_Registration_Info_UClass_UPSOneUserCenterLeftCell.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneUserCenterLeftCell.OuterSingleton, Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterLeftCell.OuterSingleton;
}
UPSOneUserCenterLeftCell::UPSOneUserCenterLeftCell(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterLeftCell);
UPSOneUserCenterLeftCell::~UPSOneUserCenterLeftCell() {}
// ********** End Class UPSOneUserCenterLeftCell ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterLeftCell_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneUserCenterLeftCell, UPSOneUserCenterLeftCell::StaticClass, TEXT("UPSOneUserCenterLeftCell"), &Z_Registration_Info_UClass_UPSOneUserCenterLeftCell, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneUserCenterLeftCell), 470930316U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterLeftCell_h__Script_OneEngineSDK_745018335(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterLeftCell_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterLeftCell_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
