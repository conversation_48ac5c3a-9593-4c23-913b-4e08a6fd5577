// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSOneLoginSuccessToast.h"

#ifdef ONEENGINESDK_PSOneLoginSuccessToast_generated_h
#error "PSOneLoginSuccessToast.generated.h already included, missing '#pragma once' in PSOneLoginSuccessToast.h"
#endif
#define ONEENGINESDK_PSOneLoginSuccessToast_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UPSOneLoginSuccessToast **************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneLoginSuccessToast_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneLoginSuccessToast_h_15_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneLoginSuccessToast(); \
	friend struct Z_Construct_UClass_UPSOneLoginSuccessToast_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneLoginSuccessToast_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSOneLoginSuccessToast, UUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSOneLoginSuccessToast_NoRegister) \
	DECLARE_SERIALIZER(UPSOneLoginSuccessToast)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneLoginSuccessToast_h_15_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneLoginSuccessToast(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSOneLoginSuccessToast(UPSOneLoginSuccessToast&&) = delete; \
	UPSOneLoginSuccessToast(const UPSOneLoginSuccessToast&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneLoginSuccessToast); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneLoginSuccessToast); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneLoginSuccessToast) \
	NO_API virtual ~UPSOneLoginSuccessToast();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneLoginSuccessToast_h_12_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneLoginSuccessToast_h_15_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneLoginSuccessToast_h_15_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneLoginSuccessToast_h_15_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSOneLoginSuccessToast;

// ********** End Class UPSOneLoginSuccessToast ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneLoginSuccessToast_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
