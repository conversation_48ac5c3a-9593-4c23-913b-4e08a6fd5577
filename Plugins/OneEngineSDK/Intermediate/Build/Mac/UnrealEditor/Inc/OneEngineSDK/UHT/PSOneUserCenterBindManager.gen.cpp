// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneUserCenterBindManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneUserCenterBindManager() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterBindManager();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterBindManager_NoRegister();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FPSOneBindItem();
UMG_API UClass* Z_Construct_UClass_UScrollBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FPSOneBindItem ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPSOneBindItem;
class UScriptStruct* FPSOneBindItem::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPSOneBindItem.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPSOneBindItem.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPSOneBindItem, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("PSOneBindItem"));
	}
	return Z_Registration_Info_UScriptStruct_FPSOneBindItem.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPSOneBindItem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterBindManager.h" },
	};
#endif // WITH_METADATA
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPSOneBindItem>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPSOneBindItem_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"PSOneBindItem",
	nullptr,
	0,
	sizeof(FPSOneBindItem),
	alignof(FPSOneBindItem),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneBindItem_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPSOneBindItem_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPSOneBindItem()
{
	if (!Z_Registration_Info_UScriptStruct_FPSOneBindItem.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPSOneBindItem.InnerSingleton, Z_Construct_UScriptStruct_FPSOneBindItem_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPSOneBindItem.InnerSingleton;
}
// ********** End ScriptStruct FPSOneBindItem ******************************************************

// ********** Begin Class UPSOneUserCenterBindManager **********************************************
void UPSOneUserCenterBindManager::StaticRegisterNativesUPSOneUserCenterBindManager()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneUserCenterBindManager;
UClass* UPSOneUserCenterBindManager::GetPrivateStaticClass()
{
	using TClass = UPSOneUserCenterBindManager;
	if (!Z_Registration_Info_UClass_UPSOneUserCenterBindManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneUserCenterBindManager"),
			Z_Registration_Info_UClass_UPSOneUserCenterBindManager.InnerSingleton,
			StaticRegisterNativesUPSOneUserCenterBindManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterBindManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneUserCenterBindManager_NoRegister()
{
	return UPSOneUserCenterBindManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneUserCenterBindManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * \n */" },
#endif
		{ "IncludePath", "Views/PSOneUserCenterBindManager.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterBindManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TitleBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterBindManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScrollBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterBindManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TitleBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ScrollBox;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterBindManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::NewProp_TitleBlock = { "TitleBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterBindManager, TitleBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TitleBlock_MetaData), NewProp_TitleBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::NewProp_ScrollBox = { "ScrollBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterBindManager, ScrollBox), Z_Construct_UClass_UScrollBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScrollBox_MetaData), NewProp_ScrollBox_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::NewProp_TitleBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::NewProp_ScrollBox,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::ClassParams = {
	&UPSOneUserCenterBindManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::PropPointers),
	0,
	0x00A010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneUserCenterBindManager()
{
	if (!Z_Registration_Info_UClass_UPSOneUserCenterBindManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneUserCenterBindManager.OuterSingleton, Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterBindManager.OuterSingleton;
}
UPSOneUserCenterBindManager::UPSOneUserCenterBindManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterBindManager);
UPSOneUserCenterBindManager::~UPSOneUserCenterBindManager() {}
// ********** End Class UPSOneUserCenterBindManager ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h__Script_OneEngineSDK_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPSOneBindItem::StaticStruct, Z_Construct_UScriptStruct_FPSOneBindItem_Statics::NewStructOps, TEXT("PSOneBindItem"), &Z_Registration_Info_UScriptStruct_FPSOneBindItem, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPSOneBindItem), 1721802004U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneUserCenterBindManager, UPSOneUserCenterBindManager::StaticClass, TEXT("UPSOneUserCenterBindManager"), &Z_Registration_Info_UClass_UPSOneUserCenterBindManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneUserCenterBindManager), 1401051920U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h__Script_OneEngineSDK_2747741434(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h__Script_OneEngineSDK_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h__Script_OneEngineSDK_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h__Script_OneEngineSDK_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
