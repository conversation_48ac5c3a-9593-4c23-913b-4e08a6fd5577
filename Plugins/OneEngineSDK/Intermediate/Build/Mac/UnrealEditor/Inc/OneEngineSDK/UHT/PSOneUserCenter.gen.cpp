// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneUserCenter.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneUserCenter() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneAvatarImage_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenter();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenter_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterAccountInfo_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterBindManager_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterDeviceManager_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterIDInfo_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterLegalTerms_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterOther_NoRegister();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FPSOneUserCenterModel();
UMG_API UClass* Z_Construct_UClass_UBackgroundBlur_NoRegister();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
UMG_API UClass* Z_Construct_UClass_UVerticalBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UWidgetSwitcher_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FPSOneUserCenterModel *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPSOneUserCenterModel;
class UScriptStruct* FPSOneUserCenterModel::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPSOneUserCenterModel.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPSOneUserCenterModel.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPSOneUserCenterModel, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("PSOneUserCenterModel"));
	}
	return Z_Registration_Info_UScriptStruct_FPSOneUserCenterModel.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UserName_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NickName_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellPhone_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShowCellPhone_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShowEmail_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeadImg_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealName_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IDNumber_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Email_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UserID_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OneID_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Token_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewUser_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UserIP_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HavePwd_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_UserName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NickName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellPhone;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ShowCellPhone;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ShowEmail;
	static const UECodeGen_Private::FStrPropertyParams NewProp_HeadImg;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RealName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_IDNumber;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Email;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UserID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OneID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Token;
	static void NewProp_NewUser_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_NewUser;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UserIP;
	static void NewProp_HavePwd_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_HavePwd;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPSOneUserCenterModel>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserName = { "UserName", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneUserCenterModel, UserName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UserName_MetaData), NewProp_UserName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NickName = { "NickName", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneUserCenterModel, NickName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NickName_MetaData), NewProp_NickName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_CellPhone = { "CellPhone", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneUserCenterModel, CellPhone), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellPhone_MetaData), NewProp_CellPhone_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_ShowCellPhone = { "ShowCellPhone", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneUserCenterModel, ShowCellPhone), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShowCellPhone_MetaData), NewProp_ShowCellPhone_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_ShowEmail = { "ShowEmail", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneUserCenterModel, ShowEmail), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShowEmail_MetaData), NewProp_ShowEmail_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HeadImg = { "HeadImg", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneUserCenterModel, HeadImg), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeadImg_MetaData), NewProp_HeadImg_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_RealName = { "RealName", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneUserCenterModel, RealName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealName_MetaData), NewProp_RealName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_IDNumber = { "IDNumber", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneUserCenterModel, IDNumber), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IDNumber_MetaData), NewProp_IDNumber_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_Email = { "Email", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneUserCenterModel, Email), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Email_MetaData), NewProp_Email_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserID = { "UserID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneUserCenterModel, UserID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UserID_MetaData), NewProp_UserID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_OneID = { "OneID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneUserCenterModel, OneID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OneID_MetaData), NewProp_OneID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_Token = { "Token", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneUserCenterModel, Token), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Token_MetaData), NewProp_Token_MetaData) };
void Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NewUser_SetBit(void* Obj)
{
	((FPSOneUserCenterModel*)Obj)->NewUser = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NewUser = { "NewUser", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPSOneUserCenterModel), &Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NewUser_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewUser_MetaData), NewProp_NewUser_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserIP = { "UserIP", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneUserCenterModel, UserIP), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UserIP_MetaData), NewProp_UserIP_MetaData) };
void Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HavePwd_SetBit(void* Obj)
{
	((FPSOneUserCenterModel*)Obj)->HavePwd = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HavePwd = { "HavePwd", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPSOneUserCenterModel), &Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HavePwd_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HavePwd_MetaData), NewProp_HavePwd_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NickName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_CellPhone,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_ShowCellPhone,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_ShowEmail,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HeadImg,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_RealName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_IDNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_Email,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_OneID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_Token,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NewUser,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserIP,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HavePwd,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"PSOneUserCenterModel",
	Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::PropPointers),
	sizeof(FPSOneUserCenterModel),
	alignof(FPSOneUserCenterModel),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPSOneUserCenterModel()
{
	if (!Z_Registration_Info_UScriptStruct_FPSOneUserCenterModel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPSOneUserCenterModel.InnerSingleton, Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPSOneUserCenterModel.InnerSingleton;
}
// ********** End ScriptStruct FPSOneUserCenterModel ***********************************************

// ********** Begin Class UPSOneUserCenter Function BindData ***************************************
struct Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics
{
	struct PSOneUserCenter_eventBindData_Parms
	{
		FString JsonString;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JsonString_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_JsonString;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::NewProp_JsonString = { "JsonString", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneUserCenter_eventBindData_Parms, JsonString), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JsonString_MetaData), NewProp_JsonString_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::NewProp_JsonString,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUserCenter, nullptr, "BindData", Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::PSOneUserCenter_eventBindData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::PSOneUserCenter_eventBindData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneUserCenter_BindData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUserCenter::execBindData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_JsonString);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BindData(Z_Param_JsonString);
	P_NATIVE_END;
}
// ********** End Class UPSOneUserCenter Function BindData *****************************************

// ********** Begin Class UPSOneUserCenter Function RefreshData ************************************
struct Z_Construct_UFunction_UPSOneUserCenter_RefreshData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUserCenter_RefreshData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUserCenter, nullptr, "RefreshData", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_RefreshData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUserCenter_RefreshData_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPSOneUserCenter_RefreshData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUserCenter_RefreshData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUserCenter::execRefreshData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RefreshData();
	P_NATIVE_END;
}
// ********** End Class UPSOneUserCenter Function RefreshData **************************************

// ********** Begin Class UPSOneUserCenter Function RefreshDeviceData ******************************
struct Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics
{
	struct PSOneUserCenter_eventRefreshDeviceData_Parms
	{
		FString JsonString;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JsonString_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_JsonString;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::NewProp_JsonString = { "JsonString", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneUserCenter_eventRefreshDeviceData_Parms, JsonString), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JsonString_MetaData), NewProp_JsonString_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::NewProp_JsonString,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUserCenter, nullptr, "RefreshDeviceData", Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::PSOneUserCenter_eventRefreshDeviceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::PSOneUserCenter_eventRefreshDeviceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUserCenter::execRefreshDeviceData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_JsonString);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RefreshDeviceData(Z_Param_JsonString);
	P_NATIVE_END;
}
// ********** End Class UPSOneUserCenter Function RefreshDeviceData ********************************

// ********** Begin Class UPSOneUserCenter Function UpdateBoundAccounts ****************************
struct Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics
{
	struct PSOneUserCenter_eventUpdateBoundAccounts_Parms
	{
		TMap<int32,bool> Accounts;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Accounts_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBoolPropertyParams NewProp_Accounts_ValueProp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Accounts_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Accounts;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::NewProp_Accounts_ValueProp = { "Accounts", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::NewProp_Accounts_Key_KeyProp = { "Accounts_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::NewProp_Accounts = { "Accounts", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneUserCenter_eventUpdateBoundAccounts_Parms, Accounts), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Accounts_MetaData), NewProp_Accounts_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::NewProp_Accounts_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::NewProp_Accounts_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::NewProp_Accounts,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUserCenter, nullptr, "UpdateBoundAccounts", Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::PSOneUserCenter_eventUpdateBoundAccounts_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::PSOneUserCenter_eventUpdateBoundAccounts_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUserCenter::execUpdateBoundAccounts)
{
	P_GET_TMAP_REF(int32,bool,Z_Param_Out_Accounts);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateBoundAccounts(Z_Param_Out_Accounts);
	P_NATIVE_END;
}
// ********** End Class UPSOneUserCenter Function UpdateBoundAccounts ******************************

// ********** Begin Class UPSOneUserCenter *********************************************************
void UPSOneUserCenter::StaticRegisterNativesUPSOneUserCenter()
{
	UClass* Class = UPSOneUserCenter::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "BindData", &UPSOneUserCenter::execBindData },
		{ "RefreshData", &UPSOneUserCenter::execRefreshData },
		{ "RefreshDeviceData", &UPSOneUserCenter::execRefreshDeviceData },
		{ "UpdateBoundAccounts", &UPSOneUserCenter::execUpdateBoundAccounts },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneUserCenter;
UClass* UPSOneUserCenter::GetPrivateStaticClass()
{
	using TClass = UPSOneUserCenter;
	if (!Z_Registration_Info_UClass_UPSOneUserCenter.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneUserCenter"),
			Z_Registration_Info_UClass_UPSOneUserCenter.InnerSingleton,
			StaticRegisterNativesUPSOneUserCenter,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenter.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneUserCenter_NoRegister()
{
	return UPSOneUserCenter::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneUserCenter_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "IncludePath", "Views/PSOneUserCenter.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackgroundBlur_MetaData[] = {
		{ "bindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ContentBox_MetaData[] = {
		{ "bindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvatarImage_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xa4\xb4\xe5\x83\x8f\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xa4\xb4\xe5\x83\x8f" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MainIdText_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Id\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Id" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NickTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NickText_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8d\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8d" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UIDTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UIDText_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// UID\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BindAccountTabCell_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\xbb\x91\xe5\xae\x9a\xe8\xb4\xa6\xe5\x8f\xb7 cell\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\xbb\x91\xe5\xae\x9a\xe8\xb4\xa6\xe5\x8f\xb7 cell" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccountTabCell_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xb4\xa6\xe5\x8f\xb7\xe4\xbf\xa1\xe6\x81\xaf cell\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xb4\xa6\xe5\x8f\xb7\xe4\xbf\xa1\xe6\x81\xaf cell" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealNameTabCell_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xae\x9e\xe5\x90\x8d\xe4\xbf\xa1\xe6\x81\xaf\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xae\x9e\xe5\x90\x8d\xe4\xbf\xa1\xe6\x81\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DevicesTabCell_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xae\xbe\xe5\xa4\x87\xe7\xae\xa1\xe7\x90\x86\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xae\xbe\xe5\xa4\x87\xe7\xae\xa1\xe7\x90\x86" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LawTabCell_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\xb3\x95\xe5\xbe\x8b\xe6\x9d\xa1\xe6\xac\xbe\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\xb3\x95\xe5\xbe\x8b\xe6\x9d\xa1\xe6\xac\xbe" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherTabCell_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x85\xb6\xe4\xbb\x96\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x85\xb6\xe4\xbb\x96" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TabSwitcher_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// tabSwitcher\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "tabSwitcher" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BindAccountManagerWidget_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccountInfoWidget_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IDInfoWidget_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeviceManagerWidget_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LegalTermsWidget_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherWidget_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// enter icon\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "enter icon" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// backspace icon\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "backspace icon" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsMainLand_MetaData[] = {
		{ "Category", "PSOneUserCenter" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BackgroundBlur;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ContentBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AvatarImage;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MainIdText;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NickTextBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NickText;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_UIDTextBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_UIDText;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BindAccountTabCell;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AccountTabCell;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RealNameTabCell;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DevicesTabCell;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LawTabCell;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherTabCell;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TabSwitcher;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BindAccountManagerWidget;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AccountInfoWidget;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IDInfoWidget;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DeviceManagerWidget;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LegalTermsWidget;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherWidget;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
	static void NewProp_bIsMainLand_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMainLand;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UPSOneUserCenter_BindData, "BindData" }, // **********
		{ &Z_Construct_UFunction_UPSOneUserCenter_RefreshData, "RefreshData" }, // **********
		{ &Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData, "RefreshDeviceData" }, // **********
		{ &Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts, "UpdateBoundAccounts" }, // **********
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenter>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleBox_MetaData), NewProp_ScaleBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BackgroundBlur = { "BackgroundBlur", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, BackgroundBlur), Z_Construct_UClass_UBackgroundBlur_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackgroundBlur_MetaData), NewProp_BackgroundBlur_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_ContentBox = { "ContentBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, ContentBox), Z_Construct_UClass_UVerticalBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ContentBox_MetaData), NewProp_ContentBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AvatarImage = { "AvatarImage", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, AvatarImage), Z_Construct_UClass_UPSOneAvatarImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvatarImage_MetaData), NewProp_AvatarImage_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_MainIdText = { "MainIdText", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, MainIdText), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MainIdText_MetaData), NewProp_MainIdText_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_NickTextBlock = { "NickTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, NickTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NickTextBlock_MetaData), NewProp_NickTextBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_NickText = { "NickText", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, NickText), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NickText_MetaData), NewProp_NickText_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_UIDTextBlock = { "UIDTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, UIDTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UIDTextBlock_MetaData), NewProp_UIDTextBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_UIDText = { "UIDText", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, UIDText), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UIDText_MetaData), NewProp_UIDText_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BindAccountTabCell = { "BindAccountTabCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, BindAccountTabCell), Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BindAccountTabCell_MetaData), NewProp_BindAccountTabCell_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AccountTabCell = { "AccountTabCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, AccountTabCell), Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccountTabCell_MetaData), NewProp_AccountTabCell_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_RealNameTabCell = { "RealNameTabCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, RealNameTabCell), Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealNameTabCell_MetaData), NewProp_RealNameTabCell_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_DevicesTabCell = { "DevicesTabCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, DevicesTabCell), Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DevicesTabCell_MetaData), NewProp_DevicesTabCell_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_LawTabCell = { "LawTabCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, LawTabCell), Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LawTabCell_MetaData), NewProp_LawTabCell_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_OtherTabCell = { "OtherTabCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, OtherTabCell), Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherTabCell_MetaData), NewProp_OtherTabCell_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_TabSwitcher = { "TabSwitcher", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, TabSwitcher), Z_Construct_UClass_UWidgetSwitcher_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TabSwitcher_MetaData), NewProp_TabSwitcher_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BindAccountManagerWidget = { "BindAccountManagerWidget", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, BindAccountManagerWidget), Z_Construct_UClass_UPSOneUserCenterBindManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BindAccountManagerWidget_MetaData), NewProp_BindAccountManagerWidget_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AccountInfoWidget = { "AccountInfoWidget", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, AccountInfoWidget), Z_Construct_UClass_UPSOneUserCenterAccountInfo_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccountInfoWidget_MetaData), NewProp_AccountInfoWidget_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_IDInfoWidget = { "IDInfoWidget", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, IDInfoWidget), Z_Construct_UClass_UPSOneUserCenterIDInfo_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IDInfoWidget_MetaData), NewProp_IDInfoWidget_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_DeviceManagerWidget = { "DeviceManagerWidget", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, DeviceManagerWidget), Z_Construct_UClass_UPSOneUserCenterDeviceManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeviceManagerWidget_MetaData), NewProp_DeviceManagerWidget_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_LegalTermsWidget = { "LegalTermsWidget", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, LegalTermsWidget), Z_Construct_UClass_UPSOneUserCenterLegalTerms_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LegalTermsWidget_MetaData), NewProp_LegalTermsWidget_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_OtherWidget = { "OtherWidget", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, OtherWidget), Z_Construct_UClass_UPSOneUserCenterOther_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherWidget_MetaData), NewProp_OtherWidget_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnterIcon_MetaData), NewProp_EnterIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenter, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackspaceIcon_MetaData), NewProp_BackspaceIcon_MetaData) };
void Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_bIsMainLand_SetBit(void* Obj)
{
	((UPSOneUserCenter*)Obj)->bIsMainLand = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_bIsMainLand = { "bIsMainLand", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPSOneUserCenter), &Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_bIsMainLand_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsMainLand_MetaData), NewProp_bIsMainLand_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_ScaleBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BackgroundBlur,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_ContentBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AvatarImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_MainIdText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_NickTextBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_NickText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_UIDTextBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_UIDText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BindAccountTabCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AccountTabCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_RealNameTabCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_DevicesTabCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_LawTabCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_OtherTabCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_TabSwitcher,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BindAccountManagerWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AccountInfoWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_IDInfoWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_DeviceManagerWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_LegalTermsWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_OtherWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_EnterIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BackspaceIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_bIsMainLand,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneUserCenter_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenter_Statics::ClassParams = {
	&UPSOneUserCenter::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UPSOneUserCenter_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneUserCenter_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneUserCenter()
{
	if (!Z_Registration_Info_UClass_UPSOneUserCenter.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneUserCenter.OuterSingleton, Z_Construct_UClass_UPSOneUserCenter_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenter.OuterSingleton;
}
UPSOneUserCenter::UPSOneUserCenter(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenter);
UPSOneUserCenter::~UPSOneUserCenter() {}
// ********** End Class UPSOneUserCenter ***********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenter_h__Script_OneEngineSDK_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPSOneUserCenterModel::StaticStruct, Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewStructOps, TEXT("PSOneUserCenterModel"), &Z_Registration_Info_UScriptStruct_FPSOneUserCenterModel, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPSOneUserCenterModel), 460775668U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneUserCenter, UPSOneUserCenter::StaticClass, TEXT("UPSOneUserCenter"), &Z_Registration_Info_UClass_UPSOneUserCenter, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneUserCenter), 3918499268U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenter_h__Script_OneEngineSDK_281375769(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenter_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenter_h__Script_OneEngineSDK_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenter_h__Script_OneEngineSDK_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenter_h__Script_OneEngineSDK_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
