// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneConfirmButton.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneConfirmButton() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneConfirmButton();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneConfirmButton_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneConfirmButton ******************************************************
void UPSOneConfirmButton::StaticRegisterNativesUPSOneConfirmButton()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneConfirmButton;
UClass* UPSOneConfirmButton::GetPrivateStaticClass()
{
	using TClass = UPSOneConfirmButton;
	if (!Z_Registration_Info_UClass_UPSOneConfirmButton.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneConfirmButton"),
			Z_Registration_Info_UClass_UPSOneConfirmButton.InnerSingleton,
			StaticRegisterNativesUPSOneConfirmButton,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneConfirmButton.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneConfirmButton_NoRegister()
{
	return UPSOneConfirmButton::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneConfirmButton_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * \n */" },
#endif
		{ "IncludePath", "Views/PSOneConfirmButton.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneConfirmButton.h" },
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneConfirmButton>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UPSOneConfirmButton_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneButtonBase,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneConfirmButton_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneConfirmButton_Statics::ClassParams = {
	&UPSOneConfirmButton::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	0,
	0,
	0x00A010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneConfirmButton_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneConfirmButton_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneConfirmButton()
{
	if (!Z_Registration_Info_UClass_UPSOneConfirmButton.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneConfirmButton.OuterSingleton, Z_Construct_UClass_UPSOneConfirmButton_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneConfirmButton.OuterSingleton;
}
UPSOneConfirmButton::UPSOneConfirmButton(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneConfirmButton);
UPSOneConfirmButton::~UPSOneConfirmButton() {}
// ********** End Class UPSOneConfirmButton ********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneConfirmButton_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneConfirmButton, UPSOneConfirmButton::StaticClass, TEXT("UPSOneConfirmButton"), &Z_Registration_Info_UClass_UPSOneConfirmButton, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneConfirmButton), 1886062525U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneConfirmButton_h__Script_OneEngineSDK_2841800221(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneConfirmButton_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneConfirmButton_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
