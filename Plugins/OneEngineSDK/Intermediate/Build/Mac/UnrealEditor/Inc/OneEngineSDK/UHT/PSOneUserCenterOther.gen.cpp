// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneUserCenterOther.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneUserCenterOther() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterOther();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterOther_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneUserCenterOther ****************************************************
void UPSOneUserCenterOther::StaticRegisterNativesUPSOneUserCenterOther()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneUserCenterOther;
UClass* UPSOneUserCenterOther::GetPrivateStaticClass()
{
	using TClass = UPSOneUserCenterOther;
	if (!Z_Registration_Info_UClass_UPSOneUserCenterOther.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneUserCenterOther"),
			Z_Registration_Info_UClass_UPSOneUserCenterOther.InnerSingleton,
			StaticRegisterNativesUPSOneUserCenterOther,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterOther.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneUserCenterOther_NoRegister()
{
	return UPSOneUserCenterOther::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneUserCenterOther_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * \n */" },
#endif
		{ "IncludePath", "Views/PSOneUserCenterOther.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterOther.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeleteAccountCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterOther.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DeleteAccountCell;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterOther>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterOther_Statics::NewProp_DeleteAccountCell = { "DeleteAccountCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterOther, DeleteAccountCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeleteAccountCell_MetaData), NewProp_DeleteAccountCell_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterOther_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterOther_Statics::NewProp_DeleteAccountCell,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterOther_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneUserCenterOther_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterOther_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterOther_Statics::ClassParams = {
	&UPSOneUserCenterOther::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneUserCenterOther_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterOther_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterOther_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneUserCenterOther_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneUserCenterOther()
{
	if (!Z_Registration_Info_UClass_UPSOneUserCenterOther.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneUserCenterOther.OuterSingleton, Z_Construct_UClass_UPSOneUserCenterOther_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterOther.OuterSingleton;
}
UPSOneUserCenterOther::UPSOneUserCenterOther(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterOther);
UPSOneUserCenterOther::~UPSOneUserCenterOther() {}
// ********** End Class UPSOneUserCenterOther ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterOther_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneUserCenterOther, UPSOneUserCenterOther::StaticClass, TEXT("UPSOneUserCenterOther"), &Z_Registration_Info_UClass_UPSOneUserCenterOther, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneUserCenterOther), 985003176U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterOther_h__Script_OneEngineSDK_2430708029(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterOther_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterOther_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
