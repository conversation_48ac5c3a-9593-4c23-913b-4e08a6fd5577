// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneSelectAreaCode.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneSelectAreaCode() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneSelectAreaCode();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneSelectAreaCode_NoRegister();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FPSOneAreaCode();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UScrollBox_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FPSOneAreaCode ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPSOneAreaCode;
class UScriptStruct* FPSOneAreaCode::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPSOneAreaCode.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPSOneAreaCode.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPSOneAreaCode, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("PSOneAreaCode"));
	}
	return Z_Registration_Info_UScriptStruct_FPSOneAreaCode.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPSOneAreaCode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Id_MetaData[] = {
		{ "Category", "PSOneAreaCode" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Code_MetaData[] = {
		{ "Category", "PSOneAreaCode" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Area_MetaData[] = {
		{ "Category", "PSOneAreaCode" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Id;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Code;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Area;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPSOneAreaCode>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Id = { "Id", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneAreaCode, Id), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Id_MetaData), NewProp_Id_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Code = { "Code", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneAreaCode, Code), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Code_MetaData), NewProp_Code_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Area = { "Area", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneAreaCode, Area), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Area_MetaData), NewProp_Area_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Id,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Code,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Area,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"PSOneAreaCode",
	Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::PropPointers),
	sizeof(FPSOneAreaCode),
	alignof(FPSOneAreaCode),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPSOneAreaCode()
{
	if (!Z_Registration_Info_UScriptStruct_FPSOneAreaCode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPSOneAreaCode.InnerSingleton, Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPSOneAreaCode.InnerSingleton;
}
// ********** End ScriptStruct FPSOneAreaCode ******************************************************

// ********** Begin Class UPSOneSelectAreaCode Function BindData ***********************************
struct Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics
{
	struct PSOneSelectAreaCode_eventBindData_Parms
	{
		TArray<FPSOneAreaCode> InAreaCodes;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// bind data\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "bind data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InAreaCodes_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InAreaCodes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InAreaCodes;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::NewProp_InAreaCodes_Inner = { "InAreaCodes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPSOneAreaCode, METADATA_PARAMS(0, nullptr) }; // 843000811
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::NewProp_InAreaCodes = { "InAreaCodes", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneSelectAreaCode_eventBindData_Parms, InAreaCodes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InAreaCodes_MetaData), NewProp_InAreaCodes_MetaData) }; // 843000811
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::NewProp_InAreaCodes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::NewProp_InAreaCodes,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneSelectAreaCode, nullptr, "BindData", Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::PSOneSelectAreaCode_eventBindData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::PSOneSelectAreaCode_eventBindData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneSelectAreaCode_BindData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneSelectAreaCode::execBindData)
{
	P_GET_TARRAY_REF(FPSOneAreaCode,Z_Param_Out_InAreaCodes);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BindData(Z_Param_Out_InAreaCodes);
	P_NATIVE_END;
}
// ********** End Class UPSOneSelectAreaCode Function BindData *************************************

// ********** Begin Class UPSOneSelectAreaCode *****************************************************
void UPSOneSelectAreaCode::StaticRegisterNativesUPSOneSelectAreaCode()
{
	UClass* Class = UPSOneSelectAreaCode::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "BindData", &UPSOneSelectAreaCode::execBindData },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneSelectAreaCode;
UClass* UPSOneSelectAreaCode::GetPrivateStaticClass()
{
	using TClass = UPSOneSelectAreaCode;
	if (!Z_Registration_Info_UClass_UPSOneSelectAreaCode.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneSelectAreaCode"),
			Z_Registration_Info_UClass_UPSOneSelectAreaCode.InnerSingleton,
			StaticRegisterNativesUPSOneSelectAreaCode,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneSelectAreaCode.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneSelectAreaCode_NoRegister()
{
	return UPSOneSelectAreaCode::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneSelectAreaCode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n *\n */" },
#endif
		{ "IncludePath", "Views/PSOneSelectAreaCode.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// enter icon\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "enter icon" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// backspace icon\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "backspace icon" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScrollBox_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x98\xbe\xe7\xa4\xba\xe7\x9a\x84\xe5\x8c\xba\xe5\x9f\x9f\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x98\xbe\xe7\xa4\xba\xe7\x9a\x84\xe5\x8c\xba\xe5\x9f\x9f" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ScrollBox;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UPSOneSelectAreaCode_BindData, "BindData" }, // 645390633
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneSelectAreaCode>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneSelectAreaCode, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleBox_MetaData), NewProp_ScaleBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneSelectAreaCode, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnterIcon_MetaData), NewProp_EnterIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneSelectAreaCode, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackspaceIcon_MetaData), NewProp_BackspaceIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_ScrollBox = { "ScrollBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneSelectAreaCode, ScrollBox), Z_Construct_UClass_UScrollBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScrollBox_MetaData), NewProp_ScrollBox_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneSelectAreaCode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_ScaleBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_EnterIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_BackspaceIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_ScrollBox,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneSelectAreaCode_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneSelectAreaCode_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneSelectAreaCode_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneSelectAreaCode_Statics::ClassParams = {
	&UPSOneSelectAreaCode::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UPSOneSelectAreaCode_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneSelectAreaCode_Statics::PropPointers),
	0,
	0x00A010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneSelectAreaCode_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneSelectAreaCode_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneSelectAreaCode()
{
	if (!Z_Registration_Info_UClass_UPSOneSelectAreaCode.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneSelectAreaCode.OuterSingleton, Z_Construct_UClass_UPSOneSelectAreaCode_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneSelectAreaCode.OuterSingleton;
}
UPSOneSelectAreaCode::UPSOneSelectAreaCode(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneSelectAreaCode);
UPSOneSelectAreaCode::~UPSOneSelectAreaCode() {}
// ********** End Class UPSOneSelectAreaCode *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h__Script_OneEngineSDK_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPSOneAreaCode::StaticStruct, Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewStructOps, TEXT("PSOneAreaCode"), &Z_Registration_Info_UScriptStruct_FPSOneAreaCode, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPSOneAreaCode), 843000811U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneSelectAreaCode, UPSOneSelectAreaCode::StaticClass, TEXT("UPSOneSelectAreaCode"), &Z_Registration_Info_UClass_UPSOneSelectAreaCode, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneSelectAreaCode), 1220135739U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h__Script_OneEngineSDK_2591698363(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h__Script_OneEngineSDK_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h__Script_OneEngineSDK_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h__Script_OneEngineSDK_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
