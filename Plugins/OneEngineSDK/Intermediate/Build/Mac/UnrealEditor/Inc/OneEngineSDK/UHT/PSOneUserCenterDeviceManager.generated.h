// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSOneUserCenterDeviceManager.h"

#ifdef ONEENGINESDK_PSOneUserCenterDeviceManager_generated_h
#error "PSOneUserCenterDeviceManager.generated.h already included, missing '#pragma once' in PSOneUserCenterDeviceManager.h"
#endif
#define ONEENGINESDK_PSOneUserCenterDeviceManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FOneOnlineDevice **************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterDeviceManager_h_12_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneOnlineDevice_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOneOnlineDevice;
// ********** End ScriptStruct FOneOnlineDevice ****************************************************

// ********** Begin Class UPSOneUserCenterDeviceManager ********************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterDeviceManager_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterDeviceManager_h_25_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneUserCenterDeviceManager(); \
	friend struct Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterDeviceManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSOneUserCenterDeviceManager, UPSOneFocusUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSOneUserCenterDeviceManager_NoRegister) \
	DECLARE_SERIALIZER(UPSOneUserCenterDeviceManager)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterDeviceManager_h_25_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneUserCenterDeviceManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSOneUserCenterDeviceManager(UPSOneUserCenterDeviceManager&&) = delete; \
	UPSOneUserCenterDeviceManager(const UPSOneUserCenterDeviceManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneUserCenterDeviceManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneUserCenterDeviceManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneUserCenterDeviceManager) \
	NO_API virtual ~UPSOneUserCenterDeviceManager();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterDeviceManager_h_22_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterDeviceManager_h_25_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterDeviceManager_h_25_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterDeviceManager_h_25_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSOneUserCenterDeviceManager;

// ********** End Class UPSOneUserCenterDeviceManager **********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterDeviceManager_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
