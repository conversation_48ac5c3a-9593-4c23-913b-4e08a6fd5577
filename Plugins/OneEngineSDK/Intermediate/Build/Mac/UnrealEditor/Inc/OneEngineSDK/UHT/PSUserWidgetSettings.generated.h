// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSUserWidgetSettings.h"

#ifdef ONEENGINESDK_PSUserWidgetSettings_generated_h
#error "PSUserWidgetSettings.generated.h already included, missing '#pragma once' in PSUserWidgetSettings.h"
#endif
#define ONEENGINESDK_PSUserWidgetSettings_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UPSUserWidgetSettings;

// ********** Begin Class UPSUserWidgetSettings ****************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSUserWidgetSettings_h_9_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGet);


ONEENGINESDK_API UClass* Z_Construct_UClass_UPSUserWidgetSettings_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSUserWidgetSettings_h_9_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSUserWidgetSettings(); \
	friend struct Z_Construct_UClass_UPSUserWidgetSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSUserWidgetSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSUserWidgetSettings, UDataAsset, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSUserWidgetSettings_NoRegister) \
	DECLARE_SERIALIZER(UPSUserWidgetSettings)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSUserWidgetSettings_h_9_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSUserWidgetSettings(UPSUserWidgetSettings&&) = delete; \
	UPSUserWidgetSettings(const UPSUserWidgetSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSUserWidgetSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSUserWidgetSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UPSUserWidgetSettings) \
	NO_API virtual ~UPSUserWidgetSettings();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSUserWidgetSettings_h_6_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSUserWidgetSettings_h_9_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSUserWidgetSettings_h_9_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSUserWidgetSettings_h_9_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSUserWidgetSettings_h_9_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSUserWidgetSettings;

// ********** End Class UPSUserWidgetSettings ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSUserWidgetSettings_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
