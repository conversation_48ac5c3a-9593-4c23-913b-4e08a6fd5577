// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSOneUserCenterRightCellAvatar.h"

#ifdef ONEENGINESDK_PSOneUserCenterRightCellAvatar_generated_h
#error "PSOneUserCenterRightCellAvatar.generated.h already included, missing '#pragma once' in PSOneUserCenterRightCellAvatar.h"
#endif
#define ONEENGINESDK_PSOneUserCenterRightCellAvatar_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UPSOneUserCenterRightCellAvatar ******************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAvatar_h_15_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneUserCenterRightCellAvatar(); \
	friend struct Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSOneUserCenterRightCellAvatar, UPSOneButtonBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_NoRegister) \
	DECLARE_SERIALIZER(UPSOneUserCenterRightCellAvatar)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAvatar_h_15_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneUserCenterRightCellAvatar(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSOneUserCenterRightCellAvatar(UPSOneUserCenterRightCellAvatar&&) = delete; \
	UPSOneUserCenterRightCellAvatar(const UPSOneUserCenterRightCellAvatar&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneUserCenterRightCellAvatar); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneUserCenterRightCellAvatar); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneUserCenterRightCellAvatar) \
	NO_API virtual ~UPSOneUserCenterRightCellAvatar();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAvatar_h_12_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAvatar_h_15_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAvatar_h_15_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAvatar_h_15_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSOneUserCenterRightCellAvatar;

// ********** End Class UPSOneUserCenterRightCellAvatar ********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAvatar_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
