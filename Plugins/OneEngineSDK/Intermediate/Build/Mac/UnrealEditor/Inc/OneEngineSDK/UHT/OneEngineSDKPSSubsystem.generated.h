// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "OneEngineSDKPSSubsystem.h"

#ifdef ONEENGINESDK_OneEngineSDKPSSubsystem_generated_h
#error "OneEngineSDKPSSubsystem.generated.h already included, missing '#pragma once' in OneEngineSDKPSSubsystem.h"
#endif
#define ONEENGINESDK_OneEngineSDKPSSubsystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EOnePsnAccountState : uint8;
enum class EOnePSStoreIconPos : uint8;
struct FOnePSProductCategory;
struct FOnePSUserProfileResponse;

// ********** Begin ScriptStruct FOnePSUserProfile *************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_30_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSUserProfile_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOnePSUserProfile;
// ********** End ScriptStruct FOnePSUserProfile ***************************************************

// ********** Begin ScriptStruct FOnePSUserProfileResponse *****************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_63_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOnePSUserProfileResponse;
// ********** End ScriptStruct FOnePSUserProfileResponse *******************************************

// ********** Begin ScriptStruct FOnePSPurchaseForm ************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_81_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOnePSPurchaseForm;
// ********** End ScriptStruct FOnePSPurchaseForm **************************************************

// ********** Begin ScriptStruct FOnePSProductMediaImage *******************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_147_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOnePSProductMediaImage;
// ********** End ScriptStruct FOnePSProductMediaImage *********************************************

// ********** Begin ScriptStruct FOnePSProductMedia ************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_171_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSProductMedia_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOnePSProductMedia;
// ********** End ScriptStruct FOnePSProductMedia **************************************************

// ********** Begin ScriptStruct FOnePSProductSku **************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_179_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSProductSku_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOnePSProductSku;
// ********** End ScriptStruct FOnePSProductSku ****************************************************

// ********** Begin ScriptStruct FOnePSProduct *****************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_269_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSProduct_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOnePSProduct;
// ********** End ScriptStruct FOnePSProduct *******************************************************

// ********** Begin ScriptStruct FOnePSProductCategory *********************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_324_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSProductCategory_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOnePSProductCategory;
// ********** End ScriptStruct FOnePSProductCategory ***********************************************

// ********** Begin Class UOnePSLocaleEnum *********************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execIndonesian); \
	DECLARE_FUNCTION(execVietnamese); \
	DECLARE_FUNCTION(execThai); \
	DECLARE_FUNCTION(execRomanian); \
	DECLARE_FUNCTION(execGreek); \
	DECLARE_FUNCTION(execHungarian); \
	DECLARE_FUNCTION(execCzech); \
	DECLARE_FUNCTION(execFrench_CA); \
	DECLARE_FUNCTION(execArabic); \
	DECLARE_FUNCTION(execSpanish_LA); \
	DECLARE_FUNCTION(execTurkish); \
	DECLARE_FUNCTION(execEnglish_GB); \
	DECLARE_FUNCTION(execPortuguese_BR); \
	DECLARE_FUNCTION(execPolish); \
	DECLARE_FUNCTION(execNorwegian); \
	DECLARE_FUNCTION(execDanish); \
	DECLARE_FUNCTION(execSwedish); \
	DECLARE_FUNCTION(execFinnish); \
	DECLARE_FUNCTION(execChinese_S); \
	DECLARE_FUNCTION(execChinese_T); \
	DECLARE_FUNCTION(execKorean); \
	DECLARE_FUNCTION(execRussian); \
	DECLARE_FUNCTION(execPortuguese_PT); \
	DECLARE_FUNCTION(execDutch); \
	DECLARE_FUNCTION(execItalian); \
	DECLARE_FUNCTION(execGerman); \
	DECLARE_FUNCTION(execSpanish); \
	DECLARE_FUNCTION(execFrench); \
	DECLARE_FUNCTION(execEnglish_US); \
	DECLARE_FUNCTION(execJapanese);


ONEENGINESDK_API UClass* Z_Construct_UClass_UOnePSLocaleEnum_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUOnePSLocaleEnum(); \
	friend struct Z_Construct_UClass_UOnePSLocaleEnum_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UOnePSLocaleEnum_NoRegister(); \
public: \
	DECLARE_CLASS2(UOnePSLocaleEnum, UBlueprintFunctionLibrary, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UOnePSLocaleEnum_NoRegister) \
	DECLARE_SERIALIZER(UOnePSLocaleEnum)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UOnePSLocaleEnum(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UOnePSLocaleEnum(UOnePSLocaleEnum&&) = delete; \
	UOnePSLocaleEnum(const UOnePSLocaleEnum&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UOnePSLocaleEnum); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UOnePSLocaleEnum); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UOnePSLocaleEnum) \
	NO_API virtual ~UOnePSLocaleEnum();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_388_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UOnePSLocaleEnum;

// ********** End Class UOnePSLocaleEnum ***********************************************************

// ********** Begin Delegate FOnGetFriendsResultDelegate *******************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_491_DELEGATE \
static void FOnGetFriendsResultDelegate_DelegateWrapper(const FScriptDelegate& OnGetFriendsResultDelegate, FOnePSUserProfileResponse const& ProfileList);


// ********** End Delegate FOnGetFriendsResultDelegate *********************************************

// ********** Begin Delegate FOnGetBlockingUsersResultDelegate *************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_493_DELEGATE \
static void FOnGetBlockingUsersResultDelegate_DelegateWrapper(const FScriptDelegate& OnGetBlockingUsersResultDelegate, FOnePSUserProfileResponse const& ProfileList);


// ********** End Delegate FOnGetBlockingUsersResultDelegate ***************************************

// ********** Begin Delegate FOnRestrictionStatusResultDelegate ************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_495_DELEGATE \
static void FOnRestrictionStatusResultDelegate_DelegateWrapper(const FScriptDelegate& OnRestrictionStatusResultDelegate, int32 Result);


// ********** End Delegate FOnRestrictionStatusResultDelegate **************************************

// ********** Begin Delegate FOnCheckPremiumResultDelegate *****************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_497_DELEGATE \
static void FOnCheckPremiumResultDelegate_DelegateWrapper(const FScriptDelegate& OnCheckPremiumResultDelegate, int32 Code, bool bIsPremium);


// ********** End Delegate FOnCheckPremiumResultDelegate *******************************************

// ********** Begin Delegate FOnFilterProfanityResultDelegate **************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_499_DELEGATE \
static void FOnFilterProfanityResultDelegate_DelegateWrapper(const FScriptDelegate& OnFilterProfanityResultDelegate, int32 Code, const FString& Text);


// ********** End Delegate FOnFilterProfanityResultDelegate ****************************************

// ********** Begin Delegate FOnOpenDialogResultDelegate *******************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_501_DELEGATE \
static void FOnOpenDialogResultDelegate_DelegateWrapper(const FScriptDelegate& OnOpenDialogResultDelegate, int32 Result);


// ********** End Delegate FOnOpenDialogResultDelegate *********************************************

// ********** Begin Delegate FOnGetProductInfoListPSDelegate ***************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_503_DELEGATE \
static void FOnGetProductInfoListPSDelegate_DelegateWrapper(const FScriptDelegate& OnGetProductInfoListPSDelegate, bool bSucceed, FOnePSProductCategory const& Category, int32 Code, const FString& Msg);


// ********** End Delegate FOnGetProductInfoListPSDelegate *****************************************

// ********** Begin Delegate FWidgetVisibilityDelegate *********************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_659_DELEGATE \
static void FWidgetVisibilityDelegate_DelegateWrapper(const FMulticastScriptDelegate& WidgetVisibilityDelegate, int32 WidgetType, bool bVisible);


// ********** End Delegate FWidgetVisibilityDelegate ***********************************************

// ********** Begin Class UOneEngineSDKPSSubsystem *************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetFontPath); \
	DECLARE_FUNCTION(execGetProductInfoListPS); \
	DECLARE_FUNCTION(execOpenCommerceDialogPremiumMode); \
	DECLARE_FUNCTION(execFilterProfanitySync); \
	DECLARE_FUNCTION(execFilterProfanity); \
	DECLARE_FUNCTION(execStopNotifyPremiumFeature); \
	DECLARE_FUNCTION(execStartNotifyPremiumFeature); \
	DECLARE_FUNCTION(execCheckPremium); \
	DECLARE_FUNCTION(execGetCommunicationRestrictionStatus); \
	DECLARE_FUNCTION(execHideStoreIcon); \
	DECLARE_FUNCTION(execShowStoreIcon); \
	DECLARE_FUNCTION(execGetCountryRegion); \
	DECLARE_FUNCTION(execGetCountryCode); \
	DECLARE_FUNCTION(execGetOnlineId); \
	DECLARE_FUNCTION(execGetAccountId); \
	DECLARE_FUNCTION(execGetBlockingUsers); \
	DECLARE_FUNCTION(execGetFriends); \
	DECLARE_FUNCTION(execGetAccountState);


ONEENGINESDK_API UClass* Z_Construct_UClass_UOneEngineSDKPSSubsystem_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUOneEngineSDKPSSubsystem(); \
	friend struct Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UOneEngineSDKPSSubsystem_NoRegister(); \
public: \
	DECLARE_CLASS2(UOneEngineSDKPSSubsystem, UGameInstanceSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UOneEngineSDKPSSubsystem_NoRegister) \
	DECLARE_SERIALIZER(UOneEngineSDKPSSubsystem)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UOneEngineSDKPSSubsystem(); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UOneEngineSDKPSSubsystem(UOneEngineSDKPSSubsystem&&) = delete; \
	UOneEngineSDKPSSubsystem(const UOneEngineSDKPSSubsystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UOneEngineSDKPSSubsystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UOneEngineSDKPSSubsystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UOneEngineSDKPSSubsystem) \
	NO_API virtual ~UOneEngineSDKPSSubsystem();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_485_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UOneEngineSDKPSSubsystem;

// ********** End Class UOneEngineSDKPSSubsystem ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h

// ********** Begin Enum EOnePSStoreIconPos ********************************************************
#define FOREACH_ENUM_EONEPSSTOREICONPOS(op) \
	op(EOnePSStoreIconPos::Center) \
	op(EOnePSStoreIconPos::Left) \
	op(EOnePSStoreIconPos::Right) 

enum class EOnePSStoreIconPos : uint8;
template<> struct TIsUEnumClass<EOnePSStoreIconPos> { enum { Value = true }; };
template<> ONEENGINESDK_API UEnum* StaticEnum<EOnePSStoreIconPos>();
// ********** End Enum EOnePSStoreIconPos **********************************************************

// ********** Begin Enum EOnePsnAccountState *******************************************************
#define FOREACH_ENUM_EONEPSNACCOUNTSTATE(op) \
	op(EOnePsnAccountState::Unknown) \
	op(EOnePsnAccountState::SignedOut) \
	op(EOnePsnAccountState::SignedIn) 

enum class EOnePsnAccountState : uint8;
template<> struct TIsUEnumClass<EOnePsnAccountState> { enum { Value = true }; };
template<> ONEENGINESDK_API UEnum* StaticEnum<EOnePsnAccountState>();
// ********** End Enum EOnePsnAccountState *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
