// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneScaleWidget.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneScaleWidget() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneScaleWidget();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneScaleWidget_NoRegister();
UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UUserWidget();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneScaleWidget ********************************************************
void UPSOneScaleWidget::StaticRegisterNativesUPSOneScaleWidget()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneScaleWidget;
UClass* UPSOneScaleWidget::GetPrivateStaticClass()
{
	using TClass = UPSOneScaleWidget;
	if (!Z_Registration_Info_UClass_UPSOneScaleWidget.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneScaleWidget"),
			Z_Registration_Info_UClass_UPSOneScaleWidget.InnerSingleton,
			StaticRegisterNativesUPSOneScaleWidget,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneScaleWidget.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneScaleWidget_NoRegister()
{
	return UPSOneScaleWidget::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneScaleWidget_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * \n */" },
#endif
		{ "IncludePath", "Views/PSOneScaleWidget.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneScaleWidget.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneScaleWidget.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneScaleWidget>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneScaleWidget_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneScaleWidget, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleBox_MetaData), NewProp_ScaleBox_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneScaleWidget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneScaleWidget_Statics::NewProp_ScaleBox,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneScaleWidget_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneScaleWidget_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneScaleWidget_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneScaleWidget_Statics::ClassParams = {
	&UPSOneScaleWidget::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneScaleWidget_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneScaleWidget_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneScaleWidget_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneScaleWidget_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneScaleWidget()
{
	if (!Z_Registration_Info_UClass_UPSOneScaleWidget.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneScaleWidget.OuterSingleton, Z_Construct_UClass_UPSOneScaleWidget_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneScaleWidget.OuterSingleton;
}
UPSOneScaleWidget::UPSOneScaleWidget(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneScaleWidget);
UPSOneScaleWidget::~UPSOneScaleWidget() {}
// ********** End Class UPSOneScaleWidget **********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneScaleWidget, UPSOneScaleWidget::StaticClass, TEXT("UPSOneScaleWidget"), &Z_Registration_Info_UClass_UPSOneScaleWidget, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneScaleWidget), 1867164838U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h__Script_OneEngineSDK_2946659514(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
