// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneUserCenterRightCellAvatar.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneUserCenterRightCellAvatar() {}

// ********** Begin Cross Module References ********************************************************
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneAvatarImage_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAvatar();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneUserCenterRightCellAvatar ******************************************
void UPSOneUserCenterRightCellAvatar::StaticRegisterNativesUPSOneUserCenterRightCellAvatar()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneUserCenterRightCellAvatar;
UClass* UPSOneUserCenterRightCellAvatar::GetPrivateStaticClass()
{
	using TClass = UPSOneUserCenterRightCellAvatar;
	if (!Z_Registration_Info_UClass_UPSOneUserCenterRightCellAvatar.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneUserCenterRightCellAvatar"),
			Z_Registration_Info_UClass_UPSOneUserCenterRightCellAvatar.InnerSingleton,
			StaticRegisterNativesUPSOneUserCenterRightCellAvatar,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterRightCellAvatar.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_NoRegister()
{
	return UPSOneUserCenterRightCellAvatar::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * \n */" },
#endif
		{ "IncludePath", "Views/PSOneUserCenterRightCellAvatar.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAvatar.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImageIcon_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellAvatar" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAvatar.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvatarImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAvatar.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ImageIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AvatarImage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterRightCellAvatar>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::NewProp_ImageIcon = { "ImageIcon", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellAvatar, ImageIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImageIcon_MetaData), NewProp_ImageIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::NewProp_AvatarImage = { "AvatarImage", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellAvatar, AvatarImage), Z_Construct_UClass_UPSOneAvatarImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvatarImage_MetaData), NewProp_AvatarImage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::NewProp_ImageIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::NewProp_AvatarImage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneButtonBase,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::ClassParams = {
	&UPSOneUserCenterRightCellAvatar::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAvatar()
{
	if (!Z_Registration_Info_UClass_UPSOneUserCenterRightCellAvatar.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneUserCenterRightCellAvatar.OuterSingleton, Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterRightCellAvatar.OuterSingleton;
}
UPSOneUserCenterRightCellAvatar::UPSOneUserCenterRightCellAvatar(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterRightCellAvatar);
UPSOneUserCenterRightCellAvatar::~UPSOneUserCenterRightCellAvatar() {}
// ********** End Class UPSOneUserCenterRightCellAvatar ********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAvatar_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneUserCenterRightCellAvatar, UPSOneUserCenterRightCellAvatar::StaticClass, TEXT("UPSOneUserCenterRightCellAvatar"), &Z_Registration_Info_UClass_UPSOneUserCenterRightCellAvatar, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneUserCenterRightCellAvatar), 1965490477U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAvatar_h__Script_OneEngineSDK_2905115568(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAvatar_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAvatar_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
