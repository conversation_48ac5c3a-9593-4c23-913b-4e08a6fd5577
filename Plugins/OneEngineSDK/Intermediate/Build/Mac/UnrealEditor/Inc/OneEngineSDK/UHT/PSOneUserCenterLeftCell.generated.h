// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSOneUserCenterLeftCell.h"

#ifdef ONEENGINESDK_PSOneUserCenterLeftCell_generated_h
#error "PSOneUserCenterLeftCell.generated.h already included, missing '#pragma once' in PSOneUserCenterLeftCell.h"
#endif
#define ONEENGINESDK_PSOneUserCenterLeftCell_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UPSOneUserCenterLeftCell *************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterLeftCell_h_15_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneUserCenterLeftCell(); \
	friend struct Z_Construct_UClass_UPSOneUserCenterLeftCell_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSOneUserCenterLeftCell, UPSOneButtonBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister) \
	DECLARE_SERIALIZER(UPSOneUserCenterLeftCell)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterLeftCell_h_15_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneUserCenterLeftCell(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSOneUserCenterLeftCell(UPSOneUserCenterLeftCell&&) = delete; \
	UPSOneUserCenterLeftCell(const UPSOneUserCenterLeftCell&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneUserCenterLeftCell); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneUserCenterLeftCell); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneUserCenterLeftCell) \
	NO_API virtual ~UPSOneUserCenterLeftCell();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterLeftCell_h_12_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterLeftCell_h_15_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterLeftCell_h_15_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterLeftCell_h_15_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSOneUserCenterLeftCell;

// ********** End Class UPSOneUserCenterLeftCell ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterLeftCell_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
