// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSOneFocusCheckButton.h"

#ifdef ONEENGINESDK_PSOneFocusCheckButton_generated_h
#error "PSOneFocusCheckButton.generated.h already included, missing '#pragma once' in PSOneFocusCheckButton.h"
#endif
#define ONEENGINESDK_PSOneFocusCheckButton_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UPSOneFocusCheckButton ***************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusCheckButton_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneFocusCheckButton(); \
	friend struct Z_Construct_UClass_UPSOneFocusCheckButton_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusCheckButton_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSOneFocusCheckButton, UPSOneButtonBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSOneFocusCheckButton_NoRegister) \
	DECLARE_SERIALIZER(UPSOneFocusCheckButton)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneFocusCheckButton(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSOneFocusCheckButton(UPSOneFocusCheckButton&&) = delete; \
	UPSOneFocusCheckButton(const UPSOneFocusCheckButton&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneFocusCheckButton); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneFocusCheckButton); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneFocusCheckButton) \
	NO_API virtual ~UPSOneFocusCheckButton();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_12_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSOneFocusCheckButton;

// ********** End Class UPSOneFocusCheckButton *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
