// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "OneEngineSettings.h"

#ifdef ONEENGINEEDITOR_OneEngineSettings_generated_h
#error "OneEngineSettings.generated.h already included, missing '#pragma once' in OneEngineSettings.h"
#endif
#define ONEENGINEEDITOR_OneEngineSettings_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UOneEngineSettings *******************************************************
ONEENGINEEDITOR_API UClass* Z_Construct_UClass_UOneEngineSettings_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUOneEngineSettings(); \
	friend struct Z_Construct_UClass_UOneEngineSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINEEDITOR_API UClass* Z_Construct_UClass_UOneEngineSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UOneEngineSettings, UObject, COMPILED_IN_FLAGS(0 | CLASS_DefaultConfig | CLASS_Config), CASTCLASS_None, TEXT("/Script/OneEngineEditor"), Z_Construct_UClass_UOneEngineSettings_NoRegister) \
	DECLARE_SERIALIZER(UOneEngineSettings) \
	static const TCHAR* StaticConfigName() {return TEXT("Game");} \



#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UOneEngineSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UOneEngineSettings(UOneEngineSettings&&) = delete; \
	UOneEngineSettings(const UOneEngineSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UOneEngineSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UOneEngineSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UOneEngineSettings) \
	NO_API virtual ~UOneEngineSettings();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_17_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UOneEngineSettings;

// ********** End Class UOneEngineSettings *********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h

// ********** Begin Enum ESDKRegionConfig **********************************************************
#define FOREACH_ENUM_ESDKREGIONCONFIG(op) \
	op(ESDKRegionConfig::Mainland) \
	op(ESDKRegionConfig::Oversea) 

enum class ESDKRegionConfig : uint8;
template<> struct TIsUEnumClass<ESDKRegionConfig> { enum { Value = true }; };
template<> ONEENGINEEDITOR_API UEnum* StaticEnum<ESDKRegionConfig>();
// ********** End Enum ESDKRegionConfig ************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
