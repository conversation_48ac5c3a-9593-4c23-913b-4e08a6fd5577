// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSettings.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeOneEngineSettings() {}

// ********** Begin Cross Module References ********************************************************
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
ONEENGINEEDITOR_API UClass* Z_Construct_UClass_UOneEngineSettings();
ONEENGINEEDITOR_API UClass* Z_Construct_UClass_UOneEngineSettings_NoRegister();
ONEENGINEEDITOR_API UEnum* Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig();
UPackage* Z_Construct_UPackage__Script_OneEngineEditor();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ESDKRegionConfig **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESDKRegionConfig;
static UEnum* ESDKRegionConfig_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESDKRegionConfig.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESDKRegionConfig.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig, (UObject*)Z_Construct_UPackage__Script_OneEngineEditor(), TEXT("ESDKRegionConfig"));
	}
	return Z_Registration_Info_UEnum_ESDKRegionConfig.OuterSingleton;
}
template<> ONEENGINEEDITOR_API UEnum* StaticEnum<ESDKRegionConfig>()
{
	return ESDKRegionConfig_StaticEnum();
}
struct Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Mainland.Name", "ESDKRegionConfig::Mainland" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
		{ "Oversea.Name", "ESDKRegionConfig::Oversea" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESDKRegionConfig::Mainland", (int64)ESDKRegionConfig::Mainland },
		{ "ESDKRegionConfig::Oversea", (int64)ESDKRegionConfig::Oversea },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_OneEngineEditor,
	nullptr,
	"ESDKRegionConfig",
	"ESDKRegionConfig",
	Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig_Statics::Enum_MetaDataParams), Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig()
{
	if (!Z_Registration_Info_UEnum_ESDKRegionConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESDKRegionConfig.InnerSingleton, Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESDKRegionConfig.InnerSingleton;
}
// ********** End Enum ESDKRegionConfig ************************************************************

// ********** Begin Class UOneEngineSettings *******************************************************
void UOneEngineSettings::StaticRegisterNativesUOneEngineSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UOneEngineSettings;
UClass* UOneEngineSettings::GetPrivateStaticClass()
{
	using TClass = UOneEngineSettings;
	if (!Z_Registration_Info_UClass_UOneEngineSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("OneEngineSettings"),
			Z_Registration_Info_UClass_UOneEngineSettings.InnerSingleton,
			StaticRegisterNativesUOneEngineSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UOneEngineSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UOneEngineSettings_NoRegister()
{
	return UOneEngineSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UOneEngineSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "IncludePath", "OneEngineSettings.h" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SDKRegion_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AppID_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Env_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe9\x85\x8d\xe7\xbd\xae\xe6\x96\x87\xe4\xbb\xb6\xe7\x89\x88\xe6\x9c\xac\xe5\x8f\xb7\xef\xbc\x8c\xe9\x9c\x80\xe5\x92\x8c""Dev\xe9\x85\x8d\xe7\xbd\xae\xe4\xb8\x80\xe8\x87\xb4" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OneAppKey_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "EditCondition", "SDKRegion == ESDKRegionConfig::Mainland" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PSMainlandConfigData_MetaData[] = {
		{ "Category", "PS" },
		{ "DisplayName", "PSMainlandConfigData" },
		{ "EditCondition", "SDKRegion == ESDKRegionConfig::Mainland" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PSOverseaConfigData_MetaData[] = {
		{ "Category", "PS" },
		{ "DisplayName", "PSOverseaConfigData" },
		{ "EditCondition", "SDKRegion == ESDKRegionConfig::Oversea" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HarmonyOSConfigFilePath_MetaData[] = {
		{ "Category", "HarmonyOS" },
		{ "DisplayName", "LaohuConfig" },
		{ "EditCondition", "SDKRegion == ESDKRegionConfig::Mainland" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowSplashBackGroundImage_MetaData[] = {
		{ "Category", "Android" },
		{ "DisplayName", "\xe6\x98\xaf\xe5\x90\xa6\xe5\x90\xaf\xe7\x94\xa8\xe5\x90\xaf\xe5\x8a\xa8\xe9\xa1\xb5\xe8\x83\x8c\xe6\x99\xaf\xe5\x9b\xbe" },
		{ "EditCondition", "SDKRegion == ESDKRegionConfig::Mainland" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplashShowTime_MetaData[] = {
		{ "Category", "Android" },
		{ "DisplayName", "\xe5\x90\xaf\xe5\x8a\xa8\xe8\x83\x8c\xe6\x99\xaf\xe5\x9b\xbe\xe6\x98\xbe\xe7\xa4\xba\xe6\x97\xb6\xe9\x97\xb4 \xe5\x8d\x95\xe4\xbd\x8dms,\xe9\xbb\x98\xe8\xae\xa4""500ms" },
		{ "EditCondition", "SDKRegion == ESDKRegionConfig::Mainland" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SDKRegion_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SDKRegion;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AppID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Env;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OneAppKey;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PSMainlandConfigData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PSOverseaConfigData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_HarmonyOSConfigFilePath;
	static void NewProp_bShowSplashBackGroundImage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowSplashBackGroundImage;
	static const UECodeGen_Private::FInt64PropertyParams NewProp_SplashShowTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UOneEngineSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SDKRegion_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SDKRegion = { "SDKRegion", nullptr, (EPropertyFlags)0x0010000000004001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UOneEngineSettings, SDKRegion), Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SDKRegion_MetaData), NewProp_SDKRegion_MetaData) }; // 1140499994
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_AppID = { "AppID", nullptr, (EPropertyFlags)0x0010000000004001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UOneEngineSettings, AppID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AppID_MetaData), NewProp_AppID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_Env = { "Env", nullptr, (EPropertyFlags)0x0010000000004001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UOneEngineSettings, Env), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Env_MetaData), NewProp_Env_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_OneAppKey = { "OneAppKey", nullptr, (EPropertyFlags)0x0010000000004001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UOneEngineSettings, OneAppKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OneAppKey_MetaData), NewProp_OneAppKey_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_PSMainlandConfigData = { "PSMainlandConfigData", nullptr, (EPropertyFlags)0x0010000000004001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UOneEngineSettings, PSMainlandConfigData), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PSMainlandConfigData_MetaData), NewProp_PSMainlandConfigData_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_PSOverseaConfigData = { "PSOverseaConfigData", nullptr, (EPropertyFlags)0x0010000000004001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UOneEngineSettings, PSOverseaConfigData), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PSOverseaConfigData_MetaData), NewProp_PSOverseaConfigData_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_HarmonyOSConfigFilePath = { "HarmonyOSConfigFilePath", nullptr, (EPropertyFlags)0x0010000000004001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UOneEngineSettings, HarmonyOSConfigFilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HarmonyOSConfigFilePath_MetaData), NewProp_HarmonyOSConfigFilePath_MetaData) };
void Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_bShowSplashBackGroundImage_SetBit(void* Obj)
{
	((UOneEngineSettings*)Obj)->bShowSplashBackGroundImage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_bShowSplashBackGroundImage = { "bShowSplashBackGroundImage", nullptr, (EPropertyFlags)0x0010000000004001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UOneEngineSettings), &Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_bShowSplashBackGroundImage_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowSplashBackGroundImage_MetaData), NewProp_bShowSplashBackGroundImage_MetaData) };
const UECodeGen_Private::FInt64PropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SplashShowTime = { "SplashShowTime", nullptr, (EPropertyFlags)0x0010000000004001, UECodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UOneEngineSettings, SplashShowTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplashShowTime_MetaData), NewProp_SplashShowTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UOneEngineSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SDKRegion_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SDKRegion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_AppID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_Env,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_OneAppKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_PSMainlandConfigData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_PSOverseaConfigData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_HarmonyOSConfigFilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_bShowSplashBackGroundImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SplashShowTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UOneEngineSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineEditor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UOneEngineSettings_Statics::ClassParams = {
	&UOneEngineSettings::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UOneEngineSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSettings_Statics::PropPointers),
	0,
	0x001000A6u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UOneEngineSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UOneEngineSettings()
{
	if (!Z_Registration_Info_UClass_UOneEngineSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UOneEngineSettings.OuterSingleton, Z_Construct_UClass_UOneEngineSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UOneEngineSettings.OuterSingleton;
}
UOneEngineSettings::UOneEngineSettings(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UOneEngineSettings);
UOneEngineSettings::~UOneEngineSettings() {}
// ********** End Class UOneEngineSettings *********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h__Script_OneEngineEditor_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ESDKRegionConfig_StaticEnum, TEXT("ESDKRegionConfig"), &Z_Registration_Info_UEnum_ESDKRegionConfig, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1140499994U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UOneEngineSettings, UOneEngineSettings::StaticClass, TEXT("UOneEngineSettings"), &Z_Registration_Info_UClass_UOneEngineSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UOneEngineSettings), 2547134600U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h__Script_OneEngineEditor_3098337357(TEXT("/Script/OneEngineEditor"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h__Script_OneEngineEditor_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h__Script_OneEngineEditor_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h__Script_OneEngineEditor_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h__Script_OneEngineEditor_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
