// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDKPSSubsystem.h"
#include "Engine/GameInstance.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeOneEngineSDKPSSubsystem() {}

// ********** Begin Cross Module References ********************************************************
ENGINE_API UClass* Z_Construct_UClass_UBlueprintFunctionLibrary();
ENGINE_API UClass* Z_Construct_UClass_UGameInstanceSubsystem();
ONEENGINESDK_API UClass* Z_Construct_UClass_UOneEngineSDKPSSubsystem();
ONEENGINESDK_API UClass* Z_Construct_UClass_UOneEngineSDKPSSubsystem_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UOnePSLocaleEnum();
ONEENGINESDK_API UClass* Z_Construct_UClass_UOnePSLocaleEnum_NoRegister();
ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState();
ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos();
ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature();
ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature();
ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature();
ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature();
ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature();
ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature();
ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature();
ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSProduct();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductCategory();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductMedia();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductMediaImage();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductSku();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSPurchaseForm();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSUserProfile();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSUserProfileResponse();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EOnePSStoreIconPos ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EOnePSStoreIconPos;
static UEnum* EOnePSStoreIconPos_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EOnePSStoreIconPos.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EOnePSStoreIconPos.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOnePSStoreIconPos"));
	}
	return Z_Registration_Info_UEnum_EOnePSStoreIconPos.OuterSingleton;
}
template<> ONEENGINESDK_API UEnum* StaticEnum<EOnePSStoreIconPos>()
{
	return EOnePSStoreIconPos_StaticEnum();
}
struct Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Center.Name", "EOnePSStoreIconPos::Center" },
		{ "Left.Name", "EOnePSStoreIconPos::Left" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "Right.Name", "EOnePSStoreIconPos::Right" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EOnePSStoreIconPos::Center", (int64)EOnePSStoreIconPos::Center },
		{ "EOnePSStoreIconPos::Left", (int64)EOnePSStoreIconPos::Left },
		{ "EOnePSStoreIconPos::Right", (int64)EOnePSStoreIconPos::Right },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	"EOnePSStoreIconPos",
	"EOnePSStoreIconPos",
	Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos_Statics::Enum_MetaDataParams), Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos()
{
	if (!Z_Registration_Info_UEnum_EOnePSStoreIconPos.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EOnePSStoreIconPos.InnerSingleton, Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EOnePSStoreIconPos.InnerSingleton;
}
// ********** End Enum EOnePSStoreIconPos **********************************************************

// ********** Begin Enum EOnePsnAccountState *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EOnePsnAccountState;
static UEnum* EOnePsnAccountState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EOnePsnAccountState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EOnePsnAccountState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOnePsnAccountState"));
	}
	return Z_Registration_Info_UEnum_EOnePsnAccountState.OuterSingleton;
}
template<> ONEENGINESDK_API UEnum* StaticEnum<EOnePsnAccountState>()
{
	return EOnePsnAccountState_StaticEnum();
}
struct Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "SignedIn.Name", "EOnePsnAccountState::SignedIn" },
		{ "SignedOut.Name", "EOnePsnAccountState::SignedOut" },
		{ "Unknown.Name", "EOnePsnAccountState::Unknown" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EOnePsnAccountState::Unknown", (int64)EOnePsnAccountState::Unknown },
		{ "EOnePsnAccountState::SignedOut", (int64)EOnePsnAccountState::SignedOut },
		{ "EOnePsnAccountState::SignedIn", (int64)EOnePsnAccountState::SignedIn },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	"EOnePsnAccountState",
	"EOnePsnAccountState",
	Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState()
{
	if (!Z_Registration_Info_UEnum_EOnePsnAccountState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EOnePsnAccountState.InnerSingleton, Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EOnePsnAccountState.InnerSingleton;
}
// ********** End Enum EOnePsnAccountState *********************************************************

// ********** Begin ScriptStruct FOnePSUserProfile *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOnePSUserProfile;
class UScriptStruct* FOnePSUserProfile::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSUserProfile.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOnePSUserProfile.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSUserProfile, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSUserProfile"));
	}
	return Z_Registration_Info_UScriptStruct_FOnePSUserProfile.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOnePSUserProfile_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccountId_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnlineId_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvatarUrl_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AboutMe_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsOfficiallyVerified_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SDKuid_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ServerId_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RoleId_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RoleName_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AccountId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OnlineId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AvatarUrl;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AboutMe;
	static void NewProp_bIsOfficiallyVerified_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsOfficiallyVerified;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SDKuid;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ServerId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RoleId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RoleName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSUserProfile>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AccountId = { "AccountId", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSUserProfile, AccountId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccountId_MetaData), NewProp_AccountId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_OnlineId = { "OnlineId", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSUserProfile, OnlineId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnlineId_MetaData), NewProp_OnlineId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AvatarUrl = { "AvatarUrl", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSUserProfile, AvatarUrl), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvatarUrl_MetaData), NewProp_AvatarUrl_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AboutMe = { "AboutMe", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSUserProfile, AboutMe), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AboutMe_MetaData), NewProp_AboutMe_MetaData) };
void Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_bIsOfficiallyVerified_SetBit(void* Obj)
{
	((FOnePSUserProfile*)Obj)->bIsOfficiallyVerified = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_bIsOfficiallyVerified = { "bIsOfficiallyVerified", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FOnePSUserProfile), &Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_bIsOfficiallyVerified_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsOfficiallyVerified_MetaData), NewProp_bIsOfficiallyVerified_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_SDKuid = { "SDKuid", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSUserProfile, SDKuid), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SDKuid_MetaData), NewProp_SDKuid_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_ServerId = { "ServerId", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSUserProfile, ServerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ServerId_MetaData), NewProp_ServerId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_RoleId = { "RoleId", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSUserProfile, RoleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RoleId_MetaData), NewProp_RoleId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_RoleName = { "RoleName", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSUserProfile, RoleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RoleName_MetaData), NewProp_RoleName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AccountId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_OnlineId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AvatarUrl,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AboutMe,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_bIsOfficiallyVerified,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_SDKuid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_ServerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_RoleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_RoleName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OnePSUserProfile",
	Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::PropPointers),
	sizeof(FOnePSUserProfile),
	alignof(FOnePSUserProfile),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOnePSUserProfile()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSUserProfile.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOnePSUserProfile.InnerSingleton, Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOnePSUserProfile.InnerSingleton;
}
// ********** End ScriptStruct FOnePSUserProfile ***************************************************

// ********** Begin ScriptStruct FOnePSUserProfileResponse *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOnePSUserProfileResponse;
class UScriptStruct* FOnePSUserProfileResponse::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSUserProfileResponse.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOnePSUserProfileResponse.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSUserProfileResponse, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSUserProfileResponse"));
	}
	return Z_Registration_Info_UScriptStruct_FOnePSUserProfileResponse.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Profiles_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Total_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NextOffset_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreviousOffset_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Profiles_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Profiles;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Total;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NextOffset;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PreviousOffset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSUserProfileResponse>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Profiles_Inner = { "Profiles", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FOnePSUserProfile, METADATA_PARAMS(0, nullptr) }; // 368822748
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Profiles = { "Profiles", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSUserProfileResponse, Profiles), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Profiles_MetaData), NewProp_Profiles_MetaData) }; // 368822748
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Total = { "Total", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSUserProfileResponse, Total), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Total_MetaData), NewProp_Total_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_NextOffset = { "NextOffset", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSUserProfileResponse, NextOffset), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NextOffset_MetaData), NewProp_NextOffset_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_PreviousOffset = { "PreviousOffset", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSUserProfileResponse, PreviousOffset), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreviousOffset_MetaData), NewProp_PreviousOffset_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Profiles_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Profiles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Total,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_NextOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_PreviousOffset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OnePSUserProfileResponse",
	Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::PropPointers),
	sizeof(FOnePSUserProfileResponse),
	alignof(FOnePSUserProfileResponse),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOnePSUserProfileResponse()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSUserProfileResponse.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOnePSUserProfileResponse.InnerSingleton, Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOnePSUserProfileResponse.InnerSingleton;
}
// ********** End ScriptStruct FOnePSUserProfileResponse *******************************************

// ********** Begin ScriptStruct FOnePSPurchaseForm ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOnePSPurchaseForm;
class UScriptStruct* FOnePSPurchaseForm::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSPurchaseForm.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOnePSPurchaseForm.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSPurchaseForm, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSPurchaseForm"));
	}
	return Z_Registration_Info_UScriptStruct_FOnePSPurchaseForm.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameOrderId_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe6\xb8\xb8\xe6\x88\x8f\xe8\xae\xa2\xe5\x8d\x95Id\xef\xbc\x8c\xe5\xba\x94\xe7\x94\xb1\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe7\x94\x9f\xe6\x88\x90\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe6\xb8\xb8\xe6\x88\x8f\xe8\xae\xa2\xe5\x8d\x95Id\xef\xbc\x8c\xe5\xba\x94\xe7\x94\xb1\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe7\x94\x9f\xe6\x88\x90" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameRoleId_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe5\xbd\x93\xe5\x89\x8d\xe6\xb8\xb8\xe6\x88\x8f\xe8\xa7\x92\xe8\x89\xb2\xe4\xbf\xa1\xe6\x81\xaf\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe5\xbd\x93\xe5\x89\x8d\xe6\xb8\xb8\xe6\x88\x8f\xe8\xa7\x92\xe8\x89\xb2\xe4\xbf\xa1\xe6\x81\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameRoleName_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe5\xbd\x93\xe5\x89\x8d\xe6\xb8\xb8\xe6\x88\x8f\xe8\xa7\x92\xe8\x89\xb2\xe5\x90\x8d\xe7\xa7\xb0\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe5\xbd\x93\xe5\x89\x8d\xe6\xb8\xb8\xe6\x88\x8f\xe8\xa7\x92\xe8\x89\xb2\xe5\x90\x8d\xe7\xa7\xb0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameServerId_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe5\xbd\x93\xe5\x89\x8d\xe6\xb8\xb8\xe6\x88\x8f\xe8\xa7\x92\xe8\x89\xb2\xe6\x89\x80\xe5\x9c\xa8\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8ID\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe5\xbd\x93\xe5\x89\x8d\xe6\xb8\xb8\xe6\x88\x8f\xe8\xa7\x92\xe8\x89\xb2\xe6\x89\x80\xe5\x9c\xa8\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductId_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe4\xba\xa7\xe5\x93\x81ID\xef\xbc\x8c\xe8\xaf\xa5\xe5\x80\xbc\xe5\x9c\xa8PS\xe5\xb9\xb3\xe5\x8f\xb0\xe4\xb8\x8a\xef\xbc\x8c\xe5\xba\x94\xe4\xbd\xbf\xe7\x94\xa8 FOnePSProduct.Label \xe5\xad\x97\xe6\xae\xb5\xe5\xb1\x9e\xe6\x80\xa7\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe4\xba\xa7\xe5\x93\x81ID\xef\xbc\x8c\xe8\xaf\xa5\xe5\x80\xbc\xe5\x9c\xa8PS\xe5\xb9\xb3\xe5\x8f\xb0\xe4\xb8\x8a\xef\xbc\x8c\xe5\xba\x94\xe4\xbd\xbf\xe7\x94\xa8 FOnePSProduct.Label \xe5\xad\x97\xe6\xae\xb5\xe5\xb1\x9e\xe6\x80\xa7" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductName_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe4\xba\xa7\xe5\x93\x81\xe5\x90\x8d\xe7\xa7\xb0\xef\xbc\x8c\xe8\xaf\xa5\xe5\x80\xbc\xe5\x9c\xa8PS\xe5\xb9\xb3\xe5\x8f\xb0\xe4\xb8\x8a\xef\xbc\x8c\xe5\xba\x94\xe4\xbd\xbf\xe7\x94\xa8 FOnePSProduct.DisplayName \xe5\xad\x97\xe6\xae\xb5\xe5\xb1\x9e\xe6\x80\xa7\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe4\xba\xa7\xe5\x93\x81\xe5\x90\x8d\xe7\xa7\xb0\xef\xbc\x8c\xe8\xaf\xa5\xe5\x80\xbc\xe5\x9c\xa8PS\xe5\xb9\xb3\xe5\x8f\xb0\xe4\xb8\x8a\xef\xbc\x8c\xe5\xba\x94\xe4\xbd\xbf\xe7\x94\xa8 FOnePSProduct.DisplayName \xe5\xad\x97\xe6\xae\xb5\xe5\xb1\x9e\xe6\x80\xa7" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OrderAmount_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe8\xae\xa2\xe5\x8d\x95\xe6\x80\xbb\xe9\x87\x91\xe9\xa2\x9d\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe8\xae\xa2\xe5\x8d\x95\xe6\x80\xbb\xe9\x87\x91\xe9\xa2\x9d" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameExtraInfo_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe9\x80\x8f\xe4\xbc\xa0\xe7\xbb\x99\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe7\xab\xaf\xe7\x9a\x84\xe4\xbf\xa1\xe6\x81\xaf\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe9\x80\x8f\xe4\xbc\xa0\xe7\xbb\x99\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe7\xab\xaf\xe7\x9a\x84\xe4\xbf\xa1\xe6\x81\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameDescription_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe9\x80\x8f\xe4\xbc\xa0\xe7\xbb\x99\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe7\xab\xaf\xe7\x9a\x84\xe4\xbf\xa1\xe6\x81\xaf\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe9\x80\x8f\xe4\xbc\xa0\xe7\xbb\x99\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe7\xab\xaf\xe7\x9a\x84\xe4\xbf\xa1\xe6\x81\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PSNServerLabel_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief PSN\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe6\xa0\x87\xe7\xad\xbe, \xe7\x9b\xae\xe5\x89\x8d\xe9\x83\xbd\xe6\x98\xaf\xe9\xbb\x98\xe8\xae\xa4\xe5\x85\xb1\xe4\xba\xabPS4\xe5\x95\x86\xe5\xba\x97\xe7\x9a\x84\xe5\x81\x9a\xe6\xb3\x95\xef\xbc\x8cPS4 \xe5\xba\x94\xe8\xaf\xa5\xe4\xbc\xa0 0\xef\xbc\x8cPS5 \xe4\xb8\xba 1\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief PSN\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe6\xa0\x87\xe7\xad\xbe, \xe7\x9b\xae\xe5\x89\x8d\xe9\x83\xbd\xe6\x98\xaf\xe9\xbb\x98\xe8\xae\xa4\xe5\x85\xb1\xe4\xba\xabPS4\xe5\x95\x86\xe5\xba\x97\xe7\x9a\x84\xe5\x81\x9a\xe6\xb3\x95\xef\xbc\x8cPS4 \xe5\xba\x94\xe8\xaf\xa5\xe4\xbc\xa0 0\xef\xbc\x8cPS5 \xe4\xb8\xba 1" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GameOrderId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GameRoleId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GameRoleName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GameServerId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OrderAmount;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GameExtraInfo;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GameDescription;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PSNServerLabel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSPurchaseForm>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameOrderId = { "GameOrderId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSPurchaseForm, GameOrderId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameOrderId_MetaData), NewProp_GameOrderId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameRoleId = { "GameRoleId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSPurchaseForm, GameRoleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameRoleId_MetaData), NewProp_GameRoleId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameRoleName = { "GameRoleName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSPurchaseForm, GameRoleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameRoleName_MetaData), NewProp_GameRoleName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameServerId = { "GameServerId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSPurchaseForm, GameServerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameServerId_MetaData), NewProp_GameServerId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_ProductId = { "ProductId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSPurchaseForm, ProductId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductId_MetaData), NewProp_ProductId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_ProductName = { "ProductName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSPurchaseForm, ProductName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductName_MetaData), NewProp_ProductName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_OrderAmount = { "OrderAmount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSPurchaseForm, OrderAmount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OrderAmount_MetaData), NewProp_OrderAmount_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameExtraInfo = { "GameExtraInfo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSPurchaseForm, GameExtraInfo), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameExtraInfo_MetaData), NewProp_GameExtraInfo_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameDescription = { "GameDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSPurchaseForm, GameDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameDescription_MetaData), NewProp_GameDescription_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_PSNServerLabel = { "PSNServerLabel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSPurchaseForm, PSNServerLabel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PSNServerLabel_MetaData), NewProp_PSNServerLabel_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameOrderId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameRoleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameRoleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameServerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_ProductId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_ProductName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_OrderAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameExtraInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_PSNServerLabel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OnePSPurchaseForm",
	Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::PropPointers),
	sizeof(FOnePSPurchaseForm),
	alignof(FOnePSPurchaseForm),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOnePSPurchaseForm()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSPurchaseForm.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOnePSPurchaseForm.InnerSingleton, Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOnePSPurchaseForm.InnerSingleton;
}
// ********** End ScriptStruct FOnePSPurchaseForm **************************************************

// ********** Begin ScriptStruct FOnePSProductMediaImage *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOnePSProductMediaImage;
class UScriptStruct* FOnePSProductMediaImage::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSProductMediaImage.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOnePSProductMediaImage.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSProductMediaImage, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSProductMediaImage"));
	}
	return Z_Registration_Info_UScriptStruct_FOnePSProductMediaImage.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Format_MetaData[] = {
		{ "Category", "OnePSProductMediaImage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe5\xa6\x82,JPEG,PNG,GIF,WEBP\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe5\xa6\x82,JPEG,PNG,GIF,WEBP" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[] = {
		{ "Category", "OnePSProductMediaImage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe5\x9c\xa8PSN\xe4\xbd\xbf\xe7\x94\xa8\xe7\xb1\xbb\xe5\x9e\x8b\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe5\x9c\xa8PSN\xe4\xbd\xbf\xe7\x94\xa8\xe7\xb1\xbb\xe5\x9e\x8b" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Url_MetaData[] = {
		{ "Category", "OnePSProductMediaImage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe9\x9d\x99\xe6\x80\x81\xe5\x9b\xbe\xe7\x89\x87URL\xe5\x9c\xb0\xe5\x9d\x80\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe9\x9d\x99\xe6\x80\x81\xe5\x9b\xbe\xe7\x89\x87URL\xe5\x9c\xb0\xe5\x9d\x80" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Format;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Type;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Url;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSProductMediaImage>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Format = { "Format", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductMediaImage, Format), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Format_MetaData), NewProp_Format_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductMediaImage, Type), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Type_MetaData), NewProp_Type_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Url = { "Url", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductMediaImage, Url), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Url_MetaData), NewProp_Url_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Format,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Url,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OnePSProductMediaImage",
	Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::PropPointers),
	sizeof(FOnePSProductMediaImage),
	alignof(FOnePSProductMediaImage),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductMediaImage()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSProductMediaImage.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOnePSProductMediaImage.InnerSingleton, Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOnePSProductMediaImage.InnerSingleton;
}
// ********** End ScriptStruct FOnePSProductMediaImage *********************************************

// ********** Begin ScriptStruct FOnePSProductMedia ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOnePSProductMedia;
class UScriptStruct* FOnePSProductMedia::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSProductMedia.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOnePSProductMedia.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSProductMedia, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSProductMedia"));
	}
	return Z_Registration_Info_UScriptStruct_FOnePSProductMedia.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOnePSProductMedia_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Images_MetaData[] = {
		{ "Category", "OnePSProductMedia" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Images_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Images;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSProductMedia>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::NewProp_Images_Inner = { "Images", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FOnePSProductMediaImage, METADATA_PARAMS(0, nullptr) }; // 657213321
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::NewProp_Images = { "Images", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductMedia, Images), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Images_MetaData), NewProp_Images_MetaData) }; // 657213321
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::NewProp_Images_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::NewProp_Images,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OnePSProductMedia",
	Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::PropPointers),
	sizeof(FOnePSProductMedia),
	alignof(FOnePSProductMedia),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductMedia()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSProductMedia.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOnePSProductMedia.InnerSingleton, Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOnePSProductMedia.InnerSingleton;
}
// ********** End ScriptStruct FOnePSProductMedia **************************************************

// ********** Begin ScriptStruct FOnePSProductSku **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOnePSProductSku;
class UScriptStruct* FOnePSProductSku::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSProductSku.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOnePSProductSku.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSProductSku, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSProductSku"));
	}
	return Z_Registration_Info_UScriptStruct_FOnePSProductSku.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOnePSProductSku_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ID_MetaData[] = {
		{ "Category", "OnePSProductSku" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief id\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief id" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[] = {
		{ "Category", "OnePSProductSku" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe5\x90\x8d\xe7\xa7\xb0\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe5\x90\x8d\xe7\xa7\xb0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Label_MetaData[] = {
		{ "Category", "OnePSProductSku" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe6\xa0\x87\xe7\xad\xbe\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe6\xa0\x87\xe7\xad\xbe" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndDate_MetaData[] = {
		{ "Category", "OnePSProductSku" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xa5\xe6\x9c\x9f\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xa5\xe6\x9c\x9f" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[] = {
		{ "Category", "OnePSProductSku" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe7\xb1\xbb\xe5\x9e\x8b\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe7\xb1\xbb\xe5\x9e\x8b" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Price_MetaData[] = {
		{ "Category", "OnePSProductSku" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief  \xe4\xbb\xb7\xe6\xa0\xbc\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief  \xe4\xbb\xb7\xe6\xa0\xbc" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayPrice_MetaData[] = {
		{ "Category", "OnePSProductSku" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe8\xa1\xa8\xe7\xa4\xba\xe6\xad\xa4\xe4\xba\xa7\xe5\x93\x81\xe7\x9a\x84\xe6\x98\xbe\xe7\xa4\xba\xe4\xbb\xb7\xe6\xa0\xbc\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe5\xad\x98\xe5\x9c\xa8\xe4\xbb\xbb\xe4\xbd\x95\xe6\x8a\x98\xe6\x89\xa3\xef\xbc\x8c\xe5\x88\x99\xe6\x98\xbe\xe7\xa4\xba\xe7\x9a\x84\xe4\xbb\xb7\xe6\xa0\xbc\xe5\x8c\x85\xe5\x90\xab\xe6\x8a\x98\xe6\x89\xa3\xe4\xbb\xb7\xe6\xa0\xbc\xe3\x80\x82\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe8\xa1\xa8\xe7\xa4\xba\xe6\xad\xa4\xe4\xba\xa7\xe5\x93\x81\xe7\x9a\x84\xe6\x98\xbe\xe7\xa4\xba\xe4\xbb\xb7\xe6\xa0\xbc\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe5\xad\x98\xe5\x9c\xa8\xe4\xbb\xbb\xe4\xbd\x95\xe6\x8a\x98\xe6\x89\xa3\xef\xbc\x8c\xe5\x88\x99\xe6\x98\xbe\xe7\xa4\xba\xe7\x9a\x84\xe4\xbb\xb7\xe6\xa0\xbc\xe5\x8c\x85\xe5\x90\xab\xe6\x8a\x98\xe6\x89\xa3\xe4\xbb\xb7\xe6\xa0\xbc\xe3\x80\x82" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalPrice_MetaData[] = {
		{ "Category", "OnePSProductSku" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe8\xa1\xa8\xe7\xa4\xba\xe6\xad\xa4\xe4\xba\xa7\xe5\x93\x81\xe7\x9a\x84\xe5\x8e\x9f\xe5\xa7\x8b\xe4\xbb\xb7\xe6\xa0\xbc\xe3\x80\x82\xe5\xbd\x93\xe5\xad\x98\xe5\x9c\xa8\xe4\xbb\xbb\xe4\xbd\x95\xe6\x8a\x98\xe6\x89\xa3\xef\xbc\x8c\xe8\xaf\xa5\xe5\xad\x97\xe6\xae\xb5\xe6\x9c\x89\xe5\x80\xbc\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe8\xa1\xa8\xe7\xa4\xba\xe6\xad\xa4\xe4\xba\xa7\xe5\x93\x81\xe7\x9a\x84\xe5\x8e\x9f\xe5\xa7\x8b\xe4\xbb\xb7\xe6\xa0\xbc\xe3\x80\x82\xe5\xbd\x93\xe5\xad\x98\xe5\x9c\xa8\xe4\xbb\xbb\xe4\xbd\x95\xe6\x8a\x98\xe6\x89\xa3\xef\xbc\x8c\xe8\xaf\xa5\xe5\xad\x97\xe6\xae\xb5\xe6\x9c\x89\xe5\x80\xbc" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayOriginalPrice_MetaData[] = {
		{ "Category", "OnePSProductSku" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe8\xa1\xa8\xe7\xa4\xba\xe5\x8e\x9f\xe5\xa7\x8b\xe4\xbb\xb7\xe6\xa0\xbc\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe3\x80\x82\xe5\xa7\x8b\xe7\xbb\x88\xe4\xb8\x8e\xe5\x88\xa0\xe9\x99\xa4\xe7\xba\xbf\xe6\x90\xad\xe9\x85\x8d\xe4\xbd\xbf\xe7\x94\xa8\xe3\x80\x82\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe8\xa1\xa8\xe7\xa4\xba\xe5\x8e\x9f\xe5\xa7\x8b\xe4\xbb\xb7\xe6\xa0\xbc\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe3\x80\x82\xe5\xa7\x8b\xe7\xbb\x88\xe4\xb8\x8e\xe5\x88\xa0\xe9\x99\xa4\xe7\xba\xbf\xe6\x90\xad\xe9\x85\x8d\xe4\xbd\xbf\xe7\x94\xa8\xe3\x80\x82" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UseLimit_MetaData[] = {
		{ "Category", "OnePSProductSku" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief skus \xe5\x8d\x95\xe4\xbd\x8d\xe6\x95\xb0\xe9\x87\x8f\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief skus \xe5\x8d\x95\xe4\xbd\x8d\xe6\x95\xb0\xe9\x87\x8f" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlusUpsellPrice_MetaData[] = {
		{ "Category", "OnePSProductSku" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief Plus \xe4\xbc\x9a\xe5\x91\x98\xe6\x8a\x98\xe6\x89\xa3\xe4\xbb\xb7\xe3\x80\x82\xe6\xad\xa4\xe4\xbb\xb7\xe6\xa0\xbc\xe6\x98\xaf\xe4\xb8\x80\xe4\xb8\xaa\xe6\x95\xb4\xe6\x95\xb0\xef\xbc\x8c\xe4\xb8\x8d\xe8\xa1\xa8\xe7\xa4\xba\xe8\xb4\xa7\xe5\xb8\x81\xe4\xbb\xa3\xe7\xa0\x81\xe6\x88\x96\xe7\xac\xa6\xe5\x8f\xb7\xe3\x80\x82\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief Plus \xe4\xbc\x9a\xe5\x91\x98\xe6\x8a\x98\xe6\x89\xa3\xe4\xbb\xb7\xe3\x80\x82\xe6\xad\xa4\xe4\xbb\xb7\xe6\xa0\xbc\xe6\x98\xaf\xe4\xb8\x80\xe4\xb8\xaa\xe6\x95\xb4\xe6\x95\xb0\xef\xbc\x8c\xe4\xb8\x8d\xe8\xa1\xa8\xe7\xa4\xba\xe8\xb4\xa7\xe5\xb8\x81\xe4\xbb\xa3\xe7\xa0\x81\xe6\x88\x96\xe7\xac\xa6\xe5\x8f\xb7\xe3\x80\x82" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayPlusUpsellPrice_MetaData[] = {
		{ "Category", "OnePSProductSku" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe5\xb1\x95\xe7\xa4\xba Plus \xe4\xbc\x9a\xe5\x91\x98\xe6\x8a\x98\xe6\x89\xa3\xe4\xbb\xb7\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe3\x80\x82\xe3\x80\x82\xe5\xa7\x8b\xe7\xbb\x88\xe4\xb8\x8e PlayStation\xc2\xaePlus \xe5\x9b\xbe\xe6\xa0\x87\xe6\x90\xad\xe9\x85\x8d\xe4\xbd\xbf\xe7\x94\xa8\xe3\x80\x82\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe5\xb1\x95\xe7\xa4\xba Plus \xe4\xbc\x9a\xe5\x91\x98\xe6\x8a\x98\xe6\x89\xa3\xe4\xbb\xb7\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe3\x80\x82\xe3\x80\x82\xe5\xa7\x8b\xe7\xbb\x88\xe4\xb8\x8e PlayStation\xc2\xaePlus \xe5\x9b\xbe\xe6\xa0\x87\xe6\x90\xad\xe9\x85\x8d\xe4\xbd\xbf\xe7\x94\xa8\xe3\x80\x82" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IsPlusPrice_MetaData[] = {
		{ "Category", "OnePSProductSku" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief  \xe4\xb8\x80\xe4\xb8\xaa\xe5\xb8\x83\xe5\xb0\x94\xe5\x80\xbc\xef\xbc\x8c\xe8\xa1\xa8\xe7\xa4\xba price \xe5\xad\x97\xe6\xae\xb5\xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba Plus \xe4\xbc\x9a\xe5\x91\x98\xe4\xbb\xb7\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief  \xe4\xb8\x80\xe4\xb8\xaa\xe5\xb8\x83\xe5\xb0\x94\xe5\x80\xbc\xef\xbc\x8c\xe8\xa1\xa8\xe7\xa4\xba price \xe5\xad\x97\xe6\xae\xb5\xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba Plus \xe4\xbc\x9a\xe5\x91\x98\xe4\xbb\xb7" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnnotationName_MetaData[] = {
		{ "Category", "OnePSProductSku" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe6\x8c\x87\xe7\xa4\xba Sku \xe8\xb4\xad\xe4\xb9\xb0\xe7\x8a\xb6\xe6\x80\x81\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe5\x80\xbc\xe3\x80\x82\xe2\x80\x9cNONE\xe2\x80\x9d: \xe6\x9c\xaa\xe8\xb4\xad\xe4\xb9\xb0\xe3\x80\x82\xe2\x80\x9c""BLUE_BAG\xe2\x80\x9d: \xe5\xb7\xb2\xe8\xb4\xad\xe4\xb9\xb0\xef\xbc\x8c\xe5\x8f\xaf\xe9\x87\x8d\xe6\x96\xb0\xe8\xb4\xad\xe4\xb9\xb0\xe3\x80\x82\xe2\x80\x9cRED_BAG\xe2\x80\x9d: \xe5\xb7\xb2\xe8\xb4\xad\xe4\xb9\xb0\xef\xbc\x8c\xe4\xb8\x8d\xe5\x8f\xaf\xe9\x87\x8d\xe6\x96\xb0\xe8\xb4\xad\xe4\xb9\xb0\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe6\x8c\x87\xe7\xa4\xba Sku \xe8\xb4\xad\xe4\xb9\xb0\xe7\x8a\xb6\xe6\x80\x81\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe5\x80\xbc\xe3\x80\x82\xe2\x80\x9cNONE\xe2\x80\x9d: \xe6\x9c\xaa\xe8\xb4\xad\xe4\xb9\xb0\xe3\x80\x82\xe2\x80\x9c""BLUE_BAG\xe2\x80\x9d: \xe5\xb7\xb2\xe8\xb4\xad\xe4\xb9\xb0\xef\xbc\x8c\xe5\x8f\xaf\xe9\x87\x8d\xe6\x96\xb0\xe8\xb4\xad\xe4\xb9\xb0\xe3\x80\x82\xe2\x80\x9cRED_BAG\xe2\x80\x9d: \xe5\xb7\xb2\xe8\xb4\xad\xe4\xb9\xb0\xef\xbc\x8c\xe4\xb8\x8d\xe5\x8f\xaf\xe9\x87\x8d\xe6\x96\xb0\xe8\xb4\xad\xe4\xb9\xb0" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Name;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Label;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EndDate;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Type;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Price;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DisplayPrice;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OriginalPrice;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DisplayOriginalPrice;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UseLimit;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlusUpsellPrice;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DisplayPlusUpsellPrice;
	static void NewProp_IsPlusPrice_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_IsPlusPrice;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AnnotationName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSProductSku>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_ID = { "ID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductSku, ID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ID_MetaData), NewProp_ID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductSku, Name), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Name_MetaData), NewProp_Name_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Label = { "Label", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductSku, Label), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Label_MetaData), NewProp_Label_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_EndDate = { "EndDate", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductSku, EndDate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndDate_MetaData), NewProp_EndDate_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductSku, Type), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Type_MetaData), NewProp_Type_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Price = { "Price", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductSku, Price), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Price_MetaData), NewProp_Price_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayPrice = { "DisplayPrice", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductSku, DisplayPrice), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayPrice_MetaData), NewProp_DisplayPrice_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_OriginalPrice = { "OriginalPrice", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductSku, OriginalPrice), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalPrice_MetaData), NewProp_OriginalPrice_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayOriginalPrice = { "DisplayOriginalPrice", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductSku, DisplayOriginalPrice), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayOriginalPrice_MetaData), NewProp_DisplayOriginalPrice_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_UseLimit = { "UseLimit", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductSku, UseLimit), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UseLimit_MetaData), NewProp_UseLimit_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_PlusUpsellPrice = { "PlusUpsellPrice", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductSku, PlusUpsellPrice), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlusUpsellPrice_MetaData), NewProp_PlusUpsellPrice_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayPlusUpsellPrice = { "DisplayPlusUpsellPrice", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductSku, DisplayPlusUpsellPrice), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayPlusUpsellPrice_MetaData), NewProp_DisplayPlusUpsellPrice_MetaData) };
void Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_IsPlusPrice_SetBit(void* Obj)
{
	((FOnePSProductSku*)Obj)->IsPlusPrice = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_IsPlusPrice = { "IsPlusPrice", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FOnePSProductSku), &Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_IsPlusPrice_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IsPlusPrice_MetaData), NewProp_IsPlusPrice_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_AnnotationName = { "AnnotationName", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductSku, AnnotationName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnnotationName_MetaData), NewProp_AnnotationName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSProductSku_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_ID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Name,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Label,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_EndDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Price,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayPrice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_OriginalPrice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayOriginalPrice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_UseLimit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_PlusUpsellPrice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayPlusUpsellPrice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_IsPlusPrice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_AnnotationName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OnePSProductSku",
	Z_Construct_UScriptStruct_FOnePSProductSku_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::PropPointers),
	sizeof(FOnePSProductSku),
	alignof(FOnePSProductSku),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOnePSProductSku_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductSku()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSProductSku.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOnePSProductSku.InnerSingleton, Z_Construct_UScriptStruct_FOnePSProductSku_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOnePSProductSku.InnerSingleton;
}
// ********** End ScriptStruct FOnePSProductSku ****************************************************

// ********** Begin ScriptStruct FOnePSProduct *****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOnePSProduct;
class UScriptStruct* FOnePSProduct::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSProduct.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOnePSProduct.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSProduct, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSProduct"));
	}
	return Z_Registration_Info_UScriptStruct_FOnePSProduct.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOnePSProduct_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ID_MetaData[] = {
		{ "Category", "OnePSProduct" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief ID\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Label_MetaData[] = {
		{ "Category", "OnePSProduct" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe6\xa0\x87\xe7\xad\xbe\xef\xbc\x8c\xe8\xb4\xad\xe4\xb9\xb0\xe4\xba\xa7\xe5\x93\x81\xe9\x9c\x80\xe8\xa6\x81\xe4\xbc\xa0\xe5\x85\xa5\xe8\xaf\xa5\xe5\xad\x97\xe6\xae\xb5\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe6\xa0\x87\xe7\xad\xbe\xef\xbc\x8c\xe8\xb4\xad\xe4\xb9\xb0\xe4\xba\xa7\xe5\x93\x81\xe9\x9c\x80\xe8\xa6\x81\xe4\xbc\xa0\xe5\x85\xa5\xe8\xaf\xa5\xe5\xad\x97\xe6\xae\xb5" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AgeLimit_MetaData[] = {
		{ "Category", "OnePSProduct" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe5\x85\x81\xe8\xae\xb8\xe7\x94\xa8\xe6\x88\xb7\xe6\x9f\xa5\xe7\x9c\x8b\xe6\xb8\xb8\xe6\x88\x8f\xe4\xb8\xad\xe5\x95\x86\xe5\xba\x97\xe4\xb8\xad\xe5\x95\x86\xe5\x93\x81\xe7\x9a\x84\xe6\x9c\x80\xe4\xbd\x8e\xe5\xb9\xb4\xe9\xbe\x84\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe5\xb9\xb4\xe9\xbe\x84\xe5\xb0\x8f\xe4\xba\x8e\xe6\xad\xa4\xe5\x80\xbc\xef\xbc\x8c\xe5\x88\x99\xe4\xba\xa7\xe5\x93\x81\xe4\xb8\x8d\xe5\x8c\x85\xe5\x90\xab\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xbb\xe4\xbd\x93\xe4\xb8\xad\xe3\x80\x82\xe5\x80\xbc\xe4\xb8\xba""0\xe8\xa1\xa8\xe7\xa4\xba\xe8\xaf\xa5\xe9\xa1\xb9\xe6\xb2\xa1\xe6\x9c\x89\xe5\xb9\xb4\xe9\xbe\x84\xe8\xa6\x81\xe6\xb1\x82\xe3\x80\x82\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe5\x85\x81\xe8\xae\xb8\xe7\x94\xa8\xe6\x88\xb7\xe6\x9f\xa5\xe7\x9c\x8b\xe6\xb8\xb8\xe6\x88\x8f\xe4\xb8\xad\xe5\x95\x86\xe5\xba\x97\xe4\xb8\xad\xe5\x95\x86\xe5\x93\x81\xe7\x9a\x84\xe6\x9c\x80\xe4\xbd\x8e\xe5\xb9\xb4\xe9\xbe\x84\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe5\xb9\xb4\xe9\xbe\x84\xe5\xb0\x8f\xe4\xba\x8e\xe6\xad\xa4\xe5\x80\xbc\xef\xbc\x8c\xe5\x88\x99\xe4\xba\xa7\xe5\x93\x81\xe4\xb8\x8d\xe5\x8c\x85\xe5\x90\xab\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xbb\xe4\xbd\x93\xe4\xb8\xad\xe3\x80\x82\xe5\x80\xbc\xe4\xb8\xba""0\xe8\xa1\xa8\xe7\xa4\xba\xe8\xaf\xa5\xe9\xa1\xb9\xe6\xb2\xa1\xe6\x9c\x89\xe5\xb9\xb4\xe9\xbe\x84\xe8\xa6\x81\xe6\xb1\x82\xe3\x80\x82" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayName_MetaData[] = {
		{ "Category", "OnePSProduct" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe6\x9c\xac\xe5\x9c\xb0\xe5\x8c\x96\xe5\x90\x8d\xe7\xa7\xb0\xe3\x80\x82\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe6\x9c\xac\xe5\x9c\xb0\xe5\x8c\x96\xe5\x90\x8d\xe7\xa7\xb0\xe3\x80\x82" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "OnePSProduct" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe8\xaf\xa6\xe6\x83\x85\xe6\x8f\x8f\xe8\xbf\xb0\xe3\x80\x82\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe8\xaf\xa6\xe6\x83\x85\xe6\x8f\x8f\xe8\xbf\xb0\xe3\x80\x82" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[] = {
		{ "Category", "OnePSProduct" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe7\xb1\xbb\xe5\x9e\x8b, \xe5\x8f\xaf\xe8\x83\xbd\xe7\x9a\x84\xe5\x80\xbc\xef\xbc\x8c""category\xef\xbc\x8cproduct\xe3\x80\x82\n\x09 * @note \xe5\x8f\xaa\xe6\x9c\x89 Type \xe7\xad\x89\xe4\xba\x8e product \xef\xbc\x8c\xe6\x89\x8d\xe5\x85\x81\xe8\xae\xb8\xe8\xb4\xad\xe4\xb9\xb0\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe7\xb1\xbb\xe5\x9e\x8b, \xe5\x8f\xaf\xe8\x83\xbd\xe7\x9a\x84\xe5\x80\xbc\xef\xbc\x8c""category\xef\xbc\x8cproduct\xe3\x80\x82\n@note \xe5\x8f\xaa\xe6\x9c\x89 Type \xe7\xad\x89\xe4\xba\x8e product \xef\xbc\x8c\xe6\x89\x8d\xe5\x85\x81\xe8\xae\xb8\xe8\xb4\xad\xe4\xb9\xb0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Skus_MetaData[] = {
		{ "Category", "OnePSProduct" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe9\x94\x80\xe5\x94\xae\xe5\x8d\x95\xe4\xbd\x8d\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe5\xb1\x95\xe7\xa4\xba\xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc\xe4\xbf\xa1\xe6\x81\xaf\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe9\x94\x80\xe5\x94\xae\xe5\x8d\x95\xe4\xbd\x8d\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe5\xb1\x95\xe7\xa4\xba\xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc\xe4\xbf\xa1\xe6\x81\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Media_MetaData[] = {
		{ "Category", "OnePSProduct" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe5\xaa\x92\xe4\xbd\x93\xe4\xbf\xa1\xe6\x81\xaf\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe5\xaa\x92\xe4\xbd\x93\xe4\xbf\xa1\xe6\x81\xaf" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Label;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AgeLimit;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DisplayName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Type;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Skus_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Skus;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Media;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSProduct>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_ID = { "ID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProduct, ID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ID_MetaData), NewProp_ID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Label = { "Label", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProduct, Label), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Label_MetaData), NewProp_Label_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_AgeLimit = { "AgeLimit", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProduct, AgeLimit), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AgeLimit_MetaData), NewProp_AgeLimit_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_DisplayName = { "DisplayName", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProduct, DisplayName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayName_MetaData), NewProp_DisplayName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProduct, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProduct, Type), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Type_MetaData), NewProp_Type_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Skus_Inner = { "Skus", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FOnePSProductSku, METADATA_PARAMS(0, nullptr) }; // 4209802740
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Skus = { "Skus", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProduct, Skus), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Skus_MetaData), NewProp_Skus_MetaData) }; // 4209802740
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Media = { "Media", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProduct, Media), Z_Construct_UScriptStruct_FOnePSProductMedia, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Media_MetaData), NewProp_Media_MetaData) }; // 2579785438
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSProduct_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_ID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Label,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_AgeLimit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_DisplayName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Skus_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Skus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Media,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProduct_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OnePSProduct",
	Z_Construct_UScriptStruct_FOnePSProduct_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProduct_Statics::PropPointers),
	sizeof(FOnePSProduct),
	alignof(FOnePSProduct),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProduct_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOnePSProduct_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOnePSProduct()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSProduct.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOnePSProduct.InnerSingleton, Z_Construct_UScriptStruct_FOnePSProduct_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOnePSProduct.InnerSingleton;
}
// ********** End ScriptStruct FOnePSProduct *******************************************************

// ********** Begin ScriptStruct FOnePSProductCategory *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOnePSProductCategory;
class UScriptStruct* FOnePSProductCategory::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSProductCategory.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOnePSProductCategory.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSProductCategory, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSProductCategory"));
	}
	return Z_Registration_Info_UScriptStruct_FOnePSProductCategory.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOnePSProductCategory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Code_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe9\x9d\x9e 0 \xe8\xa1\xa8\xe7\xa4\xba\xe5\xbc\x82\xe5\xb8\xb8\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe9\x9d\x9e 0 \xe8\xa1\xa8\xe7\xa4\xba\xe5\xbc\x82\xe5\xb8\xb8" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RawData_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief PSN \xe8\xbf\x94\xe5\x9b\x9e\xe7\x9a\x84\xe5\x8e\x9f\xe5\xa7\x8b\xe6\x95\xb0\xe6\x8d\xae\xef\xbc\x8c\xe5\xa6\x82\xe8\xaf\xa5\xe7\xbb\x93\xe6\x9e\x84\xe4\xbd\x93\xe4\xb8\x8d\xe6\xbb\xa1\xe4\xbd\x8f\xe9\x9c\x80\xe6\xb1\x82\xef\xbc\x8c\xe5\x8f\xaf\xe4\xbd\xbf\xe7\x94\xa8 RawData \xe8\x87\xaa\xe5\xae\x9a\xe4\xb9\x89\xe5\xa4\x84\xe7\x90\x86\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief PSN \xe8\xbf\x94\xe5\x9b\x9e\xe7\x9a\x84\xe5\x8e\x9f\xe5\xa7\x8b\xe6\x95\xb0\xe6\x8d\xae\xef\xbc\x8c\xe5\xa6\x82\xe8\xaf\xa5\xe7\xbb\x93\xe6\x9e\x84\xe4\xbd\x93\xe4\xb8\x8d\xe6\xbb\xa1\xe4\xbd\x8f\xe9\x9c\x80\xe6\xb1\x82\xef\xbc\x8c\xe5\x8f\xaf\xe4\xbd\xbf\xe7\x94\xa8 RawData \xe8\x87\xaa\xe5\xae\x9a\xe4\xb9\x89\xe5\xa4\x84\xe7\x90\x86" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ID_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief ID\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Label_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe6\xa0\x87\xe7\xad\xbe\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe6\xa0\x87\xe7\xad\xbe" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AgeLimit_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe5\x85\x81\xe8\xae\xb8\xe7\x94\xa8\xe6\x88\xb7\xe6\x9f\xa5\xe7\x9c\x8b\xe6\xb8\xb8\xe6\x88\x8f\xe4\xb8\xad\xe5\x95\x86\xe5\xba\x97\xe4\xb8\xad\xe5\x95\x86\xe5\x93\x81\xe7\x9a\x84\xe6\x9c\x80\xe4\xbd\x8e\xe5\xb9\xb4\xe9\xbe\x84\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe5\xb9\xb4\xe9\xbe\x84\xe5\xb0\x8f\xe4\xba\x8e\xe6\xad\xa4\xe5\x80\xbc\xef\xbc\x8c\xe5\x88\x99\xe4\xba\xa7\xe5\x93\x81\xe4\xb8\x8d\xe5\x8c\x85\xe5\x90\xab\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xbb\xe4\xbd\x93\xe4\xb8\xad\xe3\x80\x82\xe5\x80\xbc\xe4\xb8\xba""0\xe8\xa1\xa8\xe7\xa4\xba\xe8\xaf\xa5\xe9\xa1\xb9\xe6\xb2\xa1\xe6\x9c\x89\xe5\xb9\xb4\xe9\xbe\x84\xe8\xa6\x81\xe6\xb1\x82\xe3\x80\x82\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe5\x85\x81\xe8\xae\xb8\xe7\x94\xa8\xe6\x88\xb7\xe6\x9f\xa5\xe7\x9c\x8b\xe6\xb8\xb8\xe6\x88\x8f\xe4\xb8\xad\xe5\x95\x86\xe5\xba\x97\xe4\xb8\xad\xe5\x95\x86\xe5\x93\x81\xe7\x9a\x84\xe6\x9c\x80\xe4\xbd\x8e\xe5\xb9\xb4\xe9\xbe\x84\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe5\xb9\xb4\xe9\xbe\x84\xe5\xb0\x8f\xe4\xba\x8e\xe6\xad\xa4\xe5\x80\xbc\xef\xbc\x8c\xe5\x88\x99\xe4\xba\xa7\xe5\x93\x81\xe4\xb8\x8d\xe5\x8c\x85\xe5\x90\xab\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xbb\xe4\xbd\x93\xe4\xb8\xad\xe3\x80\x82\xe5\x80\xbc\xe4\xb8\xba""0\xe8\xa1\xa8\xe7\xa4\xba\xe8\xaf\xa5\xe9\xa1\xb9\xe6\xb2\xa1\xe6\x9c\x89\xe5\xb9\xb4\xe9\xbe\x84\xe8\xa6\x81\xe6\xb1\x82\xe3\x80\x82" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayName_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe6\x9c\xac\xe5\x9c\xb0\xe5\x8c\x96\xe6\x98\xbe\xe7\xa4\xba\xe5\x90\x8d\xe7\xa7\xb0\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe6\x9c\xac\xe5\x9c\xb0\xe5\x8c\x96\xe6\x98\xbe\xe7\xa4\xba\xe5\x90\x8d\xe7\xa7\xb0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe7\xb1\xbb\xe5\x9e\x8b, \xe5\x8f\xaf\xe8\x83\xbd\xe7\x9a\x84\xe5\x80\xbc\xef\xbc\x8c""category\xef\xbc\x8cproduct\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe7\xb1\xbb\xe5\x9e\x8b, \xe5\x8f\xaf\xe8\x83\xbd\xe7\x9a\x84\xe5\x80\xbc\xef\xbc\x8c""category\xef\xbc\x8cproduct" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Children_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe5\xad\x90\xe5\xae\xb9\xe5\x99\xa8\xe3\x80\x81\xe4\xba\xa7\xe5\x93\x81\xef\xbc\x88\xe6\x8e\xa5\xe5\x8f\xa3\xe9\xbb\x98\xe8\xae\xa4\xe8\xbf\x94\xe5\x9b\x9e\xe7\x88\xb6\xe5\xae\xb9\xe5\x99\xa8\xef\xbc\x8c\xe5\xae\x9e\xe9\x99\x85\xe7\x9a\x84\xe4\xba\xa7\xe5\x93\x81\xe8\xaf\xb7\xe4\xbb\x8e\xe8\xaf\xa5\xe5\xad\x97\xe6\xae\xb5\xe8\x8e\xb7\xe5\x8f\x96\xef\xbc\x89\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe5\xad\x90\xe5\xae\xb9\xe5\x99\xa8\xe3\x80\x81\xe4\xba\xa7\xe5\x93\x81\xef\xbc\x88\xe6\x8e\xa5\xe5\x8f\xa3\xe9\xbb\x98\xe8\xae\xa4\xe8\xbf\x94\xe5\x9b\x9e\xe7\x88\xb6\xe5\xae\xb9\xe5\x99\xa8\xef\xbc\x8c\xe5\xae\x9e\xe9\x99\x85\xe7\x9a\x84\xe4\xba\xa7\xe5\x93\x81\xe8\xaf\xb7\xe4\xbb\x8e\xe8\xaf\xa5\xe5\xad\x97\xe6\xae\xb5\xe8\x8e\xb7\xe5\x8f\x96\xef\xbc\x89" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Media_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe5\xaa\x92\xe4\xbd\x93\xe4\xbf\xa1\xe6\x81\xaf\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe5\xaa\x92\xe4\xbd\x93\xe4\xbf\xa1\xe6\x81\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalItemCount_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe5\xad\x90\xe5\xae\xb9\xe5\x99\xa8\xe6\x80\xbb\xe6\x95\xb0\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe5\xad\x90\xe5\xae\xb9\xe5\x99\xa8\xe6\x80\xbb\xe6\x95\xb0" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Code;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RawData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Label;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AgeLimit;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DisplayName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Type;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Children_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Children;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Media;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalItemCount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSProductCategory>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Code = { "Code", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductCategory, Code), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Code_MetaData), NewProp_Code_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_RawData = { "RawData", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductCategory, RawData), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RawData_MetaData), NewProp_RawData_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_ID = { "ID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductCategory, ID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ID_MetaData), NewProp_ID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Label = { "Label", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductCategory, Label), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Label_MetaData), NewProp_Label_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_AgeLimit = { "AgeLimit", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductCategory, AgeLimit), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AgeLimit_MetaData), NewProp_AgeLimit_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_DisplayName = { "DisplayName", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductCategory, DisplayName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayName_MetaData), NewProp_DisplayName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductCategory, Type), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Type_MetaData), NewProp_Type_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Children_Inner = { "Children", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FOnePSProduct, METADATA_PARAMS(0, nullptr) }; // 674518079
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Children = { "Children", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductCategory, Children), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Children_MetaData), NewProp_Children_MetaData) }; // 674518079
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Media = { "Media", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductCategory, Media), Z_Construct_UScriptStruct_FOnePSProductMedia, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Media_MetaData), NewProp_Media_MetaData) }; // 2579785438
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_TotalItemCount = { "TotalItemCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePSProductCategory, TotalItemCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalItemCount_MetaData), NewProp_TotalItemCount_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Code,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_RawData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_ID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Label,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_AgeLimit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_DisplayName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Children_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Children,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Media,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_TotalItemCount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OnePSProductCategory",
	Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::PropPointers),
	sizeof(FOnePSProductCategory),
	alignof(FOnePSProductCategory),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductCategory()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePSProductCategory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOnePSProductCategory.InnerSingleton, Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOnePSProductCategory.InnerSingleton;
}
// ********** End ScriptStruct FOnePSProductCategory ***********************************************

// ********** Begin Class UOnePSLocaleEnum Function Arabic *****************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics
{
	struct OnePSLocaleEnum_eventArabic_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventArabic_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Arabic", Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::OnePSLocaleEnum_eventArabic_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::OnePSLocaleEnum_eventArabic_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Arabic()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execArabic)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Arabic();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Arabic *******************************************

// ********** Begin Class UOnePSLocaleEnum Function Chinese_S **************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics
{
	struct OnePSLocaleEnum_eventChinese_S_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventChinese_S_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Chinese_S", Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::OnePSLocaleEnum_eventChinese_S_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::OnePSLocaleEnum_eventChinese_S_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execChinese_S)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Chinese_S();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Chinese_S ****************************************

// ********** Begin Class UOnePSLocaleEnum Function Chinese_T **************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics
{
	struct OnePSLocaleEnum_eventChinese_T_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventChinese_T_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Chinese_T", Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::OnePSLocaleEnum_eventChinese_T_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::OnePSLocaleEnum_eventChinese_T_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execChinese_T)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Chinese_T();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Chinese_T ****************************************

// ********** Begin Class UOnePSLocaleEnum Function Czech ******************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics
{
	struct OnePSLocaleEnum_eventCzech_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventCzech_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Czech", Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::OnePSLocaleEnum_eventCzech_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::OnePSLocaleEnum_eventCzech_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Czech()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execCzech)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Czech();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Czech ********************************************

// ********** Begin Class UOnePSLocaleEnum Function Danish *****************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics
{
	struct OnePSLocaleEnum_eventDanish_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventDanish_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Danish", Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::OnePSLocaleEnum_eventDanish_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::OnePSLocaleEnum_eventDanish_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Danish()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execDanish)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Danish();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Danish *******************************************

// ********** Begin Class UOnePSLocaleEnum Function Dutch ******************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics
{
	struct OnePSLocaleEnum_eventDutch_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventDutch_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Dutch", Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::OnePSLocaleEnum_eventDutch_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::OnePSLocaleEnum_eventDutch_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Dutch()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execDutch)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Dutch();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Dutch ********************************************

// ********** Begin Class UOnePSLocaleEnum Function English_GB *************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics
{
	struct OnePSLocaleEnum_eventEnglish_GB_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventEnglish_GB_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "English_GB", Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::OnePSLocaleEnum_eventEnglish_GB_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::OnePSLocaleEnum_eventEnglish_GB_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_English_GB()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execEnglish_GB)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::English_GB();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function English_GB ***************************************

// ********** Begin Class UOnePSLocaleEnum Function English_US *************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics
{
	struct OnePSLocaleEnum_eventEnglish_US_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventEnglish_US_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "English_US", Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::OnePSLocaleEnum_eventEnglish_US_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::OnePSLocaleEnum_eventEnglish_US_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_English_US()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execEnglish_US)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::English_US();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function English_US ***************************************

// ********** Begin Class UOnePSLocaleEnum Function Finnish ****************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics
{
	struct OnePSLocaleEnum_eventFinnish_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventFinnish_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Finnish", Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::OnePSLocaleEnum_eventFinnish_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::OnePSLocaleEnum_eventFinnish_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Finnish()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execFinnish)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Finnish();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Finnish ******************************************

// ********** Begin Class UOnePSLocaleEnum Function French *****************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics
{
	struct OnePSLocaleEnum_eventFrench_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventFrench_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "French", Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::OnePSLocaleEnum_eventFrench_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::OnePSLocaleEnum_eventFrench_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_French()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execFrench)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::French();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function French *******************************************

// ********** Begin Class UOnePSLocaleEnum Function French_CA **************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics
{
	struct OnePSLocaleEnum_eventFrench_CA_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventFrench_CA_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "French_CA", Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::OnePSLocaleEnum_eventFrench_CA_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::OnePSLocaleEnum_eventFrench_CA_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_French_CA()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execFrench_CA)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::French_CA();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function French_CA ****************************************

// ********** Begin Class UOnePSLocaleEnum Function German *****************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics
{
	struct OnePSLocaleEnum_eventGerman_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventGerman_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "German", Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::OnePSLocaleEnum_eventGerman_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::OnePSLocaleEnum_eventGerman_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_German()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execGerman)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::German();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function German *******************************************

// ********** Begin Class UOnePSLocaleEnum Function Greek ******************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics
{
	struct OnePSLocaleEnum_eventGreek_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventGreek_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Greek", Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::OnePSLocaleEnum_eventGreek_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::OnePSLocaleEnum_eventGreek_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Greek()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execGreek)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Greek();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Greek ********************************************

// ********** Begin Class UOnePSLocaleEnum Function Hungarian **************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics
{
	struct OnePSLocaleEnum_eventHungarian_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventHungarian_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Hungarian", Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::OnePSLocaleEnum_eventHungarian_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::OnePSLocaleEnum_eventHungarian_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execHungarian)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Hungarian();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Hungarian ****************************************

// ********** Begin Class UOnePSLocaleEnum Function Indonesian *************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics
{
	struct OnePSLocaleEnum_eventIndonesian_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventIndonesian_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Indonesian", Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::OnePSLocaleEnum_eventIndonesian_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::OnePSLocaleEnum_eventIndonesian_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execIndonesian)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Indonesian();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Indonesian ***************************************

// ********** Begin Class UOnePSLocaleEnum Function Italian ****************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics
{
	struct OnePSLocaleEnum_eventItalian_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventItalian_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Italian", Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::OnePSLocaleEnum_eventItalian_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::OnePSLocaleEnum_eventItalian_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Italian()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execItalian)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Italian();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Italian ******************************************

// ********** Begin Class UOnePSLocaleEnum Function Japanese ***************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics
{
	struct OnePSLocaleEnum_eventJapanese_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventJapanese_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Japanese", Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::OnePSLocaleEnum_eventJapanese_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::OnePSLocaleEnum_eventJapanese_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Japanese()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execJapanese)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Japanese();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Japanese *****************************************

// ********** Begin Class UOnePSLocaleEnum Function Korean *****************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics
{
	struct OnePSLocaleEnum_eventKorean_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventKorean_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Korean", Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::OnePSLocaleEnum_eventKorean_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::OnePSLocaleEnum_eventKorean_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Korean()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execKorean)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Korean();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Korean *******************************************

// ********** Begin Class UOnePSLocaleEnum Function Norwegian **************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics
{
	struct OnePSLocaleEnum_eventNorwegian_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventNorwegian_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Norwegian", Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::OnePSLocaleEnum_eventNorwegian_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::OnePSLocaleEnum_eventNorwegian_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execNorwegian)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Norwegian();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Norwegian ****************************************

// ********** Begin Class UOnePSLocaleEnum Function Polish *****************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics
{
	struct OnePSLocaleEnum_eventPolish_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventPolish_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Polish", Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::OnePSLocaleEnum_eventPolish_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::OnePSLocaleEnum_eventPolish_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Polish()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execPolish)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Polish();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Polish *******************************************

// ********** Begin Class UOnePSLocaleEnum Function Portuguese_BR **********************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics
{
	struct OnePSLocaleEnum_eventPortuguese_BR_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventPortuguese_BR_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Portuguese_BR", Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::OnePSLocaleEnum_eventPortuguese_BR_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::OnePSLocaleEnum_eventPortuguese_BR_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execPortuguese_BR)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Portuguese_BR();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Portuguese_BR ************************************

// ********** Begin Class UOnePSLocaleEnum Function Portuguese_PT **********************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics
{
	struct OnePSLocaleEnum_eventPortuguese_PT_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventPortuguese_PT_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Portuguese_PT", Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::OnePSLocaleEnum_eventPortuguese_PT_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::OnePSLocaleEnum_eventPortuguese_PT_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execPortuguese_PT)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Portuguese_PT();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Portuguese_PT ************************************

// ********** Begin Class UOnePSLocaleEnum Function Romanian ***************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics
{
	struct OnePSLocaleEnum_eventRomanian_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventRomanian_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Romanian", Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::OnePSLocaleEnum_eventRomanian_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::OnePSLocaleEnum_eventRomanian_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Romanian()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execRomanian)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Romanian();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Romanian *****************************************

// ********** Begin Class UOnePSLocaleEnum Function Russian ****************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics
{
	struct OnePSLocaleEnum_eventRussian_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventRussian_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Russian", Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::OnePSLocaleEnum_eventRussian_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::OnePSLocaleEnum_eventRussian_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Russian()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execRussian)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Russian();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Russian ******************************************

// ********** Begin Class UOnePSLocaleEnum Function Spanish ****************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics
{
	struct OnePSLocaleEnum_eventSpanish_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventSpanish_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Spanish", Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::OnePSLocaleEnum_eventSpanish_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::OnePSLocaleEnum_eventSpanish_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Spanish()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execSpanish)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Spanish();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Spanish ******************************************

// ********** Begin Class UOnePSLocaleEnum Function Spanish_LA *************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics
{
	struct OnePSLocaleEnum_eventSpanish_LA_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventSpanish_LA_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Spanish_LA", Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::OnePSLocaleEnum_eventSpanish_LA_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::OnePSLocaleEnum_eventSpanish_LA_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execSpanish_LA)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Spanish_LA();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Spanish_LA ***************************************

// ********** Begin Class UOnePSLocaleEnum Function Swedish ****************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics
{
	struct OnePSLocaleEnum_eventSwedish_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventSwedish_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Swedish", Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::OnePSLocaleEnum_eventSwedish_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::OnePSLocaleEnum_eventSwedish_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Swedish()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execSwedish)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Swedish();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Swedish ******************************************

// ********** Begin Class UOnePSLocaleEnum Function Thai *******************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics
{
	struct OnePSLocaleEnum_eventThai_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventThai_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Thai", Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::OnePSLocaleEnum_eventThai_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::OnePSLocaleEnum_eventThai_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Thai()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execThai)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Thai();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Thai *********************************************

// ********** Begin Class UOnePSLocaleEnum Function Turkish ****************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics
{
	struct OnePSLocaleEnum_eventTurkish_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventTurkish_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Turkish", Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::OnePSLocaleEnum_eventTurkish_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::OnePSLocaleEnum_eventTurkish_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Turkish()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execTurkish)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Turkish();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Turkish ******************************************

// ********** Begin Class UOnePSLocaleEnum Function Vietnamese *************************************
struct Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics
{
	struct OnePSLocaleEnum_eventVietnamese_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventVietnamese_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Vietnamese", Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::OnePSLocaleEnum_eventVietnamese_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::OnePSLocaleEnum_eventVietnamese_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOnePSLocaleEnum::execVietnamese)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UOnePSLocaleEnum::Vietnamese();
	P_NATIVE_END;
}
// ********** End Class UOnePSLocaleEnum Function Vietnamese ***************************************

// ********** Begin Class UOnePSLocaleEnum *********************************************************
void UOnePSLocaleEnum::StaticRegisterNativesUOnePSLocaleEnum()
{
	UClass* Class = UOnePSLocaleEnum::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "Arabic", &UOnePSLocaleEnum::execArabic },
		{ "Chinese_S", &UOnePSLocaleEnum::execChinese_S },
		{ "Chinese_T", &UOnePSLocaleEnum::execChinese_T },
		{ "Czech", &UOnePSLocaleEnum::execCzech },
		{ "Danish", &UOnePSLocaleEnum::execDanish },
		{ "Dutch", &UOnePSLocaleEnum::execDutch },
		{ "English_GB", &UOnePSLocaleEnum::execEnglish_GB },
		{ "English_US", &UOnePSLocaleEnum::execEnglish_US },
		{ "Finnish", &UOnePSLocaleEnum::execFinnish },
		{ "French", &UOnePSLocaleEnum::execFrench },
		{ "French_CA", &UOnePSLocaleEnum::execFrench_CA },
		{ "German", &UOnePSLocaleEnum::execGerman },
		{ "Greek", &UOnePSLocaleEnum::execGreek },
		{ "Hungarian", &UOnePSLocaleEnum::execHungarian },
		{ "Indonesian", &UOnePSLocaleEnum::execIndonesian },
		{ "Italian", &UOnePSLocaleEnum::execItalian },
		{ "Japanese", &UOnePSLocaleEnum::execJapanese },
		{ "Korean", &UOnePSLocaleEnum::execKorean },
		{ "Norwegian", &UOnePSLocaleEnum::execNorwegian },
		{ "Polish", &UOnePSLocaleEnum::execPolish },
		{ "Portuguese_BR", &UOnePSLocaleEnum::execPortuguese_BR },
		{ "Portuguese_PT", &UOnePSLocaleEnum::execPortuguese_PT },
		{ "Romanian", &UOnePSLocaleEnum::execRomanian },
		{ "Russian", &UOnePSLocaleEnum::execRussian },
		{ "Spanish", &UOnePSLocaleEnum::execSpanish },
		{ "Spanish_LA", &UOnePSLocaleEnum::execSpanish_LA },
		{ "Swedish", &UOnePSLocaleEnum::execSwedish },
		{ "Thai", &UOnePSLocaleEnum::execThai },
		{ "Turkish", &UOnePSLocaleEnum::execTurkish },
		{ "Vietnamese", &UOnePSLocaleEnum::execVietnamese },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UOnePSLocaleEnum;
UClass* UOnePSLocaleEnum::GetPrivateStaticClass()
{
	using TClass = UOnePSLocaleEnum;
	if (!Z_Registration_Info_UClass_UOnePSLocaleEnum.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("OnePSLocaleEnum"),
			Z_Registration_Info_UClass_UOnePSLocaleEnum.InnerSingleton,
			StaticRegisterNativesUOnePSLocaleEnum,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UOnePSLocaleEnum.InnerSingleton;
}
UClass* Z_Construct_UClass_UOnePSLocaleEnum_NoRegister()
{
	return UOnePSLocaleEnum::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UOnePSLocaleEnum_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/// \xe4\xb8\xbb\xe8\xa6\x81\xe7\x94\xa8\xe4\xba\x8e \xe8\x84\x8f\xe8\xaf\x9d\xe8\xbf\x87\xe6\xbb\xa4\xe5\x99\xa8\n" },
#endif
		{ "IncludePath", "OneEngineSDKPSSubsystem.h" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe4\xb8\xbb\xe8\xa6\x81\xe7\x94\xa8\xe4\xba\x8e \xe8\x84\x8f\xe8\xaf\x9d\xe8\xbf\x87\xe6\xbb\xa4\xe5\x99\xa8" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Arabic, "Arabic" }, // 3564674335
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S, "Chinese_S" }, // 138404652
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T, "Chinese_T" }, // 3854447715
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Czech, "Czech" }, // 2650500725
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Danish, "Danish" }, // 46882663
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Dutch, "Dutch" }, // 2772842677
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_English_GB, "English_GB" }, // 3933636449
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_English_US, "English_US" }, // 1673179985
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Finnish, "Finnish" }, // 2135353595
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_French, "French" }, // 2506333778
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_French_CA, "French_CA" }, // 1021470039
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_German, "German" }, // 3799704768
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Greek, "Greek" }, // 3484236299
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian, "Hungarian" }, // 2624612592
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian, "Indonesian" }, // 2835707830
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Italian, "Italian" }, // 3195815819
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Japanese, "Japanese" }, // 2739851088
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Korean, "Korean" }, // 3615012595
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian, "Norwegian" }, // 3240963067
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Polish, "Polish" }, // 1463567134
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR, "Portuguese_BR" }, // 1469610249
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT, "Portuguese_PT" }, // 2774446733
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Romanian, "Romanian" }, // 3619611709
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Russian, "Russian" }, // 252840967
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Spanish, "Spanish" }, // 2577252205
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA, "Spanish_LA" }, // 2152912956
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Swedish, "Swedish" }, // 269401432
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Thai, "Thai" }, // 215767259
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Turkish, "Turkish" }, // 2559287059
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese, "Vietnamese" }, // 317099869
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UOnePSLocaleEnum>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UOnePSLocaleEnum_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UBlueprintFunctionLibrary,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UOnePSLocaleEnum_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UOnePSLocaleEnum_Statics::ClassParams = {
	&UOnePSLocaleEnum::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x000000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UOnePSLocaleEnum_Statics::Class_MetaDataParams), Z_Construct_UClass_UOnePSLocaleEnum_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UOnePSLocaleEnum()
{
	if (!Z_Registration_Info_UClass_UOnePSLocaleEnum.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UOnePSLocaleEnum.OuterSingleton, Z_Construct_UClass_UOnePSLocaleEnum_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UOnePSLocaleEnum.OuterSingleton;
}
UOnePSLocaleEnum::UOnePSLocaleEnum(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UOnePSLocaleEnum);
UOnePSLocaleEnum::~UOnePSLocaleEnum() {}
// ********** End Class UOnePSLocaleEnum ***********************************************************

// ********** Begin Delegate FOnGetFriendsResultDelegate *******************************************
struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics
{
	struct OneEngineSDKPSSubsystem_eventOnGetFriendsResultDelegate_Parms
	{
		FOnePSUserProfileResponse ProfileList;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProfileList_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProfileList;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::NewProp_ProfileList = { "ProfileList", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnGetFriendsResultDelegate_Parms, ProfileList), Z_Construct_UScriptStruct_FOnePSUserProfileResponse, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProfileList_MetaData), NewProp_ProfileList_MetaData) }; // 2081087001
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::NewProp_ProfileList,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OnGetFriendsResultDelegate__DelegateSignature", Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventOnGetFriendsResultDelegate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00520000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventOnGetFriendsResultDelegate_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UOneEngineSDKPSSubsystem::FOnGetFriendsResultDelegate_DelegateWrapper(const FScriptDelegate& OnGetFriendsResultDelegate, FOnePSUserProfileResponse const& ProfileList)
{
	struct OneEngineSDKPSSubsystem_eventOnGetFriendsResultDelegate_Parms
	{
		FOnePSUserProfileResponse ProfileList;
	};
	OneEngineSDKPSSubsystem_eventOnGetFriendsResultDelegate_Parms Parms;
	Parms.ProfileList=ProfileList;
	OnGetFriendsResultDelegate.ProcessDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnGetFriendsResultDelegate *********************************************

// ********** Begin Delegate FOnGetBlockingUsersResultDelegate *************************************
struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics
{
	struct OneEngineSDKPSSubsystem_eventOnGetBlockingUsersResultDelegate_Parms
	{
		FOnePSUserProfileResponse ProfileList;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProfileList_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProfileList;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::NewProp_ProfileList = { "ProfileList", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnGetBlockingUsersResultDelegate_Parms, ProfileList), Z_Construct_UScriptStruct_FOnePSUserProfileResponse, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProfileList_MetaData), NewProp_ProfileList_MetaData) }; // 2081087001
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::NewProp_ProfileList,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OnGetBlockingUsersResultDelegate__DelegateSignature", Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventOnGetBlockingUsersResultDelegate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00520000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventOnGetBlockingUsersResultDelegate_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UOneEngineSDKPSSubsystem::FOnGetBlockingUsersResultDelegate_DelegateWrapper(const FScriptDelegate& OnGetBlockingUsersResultDelegate, FOnePSUserProfileResponse const& ProfileList)
{
	struct OneEngineSDKPSSubsystem_eventOnGetBlockingUsersResultDelegate_Parms
	{
		FOnePSUserProfileResponse ProfileList;
	};
	OneEngineSDKPSSubsystem_eventOnGetBlockingUsersResultDelegate_Parms Parms;
	Parms.ProfileList=ProfileList;
	OnGetBlockingUsersResultDelegate.ProcessDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnGetBlockingUsersResultDelegate ***************************************

// ********** Begin Delegate FOnRestrictionStatusResultDelegate ************************************
struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics
{
	struct OneEngineSDKPSSubsystem_eventOnRestrictionStatusResultDelegate_Parms
	{
		int32 Result;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Result;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnRestrictionStatusResultDelegate_Parms, Result), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::NewProp_Result,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OnRestrictionStatusResultDelegate__DelegateSignature", Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventOnRestrictionStatusResultDelegate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00120000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventOnRestrictionStatusResultDelegate_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UOneEngineSDKPSSubsystem::FOnRestrictionStatusResultDelegate_DelegateWrapper(const FScriptDelegate& OnRestrictionStatusResultDelegate, int32 Result)
{
	struct OneEngineSDKPSSubsystem_eventOnRestrictionStatusResultDelegate_Parms
	{
		int32 Result;
	};
	OneEngineSDKPSSubsystem_eventOnRestrictionStatusResultDelegate_Parms Parms;
	Parms.Result=Result;
	OnRestrictionStatusResultDelegate.ProcessDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnRestrictionStatusResultDelegate **************************************

// ********** Begin Delegate FOnCheckPremiumResultDelegate *****************************************
struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics
{
	struct OneEngineSDKPSSubsystem_eventOnCheckPremiumResultDelegate_Parms
	{
		int32 Code;
		bool bIsPremium;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Code;
	static void NewProp_bIsPremium_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPremium;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::NewProp_Code = { "Code", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnCheckPremiumResultDelegate_Parms, Code), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::NewProp_bIsPremium_SetBit(void* Obj)
{
	((OneEngineSDKPSSubsystem_eventOnCheckPremiumResultDelegate_Parms*)Obj)->bIsPremium = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::NewProp_bIsPremium = { "bIsPremium", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(OneEngineSDKPSSubsystem_eventOnCheckPremiumResultDelegate_Parms), &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::NewProp_bIsPremium_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::NewProp_Code,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::NewProp_bIsPremium,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OnCheckPremiumResultDelegate__DelegateSignature", Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventOnCheckPremiumResultDelegate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00120000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventOnCheckPremiumResultDelegate_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UOneEngineSDKPSSubsystem::FOnCheckPremiumResultDelegate_DelegateWrapper(const FScriptDelegate& OnCheckPremiumResultDelegate, int32 Code, bool bIsPremium)
{
	struct OneEngineSDKPSSubsystem_eventOnCheckPremiumResultDelegate_Parms
	{
		int32 Code;
		bool bIsPremium;
	};
	OneEngineSDKPSSubsystem_eventOnCheckPremiumResultDelegate_Parms Parms;
	Parms.Code=Code;
	Parms.bIsPremium=bIsPremium ? true : false;
	OnCheckPremiumResultDelegate.ProcessDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCheckPremiumResultDelegate *******************************************

// ********** Begin Delegate FOnFilterProfanityResultDelegate **************************************
struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics
{
	struct OneEngineSDKPSSubsystem_eventOnFilterProfanityResultDelegate_Parms
	{
		int32 Code;
		FString Text;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Code;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Text;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::NewProp_Code = { "Code", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnFilterProfanityResultDelegate_Parms, Code), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::NewProp_Text = { "Text", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnFilterProfanityResultDelegate_Parms, Text), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::NewProp_Code,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::NewProp_Text,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OnFilterProfanityResultDelegate__DelegateSignature", Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventOnFilterProfanityResultDelegate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00120000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventOnFilterProfanityResultDelegate_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UOneEngineSDKPSSubsystem::FOnFilterProfanityResultDelegate_DelegateWrapper(const FScriptDelegate& OnFilterProfanityResultDelegate, int32 Code, const FString& Text)
{
	struct OneEngineSDKPSSubsystem_eventOnFilterProfanityResultDelegate_Parms
	{
		int32 Code;
		FString Text;
	};
	OneEngineSDKPSSubsystem_eventOnFilterProfanityResultDelegate_Parms Parms;
	Parms.Code=Code;
	Parms.Text=Text;
	OnFilterProfanityResultDelegate.ProcessDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFilterProfanityResultDelegate ****************************************

// ********** Begin Delegate FOnOpenDialogResultDelegate *******************************************
struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics
{
	struct OneEngineSDKPSSubsystem_eventOnOpenDialogResultDelegate_Parms
	{
		int32 Result;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Result;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnOpenDialogResultDelegate_Parms, Result), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::NewProp_Result,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OnOpenDialogResultDelegate__DelegateSignature", Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventOnOpenDialogResultDelegate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00120000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventOnOpenDialogResultDelegate_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UOneEngineSDKPSSubsystem::FOnOpenDialogResultDelegate_DelegateWrapper(const FScriptDelegate& OnOpenDialogResultDelegate, int32 Result)
{
	struct OneEngineSDKPSSubsystem_eventOnOpenDialogResultDelegate_Parms
	{
		int32 Result;
	};
	OneEngineSDKPSSubsystem_eventOnOpenDialogResultDelegate_Parms Parms;
	Parms.Result=Result;
	OnOpenDialogResultDelegate.ProcessDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnOpenDialogResultDelegate *********************************************

// ********** Begin Delegate FOnGetProductInfoListPSDelegate ***************************************
struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics
{
	struct OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms
	{
		bool bSucceed;
		FOnePSProductCategory Category;
		int32 Code;
		FString Msg;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Msg_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static void NewProp_bSucceed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSucceed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Category;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Code;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Msg;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_bSucceed_SetBit(void* Obj)
{
	((OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms*)Obj)->bSucceed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_bSucceed = { "bSucceed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms), &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_bSucceed_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms, Category), Z_Construct_UScriptStruct_FOnePSProductCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) }; // 1330006013
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Code = { "Code", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms, Code), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Msg = { "Msg", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms, Msg), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Msg_MetaData), NewProp_Msg_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_bSucceed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Code,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Msg,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OnGetProductInfoListPSDelegate__DelegateSignature", Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00520000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UOneEngineSDKPSSubsystem::FOnGetProductInfoListPSDelegate_DelegateWrapper(const FScriptDelegate& OnGetProductInfoListPSDelegate, bool bSucceed, FOnePSProductCategory const& Category, int32 Code, const FString& Msg)
{
	struct OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms
	{
		bool bSucceed;
		FOnePSProductCategory Category;
		int32 Code;
		FString Msg;
	};
	OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms Parms;
	Parms.bSucceed=bSucceed ? true : false;
	Parms.Category=Category;
	Parms.Code=Code;
	Parms.Msg=Msg;
	OnGetProductInfoListPSDelegate.ProcessDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnGetProductInfoListPSDelegate *****************************************

// ********** Begin Delegate FWidgetVisibilityDelegate *********************************************
struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics
{
	struct OneEngineSDKPSSubsystem_eventWidgetVisibilityDelegate_Parms
	{
		int32 WidgetType;
		bool bVisible;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_WidgetType;
	static void NewProp_bVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::NewProp_WidgetType = { "WidgetType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventWidgetVisibilityDelegate_Parms, WidgetType), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::NewProp_bVisible_SetBit(void* Obj)
{
	((OneEngineSDKPSSubsystem_eventWidgetVisibilityDelegate_Parms*)Obj)->bVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::NewProp_bVisible = { "bVisible", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(OneEngineSDKPSSubsystem_eventWidgetVisibilityDelegate_Parms), &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::NewProp_bVisible_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::NewProp_WidgetType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::NewProp_bVisible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "WidgetVisibilityDelegate__DelegateSignature", Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventWidgetVisibilityDelegate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::OneEngineSDKPSSubsystem_eventWidgetVisibilityDelegate_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UOneEngineSDKPSSubsystem::FWidgetVisibilityDelegate_DelegateWrapper(const FMulticastScriptDelegate& WidgetVisibilityDelegate, int32 WidgetType, bool bVisible)
{
	struct OneEngineSDKPSSubsystem_eventWidgetVisibilityDelegate_Parms
	{
		int32 WidgetType;
		bool bVisible;
	};
	OneEngineSDKPSSubsystem_eventWidgetVisibilityDelegate_Parms Parms;
	Parms.WidgetType=WidgetType;
	Parms.bVisible=bVisible ? true : false;
	WidgetVisibilityDelegate.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FWidgetVisibilityDelegate ***********************************************

// ********** Begin Class UOneEngineSDKPSSubsystem Function CheckPremium ***************************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics
{
	struct OneEngineSDKPSSubsystem_eventCheckPremium_Parms
	{
		FScriptDelegate OnCheckPremiumResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe6\xa3\x80\xe6\x9f\xa5\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba\xe4\xbc\x9a\xe5\x91\x98\n\x09 * @param OnCheckPremiumResult \xe6\xa3\x80\xe6\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe7\xbb\x93\xe6\x9e\x9c\xe5\x9b\x9e\xe8\xb0\x83\xef\xbc\x8c""code \xe9\x9d\x9e""0\xe8\xa1\xa8\xe7\xa4\xba\xe6\xa3\x80\xe6\x9f\xa5\xe5\xbc\x82\xe5\xb8\xb8\xef\xbc\x88\xe6\x9c\x89\xe5\x8f\xaf\xe8\x83\xbd\xe6\x98\xaf\xe7\xbd\x91\xe7\xbb\x9c\xe5\x8e\x9f\xe5\x9b\xa0\xe5\xaf\xbc\xe8\x87\xb4\xef\xbc\x8c\xe9\x9c\x80\xe8\xa6\x81\xe8\xbf\x9b\xe8\xa1\x8c\xe9\x87\x8d\xe8\xaf\x95\xef\xbc\x89\xef\xbc\x8c""bIsPremium \xe4\xb8\xba true \xe8\xa1\xa8\xe7\xa4\xba\xe6\x98\xaf\xe4\xbc\x9a\xe5\x91\x98\xef\xbc\x8c""false \xe8\xa1\xa8\xe7\xa4\xba\xe4\xb8\x8d\xe6\x98\xaf\xe4\xbc\x9a\xe5\x91\x98\n\x09 * @warning \xe9\x9c\x80\xe8\xa6\x81\xe5\x9c\xa8\xe8\xbf\x9b\xe5\x85\xa5Premium\xe5\x8a\x9f\xe8\x83\xbd\xe4\xb9\x8b\xe5\x89\x8d\xe8\xb0\x83\xe7\x94\xa8\xe3\x80\x82\xe7\xbd\x91\xe7\xbb\x9c\xe5\xae\x9e\xe6\x97\xb6\xe5\xaf\xb9\xe6\x88\x98\xe5\xb0\xb1\xe6\x98\xafPremium\xe5\x8a\x9f\xe8\x83\xbd\xe3\x80\x82\xe5\x8f\xaf\xe6\x97\xa9\xe4\xb8\x8d\xe5\x8f\xaf\xe6\x99\x9a\xe3\x80\x82\xe5\x9b\xa0\xe6\xad\xa4\xef\xbc\x8c\xe5\xaf\xb9\xe4\xba\x8e\xe7\xbd\x91\xe7\xbb\x9c\xe6\xb8\xb8\xe6\x88\x8f\xef\xbc\x8c\xe5\x8f\xaf\xe4\xbb\xa5\xe5\x9c\xa8\xe5\x90\xaf\xe5\x8a\xa8\xe7\x9a\x84\xe6\x97\xb6\xe5\x80\x99\xe5\xb0\xb1\xe8\xb0\x83\xe7\x94\xa8.\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe6\xa3\x80\xe6\x9f\xa5\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba\xe4\xbc\x9a\xe5\x91\x98\n@param OnCheckPremiumResult \xe6\xa3\x80\xe6\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe7\xbb\x93\xe6\x9e\x9c\xe5\x9b\x9e\xe8\xb0\x83\xef\xbc\x8c""code \xe9\x9d\x9e""0\xe8\xa1\xa8\xe7\xa4\xba\xe6\xa3\x80\xe6\x9f\xa5\xe5\xbc\x82\xe5\xb8\xb8\xef\xbc\x88\xe6\x9c\x89\xe5\x8f\xaf\xe8\x83\xbd\xe6\x98\xaf\xe7\xbd\x91\xe7\xbb\x9c\xe5\x8e\x9f\xe5\x9b\xa0\xe5\xaf\xbc\xe8\x87\xb4\xef\xbc\x8c\xe9\x9c\x80\xe8\xa6\x81\xe8\xbf\x9b\xe8\xa1\x8c\xe9\x87\x8d\xe8\xaf\x95\xef\xbc\x89\xef\xbc\x8c""bIsPremium \xe4\xb8\xba true \xe8\xa1\xa8\xe7\xa4\xba\xe6\x98\xaf\xe4\xbc\x9a\xe5\x91\x98\xef\xbc\x8c""false \xe8\xa1\xa8\xe7\xa4\xba\xe4\xb8\x8d\xe6\x98\xaf\xe4\xbc\x9a\xe5\x91\x98\n@warning \xe9\x9c\x80\xe8\xa6\x81\xe5\x9c\xa8\xe8\xbf\x9b\xe5\x85\xa5Premium\xe5\x8a\x9f\xe8\x83\xbd\xe4\xb9\x8b\xe5\x89\x8d\xe8\xb0\x83\xe7\x94\xa8\xe3\x80\x82\xe7\xbd\x91\xe7\xbb\x9c\xe5\xae\x9e\xe6\x97\xb6\xe5\xaf\xb9\xe6\x88\x98\xe5\xb0\xb1\xe6\x98\xafPremium\xe5\x8a\x9f\xe8\x83\xbd\xe3\x80\x82\xe5\x8f\xaf\xe6\x97\xa9\xe4\xb8\x8d\xe5\x8f\xaf\xe6\x99\x9a\xe3\x80\x82\xe5\x9b\xa0\xe6\xad\xa4\xef\xbc\x8c\xe5\xaf\xb9\xe4\xba\x8e\xe7\xbd\x91\xe7\xbb\x9c\xe6\xb8\xb8\xe6\x88\x8f\xef\xbc\x8c\xe5\x8f\xaf\xe4\xbb\xa5\xe5\x9c\xa8\xe5\x90\xaf\xe5\x8a\xa8\xe7\x9a\x84\xe6\x97\xb6\xe5\x80\x99\xe5\xb0\xb1\xe8\xb0\x83\xe7\x94\xa8." },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FDelegatePropertyParams NewProp_OnCheckPremiumResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FDelegatePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::NewProp_OnCheckPremiumResult = { "OnCheckPremiumResult", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventCheckPremium_Parms, OnCheckPremiumResult), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature, METADATA_PARAMS(0, nullptr) }; // 2687271056
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::NewProp_OnCheckPremiumResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "CheckPremium", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::OneEngineSDKPSSubsystem_eventCheckPremium_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::OneEngineSDKPSSubsystem_eventCheckPremium_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execCheckPremium)
{
	P_GET_PROPERTY(FDelegateProperty,Z_Param_OnCheckPremiumResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CheckPremium(FOnCheckPremiumResultDelegate(Z_Param_OnCheckPremiumResult));
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function CheckPremium *****************************

// ********** Begin Class UOneEngineSDKPSSubsystem Function FilterProfanity ************************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics
{
	struct OneEngineSDKPSSubsystem_eventFilterProfanity_Parms
	{
		FString Text;
		FString Language;
		FScriptDelegate OnFilterProfanityResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe8\x84\x8f\xe8\xaf\x9d\xe8\xbf\x87\xe6\xbb\xa4\xe5\x99\xa8\xef\xbc\x8c\xe5\xae\xa1\xe6\x9f\xa5\xe7\x94\xa8\xe6\x88\xb7\xe5\x86\x85\xe5\xae\xb9\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xb8\xad\xe7\x9a\x84\xe4\xba\xb5\xe6\xb8\x8e\xe8\xa1\x8c\xe4\xb8\xba\xe3\x80\x82\xe8\xaf\xa5\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xbc\x9a\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xad\xe8\xbf\x94\xe5\x9b\x9e\xef\xbc\x8c\xe5\xb9\xb6\xe4\xb8\x94\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe7\x9a\x84\xe4\xbb\xbb\xe4\xbd\x95\xe4\xb8\x8d\xe9\x9b\x85\xe5\x86\x85\xe5\xae\xb9\xe9\x83\xbd\xe5\xb0\x86\xe6\x9b\xbf\xe6\x8d\xa2\xe4\xb8\xba\xe2\x80\x9c*\xe2\x80\x9d\xe5\xad\x97\xe7\xac\xa6\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\x9c\xa8\xe6\x96\x87\xe6\x9c\xac\xe4\xb8\xad\xe6\x9c\xaa\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe8\x84\x8f\xe8\xaf\x9d\xef\xbc\x8c\xe5\x88\x99\xe5\x8e\x9f\xe5\xb0\x81\xe4\xb8\x8d\xe5\x8a\xa8\xe5\x9c\xb0\xe8\xbf\x94\xe5\x9b\x9e\xe3\x80\x82\n\x09 * @param Text \xe8\xa6\x81\xe8\xbf\x87\xe6\xbb\xa4\xe7\x9a\x84\xe6\x96\x87\xe6\x9c\xac\xe3\x80\x82\xe6\xad\xa4\xe6\x96\x87\xe6\x9c\xac\xe7\x9a\x84\xe6\x9c\x80\xe5\xa4\xa7\xe5\xa4\xa7\xe5\xb0\x8f\xe4\xb8\xba 4KB\xe3\x80\x82\n\x09 * @param Language \xe4\xbe\x8b\xe5\xa6\x82\xef\xbc\x8c\xe2\x80\x9c""en-GB\xe2\x80\x9d,\xe5\x85\xb7\xe4\xbd\x93\xe6\x94\xaf\xe6\x8c\x81\xe7\x9a\x84locale\xe5\x80\xbc \xe8\xaf\xb7\xe5\x8f\x82\xe8\x80\x83 @see UOnePSLocaleEnum\n\x09 * @param OnFilterProfanityResult \xe5\x9b\x9e\xe8\xb0\x83\n\x09 * @note \xe4\xbd\x9c\xe4\xb8\xba\xe6\x9c\x80\xe4\xbd\xb3\xe5\xae\x9e\xe8\xb7\xb5\xef\xbc\x8c\xe5\xbf\xbd\xe7\x95\xa5\xe8\xb0\x83\xe7\x94\xa8\xe6\xad\xa4 API \xe6\x97\xb6\xe9\x81\x87\xe5\x88\xb0\xe7\x9a\x84\xe9\x94\x99\xe8\xaf\xaf\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe5\x9c\xa8\xe8\xb0\x83\xe7\x94\xa8\xe6\xad\xa4 API \xe6\x97\xb6\xe5\x8f\x91\xe7\x94\x9f\xe9\x94\x99\xe8\xaf\xaf\xef\xbc\x8c\xe5\xba\x94\xe7\x94\xa8\xe7\xa8\x8b\xe5\xba\x8f\xe5\xba\x94\xe7\xbb\xa7\xe7\xbb\xad\xe5\x89\x8d\xe8\xbf\x9b\xef\xbc\x8c\xe5\xb0\xb1\xe5\xa5\xbd\xe5\x83\x8f\xe6\x9c\xaa\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe4\xba\xb5\xe6\xb8\x8e\xe8\xa1\x8c\xe4\xb8\xba\xe4\xb8\x80\xe6\xa0\xb7\xe3\x80\x82\n\x09 * @warning \xe8\xb0\x83\xe7\x94\xa8\xe9\xa2\x91\xe7\x8e\x87\xe9\x99\x90\xe5\x88\xb6 300/minute \xef\xbc\x8c\xe8\xb6\x85\xe8\xbf\x87\xe9\x99\x90\xe5\x88\xb6\xef\xbc\x8cSIE\xe5\xb0\x86\xe9\x99\x90\xe5\x88\xb6\xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe8\x84\x8f\xe8\xaf\x9d\xe8\xbf\x87\xe6\xbb\xa4\xe5\x99\xa8\xef\xbc\x8c\xe5\xae\xa1\xe6\x9f\xa5\xe7\x94\xa8\xe6\x88\xb7\xe5\x86\x85\xe5\xae\xb9\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xb8\xad\xe7\x9a\x84\xe4\xba\xb5\xe6\xb8\x8e\xe8\xa1\x8c\xe4\xb8\xba\xe3\x80\x82\xe8\xaf\xa5\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xbc\x9a\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xad\xe8\xbf\x94\xe5\x9b\x9e\xef\xbc\x8c\xe5\xb9\xb6\xe4\xb8\x94\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe7\x9a\x84\xe4\xbb\xbb\xe4\xbd\x95\xe4\xb8\x8d\xe9\x9b\x85\xe5\x86\x85\xe5\xae\xb9\xe9\x83\xbd\xe5\xb0\x86\xe6\x9b\xbf\xe6\x8d\xa2\xe4\xb8\xba\xe2\x80\x9c*\xe2\x80\x9d\xe5\xad\x97\xe7\xac\xa6\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\x9c\xa8\xe6\x96\x87\xe6\x9c\xac\xe4\xb8\xad\xe6\x9c\xaa\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe8\x84\x8f\xe8\xaf\x9d\xef\xbc\x8c\xe5\x88\x99\xe5\x8e\x9f\xe5\xb0\x81\xe4\xb8\x8d\xe5\x8a\xa8\xe5\x9c\xb0\xe8\xbf\x94\xe5\x9b\x9e\xe3\x80\x82\n@param Text \xe8\xa6\x81\xe8\xbf\x87\xe6\xbb\xa4\xe7\x9a\x84\xe6\x96\x87\xe6\x9c\xac\xe3\x80\x82\xe6\xad\xa4\xe6\x96\x87\xe6\x9c\xac\xe7\x9a\x84\xe6\x9c\x80\xe5\xa4\xa7\xe5\xa4\xa7\xe5\xb0\x8f\xe4\xb8\xba 4KB\xe3\x80\x82\n@param Language \xe4\xbe\x8b\xe5\xa6\x82\xef\xbc\x8c\xe2\x80\x9c""en-GB\xe2\x80\x9d,\xe5\x85\xb7\xe4\xbd\x93\xe6\x94\xaf\xe6\x8c\x81\xe7\x9a\x84locale\xe5\x80\xbc \xe8\xaf\xb7\xe5\x8f\x82\xe8\x80\x83 @see UOnePSLocaleEnum\n@param OnFilterProfanityResult \xe5\x9b\x9e\xe8\xb0\x83\n@note \xe4\xbd\x9c\xe4\xb8\xba\xe6\x9c\x80\xe4\xbd\xb3\xe5\xae\x9e\xe8\xb7\xb5\xef\xbc\x8c\xe5\xbf\xbd\xe7\x95\xa5\xe8\xb0\x83\xe7\x94\xa8\xe6\xad\xa4 API \xe6\x97\xb6\xe9\x81\x87\xe5\x88\xb0\xe7\x9a\x84\xe9\x94\x99\xe8\xaf\xaf\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe5\x9c\xa8\xe8\xb0\x83\xe7\x94\xa8\xe6\xad\xa4 API \xe6\x97\xb6\xe5\x8f\x91\xe7\x94\x9f\xe9\x94\x99\xe8\xaf\xaf\xef\xbc\x8c\xe5\xba\x94\xe7\x94\xa8\xe7\xa8\x8b\xe5\xba\x8f\xe5\xba\x94\xe7\xbb\xa7\xe7\xbb\xad\xe5\x89\x8d\xe8\xbf\x9b\xef\xbc\x8c\xe5\xb0\xb1\xe5\xa5\xbd\xe5\x83\x8f\xe6\x9c\xaa\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe4\xba\xb5\xe6\xb8\x8e\xe8\xa1\x8c\xe4\xb8\xba\xe4\xb8\x80\xe6\xa0\xb7\xe3\x80\x82\n@warning \xe8\xb0\x83\xe7\x94\xa8\xe9\xa2\x91\xe7\x8e\x87\xe9\x99\x90\xe5\x88\xb6 300/minute \xef\xbc\x8c\xe8\xb6\x85\xe8\xbf\x87\xe9\x99\x90\xe5\x88\xb6\xef\xbc\x8cSIE\xe5\xb0\x86\xe9\x99\x90\xe5\x88\xb6\xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Text_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Language_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Text;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Language;
	static const UECodeGen_Private::FDelegatePropertyParams NewProp_OnFilterProfanityResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_Text = { "Text", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventFilterProfanity_Parms, Text), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Text_MetaData), NewProp_Text_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_Language = { "Language", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventFilterProfanity_Parms, Language), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Language_MetaData), NewProp_Language_MetaData) };
const UECodeGen_Private::FDelegatePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_OnFilterProfanityResult = { "OnFilterProfanityResult", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventFilterProfanity_Parms, OnFilterProfanityResult), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature, METADATA_PARAMS(0, nullptr) }; // 4042865203
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_Text,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_Language,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_OnFilterProfanityResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "FilterProfanity", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::OneEngineSDKPSSubsystem_eventFilterProfanity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::OneEngineSDKPSSubsystem_eventFilterProfanity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execFilterProfanity)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Text);
	P_GET_PROPERTY(FStrProperty,Z_Param_Language);
	P_GET_PROPERTY(FDelegateProperty,Z_Param_OnFilterProfanityResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->FilterProfanity(Z_Param_Text,Z_Param_Language,FOnFilterProfanityResultDelegate(Z_Param_OnFilterProfanityResult));
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function FilterProfanity **************************

// ********** Begin Class UOneEngineSDKPSSubsystem Function FilterProfanitySync ********************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics
{
	struct OneEngineSDKPSSubsystem_eventFilterProfanitySync_Parms
	{
		FString Text;
		FString Language;
		FString OutResult;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief  \xe8\x84\x8f\xe8\xaf\x9d\xe8\xbf\x87\xe6\xbb\xa4\xe5\x99\xa8\xef\xbc\x8c\xe5\xae\xa1\xe6\x9f\xa5\xe7\x94\xa8\xe6\x88\xb7\xe5\x86\x85\xe5\xae\xb9\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xb8\xad\xe7\x9a\x84\xe4\xba\xb5\xe6\xb8\x8e\xe8\xa1\x8c\xe4\xb8\xba\xe3\x80\x82 \xe5\x90\x8c\xe6\xad\xa5\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x8c\xe4\xbc\x9a\xe9\x98\xbb\xe5\xa1\x9e\xe5\xbd\x93\xe5\x89\x8d\xe7\xba\xbf\xe7\xa8\x8b\n\x09 * @param Text \xe8\xa6\x81\xe8\xbf\x87\xe6\xbb\xa4\xe7\x9a\x84\xe6\x96\x87\xe6\x9c\xac\xe3\x80\x82\xe6\xad\xa4\xe6\x96\x87\xe6\x9c\xac\xe7\x9a\x84\xe6\x9c\x80\xe5\xa4\xa7\xe5\xa4\xa7\xe5\xb0\x8f\xe4\xb8\xba 4KB\xe3\x80\x82\n\x09 * @param Language \xe4\xbe\x8b\xe5\xa6\x82\xef\xbc\x8c\xe2\x80\x9c""en-GB\xe2\x80\x9d,\xe5\x85\xb7\xe4\xbd\x93\xe6\x94\xaf\xe6\x8c\x81\xe7\x9a\x84locale\xe5\x80\xbc \xe8\xaf\xb7\xe5\x8f\x82\xe8\x80\x83 @see UOnePSLocaleEnum\n\x09 * @param OutResult \xe8\xaf\xa5\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xbc\x9a\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xad\xe8\xbf\x94\xe5\x9b\x9e\xef\xbc\x8c\xe5\xb9\xb6\xe4\xb8\x94\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe7\x9a\x84\xe4\xbb\xbb\xe4\xbd\x95\xe4\xb8\x8d\xe9\x9b\x85\xe5\x86\x85\xe5\xae\xb9\xe9\x83\xbd\xe5\xb0\x86\xe6\x9b\xbf\xe6\x8d\xa2\xe4\xb8\xba\xe2\x80\x9c*\xe2\x80\x9d\xe5\xad\x97\xe7\xac\xa6\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\x9c\xa8\xe6\x96\x87\xe6\x9c\xac\xe4\xb8\xad\xe6\x9c\xaa\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe8\x84\x8f\xe8\xaf\x9d\xef\xbc\x8c\xe5\x88\x99\xe5\x8e\x9f\xe5\xb0\x81\xe4\xb8\x8d\xe5\x8a\xa8\xe5\x9c\xb0\xe8\xbf\x94\xe5\x9b\x9e\xe3\x80\x82\n\x09 * @return 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe5\xa4\xb1\xe8\xb4\xa5\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief  \xe8\x84\x8f\xe8\xaf\x9d\xe8\xbf\x87\xe6\xbb\xa4\xe5\x99\xa8\xef\xbc\x8c\xe5\xae\xa1\xe6\x9f\xa5\xe7\x94\xa8\xe6\x88\xb7\xe5\x86\x85\xe5\xae\xb9\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xb8\xad\xe7\x9a\x84\xe4\xba\xb5\xe6\xb8\x8e\xe8\xa1\x8c\xe4\xb8\xba\xe3\x80\x82 \xe5\x90\x8c\xe6\xad\xa5\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x8c\xe4\xbc\x9a\xe9\x98\xbb\xe5\xa1\x9e\xe5\xbd\x93\xe5\x89\x8d\xe7\xba\xbf\xe7\xa8\x8b\n@param Text \xe8\xa6\x81\xe8\xbf\x87\xe6\xbb\xa4\xe7\x9a\x84\xe6\x96\x87\xe6\x9c\xac\xe3\x80\x82\xe6\xad\xa4\xe6\x96\x87\xe6\x9c\xac\xe7\x9a\x84\xe6\x9c\x80\xe5\xa4\xa7\xe5\xa4\xa7\xe5\xb0\x8f\xe4\xb8\xba 4KB\xe3\x80\x82\n@param Language \xe4\xbe\x8b\xe5\xa6\x82\xef\xbc\x8c\xe2\x80\x9c""en-GB\xe2\x80\x9d,\xe5\x85\xb7\xe4\xbd\x93\xe6\x94\xaf\xe6\x8c\x81\xe7\x9a\x84locale\xe5\x80\xbc \xe8\xaf\xb7\xe5\x8f\x82\xe8\x80\x83 @see UOnePSLocaleEnum\n@param OutResult \xe8\xaf\xa5\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xbc\x9a\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xad\xe8\xbf\x94\xe5\x9b\x9e\xef\xbc\x8c\xe5\xb9\xb6\xe4\xb8\x94\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe7\x9a\x84\xe4\xbb\xbb\xe4\xbd\x95\xe4\xb8\x8d\xe9\x9b\x85\xe5\x86\x85\xe5\xae\xb9\xe9\x83\xbd\xe5\xb0\x86\xe6\x9b\xbf\xe6\x8d\xa2\xe4\xb8\xba\xe2\x80\x9c*\xe2\x80\x9d\xe5\xad\x97\xe7\xac\xa6\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\x9c\xa8\xe6\x96\x87\xe6\x9c\xac\xe4\xb8\xad\xe6\x9c\xaa\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe8\x84\x8f\xe8\xaf\x9d\xef\xbc\x8c\xe5\x88\x99\xe5\x8e\x9f\xe5\xb0\x81\xe4\xb8\x8d\xe5\x8a\xa8\xe5\x9c\xb0\xe8\xbf\x94\xe5\x9b\x9e\xe3\x80\x82\n@return 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe5\xa4\xb1\xe8\xb4\xa5" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Text_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Language_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Text;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Language;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutResult;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_Text = { "Text", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventFilterProfanitySync_Parms, Text), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Text_MetaData), NewProp_Text_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_Language = { "Language", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventFilterProfanitySync_Parms, Language), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Language_MetaData), NewProp_Language_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_OutResult = { "OutResult", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventFilterProfanitySync_Parms, OutResult), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventFilterProfanitySync_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_Text,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_Language,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_OutResult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "FilterProfanitySync", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::OneEngineSDKPSSubsystem_eventFilterProfanitySync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::OneEngineSDKPSSubsystem_eventFilterProfanitySync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execFilterProfanitySync)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Text);
	P_GET_PROPERTY(FStrProperty,Z_Param_Language);
	P_GET_PROPERTY_REF(FStrProperty,Z_Param_Out_OutResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->FilterProfanitySync(Z_Param_Text,Z_Param_Language,Z_Param_Out_OutResult);
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function FilterProfanitySync **********************

// ********** Begin Class UOneEngineSDKPSSubsystem Function GetAccountId ***************************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics
{
	struct OneEngineSDKPSSubsystem_eventGetAccountId_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09* @brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84""AccountId\n\x09* @return FString AccountId\n\x09*/" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84""AccountId\n@return FString AccountId" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetAccountId_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetAccountId", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::OneEngineSDKPSSubsystem_eventGetAccountId_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::OneEngineSDKPSSubsystem_eventGetAccountId_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetAccountId)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetAccountId();
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function GetAccountId *****************************

// ********** Begin Class UOneEngineSDKPSSubsystem Function GetAccountState ************************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics
{
	struct OneEngineSDKPSSubsystem_eventGetAccountState_Parms
	{
		EOnePsnAccountState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe8\x8e\xb7\xe5\x8f\x96PSN\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x8a\xb6\xe6\x80\x81\n\x09 * @return EOnePsnAccountState PSN\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x8a\xb6\xe6\x80\x81\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe8\x8e\xb7\xe5\x8f\x96PSN\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x8a\xb6\xe6\x80\x81\n@return EOnePsnAccountState PSN\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x8a\xb6\xe6\x80\x81" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetAccountState_Parms, ReturnValue), Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState, METADATA_PARAMS(0, nullptr) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetAccountState", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::OneEngineSDKPSSubsystem_eventGetAccountState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::OneEngineSDKPSSubsystem_eventGetAccountState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetAccountState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EOnePsnAccountState*)Z_Param__Result=P_THIS->GetAccountState();
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function GetAccountState **************************

// ********** Begin Class UOneEngineSDKPSSubsystem Function GetBlockingUsers ***********************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics
{
	struct OneEngineSDKPSSubsystem_eventGetBlockingUsers_Parms
	{
		int32 Offset;
		int32 Limit;
		FScriptDelegate OnGetBlockingUsersResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xb1\x8f\xe8\x94\xbd\xe5\x88\x97\xe8\xa1\xa8\n\x09 * @param Offset \xe5\x81\x8f\xe7\xa7\xbb\xe9\x87\x8f\n\x09 * @param Limit \xe9\x99\x90\xe5\x88\xb6\xe6\x95\xb0\xe9\x87\x8f\n\x09 * @param OnGetBlockingUsersResult \xe8\x8e\xb7\xe5\x8f\x96\xe5\xb1\x8f\xe8\x94\xbd\xe5\x88\x97\xe8\xa1\xa8\xe5\x9b\x9e\xe8\xb0\x83\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xb1\x8f\xe8\x94\xbd\xe5\x88\x97\xe8\xa1\xa8\n@param Offset \xe5\x81\x8f\xe7\xa7\xbb\xe9\x87\x8f\n@param Limit \xe9\x99\x90\xe5\x88\xb6\xe6\x95\xb0\xe9\x87\x8f\n@param OnGetBlockingUsersResult \xe8\x8e\xb7\xe5\x8f\x96\xe5\xb1\x8f\xe8\x94\xbd\xe5\x88\x97\xe8\xa1\xa8\xe5\x9b\x9e\xe8\xb0\x83" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Offset;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Limit;
	static const UECodeGen_Private::FDelegatePropertyParams NewProp_OnGetBlockingUsersResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::NewProp_Offset = { "Offset", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetBlockingUsers_Parms, Offset), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::NewProp_Limit = { "Limit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetBlockingUsers_Parms, Limit), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FDelegatePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::NewProp_OnGetBlockingUsersResult = { "OnGetBlockingUsersResult", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetBlockingUsers_Parms, OnGetBlockingUsersResult), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature, METADATA_PARAMS(0, nullptr) }; // 2507314415
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::NewProp_Offset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::NewProp_Limit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::NewProp_OnGetBlockingUsersResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetBlockingUsers", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::OneEngineSDKPSSubsystem_eventGetBlockingUsers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::OneEngineSDKPSSubsystem_eventGetBlockingUsers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetBlockingUsers)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Offset);
	P_GET_PROPERTY(FIntProperty,Z_Param_Limit);
	P_GET_PROPERTY(FDelegateProperty,Z_Param_OnGetBlockingUsersResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GetBlockingUsers(Z_Param_Offset,Z_Param_Limit,FOnGetBlockingUsersResultDelegate(Z_Param_OnGetBlockingUsersResult));
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function GetBlockingUsers *************************

// ********** Begin Class UOneEngineSDKPSSubsystem Function GetCommunicationRestrictionStatus ******
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics
{
	struct OneEngineSDKPSSubsystem_eventGetCommunicationRestrictionStatus_Parms
	{
		FScriptDelegate OnRestrictionStatusResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe9\x80\x9a\xe4\xbf\xa1\xe9\x99\x90\xe5\x88\xb6\xe7\x8a\xb6\xe6\x80\x81\n\x09 * @param OnRestrictionStatusResult \xe8\x8e\xb7\xe5\x8f\x96\xe9\x80\x9a\xe4\xbf\xa1\xe9\x99\x90\xe5\x88\xb6\xe7\x8a\xb6\xe6\x80\x81\xe5\x9b\x9e\xe8\xb0\x83\xef\xbc\x8cResult \xe5\x8f\xaf\xe8\x83\xbd\xe7\x9a\x84\xe5\x80\xbc -1 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xa3\x80\xe6\x9f\xa5\xe5\xa4\xb1\xe8\xb4\xa5\xef\xbc\x8c 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x9c\xaa\xe6\xa3\x80\xe6\x9f\xa5\xef\xbc\x8c 1 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x9c\xaa\xe9\x99\x90\xe5\x88\xb6\xef\xbc\x8c 2 \xe8\xa1\xa8\xe7\xa4\xba\xe5\x8f\x97\xe9\x99\x90\xe5\x88\xb6\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe9\x80\x9a\xe4\xbf\xa1\xe9\x99\x90\xe5\x88\xb6\xe7\x8a\xb6\xe6\x80\x81\n@param OnRestrictionStatusResult \xe8\x8e\xb7\xe5\x8f\x96\xe9\x80\x9a\xe4\xbf\xa1\xe9\x99\x90\xe5\x88\xb6\xe7\x8a\xb6\xe6\x80\x81\xe5\x9b\x9e\xe8\xb0\x83\xef\xbc\x8cResult \xe5\x8f\xaf\xe8\x83\xbd\xe7\x9a\x84\xe5\x80\xbc -1 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xa3\x80\xe6\x9f\xa5\xe5\xa4\xb1\xe8\xb4\xa5\xef\xbc\x8c 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x9c\xaa\xe6\xa3\x80\xe6\x9f\xa5\xef\xbc\x8c 1 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x9c\xaa\xe9\x99\x90\xe5\x88\xb6\xef\xbc\x8c 2 \xe8\xa1\xa8\xe7\xa4\xba\xe5\x8f\x97\xe9\x99\x90\xe5\x88\xb6" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FDelegatePropertyParams NewProp_OnRestrictionStatusResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FDelegatePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::NewProp_OnRestrictionStatusResult = { "OnRestrictionStatusResult", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetCommunicationRestrictionStatus_Parms, OnRestrictionStatusResult), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature, METADATA_PARAMS(0, nullptr) }; // 3028338522
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::NewProp_OnRestrictionStatusResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetCommunicationRestrictionStatus", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::OneEngineSDKPSSubsystem_eventGetCommunicationRestrictionStatus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::OneEngineSDKPSSubsystem_eventGetCommunicationRestrictionStatus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetCommunicationRestrictionStatus)
{
	P_GET_PROPERTY(FDelegateProperty,Z_Param_OnRestrictionStatusResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GetCommunicationRestrictionStatus(FOnRestrictionStatusResultDelegate(Z_Param_OnRestrictionStatusResult));
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function GetCommunicationRestrictionStatus ********

// ********** Begin Class UOneEngineSDKPSSubsystem Function GetCountryCode *************************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics
{
	struct OneEngineSDKPSSubsystem_eventGetCountryCode_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe8\x8e\xb7\xe5\x8f\x96 CountryCode\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe8\x8e\xb7\xe5\x8f\x96 CountryCode" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetCountryCode_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetCountryCode", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::OneEngineSDKPSSubsystem_eventGetCountryCode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::OneEngineSDKPSSubsystem_eventGetCountryCode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetCountryCode)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetCountryCode();
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function GetCountryCode ***************************

// ********** Begin Class UOneEngineSDKPSSubsystem Function GetCountryRegion ***********************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics
{
	struct OneEngineSDKPSSubsystem_eventGetCountryRegion_Parms
	{
		FString CountryCode;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe6\xa0\xb9\xe6\x8d\xae\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\xe8\x8e\xb7\xe5\x8f\x96\xe5\x9b\xbd\xe5\xae\xb6\xe6\x89\x80\xe5\xb1\x9e\xe5\x9c\xb0\xe5\x8c\xba\n\x09 * @param CountryCode \xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\n\x09 * @return \xe5\x9b\xbd\xe5\xae\xb6\xe5\x9c\xb0\xe5\x8c\xba\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe6\xa0\xb9\xe6\x8d\xae\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\xe8\x8e\xb7\xe5\x8f\x96\xe5\x9b\xbd\xe5\xae\xb6\xe6\x89\x80\xe5\xb1\x9e\xe5\x9c\xb0\xe5\x8c\xba\n@param CountryCode \xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\n@return \xe5\x9b\xbd\xe5\xae\xb6\xe5\x9c\xb0\xe5\x8c\xba" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CountryCode_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CountryCode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::NewProp_CountryCode = { "CountryCode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetCountryRegion_Parms, CountryCode), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CountryCode_MetaData), NewProp_CountryCode_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetCountryRegion_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::NewProp_CountryCode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetCountryRegion", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::OneEngineSDKPSSubsystem_eventGetCountryRegion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::OneEngineSDKPSSubsystem_eventGetCountryRegion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetCountryRegion)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CountryCode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetCountryRegion(Z_Param_CountryCode);
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function GetCountryRegion *************************

// ********** Begin Class UOneEngineSDKPSSubsystem Function GetFriends *****************************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics
{
	struct OneEngineSDKPSSubsystem_eventGetFriends_Parms
	{
		int32 Offset;
		int32 Limit;
		FScriptDelegate OnGetFriendsResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xa5\xbd\xe5\x8f\x8b\xe5\x88\x97\xe8\xa1\xa8\n\x09 * @param Offset \xe5\x81\x8f\xe7\xa7\xbb\xe9\x87\x8f\n\x09 * @param Limit \xe9\x99\x90\xe5\x88\xb6\xe6\x95\xb0\xe9\x87\x8f\n\x09 * @param OnGetFriendsResult \xe8\x8e\xb7\xe5\x8f\x96\xe5\xa5\xbd\xe5\x8f\x8b\xe5\x88\x97\xe8\xa1\xa8\xe5\x9b\x9e\xe8\xb0\x83\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xa5\xbd\xe5\x8f\x8b\xe5\x88\x97\xe8\xa1\xa8\n@param Offset \xe5\x81\x8f\xe7\xa7\xbb\xe9\x87\x8f\n@param Limit \xe9\x99\x90\xe5\x88\xb6\xe6\x95\xb0\xe9\x87\x8f\n@param OnGetFriendsResult \xe8\x8e\xb7\xe5\x8f\x96\xe5\xa5\xbd\xe5\x8f\x8b\xe5\x88\x97\xe8\xa1\xa8\xe5\x9b\x9e\xe8\xb0\x83" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Offset;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Limit;
	static const UECodeGen_Private::FDelegatePropertyParams NewProp_OnGetFriendsResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::NewProp_Offset = { "Offset", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetFriends_Parms, Offset), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::NewProp_Limit = { "Limit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetFriends_Parms, Limit), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FDelegatePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::NewProp_OnGetFriendsResult = { "OnGetFriendsResult", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetFriends_Parms, OnGetFriendsResult), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature, METADATA_PARAMS(0, nullptr) }; // 209594987
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::NewProp_Offset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::NewProp_Limit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::NewProp_OnGetFriendsResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetFriends", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::OneEngineSDKPSSubsystem_eventGetFriends_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::OneEngineSDKPSSubsystem_eventGetFriends_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetFriends)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Offset);
	P_GET_PROPERTY(FIntProperty,Z_Param_Limit);
	P_GET_PROPERTY(FDelegateProperty,Z_Param_OnGetFriendsResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GetFriends(Z_Param_Offset,Z_Param_Limit,FOnGetFriendsResultDelegate(Z_Param_OnGetFriendsResult));
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function GetFriends *******************************

// ********** Begin Class UOneEngineSDKPSSubsystem Function GetOnlineId ****************************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics
{
	struct OneEngineSDKPSSubsystem_eventGetOnlineId_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09* @brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84OnlineId\n\x09* @return  FString OnlineId\n\x09*/" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84OnlineId\n@return  FString OnlineId" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetOnlineId_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetOnlineId", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::OneEngineSDKPSSubsystem_eventGetOnlineId_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::OneEngineSDKPSSubsystem_eventGetOnlineId_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetOnlineId)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetOnlineId();
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function GetOnlineId ******************************

// ********** Begin Class UOneEngineSDKPSSubsystem Function GetProductInfoListPS *******************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics
{
	struct OneEngineSDKPSSubsystem_eventGetProductInfoListPS_Parms
	{
		int32 ServiceLabel;
		FString CategoryLabel;
		FScriptDelegate Callback;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief  \xe8\x8e\xb7\xe5\x8f\x96\xe4\xba\xa7\xe5\x93\x81\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\x97\xe8\xa1\xa8\n\x09 * @param ServiceLabel \xe5\xa6\x82\xe6\x9e\x9c\xe5\x85\xb1\xe7\x94\xa8PS4\xe7\x9a\x84\xe5\x90\x8e\xe5\x8f\xb0\xe5\x95\x86\xe5\xba\x97\xe7\x9a\x84\xe8\xaf\x9d\xef\xbc\x8cPS5 \xe4\xb8\xba 1\xef\xbc\x8cPS4 \xe4\xb8\xba 0\n\x09 * @param CategoryLabel \xe9\xbb\x98\xe8\xae\xa4\xe4\xb8\xba\xe7\xa9\xba\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\n\x09 * @param Callback  \xe5\x9b\x9e\xe8\xb0\x83\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief  \xe8\x8e\xb7\xe5\x8f\x96\xe4\xba\xa7\xe5\x93\x81\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\x97\xe8\xa1\xa8\n@param ServiceLabel \xe5\xa6\x82\xe6\x9e\x9c\xe5\x85\xb1\xe7\x94\xa8PS4\xe7\x9a\x84\xe5\x90\x8e\xe5\x8f\xb0\xe5\x95\x86\xe5\xba\x97\xe7\x9a\x84\xe8\xaf\x9d\xef\xbc\x8cPS5 \xe4\xb8\xba 1\xef\xbc\x8cPS4 \xe4\xb8\xba 0\n@param CategoryLabel \xe9\xbb\x98\xe8\xae\xa4\xe4\xb8\xba\xe7\xa9\xba\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\n@param Callback  \xe5\x9b\x9e\xe8\xb0\x83" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Callback_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ServiceLabel;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CategoryLabel;
	static const UECodeGen_Private::FDelegatePropertyParams NewProp_Callback;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::NewProp_ServiceLabel = { "ServiceLabel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetProductInfoListPS_Parms, ServiceLabel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::NewProp_CategoryLabel = { "CategoryLabel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetProductInfoListPS_Parms, CategoryLabel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FDelegatePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::NewProp_Callback = { "Callback", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetProductInfoListPS_Parms, Callback), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Callback_MetaData), NewProp_Callback_MetaData) }; // 4292504417
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::NewProp_ServiceLabel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::NewProp_CategoryLabel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::NewProp_Callback,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetProductInfoListPS", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::OneEngineSDKPSSubsystem_eventGetProductInfoListPS_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::OneEngineSDKPSSubsystem_eventGetProductInfoListPS_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetProductInfoListPS)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ServiceLabel);
	P_GET_PROPERTY(FStrProperty,Z_Param_CategoryLabel);
	P_GET_PROPERTY_REF(FDelegateProperty,Z_Param_Out_Callback);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GetProductInfoListPS(Z_Param_ServiceLabel,Z_Param_CategoryLabel,FOnGetProductInfoListPSDelegate(Z_Param_Out_Callback));
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function GetProductInfoListPS *********************

// ********** Begin Class UOneEngineSDKPSSubsystem Function HideStoreIcon **************************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics
{
	struct OneEngineSDKPSSubsystem_eventHideStoreIcon_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe9\x9a\x90\xe8\x97\x8f\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe9\x9a\x90\xe8\x97\x8f" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventHideStoreIcon_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "HideStoreIcon", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::OneEngineSDKPSSubsystem_eventHideStoreIcon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::OneEngineSDKPSSubsystem_eventHideStoreIcon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execHideStoreIcon)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->HideStoreIcon();
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function HideStoreIcon ****************************

// ********** Begin Class UOneEngineSDKPSSubsystem Function OpenCommerceDialogPremiumMode **********
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics
{
	struct OneEngineSDKPSSubsystem_eventOpenCommerceDialogPremiumMode_Parms
	{
		FScriptDelegate OnOpenDialogResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe6\x89\x93\xe5\xbc\x80\xe5\x95\x86\xe5\xba\x97\xe5\xaf\xb9\xe8\xaf\x9d\xe6\xa1\x86,\xe9\xbc\x93\xe5\x8a\xb1\xe7\x94\xa8\xe6\x88\xb7\xe6\x88\x90\xe4\xb8\xbaPlus\xe4\xbc\x9a\xe5\x91\x98,\xe4\xbb\x85\xe6\x94\xaf\xe6\x8c\x81PS5\xe5\xb9\xb3\xe5\x8f\xb0\n\x09 * @param OnOpenDialogResult \xe6\x89\x93\xe5\xbc\x80\xe5\x95\x86\xe5\xba\x97\xe5\xaf\xb9\xe8\xaf\x9d\xe6\xa1\x86\xe5\x9b\x9e\xe8\xb0\x83\xef\xbc\x8cResult \xe4\xb8\xba 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe5\xa4\xb1\xe8\xb4\xa5\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe6\x89\x93\xe5\xbc\x80\xe5\x95\x86\xe5\xba\x97\xe5\xaf\xb9\xe8\xaf\x9d\xe6\xa1\x86,\xe9\xbc\x93\xe5\x8a\xb1\xe7\x94\xa8\xe6\x88\xb7\xe6\x88\x90\xe4\xb8\xbaPlus\xe4\xbc\x9a\xe5\x91\x98,\xe4\xbb\x85\xe6\x94\xaf\xe6\x8c\x81PS5\xe5\xb9\xb3\xe5\x8f\xb0\n@param OnOpenDialogResult \xe6\x89\x93\xe5\xbc\x80\xe5\x95\x86\xe5\xba\x97\xe5\xaf\xb9\xe8\xaf\x9d\xe6\xa1\x86\xe5\x9b\x9e\xe8\xb0\x83\xef\xbc\x8cResult \xe4\xb8\xba 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe5\xa4\xb1\xe8\xb4\xa5" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FDelegatePropertyParams NewProp_OnOpenDialogResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FDelegatePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::NewProp_OnOpenDialogResult = { "OnOpenDialogResult", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOpenCommerceDialogPremiumMode_Parms, OnOpenDialogResult), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature, METADATA_PARAMS(0, nullptr) }; // 1080759034
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::NewProp_OnOpenDialogResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OpenCommerceDialogPremiumMode", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::OneEngineSDKPSSubsystem_eventOpenCommerceDialogPremiumMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::OneEngineSDKPSSubsystem_eventOpenCommerceDialogPremiumMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execOpenCommerceDialogPremiumMode)
{
	P_GET_PROPERTY(FDelegateProperty,Z_Param_OnOpenDialogResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OpenCommerceDialogPremiumMode(FOnOpenDialogResultDelegate(Z_Param_OnOpenDialogResult));
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function OpenCommerceDialogPremiumMode ************

// ********** Begin Class UOneEngineSDKPSSubsystem Function SetFontPath ****************************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics
{
	struct OneEngineSDKPSSubsystem_eventSetFontPath_Parms
	{
		FString Path;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Path_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Path;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::NewProp_Path = { "Path", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventSetFontPath_Parms, Path), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Path_MetaData), NewProp_Path_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::NewProp_Path,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "SetFontPath", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::OneEngineSDKPSSubsystem_eventSetFontPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::OneEngineSDKPSSubsystem_eventSetFontPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execSetFontPath)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Path);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFontPath(Z_Param_Path);
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function SetFontPath ******************************

// ********** Begin Class UOneEngineSDKPSSubsystem Function ShowStoreIcon **************************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics
{
	struct OneEngineSDKPSSubsystem_eventShowStoreIcon_Parms
	{
		EOnePSStoreIconPos Pos;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe6\x98\xbe\xe7\xa4\xba\xe5\x95\x86\xe5\xba\x97\xe5\x9b\xbe\xe6\xa0\x87\n\x09 * @param Pos \xe6\x98\xbe\xe7\xa4\xba\xe4\xbd\x8d\xe7\xbd\xae\n\x09 * @return 0 \xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe5\xa4\xb1\xe8\xb4\xa5\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe6\x98\xbe\xe7\xa4\xba\xe5\x95\x86\xe5\xba\x97\xe5\x9b\xbe\xe6\xa0\x87\n@param Pos \xe6\x98\xbe\xe7\xa4\xba\xe4\xbd\x8d\xe7\xbd\xae\n@return 0 \xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe5\xa4\xb1\xe8\xb4\xa5" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Pos_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Pos;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::NewProp_Pos_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::NewProp_Pos = { "Pos", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventShowStoreIcon_Parms, Pos), Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos, METADATA_PARAMS(0, nullptr) }; // 3646537677
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventShowStoreIcon_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::NewProp_Pos_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::NewProp_Pos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "ShowStoreIcon", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::OneEngineSDKPSSubsystem_eventShowStoreIcon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::OneEngineSDKPSSubsystem_eventShowStoreIcon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execShowStoreIcon)
{
	P_GET_ENUM(EOnePSStoreIconPos,Z_Param_Pos);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->ShowStoreIcon(EOnePSStoreIconPos(Z_Param_Pos));
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function ShowStoreIcon ****************************

// ********** Begin Class UOneEngineSDKPSSubsystem Function StartNotifyPremiumFeature **************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics
{
	struct OneEngineSDKPSSubsystem_eventStartNotifyPremiumFeature_Parms
	{
		int32 Interval;
		int32 Mark;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe5\xbc\x80\xe5\xa7\x8b\xe5\xae\x9a\xe6\x9c\x9f\xe8\xb0\x83\xe7\x94\xa8\xe9\x80\x9a\xe7\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe7\x89\xb9\xe6\x80\xa7\xef\xbc\x8c\xe5\x9c\xa8\xe8\xb0\x83\xe7\x94\xa8\xe5\x89\x8d\xe9\x9c\x80\xe8\xa6\x81\xe5\x85\x88\xe8\xb0\x83\xe7\x94\xa8 CheckPremium \xe6\xa3\x80\xe6\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe6\x9d\x83\xe9\x99\x90\xe3\x80\x82\n\x09 * \xe9\x9c\x80\xe8\xa6\x81\xe5\x9c\xa8\xe5\x8f\x91\xe7\x94\x9f\xe5\xae\x9e\xe9\x99\x85\xe5\xa4\x9a\xe4\xba\xba\xe6\xb8\xb8\xe7\x8e\xa9\xe7\x9a\x84\xe6\x97\xb6\xe5\x80\x99\xe8\xb0\x83\xe7\x94\xa8\xef\xbc\x8c\xe4\xb8\x8d\xe5\x8c\x85\xe6\x8b\xac\xe5\xa4\xa7\xe5\x8e\x85\xe7\xad\x89\xe4\xba\x8b\xe5\x89\x8d\xe5\x87\x86\xe5\xa4\x87\xe9\x98\xb6\xe6\xae\xb5\xe3\x80\x82\n\x09 * \xe5\x9b\xa0\xe6\xad\xa4\xef\xbc\x8c\xe4\xb8\x80\xe8\x88\xac\xe6\x98\xaf\xe5\x9c\xa8\xe8\xbf\x9b\xe5\x85\xa5\xe6\xb8\xb8\xe6\x88\x8f\xef\xbc\x8c\xe7\x8e\xa9\xe5\xae\xb6\xe5\x8f\xaf\xe4\xbb\xa5\xe6\x93\x8d\xe4\xbd\x9c\xe7\x9a\x84\xe5\x90\x8c\xe6\x97\xb6\xef\xbc\x8c\xe5\xbc\x80\xe5\xa7\x8b\xe8\xb0\x83\xe7\x94\xa8\xe3\x80\x82\n\x09 * @param Interval `\xe5\xae\x9a\xe6\x9c\x9f\xe8\xb0\x83\xe7\x94\xa8\xe7\x9a\x84\xe6\x97\xb6\xe9\x97\xb4\xe9\x97\xb4\xe9\x9a\x94,\xe5\x8d\x95\xe4\xbd\x8d\xe4\xb8\xba\xe7\xa7\x92\xef\xbc\x8c\xe9\xbb\x98\xe8\xae\xa4\xe4\xb8\xba 1 \xe7\xa7\x92\xef\xbc\x88\xe5\xbb\xba\xe8\xae\xae\xef\xbc\x89\n\x09 * @param Mark \xe4\xbc\x9a\xe5\x91\x98\xe7\x89\xb9\xe6\x80\xa7\xe6\xa0\x87\xe8\xae\xb0\xef\xbc\x8c 0 \xe8\xa1\xa8\xe7\xa4\xba\xe4\xb8\x8d\xe6\x8c\x87\xe5\xae\x9a\xe4\xbb\xbb\xe4\xbd\x95\xe7\x89\xb9\xe6\x80\xa7\xef\xbc\x8c 1 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xbb\xa1\xe8\xb6\xb3\xe8\xb7\xa8\xe5\xb9\xb3\xe5\x8f\xb0\xe6\x92\xad\xe6\x94\xbe\xe6\x9d\xa1\xe4\xbb\xb6\xef\xbc\x8c 2 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xbb\xa1\xe8\xb6\xb3\xe5\xbc\x95\xe6\x93\x8e\xe5\x86\x85\xe8\xa7\x82\xe7\x9c\x8b\xe6\x9d\xa1\xe4\xbb\xb6\xef\xbc\x8c 3 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xbb\xa1\xe8\xb6\xb3 1 \xe5\x92\x8c 2 \xe7\x9a\x84\xe6\x9d\xa1\xe4\xbb\xb6\n\x09 * @return 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe5\xa4\xb1\xe8\xb4\xa5\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe5\xbc\x80\xe5\xa7\x8b\xe5\xae\x9a\xe6\x9c\x9f\xe8\xb0\x83\xe7\x94\xa8\xe9\x80\x9a\xe7\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe7\x89\xb9\xe6\x80\xa7\xef\xbc\x8c\xe5\x9c\xa8\xe8\xb0\x83\xe7\x94\xa8\xe5\x89\x8d\xe9\x9c\x80\xe8\xa6\x81\xe5\x85\x88\xe8\xb0\x83\xe7\x94\xa8 CheckPremium \xe6\xa3\x80\xe6\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe6\x9d\x83\xe9\x99\x90\xe3\x80\x82\n\xe9\x9c\x80\xe8\xa6\x81\xe5\x9c\xa8\xe5\x8f\x91\xe7\x94\x9f\xe5\xae\x9e\xe9\x99\x85\xe5\xa4\x9a\xe4\xba\xba\xe6\xb8\xb8\xe7\x8e\xa9\xe7\x9a\x84\xe6\x97\xb6\xe5\x80\x99\xe8\xb0\x83\xe7\x94\xa8\xef\xbc\x8c\xe4\xb8\x8d\xe5\x8c\x85\xe6\x8b\xac\xe5\xa4\xa7\xe5\x8e\x85\xe7\xad\x89\xe4\xba\x8b\xe5\x89\x8d\xe5\x87\x86\xe5\xa4\x87\xe9\x98\xb6\xe6\xae\xb5\xe3\x80\x82\n\xe5\x9b\xa0\xe6\xad\xa4\xef\xbc\x8c\xe4\xb8\x80\xe8\x88\xac\xe6\x98\xaf\xe5\x9c\xa8\xe8\xbf\x9b\xe5\x85\xa5\xe6\xb8\xb8\xe6\x88\x8f\xef\xbc\x8c\xe7\x8e\xa9\xe5\xae\xb6\xe5\x8f\xaf\xe4\xbb\xa5\xe6\x93\x8d\xe4\xbd\x9c\xe7\x9a\x84\xe5\x90\x8c\xe6\x97\xb6\xef\xbc\x8c\xe5\xbc\x80\xe5\xa7\x8b\xe8\xb0\x83\xe7\x94\xa8\xe3\x80\x82\n@param Interval `\xe5\xae\x9a\xe6\x9c\x9f\xe8\xb0\x83\xe7\x94\xa8\xe7\x9a\x84\xe6\x97\xb6\xe9\x97\xb4\xe9\x97\xb4\xe9\x9a\x94,\xe5\x8d\x95\xe4\xbd\x8d\xe4\xb8\xba\xe7\xa7\x92\xef\xbc\x8c\xe9\xbb\x98\xe8\xae\xa4\xe4\xb8\xba 1 \xe7\xa7\x92\xef\xbc\x88\xe5\xbb\xba\xe8\xae\xae\xef\xbc\x89\n@param Mark \xe4\xbc\x9a\xe5\x91\x98\xe7\x89\xb9\xe6\x80\xa7\xe6\xa0\x87\xe8\xae\xb0\xef\xbc\x8c 0 \xe8\xa1\xa8\xe7\xa4\xba\xe4\xb8\x8d\xe6\x8c\x87\xe5\xae\x9a\xe4\xbb\xbb\xe4\xbd\x95\xe7\x89\xb9\xe6\x80\xa7\xef\xbc\x8c 1 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xbb\xa1\xe8\xb6\xb3\xe8\xb7\xa8\xe5\xb9\xb3\xe5\x8f\xb0\xe6\x92\xad\xe6\x94\xbe\xe6\x9d\xa1\xe4\xbb\xb6\xef\xbc\x8c 2 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xbb\xa1\xe8\xb6\xb3\xe5\xbc\x95\xe6\x93\x8e\xe5\x86\x85\xe8\xa7\x82\xe7\x9c\x8b\xe6\x9d\xa1\xe4\xbb\xb6\xef\xbc\x8c 3 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xbb\xa1\xe8\xb6\xb3 1 \xe5\x92\x8c 2 \xe7\x9a\x84\xe6\x9d\xa1\xe4\xbb\xb6\n@return 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe5\xa4\xb1\xe8\xb4\xa5" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Interval;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Mark;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::NewProp_Interval = { "Interval", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventStartNotifyPremiumFeature_Parms, Interval), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::NewProp_Mark = { "Mark", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventStartNotifyPremiumFeature_Parms, Mark), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventStartNotifyPremiumFeature_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::NewProp_Interval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::NewProp_Mark,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "StartNotifyPremiumFeature", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::OneEngineSDKPSSubsystem_eventStartNotifyPremiumFeature_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::OneEngineSDKPSSubsystem_eventStartNotifyPremiumFeature_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execStartNotifyPremiumFeature)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Interval);
	P_GET_PROPERTY(FIntProperty,Z_Param_Mark);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->StartNotifyPremiumFeature(Z_Param_Interval,Z_Param_Mark);
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function StartNotifyPremiumFeature ****************

// ********** Begin Class UOneEngineSDKPSSubsystem Function StopNotifyPremiumFeature ***************
struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics
{
	struct OneEngineSDKPSSubsystem_eventStopNotifyPremiumFeature_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n\x09 * @brief \xe7\x84\xb6\xe5\x90\x8e\xe5\x9c\xa8\xe7\x8e\xa9\xe5\xae\xb6\xe7\xbb\x93\xe6\x9d\x9f\xe6\x9c\xac\xe5\x9c\xba\xe6\xb8\xb8\xe6\x88\x8f\xe7\x9a\x84\xe6\x97\xb6\xe5\x80\x99\xef\xbc\x8c\xe5\x81\x9c\xe6\xad\xa2\xe8\xb0\x83\xe7\x94\xa8\xe9\x80\x9a\xe7\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe7\x89\xb9\xe6\x80\xa7.\xe6\xb3\xa8\xe6\x84\x8f\xe4\xb8\x8d\xe5\x8f\xaf\xe4\xbb\xa5\xe5\x9c\xa8\xe7\x94\xbb\xe9\x9d\xa2\xe8\xbf\x81\xe7\xa7\xbb\xe5\x9b\x9e\xe5\xa4\xa7\xe5\x8e\x85\xe6\x89\x8d\xe5\x81\x9c\xe6\xad\xa2\xe8\xb0\x83\xe7\x94\xa8\xe3\x80\x82\xe8\x80\x8c\xe6\x98\xaf\xe5\xba\x94\xe8\xaf\xa5\xe5\x9c\xa8\xe5\xaf\xb9\xe5\xb1\x80\xe7\xbb\x93\xe6\x9d\x9f\xe7\x9a\x84\xe5\x90\x8c\xe6\x97\xb6\xe5\x81\x9c\xe6\xad\xa2\xe8\xb0\x83\xe7\x94\xa8\n\x09 * @return 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe5\xa4\xb1\xe8\xb4\xa5\n\x09 */" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@brief \xe7\x84\xb6\xe5\x90\x8e\xe5\x9c\xa8\xe7\x8e\xa9\xe5\xae\xb6\xe7\xbb\x93\xe6\x9d\x9f\xe6\x9c\xac\xe5\x9c\xba\xe6\xb8\xb8\xe6\x88\x8f\xe7\x9a\x84\xe6\x97\xb6\xe5\x80\x99\xef\xbc\x8c\xe5\x81\x9c\xe6\xad\xa2\xe8\xb0\x83\xe7\x94\xa8\xe9\x80\x9a\xe7\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe7\x89\xb9\xe6\x80\xa7.\xe6\xb3\xa8\xe6\x84\x8f\xe4\xb8\x8d\xe5\x8f\xaf\xe4\xbb\xa5\xe5\x9c\xa8\xe7\x94\xbb\xe9\x9d\xa2\xe8\xbf\x81\xe7\xa7\xbb\xe5\x9b\x9e\xe5\xa4\xa7\xe5\x8e\x85\xe6\x89\x8d\xe5\x81\x9c\xe6\xad\xa2\xe8\xb0\x83\xe7\x94\xa8\xe3\x80\x82\xe8\x80\x8c\xe6\x98\xaf\xe5\xba\x94\xe8\xaf\xa5\xe5\x9c\xa8\xe5\xaf\xb9\xe5\xb1\x80\xe7\xbb\x93\xe6\x9d\x9f\xe7\x9a\x84\xe5\x90\x8c\xe6\x97\xb6\xe5\x81\x9c\xe6\xad\xa2\xe8\xb0\x83\xe7\x94\xa8\n@return 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe5\xa4\xb1\xe8\xb4\xa5" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventStopNotifyPremiumFeature_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "StopNotifyPremiumFeature", Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::OneEngineSDKPSSubsystem_eventStopNotifyPremiumFeature_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::OneEngineSDKPSSubsystem_eventStopNotifyPremiumFeature_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execStopNotifyPremiumFeature)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->StopNotifyPremiumFeature();
	P_NATIVE_END;
}
// ********** End Class UOneEngineSDKPSSubsystem Function StopNotifyPremiumFeature *****************

// ********** Begin Class UOneEngineSDKPSSubsystem *************************************************
void UOneEngineSDKPSSubsystem::StaticRegisterNativesUOneEngineSDKPSSubsystem()
{
	UClass* Class = UOneEngineSDKPSSubsystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CheckPremium", &UOneEngineSDKPSSubsystem::execCheckPremium },
		{ "FilterProfanity", &UOneEngineSDKPSSubsystem::execFilterProfanity },
		{ "FilterProfanitySync", &UOneEngineSDKPSSubsystem::execFilterProfanitySync },
		{ "GetAccountId", &UOneEngineSDKPSSubsystem::execGetAccountId },
		{ "GetAccountState", &UOneEngineSDKPSSubsystem::execGetAccountState },
		{ "GetBlockingUsers", &UOneEngineSDKPSSubsystem::execGetBlockingUsers },
		{ "GetCommunicationRestrictionStatus", &UOneEngineSDKPSSubsystem::execGetCommunicationRestrictionStatus },
		{ "GetCountryCode", &UOneEngineSDKPSSubsystem::execGetCountryCode },
		{ "GetCountryRegion", &UOneEngineSDKPSSubsystem::execGetCountryRegion },
		{ "GetFriends", &UOneEngineSDKPSSubsystem::execGetFriends },
		{ "GetOnlineId", &UOneEngineSDKPSSubsystem::execGetOnlineId },
		{ "GetProductInfoListPS", &UOneEngineSDKPSSubsystem::execGetProductInfoListPS },
		{ "HideStoreIcon", &UOneEngineSDKPSSubsystem::execHideStoreIcon },
		{ "OpenCommerceDialogPremiumMode", &UOneEngineSDKPSSubsystem::execOpenCommerceDialogPremiumMode },
		{ "SetFontPath", &UOneEngineSDKPSSubsystem::execSetFontPath },
		{ "ShowStoreIcon", &UOneEngineSDKPSSubsystem::execShowStoreIcon },
		{ "StartNotifyPremiumFeature", &UOneEngineSDKPSSubsystem::execStartNotifyPremiumFeature },
		{ "StopNotifyPremiumFeature", &UOneEngineSDKPSSubsystem::execStopNotifyPremiumFeature },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UOneEngineSDKPSSubsystem;
UClass* UOneEngineSDKPSSubsystem::GetPrivateStaticClass()
{
	using TClass = UOneEngineSDKPSSubsystem;
	if (!Z_Registration_Info_UClass_UOneEngineSDKPSSubsystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("OneEngineSDKPSSubsystem"),
			Z_Registration_Info_UClass_UOneEngineSDKPSSubsystem.InnerSingleton,
			StaticRegisterNativesUOneEngineSDKPSSubsystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UOneEngineSDKPSSubsystem.InnerSingleton;
}
UClass* Z_Construct_UClass_UOneEngineSDKPSSubsystem_NoRegister()
{
	return UOneEngineSDKPSSubsystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "IncludePath", "OneEngineSDKPSSubsystem.h" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium, "CheckPremium" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity, "FilterProfanity" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync, "FilterProfanitySync" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId, "GetAccountId" }, // *********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState, "GetAccountState" }, // *********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers, "GetBlockingUsers" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus, "GetCommunicationRestrictionStatus" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode, "GetCountryCode" }, // *********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion, "GetCountryRegion" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends, "GetFriends" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId, "GetOnlineId" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS, "GetProductInfoListPS" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon, "HideStoreIcon" }, // **********
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature, "OnCheckPremiumResultDelegate__DelegateSignature" }, // 2687271056
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature, "OnFilterProfanityResultDelegate__DelegateSignature" }, // 4042865203
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature, "OnGetBlockingUsersResultDelegate__DelegateSignature" }, // 2507314415
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature, "OnGetFriendsResultDelegate__DelegateSignature" }, // 209594987
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature, "OnGetProductInfoListPSDelegate__DelegateSignature" }, // 4292504417
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature, "OnOpenDialogResultDelegate__DelegateSignature" }, // 1080759034
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature, "OnRestrictionStatusResultDelegate__DelegateSignature" }, // 3028338522
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode, "OpenCommerceDialogPremiumMode" }, // 3222362838
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath, "SetFontPath" }, // 1348551015
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon, "ShowStoreIcon" }, // 2617060964
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature, "StartNotifyPremiumFeature" }, // 631777858
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature, "StopNotifyPremiumFeature" }, // 860737961
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature, "WidgetVisibilityDelegate__DelegateSignature" }, // 3948002348
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UOneEngineSDKPSSubsystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameInstanceSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics::ClassParams = {
	&UOneEngineSDKPSSubsystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics::Class_MetaDataParams), Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UOneEngineSDKPSSubsystem()
{
	if (!Z_Registration_Info_UClass_UOneEngineSDKPSSubsystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UOneEngineSDKPSSubsystem.OuterSingleton, Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UOneEngineSDKPSSubsystem.OuterSingleton;
}
UOneEngineSDKPSSubsystem::UOneEngineSDKPSSubsystem() {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UOneEngineSDKPSSubsystem);
UOneEngineSDKPSSubsystem::~UOneEngineSDKPSSubsystem() {}
// ********** End Class UOneEngineSDKPSSubsystem ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h__Script_OneEngineSDK_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EOnePSStoreIconPos_StaticEnum, TEXT("EOnePSStoreIconPos"), &Z_Registration_Info_UEnum_EOnePSStoreIconPos, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3646537677U) },
		{ EOnePsnAccountState_StaticEnum, TEXT("EOnePsnAccountState"), &Z_Registration_Info_UEnum_EOnePsnAccountState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, **********U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FOnePSUserProfile::StaticStruct, Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewStructOps, TEXT("OnePSUserProfile"), &Z_Registration_Info_UScriptStruct_FOnePSUserProfile, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOnePSUserProfile), 368822748U) },
		{ FOnePSUserProfileResponse::StaticStruct, Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewStructOps, TEXT("OnePSUserProfileResponse"), &Z_Registration_Info_UScriptStruct_FOnePSUserProfileResponse, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOnePSUserProfileResponse), 2081087001U) },
		{ FOnePSPurchaseForm::StaticStruct, Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewStructOps, TEXT("OnePSPurchaseForm"), &Z_Registration_Info_UScriptStruct_FOnePSPurchaseForm, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOnePSPurchaseForm), 191863169U) },
		{ FOnePSProductMediaImage::StaticStruct, Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewStructOps, TEXT("OnePSProductMediaImage"), &Z_Registration_Info_UScriptStruct_FOnePSProductMediaImage, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOnePSProductMediaImage), 657213321U) },
		{ FOnePSProductMedia::StaticStruct, Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::NewStructOps, TEXT("OnePSProductMedia"), &Z_Registration_Info_UScriptStruct_FOnePSProductMedia, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOnePSProductMedia), 2579785438U) },
		{ FOnePSProductSku::StaticStruct, Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewStructOps, TEXT("OnePSProductSku"), &Z_Registration_Info_UScriptStruct_FOnePSProductSku, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOnePSProductSku), 4209802740U) },
		{ FOnePSProduct::StaticStruct, Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewStructOps, TEXT("OnePSProduct"), &Z_Registration_Info_UScriptStruct_FOnePSProduct, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOnePSProduct), 674518079U) },
		{ FOnePSProductCategory::StaticStruct, Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewStructOps, TEXT("OnePSProductCategory"), &Z_Registration_Info_UScriptStruct_FOnePSProductCategory, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOnePSProductCategory), 1330006013U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UOnePSLocaleEnum, UOnePSLocaleEnum::StaticClass, TEXT("UOnePSLocaleEnum"), &Z_Registration_Info_UClass_UOnePSLocaleEnum, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UOnePSLocaleEnum), 1065997023U) },
		{ Z_Construct_UClass_UOneEngineSDKPSSubsystem, UOneEngineSDKPSSubsystem::StaticClass, TEXT("UOneEngineSDKPSSubsystem"), &Z_Registration_Info_UClass_UOneEngineSDKPSSubsystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UOneEngineSDKPSSubsystem), 682460440U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h__Script_OneEngineSDK_3979310773(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h__Script_OneEngineSDK_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h__Script_OneEngineSDK_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h__Script_OneEngineSDK_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h__Script_OneEngineSDK_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h__Script_OneEngineSDK_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
