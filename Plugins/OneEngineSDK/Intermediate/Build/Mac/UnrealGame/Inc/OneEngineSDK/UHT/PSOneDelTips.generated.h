// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSOneDelTips.h"

#ifdef ONEENGINESDK_PSOneDelTips_generated_h
#error "PSOneDelTips.generated.h already included, missing '#pragma once' in PSOneDelTips.h"
#endif
#define ONEENGINESDK_PSOneDelTips_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UPSOneDelTips ************************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneDelTips_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneDelTips_h_15_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneDelTips(); \
	friend struct Z_Construct_UClass_UPSOneDelTips_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneDelTips_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSOneDelTips, UPSOneFocusUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSOneDelTips_NoRegister) \
	DECLARE_SERIALIZER(UPSOneDelTips)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneDelTips_h_15_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneDelTips(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSOneDelTips(UPSOneDelTips&&) = delete; \
	UPSOneDelTips(const UPSOneDelTips&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneDelTips); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneDelTips); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneDelTips) \
	NO_API virtual ~UPSOneDelTips();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneDelTips_h_12_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneDelTips_h_15_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneDelTips_h_15_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneDelTips_h_15_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSOneDelTips;

// ********** End Class UPSOneDelTips **************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneDelTips_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
