// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDKHelper.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeOneEngineSDKHelper() {}

// ********** Begin Cross Module References ********************************************************
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneAIHelpType();
ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneEngineSDKRegionType();
ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType();
ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneNaverGameType();
ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOnePermissionType();
ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneResEventState();
ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneScreenOrientation();
ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneShareAppTarget();
ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneShareType();
ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockResult();
ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockType();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneActiveQualificationInfo();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneAntiAddictionInfo();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneDeviceInfo();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePaymentInfo();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePermissionInfo();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneProductInfo();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePushMessage();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePushNotDisturbInfo();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePushStatus();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePushTypeInfo();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneRoleInfo();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneShareData();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneShareWeiboSuperData();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneURCRoleInfo();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneUserInfo();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneUserLocationInfo();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneUserThirdInfo();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FUserIpInfo();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EOneEngineThirdType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EOneEngineThirdType;
static UEnum* EOneEngineThirdType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EOneEngineThirdType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EOneEngineThirdType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneEngineThirdType"));
	}
	return Z_Registration_Info_UEnum_EOneEngineThirdType.OuterSingleton;
}
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneEngineThirdType>()
{
	return EOneEngineThirdType_StaticEnum();
}
struct Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "APJ.Comment", "//PlayStation \n" },
		{ "APJ.Name", "EOneEngineThirdType::APJ" },
		{ "APJ.ToolTip", "PlayStation" },
		{ "Apple.Comment", "//Naver\n" },
		{ "Apple.Name", "EOneEngineThirdType::Apple" },
		{ "Apple.ToolTip", "Naver" },
		{ "BlueprintType", "true" },
		{ "Crunchyrool.Comment", "//APJ\n" },
		{ "Crunchyrool.Name", "EOneEngineThirdType::Crunchyrool" },
		{ "Crunchyrool.ToolTip", "APJ" },
		{ "Email.Comment", "//\xe6\x89\x8b\xe6\x9c\xba\n" },
		{ "Email.Name", "EOneEngineThirdType::Email" },
		{ "Email.ToolTip", "\xe6\x89\x8b\xe6\x9c\xba" },
		{ "Facebook.Comment", "//\xe6\xb8\xb8\xe5\xae\xa2\n" },
		{ "Facebook.Name", "EOneEngineThirdType::Facebook" },
		{ "Facebook.ToolTip", "\xe6\xb8\xb8\xe5\xae\xa2" },
		{ "GameCenter.Comment", "//GooglePlay\n" },
		{ "GameCenter.Name", "EOneEngineThirdType::GameCenter" },
		{ "GameCenter.ToolTip", "GooglePlay" },
		{ "Google.Comment", "//Facebook\n" },
		{ "Google.Name", "EOneEngineThirdType::Google" },
		{ "Google.ToolTip", "Facebook" },
		{ "GooglePlay.Comment", "//Line\n" },
		{ "GooglePlay.Name", "EOneEngineThirdType::GooglePlay" },
		{ "GooglePlay.ToolTip", "Line" },
		{ "Guest.Name", "EOneEngineThirdType::Guest" },
		{ "GuestInherit.Comment", "//\xe5\xbe\xae\xe4\xbf\xa1\n" },
		{ "GuestInherit.Name", "EOneEngineThirdType::GuestInherit" },
		{ "GuestInherit.ToolTip", "\xe5\xbe\xae\xe4\xbf\xa1" },
		{ "HONOR.Comment", "//ShareCode\n" },
		{ "HONOR.Name", "EOneEngineThirdType::HONOR" },
		{ "HONOR.ToolTip", "ShareCode" },
		{ "HW.Comment", "//Infiplay\n" },
		{ "HW.Name", "EOneEngineThirdType::HW" },
		{ "HW.ToolTip", "Infiplay" },
		{ "Infiplay.Comment", "//MailRu\n" },
		{ "Infiplay.Name", "EOneEngineThirdType::Infiplay" },
		{ "Infiplay.ToolTip", "MailRu" },
		{ "Line.Comment", "//Twitter\n" },
		{ "Line.Name", "EOneEngineThirdType::Line" },
		{ "Line.ToolTip", "Twitter" },
		{ "LinkAccount.Comment", "//Crunchyrool\n" },
		{ "LinkAccount.Name", "EOneEngineThirdType::LinkAccount" },
		{ "LinkAccount.ToolTip", "Crunchyrool" },
		{ "MailRu.Comment", "//Yandex\n" },
		{ "MailRu.Name", "EOneEngineThirdType::MailRu" },
		{ "MailRu.ToolTip", "Yandex" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "Naver.Comment", "//VK\n" },
		{ "Naver.Name", "EOneEngineThirdType::Naver" },
		{ "Naver.ToolTip", "VK" },
		{ "NaverCafe.Comment", "//\xe8\xbf\x9e\xe6\x90\xbaID\n" },
		{ "NaverCafe.Name", "EOneEngineThirdType::NaverCafe" },
		{ "NaverCafe.ToolTip", "\xe8\xbf\x9e\xe6\x90\xbaID" },
		{ "PCScanCode.Comment", "//steam\n" },
		{ "PCScanCode.Name", "EOneEngineThirdType::PCScanCode" },
		{ "PCScanCode.ToolTip", "steam" },
		{ "Phone.Comment", "//GameCenter\n" },
		{ "Phone.Name", "EOneEngineThirdType::Phone" },
		{ "Phone.ToolTip", "GameCenter" },
		{ "PlayStation.Comment", "//PCScanCode\n" },
		{ "PlayStation.Name", "EOneEngineThirdType::PlayStation" },
		{ "PlayStation.ToolTip", "PCScanCode" },
		{ "ShareCode.Comment", "//\xe5\x8d\x8e\xe4\xb8\xba\n" },
		{ "ShareCode.Name", "EOneEngineThirdType::ShareCode" },
		{ "ShareCode.ToolTip", "\xe5\x8d\x8e\xe4\xb8\xba" },
		{ "STEAM.Comment", "//\xe8\x8d\xa3\xe8\x80\x80\n" },
		{ "STEAM.Name", "EOneEngineThirdType::STEAM" },
		{ "STEAM.ToolTip", "\xe8\x8d\xa3\xe8\x80\x80" },
		{ "Twitter.Comment", "//Google\n" },
		{ "Twitter.Name", "EOneEngineThirdType::Twitter" },
		{ "Twitter.ToolTip", "Google" },
		{ "VK.Comment", "//Email\n" },
		{ "VK.Name", "EOneEngineThirdType::VK" },
		{ "VK.ToolTip", "Email" },
		{ "WeChat.Comment", "//Apple\n" },
		{ "WeChat.Name", "EOneEngineThirdType::WeChat" },
		{ "WeChat.ToolTip", "Apple" },
		{ "WMPass.Comment", "//NaverCafe\n" },
		{ "WMPass.Name", "EOneEngineThirdType::WMPass" },
		{ "WMPass.ToolTip", "NaverCafe" },
		{ "Yandex.Comment", "//\xe6\xb8\xb8\xe5\xae\xa2\xe7\xbb\xa7\xe6\x89\xbf\xe7\xa0\x81\n" },
		{ "Yandex.Name", "EOneEngineThirdType::Yandex" },
		{ "Yandex.ToolTip", "\xe6\xb8\xb8\xe5\xae\xa2\xe7\xbb\xa7\xe6\x89\xbf\xe7\xa0\x81" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EOneEngineThirdType::Guest", (int64)EOneEngineThirdType::Guest },
		{ "EOneEngineThirdType::Facebook", (int64)EOneEngineThirdType::Facebook },
		{ "EOneEngineThirdType::Google", (int64)EOneEngineThirdType::Google },
		{ "EOneEngineThirdType::Twitter", (int64)EOneEngineThirdType::Twitter },
		{ "EOneEngineThirdType::Line", (int64)EOneEngineThirdType::Line },
		{ "EOneEngineThirdType::GooglePlay", (int64)EOneEngineThirdType::GooglePlay },
		{ "EOneEngineThirdType::GameCenter", (int64)EOneEngineThirdType::GameCenter },
		{ "EOneEngineThirdType::Phone", (int64)EOneEngineThirdType::Phone },
		{ "EOneEngineThirdType::Email", (int64)EOneEngineThirdType::Email },
		{ "EOneEngineThirdType::VK", (int64)EOneEngineThirdType::VK },
		{ "EOneEngineThirdType::Naver", (int64)EOneEngineThirdType::Naver },
		{ "EOneEngineThirdType::Apple", (int64)EOneEngineThirdType::Apple },
		{ "EOneEngineThirdType::WeChat", (int64)EOneEngineThirdType::WeChat },
		{ "EOneEngineThirdType::GuestInherit", (int64)EOneEngineThirdType::GuestInherit },
		{ "EOneEngineThirdType::Yandex", (int64)EOneEngineThirdType::Yandex },
		{ "EOneEngineThirdType::MailRu", (int64)EOneEngineThirdType::MailRu },
		{ "EOneEngineThirdType::Infiplay", (int64)EOneEngineThirdType::Infiplay },
		{ "EOneEngineThirdType::HW", (int64)EOneEngineThirdType::HW },
		{ "EOneEngineThirdType::ShareCode", (int64)EOneEngineThirdType::ShareCode },
		{ "EOneEngineThirdType::HONOR", (int64)EOneEngineThirdType::HONOR },
		{ "EOneEngineThirdType::STEAM", (int64)EOneEngineThirdType::STEAM },
		{ "EOneEngineThirdType::PCScanCode", (int64)EOneEngineThirdType::PCScanCode },
		{ "EOneEngineThirdType::PlayStation", (int64)EOneEngineThirdType::PlayStation },
		{ "EOneEngineThirdType::APJ", (int64)EOneEngineThirdType::APJ },
		{ "EOneEngineThirdType::Crunchyrool", (int64)EOneEngineThirdType::Crunchyrool },
		{ "EOneEngineThirdType::LinkAccount", (int64)EOneEngineThirdType::LinkAccount },
		{ "EOneEngineThirdType::NaverCafe", (int64)EOneEngineThirdType::NaverCafe },
		{ "EOneEngineThirdType::WMPass", (int64)EOneEngineThirdType::WMPass },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	"EOneEngineThirdType",
	"EOneEngineThirdType",
	Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType()
{
	if (!Z_Registration_Info_UEnum_EOneEngineThirdType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EOneEngineThirdType.InnerSingleton, Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EOneEngineThirdType.InnerSingleton;
}
// ********** End Enum EOneEngineThirdType *********************************************************

// ********** Begin ScriptStruct FOneUserThirdInfo *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOneUserThirdInfo;
class UScriptStruct* FOneUserThirdInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOneUserThirdInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOneUserThirdInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneUserThirdInfo, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneUserThirdInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FOneUserThirdInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe4\xb8\x89\xe6\x96\xb9\xe5\xb9\xb3\xe5\x8f\xb0\xe4\xbf\xa1\xe6\x81\xaf\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe4\xb8\x89\xe6\x96\xb9\xe5\xb9\xb3\xe5\x8f\xb0\xe4\xbf\xa1\xe6\x81\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UserId_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7ID\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ThirdId_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7ThirdId\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7ThirdId" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Avatar_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe5\xa4\xb4\xe5\x83\x8f\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe5\xa4\xb4\xe5\x83\x8f" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UserName_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8d\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8d" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Email_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Email\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Email" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//  \xe5\xb9\xb3\xe5\x8f\xb0\xe7\xb1\xbb\xe5\x9e\x8b\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xb9\xb3\xe5\x8f\xb0\xe7\xb1\xbb\xe5\x9e\x8b" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_UserId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ThirdId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Avatar;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UserName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Email;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Type;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneUserThirdInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_UserId = { "UserId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserThirdInfo, UserId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UserId_MetaData), NewProp_UserId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_ThirdId = { "ThirdId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserThirdInfo, ThirdId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ThirdId_MetaData), NewProp_ThirdId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Avatar = { "Avatar", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserThirdInfo, Avatar), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Avatar_MetaData), NewProp_Avatar_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_UserName = { "UserName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserThirdInfo, UserName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UserName_MetaData), NewProp_UserName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Email = { "Email", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserThirdInfo, Email), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Email_MetaData), NewProp_Email_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserThirdInfo, Type), Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Type_MetaData), NewProp_Type_MetaData) }; // 1323311522
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_UserId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_ThirdId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Avatar,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_UserName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Email,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Type_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Type,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OneUserThirdInfo",
	Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::PropPointers),
	sizeof(FOneUserThirdInfo),
	alignof(FOneUserThirdInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOneUserThirdInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FOneUserThirdInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOneUserThirdInfo.InnerSingleton, Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOneUserThirdInfo.InnerSingleton;
}
// ********** End ScriptStruct FOneUserThirdInfo ***************************************************

// ********** Begin ScriptStruct FOneUserInfo ******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOneUserInfo;
class UScriptStruct* FOneUserInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOneUserInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOneUserInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneUserInfo, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneUserInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FOneUserInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOneUserInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe4\xbf\xa1\xe6\x81\xaf\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe4\xbf\xa1\xe6\x81\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UserId_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7ID\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Token_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7token\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7token" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Phone_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x89\x8b\xe6\x9c\xba\xe5\x8f\xb7\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x89\x8b\xe6\x9c\xba\xe5\x8f\xb7" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Avatar_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe5\xa4\xb4\xe5\x83\x8f\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe5\xa4\xb4\xe5\x83\x8f" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UserName_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8d\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8d" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InheritCode_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\xbb\xa7\xe6\x89\xbf\xe7\xa0\x81\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\xbb\xa7\xe6\x89\xbf\xe7\xa0\x81" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPasswordExist_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe5\xad\x98\xe5\x9c\xa8\xe6\x89\x8b\xe6\x9c\xba\xe8\xb4\xa6\xe5\x8f\xb7\xe5\xaf\x86\xe7\xa0\x81\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe5\xad\x98\xe5\x9c\xa8\xe6\x89\x8b\xe6\x9c\xba\xe8\xb4\xa6\xe5\x8f\xb7\xe5\xaf\x86\xe7\xa0\x81" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CountryCode_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Thirds_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//  \xe4\xb8\x89\xe6\x96\xb9\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\x97\xe8\xa1\xa8\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe4\xb8\x89\xe6\x96\xb9\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\x97\xe8\xa1\xa8" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//  \xe5\xb9\xb3\xe5\x8f\xb0\xe7\xb1\xbb\xe5\x9e\x8b\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xb9\xb3\xe5\x8f\xb0\xe7\xb1\xbb\xe5\x9e\x8b" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsNewCreate_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba\xe6\x96\xb0\xe7\x94\xa8\xe6\x88\xb7\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba\xe6\x96\xb0\xe7\x94\xa8\xe6\x88\xb7" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Age_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xb9\xb4\xe9\xbe\x84\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xb9\xb4\xe9\xbe\x84" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAdult_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe6\x88\x90\xe5\xb9\xb4\xe4\xba\xba\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe6\x88\x90\xe5\xb9\xb4\xe4\xba\xba" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AgeCountryCode_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_UserId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Token;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Phone;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Avatar;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UserName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_InheritCode;
	static void NewProp_bPasswordExist_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPasswordExist;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CountryCode;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Thirds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Thirds;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Type;
	static void NewProp_bIsNewCreate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsNewCreate;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Age;
	static void NewProp_bIsAdult_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAdult;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AgeCountryCode;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneUserInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_UserId = { "UserId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserInfo, UserId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UserId_MetaData), NewProp_UserId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Token = { "Token", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserInfo, Token), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Token_MetaData), NewProp_Token_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Phone = { "Phone", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserInfo, Phone), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Phone_MetaData), NewProp_Phone_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Avatar = { "Avatar", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserInfo, Avatar), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Avatar_MetaData), NewProp_Avatar_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_UserName = { "UserName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserInfo, UserName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UserName_MetaData), NewProp_UserName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_InheritCode = { "InheritCode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserInfo, InheritCode), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InheritCode_MetaData), NewProp_InheritCode_MetaData) };
void Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bPasswordExist_SetBit(void* Obj)
{
	((FOneUserInfo*)Obj)->bPasswordExist = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bPasswordExist = { "bPasswordExist", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FOneUserInfo), &Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bPasswordExist_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPasswordExist_MetaData), NewProp_bPasswordExist_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_CountryCode = { "CountryCode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserInfo, CountryCode), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CountryCode_MetaData), NewProp_CountryCode_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Thirds_Inner = { "Thirds", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FOneUserThirdInfo, METADATA_PARAMS(0, nullptr) }; // 1657502178
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Thirds = { "Thirds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserInfo, Thirds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Thirds_MetaData), NewProp_Thirds_MetaData) }; // 1657502178
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserInfo, Type), Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Type_MetaData), NewProp_Type_MetaData) }; // 1323311522
void Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsNewCreate_SetBit(void* Obj)
{
	((FOneUserInfo*)Obj)->bIsNewCreate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsNewCreate = { "bIsNewCreate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FOneUserInfo), &Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsNewCreate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsNewCreate_MetaData), NewProp_bIsNewCreate_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Age = { "Age", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserInfo, Age), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Age_MetaData), NewProp_Age_MetaData) };
void Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsAdult_SetBit(void* Obj)
{
	((FOneUserInfo*)Obj)->bIsAdult = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsAdult = { "bIsAdult", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FOneUserInfo), &Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsAdult_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAdult_MetaData), NewProp_bIsAdult_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_AgeCountryCode = { "AgeCountryCode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserInfo, AgeCountryCode), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AgeCountryCode_MetaData), NewProp_AgeCountryCode_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneUserInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_UserId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Token,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Phone,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Avatar,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_UserName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_InheritCode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bPasswordExist,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_CountryCode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Thirds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Thirds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Type_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsNewCreate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Age,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsAdult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_AgeCountryCode,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OneUserInfo",
	Z_Construct_UScriptStruct_FOneUserInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::PropPointers),
	sizeof(FOneUserInfo),
	alignof(FOneUserInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOneUserInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOneUserInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FOneUserInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOneUserInfo.InnerSingleton, Z_Construct_UScriptStruct_FOneUserInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOneUserInfo.InnerSingleton;
}
// ********** End ScriptStruct FOneUserInfo ********************************************************

// ********** Begin ScriptStruct FOneProductInfo ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOneProductInfo;
class UScriptStruct* FOneProductInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOneProductInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOneProductInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneProductInfo, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneProductInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FOneProductInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOneProductInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x94\xaf\xe4\xbb\x98\xe6\xa1\xa3\xe4\xbd\x8d\xe4\xbf\xa1\xe6\x81\xaf\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x94\xaf\xe4\xbb\x98\xe6\xa1\xa3\xe4\xbd\x8d\xe4\xbf\xa1\xe6\x81\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductId_MetaData[] = {
		{ "Category", "OneEngineSDK|ProductInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81ID\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Price_MetaData[] = {
		{ "Category", "OneEngineSDK|ProductInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Currency_MetaData[] = {
		{ "Category", "OneEngineSDK|ProductInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xb4\xa7\xe5\xb8\x81\xe4\xbb\xa3\xe7\xa0\x81\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xb4\xa7\xe5\xb8\x81\xe4\xbb\xa3\xe7\xa0\x81" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SymbolPrice_MetaData[] = {
		{ "Category", "OneEngineSDK|ProductInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xb8\xa6\xe8\xb4\xa7\xe5\xb8\x81\xe7\xac\xa6\xe5\x8f\xb7\xe7\x9a\x84\xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xb8\xa6\xe8\xb4\xa7\xe5\xb8\x81\xe7\xac\xa6\xe5\x8f\xb7\xe7\x9a\x84\xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[] = {
		{ "Category", "OneEngineSDK|ProductInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81\xe6\xa0\x87\xe9\xa2\x98\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81\xe6\xa0\x87\xe9\xa2\x98" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Desc_MetaData[] = {
		{ "Category", "OneEngineSDK|ProductInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81\xe6\x8f\x8f\xe8\xbf\xb0\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81\xe6\x8f\x8f\xe8\xbf\xb0" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Price;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Currency;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SymbolPrice;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Title;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Desc;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneProductInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_ProductId = { "ProductId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneProductInfo, ProductId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductId_MetaData), NewProp_ProductId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Price = { "Price", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneProductInfo, Price), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Price_MetaData), NewProp_Price_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Currency = { "Currency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneProductInfo, Currency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Currency_MetaData), NewProp_Currency_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_SymbolPrice = { "SymbolPrice", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneProductInfo, SymbolPrice), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SymbolPrice_MetaData), NewProp_SymbolPrice_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneProductInfo, Title), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Title_MetaData), NewProp_Title_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Desc = { "Desc", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneProductInfo, Desc), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Desc_MetaData), NewProp_Desc_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneProductInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_ProductId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Price,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Currency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_SymbolPrice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Title,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Desc,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneProductInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneProductInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OneProductInfo",
	Z_Construct_UScriptStruct_FOneProductInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneProductInfo_Statics::PropPointers),
	sizeof(FOneProductInfo),
	alignof(FOneProductInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneProductInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOneProductInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOneProductInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FOneProductInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOneProductInfo.InnerSingleton, Z_Construct_UScriptStruct_FOneProductInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOneProductInfo.InnerSingleton;
}
// ********** End ScriptStruct FOneProductInfo *****************************************************

// ********** Begin ScriptStruct FOnePaymentInfo ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOnePaymentInfo;
class UScriptStruct* FOnePaymentInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePaymentInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOnePaymentInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePaymentInfo, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePaymentInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FOnePaymentInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOnePaymentInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x94\xaf\xe4\xbb\x98\xe5\x8f\x82\xe6\x95\xb0\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x94\xaf\xe4\xbb\x98\xe5\x8f\x82\xe6\x95\xb0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OrderId_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xae\xa2\xe5\x8d\x95\xe5\x8f\xb7\xef\xbc\x8c\xe5\xbf\x85\xe9\xa1\xbb\xe5\x94\xaf\xe4\xb8\x80\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xae\xa2\xe5\x8d\x95\xe5\x8f\xb7\xef\xbc\x8c\xe5\xbf\x85\xe9\xa1\xbb\xe5\x94\xaf\xe4\xb8\x80" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Price_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductName_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81\xe5\x90\x8d\xe7\xa7\xb0\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81\xe5\x90\x8d\xe7\xa7\xb0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameServerId_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8ID\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ServerName_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe5\x90\x8d\xe7\xa7\xb0\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe5\x90\x8d\xe7\xa7\xb0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RoleId_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2ID\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RoleName_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2\xe5\x90\x8d\xe7\xa7\xb0\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2\xe5\x90\x8d\xe7\xa7\xb0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductId_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81ID\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProductCount_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81\xe6\x95\xb0\xe9\x87\x8f\xef\xbc\x8c\xe9\xbb\x98\xe8\xae\xa4""1\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81\xe6\x95\xb0\xe9\x87\x8f\xef\xbc\x8c\xe9\xbb\x98\xe8\xae\xa4""1" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExtInfo_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x89\xa9\xe5\xb1\x95\xe5\xad\x97\xe6\xae\xb5\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe9\x80\x8f\xe4\xbc\xa0\xe4\xbf\xa1\xe6\x81\xaf\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x89\xa9\xe5\xb1\x95\xe5\xad\x97\xe6\xae\xb5\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe9\x80\x8f\xe4\xbc\xa0\xe4\xbf\xa1\xe6\x81\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PaySuccessUrl_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x94\xaf\xe4\xbb\x98\xe6\x88\x90\xe5\x8a\x9f\xe4\xbb\xa5\xe5\x90\x8e\xef\xbc\x8c\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe7\x9a\x84\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xef\xbc\x8c\xe6\xb5\xb7\xe5\xa4\x96\xe6\x97\xb6\xe4\xb8\x8d\xe8\x83\xbd\xe4\xb8\xba\xe7\xa9\xba\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x94\xaf\xe4\xbb\x98\xe6\x88\x90\xe5\x8a\x9f\xe4\xbb\xa5\xe5\x90\x8e\xef\xbc\x8c\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe7\x9a\x84\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xef\xbc\x8c\xe6\xb5\xb7\xe5\xa4\x96\xe6\x97\xb6\xe4\xb8\x8d\xe8\x83\xbd\xe4\xb8\xba\xe7\xa9\xba" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OrderId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Price;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GameServerId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ServerName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RoleId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RoleName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProductCount;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ExtInfo;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PaySuccessUrl;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePaymentInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_OrderId = { "OrderId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePaymentInfo, OrderId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OrderId_MetaData), NewProp_OrderId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_Price = { "Price", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePaymentInfo, Price), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Price_MetaData), NewProp_Price_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductName = { "ProductName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePaymentInfo, ProductName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductName_MetaData), NewProp_ProductName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_GameServerId = { "GameServerId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePaymentInfo, GameServerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameServerId_MetaData), NewProp_GameServerId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ServerName = { "ServerName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePaymentInfo, ServerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ServerName_MetaData), NewProp_ServerName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_RoleId = { "RoleId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePaymentInfo, RoleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RoleId_MetaData), NewProp_RoleId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_RoleName = { "RoleName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePaymentInfo, RoleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RoleName_MetaData), NewProp_RoleName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductId = { "ProductId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePaymentInfo, ProductId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductId_MetaData), NewProp_ProductId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductCount = { "ProductCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePaymentInfo, ProductCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProductCount_MetaData), NewProp_ProductCount_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ExtInfo = { "ExtInfo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePaymentInfo, ExtInfo), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExtInfo_MetaData), NewProp_ExtInfo_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_PaySuccessUrl = { "PaySuccessUrl", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePaymentInfo, PaySuccessUrl), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PaySuccessUrl_MetaData), NewProp_PaySuccessUrl_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_OrderId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_Price,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_GameServerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ServerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_RoleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_RoleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ExtInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_PaySuccessUrl,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OnePaymentInfo",
	Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::PropPointers),
	sizeof(FOnePaymentInfo),
	alignof(FOnePaymentInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOnePaymentInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePaymentInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOnePaymentInfo.InnerSingleton, Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOnePaymentInfo.InnerSingleton;
}
// ********** End ScriptStruct FOnePaymentInfo *****************************************************

// ********** Begin ScriptStruct FOneRoleInfo ******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOneRoleInfo;
class UScriptStruct* FOneRoleInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOneRoleInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOneRoleInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneRoleInfo, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneRoleInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FOneRoleInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOneRoleInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2\xe4\xbf\xa1\xe6\x81\xaf\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2\xe4\xbf\xa1\xe6\x81\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RoleId_MetaData[] = {
		{ "Category", "OneEngineSDK|RoleInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RoleName_MetaData[] = {
		{ "Category", "OneEngineSDK|RoleInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Vip_MetaData[] = {
		{ "Category", "OneEngineSDK|RoleInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Level_MetaData[] = {
		{ "Category", "OneEngineSDK|RoleInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ServerId_MetaData[] = {
		{ "Category", "OneEngineSDK|RoleInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ServerName_MetaData[] = {
		{ "Category", "OneEngineSDK|RoleInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CombatValue_MetaData[] = {
		{ "Category", "OneEngineSDK|RoleInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RoleId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RoleName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Vip;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Level;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ServerId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ServerName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CombatValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneRoleInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_RoleId = { "RoleId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneRoleInfo, RoleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RoleId_MetaData), NewProp_RoleId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_RoleName = { "RoleName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneRoleInfo, RoleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RoleName_MetaData), NewProp_RoleName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_Vip = { "Vip", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneRoleInfo, Vip), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Vip_MetaData), NewProp_Vip_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneRoleInfo, Level), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Level_MetaData), NewProp_Level_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_ServerId = { "ServerId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneRoleInfo, ServerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ServerId_MetaData), NewProp_ServerId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_ServerName = { "ServerName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneRoleInfo, ServerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ServerName_MetaData), NewProp_ServerName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_CombatValue = { "CombatValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneRoleInfo, CombatValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CombatValue_MetaData), NewProp_CombatValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneRoleInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_RoleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_RoleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_Vip,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_Level,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_ServerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_ServerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_CombatValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OneRoleInfo",
	Z_Construct_UScriptStruct_FOneRoleInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::PropPointers),
	sizeof(FOneRoleInfo),
	alignof(FOneRoleInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOneRoleInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOneRoleInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FOneRoleInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOneRoleInfo.InnerSingleton, Z_Construct_UScriptStruct_FOneRoleInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOneRoleInfo.InnerSingleton;
}
// ********** End ScriptStruct FOneRoleInfo ********************************************************

// ********** Begin Enum EOneResEventState *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EOneResEventState;
static UEnum* EOneResEventState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EOneResEventState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EOneResEventState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneResEventState, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneResEventState"));
	}
	return Z_Registration_Info_UEnum_EOneResEventState.OuterSingleton;
}
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneResEventState>()
{
	return EOneResEventState_StaticEnum();
}
struct Z_Construct_UEnum_OneEngineSDK_EOneResEventState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Begin.Name", "EOneResEventState::Begin" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xb5\x84\xe6\xba\x90\xe6\x89\x93\xe7\x82\xb9\xe7\xb1\xbb\xe5\x9e\x8b\n" },
#endif
		{ "Failed.Name", "EOneResEventState::Failed" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "Success.Name", "EOneResEventState::Success" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xb5\x84\xe6\xba\x90\xe6\x89\x93\xe7\x82\xb9\xe7\xb1\xbb\xe5\x9e\x8b" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EOneResEventState::Begin", (int64)EOneResEventState::Begin },
		{ "EOneResEventState::Success", (int64)EOneResEventState::Success },
		{ "EOneResEventState::Failed", (int64)EOneResEventState::Failed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_OneEngineSDK_EOneResEventState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	"EOneResEventState",
	"EOneResEventState",
	Z_Construct_UEnum_OneEngineSDK_EOneResEventState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneResEventState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneResEventState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_OneEngineSDK_EOneResEventState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_OneEngineSDK_EOneResEventState()
{
	if (!Z_Registration_Info_UEnum_EOneResEventState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EOneResEventState.InnerSingleton, Z_Construct_UEnum_OneEngineSDK_EOneResEventState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EOneResEventState.InnerSingleton;
}
// ********** End Enum EOneResEventState ***********************************************************

// ********** Begin ScriptStruct FOneUserLocationInfo **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOneUserLocationInfo;
class UScriptStruct* FOneUserLocationInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOneUserLocationInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOneUserLocationInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneUserLocationInfo, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneUserLocationInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FOneUserLocationInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe4\xbd\x8d\xe7\xbd\xae\xe4\xbf\xa1\xe6\x81\xaf\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe4\xbd\x8d\xe7\xbd\xae\xe4\xbf\xa1\xe6\x81\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CountryAbbr_MetaData[] = {
		{ "Category", "OneEngineSDK|LocationInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//\xe6\x9c\x8d\xe5\x8a\xa1\xe7\xab\xaf\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x9c\x8d\xe5\x8a\xa1\xe7\xab\xaf\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Country_MetaData[] = {
		{ "Category", "OneEngineSDK|LocationInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//\xe5\x9b\xbd\xe5\xae\xb6\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x9b\xbd\xe5\xae\xb6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Province_MetaData[] = {
		{ "Category", "OneEngineSDK|LocationInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//\xe7\x9c\x81\xe4\xbb\xbd\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x9c\x81\xe4\xbb\xbd" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_City_MetaData[] = {
		{ "Category", "OneEngineSDK|LocationInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//\xe5\x9f\x8e\xe5\xb8\x82\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x9f\x8e\xe5\xb8\x82" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CountryCode_MetaData[] = {
		{ "Category", "OneEngineSDK|LocationInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//\xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IP_MetaData[] = {
		{ "Category", "OneEngineSDK|LocationInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//ip\xe5\x9c\xb0\xe5\x9d\x80\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ip\xe5\x9c\xb0\xe5\x9d\x80" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CountryAbbr;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Country;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Province;
	static const UECodeGen_Private::FStrPropertyParams NewProp_City;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CountryCode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_IP;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneUserLocationInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_CountryAbbr = { "CountryAbbr", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserLocationInfo, CountryAbbr), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CountryAbbr_MetaData), NewProp_CountryAbbr_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_Country = { "Country", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserLocationInfo, Country), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Country_MetaData), NewProp_Country_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_Province = { "Province", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserLocationInfo, Province), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Province_MetaData), NewProp_Province_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_City = { "City", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserLocationInfo, City), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_City_MetaData), NewProp_City_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_CountryCode = { "CountryCode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserLocationInfo, CountryCode), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CountryCode_MetaData), NewProp_CountryCode_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_IP = { "IP", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneUserLocationInfo, IP), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IP_MetaData), NewProp_IP_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_CountryAbbr,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_Country,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_Province,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_City,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_CountryCode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_IP,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OneUserLocationInfo",
	Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::PropPointers),
	sizeof(FOneUserLocationInfo),
	alignof(FOneUserLocationInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOneUserLocationInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FOneUserLocationInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOneUserLocationInfo.InnerSingleton, Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOneUserLocationInfo.InnerSingleton;
}
// ********** End ScriptStruct FOneUserLocationInfo ************************************************

// ********** Begin ScriptStruct FOneDeviceInfo ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOneDeviceInfo;
class UScriptStruct* FOneDeviceInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOneDeviceInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOneDeviceInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneDeviceInfo, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneDeviceInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FOneDeviceInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOneDeviceInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xae\xbe\xe5\xa4\x87\xe4\xbf\xa1\xe6\x81\xaf\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xae\xbe\xe5\xa4\x87\xe4\xbf\xa1\xe6\x81\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeviceId_MetaData[] = {
		{ "Category", "OneEngineSDK|DeviceInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//\xe8\xae\xbe\xe5\xa4\x87\xe5\x94\xaf\xe4\xb8\x80\xe6\xa0\x87\xe8\xaf\x86\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xae\xbe\xe5\xa4\x87\xe5\x94\xaf\xe4\xb8\x80\xe6\xa0\x87\xe8\xaf\x86" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeviceSys_MetaData[] = {
		{ "Category", "OneEngineSDK|DeviceInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//\xe8\xae\xbe\xe5\xa4\x87\xe6\x93\x8d\xe4\xbd\x9c\xe7\xb3\xbb\xe7\xbb\x9f\xe7\x89\x88\xe6\x9c\xac\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xae\xbe\xe5\xa4\x87\xe6\x93\x8d\xe4\xbd\x9c\xe7\xb3\xbb\xe7\xbb\x9f\xe7\x89\x88\xe6\x9c\xac" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Ext_MetaData[] = {
		{ "Category", "OneEngineSDK|DeviceInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//\xe8\xae\xbe\xe5\xa4\x87\xe5\x85\xb6\xe4\xbb\x96\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe6\xb8\xb8\xe6\x88\x8f\xe6\x8c\x89\xe7\x85\xa7\xe9\x9c\x80\xe8\xa6\x81\xe8\xbf\x9b\xe8\xa1\x8c\xe8\x8e\xb7\xe5\x8f\x96\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xae\xbe\xe5\xa4\x87\xe5\x85\xb6\xe4\xbb\x96\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe6\xb8\xb8\xe6\x88\x8f\xe6\x8c\x89\xe7\x85\xa7\xe9\x9c\x80\xe8\xa6\x81\xe8\xbf\x9b\xe8\xa1\x8c\xe8\x8e\xb7\xe5\x8f\x96" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DeviceId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DeviceSys;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Ext_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Ext_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Ext;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneDeviceInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_DeviceId = { "DeviceId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneDeviceInfo, DeviceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeviceId_MetaData), NewProp_DeviceId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_DeviceSys = { "DeviceSys", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneDeviceInfo, DeviceSys), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeviceSys_MetaData), NewProp_DeviceSys_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_Ext_ValueProp = { "Ext", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_Ext_Key_KeyProp = { "Ext_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_Ext = { "Ext", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneDeviceInfo, Ext), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Ext_MetaData), NewProp_Ext_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_DeviceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_DeviceSys,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_Ext_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_Ext_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_Ext,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OneDeviceInfo",
	Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::PropPointers),
	sizeof(FOneDeviceInfo),
	alignof(FOneDeviceInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOneDeviceInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FOneDeviceInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOneDeviceInfo.InnerSingleton, Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOneDeviceInfo.InnerSingleton;
}
// ********** End ScriptStruct FOneDeviceInfo ******************************************************

// ********** Begin ScriptStruct FOneAntiAddictionInfo *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOneAntiAddictionInfo;
class UScriptStruct* FOneAntiAddictionInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOneAntiAddictionInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOneAntiAddictionInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneAntiAddictionInfo, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneAntiAddictionInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FOneAntiAddictionInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//\xe9\x98\xb2\xe6\xb2\x89\xe8\xbf\xb7\xe4\xbf\xa1\xe6\x81\xaf\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe9\x98\xb2\xe6\xb2\x89\xe8\xbf\xb7\xe4\xbf\xa1\xe6\x81\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AppID_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xba\x94\xe7\x94\xa8ID\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xba\x94\xe7\x94\xa8ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UserId_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7ID\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Status_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe9\x98\xb2\xe6\xb2\x89\xe8\xbf\xb7\xe7\x8a\xb6\xe6\x80\x81: 0 \xe6\xad\xa3\xe5\xb8\xb8\xe5\x8f\xaf\xe8\xbf\x9b\xe5\x85\xa5\xe6\xb8\xb8\xe6\x88\x8f; 1 \xe4\xb8\x8d\xe5\x8f\xaf\xe8\xbf\x9b\xe5\x85\xa5\xe6\xb8\xb8\xe6\x88\x8f\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe9\x98\xb2\xe6\xb2\x89\xe8\xbf\xb7\xe7\x8a\xb6\xe6\x80\x81: 0 \xe6\xad\xa3\xe5\xb8\xb8\xe5\x8f\xaf\xe8\xbf\x9b\xe5\x85\xa5\xe6\xb8\xb8\xe6\x88\x8f; 1 \xe4\xb8\x8d\xe5\x8f\xaf\xe8\xbf\x9b\xe5\x85\xa5\xe6\xb8\xb8\xe6\x88\x8f" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeartbeatInterval_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xbf\x83\xe8\xb7\xb3\xe9\x97\xb4\xe9\x9a\x94\xef\xbc\x8c\xe5\x8d\x95\xe4\xbd\x8d:\xe7\xa7\x92\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xbf\x83\xe8\xb7\xb3\xe9\x97\xb4\xe9\x9a\x94\xef\xbc\x8c\xe5\x8d\x95\xe4\xbd\x8d:\xe7\xa7\x92" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BannedType_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xb0\x81\xe7\xa6\x81\xe7\xb1\xbb\xe5\x9e\x8b: 1 \xe5\xb0\x81\xe7\xa6\x81\xef\xbc\x9b""2 \xe5\xae\xb5\xe7\xa6\x81\xef\xbc\x9b""3 \xe5\x8d\x95\xe6\xac\xa1\xe5\x9c\xa8\xe7\xba\xbf\xe6\x97\xb6\xe9\x95\xbf\xe8\xb6\x85\xe9\x99\x90\xef\xbc\x9b""4 \xe5\xbd\x93\xe6\x97\xa5\xe7\xb4\xaf\xe8\xae\xa1\xe5\x9c\xa8\xe7\xba\xbf\xe6\x97\xb6\xe9\x95\xbf\xe8\xb6\x85\xe9\x99\x90 5\xe6\x98\xaf\xe6\x9c\xaa\xe5\xae\x9e\xe5\x90\x8d\xe4\xbd\x93\xe9\xaa\x8c\xe6\x97\xb6\xe9\x97\xb4\xe5\x88\xb0\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xb0\x81\xe7\xa6\x81\xe7\xb1\xbb\xe5\x9e\x8b: 1 \xe5\xb0\x81\xe7\xa6\x81\xef\xbc\x9b""2 \xe5\xae\xb5\xe7\xa6\x81\xef\xbc\x9b""3 \xe5\x8d\x95\xe6\xac\xa1\xe5\x9c\xa8\xe7\xba\xbf\xe6\x97\xb6\xe9\x95\xbf\xe8\xb6\x85\xe9\x99\x90\xef\xbc\x9b""4 \xe5\xbd\x93\xe6\x97\xa5\xe7\xb4\xaf\xe8\xae\xa1\xe5\x9c\xa8\xe7\xba\xbf\xe6\x97\xb6\xe9\x95\xbf\xe8\xb6\x85\xe9\x99\x90 5\xe6\x98\xaf\xe6\x9c\xaa\xe5\xae\x9e\xe5\x90\x8d\xe4\xbd\x93\xe9\xaa\x8c\xe6\x97\xb6\xe9\x97\xb4\xe5\x88\xb0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BannedReason_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\xa6\x81\xe7\x8e\xa9\xe7\x90\x86\xe7\x94\xb1\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe8\x87\xaa\xe8\xa1\x8c\xe5\xae\x9e\xe7\x8e\xb0\xe6\x8f\x90\xe9\x86\x92\xe6\x88\x96\xe4\xb8\x8b\xe7\xba\xbf\xe7\x9a\x84UI\xef\xbc\x8c\xe8\xaf\xb7\xe8\xaf\xbb\xe5\x8f\x96\xe6\x9c\xac\xe5\xad\x97\xe6\xae\xb5\xe7\x94\xa8\xe4\xba\x8e\xe6\x98\xbe\xe7\xa4\xba\xe3\x80\x82\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\xa6\x81\xe7\x8e\xa9\xe7\x90\x86\xe7\x94\xb1\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe8\x87\xaa\xe8\xa1\x8c\xe5\xae\x9e\xe7\x8e\xb0\xe6\x8f\x90\xe9\x86\x92\xe6\x88\x96\xe4\xb8\x8b\xe7\xba\xbf\xe7\x9a\x84UI\xef\xbc\x8c\xe8\xaf\xb7\xe8\xaf\xbb\xe5\x8f\x96\xe6\x9c\xac\xe5\xad\x97\xe6\xae\xb5\xe7\x94\xa8\xe4\xba\x8e\xe6\x98\xbe\xe7\xa4\xba\xe3\x80\x82" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BreakNotice_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe4\xbc\x91\xe6\x81\xaf\xe9\x80\x9a\xe7\x9f\xa5\xef\xbc\x8c\xe4\xbb\x85\xe6\x8f\x90\xe9\x86\x92\xef\xbc\x8c\xe4\xb8\x8d\xe8\xa6\x81\xe6\xb1\x82\xe7\x99\xbb\xe5\x87\xba\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe4\xbc\x91\xe6\x81\xaf\xe9\x80\x9a\xe7\x9f\xa5\xef\xbc\x8c\xe4\xbb\x85\xe6\x8f\x90\xe9\x86\x92\xef\xbc\x8c\xe4\xb8\x8d\xe8\xa6\x81\xe6\xb1\x82\xe7\x99\xbb\xe5\x87\xba" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Realuser_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xae\x9e\xe5\x90\x8d\xe7\xb1\xbb\xe5\x9e\x8b: 0 \xe6\x9c\xaa\xe5\xae\x9e\xe5\x90\x8d; 1 \xe8\xba\xab\xe4\xbb\xbd\xe8\xaf\x81\xe5\xae\x9e\xe5\x90\x8d; 2 \xe6\x8a\xa4\xe7\x85\xa7\xe5\xae\x9e\xe5\x90\x8d\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xae\x9e\xe5\x90\x8d\xe7\xb1\xbb\xe5\x9e\x8b: 0 \xe6\x9c\xaa\xe5\xae\x9e\xe5\x90\x8d; 1 \xe8\xba\xab\xe4\xbb\xbd\xe8\xaf\x81\xe5\xae\x9e\xe5\x90\x8d; 2 \xe6\x8a\xa4\xe7\x85\xa7\xe5\xae\x9e\xe5\x90\x8d" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CivicType_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x85\xac\xe6\xb0\x91\xe7\xb1\xbb\xe5\x9e\x8b: 0 \xe6\x9c\xaa\xe5\xae\x9e\xe5\x90\x8d; 1 \xe4\xb8\xad\xe5\x9b\xbd\xe5\xa4\xa7\xe9\x99\x86; 2 \xe6\xb5\xb7\xe5\xa4\x96\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x85\xac\xe6\xb0\x91\xe7\xb1\xbb\xe5\x9e\x8b: 0 \xe6\x9c\xaa\xe5\xae\x9e\xe5\x90\x8d; 1 \xe4\xb8\xad\xe5\x9b\xbd\xe5\xa4\xa7\xe9\x99\x86; 2 \xe6\xb5\xb7\xe5\xa4\x96" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Age_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xb9\xb4\xe9\xbe\x84: 0 \xe6\x9c\xaa\xe7\x9f\xa5; \xe5\x85\xb6\xe5\xae\x83\xe6\x95\xb0\xe5\x80\xbc\xe4\xb8\xba\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe5\xb9\xb4\xe9\xbe\x84\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xb9\xb4\xe9\xbe\x84: 0 \xe6\x9c\xaa\xe7\x9f\xa5; \xe5\x85\xb6\xe5\xae\x83\xe6\x95\xb0\xe5\x80\xbc\xe4\xb8\xba\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe5\xb9\xb4\xe9\xbe\x84" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Gender_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x80\xa7\xe5\x88\xab: 0 \xe6\x9c\xaa\xe7\x9f\xa5; 1 \xe7\x94\xb7; 2 \xe5\xa5\xb3\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x80\xa7\xe5\x88\xab: 0 \xe6\x9c\xaa\xe7\x9f\xa5; 1 \xe7\x94\xb7; 2 \xe5\xa5\xb3" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccountType_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xb4\xa6\xe5\x8f\xb7\xe7\xb1\xbb\xe5\x9e\x8b: 0 \xe6\x9c\xaa\xe6\x88\x90\xe5\xb9\xb4;1 \xe6\x88\x90\xe5\xb9\xb4\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xb4\xa6\xe5\x8f\xb7\xe7\xb1\xbb\xe5\x9e\x8b: 0 \xe6\x9c\xaa\xe6\x88\x90\xe5\xb9\xb4;1 \xe6\x88\x90\xe5\xb9\xb4" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequestIp_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xaf\xb7\xe6\xb1\x82IP\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xaf\xb7\xe6\xb1\x82IP" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DayOnlineDuration_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xbd\x93\xe6\x97\xa5\xe5\x9c\xa8\xe7\xba\xbf\xe6\x97\xb6\xe9\x95\xbf\xef\xbc\x8c\xe5\x8d\x95\xe4\xbd\x8d\xef\xbc\x9a\xe5\x88\x86\xe9\x92\x9f\xe3\x80\x82\xe5\x85\xb3\xe9\x97\xad\xe9\x98\xb2\xe6\xb2\x89\xe8\xbf\xb7\xe6\x97\xb6\xe8\xaf\xa5\xe9\xa1\xb9\xe4\xb8\xba 0 \n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xbd\x93\xe6\x97\xa5\xe5\x9c\xa8\xe7\xba\xbf\xe6\x97\xb6\xe9\x95\xbf\xef\xbc\x8c\xe5\x8d\x95\xe4\xbd\x8d\xef\xbc\x9a\xe5\x88\x86\xe9\x92\x9f\xe3\x80\x82\xe5\x85\xb3\xe9\x97\xad\xe9\x98\xb2\xe6\xb2\x89\xe8\xbf\xb7\xe6\x97\xb6\xe8\xaf\xa5\xe9\xa1\xb9\xe4\xb8\xba 0" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AppID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UserId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Status;
	static const UECodeGen_Private::FIntPropertyParams NewProp_HeartbeatInterval;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BannedType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BannedReason;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BreakNotice;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Realuser;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CivicType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Age;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Gender;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AccountType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequestIp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DayOnlineDuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneAntiAddictionInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_AppID = { "AppID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, AppID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AppID_MetaData), NewProp_AppID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_UserId = { "UserId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, UserId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UserId_MetaData), NewProp_UserId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Status = { "Status", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, Status), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Status_MetaData), NewProp_Status_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_HeartbeatInterval = { "HeartbeatInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, HeartbeatInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeartbeatInterval_MetaData), NewProp_HeartbeatInterval_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BannedType = { "BannedType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, BannedType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BannedType_MetaData), NewProp_BannedType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BannedReason = { "BannedReason", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, BannedReason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BannedReason_MetaData), NewProp_BannedReason_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BreakNotice = { "BreakNotice", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, BreakNotice), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BreakNotice_MetaData), NewProp_BreakNotice_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Realuser = { "Realuser", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, Realuser), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Realuser_MetaData), NewProp_Realuser_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_CivicType = { "CivicType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, CivicType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CivicType_MetaData), NewProp_CivicType_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Age = { "Age", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, Age), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Age_MetaData), NewProp_Age_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Gender = { "Gender", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, Gender), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Gender_MetaData), NewProp_Gender_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_AccountType = { "AccountType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, AccountType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccountType_MetaData), NewProp_AccountType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_RequestIp = { "RequestIp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, RequestIp), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequestIp_MetaData), NewProp_RequestIp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_DayOnlineDuration = { "DayOnlineDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, DayOnlineDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DayOnlineDuration_MetaData), NewProp_DayOnlineDuration_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_AppID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_UserId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Status,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_HeartbeatInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BannedType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BannedReason,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BreakNotice,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Realuser,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_CivicType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Age,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Gender,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_AccountType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_RequestIp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_DayOnlineDuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OneAntiAddictionInfo",
	Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::PropPointers),
	sizeof(FOneAntiAddictionInfo),
	alignof(FOneAntiAddictionInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOneAntiAddictionInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FOneAntiAddictionInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOneAntiAddictionInfo.InnerSingleton, Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOneAntiAddictionInfo.InnerSingleton;
}
// ********** End ScriptStruct FOneAntiAddictionInfo ***********************************************

// ********** Begin ScriptStruct FOneURCRoleInfo ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOneURCRoleInfo;
class UScriptStruct* FOneURCRoleInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOneURCRoleInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOneURCRoleInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneURCRoleInfo, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneURCRoleInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FOneURCRoleInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x85\x91\xe6\x8d\xa2\xe7\xa0\x81\xe6\x9c\x8d\xe5\x8a\xa1\xe8\x8e\xb7\xe5\x8f\x96\xe7\x94\xa8\xe6\x88\xb7\xe8\xa7\x92\xe8\x89\xb2\xe5\x88\x97\xe8\xa1\xa8\xe6\x95\xb0\xe6\x8d\xae\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x85\x91\xe6\x8d\xa2\xe7\xa0\x81\xe6\x9c\x8d\xe5\x8a\xa1\xe8\x8e\xb7\xe5\x8f\x96\xe7\x94\xa8\xe6\x88\xb7\xe8\xa7\x92\xe8\x89\xb2\xe5\x88\x97\xe8\xa1\xa8\xe6\x95\xb0\xe6\x8d\xae" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UserId_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7ID\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RoleId_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2ID\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RoleName_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2\xe5\x90\x8d\xe7\xa7\xb0\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2\xe5\x90\x8d\xe7\xa7\xb0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Level_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2\xe7\xad\x89\xe7\xba\xa7\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2\xe7\xad\x89\xe7\xba\xa7" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ServerId_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8ID\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ServerName_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe5\x90\x8d\xe7\xa7\xb0\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe5\x90\x8d\xe7\xa7\xb0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Gender_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2\xe6\x80\xa7\xe5\x88\xab\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2\xe6\x80\xa7\xe5\x88\xab" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Occupation_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2\xe8\x81\x8c\xe4\xb8\x9a\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2\xe8\x81\x8c\xe4\xb8\x9a" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastLoginTime_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x9c\x80\xe5\x90\x8e\xe4\xb8\x80\xe6\xac\xa1\xe7\x99\xbb\xe5\xbd\x95\xe7\x9a\x84\xe6\x97\xb6\xe9\x97\xb4\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x9c\x80\xe5\x90\x8e\xe4\xb8\x80\xe6\xac\xa1\xe7\x99\xbb\xe5\xbd\x95\xe7\x9a\x84\xe6\x97\xb6\xe9\x97\xb4" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_UserId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RoleId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RoleName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Level;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ServerId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ServerName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Gender;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Occupation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LastLoginTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneURCRoleInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_UserId = { "UserId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneURCRoleInfo, UserId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UserId_MetaData), NewProp_UserId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_RoleId = { "RoleId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneURCRoleInfo, RoleId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RoleId_MetaData), NewProp_RoleId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_RoleName = { "RoleName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneURCRoleInfo, RoleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RoleName_MetaData), NewProp_RoleName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneURCRoleInfo, Level), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Level_MetaData), NewProp_Level_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_ServerId = { "ServerId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneURCRoleInfo, ServerId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ServerId_MetaData), NewProp_ServerId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_ServerName = { "ServerName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneURCRoleInfo, ServerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ServerName_MetaData), NewProp_ServerName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Gender = { "Gender", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneURCRoleInfo, Gender), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Gender_MetaData), NewProp_Gender_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Occupation = { "Occupation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneURCRoleInfo, Occupation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Occupation_MetaData), NewProp_Occupation_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_LastLoginTime = { "LastLoginTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneURCRoleInfo, LastLoginTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastLoginTime_MetaData), NewProp_LastLoginTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_UserId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_RoleId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_RoleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Level,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_ServerId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_ServerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Gender,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Occupation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_LastLoginTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OneURCRoleInfo",
	Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::PropPointers),
	sizeof(FOneURCRoleInfo),
	alignof(FOneURCRoleInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOneURCRoleInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FOneURCRoleInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOneURCRoleInfo.InnerSingleton, Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOneURCRoleInfo.InnerSingleton;
}
// ********** End ScriptStruct FOneURCRoleInfo *****************************************************

// ********** Begin Enum EOnePermissionType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EOnePermissionType;
static UEnum* EOnePermissionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EOnePermissionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EOnePermissionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOnePermissionType, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOnePermissionType"));
	}
	return Z_Registration_Info_UEnum_EOnePermissionType.OuterSingleton;
}
template<> ONEENGINESDK_API UEnum* StaticEnum<EOnePermissionType>()
{
	return EOnePermissionType_StaticEnum();
}
struct Z_Construct_UEnum_OneEngineSDK_EOnePermissionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "ATTTrack.Comment", "//\xe9\x80\x9a\xe7\x9f\xa5\xe6\x9d\x83\xe9\x99\x90\n" },
		{ "ATTTrack.Name", "EOnePermissionType::ATTTrack" },
		{ "ATTTrack.ToolTip", "\xe9\x80\x9a\xe7\x9f\xa5\xe6\x9d\x83\xe9\x99\x90" },
		{ "BlueprintType", "true" },
		{ "BodySensors.Comment", "//\xe8\xaf\xbb\xe5\x8f\x96\xe6\x97\xa5\xe5\x8e\x86\n" },
		{ "BodySensors.Name", "EOnePermissionType::BodySensors" },
		{ "BodySensors.ToolTip", "\xe8\xaf\xbb\xe5\x8f\x96\xe6\x97\xa5\xe5\x8e\x86" },
		{ "CallPhone.Comment", "//GPS\xe5\xae\x9a\xe4\xbd\x8d\n" },
		{ "CallPhone.Name", "EOnePermissionType::CallPhone" },
		{ "CallPhone.ToolTip", "GPS\xe5\xae\x9a\xe4\xbd\x8d" },
		{ "Camera.Comment", "//\xe5\x86\x99\xe5\x85\xa5\xe5\xad\x98\xe5\x82\xa8\xe5\x8d\xa1\n" },
		{ "Camera.Name", "EOnePermissionType::Camera" },
		{ "Camera.ToolTip", "\xe5\x86\x99\xe5\x85\xa5\xe5\xad\x98\xe5\x82\xa8\xe5\x8d\xa1" },
		{ "Clipboard.Name", "EOnePermissionType::Clipboard" },
		{ "CoarseLocation.Comment", "//\xe9\xba\xa6\xe5\x85\x8b\xe9\xa3\x8e\xe9\x9f\xb3\xe9\xa2\x91\n" },
		{ "CoarseLocation.Name", "EOnePermissionType::CoarseLocation" },
		{ "CoarseLocation.ToolTip", "\xe9\xba\xa6\xe5\x85\x8b\xe9\xa3\x8e\xe9\x9f\xb3\xe9\xa2\x91" },
		{ "FineLocation.Comment", "//\xe7\xbd\x91\xe7\xbb\x9c\xe5\xae\x9a\xe4\xbd\x8d\n" },
		{ "FineLocation.Name", "EOnePermissionType::FineLocation" },
		{ "FineLocation.ToolTip", "\xe7\xbd\x91\xe7\xbb\x9c\xe5\xae\x9a\xe4\xbd\x8d" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "Notification.Comment", "//\xe4\xbc\xa0\xe6\x84\x9f\xe5\x99\xa8\n" },
		{ "Notification.Name", "EOnePermissionType::Notification" },
		{ "Notification.ToolTip", "\xe4\xbc\xa0\xe6\x84\x9f\xe5\x99\xa8" },
		{ "ReadCalendar.Comment", "//\xe8\xaf\xbb\xe5\x8f\x96\xe7\x9f\xad\xe4\xbf\xa1\n" },
		{ "ReadCalendar.Name", "EOnePermissionType::ReadCalendar" },
		{ "ReadCalendar.ToolTip", "\xe8\xaf\xbb\xe5\x8f\x96\xe7\x9f\xad\xe4\xbf\xa1" },
		{ "ReadContacts.Comment", "//\xe6\x89\x93\xe7\x94\xb5\xe8\xaf\x9d\n" },
		{ "ReadContacts.Name", "EOnePermissionType::ReadContacts" },
		{ "ReadContacts.ToolTip", "\xe6\x89\x93\xe7\x94\xb5\xe8\xaf\x9d" },
		{ "ReadExternalStorage.Comment", "//\xe5\x89\xaa\xe8\xb4\xb4\xe6\x9d\xbf\n" },
		{ "ReadExternalStorage.Name", "EOnePermissionType::ReadExternalStorage" },
		{ "ReadExternalStorage.ToolTip", "\xe5\x89\xaa\xe8\xb4\xb4\xe6\x9d\xbf" },
		{ "ReadSms.Comment", "//\xe8\xaf\xbb\xe5\x8f\x96\xe9\x80\x9a\xe8\xae\xaf\xe5\xbd\x95\n" },
		{ "ReadSms.Name", "EOnePermissionType::ReadSms" },
		{ "ReadSms.ToolTip", "\xe8\xaf\xbb\xe5\x8f\x96\xe9\x80\x9a\xe8\xae\xaf\xe5\xbd\x95" },
		{ "RecordAudio.Comment", "//\xe7\x9b\xb8\xe6\x9c\xba\n" },
		{ "RecordAudio.Name", "EOnePermissionType::RecordAudio" },
		{ "RecordAudio.ToolTip", "\xe7\x9b\xb8\xe6\x9c\xba" },
		{ "WriteExternalStorage.Comment", "//\xe8\xaf\xbb\xe5\x8f\x96\xe5\xad\x98\xe5\x82\xa8\xe5\x8d\xa1\n" },
		{ "WriteExternalStorage.Name", "EOnePermissionType::WriteExternalStorage" },
		{ "WriteExternalStorage.ToolTip", "\xe8\xaf\xbb\xe5\x8f\x96\xe5\xad\x98\xe5\x82\xa8\xe5\x8d\xa1" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EOnePermissionType::Clipboard", (int64)EOnePermissionType::Clipboard },
		{ "EOnePermissionType::ReadExternalStorage", (int64)EOnePermissionType::ReadExternalStorage },
		{ "EOnePermissionType::WriteExternalStorage", (int64)EOnePermissionType::WriteExternalStorage },
		{ "EOnePermissionType::Camera", (int64)EOnePermissionType::Camera },
		{ "EOnePermissionType::RecordAudio", (int64)EOnePermissionType::RecordAudio },
		{ "EOnePermissionType::CoarseLocation", (int64)EOnePermissionType::CoarseLocation },
		{ "EOnePermissionType::FineLocation", (int64)EOnePermissionType::FineLocation },
		{ "EOnePermissionType::CallPhone", (int64)EOnePermissionType::CallPhone },
		{ "EOnePermissionType::ReadContacts", (int64)EOnePermissionType::ReadContacts },
		{ "EOnePermissionType::ReadSms", (int64)EOnePermissionType::ReadSms },
		{ "EOnePermissionType::ReadCalendar", (int64)EOnePermissionType::ReadCalendar },
		{ "EOnePermissionType::BodySensors", (int64)EOnePermissionType::BodySensors },
		{ "EOnePermissionType::Notification", (int64)EOnePermissionType::Notification },
		{ "EOnePermissionType::ATTTrack", (int64)EOnePermissionType::ATTTrack },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_OneEngineSDK_EOnePermissionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	"EOnePermissionType",
	"EOnePermissionType",
	Z_Construct_UEnum_OneEngineSDK_EOnePermissionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOnePermissionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOnePermissionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_OneEngineSDK_EOnePermissionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_OneEngineSDK_EOnePermissionType()
{
	if (!Z_Registration_Info_UEnum_EOnePermissionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EOnePermissionType.InnerSingleton, Z_Construct_UEnum_OneEngineSDK_EOnePermissionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EOnePermissionType.InnerSingleton;
}
// ********** End Enum EOnePermissionType **********************************************************

// ********** Begin ScriptStruct FOnePermissionInfo ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOnePermissionInfo;
class UScriptStruct* FOnePermissionInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePermissionInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOnePermissionInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePermissionInfo, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePermissionInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FOnePermissionInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOnePermissionInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PermissionType_MetaData[] = {
		{ "Category", "OneEngineSDK" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x9d\x83\xe9\x99\x90\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x9d\x83\xe9\x99\x90" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[] = {
		{ "Category", "OneEngineSDK" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x9d\x83\xe9\x99\x90\xe5\x90\x8d\xe7\xa7\xb0\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x9d\x83\xe9\x99\x90\xe5\x90\x8d\xe7\xa7\xb0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Desc_MetaData[] = {
		{ "Category", "OneEngineSDK" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x9d\x83\xe9\x99\x90\xe6\x8f\x8f\xe8\xbf\xb0\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x9d\x83\xe9\x99\x90\xe6\x8f\x8f\xe8\xbf\xb0" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_PermissionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PermissionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Title;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Desc;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePermissionInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_PermissionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_PermissionType = { "PermissionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePermissionInfo, PermissionType), Z_Construct_UEnum_OneEngineSDK_EOnePermissionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PermissionType_MetaData), NewProp_PermissionType_MetaData) }; // 784157622
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePermissionInfo, Title), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Title_MetaData), NewProp_Title_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_Desc = { "Desc", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePermissionInfo, Desc), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Desc_MetaData), NewProp_Desc_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_PermissionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_PermissionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_Title,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_Desc,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OnePermissionInfo",
	Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::PropPointers),
	sizeof(FOnePermissionInfo),
	alignof(FOnePermissionInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOnePermissionInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePermissionInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOnePermissionInfo.InnerSingleton, Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOnePermissionInfo.InnerSingleton;
}
// ********** End ScriptStruct FOnePermissionInfo **************************************************

// ********** Begin Enum EOneAIHelpType ************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EOneAIHelpType;
static UEnum* EOneAIHelpType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EOneAIHelpType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EOneAIHelpType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneAIHelpType, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneAIHelpType"));
	}
	return Z_Registration_Info_UEnum_EOneAIHelpType.OuterSingleton;
}
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneAIHelpType>()
{
	return EOneAIHelpType_StaticEnum();
}
struct Z_Construct_UEnum_OneEngineSDK_EOneAIHelpType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// AIHelp\xe5\xae\xa2\xe6\x9c\x8d\xe7\xb1\xbb\xe5\x9e\x8b\n" },
#endif
		{ "FAQ.Name", "EOneAIHelpType::FAQ" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "RobotChat.Name", "EOneAIHelpType::RobotChat" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AIHelp\xe5\xae\xa2\xe6\x9c\x8d\xe7\xb1\xbb\xe5\x9e\x8b" },
#endif
		{ "Unknown.Name", "EOneAIHelpType::Unknown" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EOneAIHelpType::Unknown", (int64)EOneAIHelpType::Unknown },
		{ "EOneAIHelpType::RobotChat", (int64)EOneAIHelpType::RobotChat },
		{ "EOneAIHelpType::FAQ", (int64)EOneAIHelpType::FAQ },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_OneEngineSDK_EOneAIHelpType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	"EOneAIHelpType",
	"EOneAIHelpType",
	Z_Construct_UEnum_OneEngineSDK_EOneAIHelpType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneAIHelpType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneAIHelpType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_OneEngineSDK_EOneAIHelpType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_OneEngineSDK_EOneAIHelpType()
{
	if (!Z_Registration_Info_UEnum_EOneAIHelpType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EOneAIHelpType.InnerSingleton, Z_Construct_UEnum_OneEngineSDK_EOneAIHelpType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EOneAIHelpType.InnerSingleton;
}
// ********** End Enum EOneAIHelpType **************************************************************

// ********** Begin Enum EOneScreenOrientation *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EOneScreenOrientation;
static UEnum* EOneScreenOrientation_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EOneScreenOrientation.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EOneScreenOrientation.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneScreenOrientation, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneScreenOrientation"));
	}
	return Z_Registration_Info_UEnum_EOneScreenOrientation.OuterSingleton;
}
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneScreenOrientation>()
{
	return EOneScreenOrientation_StaticEnum();
}
struct Z_Construct_UEnum_OneEngineSDK_EOneScreenOrientation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xb1\x8f\xe5\xb9\x95\xe6\x96\xb9\xe5\x90\x91\n" },
#endif
		{ "Landscape.Name", "EOneScreenOrientation::Landscape" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "Portrait.Name", "EOneScreenOrientation::Portrait" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xb1\x8f\xe5\xb9\x95\xe6\x96\xb9\xe5\x90\x91" },
#endif
		{ "Unknown.Name", "EOneScreenOrientation::Unknown" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EOneScreenOrientation::Unknown", (int64)EOneScreenOrientation::Unknown },
		{ "EOneScreenOrientation::Portrait", (int64)EOneScreenOrientation::Portrait },
		{ "EOneScreenOrientation::Landscape", (int64)EOneScreenOrientation::Landscape },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_OneEngineSDK_EOneScreenOrientation_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	"EOneScreenOrientation",
	"EOneScreenOrientation",
	Z_Construct_UEnum_OneEngineSDK_EOneScreenOrientation_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneScreenOrientation_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneScreenOrientation_Statics::Enum_MetaDataParams), Z_Construct_UEnum_OneEngineSDK_EOneScreenOrientation_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_OneEngineSDK_EOneScreenOrientation()
{
	if (!Z_Registration_Info_UEnum_EOneScreenOrientation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EOneScreenOrientation.InnerSingleton, Z_Construct_UEnum_OneEngineSDK_EOneScreenOrientation_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EOneScreenOrientation.InnerSingleton;
}
// ********** End Enum EOneScreenOrientation *******************************************************

// ********** Begin Enum EOneShareAppTarget ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EOneShareAppTarget;
static UEnum* EOneShareAppTarget_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EOneShareAppTarget.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EOneShareAppTarget.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneShareAppTarget, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneShareAppTarget"));
	}
	return Z_Registration_Info_UEnum_EOneShareAppTarget.OuterSingleton;
}
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneShareAppTarget>()
{
	return EOneShareAppTarget_StaticEnum();
}
struct Z_Construct_UEnum_OneEngineSDK_EOneShareAppTarget_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x88\x86\xe4\xba\xab\xe5\xb9\xb3\xe5\x8f\xb0\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "PE_Bilibili.Name", "EOneShareAppTarget::PE_Bilibili" },
		{ "PE_Discord.Name", "EOneShareAppTarget::PE_Discord" },
		{ "PE_Facebook.Name", "EOneShareAppTarget::PE_Facebook" },
		{ "PE_Instagram.Name", "EOneShareAppTarget::PE_Instagram" },
		{ "PE_Line.Name", "EOneShareAppTarget::PE_Line" },
		{ "PE_NaverGame.Name", "EOneShareAppTarget::PE_NaverGame" },
		{ "PE_QQ.Name", "EOneShareAppTarget::PE_QQ" },
		{ "PE_QZone.Name", "EOneShareAppTarget::PE_QZone" },
		{ "PE_Telegram.Name", "EOneShareAppTarget::PE_Telegram" },
		{ "PE_TikTok.Name", "EOneShareAppTarget::PE_TikTok" },
		{ "PE_Twitter.Name", "EOneShareAppTarget::PE_Twitter" },
		{ "PE_VK.Name", "EOneShareAppTarget::PE_VK" },
		{ "PE_WeChatMoment.Name", "EOneShareAppTarget::PE_WeChatMoment" },
		{ "PE_WeChatSession.Name", "EOneShareAppTarget::PE_WeChatSession" },
		{ "PE_Weibo.Name", "EOneShareAppTarget::PE_Weibo" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x88\x86\xe4\xba\xab\xe5\xb9\xb3\xe5\x8f\xb0" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EOneShareAppTarget::PE_WeChatSession", (int64)EOneShareAppTarget::PE_WeChatSession },
		{ "EOneShareAppTarget::PE_WeChatMoment", (int64)EOneShareAppTarget::PE_WeChatMoment },
		{ "EOneShareAppTarget::PE_QQ", (int64)EOneShareAppTarget::PE_QQ },
		{ "EOneShareAppTarget::PE_QZone", (int64)EOneShareAppTarget::PE_QZone },
		{ "EOneShareAppTarget::PE_Weibo", (int64)EOneShareAppTarget::PE_Weibo },
		{ "EOneShareAppTarget::PE_Bilibili", (int64)EOneShareAppTarget::PE_Bilibili },
		{ "EOneShareAppTarget::PE_Facebook", (int64)EOneShareAppTarget::PE_Facebook },
		{ "EOneShareAppTarget::PE_VK", (int64)EOneShareAppTarget::PE_VK },
		{ "EOneShareAppTarget::PE_Instagram", (int64)EOneShareAppTarget::PE_Instagram },
		{ "EOneShareAppTarget::PE_Twitter", (int64)EOneShareAppTarget::PE_Twitter },
		{ "EOneShareAppTarget::PE_Line", (int64)EOneShareAppTarget::PE_Line },
		{ "EOneShareAppTarget::PE_NaverGame", (int64)EOneShareAppTarget::PE_NaverGame },
		{ "EOneShareAppTarget::PE_TikTok", (int64)EOneShareAppTarget::PE_TikTok },
		{ "EOneShareAppTarget::PE_Discord", (int64)EOneShareAppTarget::PE_Discord },
		{ "EOneShareAppTarget::PE_Telegram", (int64)EOneShareAppTarget::PE_Telegram },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_OneEngineSDK_EOneShareAppTarget_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	"EOneShareAppTarget",
	"EOneShareAppTarget",
	Z_Construct_UEnum_OneEngineSDK_EOneShareAppTarget_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneShareAppTarget_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneShareAppTarget_Statics::Enum_MetaDataParams), Z_Construct_UEnum_OneEngineSDK_EOneShareAppTarget_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_OneEngineSDK_EOneShareAppTarget()
{
	if (!Z_Registration_Info_UEnum_EOneShareAppTarget.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EOneShareAppTarget.InnerSingleton, Z_Construct_UEnum_OneEngineSDK_EOneShareAppTarget_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EOneShareAppTarget.InnerSingleton;
}
// ********** End Enum EOneShareAppTarget **********************************************************

// ********** Begin Enum EOneShareType *************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EOneShareType;
static UEnum* EOneShareType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EOneShareType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EOneShareType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneShareType, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneShareType"));
	}
	return Z_Registration_Info_UEnum_EOneShareType.OuterSingleton;
}
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneShareType>()
{
	return EOneShareType_StaticEnum();
}
struct Z_Construct_UEnum_OneEngineSDK_EOneShareType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x88\x86\xe4\xba\xab\xe5\x86\x85\xe5\xae\xb9\xe7\xb1\xbb\xe5\x9e\x8b\n" },
#endif
		{ "Image.Name", "EOneShareType::Image" },
		{ "ImageSnapShot.Comment", "//\xe6\x96\x87\xe6\x9c\xac\n" },
		{ "ImageSnapShot.Name", "EOneShareType::ImageSnapShot" },
		{ "ImageSnapShot.ToolTip", "\xe6\x96\x87\xe6\x9c\xac" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "Text.Comment", "//\xe7\xbd\x91\xe9\xa1\xb5\n" },
		{ "Text.Name", "EOneShareType::Text" },
		{ "Text.ToolTip", "\xe7\xbd\x91\xe9\xa1\xb5" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x88\x86\xe4\xba\xab\xe5\x86\x85\xe5\xae\xb9\xe7\xb1\xbb\xe5\x9e\x8b" },
#endif
		{ "WebPage.Comment", "//\xe5\x9b\xbe\xe7\x89\x87\n" },
		{ "WebPage.Name", "EOneShareType::WebPage" },
		{ "WebPage.ToolTip", "\xe5\x9b\xbe\xe7\x89\x87" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EOneShareType::Image", (int64)EOneShareType::Image },
		{ "EOneShareType::WebPage", (int64)EOneShareType::WebPage },
		{ "EOneShareType::Text", (int64)EOneShareType::Text },
		{ "EOneShareType::ImageSnapShot", (int64)EOneShareType::ImageSnapShot },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_OneEngineSDK_EOneShareType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	"EOneShareType",
	"EOneShareType",
	Z_Construct_UEnum_OneEngineSDK_EOneShareType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneShareType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneShareType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_OneEngineSDK_EOneShareType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_OneEngineSDK_EOneShareType()
{
	if (!Z_Registration_Info_UEnum_EOneShareType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EOneShareType.InnerSingleton, Z_Construct_UEnum_OneEngineSDK_EOneShareType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EOneShareType.InnerSingleton;
}
// ********** End Enum EOneShareType ***************************************************************

// ********** Begin ScriptStruct FOneShareWeiboSuperData *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOneShareWeiboSuperData;
class UScriptStruct* FOneShareWeiboSuperData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOneShareWeiboSuperData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOneShareWeiboSuperData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneShareWeiboSuperData, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneShareWeiboSuperData"));
	}
	return Z_Registration_Info_UScriptStruct_FOneShareWeiboSuperData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xbe\xae\xe5\x8d\x9a\xe8\xb6\x85\xe8\xaf\x9d\xe5\x88\x86\xe4\xba\xab\xe5\x86\x85\xe5\xae\xb9\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xbe\xae\xe5\x8d\x9a\xe8\xb6\x85\xe8\xaf\x9d\xe5\x88\x86\xe4\xba\xab\xe5\x86\x85\xe5\xae\xb9" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuperGroup_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Section_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExtraInfo_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SuperGroup;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Section;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ExtraInfo_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ExtraInfo_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ExtraInfo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneShareWeiboSuperData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_SuperGroup = { "SuperGroup", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneShareWeiboSuperData, SuperGroup), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuperGroup_MetaData), NewProp_SuperGroup_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_Section = { "Section", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneShareWeiboSuperData, Section), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Section_MetaData), NewProp_Section_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_ExtraInfo_ValueProp = { "ExtraInfo", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_ExtraInfo_Key_KeyProp = { "ExtraInfo_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_ExtraInfo = { "ExtraInfo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneShareWeiboSuperData, ExtraInfo), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExtraInfo_MetaData), NewProp_ExtraInfo_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_SuperGroup,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_Section,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_ExtraInfo_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_ExtraInfo_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_ExtraInfo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OneShareWeiboSuperData",
	Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::PropPointers),
	sizeof(FOneShareWeiboSuperData),
	alignof(FOneShareWeiboSuperData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOneShareWeiboSuperData()
{
	if (!Z_Registration_Info_UScriptStruct_FOneShareWeiboSuperData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOneShareWeiboSuperData.InnerSingleton, Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOneShareWeiboSuperData.InnerSingleton;
}
// ********** End ScriptStruct FOneShareWeiboSuperData *********************************************

// ********** Begin ScriptStruct FOneShareData *****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOneShareData;
class UScriptStruct* FOneShareData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOneShareData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOneShareData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneShareData, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneShareData"));
	}
	return Z_Registration_Info_UScriptStruct_FOneShareData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOneShareData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\x88\x86\xe4\xba\xab\xe6\x95\xb0\xe6\x8d\xae\xe5\x86\x85\xe5\xae\xb9\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x88\x86\xe4\xba\xab\xe6\x95\xb0\xe6\x8d\xae\xe5\x86\x85\xe5\xae\xb9" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WebPageUrl_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Content_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Thumbnail_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Image_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NetImageUrl_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LocalImagePath_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TopicId_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SinaSuperGroup_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_WebPageUrl;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Title;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Content;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Thumbnail;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Image;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NetImageUrl;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LocalImagePath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TopicId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SinaSuperGroup;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneShareData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_WebPageUrl = { "WebPageUrl", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneShareData, WebPageUrl), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WebPageUrl_MetaData), NewProp_WebPageUrl_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneShareData, Title), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Title_MetaData), NewProp_Title_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Content = { "Content", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneShareData, Content), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Content_MetaData), NewProp_Content_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Thumbnail = { "Thumbnail", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneShareData, Thumbnail), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Thumbnail_MetaData), NewProp_Thumbnail_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Image = { "Image", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneShareData, Image), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Image_MetaData), NewProp_Image_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_NetImageUrl = { "NetImageUrl", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneShareData, NetImageUrl), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NetImageUrl_MetaData), NewProp_NetImageUrl_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_LocalImagePath = { "LocalImagePath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneShareData, LocalImagePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LocalImagePath_MetaData), NewProp_LocalImagePath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_TopicId = { "TopicId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneShareData, TopicId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TopicId_MetaData), NewProp_TopicId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_SinaSuperGroup = { "SinaSuperGroup", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneShareData, SinaSuperGroup), Z_Construct_UScriptStruct_FOneShareWeiboSuperData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SinaSuperGroup_MetaData), NewProp_SinaSuperGroup_MetaData) }; // 1660286864
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneShareData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_WebPageUrl,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Title,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Content,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Thumbnail,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Image,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_NetImageUrl,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_LocalImagePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_TopicId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_SinaSuperGroup,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneShareData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OneShareData",
	Z_Construct_UScriptStruct_FOneShareData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareData_Statics::PropPointers),
	sizeof(FOneShareData),
	alignof(FOneShareData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOneShareData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOneShareData()
{
	if (!Z_Registration_Info_UScriptStruct_FOneShareData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOneShareData.InnerSingleton, Z_Construct_UScriptStruct_FOneShareData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOneShareData.InnerSingleton;
}
// ********** End ScriptStruct FOneShareData *******************************************************

// ********** Begin ScriptStruct FOnePushStatus ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOnePushStatus;
class UScriptStruct* FOnePushStatus::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePushStatus.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOnePushStatus.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePushStatus, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePushStatus"));
	}
	return Z_Registration_Info_UScriptStruct_FOnePushStatus.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOnePushStatus_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsSysOpen_MetaData[] = {
		{ "Category", "OnePushStatus" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe6\x98\xaf\xe6\x89\x93\xe5\xbc\x80\xe4\xba\x86\xe6\x8e\xa8\xe9\x80\x81\xe6\x9d\x83\xe9\x99\x90\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe6\x98\xaf\xe6\x89\x93\xe5\xbc\x80\xe4\xba\x86\xe6\x8e\xa8\xe9\x80\x81\xe6\x9d\x83\xe9\x99\x90" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAppOpen_MetaData[] = {
		{ "Category", "OnePushStatus" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//app \xe6\x98\xaf\xe5\x90\xa6\xe6\x89\x93\xe5\xbc\x80\xe6\x8e\xa8\xe9\x80\x81\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "app \xe6\x98\xaf\xe5\x90\xa6\xe6\x89\x93\xe5\xbc\x80\xe6\x8e\xa8\xe9\x80\x81" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsSysOpen_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsSysOpen;
	static void NewProp_bIsAppOpen_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAppOpen;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePushStatus>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsSysOpen_SetBit(void* Obj)
{
	((FOnePushStatus*)Obj)->bIsSysOpen = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsSysOpen = { "bIsSysOpen", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FOnePushStatus), &Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsSysOpen_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsSysOpen_MetaData), NewProp_bIsSysOpen_MetaData) };
void Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsAppOpen_SetBit(void* Obj)
{
	((FOnePushStatus*)Obj)->bIsAppOpen = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsAppOpen = { "bIsAppOpen", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FOnePushStatus), &Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsAppOpen_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAppOpen_MetaData), NewProp_bIsAppOpen_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePushStatus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsSysOpen,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsAppOpen,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushStatus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePushStatus_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OnePushStatus",
	Z_Construct_UScriptStruct_FOnePushStatus_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushStatus_Statics::PropPointers),
	sizeof(FOnePushStatus),
	alignof(FOnePushStatus),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushStatus_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOnePushStatus_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOnePushStatus()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePushStatus.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOnePushStatus.InnerSingleton, Z_Construct_UScriptStruct_FOnePushStatus_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOnePushStatus.InnerSingleton;
}
// ********** End ScriptStruct FOnePushStatus ******************************************************

// ********** Begin ScriptStruct FOnePushTypeInfo **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOnePushTypeInfo;
class UScriptStruct* FOnePushTypeInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePushTypeInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOnePushTypeInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePushTypeInfo, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePushTypeInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FOnePushTypeInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[] = {
		{ "Category", "OnePushTypeInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOpen_MetaData[] = {
		{ "Category", "OnePushTypeInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PushType_MetaData[] = {
		{ "Category", "OnePushTypeInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Name;
	static void NewProp_bOpen_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOpen;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PushType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePushTypeInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePushTypeInfo, Name), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Name_MetaData), NewProp_Name_MetaData) };
void Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_bOpen_SetBit(void* Obj)
{
	((FOnePushTypeInfo*)Obj)->bOpen = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_bOpen = { "bOpen", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FOnePushTypeInfo), &Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_bOpen_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOpen_MetaData), NewProp_bOpen_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_PushType = { "PushType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePushTypeInfo, PushType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PushType_MetaData), NewProp_PushType_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_Name,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_bOpen,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_PushType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OnePushTypeInfo",
	Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::PropPointers),
	sizeof(FOnePushTypeInfo),
	alignof(FOnePushTypeInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOnePushTypeInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePushTypeInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOnePushTypeInfo.InnerSingleton, Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOnePushTypeInfo.InnerSingleton;
}
// ********** End ScriptStruct FOnePushTypeInfo ****************************************************

// ********** Begin ScriptStruct FOnePushMessage ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOnePushMessage;
class UScriptStruct* FOnePushMessage::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePushMessage.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOnePushMessage.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePushMessage, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePushMessage"));
	}
	return Z_Registration_Info_UScriptStruct_FOnePushMessage.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOnePushMessage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[] = {
		{ "Category", "OnePushMessage" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Content_MetaData[] = {
		{ "Category", "OnePushMessage" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MessageId_MetaData[] = {
		{ "Category", "OnePushMessage" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Ext_MetaData[] = {
		{ "Category", "OnePushMessage" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "OnePushMessage" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Title;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Content;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MessageId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Ext;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePushMessage>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePushMessage, Title), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Title_MetaData), NewProp_Title_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Content = { "Content", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePushMessage, Content), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Content_MetaData), NewProp_Content_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_MessageId = { "MessageId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePushMessage, MessageId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MessageId_MetaData), NewProp_MessageId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Ext = { "Ext", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePushMessage, Ext), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Ext_MetaData), NewProp_Ext_MetaData) };
void Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FOnePushMessage*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FOnePushMessage), &Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePushMessage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Title,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Content,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_MessageId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Ext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_bIsActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushMessage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePushMessage_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OnePushMessage",
	Z_Construct_UScriptStruct_FOnePushMessage_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushMessage_Statics::PropPointers),
	sizeof(FOnePushMessage),
	alignof(FOnePushMessage),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushMessage_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOnePushMessage_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOnePushMessage()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePushMessage.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOnePushMessage.InnerSingleton, Z_Construct_UScriptStruct_FOnePushMessage_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOnePushMessage.InnerSingleton;
}
// ********** End ScriptStruct FOnePushMessage *****************************************************

// ********** Begin ScriptStruct FOnePushNotDisturbInfo ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOnePushNotDisturbInfo;
class UScriptStruct* FOnePushNotDisturbInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePushNotDisturbInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOnePushNotDisturbInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePushNotDisturbInfo, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePushNotDisturbInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FOnePushNotDisturbInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bNotDisturb_MetaData[] = {
		{ "Category", "OnePushNotDisturbInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe5\xbc\x80\xe5\x90\xaf\xe5\xa4\x9c\xe9\x97\xb4\xe5\x8b\xbf\xe6\x89\xb0\xef\xbc\x8cNO-\xe4\xb8\x8d\xe5\xbc\x80\xe5\x90\xaf\xef\xbc\x8cYES-\xe5\xbc\x80\xe5\x90\xaf\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe5\xbc\x80\xe5\x90\xaf\xe5\xa4\x9c\xe9\x97\xb4\xe5\x8b\xbf\xe6\x89\xb0\xef\xbc\x8cNO-\xe4\xb8\x8d\xe5\xbc\x80\xe5\x90\xaf\xef\xbc\x8cYES-\xe5\xbc\x80\xe5\x90\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NotDisturbStartTime_MetaData[] = {
		{ "Category", "OnePushNotDisturbInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xa4\x9c\xe9\x97\xb4\xe5\x8b\xbf\xe6\x89\xb0\xe5\xbc\x80\xe5\xa7\x8b\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\xbc\x80\xe5\xa7\x8b\xe6\x97\xb6\xe9\x97\xb4\xe5\xa4\xa7\xe4\xba\x8e\xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\x88\x99\xe4\xbb\xa3\xe8\xa1\xa8\xe8\xb7\xa8\xe5\xa4\xa9\xe3\x80\x82\xe7\xa4\xba\xe4\xbe\x8b\xef\xbc\x9a\"01:00\"\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xa4\x9c\xe9\x97\xb4\xe5\x8b\xbf\xe6\x89\xb0\xe5\xbc\x80\xe5\xa7\x8b\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\xbc\x80\xe5\xa7\x8b\xe6\x97\xb6\xe9\x97\xb4\xe5\xa4\xa7\xe4\xba\x8e\xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\x88\x99\xe4\xbb\xa3\xe8\xa1\xa8\xe8\xb7\xa8\xe5\xa4\xa9\xe3\x80\x82\xe7\xa4\xba\xe4\xbe\x8b\xef\xbc\x9a\"01:00\"" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NotDisturbEndTime_MetaData[] = {
		{ "Category", "OnePushNotDisturbInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xa4\x9c\xe9\x97\xb4\xe5\x8b\xbf\xe6\x89\xb0\xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\xbc\x80\xe5\xa7\x8b\xe6\x97\xb6\xe9\x97\xb4\xe5\xa4\xa7\xe4\xba\x8e\xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\x88\x99\xe4\xbb\xa3\xe8\xa1\xa8\xe8\xb7\xa8\xe5\xa4\xa9\xe3\x80\x82\xe7\xa4\xba\xe4\xbe\x8b\xef\xbc\x9a\"10:00\"\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xa4\x9c\xe9\x97\xb4\xe5\x8b\xbf\xe6\x89\xb0\xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\xbc\x80\xe5\xa7\x8b\xe6\x97\xb6\xe9\x97\xb4\xe5\xa4\xa7\xe4\xba\x8e\xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\x88\x99\xe4\xbb\xa3\xe8\xa1\xa8\xe8\xb7\xa8\xe5\xa4\xa9\xe3\x80\x82\xe7\xa4\xba\xe4\xbe\x8b\xef\xbc\x9a\"10:00\"" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bNotDisturb_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNotDisturb;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NotDisturbStartTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NotDisturbEndTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePushNotDisturbInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_bNotDisturb_SetBit(void* Obj)
{
	((FOnePushNotDisturbInfo*)Obj)->bNotDisturb = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_bNotDisturb = { "bNotDisturb", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FOnePushNotDisturbInfo), &Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_bNotDisturb_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bNotDisturb_MetaData), NewProp_bNotDisturb_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_NotDisturbStartTime = { "NotDisturbStartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePushNotDisturbInfo, NotDisturbStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NotDisturbStartTime_MetaData), NewProp_NotDisturbStartTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_NotDisturbEndTime = { "NotDisturbEndTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOnePushNotDisturbInfo, NotDisturbEndTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NotDisturbEndTime_MetaData), NewProp_NotDisturbEndTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_bNotDisturb,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_NotDisturbStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_NotDisturbEndTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OnePushNotDisturbInfo",
	Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::PropPointers),
	sizeof(FOnePushNotDisturbInfo),
	alignof(FOnePushNotDisturbInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOnePushNotDisturbInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FOnePushNotDisturbInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOnePushNotDisturbInfo.InnerSingleton, Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOnePushNotDisturbInfo.InnerSingleton;
}
// ********** End ScriptStruct FOnePushNotDisturbInfo **********************************************

// ********** Begin ScriptStruct FOneActiveQualificationInfo ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOneActiveQualificationInfo;
class UScriptStruct* FOneActiveQualificationInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOneActiveQualificationInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOneActiveQualificationInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneActiveQualificationInfo, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneActiveQualificationInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FOneActiveQualificationInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Status_MetaData[] = {
		{ "Category", "OneEngineSDK|ActiveQualification" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe6\xbf\x80\xe6\xb4\xbb\xe8\xb5\x84\xe6\xa0\xbc\xef\xbc\x88""0\xef\xbc\x9a\xe6\x97\xa0\xe8\xb5\x84\xe6\xa0\xbc\xef\xbc\x8c""1\xef\xbc\x9a\xe6\x9c\x89\xe8\xb5\x84\xe6\xa0\xbc)\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe6\xbf\x80\xe6\xb4\xbb\xe8\xb5\x84\xe6\xa0\xbc\xef\xbc\x88""0\xef\xbc\x9a\xe6\x97\xa0\xe8\xb5\x84\xe6\xa0\xbc\xef\xbc\x8c""1\xef\xbc\x9a\xe6\x9c\x89\xe8\xb5\x84\xe6\xa0\xbc)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeviceTotal_MetaData[] = {
		{ "Category", "OneEngineSDK|ActiveQualification" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xb7\xb2\xe7\x99\xbb\xe5\xbd\x95\xe8\xae\xbe\xe5\xa4\x87\xe6\x80\xbb\xe6\x95\xb0\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xb7\xb2\xe7\x99\xbb\xe5\xbd\x95\xe8\xae\xbe\xe5\xa4\x87\xe6\x80\xbb\xe6\x95\xb0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WhiteList_MetaData[] = {
		{ "Category", "OneEngineSDK|ActiveQualification" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe6\x98\xaf\xe5\x90\xa6\xe6\xb7\xbb\xe5\x8a\xa0\xe5\x88\xb0\xe7\x99\xbd\xe5\x90\x8d\xe5\x8d\x95\xef\xbc\x88""0\xef\xbc\x9a\xe6\xb2\xa1\xe6\x9c\x89\xe6\xb7\xbb\xe5\x8a\xa0\xef\xbc\x8c""1\xef\xbc\x9a\xe5\xb7\xb2\xe6\xb7\xbb\xe5\x8a\xa0\xef\xbc\x89\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe6\x98\xaf\xe5\x90\xa6\xe6\xb7\xbb\xe5\x8a\xa0\xe5\x88\xb0\xe7\x99\xbd\xe5\x90\x8d\xe5\x8d\x95\xef\xbc\x88""0\xef\xbc\x9a\xe6\xb2\xa1\xe6\x9c\x89\xe6\xb7\xbb\xe5\x8a\xa0\xef\xbc\x8c""1\xef\xbc\x9a\xe5\xb7\xb2\xe6\xb7\xbb\xe5\x8a\xa0\xef\xbc\x89" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeviceLogged_MetaData[] = {
		{ "Category", "OneEngineSDK|ActiveQualification" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xbd\x93\xe5\x89\x8d\xe8\xae\xbe\xe5\xa4\x87\xe6\x98\xaf\xe5\x90\xa6\xe5\x9c\xa8\xe5\xb7\xb2\xe7\x99\xbb\xe5\xbd\x95\xe8\xae\xbe\xe5\xa4\x87\xe4\xb8\xad\xef\xbc\x88""0\xef\xbc\x9a\xe4\xb8\x8d\xe5\x9c\xa8\xef\xbc\x8c""1\xef\xbc\x9a\xe5\x9c\xa8\xef\xbc\x89\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xbd\x93\xe5\x89\x8d\xe8\xae\xbe\xe5\xa4\x87\xe6\x98\xaf\xe5\x90\xa6\xe5\x9c\xa8\xe5\xb7\xb2\xe7\x99\xbb\xe5\xbd\x95\xe8\xae\xbe\xe5\xa4\x87\xe4\xb8\xad\xef\xbc\x88""0\xef\xbc\x9a\xe4\xb8\x8d\xe5\x9c\xa8\xef\xbc\x8c""1\xef\xbc\x9a\xe5\x9c\xa8\xef\xbc\x89" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Status;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DeviceTotal;
	static const UECodeGen_Private::FIntPropertyParams NewProp_WhiteList;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DeviceLogged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneActiveQualificationInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_Status = { "Status", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneActiveQualificationInfo, Status), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Status_MetaData), NewProp_Status_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_DeviceTotal = { "DeviceTotal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneActiveQualificationInfo, DeviceTotal), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeviceTotal_MetaData), NewProp_DeviceTotal_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_WhiteList = { "WhiteList", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneActiveQualificationInfo, WhiteList), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WhiteList_MetaData), NewProp_WhiteList_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_DeviceLogged = { "DeviceLogged", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FOneActiveQualificationInfo, DeviceLogged), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeviceLogged_MetaData), NewProp_DeviceLogged_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_Status,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_DeviceTotal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_WhiteList,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_DeviceLogged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OneActiveQualificationInfo",
	Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::PropPointers),
	sizeof(FOneActiveQualificationInfo),
	alignof(FOneActiveQualificationInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOneActiveQualificationInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FOneActiveQualificationInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOneActiveQualificationInfo.InnerSingleton, Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOneActiveQualificationInfo.InnerSingleton;
}
// ********** End ScriptStruct FOneActiveQualificationInfo *****************************************

// ********** Begin ScriptStruct FUserIpInfo *******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FUserIpInfo;
class UScriptStruct* FUserIpInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FUserIpInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FUserIpInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FUserIpInfo, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("UserIpInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FUserIpInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FUserIpInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Attribution_MetaData[] = {
		{ "Category", "UserIpInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CountryCode_MetaData[] = {
		{ "Category", "UserIpInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CityCode_MetaData[] = {
		{ "Category", "UserIpInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Country_MetaData[] = {
		{ "Category", "UserIpInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Region_MetaData[] = {
		{ "Category", "UserIpInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_City_MetaData[] = {
		{ "Category", "UserIpInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Attribution;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CountryCode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CityCode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Country;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Region;
	static const UECodeGen_Private::FStrPropertyParams NewProp_City;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FUserIpInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Attribution = { "Attribution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUserIpInfo, Attribution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Attribution_MetaData), NewProp_Attribution_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_CountryCode = { "CountryCode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUserIpInfo, CountryCode), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CountryCode_MetaData), NewProp_CountryCode_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_CityCode = { "CityCode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUserIpInfo, CityCode), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CityCode_MetaData), NewProp_CityCode_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Country = { "Country", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUserIpInfo, Country), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Country_MetaData), NewProp_Country_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Region = { "Region", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUserIpInfo, Region), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Region_MetaData), NewProp_Region_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_City = { "City", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUserIpInfo, City), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_City_MetaData), NewProp_City_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FUserIpInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Attribution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_CountryCode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_CityCode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Country,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Region,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_City,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUserIpInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FUserIpInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"UserIpInfo",
	Z_Construct_UScriptStruct_FUserIpInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUserIpInfo_Statics::PropPointers),
	sizeof(FUserIpInfo),
	alignof(FUserIpInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUserIpInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FUserIpInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FUserIpInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FUserIpInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FUserIpInfo.InnerSingleton, Z_Construct_UScriptStruct_FUserIpInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FUserIpInfo.InnerSingleton;
}
// ********** End ScriptStruct FUserIpInfo *********************************************************

// ********** Begin Enum EOneEngineSDKRegionType ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EOneEngineSDKRegionType;
static UEnum* EOneEngineSDKRegionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EOneEngineSDKRegionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EOneEngineSDKRegionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneEngineSDKRegionType, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneEngineSDKRegionType"));
	}
	return Z_Registration_Info_UEnum_EOneEngineSDKRegionType.OuterSingleton;
}
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneEngineSDKRegionType>()
{
	return EOneEngineSDKRegionType_StaticEnum();
}
struct Z_Construct_UEnum_OneEngineSDK_EOneEngineSDKRegionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Mainland.Name", "EOneEngineSDKRegionType::Mainland" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "Oversea.Comment", "//\xe5\xa4\xa7\xe9\x99\x86\n" },
		{ "Oversea.Name", "EOneEngineSDKRegionType::Oversea" },
		{ "Oversea.ToolTip", "\xe5\xa4\xa7\xe9\x99\x86" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EOneEngineSDKRegionType::Mainland", (int64)EOneEngineSDKRegionType::Mainland },
		{ "EOneEngineSDKRegionType::Oversea", (int64)EOneEngineSDKRegionType::Oversea },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_OneEngineSDK_EOneEngineSDKRegionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	"EOneEngineSDKRegionType",
	"EOneEngineSDKRegionType",
	Z_Construct_UEnum_OneEngineSDK_EOneEngineSDKRegionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneEngineSDKRegionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneEngineSDKRegionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_OneEngineSDK_EOneEngineSDKRegionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_OneEngineSDK_EOneEngineSDKRegionType()
{
	if (!Z_Registration_Info_UEnum_EOneEngineSDKRegionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EOneEngineSDKRegionType.InnerSingleton, Z_Construct_UEnum_OneEngineSDK_EOneEngineSDKRegionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EOneEngineSDKRegionType.InnerSingleton;
}
// ********** End Enum EOneEngineSDKRegionType *****************************************************

// ********** Begin Enum EOneUnlockSafeLockResult **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EOneUnlockSafeLockResult;
static UEnum* EOneUnlockSafeLockResult_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EOneUnlockSafeLockResult.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EOneUnlockSafeLockResult.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockResult, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneUnlockSafeLockResult"));
	}
	return Z_Registration_Info_UEnum_EOneUnlockSafeLockResult.OuterSingleton;
}
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneUnlockSafeLockResult>()
{
	return EOneUnlockSafeLockResult_StaticEnum();
}
struct Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xae\x9a\xe4\xb9\x89\xe8\xa7\xa3\xe9\x94\x81\xe7\xbb\x93\xe6\x9e\x9c\xe7\x9a\x84\xe6\x9e\x9a\xe4\xb8\xbe\xe7\xb1\xbb\xe5\x9e\x8b\n" },
#endif
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xae\x9a\xe4\xb9\x89\xe8\xa7\xa3\xe9\x94\x81\xe7\xbb\x93\xe6\x9e\x9c\xe7\x9a\x84\xe6\x9e\x9a\xe4\xb8\xbe\xe7\xb1\xbb\xe5\x9e\x8b" },
#endif
		{ "UnlockFailed.Comment", "// \xe8\xa7\xa3\xe9\x94\x81\xe8\xb6\x85\xe6\x97\xb6\xef\xbc\x8c\xe7\x94\xa8\xe6\x88\xb7\xe6\x9c\xaa\xe5\x9c\xa8\xe8\xa7\x84\xe5\xae\x9a\xe6\x97\xb6\xe9\x97\xb4\xe5\x86\x85\xe8\xa7\xa3\xe9\x94\x81(\xe8\xbd\xae\xe8\xaf\xa2\xe6\x97\xb6\xe9\x97\xb4\xe5\x88\xb0)\n" },
		{ "UnlockFailed.Name", "EOneUnlockSafeLockResult::UnlockFailed" },
		{ "UnlockFailed.ToolTip", "\xe8\xa7\xa3\xe9\x94\x81\xe8\xb6\x85\xe6\x97\xb6\xef\xbc\x8c\xe7\x94\xa8\xe6\x88\xb7\xe6\x9c\xaa\xe5\x9c\xa8\xe8\xa7\x84\xe5\xae\x9a\xe6\x97\xb6\xe9\x97\xb4\xe5\x86\x85\xe8\xa7\xa3\xe9\x94\x81(\xe8\xbd\xae\xe8\xaf\xa2\xe6\x97\xb6\xe9\x97\xb4\xe5\x88\xb0)" },
		{ "UnlockSucceeded.Comment", "// \xe5\xb7\xb2\xe8\xa7\xa6\xe5\x8f\x91\xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf\xe6\x8e\xa8\xe9\x80\x81\xef\xbc\x8c\xe7\xad\x89\xe5\xbe\x85\xe7\x94\xa8\xe6\x88\xb7\xe8\xa7\xa3\xe9\x94\x81\n" },
		{ "UnlockSucceeded.Name", "EOneUnlockSafeLockResult::UnlockSucceeded" },
		{ "UnlockSucceeded.ToolTip", "\xe5\xb7\xb2\xe8\xa7\xa6\xe5\x8f\x91\xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf\xe6\x8e\xa8\xe9\x80\x81\xef\xbc\x8c\xe7\xad\x89\xe5\xbe\x85\xe7\x94\xa8\xe6\x88\xb7\xe8\xa7\xa3\xe9\x94\x81" },
		{ "UnlockTimedOut.Comment", "// \xe8\xa7\xa3\xe9\x94\x81\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8c\xe6\x84\x8f\xe8\xa7\xa3\xe9\x94\x81\n" },
		{ "UnlockTimedOut.Name", "EOneUnlockSafeLockResult::UnlockTimedOut" },
		{ "UnlockTimedOut.ToolTip", "\xe8\xa7\xa3\xe9\x94\x81\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8c\xe6\x84\x8f\xe8\xa7\xa3\xe9\x94\x81" },
		{ "WaitingToUnlock.Name", "EOneUnlockSafeLockResult::WaitingToUnlock" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EOneUnlockSafeLockResult::WaitingToUnlock", (int64)EOneUnlockSafeLockResult::WaitingToUnlock },
		{ "EOneUnlockSafeLockResult::UnlockSucceeded", (int64)EOneUnlockSafeLockResult::UnlockSucceeded },
		{ "EOneUnlockSafeLockResult::UnlockTimedOut", (int64)EOneUnlockSafeLockResult::UnlockTimedOut },
		{ "EOneUnlockSafeLockResult::UnlockFailed", (int64)EOneUnlockSafeLockResult::UnlockFailed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockResult_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	"EOneUnlockSafeLockResult",
	"EOneUnlockSafeLockResult",
	Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockResult_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockResult_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockResult_Statics::Enum_MetaDataParams), Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockResult_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockResult()
{
	if (!Z_Registration_Info_UEnum_EOneUnlockSafeLockResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EOneUnlockSafeLockResult.InnerSingleton, Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockResult_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EOneUnlockSafeLockResult.InnerSingleton;
}
// ********** End Enum EOneUnlockSafeLockResult ****************************************************

// ********** Begin Enum EOneUnlockSafeLockType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EOneUnlockSafeLockType;
static UEnum* EOneUnlockSafeLockType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EOneUnlockSafeLockType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EOneUnlockSafeLockType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockType, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneUnlockSafeLockType"));
	}
	return Z_Registration_Info_UEnum_EOneUnlockSafeLockType.OuterSingleton;
}
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneUnlockSafeLockType>()
{
	return EOneUnlockSafeLockType_StaticEnum();
}
struct Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xae\x89\xe5\x85\xa8\xe9\x94\x81\xe8\xa7\xa3\xe9\x94\x81\xe6\x96\xb9\xe5\xbc\x8f\xe6\x9e\x9a\xe4\xb8\xbe\xe7\xb1\xbb\xe5\x9e\x8b\n" },
#endif
		{ "DynamicCode.Comment", "// \xe6\x8e\xa8\xe9\x80\x81\n" },
		{ "DynamicCode.Name", "EOneUnlockSafeLockType::DynamicCode" },
		{ "DynamicCode.ToolTip", "\xe6\x8e\xa8\xe9\x80\x81" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "PushNotification.Name", "EOneUnlockSafeLockType::PushNotification" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xae\x89\xe5\x85\xa8\xe9\x94\x81\xe8\xa7\xa3\xe9\x94\x81\xe6\x96\xb9\xe5\xbc\x8f\xe6\x9e\x9a\xe4\xb8\xbe\xe7\xb1\xbb\xe5\x9e\x8b" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EOneUnlockSafeLockType::PushNotification", (int64)EOneUnlockSafeLockType::PushNotification },
		{ "EOneUnlockSafeLockType::DynamicCode", (int64)EOneUnlockSafeLockType::DynamicCode },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	"EOneUnlockSafeLockType",
	"EOneUnlockSafeLockType",
	Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockType()
{
	if (!Z_Registration_Info_UEnum_EOneUnlockSafeLockType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EOneUnlockSafeLockType.InnerSingleton, Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EOneUnlockSafeLockType.InnerSingleton;
}
// ********** End Enum EOneUnlockSafeLockType ******************************************************

// ********** Begin Enum EOneNaverGameType *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EOneNaverGameType;
static UEnum* EOneNaverGameType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EOneNaverGameType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EOneNaverGameType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneNaverGameType, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneNaverGameType"));
	}
	return Z_Registration_Info_UEnum_EOneNaverGameType.OuterSingleton;
}
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneNaverGameType>()
{
	return EOneNaverGameType_StaticEnum();
}
struct Z_Construct_UEnum_OneEngineSDK_EOneNaverGameType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Banner.Name", "EOneNaverGameType::Banner" },
		{ "BlueprintType", "true" },
		{ "Board.Name", "EOneNaverGameType::Board" },
		{ "Feed.Name", "EOneNaverGameType::Feed" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "Sorry.Name", "EOneNaverGameType::Sorry" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EOneNaverGameType::Banner", (int64)EOneNaverGameType::Banner },
		{ "EOneNaverGameType::Sorry", (int64)EOneNaverGameType::Sorry },
		{ "EOneNaverGameType::Board", (int64)EOneNaverGameType::Board },
		{ "EOneNaverGameType::Feed", (int64)EOneNaverGameType::Feed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_OneEngineSDK_EOneNaverGameType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	"EOneNaverGameType",
	"EOneNaverGameType",
	Z_Construct_UEnum_OneEngineSDK_EOneNaverGameType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneNaverGameType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EOneNaverGameType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_OneEngineSDK_EOneNaverGameType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_OneEngineSDK_EOneNaverGameType()
{
	if (!Z_Registration_Info_UEnum_EOneNaverGameType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EOneNaverGameType.InnerSingleton, Z_Construct_UEnum_OneEngineSDK_EOneNaverGameType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EOneNaverGameType.InnerSingleton;
}
// ********** End Enum EOneNaverGameType ***********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h__Script_OneEngineSDK_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EOneEngineThirdType_StaticEnum, TEXT("EOneEngineThirdType"), &Z_Registration_Info_UEnum_EOneEngineThirdType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1323311522U) },
		{ EOneResEventState_StaticEnum, TEXT("EOneResEventState"), &Z_Registration_Info_UEnum_EOneResEventState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3289592693U) },
		{ EOnePermissionType_StaticEnum, TEXT("EOnePermissionType"), &Z_Registration_Info_UEnum_EOnePermissionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 784157622U) },
		{ EOneAIHelpType_StaticEnum, TEXT("EOneAIHelpType"), &Z_Registration_Info_UEnum_EOneAIHelpType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1876729435U) },
		{ EOneScreenOrientation_StaticEnum, TEXT("EOneScreenOrientation"), &Z_Registration_Info_UEnum_EOneScreenOrientation, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4155312940U) },
		{ EOneShareAppTarget_StaticEnum, TEXT("EOneShareAppTarget"), &Z_Registration_Info_UEnum_EOneShareAppTarget, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 566562849U) },
		{ EOneShareType_StaticEnum, TEXT("EOneShareType"), &Z_Registration_Info_UEnum_EOneShareType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3795355242U) },
		{ EOneEngineSDKRegionType_StaticEnum, TEXT("EOneEngineSDKRegionType"), &Z_Registration_Info_UEnum_EOneEngineSDKRegionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 770794992U) },
		{ EOneUnlockSafeLockResult_StaticEnum, TEXT("EOneUnlockSafeLockResult"), &Z_Registration_Info_UEnum_EOneUnlockSafeLockResult, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 718233398U) },
		{ EOneUnlockSafeLockType_StaticEnum, TEXT("EOneUnlockSafeLockType"), &Z_Registration_Info_UEnum_EOneUnlockSafeLockType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2493401473U) },
		{ EOneNaverGameType_StaticEnum, TEXT("EOneNaverGameType"), &Z_Registration_Info_UEnum_EOneNaverGameType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1261665170U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FOneUserThirdInfo::StaticStruct, Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewStructOps, TEXT("OneUserThirdInfo"), &Z_Registration_Info_UScriptStruct_FOneUserThirdInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOneUserThirdInfo), 1657502178U) },
		{ FOneUserInfo::StaticStruct, Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewStructOps, TEXT("OneUserInfo"), &Z_Registration_Info_UScriptStruct_FOneUserInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOneUserInfo), 3532791584U) },
		{ FOneProductInfo::StaticStruct, Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewStructOps, TEXT("OneProductInfo"), &Z_Registration_Info_UScriptStruct_FOneProductInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOneProductInfo), 4072506128U) },
		{ FOnePaymentInfo::StaticStruct, Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewStructOps, TEXT("OnePaymentInfo"), &Z_Registration_Info_UScriptStruct_FOnePaymentInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOnePaymentInfo), 3873894406U) },
		{ FOneRoleInfo::StaticStruct, Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewStructOps, TEXT("OneRoleInfo"), &Z_Registration_Info_UScriptStruct_FOneRoleInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOneRoleInfo), 1869861825U) },
		{ FOneUserLocationInfo::StaticStruct, Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewStructOps, TEXT("OneUserLocationInfo"), &Z_Registration_Info_UScriptStruct_FOneUserLocationInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOneUserLocationInfo), 3670423002U) },
		{ FOneDeviceInfo::StaticStruct, Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewStructOps, TEXT("OneDeviceInfo"), &Z_Registration_Info_UScriptStruct_FOneDeviceInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOneDeviceInfo), 2746062483U) },
		{ FOneAntiAddictionInfo::StaticStruct, Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewStructOps, TEXT("OneAntiAddictionInfo"), &Z_Registration_Info_UScriptStruct_FOneAntiAddictionInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOneAntiAddictionInfo), 4278161144U) },
		{ FOneURCRoleInfo::StaticStruct, Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewStructOps, TEXT("OneURCRoleInfo"), &Z_Registration_Info_UScriptStruct_FOneURCRoleInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOneURCRoleInfo), 904059933U) },
		{ FOnePermissionInfo::StaticStruct, Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewStructOps, TEXT("OnePermissionInfo"), &Z_Registration_Info_UScriptStruct_FOnePermissionInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOnePermissionInfo), 3961753562U) },
		{ FOneShareWeiboSuperData::StaticStruct, Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewStructOps, TEXT("OneShareWeiboSuperData"), &Z_Registration_Info_UScriptStruct_FOneShareWeiboSuperData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOneShareWeiboSuperData), 1660286864U) },
		{ FOneShareData::StaticStruct, Z_Construct_UScriptStruct_FOneShareData_Statics::NewStructOps, TEXT("OneShareData"), &Z_Registration_Info_UScriptStruct_FOneShareData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOneShareData), 3793132714U) },
		{ FOnePushStatus::StaticStruct, Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewStructOps, TEXT("OnePushStatus"), &Z_Registration_Info_UScriptStruct_FOnePushStatus, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOnePushStatus), 4113934568U) },
		{ FOnePushTypeInfo::StaticStruct, Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewStructOps, TEXT("OnePushTypeInfo"), &Z_Registration_Info_UScriptStruct_FOnePushTypeInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOnePushTypeInfo), 961715130U) },
		{ FOnePushMessage::StaticStruct, Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewStructOps, TEXT("OnePushMessage"), &Z_Registration_Info_UScriptStruct_FOnePushMessage, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOnePushMessage), 685681060U) },
		{ FOnePushNotDisturbInfo::StaticStruct, Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewStructOps, TEXT("OnePushNotDisturbInfo"), &Z_Registration_Info_UScriptStruct_FOnePushNotDisturbInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOnePushNotDisturbInfo), 2590399038U) },
		{ FOneActiveQualificationInfo::StaticStruct, Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewStructOps, TEXT("OneActiveQualificationInfo"), &Z_Registration_Info_UScriptStruct_FOneActiveQualificationInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOneActiveQualificationInfo), 2883201467U) },
		{ FUserIpInfo::StaticStruct, Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewStructOps, TEXT("UserIpInfo"), &Z_Registration_Info_UScriptStruct_FUserIpInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FUserIpInfo), 4288479508U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h__Script_OneEngineSDK_1223228430(TEXT("/Script/OneEngineSDK"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h__Script_OneEngineSDK_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h__Script_OneEngineSDK_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h__Script_OneEngineSDK_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h__Script_OneEngineSDK_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
