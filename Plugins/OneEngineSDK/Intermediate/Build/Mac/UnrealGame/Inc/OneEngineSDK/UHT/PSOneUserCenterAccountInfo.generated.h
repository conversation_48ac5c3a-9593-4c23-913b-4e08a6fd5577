// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSOneUserCenterAccountInfo.h"

#ifdef ONEENGINESDK_PSOneUserCenterAccountInfo_generated_h
#error "PSOneUserCenterAccountInfo.generated.h already included, missing '#pragma once' in PSOneUserCenterAccountInfo.h"
#endif
#define ONEENGINESDK_PSOneUserCenterAccountInfo_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FPSOneAccountInfoStruct *******************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterAccountInfo_h_24_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FPSOneAccountInfoStruct;
// ********** End ScriptStruct FPSOneAccountInfoStruct *********************************************

// ********** Begin Class UPSOneUserCenterAccountInfo **********************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterAccountInfo_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterAccountInfo_h_46_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneUserCenterAccountInfo(); \
	friend struct Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterAccountInfo_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSOneUserCenterAccountInfo, UPSOneFocusUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSOneUserCenterAccountInfo_NoRegister) \
	DECLARE_SERIALIZER(UPSOneUserCenterAccountInfo) \
	virtual UObject* _getUObject() const override { return const_cast<UPSOneUserCenterAccountInfo*>(this); }


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterAccountInfo_h_46_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneUserCenterAccountInfo(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSOneUserCenterAccountInfo(UPSOneUserCenterAccountInfo&&) = delete; \
	UPSOneUserCenterAccountInfo(const UPSOneUserCenterAccountInfo&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneUserCenterAccountInfo); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneUserCenterAccountInfo); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneUserCenterAccountInfo) \
	NO_API virtual ~UPSOneUserCenterAccountInfo();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterAccountInfo_h_43_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterAccountInfo_h_46_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterAccountInfo_h_46_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterAccountInfo_h_46_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSOneUserCenterAccountInfo;

// ********** End Class UPSOneUserCenterAccountInfo ************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterAccountInfo_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
