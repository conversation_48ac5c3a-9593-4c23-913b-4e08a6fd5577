// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneUserCenterAccountInfo.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneUserCenterAccountInfo() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterAccountInfo();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterAccountInfo_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FPSOneAccountInfoStruct();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FPSOneAccountInfoStruct *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPSOneAccountInfoStruct;
class UScriptStruct* FPSOneAccountInfoStruct::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPSOneAccountInfoStruct.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPSOneAccountInfoStruct.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("PSOneAccountInfoStruct"));
	}
	return Z_Registration_Info_UScriptStruct_FPSOneAccountInfoStruct.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImageURL_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Nick_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mobile_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Email_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasPwd_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ImageURL;
	static const UECodeGen_Private::FTextPropertyParams NewProp_Nick;
	static const UECodeGen_Private::FTextPropertyParams NewProp_Mobile;
	static const UECodeGen_Private::FTextPropertyParams NewProp_Email;
	static void NewProp_bHasPwd_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasPwd;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPSOneAccountInfoStruct>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_ImageURL = { "ImageURL", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneAccountInfoStruct, ImageURL), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImageURL_MetaData), NewProp_ImageURL_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Nick = { "Nick", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneAccountInfoStruct, Nick), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Nick_MetaData), NewProp_Nick_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Mobile = { "Mobile", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneAccountInfoStruct, Mobile), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mobile_MetaData), NewProp_Mobile_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Email = { "Email", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneAccountInfoStruct, Email), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Email_MetaData), NewProp_Email_MetaData) };
void Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_bHasPwd_SetBit(void* Obj)
{
	((FPSOneAccountInfoStruct*)Obj)->bHasPwd = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_bHasPwd = { "bHasPwd", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPSOneAccountInfoStruct), &Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_bHasPwd_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasPwd_MetaData), NewProp_bHasPwd_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_ImageURL,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Nick,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Mobile,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Email,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_bHasPwd,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"PSOneAccountInfoStruct",
	Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::PropPointers),
	sizeof(FPSOneAccountInfoStruct),
	alignof(FPSOneAccountInfoStruct),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPSOneAccountInfoStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPSOneAccountInfoStruct.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPSOneAccountInfoStruct.InnerSingleton, Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPSOneAccountInfoStruct.InnerSingleton;
}
// ********** End ScriptStruct FPSOneAccountInfoStruct *********************************************

// ********** Begin Class UPSOneUserCenterAccountInfo **********************************************
void UPSOneUserCenterAccountInfo::StaticRegisterNativesUPSOneUserCenterAccountInfo()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneUserCenterAccountInfo;
UClass* UPSOneUserCenterAccountInfo::GetPrivateStaticClass()
{
	using TClass = UPSOneUserCenterAccountInfo;
	if (!Z_Registration_Info_UClass_UPSOneUserCenterAccountInfo.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneUserCenterAccountInfo"),
			Z_Registration_Info_UClass_UPSOneUserCenterAccountInfo.InnerSingleton,
			StaticRegisterNativesUPSOneUserCenterAccountInfo,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterAccountInfo.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneUserCenterAccountInfo_NoRegister()
{
	return UPSOneUserCenterAccountInfo::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "IncludePath", "Views/PSOneUserCenterAccountInfo.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvatarCell_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// AvatarCell\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AvatarCell" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NickCell_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// NickCell\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "NickCell" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MobileCell_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// MobileCell\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "MobileCell" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmailCell_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// EmailCell\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "EmailCell" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CancellationCell_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// CancellationCell\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "CancellationCell" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChangePasswordCell_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//  ChangePasswordCell\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ChangePasswordCell" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AvatarCell;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NickCell;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MobileCell;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EmailCell;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CancellationCell;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChangePasswordCell;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static const UECodeGen_Private::FImplementedInterfaceParams InterfaceParams[];
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterAccountInfo>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_AvatarCell = { "AvatarCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterAccountInfo, AvatarCell), Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvatarCell_MetaData), NewProp_AvatarCell_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_NickCell = { "NickCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterAccountInfo, NickCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NickCell_MetaData), NewProp_NickCell_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_MobileCell = { "MobileCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterAccountInfo, MobileCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MobileCell_MetaData), NewProp_MobileCell_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_EmailCell = { "EmailCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterAccountInfo, EmailCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmailCell_MetaData), NewProp_EmailCell_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_CancellationCell = { "CancellationCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterAccountInfo, CancellationCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CancellationCell_MetaData), NewProp_CancellationCell_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_ChangePasswordCell = { "ChangePasswordCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterAccountInfo, ChangePasswordCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChangePasswordCell_MetaData), NewProp_ChangePasswordCell_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_AvatarCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_NickCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_MobileCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_EmailCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_CancellationCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_ChangePasswordCell,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FImplementedInterfaceParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::InterfaceParams[] = {
	{ Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_NoRegister, (int32)VTABLE_OFFSET(UPSOneUserCenterAccountInfo, IPSOneSaveFocusWidgetInterface), false },  // **********
};
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::ClassParams = {
	&UPSOneUserCenterAccountInfo::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::PropPointers,
	InterfaceParams,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::PropPointers),
	UE_ARRAY_COUNT(InterfaceParams),
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneUserCenterAccountInfo()
{
	if (!Z_Registration_Info_UClass_UPSOneUserCenterAccountInfo.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneUserCenterAccountInfo.OuterSingleton, Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterAccountInfo.OuterSingleton;
}
UPSOneUserCenterAccountInfo::UPSOneUserCenterAccountInfo(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterAccountInfo);
UPSOneUserCenterAccountInfo::~UPSOneUserCenterAccountInfo() {}
// ********** End Class UPSOneUserCenterAccountInfo ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterAccountInfo_h__Script_OneEngineSDK_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPSOneAccountInfoStruct::StaticStruct, Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewStructOps, TEXT("PSOneAccountInfoStruct"), &Z_Registration_Info_UScriptStruct_FPSOneAccountInfoStruct, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPSOneAccountInfoStruct), 3215285529U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneUserCenterAccountInfo, UPSOneUserCenterAccountInfo::StaticClass, TEXT("UPSOneUserCenterAccountInfo"), &Z_Registration_Info_UClass_UPSOneUserCenterAccountInfo, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneUserCenterAccountInfo), 2197944971U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterAccountInfo_h__Script_OneEngineSDK_2802796340(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterAccountInfo_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterAccountInfo_h__Script_OneEngineSDK_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterAccountInfo_h__Script_OneEngineSDK_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterAccountInfo_h__Script_OneEngineSDK_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
