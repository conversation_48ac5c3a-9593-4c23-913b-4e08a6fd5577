// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "OneEngineSDKHelper.h"

#ifdef ONEENGINESDK_OneEngineSDKHelper_generated_h
#error "OneEngineSDKHelper.generated.h already included, missing '#pragma once' in OneEngineSDKHelper.h"
#endif
#define ONEENGINESDK_OneEngineSDKHelper_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FOneUserThirdInfo *************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_44_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOneUserThirdInfo;
// ********** End ScriptStruct FOneUserThirdInfo ***************************************************

// ********** Begin ScriptStruct FOneUserInfo ******************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_77_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneUserInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOneUserInfo;
// ********** End ScriptStruct FOneUserInfo ********************************************************

// ********** Begin ScriptStruct FOneProductInfo ***************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_138_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneProductInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOneProductInfo;
// ********** End ScriptStruct FOneProductInfo *****************************************************

// ********** Begin ScriptStruct FOnePaymentInfo ***************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_164_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePaymentInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOnePaymentInfo;
// ********** End ScriptStruct FOnePaymentInfo *****************************************************

// ********** Begin ScriptStruct FOneRoleInfo ******************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_205_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneRoleInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOneRoleInfo;
// ********** End ScriptStruct FOneRoleInfo ********************************************************

// ********** Begin ScriptStruct FOneUserLocationInfo **********************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_236_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOneUserLocationInfo;
// ********** End ScriptStruct FOneUserLocationInfo ************************************************

// ********** Begin ScriptStruct FOneDeviceInfo ****************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_262_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneDeviceInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOneDeviceInfo;
// ********** End ScriptStruct FOneDeviceInfo ******************************************************

// ********** Begin ScriptStruct FOneAntiAddictionInfo *********************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_279_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOneAntiAddictionInfo;
// ********** End ScriptStruct FOneAntiAddictionInfo ***********************************************

// ********** Begin ScriptStruct FOneURCRoleInfo ***************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_328_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOneURCRoleInfo;
// ********** End ScriptStruct FOneURCRoleInfo *****************************************************

// ********** Begin ScriptStruct FOnePermissionInfo ************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_380_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePermissionInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOnePermissionInfo;
// ********** End ScriptStruct FOnePermissionInfo **************************************************

// ********** Begin ScriptStruct FOneShareWeiboSuperData *******************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_446_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOneShareWeiboSuperData;
// ********** End ScriptStruct FOneShareWeiboSuperData *********************************************

// ********** Begin ScriptStruct FOneShareData *****************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_462_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneShareData_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOneShareData;
// ********** End ScriptStruct FOneShareData *******************************************************

// ********** Begin ScriptStruct FOnePushStatus ****************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_487_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePushStatus_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOnePushStatus;
// ********** End ScriptStruct FOnePushStatus ******************************************************

// ********** Begin ScriptStruct FOnePushTypeInfo **************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_501_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOnePushTypeInfo;
// ********** End ScriptStruct FOnePushTypeInfo ****************************************************

// ********** Begin ScriptStruct FOnePushMessage ***************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_515_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePushMessage_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOnePushMessage;
// ********** End ScriptStruct FOnePushMessage *****************************************************

// ********** Begin ScriptStruct FOnePushNotDisturbInfo ********************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_533_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOnePushNotDisturbInfo;
// ********** End ScriptStruct FOnePushNotDisturbInfo **********************************************

// ********** Begin ScriptStruct FOneActiveQualificationInfo ***************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_552_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FOneActiveQualificationInfo;
// ********** End ScriptStruct FOneActiveQualificationInfo *****************************************

// ********** Begin ScriptStruct FUserIpInfo *******************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_576_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FUserIpInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


struct FUserIpInfo;
// ********** End ScriptStruct FUserIpInfo *********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h

// ********** Begin Enum EOneEngineThirdType *******************************************************
#define FOREACH_ENUM_EONEENGINETHIRDTYPE(op) \
	op(EOneEngineThirdType::Guest) \
	op(EOneEngineThirdType::Facebook) \
	op(EOneEngineThirdType::Google) \
	op(EOneEngineThirdType::Twitter) \
	op(EOneEngineThirdType::Line) \
	op(EOneEngineThirdType::GooglePlay) \
	op(EOneEngineThirdType::GameCenter) \
	op(EOneEngineThirdType::Phone) \
	op(EOneEngineThirdType::Email) \
	op(EOneEngineThirdType::VK) \
	op(EOneEngineThirdType::Naver) \
	op(EOneEngineThirdType::Apple) \
	op(EOneEngineThirdType::WeChat) \
	op(EOneEngineThirdType::GuestInherit) \
	op(EOneEngineThirdType::Yandex) \
	op(EOneEngineThirdType::MailRu) \
	op(EOneEngineThirdType::Infiplay) \
	op(EOneEngineThirdType::HW) \
	op(EOneEngineThirdType::ShareCode) \
	op(EOneEngineThirdType::HONOR) \
	op(EOneEngineThirdType::STEAM) \
	op(EOneEngineThirdType::PCScanCode) \
	op(EOneEngineThirdType::PlayStation) \
	op(EOneEngineThirdType::APJ) \
	op(EOneEngineThirdType::Crunchyrool) \
	op(EOneEngineThirdType::LinkAccount) \
	op(EOneEngineThirdType::NaverCafe) \
	op(EOneEngineThirdType::WMPass) 

enum class EOneEngineThirdType : uint8;
template<> struct TIsUEnumClass<EOneEngineThirdType> { enum { Value = true }; };
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneEngineThirdType>();
// ********** End Enum EOneEngineThirdType *********************************************************

// ********** Begin Enum EOneResEventState *********************************************************
#define FOREACH_ENUM_EONERESEVENTSTATE(op) \
	op(EOneResEventState::Begin) \
	op(EOneResEventState::Success) \
	op(EOneResEventState::Failed) 

enum class EOneResEventState : uint8;
template<> struct TIsUEnumClass<EOneResEventState> { enum { Value = true }; };
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneResEventState>();
// ********** End Enum EOneResEventState ***********************************************************

// ********** Begin Enum EOnePermissionType ********************************************************
#define FOREACH_ENUM_EONEPERMISSIONTYPE(op) \
	op(EOnePermissionType::Clipboard) \
	op(EOnePermissionType::ReadExternalStorage) \
	op(EOnePermissionType::WriteExternalStorage) \
	op(EOnePermissionType::Camera) \
	op(EOnePermissionType::RecordAudio) \
	op(EOnePermissionType::CoarseLocation) \
	op(EOnePermissionType::FineLocation) \
	op(EOnePermissionType::CallPhone) \
	op(EOnePermissionType::ReadContacts) \
	op(EOnePermissionType::ReadSms) \
	op(EOnePermissionType::ReadCalendar) \
	op(EOnePermissionType::BodySensors) \
	op(EOnePermissionType::Notification) \
	op(EOnePermissionType::ATTTrack) 

enum class EOnePermissionType : uint8;
template<> struct TIsUEnumClass<EOnePermissionType> { enum { Value = true }; };
template<> ONEENGINESDK_API UEnum* StaticEnum<EOnePermissionType>();
// ********** End Enum EOnePermissionType **********************************************************

// ********** Begin Enum EOneAIHelpType ************************************************************
#define FOREACH_ENUM_EONEAIHELPTYPE(op) \
	op(EOneAIHelpType::Unknown) \
	op(EOneAIHelpType::RobotChat) \
	op(EOneAIHelpType::FAQ) 

enum class EOneAIHelpType : uint8;
template<> struct TIsUEnumClass<EOneAIHelpType> { enum { Value = true }; };
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneAIHelpType>();
// ********** End Enum EOneAIHelpType **************************************************************

// ********** Begin Enum EOneScreenOrientation *****************************************************
#define FOREACH_ENUM_EONESCREENORIENTATION(op) \
	op(EOneScreenOrientation::Unknown) \
	op(EOneScreenOrientation::Portrait) \
	op(EOneScreenOrientation::Landscape) 

enum class EOneScreenOrientation : uint8;
template<> struct TIsUEnumClass<EOneScreenOrientation> { enum { Value = true }; };
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneScreenOrientation>();
// ********** End Enum EOneScreenOrientation *******************************************************

// ********** Begin Enum EOneShareAppTarget ********************************************************
#define FOREACH_ENUM_EONESHAREAPPTARGET(op) \
	op(EOneShareAppTarget::PE_WeChatSession) \
	op(EOneShareAppTarget::PE_WeChatMoment) \
	op(EOneShareAppTarget::PE_QQ) \
	op(EOneShareAppTarget::PE_QZone) \
	op(EOneShareAppTarget::PE_Weibo) \
	op(EOneShareAppTarget::PE_Bilibili) \
	op(EOneShareAppTarget::PE_Facebook) \
	op(EOneShareAppTarget::PE_VK) \
	op(EOneShareAppTarget::PE_Instagram) \
	op(EOneShareAppTarget::PE_Twitter) \
	op(EOneShareAppTarget::PE_Line) \
	op(EOneShareAppTarget::PE_NaverGame) \
	op(EOneShareAppTarget::PE_TikTok) \
	op(EOneShareAppTarget::PE_Discord) \
	op(EOneShareAppTarget::PE_Telegram) 

enum class EOneShareAppTarget : uint8;
template<> struct TIsUEnumClass<EOneShareAppTarget> { enum { Value = true }; };
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneShareAppTarget>();
// ********** End Enum EOneShareAppTarget **********************************************************

// ********** Begin Enum EOneShareType *************************************************************
#define FOREACH_ENUM_EONESHARETYPE(op) \
	op(EOneShareType::Image) \
	op(EOneShareType::WebPage) \
	op(EOneShareType::Text) \
	op(EOneShareType::ImageSnapShot) 

enum class EOneShareType : uint8;
template<> struct TIsUEnumClass<EOneShareType> { enum { Value = true }; };
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneShareType>();
// ********** End Enum EOneShareType ***************************************************************

// ********** Begin Enum EOneEngineSDKRegionType ***************************************************
#define FOREACH_ENUM_EONEENGINESDKREGIONTYPE(op) \
	op(EOneEngineSDKRegionType::Mainland) \
	op(EOneEngineSDKRegionType::Oversea) 

enum class EOneEngineSDKRegionType : uint8;
template<> struct TIsUEnumClass<EOneEngineSDKRegionType> { enum { Value = true }; };
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneEngineSDKRegionType>();
// ********** End Enum EOneEngineSDKRegionType *****************************************************

// ********** Begin Enum EOneUnlockSafeLockResult **************************************************
#define FOREACH_ENUM_EONEUNLOCKSAFELOCKRESULT(op) \
	op(EOneUnlockSafeLockResult::WaitingToUnlock) \
	op(EOneUnlockSafeLockResult::UnlockSucceeded) \
	op(EOneUnlockSafeLockResult::UnlockTimedOut) \
	op(EOneUnlockSafeLockResult::UnlockFailed) 

enum class EOneUnlockSafeLockResult : uint8;
template<> struct TIsUEnumClass<EOneUnlockSafeLockResult> { enum { Value = true }; };
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneUnlockSafeLockResult>();
// ********** End Enum EOneUnlockSafeLockResult ****************************************************

// ********** Begin Enum EOneUnlockSafeLockType ****************************************************
#define FOREACH_ENUM_EONEUNLOCKSAFELOCKTYPE(op) \
	op(EOneUnlockSafeLockType::PushNotification) \
	op(EOneUnlockSafeLockType::DynamicCode) 

enum class EOneUnlockSafeLockType : uint8;
template<> struct TIsUEnumClass<EOneUnlockSafeLockType> { enum { Value = true }; };
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneUnlockSafeLockType>();
// ********** End Enum EOneUnlockSafeLockType ******************************************************

// ********** Begin Enum EOneNaverGameType *********************************************************
#define FOREACH_ENUM_EONENAVERGAMETYPE(op) \
	op(EOneNaverGameType::Banner) \
	op(EOneNaverGameType::Sorry) \
	op(EOneNaverGameType::Board) \
	op(EOneNaverGameType::Feed) 

enum class EOneNaverGameType : uint8;
template<> struct TIsUEnumClass<EOneNaverGameType> { enum { Value = true }; };
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneNaverGameType>();
// ********** End Enum EOneNaverGameType ***********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
