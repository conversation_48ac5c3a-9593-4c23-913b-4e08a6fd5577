// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneUserCenterIDInfo.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneUserCenterIDInfo() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterIDInfo();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterIDInfo_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_NoRegister();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FPSOneIDInfoStruct();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FPSOneIDInfoStruct ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPSOneIDInfoStruct;
class UScriptStruct* FPSOneIDInfoStruct::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPSOneIDInfoStruct.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPSOneIDInfoStruct.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPSOneIDInfoStruct, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("PSOneIDInfoStruct"));
	}
	return Z_Registration_Info_UScriptStruct_FPSOneIDInfoStruct.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterIDInfo.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterIDInfo.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ID_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterIDInfo.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FTextPropertyParams NewProp_Name;
	static const UECodeGen_Private::FTextPropertyParams NewProp_ID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPSOneIDInfoStruct>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneIDInfoStruct, Name), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Name_MetaData), NewProp_Name_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewProp_ID = { "ID", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPSOneIDInfoStruct, ID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ID_MetaData), NewProp_ID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewProp_Name,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewProp_ID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"PSOneIDInfoStruct",
	Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::PropPointers),
	sizeof(FPSOneIDInfoStruct),
	alignof(FPSOneIDInfoStruct),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPSOneIDInfoStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPSOneIDInfoStruct.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPSOneIDInfoStruct.InnerSingleton, Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPSOneIDInfoStruct.InnerSingleton;
}
// ********** End ScriptStruct FPSOneIDInfoStruct **************************************************

// ********** Begin Class UPSOneUserCenterIDInfo ***************************************************
void UPSOneUserCenterIDInfo::StaticRegisterNativesUPSOneUserCenterIDInfo()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneUserCenterIDInfo;
UClass* UPSOneUserCenterIDInfo::GetPrivateStaticClass()
{
	using TClass = UPSOneUserCenterIDInfo;
	if (!Z_Registration_Info_UClass_UPSOneUserCenterIDInfo.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneUserCenterIDInfo"),
			Z_Registration_Info_UClass_UPSOneUserCenterIDInfo.InnerSingleton,
			StaticRegisterNativesUPSOneUserCenterIDInfo,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterIDInfo.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneUserCenterIDInfo_NoRegister()
{
	return UPSOneUserCenterIDInfo::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "IncludePath", "Views/PSOneUserCenterIDInfo.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterIDInfo.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NameCell_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// AvatarCell\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterIDInfo.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AvatarCell" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IDCell_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// NickCell\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterIDInfo.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "NickCell" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NameCell;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IDCell;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterIDInfo>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::NewProp_NameCell = { "NameCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterIDInfo, NameCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NameCell_MetaData), NewProp_NameCell_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::NewProp_IDCell = { "IDCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterIDInfo, IDCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IDCell_MetaData), NewProp_IDCell_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::NewProp_NameCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::NewProp_IDCell,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::ClassParams = {
	&UPSOneUserCenterIDInfo::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneUserCenterIDInfo()
{
	if (!Z_Registration_Info_UClass_UPSOneUserCenterIDInfo.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneUserCenterIDInfo.OuterSingleton, Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterIDInfo.OuterSingleton;
}
UPSOneUserCenterIDInfo::UPSOneUserCenterIDInfo(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterIDInfo);
UPSOneUserCenterIDInfo::~UPSOneUserCenterIDInfo() {}
// ********** End Class UPSOneUserCenterIDInfo *****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterIDInfo_h__Script_OneEngineSDK_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPSOneIDInfoStruct::StaticStruct, Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewStructOps, TEXT("PSOneIDInfoStruct"), &Z_Registration_Info_UScriptStruct_FPSOneIDInfoStruct, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPSOneIDInfoStruct), 931632714U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneUserCenterIDInfo, UPSOneUserCenterIDInfo::StaticClass, TEXT("UPSOneUserCenterIDInfo"), &Z_Registration_Info_UClass_UPSOneUserCenterIDInfo, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneUserCenterIDInfo), 3404536824U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterIDInfo_h__Script_OneEngineSDK_3465928476(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterIDInfo_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterIDInfo_h__Script_OneEngineSDK_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterIDInfo_h__Script_OneEngineSDK_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterIDInfo_h__Script_OneEngineSDK_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
