// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneUserCenterRightCellSubtitle_NF.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneUserCenterRightCellSubtitle_NF() {}

// ********** Begin Cross Module References ********************************************************
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_NoRegister();
UMG_API UClass* Z_Construct_UClass_UBorder_NoRegister();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
UMG_API UClass* Z_Construct_UClass_UUserWidget();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneUserCenterRightCellSubtitle_NF *************************************
void UPSOneUserCenterRightCellSubtitle_NF::StaticRegisterNativesUPSOneUserCenterRightCellSubtitle_NF()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle_NF;
UClass* UPSOneUserCenterRightCellSubtitle_NF::GetPrivateStaticClass()
{
	using TClass = UPSOneUserCenterRightCellSubtitle_NF;
	if (!Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle_NF.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneUserCenterRightCellSubtitle_NF"),
			Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle_NF.InnerSingleton,
			StaticRegisterNativesUPSOneUserCenterRightCellSubtitle_NF,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle_NF.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_NoRegister()
{
	return UPSOneUserCenterRightCellSubtitle_NF::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * \n */" },
#endif
		{ "IncludePath", "Views/PSOneUserCenterRightCellSubtitle_NF.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle_NF" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BgBorder_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NormalTexture_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle_NF" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubTitle_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle_NF" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HiddenArrow_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle_NF" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ArrowImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FTextPropertyParams NewProp_Title;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TextBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BgBorder;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NormalTexture;
	static const UECodeGen_Private::FTextPropertyParams NewProp_SubTitle;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SubTextBlock;
	static void NewProp_HiddenArrow_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_HiddenArrow;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ArrowImage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterRightCellSubtitle_NF>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FTextPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle_NF, Title), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Title_MetaData), NewProp_Title_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_TextBlock = { "TextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle_NF, TextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextBlock_MetaData), NewProp_TextBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_BgBorder = { "BgBorder", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle_NF, BgBorder), Z_Construct_UClass_UBorder_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BgBorder_MetaData), NewProp_BgBorder_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_NormalTexture = { "NormalTexture", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle_NF, NormalTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NormalTexture_MetaData), NewProp_NormalTexture_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_SubTitle = { "SubTitle", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle_NF, SubTitle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubTitle_MetaData), NewProp_SubTitle_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_SubTextBlock = { "SubTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle_NF, SubTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubTextBlock_MetaData), NewProp_SubTextBlock_MetaData) };
void Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_HiddenArrow_SetBit(void* Obj)
{
	((UPSOneUserCenterRightCellSubtitle_NF*)Obj)->HiddenArrow = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_HiddenArrow = { "HiddenArrow", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPSOneUserCenterRightCellSubtitle_NF), &Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_HiddenArrow_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HiddenArrow_MetaData), NewProp_HiddenArrow_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_ArrowImage = { "ArrowImage", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle_NF, ArrowImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ArrowImage_MetaData), NewProp_ArrowImage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_Title,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_TextBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_BgBorder,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_NormalTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_SubTitle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_SubTextBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_HiddenArrow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_ArrowImage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::ClassParams = {
	&UPSOneUserCenterRightCellSubtitle_NF::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF()
{
	if (!Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle_NF.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle_NF.OuterSingleton, Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle_NF.OuterSingleton;
}
UPSOneUserCenterRightCellSubtitle_NF::UPSOneUserCenterRightCellSubtitle_NF(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterRightCellSubtitle_NF);
UPSOneUserCenterRightCellSubtitle_NF::~UPSOneUserCenterRightCellSubtitle_NF() {}
// ********** End Class UPSOneUserCenterRightCellSubtitle_NF ***************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellSubtitle_NF_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF, UPSOneUserCenterRightCellSubtitle_NF::StaticClass, TEXT("UPSOneUserCenterRightCellSubtitle_NF"), &Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle_NF, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneUserCenterRightCellSubtitle_NF), 1427875110U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellSubtitle_NF_h__Script_OneEngineSDK_4265279977(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellSubtitle_NF_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellSubtitle_NF_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
