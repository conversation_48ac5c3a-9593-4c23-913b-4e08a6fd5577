// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneInputCode.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneInputCode() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneConfirmButton_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneInputCode();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneInputCode_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldBase_NoRegister();
UMG_API UClass* Z_Construct_UClass_UHorizontalBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneInputCode **********************************************************
void UPSOneInputCode::StaticRegisterNativesUPSOneInputCode()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneInputCode;
UClass* UPSOneInputCode::GetPrivateStaticClass()
{
	using TClass = UPSOneInputCode;
	if (!Z_Registration_Info_UClass_UPSOneInputCode.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneInputCode"),
			Z_Registration_Info_UClass_UPSOneInputCode.InnerSingleton,
			StaticRegisterNativesUPSOneInputCode,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneInputCode.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneInputCode_NoRegister()
{
	return UPSOneInputCode::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneInputCode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n *\n */" },
#endif
		{ "IncludePath", "Views/PSOneInputCode.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputCode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputCode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TitleTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputCode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CodeTextField_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputCode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputCode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputCode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BottomHorizontalBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputCode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputCode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SendCodeIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// enter icon\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputCode.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "enter icon" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// backspace icon\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputCode.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "backspace icon" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TitleTextBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CodeTextField;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActionButton;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetTextBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BottomHorizontalBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SendCodeIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneInputCode>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputCode, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleBox_MetaData), NewProp_ScaleBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_TitleTextBlock = { "TitleTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputCode, TitleTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TitleTextBlock_MetaData), NewProp_TitleTextBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_CodeTextField = { "CodeTextField", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputCode, CodeTextField), Z_Construct_UClass_UPSOneTextFieldBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CodeTextField_MetaData), NewProp_CodeTextField_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_ActionButton = { "ActionButton", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputCode, ActionButton), Z_Construct_UClass_UPSOneConfirmButton_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionButton_MetaData), NewProp_ActionButton_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_TargetTextBlock = { "TargetTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputCode, TargetTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetTextBlock_MetaData), NewProp_TargetTextBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_BottomHorizontalBox = { "BottomHorizontalBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputCode, BottomHorizontalBox), Z_Construct_UClass_UHorizontalBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BottomHorizontalBox_MetaData), NewProp_BottomHorizontalBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputCode, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnterIcon_MetaData), NewProp_EnterIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_SendCodeIcon = { "SendCodeIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputCode, SendCodeIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SendCodeIcon_MetaData), NewProp_SendCodeIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputCode, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackspaceIcon_MetaData), NewProp_BackspaceIcon_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneInputCode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_ScaleBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_TitleTextBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_CodeTextField,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_ActionButton,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_TargetTextBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_BottomHorizontalBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_EnterIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_SendCodeIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputCode_Statics::NewProp_BackspaceIcon,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputCode_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneInputCode_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputCode_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneInputCode_Statics::ClassParams = {
	&UPSOneInputCode::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneInputCode_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputCode_Statics::PropPointers),
	0,
	0x00A010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputCode_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneInputCode_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneInputCode()
{
	if (!Z_Registration_Info_UClass_UPSOneInputCode.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneInputCode.OuterSingleton, Z_Construct_UClass_UPSOneInputCode_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneInputCode.OuterSingleton;
}
UPSOneInputCode::UPSOneInputCode(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneInputCode);
UPSOneInputCode::~UPSOneInputCode() {}
// ********** End Class UPSOneInputCode ************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputCode_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneInputCode, UPSOneInputCode::StaticClass, TEXT("UPSOneInputCode"), &Z_Registration_Info_UClass_UPSOneInputCode, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneInputCode), 859850769U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputCode_h__Script_OneEngineSDK_3445952185(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputCode_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputCode_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
