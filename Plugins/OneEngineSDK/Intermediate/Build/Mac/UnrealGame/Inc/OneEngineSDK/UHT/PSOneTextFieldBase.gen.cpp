// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneTextFieldBase.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneTextFieldBase() {}

// ********** Begin Cross Module References ********************************************************
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldBase();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldBase_NoRegister();
SLATECORE_API UEnum* Z_Construct_UEnum_SlateCore_ETextCommit();
UMG_API UClass* Z_Construct_UClass_UBorder_NoRegister();
UMG_API UClass* Z_Construct_UClass_UEditableText_NoRegister();
UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
UMG_API UClass* Z_Construct_UClass_UUserWidget();
UMG_API UEnum* Z_Construct_UEnum_UMG_EVirtualKeyboardType();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneTextFieldBase Function DelayFocus **********************************
struct Z_Construct_UFunction_UPSOneTextFieldBase_DelayFocus_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneTextFieldBase_DelayFocus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneTextFieldBase, nullptr, "DelayFocus", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneTextFieldBase_DelayFocus_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneTextFieldBase_DelayFocus_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPSOneTextFieldBase_DelayFocus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneTextFieldBase_DelayFocus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneTextFieldBase::execDelayFocus)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DelayFocus();
	P_NATIVE_END;
}
// ********** End Class UPSOneTextFieldBase Function DelayFocus ************************************

// ********** Begin Class UPSOneTextFieldBase Function InternalOnTextChanged ***********************
struct Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics
{
	struct PSOneTextFieldBase_eventInternalOnTextChanged_Parms
	{
		FText Text;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Text_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FTextPropertyParams NewProp_Text;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FTextPropertyParams Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::NewProp_Text = { "Text", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneTextFieldBase_eventInternalOnTextChanged_Parms, Text), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Text_MetaData), NewProp_Text_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::NewProp_Text,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneTextFieldBase, nullptr, "InternalOnTextChanged", Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::PSOneTextFieldBase_eventInternalOnTextChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00440401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::PSOneTextFieldBase_eventInternalOnTextChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneTextFieldBase::execInternalOnTextChanged)
{
	P_GET_PROPERTY_REF(FTextProperty,Z_Param_Out_Text);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InternalOnTextChanged(Z_Param_Out_Text);
	P_NATIVE_END;
}
// ********** End Class UPSOneTextFieldBase Function InternalOnTextChanged *************************

// ********** Begin Class UPSOneTextFieldBase Function InternalOnTextCommitted *********************
struct Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics
{
	struct PSOneTextFieldBase_eventInternalOnTextCommitted_Parms
	{
		FText Text;
		TEnumAsByte<ETextCommit::Type> CommitType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Text_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FTextPropertyParams NewProp_Text;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CommitType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FTextPropertyParams Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::NewProp_Text = { "Text", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneTextFieldBase_eventInternalOnTextCommitted_Parms, Text), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Text_MetaData), NewProp_Text_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::NewProp_CommitType = { "CommitType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneTextFieldBase_eventInternalOnTextCommitted_Parms, CommitType), Z_Construct_UEnum_SlateCore_ETextCommit, METADATA_PARAMS(0, nullptr) }; // 1817921380
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::NewProp_Text,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::NewProp_CommitType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneTextFieldBase, nullptr, "InternalOnTextCommitted", Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::PSOneTextFieldBase_eventInternalOnTextCommitted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00440401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::PSOneTextFieldBase_eventInternalOnTextCommitted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneTextFieldBase::execInternalOnTextCommitted)
{
	P_GET_PROPERTY_REF(FTextProperty,Z_Param_Out_Text);
	P_GET_PROPERTY(FByteProperty,Z_Param_CommitType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InternalOnTextCommitted(Z_Param_Out_Text,ETextCommit::Type(Z_Param_CommitType));
	P_NATIVE_END;
}
// ********** End Class UPSOneTextFieldBase Function InternalOnTextCommitted ***********************

// ********** Begin Class UPSOneTextFieldBase ******************************************************
void UPSOneTextFieldBase::StaticRegisterNativesUPSOneTextFieldBase()
{
	UClass* Class = UPSOneTextFieldBase::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "DelayFocus", &UPSOneTextFieldBase::execDelayFocus },
		{ "InternalOnTextChanged", &UPSOneTextFieldBase::execInternalOnTextChanged },
		{ "InternalOnTextCommitted", &UPSOneTextFieldBase::execInternalOnTextCommitted },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneTextFieldBase;
UClass* UPSOneTextFieldBase::GetPrivateStaticClass()
{
	using TClass = UPSOneTextFieldBase;
	if (!Z_Registration_Info_UClass_UPSOneTextFieldBase.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneTextFieldBase"),
			Z_Registration_Info_UClass_UPSOneTextFieldBase.InnerSingleton,
			StaticRegisterNativesUPSOneTextFieldBase,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneTextFieldBase.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneTextFieldBase_NoRegister()
{
	return UPSOneTextFieldBase::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneTextFieldBase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "IncludePath", "Views/PSOneTextFieldBase.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsPassword_MetaData[] = {
		{ "Category", "PSOneTextFieldBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VirtualKeyboardType_MetaData[] = {
		{ "Category", "PSOneTextFieldBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultText_MetaData[] = {
		{ "Category", "PSOneTextFieldBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HintText_MetaData[] = {
		{ "Category", "PSOneTextFieldBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BgBorder_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextField_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HintTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NormalTexture_MetaData[] = {
		{ "Category", "PSOneTextFieldBase" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// normal\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "normal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FocusTexture_MetaData[] = {
		{ "Category", "PSOneTextFieldBase" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// onFocus\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "onFocus" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsPassword_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPassword;
	static const UECodeGen_Private::FBytePropertyParams NewProp_VirtualKeyboardType;
	static const UECodeGen_Private::FTextPropertyParams NewProp_DefaultText;
	static const UECodeGen_Private::FTextPropertyParams NewProp_HintText;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BgBorder;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TextField;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HintTextBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NormalTexture;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FocusTexture;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UPSOneTextFieldBase_DelayFocus, "DelayFocus" }, // 3623817976
		{ &Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged, "InternalOnTextChanged" }, // 18452787
		{ &Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted, "InternalOnTextCommitted" }, // 3674023269
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneTextFieldBase>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_bIsPassword_SetBit(void* Obj)
{
	((UPSOneTextFieldBase*)Obj)->bIsPassword = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_bIsPassword = { "bIsPassword", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPSOneTextFieldBase), &Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_bIsPassword_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsPassword_MetaData), NewProp_bIsPassword_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_VirtualKeyboardType = { "VirtualKeyboardType", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneTextFieldBase, VirtualKeyboardType), Z_Construct_UEnum_UMG_EVirtualKeyboardType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VirtualKeyboardType_MetaData), NewProp_VirtualKeyboardType_MetaData) }; // 2483245499
const UECodeGen_Private::FTextPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_DefaultText = { "DefaultText", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneTextFieldBase, DefaultText), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultText_MetaData), NewProp_DefaultText_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_HintText = { "HintText", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneTextFieldBase, HintText), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HintText_MetaData), NewProp_HintText_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_BgBorder = { "BgBorder", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneTextFieldBase, BgBorder), Z_Construct_UClass_UBorder_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BgBorder_MetaData), NewProp_BgBorder_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_TextField = { "TextField", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneTextFieldBase, TextField), Z_Construct_UClass_UEditableText_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextField_MetaData), NewProp_TextField_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_HintTextBlock = { "HintTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneTextFieldBase, HintTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HintTextBlock_MetaData), NewProp_HintTextBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_NormalTexture = { "NormalTexture", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneTextFieldBase, NormalTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NormalTexture_MetaData), NewProp_NormalTexture_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_FocusTexture = { "FocusTexture", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneTextFieldBase, FocusTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FocusTexture_MetaData), NewProp_FocusTexture_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneTextFieldBase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_bIsPassword,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_VirtualKeyboardType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_DefaultText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_HintText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_BgBorder,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_TextField,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_HintTextBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_NormalTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_FocusTexture,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldBase_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneTextFieldBase_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldBase_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::ClassParams = {
	&UPSOneTextFieldBase::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UPSOneTextFieldBase_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldBase_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldBase_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneTextFieldBase_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneTextFieldBase()
{
	if (!Z_Registration_Info_UClass_UPSOneTextFieldBase.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneTextFieldBase.OuterSingleton, Z_Construct_UClass_UPSOneTextFieldBase_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneTextFieldBase.OuterSingleton;
}
UPSOneTextFieldBase::UPSOneTextFieldBase(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneTextFieldBase);
UPSOneTextFieldBase::~UPSOneTextFieldBase() {}
// ********** End Class UPSOneTextFieldBase ********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneTextFieldBase, UPSOneTextFieldBase::StaticClass, TEXT("UPSOneTextFieldBase"), &Z_Registration_Info_UClass_UPSOneTextFieldBase, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneTextFieldBase), 1996748100U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h__Script_OneEngineSDK_422218529(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
