// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSUserWidgetSettings.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSUserWidgetSettings() {}

// ********** Begin Cross Module References ********************************************************
ENGINE_API UClass* Z_Construct_UClass_UDataAsset();
ENGINE_API UClass* Z_Construct_UClass_UFont_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSUserWidgetSettings();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSUserWidgetSettings_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSUserWidgetSettings Function Get ***************************************
struct Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics
{
	struct PSUserWidgetSettings_eventGet_Parms
	{
		UPSUserWidgetSettings* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "DisplayName", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSUserWidgetSettings_eventGet_Parms, ReturnValue), Z_Construct_UClass_UPSUserWidgetSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSUserWidgetSettings, nullptr, "Get", Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::PSUserWidgetSettings_eventGet_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::PSUserWidgetSettings_eventGet_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSUserWidgetSettings_Get()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSUserWidgetSettings::execGet)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPSUserWidgetSettings**)Z_Param__Result=UPSUserWidgetSettings::Get();
	P_NATIVE_END;
}
// ********** End Class UPSUserWidgetSettings Function Get *****************************************

// ********** Begin Class UPSUserWidgetSettings ****************************************************
void UPSUserWidgetSettings::StaticRegisterNativesUPSUserWidgetSettings()
{
	UClass* Class = UPSUserWidgetSettings::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "Get", &UPSUserWidgetSettings::execGet },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSUserWidgetSettings;
UClass* UPSUserWidgetSettings::GetPrivateStaticClass()
{
	using TClass = UPSUserWidgetSettings;
	if (!Z_Registration_Info_UClass_UPSUserWidgetSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSUserWidgetSettings"),
			Z_Registration_Info_UClass_UPSUserWidgetSettings.InnerSingleton,
			StaticRegisterNativesUPSUserWidgetSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSUserWidgetSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSUserWidgetSettings_NoRegister()
{
	return UPSUserWidgetSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSUserWidgetSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "Views/PSUserWidgetSettings.h" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ZOrder_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// UI Z \xe8\xbd\xb4\xe9\xa1\xba\xe5\xba\x8f\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UI Z \xe8\xbd\xb4\xe9\xa1\xba\xe5\xba\x8f" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableBlurBackground_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe5\x90\xaf\xe7\x94\xa8\xe6\xa8\xa1\xe7\xb3\x8a\xe8\x83\x8c\xe6\x99\xaf\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe5\x90\xaf\xe7\x94\xa8\xe6\xa8\xa1\xe7\xb3\x8a\xe8\x83\x8c\xe6\x99\xaf" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlurBackgroundStrength_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\xa8\xa1\xe7\xb3\x8a\xe8\x83\x8c\xe6\x99\xaf\xe5\xbc\xba\xe5\xba\xa6\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\xa8\xa1\xe7\xb3\x8a\xe8\x83\x8c\xe6\x99\xaf\xe5\xbc\xba\xe5\xba\xa6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomFont_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe4\xbd\xbf\xe7\x94\xa8\xe8\x87\xaa\xe5\xae\x9a\xe4\xb9\x89\xe5\xad\x97\xe4\xbd\x93\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe4\xbd\xbf\xe7\x94\xa8\xe8\x87\xaa\xe5\xae\x9a\xe4\xb9\x89\xe5\xad\x97\xe4\xbd\x93" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AgreementPromptBackgroundTexture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe5\xba\x94\xe8\xaf\xa5\xe8\xa6\x81\xe8\xa2\xab\xe5\xba\x9f\xe5\xbc\x83\xe4\xba\x86\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xba\x94\xe8\xaf\xa5\xe8\xa6\x81\xe8\xa2\xab\xe5\xba\x9f\xe5\xbc\x83\xe4\xba\x86" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnterButtonAssignCircle_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PSCircleTexture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PSCrossTexture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PSSquareTexture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PSTriangleTexture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PSL1Texture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PSR1Texture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PSR2Texture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ZOrder;
	static void NewProp_bEnableBlurBackground_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableBlurBackground;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlurBackgroundStrength;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CustomFont;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AgreementPromptBackgroundTexture;
	static void NewProp_bEnterButtonAssignCircle_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnterButtonAssignCircle;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PSCircleTexture;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PSCrossTexture;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PSSquareTexture;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PSTriangleTexture;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PSL1Texture;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PSR1Texture;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PSR2Texture;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UPSUserWidgetSettings_Get, "Get" }, // 488928175
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSUserWidgetSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_ZOrder = { "ZOrder", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSUserWidgetSettings, ZOrder), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ZOrder_MetaData), NewProp_ZOrder_MetaData) };
void Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnableBlurBackground_SetBit(void* Obj)
{
	((UPSUserWidgetSettings*)Obj)->bEnableBlurBackground = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnableBlurBackground = { "bEnableBlurBackground", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPSUserWidgetSettings), &Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnableBlurBackground_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableBlurBackground_MetaData), NewProp_bEnableBlurBackground_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_BlurBackgroundStrength = { "BlurBackgroundStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSUserWidgetSettings, BlurBackgroundStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlurBackgroundStrength_MetaData), NewProp_BlurBackgroundStrength_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_CustomFont = { "CustomFont", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSUserWidgetSettings, CustomFont), Z_Construct_UClass_UFont_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomFont_MetaData), NewProp_CustomFont_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_AgreementPromptBackgroundTexture = { "AgreementPromptBackgroundTexture", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSUserWidgetSettings, AgreementPromptBackgroundTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AgreementPromptBackgroundTexture_MetaData), NewProp_AgreementPromptBackgroundTexture_MetaData) };
void Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnterButtonAssignCircle_SetBit(void* Obj)
{
	((UPSUserWidgetSettings*)Obj)->bEnterButtonAssignCircle = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnterButtonAssignCircle = { "bEnterButtonAssignCircle", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPSUserWidgetSettings), &Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnterButtonAssignCircle_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnterButtonAssignCircle_MetaData), NewProp_bEnterButtonAssignCircle_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSCircleTexture = { "PSCircleTexture", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSUserWidgetSettings, PSCircleTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PSCircleTexture_MetaData), NewProp_PSCircleTexture_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSCrossTexture = { "PSCrossTexture", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSUserWidgetSettings, PSCrossTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PSCrossTexture_MetaData), NewProp_PSCrossTexture_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSSquareTexture = { "PSSquareTexture", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSUserWidgetSettings, PSSquareTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PSSquareTexture_MetaData), NewProp_PSSquareTexture_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSTriangleTexture = { "PSTriangleTexture", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSUserWidgetSettings, PSTriangleTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PSTriangleTexture_MetaData), NewProp_PSTriangleTexture_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSL1Texture = { "PSL1Texture", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSUserWidgetSettings, PSL1Texture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PSL1Texture_MetaData), NewProp_PSL1Texture_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSR1Texture = { "PSR1Texture", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSUserWidgetSettings, PSR1Texture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PSR1Texture_MetaData), NewProp_PSR1Texture_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSR2Texture = { "PSR2Texture", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSUserWidgetSettings, PSR2Texture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PSR2Texture_MetaData), NewProp_PSR2Texture_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSUserWidgetSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_ZOrder,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnableBlurBackground,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_BlurBackgroundStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_CustomFont,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_AgreementPromptBackgroundTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnterButtonAssignCircle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSCircleTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSCrossTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSSquareTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSTriangleTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSL1Texture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSR1Texture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSR2Texture,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSUserWidgetSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UDataAsset,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::ClassParams = {
	&UPSUserWidgetSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UPSUserWidgetSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::PropPointers),
	0,
	0x000000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSUserWidgetSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSUserWidgetSettings()
{
	if (!Z_Registration_Info_UClass_UPSUserWidgetSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSUserWidgetSettings.OuterSingleton, Z_Construct_UClass_UPSUserWidgetSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSUserWidgetSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSUserWidgetSettings);
UPSUserWidgetSettings::~UPSUserWidgetSettings() {}
// ********** End Class UPSUserWidgetSettings ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSUserWidgetSettings_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSUserWidgetSettings, UPSUserWidgetSettings::StaticClass, TEXT("UPSUserWidgetSettings"), &Z_Registration_Info_UClass_UPSUserWidgetSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSUserWidgetSettings), 2186114185U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSUserWidgetSettings_h__Script_OneEngineSDK_4134732000(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSUserWidgetSettings_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSUserWidgetSettings_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
