// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneUserCenterDeviceManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneUserCenterDeviceManager() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterDeviceManager();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterDeviceManager_NoRegister();
ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneOnlineDevice();
UMG_API UClass* Z_Construct_UClass_UScrollBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FOneOnlineDevice **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FOneOnlineDevice;
class UScriptStruct* FOneOnlineDevice::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FOneOnlineDevice.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FOneOnlineDevice.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneOnlineDevice, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneOnlineDevice"));
	}
	return Z_Registration_Info_UScriptStruct_FOneOnlineDevice.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FOneOnlineDevice_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterDeviceManager.h" },
	};
#endif // WITH_METADATA
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneOnlineDevice>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneOnlineDevice_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	&NewStructOps,
	"OneOnlineDevice",
	nullptr,
	0,
	sizeof(FOneOnlineDevice),
	alignof(FOneOnlineDevice),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneOnlineDevice_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FOneOnlineDevice_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FOneOnlineDevice()
{
	if (!Z_Registration_Info_UScriptStruct_FOneOnlineDevice.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FOneOnlineDevice.InnerSingleton, Z_Construct_UScriptStruct_FOneOnlineDevice_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FOneOnlineDevice.InnerSingleton;
}
// ********** End ScriptStruct FOneOnlineDevice ****************************************************

// ********** Begin Class UPSOneUserCenterDeviceManager ********************************************
void UPSOneUserCenterDeviceManager::StaticRegisterNativesUPSOneUserCenterDeviceManager()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneUserCenterDeviceManager;
UClass* UPSOneUserCenterDeviceManager::GetPrivateStaticClass()
{
	using TClass = UPSOneUserCenterDeviceManager;
	if (!Z_Registration_Info_UClass_UPSOneUserCenterDeviceManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneUserCenterDeviceManager"),
			Z_Registration_Info_UClass_UPSOneUserCenterDeviceManager.InnerSingleton,
			StaticRegisterNativesUPSOneUserCenterDeviceManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterDeviceManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneUserCenterDeviceManager_NoRegister()
{
	return UPSOneUserCenterDeviceManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n *\n */" },
#endif
		{ "IncludePath", "Views/PSOneUserCenterDeviceManager.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterDeviceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TitleBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterDeviceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScrollBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterDeviceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TitleBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ScrollBox;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterDeviceManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::NewProp_TitleBlock = { "TitleBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterDeviceManager, TitleBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TitleBlock_MetaData), NewProp_TitleBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::NewProp_ScrollBox = { "ScrollBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterDeviceManager, ScrollBox), Z_Construct_UClass_UScrollBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScrollBox_MetaData), NewProp_ScrollBox_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::NewProp_TitleBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::NewProp_ScrollBox,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::ClassParams = {
	&UPSOneUserCenterDeviceManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneUserCenterDeviceManager()
{
	if (!Z_Registration_Info_UClass_UPSOneUserCenterDeviceManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneUserCenterDeviceManager.OuterSingleton, Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterDeviceManager.OuterSingleton;
}
UPSOneUserCenterDeviceManager::UPSOneUserCenterDeviceManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterDeviceManager);
UPSOneUserCenterDeviceManager::~UPSOneUserCenterDeviceManager() {}
// ********** End Class UPSOneUserCenterDeviceManager **********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterDeviceManager_h__Script_OneEngineSDK_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FOneOnlineDevice::StaticStruct, Z_Construct_UScriptStruct_FOneOnlineDevice_Statics::NewStructOps, TEXT("OneOnlineDevice"), &Z_Registration_Info_UScriptStruct_FOneOnlineDevice, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FOneOnlineDevice), 1681410197U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneUserCenterDeviceManager, UPSOneUserCenterDeviceManager::StaticClass, TEXT("UPSOneUserCenterDeviceManager"), &Z_Registration_Info_UClass_UPSOneUserCenterDeviceManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneUserCenterDeviceManager), 1884429471U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterDeviceManager_h__Script_OneEngineSDK_2022125910(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterDeviceManager_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterDeviceManager_h__Script_OneEngineSDK_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterDeviceManager_h__Script_OneEngineSDK_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterDeviceManager_h__Script_OneEngineSDK_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
