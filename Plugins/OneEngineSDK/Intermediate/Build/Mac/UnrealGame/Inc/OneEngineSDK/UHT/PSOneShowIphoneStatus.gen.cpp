// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneShowIphoneStatus.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneShowIphoneStatus() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneConfirmButton_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneShowIphoneStatus();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneShowIphoneStatus_NoRegister();
UMG_API UClass* Z_Construct_UClass_UHorizontalBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneShowIphoneStatus ***************************************************
void UPSOneShowIphoneStatus::StaticRegisterNativesUPSOneShowIphoneStatus()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneShowIphoneStatus;
UClass* UPSOneShowIphoneStatus::GetPrivateStaticClass()
{
	using TClass = UPSOneShowIphoneStatus;
	if (!Z_Registration_Info_UClass_UPSOneShowIphoneStatus.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneShowIphoneStatus"),
			Z_Registration_Info_UClass_UPSOneShowIphoneStatus.InnerSingleton,
			StaticRegisterNativesUPSOneShowIphoneStatus,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneShowIphoneStatus.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneShowIphoneStatus_NoRegister()
{
	return UPSOneShowIphoneStatus::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneShowIphoneStatus_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n *\n */" },
#endif
		{ "IncludePath", "Views/PSOneShowIphoneStatus.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TitleTextBlock_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x98\xbe\xe7\xa4\xba\xe7\x9a\x84\xe5\x8f\xb7\xe7\xa0\x81\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x98\xbe\xe7\xa4\xba\xe7\x9a\x84\xe5\x8f\xb7\xe7\xa0\x81" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BindDescTextBlock_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\xbb\x91\xe5\xae\x9a\xe6\x8f\x8f\xe8\xbf\xb0\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\xbb\x91\xe5\xae\x9a\xe6\x8f\x8f\xe8\xbf\xb0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BindTextBlock_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe7\xbb\x91\xe5\xae\x9a\xe6\xad\xa3\xe6\x96\x87\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\xbb\x91\xe5\xae\x9a\xe6\xad\xa3\xe6\x96\x87" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// enter icon\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "enter icon" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// backspace icon\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "backspace icon" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnbindBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnbindIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HideUnbind_MetaData[] = {
		{ "Category", "PSOneShowIphoneStatus" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TitleTextBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BindDescTextBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BindTextBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActionButton;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_UnbindBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_UnbindIcon;
	static void NewProp_HideUnbind_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_HideUnbind;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneShowIphoneStatus>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleBox_MetaData), NewProp_ScaleBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_TitleTextBlock = { "TitleTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, TitleTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TitleTextBlock_MetaData), NewProp_TitleTextBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BindDescTextBlock = { "BindDescTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, BindDescTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BindDescTextBlock_MetaData), NewProp_BindDescTextBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BindTextBlock = { "BindTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, BindTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BindTextBlock_MetaData), NewProp_BindTextBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_ActionButton = { "ActionButton", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, ActionButton), Z_Construct_UClass_UPSOneConfirmButton_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionButton_MetaData), NewProp_ActionButton_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnterIcon_MetaData), NewProp_EnterIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackspaceIcon_MetaData), NewProp_BackspaceIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_UnbindBox = { "UnbindBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, UnbindBox), Z_Construct_UClass_UHorizontalBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnbindBox_MetaData), NewProp_UnbindBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_UnbindIcon = { "UnbindIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, UnbindIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnbindIcon_MetaData), NewProp_UnbindIcon_MetaData) };
void Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_HideUnbind_SetBit(void* Obj)
{
	((UPSOneShowIphoneStatus*)Obj)->HideUnbind = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_HideUnbind = { "HideUnbind", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPSOneShowIphoneStatus), &Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_HideUnbind_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HideUnbind_MetaData), NewProp_HideUnbind_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_ScaleBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_TitleTextBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BindDescTextBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BindTextBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_ActionButton,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_EnterIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BackspaceIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_UnbindBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_UnbindIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_HideUnbind,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::ClassParams = {
	&UPSOneShowIphoneStatus::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::PropPointers),
	0,
	0x00A010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneShowIphoneStatus()
{
	if (!Z_Registration_Info_UClass_UPSOneShowIphoneStatus.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneShowIphoneStatus.OuterSingleton, Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneShowIphoneStatus.OuterSingleton;
}
UPSOneShowIphoneStatus::UPSOneShowIphoneStatus(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneShowIphoneStatus);
UPSOneShowIphoneStatus::~UPSOneShowIphoneStatus() {}
// ********** End Class UPSOneShowIphoneStatus *****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneShowIphoneStatus_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneShowIphoneStatus, UPSOneShowIphoneStatus::StaticClass, TEXT("UPSOneShowIphoneStatus"), &Z_Registration_Info_UClass_UPSOneShowIphoneStatus, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneShowIphoneStatus), 2306676262U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneShowIphoneStatus_h__Script_OneEngineSDK_2215171884(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneShowIphoneStatus_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneShowIphoneStatus_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
