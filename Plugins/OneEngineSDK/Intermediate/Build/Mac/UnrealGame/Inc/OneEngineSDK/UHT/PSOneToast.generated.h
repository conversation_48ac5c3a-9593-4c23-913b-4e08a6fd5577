// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSOneToast.h"

#ifdef ONEENGINESDK_PSOneToast_generated_h
#error "PSOneToast.generated.h already included, missing '#pragma once' in PSOneToast.h"
#endif
#define ONEENGINESDK_PSOneToast_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UPSOneToast **************************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneToast_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneToast_h_15_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneToast(); \
	friend struct Z_Construct_UClass_UPSOneToast_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneToast_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSOneToast, UPSOneScaleWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSOneToast_NoRegister) \
	DECLARE_SERIALIZER(UPSOneToast)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneToast_h_15_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneToast(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSOneToast(UPSOneToast&&) = delete; \
	UPSOneToast(const UPSOneToast&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneToast); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneToast); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneToast) \
	NO_API virtual ~UPSOneToast();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneToast_h_12_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneToast_h_15_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneToast_h_15_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneToast_h_15_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSOneToast;

// ********** End Class UPSOneToast ****************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneToast_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
