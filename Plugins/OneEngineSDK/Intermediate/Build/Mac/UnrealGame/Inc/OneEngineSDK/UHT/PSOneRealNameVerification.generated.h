// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSOneRealNameVerification.h"

#ifdef ONEENGINESDK_PSOneRealNameVerification_generated_h
#error "PSOneRealNameVerification.generated.h already included, missing '#pragma once' in PSOneRealNameVerification.h"
#endif
#define ONEENGINESDK_PSOneRealNameVerification_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UPSOneRealNameVerification ***********************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneRealNameVerification_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneRealNameVerification(); \
	friend struct Z_Construct_UClass_UPSOneRealNameVerification_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneRealNameVerification_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSOneRealNameVerification, UPSOneFocusUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSOneRealNameVerification_NoRegister) \
	DECLARE_SERIALIZER(UPSOneRealNameVerification)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneRealNameVerification(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSOneRealNameVerification(UPSOneRealNameVerification&&) = delete; \
	UPSOneRealNameVerification(const UPSOneRealNameVerification&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneRealNameVerification); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneRealNameVerification); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneRealNameVerification) \
	NO_API virtual ~UPSOneRealNameVerification();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_13_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSOneRealNameVerification;

// ********** End Class UPSOneRealNameVerification *************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
