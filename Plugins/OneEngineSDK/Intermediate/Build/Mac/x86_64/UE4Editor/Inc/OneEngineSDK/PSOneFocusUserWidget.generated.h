// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ONEENGINESDK_PSOneFocusUserWidget_generated_h
#error "PSOneFocusUserWidget.generated.h already included, missing '#pragma once' in PSOneFocusUserWidget.h"
#endif
#define ONEENGINESDK_PSOneFocusUserWidget_generated_h

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_SPARSE_DATA
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_RPC_WRAPPERS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_RPC_WRAPPERS_NO_PURE_DECLS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	ONEENGINESDK_API UPSOneSaveFocusWidgetInterface(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_ABSTRACT_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneSaveFocusWidgetInterface) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(ONEENGINESDK_API, UPSOneSaveFocusWidgetInterface); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneSaveFocusWidgetInterface); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ONEENGINESDK_API UPSOneSaveFocusWidgetInterface(UPSOneSaveFocusWidgetInterface&&); \
	ONEENGINESDK_API UPSOneSaveFocusWidgetInterface(const UPSOneSaveFocusWidgetInterface&); \
public:


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	ONEENGINESDK_API UPSOneSaveFocusWidgetInterface(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()) : Super(ObjectInitializer) { }; \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ONEENGINESDK_API UPSOneSaveFocusWidgetInterface(UPSOneSaveFocusWidgetInterface&&); \
	ONEENGINESDK_API UPSOneSaveFocusWidgetInterface(const UPSOneSaveFocusWidgetInterface&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(ONEENGINESDK_API, UPSOneSaveFocusWidgetInterface); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneSaveFocusWidgetInterface); \
	DEFINE_ABSTRACT_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneSaveFocusWidgetInterface)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_GENERATED_UINTERFACE_BODY() \
private: \
	static void StaticRegisterNativesUPSOneSaveFocusWidgetInterface(); \
	friend struct Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics; \
public: \
	DECLARE_CLASS(UPSOneSaveFocusWidgetInterface, UInterface, COMPILED_IN_FLAGS(CLASS_Abstract | CLASS_Interface), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), ONEENGINESDK_API) \
	DECLARE_SERIALIZER(UPSOneSaveFocusWidgetInterface)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_GENERATED_BODY_LEGACY \
		PRAGMA_DISABLE_DEPRECATION_WARNINGS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_GENERATED_UINTERFACE_BODY() \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_STANDARD_CONSTRUCTORS \
	PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_GENERATED_BODY \
	PRAGMA_DISABLE_DEPRECATION_WARNINGS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_GENERATED_UINTERFACE_BODY() \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_ENHANCED_CONSTRUCTORS \
private: \
	PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_INCLASS_IINTERFACE_NO_PURE_DECLS \
protected: \
	virtual ~IPSOneSaveFocusWidgetInterface() {} \
public: \
	typedef UPSOneSaveFocusWidgetInterface UClassType; \
	typedef IPSOneSaveFocusWidgetInterface ThisClass; \
	virtual UObject* _getUObject() const { check(0 && "Missing required implementation."); return nullptr; }


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_INCLASS_IINTERFACE \
protected: \
	virtual ~IPSOneSaveFocusWidgetInterface() {} \
public: \
	typedef UPSOneSaveFocusWidgetInterface UClassType; \
	typedef IPSOneSaveFocusWidgetInterface ThisClass; \
	virtual UObject* _getUObject() const { check(0 && "Missing required implementation."); return nullptr; }


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_10_PROLOG
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_18_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_RPC_WRAPPERS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_INCLASS_IINTERFACE \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_18_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_RPC_WRAPPERS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_13_INCLASS_IINTERFACE_NO_PURE_DECLS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ONEENGINESDK_API UClass* StaticClass<class UPSOneSaveFocusWidgetInterface>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_SPARSE_DATA
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_RPC_WRAPPERS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_RPC_WRAPPERS_NO_PURE_DECLS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneFocusUserWidget(); \
	friend struct Z_Construct_UClass_UPSOneFocusUserWidget_Statics; \
public: \
	DECLARE_CLASS(UPSOneFocusUserWidget, UUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneFocusUserWidget)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_INCLASS \
private: \
	static void StaticRegisterNativesUPSOneFocusUserWidget(); \
	friend struct Z_Construct_UClass_UPSOneFocusUserWidget_Statics; \
public: \
	DECLARE_CLASS(UPSOneFocusUserWidget, UUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneFocusUserWidget)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneFocusUserWidget(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneFocusUserWidget) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneFocusUserWidget); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneFocusUserWidget); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneFocusUserWidget(UPSOneFocusUserWidget&&); \
	NO_API UPSOneFocusUserWidget(const UPSOneFocusUserWidget&); \
public:


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneFocusUserWidget(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()) : Super(ObjectInitializer) { }; \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneFocusUserWidget(UPSOneFocusUserWidget&&); \
	NO_API UPSOneFocusUserWidget(const UPSOneFocusUserWidget&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneFocusUserWidget); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneFocusUserWidget); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneFocusUserWidget)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_PRIVATE_PROPERTY_OFFSET
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_24_PROLOG
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_RPC_WRAPPERS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_INCLASS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_RPC_WRAPPERS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_INCLASS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h_27_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ONEENGINESDK_API UClass* StaticClass<class UPSOneFocusUserWidget>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
