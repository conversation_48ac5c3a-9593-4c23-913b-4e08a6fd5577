// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneShowIphoneStatus.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneShowIphoneStatus() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneShowIphoneStatus_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneShowIphoneStatus();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneConfirmButton_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UHorizontalBox_NoRegister();
// End Cross Module References
	void UPSOneShowIphoneStatus::StaticRegisterNativesUPSOneShowIphoneStatus()
	{
	}
	UClass* Z_Construct_UClass_UPSOneShowIphoneStatus_NoRegister()
	{
		return UPSOneShowIphoneStatus::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneShowIphoneStatus_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TitleTextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_TitleTextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BindDescTextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BindDescTextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BindTextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BindTextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ActionButton_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ActionButton;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UnbindBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_UnbindBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UnbindIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_UnbindIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_HideUnbind_MetaData[];
#endif
		static void NewProp_HideUnbind_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_HideUnbind;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n *\n */" },
		{ "IncludePath", "Views/PSOneShowIphoneStatus.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_ScaleBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_ScaleBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_TitleTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe6\x98\xbe\xe7\xa4\xba\xe7\x9a\x84\xe5\x8f\xb7\xe7\xa0\x81\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
		{ "ToolTip", "\xe6\x98\xbe\xe7\xa4\xba\xe7\x9a\x84\xe5\x8f\xb7\xe7\xa0\x81" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_TitleTextBlock = { "TitleTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, TitleTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_TitleTextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_TitleTextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BindDescTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe7\xbb\x91\xe5\xae\x9a\xe6\x8f\x8f\xe8\xbf\xb0\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
		{ "ToolTip", "\xe7\xbb\x91\xe5\xae\x9a\xe6\x8f\x8f\xe8\xbf\xb0" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BindDescTextBlock = { "BindDescTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, BindDescTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BindDescTextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BindDescTextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BindTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe7\xbb\x91\xe5\xae\x9a\xe6\xad\xa3\xe6\x96\x87\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
		{ "ToolTip", "\xe7\xbb\x91\xe5\xae\x9a\xe6\xad\xa3\xe6\x96\x87" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BindTextBlock = { "BindTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, BindTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BindTextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BindTextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_ActionButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_ActionButton = { "ActionButton", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, ActionButton), Z_Construct_UClass_UPSOneConfirmButton_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_ActionButton_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_ActionButton_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// enter icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
		{ "ToolTip", "enter icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_EnterIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_EnterIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// backspace icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
		{ "ToolTip", "backspace icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BackspaceIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BackspaceIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_UnbindBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_UnbindBox = { "UnbindBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, UnbindBox), Z_Construct_UClass_UHorizontalBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_UnbindBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_UnbindBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_UnbindIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_UnbindIcon = { "UnbindIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneShowIphoneStatus, UnbindIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_UnbindIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_UnbindIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_HideUnbind_MetaData[] = {
		{ "Category", "PSOneShowIphoneStatus" },
		{ "ModuleRelativePath", "Private/Views/PSOneShowIphoneStatus.h" },
	};
#endif
	void Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_HideUnbind_SetBit(void* Obj)
	{
		((UPSOneShowIphoneStatus*)Obj)->HideUnbind = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_HideUnbind = { "HideUnbind", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UPSOneShowIphoneStatus), &Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_HideUnbind_SetBit, METADATA_PARAMS(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_HideUnbind_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_HideUnbind_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_ScaleBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_TitleTextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BindDescTextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BindTextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_ActionButton,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_EnterIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_BackspaceIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_UnbindBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_UnbindIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::NewProp_HideUnbind,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneShowIphoneStatus>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::ClassParams = {
		&UPSOneShowIphoneStatus::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::PropPointers),
		0,
		0x00A010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneShowIphoneStatus()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneShowIphoneStatus_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneShowIphoneStatus, 90942009);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneShowIphoneStatus>()
	{
		return UPSOneShowIphoneStatus::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneShowIphoneStatus(Z_Construct_UClass_UPSOneShowIphoneStatus, &UPSOneShowIphoneStatus::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneShowIphoneStatus"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneShowIphoneStatus);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
