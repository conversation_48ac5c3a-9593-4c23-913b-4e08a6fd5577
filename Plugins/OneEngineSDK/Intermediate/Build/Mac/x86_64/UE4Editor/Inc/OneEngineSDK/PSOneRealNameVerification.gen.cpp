// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneRealNameVerification.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneRealNameVerification() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneRealNameVerification_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneRealNameVerification();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UBackgroundBlur_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldBase_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneConfirmButton_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
// End Cross Module References
	void UPSOneRealNameVerification::StaticRegisterNativesUPSOneRealNameVerification()
	{
	}
	UClass* Z_Construct_UClass_UPSOneRealNameVerification_NoRegister()
	{
		return UPSOneRealNameVerification::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneRealNameVerification_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BackgroundBlur_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BackgroundBlur;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NameTextField_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_NameTextField;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_IDTextField_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_IDTextField;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SubmitButton_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_SubmitButton;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneRealNameVerification_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneRealNameVerification_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n *\n */" },
		{ "IncludePath", "Views/PSOneRealNameVerification.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneRealNameVerification.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneRealNameVerification.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneRealNameVerification, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_ScaleBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_ScaleBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_BackgroundBlur_MetaData[] = {
		{ "bindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneRealNameVerification.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_BackgroundBlur = { "BackgroundBlur", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneRealNameVerification, BackgroundBlur), Z_Construct_UClass_UBackgroundBlur_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_BackgroundBlur_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_BackgroundBlur_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_NameTextField_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneRealNameVerification.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_NameTextField = { "NameTextField", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneRealNameVerification, NameTextField), Z_Construct_UClass_UPSOneTextFieldBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_NameTextField_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_NameTextField_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_IDTextField_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneRealNameVerification.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_IDTextField = { "IDTextField", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneRealNameVerification, IDTextField), Z_Construct_UClass_UPSOneTextFieldBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_IDTextField_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_IDTextField_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_SubmitButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneRealNameVerification.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_SubmitButton = { "SubmitButton", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneRealNameVerification, SubmitButton), Z_Construct_UClass_UPSOneConfirmButton_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_SubmitButton_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_SubmitButton_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe7\xa1\xae\xe8\xae\xa4 \xe5\x9b\xbe\xe6\xa0\x87\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneRealNameVerification.h" },
		{ "ToolTip", "\xe7\xa1\xae\xe8\xae\xa4 \xe5\x9b\xbe\xe6\xa0\x87" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneRealNameVerification, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_EnterIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_EnterIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe8\xbf\x94\xe5\x9b\x9e \xe5\x9b\xbe\xe6\xa0\x87\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneRealNameVerification.h" },
		{ "ToolTip", "\xe8\xbf\x94\xe5\x9b\x9e \xe5\x9b\xbe\xe6\xa0\x87" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneRealNameVerification, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_BackspaceIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_BackspaceIcon_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneRealNameVerification_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_ScaleBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_BackgroundBlur,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_NameTextField,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_IDTextField,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_SubmitButton,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_EnterIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneRealNameVerification_Statics::NewProp_BackspaceIcon,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneRealNameVerification_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneRealNameVerification>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneRealNameVerification_Statics::ClassParams = {
		&UPSOneRealNameVerification::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneRealNameVerification_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneRealNameVerification_Statics::PropPointers),
		0,
		0x00A010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneRealNameVerification_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneRealNameVerification_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneRealNameVerification()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneRealNameVerification_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneRealNameVerification, 619227424);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneRealNameVerification>()
	{
		return UPSOneRealNameVerification::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneRealNameVerification(Z_Construct_UClass_UPSOneRealNameVerification, &UPSOneRealNameVerification::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneRealNameVerification"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneRealNameVerification);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
