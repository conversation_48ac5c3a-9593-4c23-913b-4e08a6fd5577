// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ONEENGINESDK_PSOneFocusCheckButton_generated_h
#error "PSOneFocusCheckButton.generated.h already included, missing '#pragma once' in PSOneFocusCheckButton.h"
#endif
#define ONEENGINESDK_PSOneFocusCheckButton_generated_h

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_SPARSE_DATA
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_RPC_WRAPPERS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_RPC_WRAPPERS_NO_PURE_DECLS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneFocusCheckButton(); \
	friend struct Z_Construct_UClass_UPSOneFocusCheckButton_Statics; \
public: \
	DECLARE_CLASS(UPSOneFocusCheckButton, UPSOneButtonBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneFocusCheckButton)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_INCLASS \
private: \
	static void StaticRegisterNativesUPSOneFocusCheckButton(); \
	friend struct Z_Construct_UClass_UPSOneFocusCheckButton_Statics; \
public: \
	DECLARE_CLASS(UPSOneFocusCheckButton, UPSOneButtonBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneFocusCheckButton)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneFocusCheckButton(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneFocusCheckButton) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneFocusCheckButton); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneFocusCheckButton); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneFocusCheckButton(UPSOneFocusCheckButton&&); \
	NO_API UPSOneFocusCheckButton(const UPSOneFocusCheckButton&); \
public:


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneFocusCheckButton(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()) : Super(ObjectInitializer) { }; \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneFocusCheckButton(UPSOneFocusCheckButton&&); \
	NO_API UPSOneFocusCheckButton(const UPSOneFocusCheckButton&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneFocusCheckButton); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneFocusCheckButton); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneFocusCheckButton)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_PRIVATE_PROPERTY_OFFSET
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_12_PROLOG
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_RPC_WRAPPERS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_INCLASS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_RPC_WRAPPERS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_INCLASS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h_15_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ONEENGINESDK_API UClass* StaticClass<class UPSOneFocusCheckButton>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
