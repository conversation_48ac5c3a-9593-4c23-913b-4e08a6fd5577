// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneConfirmButton.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneConfirmButton() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneConfirmButton_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneConfirmButton();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// End Cross Module References
	void UPSOneConfirmButton::StaticRegisterNativesUPSOneConfirmButton()
	{
	}
	UClass* Z_Construct_UClass_UPSOneConfirmButton_NoRegister()
	{
		return UPSOneConfirmButton::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneConfirmButton_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneConfirmButton_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneButtonBase,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneConfirmButton_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n * \n */" },
		{ "IncludePath", "Views/PSOneConfirmButton.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneConfirmButton.h" },
	};
#endif
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneConfirmButton_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneConfirmButton>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneConfirmButton_Statics::ClassParams = {
		&UPSOneConfirmButton::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		nullptr,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		0,
		0,
		0x00A010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneConfirmButton_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneConfirmButton_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneConfirmButton()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneConfirmButton_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneConfirmButton, 3061934198);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneConfirmButton>()
	{
		return UPSOneConfirmButton::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneConfirmButton(Z_Construct_UClass_UPSOneConfirmButton, &UPSOneConfirmButton::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneConfirmButton"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneConfirmButton);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
