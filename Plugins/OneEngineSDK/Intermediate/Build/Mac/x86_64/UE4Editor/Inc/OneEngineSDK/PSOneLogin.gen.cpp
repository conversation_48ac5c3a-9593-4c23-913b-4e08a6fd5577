// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneLogin.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneLogin() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneLogin_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneLogin();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UBackgroundBlur_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UVerticalBox_NoRegister();
	UMG_API UClass* Z_Construct_UClass_USizeBox_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldBase_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UWidgetSwitcher_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldVerificationCode_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusCheckButton_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneConfirmButton_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UHorizontalBox_NoRegister();
	ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
// End Cross Module References
	void UPSOneLogin::StaticRegisterNativesUPSOneLogin()
	{
	}
	UClass* Z_Construct_UClass_UPSOneLogin_NoRegister()
	{
		return UPSOneLogin::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneLogin_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BackgroundBlur_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BackgroundBlur;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ContentVerticalBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ContentVerticalBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ContentSizeBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ContentSizeBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_LoginVerticalBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_LoginVerticalBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_VerticalSplitLine_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_VerticalSplitLine;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_QRCodeVerticalBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_QRCodeVerticalBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_LogoIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_LogoIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_LoginTypeTextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_LoginTypeTextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AccountField_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_AccountField;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PasswordSwitcher_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_PasswordSwitcher;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PasswordField_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_PasswordField;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_VerificationCodeField_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_VerificationCodeField;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AgreePolicyCheckButton_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_AgreePolicyCheckButton;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_LoginButton_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_LoginButton;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_QRCodeImage_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_QRCodeImage;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_RefreshQRCodeImage_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_RefreshQRCodeImage;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_LoginTypeSwitcher_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_LoginTypeSwitcher;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SendCodeIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_SendCodeIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CodeLoginIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_CodeLoginIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AccountLoginIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_AccountLoginIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_EmailSwitchLoginHorizontalBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_EmailSwitchLoginHorizontalBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SwitchLoginTypeTextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_SwitchLoginTypeTextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bIsMainLand_MetaData[];
#endif
		static void NewProp_bIsMainLand_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsMainLand;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_MainLandContentWidth_MetaData[];
#endif
		static const UE4CodeGen_Private::FFloatPropertyParams NewProp_MainLandContentWidth;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_GlobalContentWidth_MetaData[];
#endif
		static const UE4CodeGen_Private::FFloatPropertyParams NewProp_GlobalContentWidth;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bIsLoginWithPassword_MetaData[];
#endif
		static void NewProp_bIsLoginWithPassword_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsLoginWithPassword;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bIsSupportEmailPasswordLogin_MetaData[];
#endif
		static void NewProp_bIsSupportEmailPasswordLogin_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsSupportEmailPasswordLogin;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bIsIwPlay_MetaData[];
#endif
		static void NewProp_bIsIwPlay_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsIwPlay;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PwrdLogo_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_PwrdLogo;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_IwPlayLogo_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_IwPlayLogo;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneLogin_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n * \xe7\x99\xbb\xe5\xbd\x95\xe7\x95\x8c\xe9\x9d\xa2\xe6\x8e\xa7\xe4\xbb\xb6\n * \xe6\x94\xaf\xe6\x8c\x81\xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xe5\x92\x8c\xe6\xb5\xb7\xe5\xa4\x96\xe7\x89\x88\xe4\xb8\xa4\xe7\xa7\x8d\xe6\xa8\xa1\xe5\xbc\x8f\n * \xe6\x94\xaf\xe6\x8c\x81\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe5\x92\x8c\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe4\xb8\xa4\xe7\xa7\x8d\xe6\x96\xb9\xe5\xbc\x8f\n * \xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xe8\xbf\x98\xe6\x94\xaf\xe6\x8c\x81\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\n */" },
		{ "IncludePath", "Views/PSOneLogin.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe7\x99\xbb\xe5\xbd\x95\xe7\x95\x8c\xe9\x9d\xa2\xe6\x8e\xa7\xe4\xbb\xb6\n\xe6\x94\xaf\xe6\x8c\x81\xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xe5\x92\x8c\xe6\xb5\xb7\xe5\xa4\x96\xe7\x89\x88\xe4\xb8\xa4\xe7\xa7\x8d\xe6\xa8\xa1\xe5\xbc\x8f\n\xe6\x94\xaf\xe6\x8c\x81\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe5\x92\x8c\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe4\xb8\xa4\xe7\xa7\x8d\xe6\x96\xb9\xe5\xbc\x8f\n\xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xe8\xbf\x98\xe6\x94\xaf\xe6\x8c\x81\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ScaleBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ScaleBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_BackgroundBlur_MetaData[] = {
		{ "bindWidget", "" },
		{ "Comment", "// \xe8\x83\x8c\xe6\x99\xaf\xe6\xa8\xa1\xe7\xb3\x8a\xe7\xbb\x84\xe4\xbb\xb6\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe8\x83\x8c\xe6\x99\xaf\xe6\xa8\xa1\xe7\xb3\x8a\xe7\xbb\x84\xe4\xbb\xb6" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_BackgroundBlur = { "BackgroundBlur", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, BackgroundBlur), Z_Construct_UClass_UBackgroundBlur_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_BackgroundBlur_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_BackgroundBlur_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ContentVerticalBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe5\x86\x85\xe5\xae\xb9\xe5\x8c\x85\xe8\xa3\xb9\xe5\x99\xa8\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe5\x9e\x82\xe7\x9b\xb4\xe5\xb8\x83\xe5\xb1\x80\xe5\x86\x85\xe5\xae\xb9\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe5\x86\x85\xe5\xae\xb9\xe5\x8c\x85\xe8\xa3\xb9\xe5\x99\xa8\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe5\x9e\x82\xe7\x9b\xb4\xe5\xb8\x83\xe5\xb1\x80\xe5\x86\x85\xe5\xae\xb9" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ContentVerticalBox = { "ContentVerticalBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, ContentVerticalBox), Z_Construct_UClass_UVerticalBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ContentVerticalBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ContentVerticalBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ContentSizeBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe5\x86\x85\xe5\xae\xb9\xe5\xb0\xba\xe5\xaf\xb8\xe7\x9b\x92\xef\xbc\x8c\xe6\xa0\xb9\xe6\x8d\xae\xe7\x89\x88\xe6\x9c\xac\xe8\xae\xbe\xe7\xbd\xae\xe4\xb8\x8d\xe5\x90\x8c\xe7\x9a\x84\xe5\xae\xbd\xe5\xba\xa6 (860 for mainland, 680 for global)\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe5\x86\x85\xe5\xae\xb9\xe5\xb0\xba\xe5\xaf\xb8\xe7\x9b\x92\xef\xbc\x8c\xe6\xa0\xb9\xe6\x8d\xae\xe7\x89\x88\xe6\x9c\xac\xe8\xae\xbe\xe7\xbd\xae\xe4\xb8\x8d\xe5\x90\x8c\xe7\x9a\x84\xe5\xae\xbd\xe5\xba\xa6 (860 for mainland, 680 for global)" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ContentSizeBox = { "ContentSizeBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, ContentSizeBox), Z_Construct_UClass_USizeBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ContentSizeBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ContentSizeBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginVerticalBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe7\x99\xbb\xe5\xbd\x95\xe5\x9e\x82\xe7\x9b\xb4\xe6\xa1\x86\xef\xbc\x8c\xe5\x8c\x85\xe5\x90\xab\xe7\x99\xbb\xe5\xbd\x95\xe7\x9b\xb8\xe5\x85\xb3\xe6\x8e\xa7\xe4\xbb\xb6\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe7\x99\xbb\xe5\xbd\x95\xe5\x9e\x82\xe7\x9b\xb4\xe6\xa1\x86\xef\xbc\x8c\xe5\x8c\x85\xe5\x90\xab\xe7\x99\xbb\xe5\xbd\x95\xe7\x9b\xb8\xe5\x85\xb3\xe6\x8e\xa7\xe4\xbb\xb6" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginVerticalBox = { "LoginVerticalBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, LoginVerticalBox), Z_Construct_UClass_UVerticalBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginVerticalBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginVerticalBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_VerticalSplitLine_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe5\x9e\x82\xe7\x9b\xb4\xe5\x88\x86\xe5\x89\xb2\xe7\xba\xbf\xef\xbc\x8c\xe5\x88\x86\xe9\x9a\x94\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x99\xbb\xe5\xbd\x95\xe5\x92\x8c\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe5\x9e\x82\xe7\x9b\xb4\xe5\x88\x86\xe5\x89\xb2\xe7\xba\xbf\xef\xbc\x8c\xe5\x88\x86\xe9\x9a\x94\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x99\xbb\xe5\xbd\x95\xe5\x92\x8c\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_VerticalSplitLine = { "VerticalSplitLine", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, VerticalSplitLine), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_VerticalSplitLine_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_VerticalSplitLine_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_QRCodeVerticalBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe5\x9e\x82\xe7\x9b\xb4\xe6\xa1\x86\xef\xbc\x8c\xe5\x8c\x85\xe5\x90\xab\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe7\x9b\xb8\xe5\x85\xb3\xe6\x8e\xa7\xe4\xbb\xb6\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe5\x9e\x82\xe7\x9b\xb4\xe6\xa1\x86\xef\xbc\x8c\xe5\x8c\x85\xe5\x90\xab\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe7\x9b\xb8\xe5\x85\xb3\xe6\x8e\xa7\xe4\xbb\xb6" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_QRCodeVerticalBox = { "QRCodeVerticalBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, QRCodeVerticalBox), Z_Construct_UClass_UVerticalBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_QRCodeVerticalBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_QRCodeVerticalBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LogoIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// logo \xe5\x9b\xbe\xe6\xa0\x87\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "logo \xe5\x9b\xbe\xe6\xa0\x87" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LogoIcon = { "LogoIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, LogoIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LogoIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LogoIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginTypeTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe7\x99\xbb\xe5\xbd\x95\xe7\xb1\xbb\xe5\x9e\x8b\xe6\x96\x87\xe6\x9c\xac\xef\xbc\x8c\xe6\x98\xbe\xe7\xa4\xba\xe5\xbd\x93\xe5\x89\x8d\xe7\x99\xbb\xe5\xbd\x95\xe6\x96\xb9\xe5\xbc\x8f\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe7\x99\xbb\xe5\xbd\x95\xe7\xb1\xbb\xe5\x9e\x8b\xe6\x96\x87\xe6\x9c\xac\xef\xbc\x8c\xe6\x98\xbe\xe7\xa4\xba\xe5\xbd\x93\xe5\x89\x8d\xe7\x99\xbb\xe5\xbd\x95\xe6\x96\xb9\xe5\xbc\x8f" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginTypeTextBlock = { "LoginTypeTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, LoginTypeTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginTypeTextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginTypeTextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AccountField_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe8\xb4\xa6\xe5\x8f\xb7\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe8\xb4\xa6\xe5\x8f\xb7\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AccountField = { "AccountField", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, AccountField), Z_Construct_UClass_UPSOneTextFieldBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AccountField_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AccountField_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PasswordSwitcher_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe5\xaf\x86\xe7\xa0\x81\xe5\x88\x87\xe6\x8d\xa2\xe5\x99\xa8\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe5\x88\x87\xe6\x8d\xa2\xe5\xaf\x86\xe7\xa0\x81\xe5\x92\x8c\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe5\xaf\x86\xe7\xa0\x81\xe5\x88\x87\xe6\x8d\xa2\xe5\x99\xa8\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe5\x88\x87\xe6\x8d\xa2\xe5\xaf\x86\xe7\xa0\x81\xe5\x92\x8c\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PasswordSwitcher = { "PasswordSwitcher", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, PasswordSwitcher), Z_Construct_UClass_UWidgetSwitcher_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PasswordSwitcher_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PasswordSwitcher_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PasswordField_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe5\xaf\x86\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe5\xaf\x86\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PasswordField = { "PasswordField", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, PasswordField), Z_Construct_UClass_UPSOneTextFieldBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PasswordField_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PasswordField_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_VerificationCodeField_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_VerificationCodeField = { "VerificationCodeField", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, VerificationCodeField), Z_Construct_UClass_UPSOneTextFieldVerificationCode_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_VerificationCodeField_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_VerificationCodeField_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AgreePolicyCheckButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe9\x98\x85\xe8\xaf\xbb\xe5\xb9\xb6\xe5\x90\x8c\xe6\x84\x8f\xe5\x8d\x8f\xe8\xae\xae\xe5\xa4\x8d\xe9\x80\x89\xe6\xa1\x86\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe9\x98\x85\xe8\xaf\xbb\xe5\xb9\xb6\xe5\x90\x8c\xe6\x84\x8f\xe5\x8d\x8f\xe8\xae\xae\xe5\xa4\x8d\xe9\x80\x89\xe6\xa1\x86" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AgreePolicyCheckButton = { "AgreePolicyCheckButton", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, AgreePolicyCheckButton), Z_Construct_UClass_UPSOneFocusCheckButton_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AgreePolicyCheckButton_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AgreePolicyCheckButton_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe7\x99\xbb\xe5\xbd\x95\xe6\x8c\x89\xe9\x92\xae\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe7\x99\xbb\xe5\xbd\x95\xe6\x8c\x89\xe9\x92\xae" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginButton = { "LoginButton", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, LoginButton), Z_Construct_UClass_UPSOneConfirmButton_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginButton_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginButton_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_QRCodeImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe5\x9b\xbe\xe5\x83\x8f\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe5\x9b\xbe\xe5\x83\x8f" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_QRCodeImage = { "QRCodeImage", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, QRCodeImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_QRCodeImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_QRCodeImage_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_RefreshQRCodeImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe5\x88\xb7\xe6\x96\xb0\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe5\x9b\xbe\xe5\x83\x8f\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe5\x88\xb7\xe6\x96\xb0\xe4\xba\x8c\xe7\xbb\xb4\xe7\xa0\x81\xe5\x9b\xbe\xe5\x83\x8f" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_RefreshQRCodeImage = { "RefreshQRCodeImage", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, RefreshQRCodeImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_RefreshQRCodeImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_RefreshQRCodeImage_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginTypeSwitcher_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe7\x99\xbb\xe5\xbd\x95\xe7\xb1\xbb\xe5\x9e\x8b\xe5\x88\x87\xe6\x8d\xa2\xe5\x99\xa8\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe7\x99\xbb\xe5\xbd\x95\xe7\xb1\xbb\xe5\x9e\x8b\xe5\x88\x87\xe6\x8d\xa2\xe5\x99\xa8" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginTypeSwitcher = { "LoginTypeSwitcher", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, LoginTypeSwitcher), Z_Construct_UClass_UWidgetSwitcher_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginTypeSwitcher_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginTypeSwitcher_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_SendCodeIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe5\x8f\x91\xe9\x80\x81\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe6\x96\xb9\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe5\x8f\x91\xe9\x80\x81\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe6\x96\xb9\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_SendCodeIcon = { "SendCodeIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, SendCodeIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_SendCodeIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_SendCodeIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_CodeLoginIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe4\xb8\x89\xe8\xa7\x92\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe4\xb8\x89\xe8\xa7\x92\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_CodeLoginIcon = { "CodeLoginIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, CodeLoginIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_CodeLoginIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_CodeLoginIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AccountLoginIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe8\xb4\xa6\xe5\x8f\xb7\xe7\x99\xbb\xe5\xbd\x95\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe4\xb8\x89\xe8\xa7\x92\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x99\xbb\xe5\xbd\x95\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe4\xb8\x89\xe8\xa7\x92\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AccountLoginIcon = { "AccountLoginIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, AccountLoginIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AccountLoginIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AccountLoginIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_EmailSwitchLoginHorizontalBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe9\x82\xae\xe7\xae\xb1\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe6\xb0\xb4\xe5\xb9\xb3\xe6\xa1\x86\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe9\x82\xae\xe7\xae\xb1\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xe6\xb0\xb4\xe5\xb9\xb3\xe6\xa1\x86" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_EmailSwitchLoginHorizontalBox = { "EmailSwitchLoginHorizontalBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, EmailSwitchLoginHorizontalBox), Z_Construct_UClass_UHorizontalBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_EmailSwitchLoginHorizontalBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_EmailSwitchLoginHorizontalBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_SwitchLoginTypeTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe5\x88\x87\xe6\x8d\xa2\xe7\x99\xbb\xe5\xbd\x95\xe6\x96\xb9\xe5\xbc\x8f\xe6\x96\x87\xe6\x9c\xac\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe5\x88\x87\xe6\x8d\xa2\xe7\x99\xbb\xe5\xbd\x95\xe6\x96\xb9\xe5\xbc\x8f\xe6\x96\x87\xe6\x9c\xac" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_SwitchLoginTypeTextBlock = { "SwitchLoginTypeTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, SwitchLoginTypeTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_SwitchLoginTypeTextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_SwitchLoginTypeTextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe7\xa1\xae\xe8\xae\xa4\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe5\x9c\x86\xe5\xbd\xa2\xe6\x88\x96\xe5\x8f\x89\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe7\xa1\xae\xe8\xae\xa4\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe5\x9c\x86\xe5\xbd\xa2\xe6\x88\x96\xe5\x8f\x89\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_EnterIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_EnterIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe8\xbf\x94\xe5\x9b\x9e\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe5\x9c\x86\xe5\xbd\xa2\xe6\x88\x96\xe5\x8f\x89\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe8\xbf\x94\xe5\x9b\x9e\xe5\x9b\xbe\xe6\xa0\x87\xef\xbc\x88\xe5\x9c\x86\xe5\xbd\xa2\xe6\x88\x96\xe5\x8f\x89\xe5\xbd\xa2\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x89" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_BackspaceIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_BackspaceIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsMainLand_MetaData[] = {
		{ "Category", "PSOneLogin" },
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba\xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xef\xbc\x8c\xe5\xbd\xb1\xe5\x93\x8d\xe7\x99\xbb\xe5\xbd\x95\xe7\x95\x8c\xe9\x9d\xa2\xe7\x9a\x84\xe5\xb8\x83\xe5\xb1\x80\xe5\x92\x8c\xe6\x8f\x90\xe7\xa4\xba\xe6\x96\x87\xe6\x9c\xac\n" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba\xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xef\xbc\x8c\xe5\xbd\xb1\xe5\x93\x8d\xe7\x99\xbb\xe5\xbd\x95\xe7\x95\x8c\xe9\x9d\xa2\xe7\x9a\x84\xe5\xb8\x83\xe5\xb1\x80\xe5\x92\x8c\xe6\x8f\x90\xe7\xa4\xba\xe6\x96\x87\xe6\x9c\xac" },
	};
#endif
	void Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsMainLand_SetBit(void* Obj)
	{
		((UPSOneLogin*)Obj)->bIsMainLand = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsMainLand = { "bIsMainLand", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UPSOneLogin), &Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsMainLand_SetBit, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsMainLand_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsMainLand_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_MainLandContentWidth_MetaData[] = {
		{ "Category", "PSOneLogin" },
		{ "Comment", "// \xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xe5\x86\x85\xe5\xae\xb9\xe5\xae\xbd\xe5\xba\xa6\n" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe5\xa4\xa7\xe9\x99\x86\xe7\x89\x88\xe5\x86\x85\xe5\xae\xb9\xe5\xae\xbd\xe5\xba\xa6" },
	};
#endif
	const UE4CodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_MainLandContentWidth = { "MainLandContentWidth", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, MainLandContentWidth), METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_MainLandContentWidth_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_MainLandContentWidth_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_GlobalContentWidth_MetaData[] = {
		{ "Category", "PSOneLogin" },
		{ "Comment", "// \xe6\xb5\xb7\xe5\xa4\x96\xe7\x89\x88\xe5\x86\x85\xe5\xae\xb9\xe5\xae\xbd\xe5\xba\xa6\n" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe6\xb5\xb7\xe5\xa4\x96\xe7\x89\x88\xe5\x86\x85\xe5\xae\xb9\xe5\xae\xbd\xe5\xba\xa6" },
	};
#endif
	const UE4CodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_GlobalContentWidth = { "GlobalContentWidth", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, GlobalContentWidth), METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_GlobalContentWidth_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_GlobalContentWidth_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsLoginWithPassword_MetaData[] = {
		{ "Category", "PSOneLogin" },
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe4\xbd\xbf\xe7\x94\xa8\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xef\xbc\x8c""false \xe8\xa1\xa8\xe7\xa4\xba\xe4\xbd\xbf\xe7\x94\xa8\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\n" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe4\xbd\xbf\xe7\x94\xa8\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\xef\xbc\x8c""false \xe8\xa1\xa8\xe7\xa4\xba\xe4\xbd\xbf\xe7\x94\xa8\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95" },
	};
#endif
	void Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsLoginWithPassword_SetBit(void* Obj)
	{
		((UPSOneLogin*)Obj)->bIsLoginWithPassword = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsLoginWithPassword = { "bIsLoginWithPassword", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UPSOneLogin), &Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsLoginWithPassword_SetBit, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsLoginWithPassword_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsLoginWithPassword_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsSupportEmailPasswordLogin_MetaData[] = {
		{ "Category", "PSOneLogin" },
		{ "Comment", "// \xe6\x94\xaf\xe6\x8c\x81\xe9\x82\xae\xe7\xae\xb1\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95\n" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
		{ "ToolTip", "\xe6\x94\xaf\xe6\x8c\x81\xe9\x82\xae\xe7\xae\xb1\xe5\xaf\x86\xe7\xa0\x81\xe7\x99\xbb\xe5\xbd\x95" },
	};
#endif
	void Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsSupportEmailPasswordLogin_SetBit(void* Obj)
	{
		((UPSOneLogin*)Obj)->bIsSupportEmailPasswordLogin = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsSupportEmailPasswordLogin = { "bIsSupportEmailPasswordLogin", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UPSOneLogin), &Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsSupportEmailPasswordLogin_SetBit, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsSupportEmailPasswordLogin_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsSupportEmailPasswordLogin_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsIwPlay_MetaData[] = {
		{ "Category", "PSOneLogin" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
	};
#endif
	void Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsIwPlay_SetBit(void* Obj)
	{
		((UPSOneLogin*)Obj)->bIsIwPlay = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsIwPlay = { "bIsIwPlay", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UPSOneLogin), &Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsIwPlay_SetBit, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsIwPlay_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsIwPlay_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PwrdLogo_MetaData[] = {
		{ "Category", "PSOneLogin" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PwrdLogo = { "PwrdLogo", nullptr, (EPropertyFlags)0x0020080000000014, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, PwrdLogo), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PwrdLogo_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PwrdLogo_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneLogin_Statics::NewProp_IwPlayLogo_MetaData[] = {
		{ "Category", "PSOneLogin" },
		{ "ModuleRelativePath", "Private/Views/PSOneLogin.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLogin_Statics::NewProp_IwPlayLogo = { "IwPlayLogo", nullptr, (EPropertyFlags)0x0020080000000014, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneLogin, IwPlayLogo), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_IwPlayLogo_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::NewProp_IwPlayLogo_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneLogin_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ScaleBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_BackgroundBlur,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ContentVerticalBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_ContentSizeBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginVerticalBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_VerticalSplitLine,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_QRCodeVerticalBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LogoIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginTypeTextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AccountField,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PasswordSwitcher,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PasswordField,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_VerificationCodeField,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AgreePolicyCheckButton,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginButton,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_QRCodeImage,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_RefreshQRCodeImage,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_LoginTypeSwitcher,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_SendCodeIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_CodeLoginIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_AccountLoginIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_EmailSwitchLoginHorizontalBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_SwitchLoginTypeTextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_EnterIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_BackspaceIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsMainLand,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_MainLandContentWidth,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_GlobalContentWidth,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsLoginWithPassword,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsSupportEmailPasswordLogin,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_bIsIwPlay,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_PwrdLogo,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLogin_Statics::NewProp_IwPlayLogo,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneLogin_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneLogin>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneLogin_Statics::ClassParams = {
		&UPSOneLogin::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneLogin_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::PropPointers),
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneLogin_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLogin_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneLogin()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneLogin_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneLogin, 174086777);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneLogin>()
	{
		return UPSOneLogin::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneLogin(Z_Construct_UClass_UPSOneLogin, &UPSOneLogin::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneLogin"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneLogin);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
