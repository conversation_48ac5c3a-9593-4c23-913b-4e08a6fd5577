// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class UPSOneUIManager;
#ifdef ONEENGINESDK_PSOneUIManager_generated_h
#error "PSOneUIManager.generated.h already included, missing '#pragma once' in PSOneUIManager.h"
#endif
#define ONEENGINESDK_PSOneUIManager_generated_h

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_SPARSE_DATA
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_RPC_WRAPPERS \
 \
	DECLARE_FUNCTION(execHideDelTips); \
	DECLARE_FUNCTION(execShowDelTips); \
	DECLARE_FUNCTION(execHideUserCenter); \
	DECLARE_FUNCTION(execShowUserCenter); \
	DECLARE_FUNCTION(execHideRealName); \
	DECLARE_FUNCTION(execShowRealName); \
	DECLARE_FUNCTION(execHideLogin); \
	DECLARE_FUNCTION(execShowLogin); \
	DECLARE_FUNCTION(execSwitchToPreparePage); \
	DECLARE_FUNCTION(execHideUserAgreement); \
	DECLARE_FUNCTION(execShowUserAgreement); \
	DECLARE_FUNCTION(execShowLoginSuccessHint); \
	DECLARE_FUNCTION(execShowToast); \
	DECLARE_FUNCTION(execHideLoading); \
	DECLARE_FUNCTION(execShowLoading); \
	DECLARE_FUNCTION(execGet);


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_RPC_WRAPPERS_NO_PURE_DECLS \
 \
	DECLARE_FUNCTION(execHideDelTips); \
	DECLARE_FUNCTION(execShowDelTips); \
	DECLARE_FUNCTION(execHideUserCenter); \
	DECLARE_FUNCTION(execShowUserCenter); \
	DECLARE_FUNCTION(execHideRealName); \
	DECLARE_FUNCTION(execShowRealName); \
	DECLARE_FUNCTION(execHideLogin); \
	DECLARE_FUNCTION(execShowLogin); \
	DECLARE_FUNCTION(execSwitchToPreparePage); \
	DECLARE_FUNCTION(execHideUserAgreement); \
	DECLARE_FUNCTION(execShowUserAgreement); \
	DECLARE_FUNCTION(execShowLoginSuccessHint); \
	DECLARE_FUNCTION(execShowToast); \
	DECLARE_FUNCTION(execHideLoading); \
	DECLARE_FUNCTION(execShowLoading); \
	DECLARE_FUNCTION(execGet);


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneUIManager(); \
	friend struct Z_Construct_UClass_UPSOneUIManager_Statics; \
public: \
	DECLARE_CLASS(UPSOneUIManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneUIManager)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_INCLASS \
private: \
	static void StaticRegisterNativesUPSOneUIManager(); \
	friend struct Z_Construct_UClass_UPSOneUIManager_Statics; \
public: \
	DECLARE_CLASS(UPSOneUIManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneUIManager)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneUIManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneUIManager) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneUIManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneUIManager); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneUIManager(UPSOneUIManager&&); \
	NO_API UPSOneUIManager(const UPSOneUIManager&); \
public:


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneUIManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()) : Super(ObjectInitializer) { }; \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneUIManager(UPSOneUIManager&&); \
	NO_API UPSOneUIManager(const UPSOneUIManager&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneUIManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneUIManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneUIManager)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_PRIVATE_PROPERTY_OFFSET
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_24_PROLOG
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_RPC_WRAPPERS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_INCLASS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_RPC_WRAPPERS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_INCLASS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ONEENGINESDK_API UClass* StaticClass<class UPSOneUIManager>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
