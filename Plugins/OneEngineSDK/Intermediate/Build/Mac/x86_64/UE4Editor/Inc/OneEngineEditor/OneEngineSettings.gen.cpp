// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineEditor/Public/OneEngineSettings.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeOneEngineSettings() {}
// Cross Module References
	ONEENGINEEDITOR_API UEnum* Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig();
	UPackage* Z_Construct_UPackage__Script_OneEngineEditor();
	ONEENGINEEDITOR_API UClass* Z_Construct_UClass_UOneEngineSettings_NoRegister();
	ONEENGINEEDITOR_API UClass* Z_Construct_UClass_UOneEngineSettings();
	COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
// End Cross Module References
	static UEnum* ESDKRegionConfig_StaticEnum()
	{
		static UEnum* Singleton = nullptr;
		if (!Singleton)
		{
			Singleton = GetStaticEnum(Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig, Z_Construct_UPackage__Script_OneEngineEditor(), TEXT("ESDKRegionConfig"));
		}
		return Singleton;
	}
	template<> ONEENGINEEDITOR_API UEnum* StaticEnum<ESDKRegionConfig>()
	{
		return ESDKRegionConfig_StaticEnum();
	}
	static FCompiledInDeferEnum Z_CompiledInDeferEnum_UEnum_ESDKRegionConfig(ESDKRegionConfig_StaticEnum, TEXT("/Script/OneEngineEditor"), TEXT("ESDKRegionConfig"), false, nullptr, nullptr);
	uint32 Get_Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig_Hash() { return 2967623135U; }
	UEnum* Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig()
	{
#if WITH_HOT_RELOAD
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineEditor();
		static UEnum* ReturnEnum = FindExistingEnumIfHotReloadOrDynamic(Outer, TEXT("ESDKRegionConfig"), 0, Get_Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig_Hash(), false);
#else
		static UEnum* ReturnEnum = nullptr;
#endif // WITH_HOT_RELOAD
		if (!ReturnEnum)
		{
			static const UE4CodeGen_Private::FEnumeratorParam Enumerators[] = {
				{ "ESDKRegionConfig::Mainland", (int64)ESDKRegionConfig::Mainland },
				{ "ESDKRegionConfig::Oversea", (int64)ESDKRegionConfig::Oversea },
			};
#if WITH_METADATA
			const UE4CodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
				{ "BlueprintType", "true" },
				{ "Mainland.Name", "ESDKRegionConfig::Mainland" },
				{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
				{ "Oversea.Name", "ESDKRegionConfig::Oversea" },
			};
#endif
			static const UE4CodeGen_Private::FEnumParams EnumParams = {
				(UObject*(*)())Z_Construct_UPackage__Script_OneEngineEditor,
				nullptr,
				"ESDKRegionConfig",
				"ESDKRegionConfig",
				Enumerators,
				UE_ARRAY_COUNT(Enumerators),
				RF_Public|RF_Transient|RF_MarkAsNative,
				EEnumFlags::None,
				UE4CodeGen_Private::EDynamicType::NotDynamic,
				(uint8)UEnum::ECppForm::EnumClass,
				METADATA_PARAMS(Enum_MetaDataParams, UE_ARRAY_COUNT(Enum_MetaDataParams))
			};
			UE4CodeGen_Private::ConstructUEnum(ReturnEnum, EnumParams);
		}
		return ReturnEnum;
	}
	void UOneEngineSettings::StaticRegisterNativesUOneEngineSettings()
	{
	}
	UClass* Z_Construct_UClass_UOneEngineSettings_NoRegister()
	{
		return UOneEngineSettings::StaticClass();
	}
	struct Z_Construct_UClass_UOneEngineSettings_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FBytePropertyParams NewProp_SDKRegion_Underlying;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SDKRegion_MetaData[];
#endif
		static const UE4CodeGen_Private::FEnumPropertyParams NewProp_SDKRegion;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AppID_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_AppID;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Env_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Env;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_OneAppKey_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_OneAppKey;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PSMainlandConfigData_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_PSMainlandConfigData;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PSOverseaConfigData_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_PSOverseaConfigData;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_HarmonyOSConfigFilePath_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_HarmonyOSConfigFilePath;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bShowSplashBackGroundImage_MetaData[];
#endif
		static void NewProp_bShowSplashBackGroundImage_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bShowSplashBackGroundImage;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SplashShowTime_MetaData[];
#endif
		static const UE4CodeGen_Private::FInt64PropertyParams NewProp_SplashShowTime;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UOneEngineSettings_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UObject,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineEditor,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UOneEngineSettings_Statics::Class_MetaDataParams[] = {
		{ "IncludePath", "OneEngineSettings.h" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FBytePropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SDKRegion_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UE4CodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, nullptr, METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SDKRegion_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SDKRegion = { "SDKRegion", nullptr, (EPropertyFlags)0x0010000000004001, UE4CodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UOneEngineSettings, SDKRegion), Z_Construct_UEnum_OneEngineEditor_ESDKRegionConfig, METADATA_PARAMS(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SDKRegion_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SDKRegion_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_AppID_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_AppID = { "AppID", nullptr, (EPropertyFlags)0x0010000000004001, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UOneEngineSettings, AppID), METADATA_PARAMS(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_AppID_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_AppID_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_Env_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
		{ "ToolTip", "\xe9\x85\x8d\xe7\xbd\xae\xe6\x96\x87\xe4\xbb\xb6\xe7\x89\x88\xe6\x9c\xac\xe5\x8f\xb7\xef\xbc\x8c\xe9\x9c\x80\xe5\x92\x8c""Dev\xe9\x85\x8d\xe7\xbd\xae\xe4\xb8\x80\xe8\x87\xb4" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_Env = { "Env", nullptr, (EPropertyFlags)0x0010000000004001, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UOneEngineSettings, Env), METADATA_PARAMS(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_Env_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_Env_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_OneAppKey_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "EditCondition", "SDKRegion == ESDKRegionConfig::Mainland" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_OneAppKey = { "OneAppKey", nullptr, (EPropertyFlags)0x0010000000004001, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UOneEngineSettings, OneAppKey), METADATA_PARAMS(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_OneAppKey_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_OneAppKey_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_PSMainlandConfigData_MetaData[] = {
		{ "Category", "PS" },
		{ "DisplayName", "PSMainlandConfigData" },
		{ "EditCondition", "SDKRegion == ESDKRegionConfig::Mainland" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_PSMainlandConfigData = { "PSMainlandConfigData", nullptr, (EPropertyFlags)0x0010000000004001, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UOneEngineSettings, PSMainlandConfigData), METADATA_PARAMS(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_PSMainlandConfigData_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_PSMainlandConfigData_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_PSOverseaConfigData_MetaData[] = {
		{ "Category", "PS" },
		{ "DisplayName", "PSOverseaConfigData" },
		{ "EditCondition", "SDKRegion == ESDKRegionConfig::Oversea" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_PSOverseaConfigData = { "PSOverseaConfigData", nullptr, (EPropertyFlags)0x0010000000004001, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UOneEngineSettings, PSOverseaConfigData), METADATA_PARAMS(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_PSOverseaConfigData_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_PSOverseaConfigData_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_HarmonyOSConfigFilePath_MetaData[] = {
		{ "Category", "HarmonyOS" },
		{ "DisplayName", "LaohuConfig" },
		{ "EditCondition", "SDKRegion == ESDKRegionConfig::Mainland" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_HarmonyOSConfigFilePath = { "HarmonyOSConfigFilePath", nullptr, (EPropertyFlags)0x0010000000004001, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UOneEngineSettings, HarmonyOSConfigFilePath), METADATA_PARAMS(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_HarmonyOSConfigFilePath_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_HarmonyOSConfigFilePath_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_bShowSplashBackGroundImage_MetaData[] = {
		{ "Category", "Android" },
		{ "DisplayName", "\xe6\x98\xaf\xe5\x90\xa6\xe5\x90\xaf\xe7\x94\xa8\xe5\x90\xaf\xe5\x8a\xa8\xe9\xa1\xb5\xe8\x83\x8c\xe6\x99\xaf\xe5\x9b\xbe" },
		{ "EditCondition", "SDKRegion == ESDKRegionConfig::Mainland" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
#endif
	void Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_bShowSplashBackGroundImage_SetBit(void* Obj)
	{
		((UOneEngineSettings*)Obj)->bShowSplashBackGroundImage = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_bShowSplashBackGroundImage = { "bShowSplashBackGroundImage", nullptr, (EPropertyFlags)0x0010000000004001, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UOneEngineSettings), &Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_bShowSplashBackGroundImage_SetBit, METADATA_PARAMS(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_bShowSplashBackGroundImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_bShowSplashBackGroundImage_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SplashShowTime_MetaData[] = {
		{ "Category", "Android" },
		{ "DisplayName", "\xe5\x90\xaf\xe5\x8a\xa8\xe8\x83\x8c\xe6\x99\xaf\xe5\x9b\xbe\xe6\x98\xbe\xe7\xa4\xba\xe6\x97\xb6\xe9\x97\xb4 \xe5\x8d\x95\xe4\xbd\x8dms,\xe9\xbb\x98\xe8\xae\xa4""500ms" },
		{ "EditCondition", "SDKRegion == ESDKRegionConfig::Mainland" },
		{ "ModuleRelativePath", "Public/OneEngineSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FInt64PropertyParams Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SplashShowTime = { "SplashShowTime", nullptr, (EPropertyFlags)0x0010000000004001, UE4CodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UOneEngineSettings, SplashShowTime), METADATA_PARAMS(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SplashShowTime_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SplashShowTime_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UOneEngineSettings_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SDKRegion_Underlying,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SDKRegion,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_AppID,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_Env,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_OneAppKey,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_PSMainlandConfigData,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_PSOverseaConfigData,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_HarmonyOSConfigFilePath,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_bShowSplashBackGroundImage,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOneEngineSettings_Statics::NewProp_SplashShowTime,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UOneEngineSettings_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UOneEngineSettings>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UOneEngineSettings_Statics::ClassParams = {
		&UOneEngineSettings::StaticClass,
		"Game",
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UOneEngineSettings_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSettings_Statics::PropPointers),
		0,
		0x001000A6u,
		METADATA_PARAMS(Z_Construct_UClass_UOneEngineSettings_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSettings_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UOneEngineSettings()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UOneEngineSettings_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UOneEngineSettings, 3653544357);
	template<> ONEENGINEEDITOR_API UClass* StaticClass<UOneEngineSettings>()
	{
		return UOneEngineSettings::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UOneEngineSettings(Z_Construct_UClass_UOneEngineSettings, &UOneEngineSettings::StaticClass, TEXT("/Script/OneEngineEditor"), TEXT("UOneEngineSettings"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UOneEngineSettings);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
