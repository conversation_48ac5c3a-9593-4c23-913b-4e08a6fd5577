// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
struct FPSOneAreaCode;
#ifdef ONEENGINESDK_PSOneSelectAreaCode_generated_h
#error "PSOneSelectAreaCode.generated.h already included, missing '#pragma once' in PSOneSelectAreaCode.h"
#endif
#define ONEENGINESDK_PSOneSelectAreaCode_generated_h

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_12_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPSOneAreaCode_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FPSOneAreaCode>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_SPARSE_DATA
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_RPC_WRAPPERS \
 \
	DECLARE_FUNCTION(execBindData);


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_RPC_WRAPPERS_NO_PURE_DECLS \
 \
	DECLARE_FUNCTION(execBindData);


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneSelectAreaCode(); \
	friend struct Z_Construct_UClass_UPSOneSelectAreaCode_Statics; \
public: \
	DECLARE_CLASS(UPSOneSelectAreaCode, UPSOneFocusUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneSelectAreaCode)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_INCLASS \
private: \
	static void StaticRegisterNativesUPSOneSelectAreaCode(); \
	friend struct Z_Construct_UClass_UPSOneSelectAreaCode_Statics; \
public: \
	DECLARE_CLASS(UPSOneSelectAreaCode, UPSOneFocusUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneSelectAreaCode)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneSelectAreaCode(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneSelectAreaCode) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneSelectAreaCode); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneSelectAreaCode); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneSelectAreaCode(UPSOneSelectAreaCode&&); \
	NO_API UPSOneSelectAreaCode(const UPSOneSelectAreaCode&); \
public:


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneSelectAreaCode(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()) : Super(ObjectInitializer) { }; \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneSelectAreaCode(UPSOneSelectAreaCode&&); \
	NO_API UPSOneSelectAreaCode(const UPSOneSelectAreaCode&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneSelectAreaCode); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneSelectAreaCode); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneSelectAreaCode)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_PRIVATE_PROPERTY_OFFSET
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_41_PROLOG
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_RPC_WRAPPERS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_INCLASS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_RPC_WRAPPERS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_INCLASS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h_44_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ONEENGINESDK_API UClass* StaticClass<class UPSOneSelectAreaCode>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneSelectAreaCode_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
