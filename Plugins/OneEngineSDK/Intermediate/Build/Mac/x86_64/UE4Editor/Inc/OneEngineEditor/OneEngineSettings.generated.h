// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ONEENGINEEDITOR_OneEngineSettings_generated_h
#error "OneEngineSettings.generated.h already included, missing '#pragma once' in OneEngineSettings.h"
#endif
#define ONEENGINEEDITOR_OneEngineSettings_generated_h

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_SPARSE_DATA
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_RPC_WRAPPERS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_RPC_WRAPPERS_NO_PURE_DECLS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUOneEngineSettings(); \
	friend struct Z_Construct_UClass_UOneEngineSettings_Statics; \
public: \
	DECLARE_CLASS(UOneEngineSettings, UObject, COMPILED_IN_FLAGS(0 | CLASS_DefaultConfig | CLASS_Config), CASTCLASS_None, TEXT("/Script/OneEngineEditor"), NO_API) \
	DECLARE_SERIALIZER(UOneEngineSettings) \
	static const TCHAR* StaticConfigName() {return TEXT("Game");} \



#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_INCLASS \
private: \
	static void StaticRegisterNativesUOneEngineSettings(); \
	friend struct Z_Construct_UClass_UOneEngineSettings_Statics; \
public: \
	DECLARE_CLASS(UOneEngineSettings, UObject, COMPILED_IN_FLAGS(0 | CLASS_DefaultConfig | CLASS_Config), CASTCLASS_None, TEXT("/Script/OneEngineEditor"), NO_API) \
	DECLARE_SERIALIZER(UOneEngineSettings) \
	static const TCHAR* StaticConfigName() {return TEXT("Game");} \



#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UOneEngineSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UOneEngineSettings) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UOneEngineSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UOneEngineSettings); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UOneEngineSettings(UOneEngineSettings&&); \
	NO_API UOneEngineSettings(const UOneEngineSettings&); \
public:


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UOneEngineSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()) : Super(ObjectInitializer) { }; \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UOneEngineSettings(UOneEngineSettings&&); \
	NO_API UOneEngineSettings(const UOneEngineSettings&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UOneEngineSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UOneEngineSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UOneEngineSettings)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_PRIVATE_PROPERTY_OFFSET
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_17_PROLOG
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_RPC_WRAPPERS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_INCLASS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_RPC_WRAPPERS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_INCLASS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h_20_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ONEENGINEEDITOR_API UClass* StaticClass<class UOneEngineSettings>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineEditor_Public_OneEngineSettings_h


#define FOREACH_ENUM_ESDKREGIONCONFIG(op) \
	op(ESDKRegionConfig::Mainland) \
	op(ESDKRegionConfig::Oversea) 

enum class ESDKRegionConfig : uint8;
template<> ONEENGINEEDITOR_API UEnum* StaticEnum<ESDKRegionConfig>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
