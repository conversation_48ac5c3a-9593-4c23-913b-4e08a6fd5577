// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneUserCenterIDInfo.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneUserCenterIDInfo() {}
// Cross Module References
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FPSOneIDInfoStruct();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterIDInfo_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterIDInfo();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_NoRegister();
// End Cross Module References
class UScriptStruct* FPSOneIDInfoStruct::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FPSOneIDInfoStruct, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("PSOneIDInfoStruct"), sizeof(FPSOneIDInfoStruct), Get_Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FPSOneIDInfoStruct>()
{
	return FPSOneIDInfoStruct::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FPSOneIDInfoStruct(FPSOneIDInfoStruct::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("PSOneIDInfoStruct"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFPSOneIDInfoStruct
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFPSOneIDInfoStruct()
	{
		UScriptStruct::DeferCppStructOps<FPSOneIDInfoStruct>(FName(TEXT("PSOneIDInfoStruct")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFPSOneIDInfoStruct;
	struct Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_Name;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ID_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_ID;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::Struct_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterIDInfo.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPSOneIDInfoStruct>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewProp_Name_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterIDInfo.h" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneIDInfoStruct, Name), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewProp_Name_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewProp_Name_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewProp_ID_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterIDInfo.h" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewProp_ID = { "ID", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneIDInfoStruct, ID), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewProp_ID_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewProp_ID_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewProp_Name,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::NewProp_ID,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"PSOneIDInfoStruct",
		sizeof(FPSOneIDInfoStruct),
		alignof(FPSOneIDInfoStruct),
		Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FPSOneIDInfoStruct()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("PSOneIDInfoStruct"), sizeof(FPSOneIDInfoStruct), Get_Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FPSOneIDInfoStruct_Hash() { return 1276618967U; }
	void UPSOneUserCenterIDInfo::StaticRegisterNativesUPSOneUserCenterIDInfo()
	{
	}
	UClass* Z_Construct_UClass_UPSOneUserCenterIDInfo_NoRegister()
	{
		return UPSOneUserCenterIDInfo::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NameCell_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_NameCell;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_IDCell_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_IDCell;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::Class_MetaDataParams[] = {
		{ "IncludePath", "Views/PSOneUserCenterIDInfo.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterIDInfo.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::NewProp_NameCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// AvatarCell\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterIDInfo.h" },
		{ "ToolTip", "AvatarCell" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::NewProp_NameCell = { "NameCell", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterIDInfo, NameCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::NewProp_NameCell_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::NewProp_NameCell_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::NewProp_IDCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// NickCell\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterIDInfo.h" },
		{ "ToolTip", "NickCell" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::NewProp_IDCell = { "IDCell", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterIDInfo, IDCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::NewProp_IDCell_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::NewProp_IDCell_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::NewProp_NameCell,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::NewProp_IDCell,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterIDInfo>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::ClassParams = {
		&UPSOneUserCenterIDInfo::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::PropPointers),
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneUserCenterIDInfo()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneUserCenterIDInfo_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneUserCenterIDInfo, 3226031642);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneUserCenterIDInfo>()
	{
		return UPSOneUserCenterIDInfo::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneUserCenterIDInfo(Z_Construct_UClass_UPSOneUserCenterIDInfo, &UPSOneUserCenterIDInfo::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneUserCenterIDInfo"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterIDInfo);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
