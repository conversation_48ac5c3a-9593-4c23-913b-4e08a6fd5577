// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneButtonBase.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneButtonBase() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase();
	UMG_API UClass* Z_Construct_UClass_UUserWidget();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	SLATECORE_API UEnum* Z_Construct_UEnum_SlateCore_EHorizontalAlignment();
	UMG_API UClass* Z_Construct_UClass_UHorizontalBox_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UBorder_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UButton_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
	ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
// End Cross Module References
	DEFINE_FUNCTION(UPSOneButtonBase::execOnClick)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->OnClick();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneButtonBase::execSetTitle)
	{
		P_GET_PROPERTY_REF(FTextProperty,Z_Param_Out_InTitle);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->SetTitle(Z_Param_Out_InTitle);
		P_NATIVE_END;
	}
	void UPSOneButtonBase::StaticRegisterNativesUPSOneButtonBase()
	{
		UClass* Class = UPSOneButtonBase::StaticClass();
		static const FNameNativePtrPair Funcs[] = {
			{ "OnClick", &UPSOneButtonBase::execOnClick },
			{ "SetTitle", &UPSOneButtonBase::execSetTitle },
		};
		FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
	}
	struct Z_Construct_UFunction_UPSOneButtonBase_OnClick_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneButtonBase_OnClick_Statics::Function_MetaDataParams[] = {
		{ "Comment", "// Callback\n" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
		{ "ToolTip", "Callback" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneButtonBase_OnClick_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneButtonBase, nullptr, "OnClick", nullptr, nullptr, 0, nullptr, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneButtonBase_OnClick_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneButtonBase_OnClick_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneButtonBase_OnClick()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneButtonBase_OnClick_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics
	{
		struct PSOneButtonBase_eventSetTitle_Parms
		{
			FText InTitle;
		};
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_InTitle_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_InTitle;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::NewProp_InTitle_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::NewProp_InTitle = { "InTitle", nullptr, (EPropertyFlags)0x0010000008000182, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneButtonBase_eventSetTitle_Parms, InTitle), METADATA_PARAMS(Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::NewProp_InTitle_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::NewProp_InTitle_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::NewProp_InTitle,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneButtonBase, nullptr, "SetTitle", nullptr, nullptr, sizeof(PSOneButtonBase_eventSetTitle_Parms), Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneButtonBase_SetTitle()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	UClass* Z_Construct_UClass_UPSOneButtonBase_NoRegister()
	{
		return UPSOneButtonBase::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneButtonBase_Statics
	{
		static UObject* (*const DependentSingletons[])();
		static const FClassFunctionLinkInfo FuncInfo[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_Title;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Alignment_MetaData[];
#endif
		static const UE4CodeGen_Private::FBytePropertyParams NewProp_Alignment;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TextHorizontalBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_TextHorizontalBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TextSize_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_TextSize;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BgBorder_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BgBorder;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Button_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_Button;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_TextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SelectedTexture_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_SelectedTexture;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NormalTexture_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_NormalTexture;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_FocusTexture_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_FocusTexture;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bIsSelected_MetaData[];
#endif
		static void NewProp_bIsSelected_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsSelected;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneButtonBase_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
	const FClassFunctionLinkInfo Z_Construct_UClass_UPSOneButtonBase_Statics::FuncInfo[] = {
		{ &Z_Construct_UFunction_UPSOneButtonBase_OnClick, "OnClick" }, // 1945777318
		{ &Z_Construct_UFunction_UPSOneButtonBase_SetTitle, "SetTitle" }, // 1460691082
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneButtonBase_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n *\n */" },
		{ "IncludePath", "Views/PSOneButtonBase.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Title_MetaData[] = {
		{ "Category", "PSOneButtonBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneButtonBase, Title), METADATA_PARAMS(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Title_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Title_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Alignment_MetaData[] = {
		{ "Category", "PSOneButtonBase" },
		{ "Comment", "//\xe5\xaf\xb9\xe9\xbd\x90\xe6\x96\xb9\xe5\xbc\x8f\n" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
		{ "ToolTip", "\xe5\xaf\xb9\xe9\xbd\x90\xe6\x96\xb9\xe5\xbc\x8f" },
	};
#endif
	const UE4CodeGen_Private::FBytePropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Alignment = { "Alignment", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneButtonBase, Alignment), Z_Construct_UEnum_SlateCore_EHorizontalAlignment, METADATA_PARAMS(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Alignment_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Alignment_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextHorizontalBox_MetaData[] = {
		{ "BindWidgetOptional", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextHorizontalBox = { "TextHorizontalBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneButtonBase, TextHorizontalBox), Z_Construct_UClass_UHorizontalBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextHorizontalBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextHorizontalBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextSize_MetaData[] = {
		{ "Category", "PSOneButtonBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextSize = { "TextSize", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneButtonBase, TextSize), METADATA_PARAMS(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextSize_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextSize_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_BgBorder_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_BgBorder = { "BgBorder", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneButtonBase, BgBorder), Z_Construct_UClass_UBorder_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_BgBorder_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_BgBorder_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Button_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Button = { "Button", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneButtonBase, Button), Z_Construct_UClass_UButton_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Button_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Button_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextBlock = { "TextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneButtonBase, TextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_SelectedTexture_MetaData[] = {
		{ "Category", "PSOneButtonBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_SelectedTexture = { "SelectedTexture", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneButtonBase, SelectedTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_SelectedTexture_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_SelectedTexture_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_NormalTexture_MetaData[] = {
		{ "Category", "PSOneButtonBase" },
		{ "Comment", "// normal\n" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
		{ "ToolTip", "normal" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_NormalTexture = { "NormalTexture", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneButtonBase, NormalTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_NormalTexture_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_NormalTexture_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_FocusTexture_MetaData[] = {
		{ "Category", "PSOneButtonBase" },
		{ "Comment", "// onFocus\n" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
		{ "ToolTip", "onFocus" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_FocusTexture = { "FocusTexture", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneButtonBase, FocusTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_FocusTexture_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_FocusTexture_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_bIsSelected_MetaData[] = {
		{ "Category", "PSOneButtonBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
#endif
	void Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_bIsSelected_SetBit(void* Obj)
	{
		((UPSOneButtonBase*)Obj)->bIsSelected = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_bIsSelected = { "bIsSelected", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UPSOneButtonBase), &Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_bIsSelected_SetBit, METADATA_PARAMS(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_bIsSelected_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_bIsSelected_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneButtonBase_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Title,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Alignment,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextHorizontalBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextSize,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_BgBorder,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Button,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_SelectedTexture,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_NormalTexture,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_FocusTexture,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_bIsSelected,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneButtonBase_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneButtonBase>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneButtonBase_Statics::ClassParams = {
		&UPSOneButtonBase::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		FuncInfo,
		Z_Construct_UClass_UPSOneButtonBase_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		UE_ARRAY_COUNT(FuncInfo),
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::PropPointers),
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneButtonBase_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneButtonBase()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneButtonBase_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneButtonBase, 1362308023);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneButtonBase>()
	{
		return UPSOneButtonBase::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneButtonBase(Z_Construct_UClass_UPSOneButtonBase, &UPSOneButtonBase::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneButtonBase"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneButtonBase);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
