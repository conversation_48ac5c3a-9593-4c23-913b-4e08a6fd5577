// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneDelTips.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneDelTips() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneDelTips_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneDelTips();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UBackgroundBlur_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneConfirmButton_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
// End Cross Module References
	void UPSOneDelTips::StaticRegisterNativesUPSOneDelTips()
	{
	}
	UClass* Z_Construct_UClass_UPSOneDelTips_NoRegister()
	{
		return UPSOneDelTips::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneDelTips_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BackgroundBlur_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BackgroundBlur;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TitleTextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_TitleTextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DescTextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_DescTextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_RestoreButton_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_RestoreButton;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DeleteButton_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_DeleteButton;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneDelTips_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelTips_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n *\n */" },
		{ "IncludePath", "Views/PSOneDelTips.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelTips.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelTips.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneDelTips, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_ScaleBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_ScaleBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_BackgroundBlur_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelTips.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_BackgroundBlur = { "BackgroundBlur", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneDelTips, BackgroundBlur), Z_Construct_UClass_UBackgroundBlur_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_BackgroundBlur_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_BackgroundBlur_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_TitleTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelTips.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_TitleTextBlock = { "TitleTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneDelTips, TitleTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_TitleTextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_TitleTextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_DescTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelTips.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_DescTextBlock = { "DescTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneDelTips, DescTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_DescTextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_DescTextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_RestoreButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe6\x81\xa2\xe5\xa4\x8d\xe6\x8c\x89\xe9\x92\xae\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelTips.h" },
		{ "ToolTip", "\xe6\x81\xa2\xe5\xa4\x8d\xe6\x8c\x89\xe9\x92\xae" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_RestoreButton = { "RestoreButton", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneDelTips, RestoreButton), Z_Construct_UClass_UPSOneConfirmButton_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_RestoreButton_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_RestoreButton_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_DeleteButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe5\x88\xa0\xe9\x99\xa4\xe6\x8c\x89\xe9\x92\xae\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelTips.h" },
		{ "ToolTip", "\xe5\x88\xa0\xe9\x99\xa4\xe6\x8c\x89\xe9\x92\xae" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_DeleteButton = { "DeleteButton", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneDelTips, DeleteButton), Z_Construct_UClass_UPSOneButtonBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_DeleteButton_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_DeleteButton_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// enter icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelTips.h" },
		{ "ToolTip", "enter icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneDelTips, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_EnterIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_EnterIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// backspace icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelTips.h" },
		{ "ToolTip", "backspace icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneDelTips, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_BackspaceIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_BackspaceIcon_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneDelTips_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_ScaleBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_BackgroundBlur,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_TitleTextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_DescTextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_RestoreButton,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_DeleteButton,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_EnterIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneDelTips_Statics::NewProp_BackspaceIcon,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneDelTips_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneDelTips>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneDelTips_Statics::ClassParams = {
		&UPSOneDelTips::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneDelTips_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelTips_Statics::PropPointers),
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneDelTips_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelTips_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneDelTips()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneDelTips_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneDelTips, 2833186521);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneDelTips>()
	{
		return UPSOneDelTips::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneDelTips(Z_Construct_UClass_UPSOneDelTips, &UPSOneDelTips::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneDelTips"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneDelTips);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
