// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneUserCenterDeviceManager.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneUserCenterDeviceManager() {}
// Cross Module References
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneOnlineDevice();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterDeviceManager_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterDeviceManager();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UScrollBox_NoRegister();
// End Cross Module References
class UScriptStruct* FOneOnlineDevice::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOneOnlineDevice_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneOnlineDevice, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneOnlineDevice"), sizeof(FOneOnlineDevice), Get_Z_Construct_UScriptStruct_FOneOnlineDevice_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOneOnlineDevice>()
{
	return FOneOnlineDevice::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOneOnlineDevice(FOneOnlineDevice::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OneOnlineDevice"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneOnlineDevice
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneOnlineDevice()
	{
		UScriptStruct::DeferCppStructOps<FOneOnlineDevice>(FName(TEXT("OneOnlineDevice")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOneOnlineDevice;
	struct Z_Construct_UScriptStruct_FOneOnlineDevice_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneOnlineDevice_Statics::Struct_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterDeviceManager.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOneOnlineDevice_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneOnlineDevice>();
	}
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneOnlineDevice_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OneOnlineDevice",
		sizeof(FOneOnlineDevice),
		alignof(FOneOnlineDevice),
		nullptr,
		0,
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOneOnlineDevice_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneOnlineDevice_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOneOnlineDevice()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOneOnlineDevice_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OneOnlineDevice"), sizeof(FOneOnlineDevice), Get_Z_Construct_UScriptStruct_FOneOnlineDevice_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOneOnlineDevice_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOneOnlineDevice_Hash() { return 2475464138U; }
	void UPSOneUserCenterDeviceManager::StaticRegisterNativesUPSOneUserCenterDeviceManager()
	{
	}
	UClass* Z_Construct_UClass_UPSOneUserCenterDeviceManager_NoRegister()
	{
		return UPSOneUserCenterDeviceManager::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TitleBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_TitleBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ScrollBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ScrollBox;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n *\n */" },
		{ "IncludePath", "Views/PSOneUserCenterDeviceManager.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterDeviceManager.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::NewProp_TitleBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterDeviceManager.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::NewProp_TitleBlock = { "TitleBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterDeviceManager, TitleBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::NewProp_TitleBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::NewProp_TitleBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::NewProp_ScrollBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterDeviceManager.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::NewProp_ScrollBox = { "ScrollBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterDeviceManager, ScrollBox), Z_Construct_UClass_UScrollBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::NewProp_ScrollBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::NewProp_ScrollBox_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::NewProp_TitleBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::NewProp_ScrollBox,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterDeviceManager>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::ClassParams = {
		&UPSOneUserCenterDeviceManager::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::PropPointers),
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneUserCenterDeviceManager()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneUserCenterDeviceManager_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneUserCenterDeviceManager, 2078724204);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneUserCenterDeviceManager>()
	{
		return UPSOneUserCenterDeviceManager::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneUserCenterDeviceManager(Z_Construct_UClass_UPSOneUserCenterDeviceManager, &UPSOneUserCenterDeviceManager::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneUserCenterDeviceManager"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterDeviceManager);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
