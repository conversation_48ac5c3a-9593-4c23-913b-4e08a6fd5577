// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneUserCenterRightCellSubtitle_NF.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneUserCenterRightCellSubtitle_NF() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF();
	UMG_API UClass* Z_Construct_UClass_UUserWidget();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UBorder_NoRegister();
	ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
// End Cross Module References
	void UPSOneUserCenterRightCellSubtitle_NF::StaticRegisterNativesUPSOneUserCenterRightCellSubtitle_NF()
	{
	}
	UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_NoRegister()
	{
		return UPSOneUserCenterRightCellSubtitle_NF::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_Title;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_TextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BgBorder_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BgBorder;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NormalTexture_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_NormalTexture;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SubTitle_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_SubTitle;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SubTextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_SubTextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_HiddenArrow_MetaData[];
#endif
		static void NewProp_HiddenArrow_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_HiddenArrow;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ArrowImage_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ArrowImage;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n * \n */" },
		{ "IncludePath", "Views/PSOneUserCenterRightCellSubtitle_NF.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_Title_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle_NF" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle_NF, Title), METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_Title_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_Title_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_TextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_TextBlock = { "TextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle_NF, TextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_TextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_TextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_BgBorder_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_BgBorder = { "BgBorder", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle_NF, BgBorder), Z_Construct_UClass_UBorder_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_BgBorder_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_BgBorder_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_NormalTexture_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle_NF" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_NormalTexture = { "NormalTexture", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle_NF, NormalTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_NormalTexture_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_NormalTexture_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_SubTitle_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle_NF" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_SubTitle = { "SubTitle", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle_NF, SubTitle), METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_SubTitle_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_SubTitle_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_SubTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_SubTextBlock = { "SubTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle_NF, SubTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_SubTextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_SubTextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_HiddenArrow_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle_NF" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
#endif
	void Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_HiddenArrow_SetBit(void* Obj)
	{
		((UPSOneUserCenterRightCellSubtitle_NF*)Obj)->HiddenArrow = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_HiddenArrow = { "HiddenArrow", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UPSOneUserCenterRightCellSubtitle_NF), &Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_HiddenArrow_SetBit, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_HiddenArrow_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_HiddenArrow_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_ArrowImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle_NF.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_ArrowImage = { "ArrowImage", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle_NF, ArrowImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_ArrowImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_ArrowImage_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_Title,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_TextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_BgBorder,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_NormalTexture,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_SubTitle,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_SubTextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_HiddenArrow,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::NewProp_ArrowImage,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterRightCellSubtitle_NF>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::ClassParams = {
		&UPSOneUserCenterRightCellSubtitle_NF::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::PropPointers),
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneUserCenterRightCellSubtitle_NF, 3028408554);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneUserCenterRightCellSubtitle_NF>()
	{
		return UPSOneUserCenterRightCellSubtitle_NF::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneUserCenterRightCellSubtitle_NF(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NF, &UPSOneUserCenterRightCellSubtitle_NF::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneUserCenterRightCellSubtitle_NF"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterRightCellSubtitle_NF);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
