// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneUserCenterRightCellAccountBind.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneUserCenterRightCellAccountBind() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
// End Cross Module References
	void UPSOneUserCenterRightCellAccountBind::StaticRegisterNativesUPSOneUserCenterRightCellAccountBind()
	{
	}
	UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_NoRegister()
	{
		return UPSOneUserCenterRightCellAccountBind::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bIsBind_MetaData[];
#endif
		static void NewProp_bIsBind_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsBind;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UnBindIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_UnBindIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BindIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BindIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Icon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_Icon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_IconImage_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_IconImage;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BindImage_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BindImage;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneButtonBase,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::Class_MetaDataParams[] = {
		{ "IncludePath", "Views/PSOneUserCenterRightCellAccountBind.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAccountBind.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_bIsBind_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellAccountBind" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAccountBind.h" },
	};
#endif
	void Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_bIsBind_SetBit(void* Obj)
	{
		((UPSOneUserCenterRightCellAccountBind*)Obj)->bIsBind = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_bIsBind = { "bIsBind", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UPSOneUserCenterRightCellAccountBind), &Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_bIsBind_SetBit, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_bIsBind_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_bIsBind_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_UnBindIcon_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellAccountBind" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAccountBind.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_UnBindIcon = { "UnBindIcon", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellAccountBind, UnBindIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_UnBindIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_UnBindIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_BindIcon_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellAccountBind" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAccountBind.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_BindIcon = { "BindIcon", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellAccountBind, BindIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_BindIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_BindIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_Icon_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellAccountBind" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAccountBind.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_Icon = { "Icon", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellAccountBind, Icon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_Icon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_Icon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_IconImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAccountBind.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_IconImage = { "IconImage", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellAccountBind, IconImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_IconImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_IconImage_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_BindImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAccountBind.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_BindImage = { "BindImage", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellAccountBind, BindImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_BindImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_BindImage_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_bIsBind,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_UnBindIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_BindIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_Icon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_IconImage,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_BindImage,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterRightCellAccountBind>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::ClassParams = {
		&UPSOneUserCenterRightCellAccountBind::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::PropPointers),
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneUserCenterRightCellAccountBind, **********);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneUserCenterRightCellAccountBind>()
	{
		return UPSOneUserCenterRightCellAccountBind::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneUserCenterRightCellAccountBind(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind, &UPSOneUserCenterRightCellAccountBind::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneUserCenterRightCellAccountBind"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterRightCellAccountBind);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
