// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSUserWidgetSettings.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSUserWidgetSettings() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSUserWidgetSettings_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSUserWidgetSettings();
	ENGINE_API UClass* Z_Construct_UClass_UDataAsset();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ENGINE_API UClass* Z_Construct_UClass_UFont_NoRegister();
	ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
// End Cross Module References
	DEFINE_FUNCTION(UPSUserWidgetSettings::execGet)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(UPSUserWidgetSettings**)Z_Param__Result=UPSUserWidgetSettings::Get();
		P_NATIVE_END;
	}
	void UPSUserWidgetSettings::StaticRegisterNativesUPSUserWidgetSettings()
	{
		UClass* Class = UPSUserWidgetSettings::StaticClass();
		static const FNameNativePtrPair Funcs[] = {
			{ "Get", &UPSUserWidgetSettings::execGet },
		};
		FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
	}
	struct Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics
	{
		struct PSUserWidgetSettings_eventGet_Parms
		{
			UPSUserWidgetSettings* ReturnValue;
		};
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSUserWidgetSettings_eventGet_Parms, ReturnValue), Z_Construct_UClass_UPSUserWidgetSettings_NoRegister, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::Function_MetaDataParams[] = {
		{ "DisplayName", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSUserWidgetSettings, nullptr, "Get", nullptr, nullptr, sizeof(PSUserWidgetSettings_eventGet_Parms), Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSUserWidgetSettings_Get()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSUserWidgetSettings_Get_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	UClass* Z_Construct_UClass_UPSUserWidgetSettings_NoRegister()
	{
		return UPSUserWidgetSettings::StaticClass();
	}
	struct Z_Construct_UClass_UPSUserWidgetSettings_Statics
	{
		static UObject* (*const DependentSingletons[])();
		static const FClassFunctionLinkInfo FuncInfo[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ZOrder_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_ZOrder;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bEnableBlurBackground_MetaData[];
#endif
		static void NewProp_bEnableBlurBackground_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bEnableBlurBackground;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BlurBackgroundStrength_MetaData[];
#endif
		static const UE4CodeGen_Private::FFloatPropertyParams NewProp_BlurBackgroundStrength;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CustomFont_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_CustomFont;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AgreementPromptBackgroundTexture_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_AgreementPromptBackgroundTexture;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bEnterButtonAssignCircle_MetaData[];
#endif
		static void NewProp_bEnterButtonAssignCircle_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bEnterButtonAssignCircle;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PSCircleTexture_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_PSCircleTexture;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PSCrossTexture_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_PSCrossTexture;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PSSquareTexture_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_PSSquareTexture;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PSTriangleTexture_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_PSTriangleTexture;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PSL1Texture_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_PSL1Texture;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PSR1Texture_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_PSR1Texture;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PSR2Texture_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_PSR2Texture;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSUserWidgetSettings_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UDataAsset,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
	const FClassFunctionLinkInfo Z_Construct_UClass_UPSUserWidgetSettings_Statics::FuncInfo[] = {
		{ &Z_Construct_UFunction_UPSUserWidgetSettings_Get, "Get" }, // 3056913074
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSUserWidgetSettings_Statics::Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "Views/PSUserWidgetSettings.h" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_ZOrder_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "Comment", "// UI Z \xe8\xbd\xb4\xe9\xa1\xba\xe5\xba\x8f\n" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
		{ "ToolTip", "UI Z \xe8\xbd\xb4\xe9\xa1\xba\xe5\xba\x8f" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_ZOrder = { "ZOrder", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSUserWidgetSettings, ZOrder), METADATA_PARAMS(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_ZOrder_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_ZOrder_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnableBlurBackground_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe5\x90\xaf\xe7\x94\xa8\xe6\xa8\xa1\xe7\xb3\x8a\xe8\x83\x8c\xe6\x99\xaf\n" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe5\x90\xaf\xe7\x94\xa8\xe6\xa8\xa1\xe7\xb3\x8a\xe8\x83\x8c\xe6\x99\xaf" },
	};
#endif
	void Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnableBlurBackground_SetBit(void* Obj)
	{
		((UPSUserWidgetSettings*)Obj)->bEnableBlurBackground = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnableBlurBackground = { "bEnableBlurBackground", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UPSUserWidgetSettings), &Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnableBlurBackground_SetBit, METADATA_PARAMS(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnableBlurBackground_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnableBlurBackground_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_BlurBackgroundStrength_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "Comment", "// \xe6\xa8\xa1\xe7\xb3\x8a\xe8\x83\x8c\xe6\x99\xaf\xe5\xbc\xba\xe5\xba\xa6\n" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
		{ "ToolTip", "\xe6\xa8\xa1\xe7\xb3\x8a\xe8\x83\x8c\xe6\x99\xaf\xe5\xbc\xba\xe5\xba\xa6" },
	};
#endif
	const UE4CodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_BlurBackgroundStrength = { "BlurBackgroundStrength", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSUserWidgetSettings, BlurBackgroundStrength), METADATA_PARAMS(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_BlurBackgroundStrength_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_BlurBackgroundStrength_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_CustomFont_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "Comment", "// \xe4\xbd\xbf\xe7\x94\xa8\xe8\x87\xaa\xe5\xae\x9a\xe4\xb9\x89\xe5\xad\x97\xe4\xbd\x93\n" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
		{ "ToolTip", "\xe4\xbd\xbf\xe7\x94\xa8\xe8\x87\xaa\xe5\xae\x9a\xe4\xb9\x89\xe5\xad\x97\xe4\xbd\x93" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_CustomFont = { "CustomFont", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSUserWidgetSettings, CustomFont), Z_Construct_UClass_UFont_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_CustomFont_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_CustomFont_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_AgreementPromptBackgroundTexture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "Comment", "// \xe5\xba\x94\xe8\xaf\xa5\xe8\xa6\x81\xe8\xa2\xab\xe5\xba\x9f\xe5\xbc\x83\xe4\xba\x86\n" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
		{ "ToolTip", "\xe5\xba\x94\xe8\xaf\xa5\xe8\xa6\x81\xe8\xa2\xab\xe5\xba\x9f\xe5\xbc\x83\xe4\xba\x86" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_AgreementPromptBackgroundTexture = { "AgreementPromptBackgroundTexture", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSUserWidgetSettings, AgreementPromptBackgroundTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_AgreementPromptBackgroundTexture_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_AgreementPromptBackgroundTexture_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnterButtonAssignCircle_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
#endif
	void Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnterButtonAssignCircle_SetBit(void* Obj)
	{
		((UPSUserWidgetSettings*)Obj)->bEnterButtonAssignCircle = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnterButtonAssignCircle = { "bEnterButtonAssignCircle", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UPSUserWidgetSettings), &Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnterButtonAssignCircle_SetBit, METADATA_PARAMS(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnterButtonAssignCircle_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnterButtonAssignCircle_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSCircleTexture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSCircleTexture = { "PSCircleTexture", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSUserWidgetSettings, PSCircleTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSCircleTexture_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSCircleTexture_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSCrossTexture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSCrossTexture = { "PSCrossTexture", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSUserWidgetSettings, PSCrossTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSCrossTexture_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSCrossTexture_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSSquareTexture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSSquareTexture = { "PSSquareTexture", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSUserWidgetSettings, PSSquareTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSSquareTexture_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSSquareTexture_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSTriangleTexture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSTriangleTexture = { "PSTriangleTexture", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSUserWidgetSettings, PSTriangleTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSTriangleTexture_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSTriangleTexture_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSL1Texture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSL1Texture = { "PSL1Texture", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSUserWidgetSettings, PSL1Texture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSL1Texture_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSL1Texture_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSR1Texture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSR1Texture = { "PSR1Texture", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSUserWidgetSettings, PSR1Texture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSR1Texture_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSR1Texture_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSR2Texture_MetaData[] = {
		{ "Category", "PSUserWidgetSettings" },
		{ "ModuleRelativePath", "Private/Views/PSUserWidgetSettings.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSR2Texture = { "PSR2Texture", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSUserWidgetSettings, PSR2Texture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSR2Texture_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSR2Texture_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSUserWidgetSettings_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_ZOrder,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnableBlurBackground,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_BlurBackgroundStrength,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_CustomFont,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_AgreementPromptBackgroundTexture,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_bEnterButtonAssignCircle,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSCircleTexture,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSCrossTexture,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSSquareTexture,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSTriangleTexture,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSL1Texture,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSR1Texture,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSUserWidgetSettings_Statics::NewProp_PSR2Texture,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSUserWidgetSettings_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSUserWidgetSettings>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSUserWidgetSettings_Statics::ClassParams = {
		&UPSUserWidgetSettings::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		FuncInfo,
		Z_Construct_UClass_UPSUserWidgetSettings_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		UE_ARRAY_COUNT(FuncInfo),
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::PropPointers),
		0,
		0x000000A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSUserWidgetSettings_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSUserWidgetSettings_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSUserWidgetSettings()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSUserWidgetSettings_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSUserWidgetSettings, 2441862376);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSUserWidgetSettings>()
	{
		return UPSUserWidgetSettings::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSUserWidgetSettings(Z_Construct_UClass_UPSUserWidgetSettings, &UPSUserWidgetSettings::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSUserWidgetSettings"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSUserWidgetSettings);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
