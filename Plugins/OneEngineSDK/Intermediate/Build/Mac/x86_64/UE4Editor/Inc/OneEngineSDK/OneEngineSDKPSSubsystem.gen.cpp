// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Public/OneEngineSDKPSSubsystem.h"
#include "Engine/Classes/Engine/GameInstance.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeOneEngineSDKPSSubsystem() {}
// Cross Module References
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UOneEngineSDKPSSubsystem();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductCategory();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSUserProfileResponse();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature();
	ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSProduct();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductMedia();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductSku();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductMediaImage();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSPurchaseForm();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePSUserProfile();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UOnePSLocaleEnum_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UOnePSLocaleEnum();
	ENGINE_API UClass* Z_Construct_UClass_UBlueprintFunctionLibrary();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UOneEngineSDKPSSubsystem_NoRegister();
	ENGINE_API UClass* Z_Construct_UClass_UGameInstanceSubsystem();
// End Cross Module References
	struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics
	{
		struct OneEngineSDKPSSubsystem_eventWidgetVisibilityDelegate_Parms
		{
			int32 WidgetType;
			bool bVisible;
		};
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_WidgetType;
		static void NewProp_bVisible_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bVisible;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::NewProp_WidgetType = { "WidgetType", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventWidgetVisibilityDelegate_Parms, WidgetType), METADATA_PARAMS(nullptr, 0) };
	void Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::NewProp_bVisible_SetBit(void* Obj)
	{
		((OneEngineSDKPSSubsystem_eventWidgetVisibilityDelegate_Parms*)Obj)->bVisible = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::NewProp_bVisible = { "bVisible", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(OneEngineSDKPSSubsystem_eventWidgetVisibilityDelegate_Parms), &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::NewProp_bVisible_SetBit, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::NewProp_WidgetType,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::NewProp_bVisible,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "WidgetVisibilityDelegate__DelegateSignature", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventWidgetVisibilityDelegate_Parms), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics
	{
		struct OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms
		{
			bool bSucceed;
			FOnePSProductCategory Category;
			int32 Code;
			FString Msg;
		};
		static void NewProp_bSucceed_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bSucceed;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[];
#endif
		static const UE4CodeGen_Private::FStructPropertyParams NewProp_Category;
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_Code;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Msg_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Msg;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	void Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_bSucceed_SetBit(void* Obj)
	{
		((OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms*)Obj)->bSucceed = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_bSucceed = { "bSucceed", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms), &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_bSucceed_SetBit, METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000008000182, UE4CodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms, Category), Z_Construct_UScriptStruct_FOnePSProductCategory, METADATA_PARAMS(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Category_MetaData, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Category_MetaData)) };
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Code = { "Code", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms, Code), METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Msg_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Msg = { "Msg", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms, Msg), METADATA_PARAMS(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Msg_MetaData, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Msg_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_bSucceed,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Category,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Code,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::NewProp_Msg,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OnGetProductInfoListPSDelegate__DelegateSignature", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00520000, 0, 0, METADATA_PARAMS(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics
	{
		struct OneEngineSDKPSSubsystem_eventOnOpenDialogResultDelegate_Parms
		{
			int32 Result;
		};
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_Result;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnOpenDialogResultDelegate_Parms, Result), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::NewProp_Result,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OnOpenDialogResultDelegate__DelegateSignature", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventOnOpenDialogResultDelegate_Parms), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00120000, 0, 0, METADATA_PARAMS(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics
	{
		struct OneEngineSDKPSSubsystem_eventOnFilterProfanityResultDelegate_Parms
		{
			int32 Code;
			FString Text;
		};
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_Code;
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Text;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::NewProp_Code = { "Code", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnFilterProfanityResultDelegate_Parms, Code), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::NewProp_Text = { "Text", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnFilterProfanityResultDelegate_Parms, Text), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::NewProp_Code,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::NewProp_Text,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OnFilterProfanityResultDelegate__DelegateSignature", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventOnFilterProfanityResultDelegate_Parms), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00120000, 0, 0, METADATA_PARAMS(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics
	{
		struct OneEngineSDKPSSubsystem_eventOnCheckPremiumResultDelegate_Parms
		{
			int32 Code;
			bool bIsPremium;
		};
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_Code;
		static void NewProp_bIsPremium_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsPremium;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::NewProp_Code = { "Code", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnCheckPremiumResultDelegate_Parms, Code), METADATA_PARAMS(nullptr, 0) };
	void Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::NewProp_bIsPremium_SetBit(void* Obj)
	{
		((OneEngineSDKPSSubsystem_eventOnCheckPremiumResultDelegate_Parms*)Obj)->bIsPremium = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::NewProp_bIsPremium = { "bIsPremium", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(OneEngineSDKPSSubsystem_eventOnCheckPremiumResultDelegate_Parms), &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::NewProp_bIsPremium_SetBit, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::NewProp_Code,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::NewProp_bIsPremium,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OnCheckPremiumResultDelegate__DelegateSignature", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventOnCheckPremiumResultDelegate_Parms), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00120000, 0, 0, METADATA_PARAMS(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics
	{
		struct OneEngineSDKPSSubsystem_eventOnRestrictionStatusResultDelegate_Parms
		{
			int32 Result;
		};
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_Result;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnRestrictionStatusResultDelegate_Parms, Result), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::NewProp_Result,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OnRestrictionStatusResultDelegate__DelegateSignature", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventOnRestrictionStatusResultDelegate_Parms), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00120000, 0, 0, METADATA_PARAMS(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics
	{
		struct OneEngineSDKPSSubsystem_eventOnGetBlockingUsersResultDelegate_Parms
		{
			FOnePSUserProfileResponse ProfileList;
		};
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ProfileList_MetaData[];
#endif
		static const UE4CodeGen_Private::FStructPropertyParams NewProp_ProfileList;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::NewProp_ProfileList_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::NewProp_ProfileList = { "ProfileList", nullptr, (EPropertyFlags)0x0010000008000182, UE4CodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnGetBlockingUsersResultDelegate_Parms, ProfileList), Z_Construct_UScriptStruct_FOnePSUserProfileResponse, METADATA_PARAMS(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::NewProp_ProfileList_MetaData, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::NewProp_ProfileList_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::NewProp_ProfileList,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OnGetBlockingUsersResultDelegate__DelegateSignature", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventOnGetBlockingUsersResultDelegate_Parms), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00520000, 0, 0, METADATA_PARAMS(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics
	{
		struct OneEngineSDKPSSubsystem_eventOnGetFriendsResultDelegate_Parms
		{
			FOnePSUserProfileResponse ProfileList;
		};
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ProfileList_MetaData[];
#endif
		static const UE4CodeGen_Private::FStructPropertyParams NewProp_ProfileList;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::NewProp_ProfileList_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::NewProp_ProfileList = { "ProfileList", nullptr, (EPropertyFlags)0x0010000008000182, UE4CodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOnGetFriendsResultDelegate_Parms, ProfileList), Z_Construct_UScriptStruct_FOnePSUserProfileResponse, METADATA_PARAMS(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::NewProp_ProfileList_MetaData, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::NewProp_ProfileList_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::NewProp_ProfileList,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OnGetFriendsResultDelegate__DelegateSignature", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventOnGetFriendsResultDelegate_Parms), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00520000, 0, 0, METADATA_PARAMS(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	static UEnum* EOnePsnAccountState_StaticEnum()
	{
		static UEnum* Singleton = nullptr;
		if (!Singleton)
		{
			Singleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOnePsnAccountState"));
		}
		return Singleton;
	}
	template<> ONEENGINESDK_API UEnum* StaticEnum<EOnePsnAccountState>()
	{
		return EOnePsnAccountState_StaticEnum();
	}
	static FCompiledInDeferEnum Z_CompiledInDeferEnum_UEnum_EOnePsnAccountState(EOnePsnAccountState_StaticEnum, TEXT("/Script/OneEngineSDK"), TEXT("EOnePsnAccountState"), false, nullptr, nullptr);
	uint32 Get_Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState_Hash() { return 2410568000U; }
	UEnum* Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState()
	{
#if WITH_HOT_RELOAD
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UEnum* ReturnEnum = FindExistingEnumIfHotReloadOrDynamic(Outer, TEXT("EOnePsnAccountState"), 0, Get_Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState_Hash(), false);
#else
		static UEnum* ReturnEnum = nullptr;
#endif // WITH_HOT_RELOAD
		if (!ReturnEnum)
		{
			static const UE4CodeGen_Private::FEnumeratorParam Enumerators[] = {
				{ "EOnePsnAccountState::Unknown", (int64)EOnePsnAccountState::Unknown },
				{ "EOnePsnAccountState::SignedOut", (int64)EOnePsnAccountState::SignedOut },
				{ "EOnePsnAccountState::SignedIn", (int64)EOnePsnAccountState::SignedIn },
			};
#if WITH_METADATA
			const UE4CodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
				{ "BlueprintType", "true" },
				{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
				{ "SignedIn.Name", "EOnePsnAccountState::SignedIn" },
				{ "SignedOut.Name", "EOnePsnAccountState::SignedOut" },
				{ "Unknown.Name", "EOnePsnAccountState::Unknown" },
			};
#endif
			static const UE4CodeGen_Private::FEnumParams EnumParams = {
				(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
				nullptr,
				"EOnePsnAccountState",
				"EOnePsnAccountState",
				Enumerators,
				UE_ARRAY_COUNT(Enumerators),
				RF_Public|RF_Transient|RF_MarkAsNative,
				EEnumFlags::None,
				UE4CodeGen_Private::EDynamicType::NotDynamic,
				(uint8)UEnum::ECppForm::EnumClass,
				METADATA_PARAMS(Enum_MetaDataParams, UE_ARRAY_COUNT(Enum_MetaDataParams))
			};
			UE4CodeGen_Private::ConstructUEnum(ReturnEnum, EnumParams);
		}
		return ReturnEnum;
	}
	static UEnum* EOnePSStoreIconPos_StaticEnum()
	{
		static UEnum* Singleton = nullptr;
		if (!Singleton)
		{
			Singleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOnePSStoreIconPos"));
		}
		return Singleton;
	}
	template<> ONEENGINESDK_API UEnum* StaticEnum<EOnePSStoreIconPos>()
	{
		return EOnePSStoreIconPos_StaticEnum();
	}
	static FCompiledInDeferEnum Z_CompiledInDeferEnum_UEnum_EOnePSStoreIconPos(EOnePSStoreIconPos_StaticEnum, TEXT("/Script/OneEngineSDK"), TEXT("EOnePSStoreIconPos"), false, nullptr, nullptr);
	uint32 Get_Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos_Hash() { return 3163084838U; }
	UEnum* Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos()
	{
#if WITH_HOT_RELOAD
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UEnum* ReturnEnum = FindExistingEnumIfHotReloadOrDynamic(Outer, TEXT("EOnePSStoreIconPos"), 0, Get_Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos_Hash(), false);
#else
		static UEnum* ReturnEnum = nullptr;
#endif // WITH_HOT_RELOAD
		if (!ReturnEnum)
		{
			static const UE4CodeGen_Private::FEnumeratorParam Enumerators[] = {
				{ "EOnePSStoreIconPos::Center", (int64)EOnePSStoreIconPos::Center },
				{ "EOnePSStoreIconPos::Left", (int64)EOnePSStoreIconPos::Left },
				{ "EOnePSStoreIconPos::Right", (int64)EOnePSStoreIconPos::Right },
			};
#if WITH_METADATA
			const UE4CodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
				{ "BlueprintType", "true" },
				{ "Center.Name", "EOnePSStoreIconPos::Center" },
				{ "Left.Name", "EOnePSStoreIconPos::Left" },
				{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
				{ "Right.Name", "EOnePSStoreIconPos::Right" },
			};
#endif
			static const UE4CodeGen_Private::FEnumParams EnumParams = {
				(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
				nullptr,
				"EOnePSStoreIconPos",
				"EOnePSStoreIconPos",
				Enumerators,
				UE_ARRAY_COUNT(Enumerators),
				RF_Public|RF_Transient|RF_MarkAsNative,
				EEnumFlags::None,
				UE4CodeGen_Private::EDynamicType::NotDynamic,
				(uint8)UEnum::ECppForm::EnumClass,
				METADATA_PARAMS(Enum_MetaDataParams, UE_ARRAY_COUNT(Enum_MetaDataParams))
			};
			UE4CodeGen_Private::ConstructUEnum(ReturnEnum, EnumParams);
		}
		return ReturnEnum;
	}
class UScriptStruct* FOnePSProductCategory::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOnePSProductCategory_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSProductCategory, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSProductCategory"), sizeof(FOnePSProductCategory), Get_Z_Construct_UScriptStruct_FOnePSProductCategory_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOnePSProductCategory>()
{
	return FOnePSProductCategory::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOnePSProductCategory(FOnePSProductCategory::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OnePSProductCategory"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSProductCategory
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSProductCategory()
	{
		UScriptStruct::DeferCppStructOps<FOnePSProductCategory>(FName(TEXT("OnePSProductCategory")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSProductCategory;
	struct Z_Construct_UScriptStruct_FOnePSProductCategory_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Code_MetaData[];
#endif
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_Code;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_RawData_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_RawData;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ID_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ID;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Label_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Label;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AgeLimit_MetaData[];
#endif
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_AgeLimit;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DisplayName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_DisplayName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Type;
		static const UE4CodeGen_Private::FStructPropertyParams NewProp_Children_Inner;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Children_MetaData[];
#endif
		static const UE4CodeGen_Private::FArrayPropertyParams NewProp_Children;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Media_MetaData[];
#endif
		static const UE4CodeGen_Private::FStructPropertyParams NewProp_Media;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TotalItemCount_MetaData[];
#endif
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_TotalItemCount;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSProductCategory>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Code_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
		{ "Comment", "/**\n\x09 * @brief \xe9\x9d\x9e 0 \xe8\xa1\xa8\xe7\xa4\xba\xe5\xbc\x82\xe5\xb8\xb8\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe9\x9d\x9e 0 \xe8\xa1\xa8\xe7\xa4\xba\xe5\xbc\x82\xe5\xb8\xb8" },
	};
#endif
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Code = { "Code", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductCategory, Code), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Code_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Code_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_RawData_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
		{ "Comment", "/**\n\x09 * @brief PSN \xe8\xbf\x94\xe5\x9b\x9e\xe7\x9a\x84\xe5\x8e\x9f\xe5\xa7\x8b\xe6\x95\xb0\xe6\x8d\xae\xef\xbc\x8c\xe5\xa6\x82\xe8\xaf\xa5\xe7\xbb\x93\xe6\x9e\x84\xe4\xbd\x93\xe4\xb8\x8d\xe6\xbb\xa1\xe4\xbd\x8f\xe9\x9c\x80\xe6\xb1\x82\xef\xbc\x8c\xe5\x8f\xaf\xe4\xbd\xbf\xe7\x94\xa8 RawData \xe8\x87\xaa\xe5\xae\x9a\xe4\xb9\x89\xe5\xa4\x84\xe7\x90\x86\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief PSN \xe8\xbf\x94\xe5\x9b\x9e\xe7\x9a\x84\xe5\x8e\x9f\xe5\xa7\x8b\xe6\x95\xb0\xe6\x8d\xae\xef\xbc\x8c\xe5\xa6\x82\xe8\xaf\xa5\xe7\xbb\x93\xe6\x9e\x84\xe4\xbd\x93\xe4\xb8\x8d\xe6\xbb\xa1\xe4\xbd\x8f\xe9\x9c\x80\xe6\xb1\x82\xef\xbc\x8c\xe5\x8f\xaf\xe4\xbd\xbf\xe7\x94\xa8 RawData \xe8\x87\xaa\xe5\xae\x9a\xe4\xb9\x89\xe5\xa4\x84\xe7\x90\x86" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_RawData = { "RawData", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductCategory, RawData), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_RawData_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_RawData_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_ID_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
		{ "Comment", "/**\n\x09 * @brief ID\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief ID" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_ID = { "ID", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductCategory, ID), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_ID_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_ID_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Label_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
		{ "Comment", "/**\n\x09 * @brief \xe6\xa0\x87\xe7\xad\xbe\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe6\xa0\x87\xe7\xad\xbe" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Label = { "Label", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductCategory, Label), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Label_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Label_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_AgeLimit_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
		{ "Comment", "/**\n\x09 * @brief \xe5\x85\x81\xe8\xae\xb8\xe7\x94\xa8\xe6\x88\xb7\xe6\x9f\xa5\xe7\x9c\x8b\xe6\xb8\xb8\xe6\x88\x8f\xe4\xb8\xad\xe5\x95\x86\xe5\xba\x97\xe4\xb8\xad\xe5\x95\x86\xe5\x93\x81\xe7\x9a\x84\xe6\x9c\x80\xe4\xbd\x8e\xe5\xb9\xb4\xe9\xbe\x84\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe5\xb9\xb4\xe9\xbe\x84\xe5\xb0\x8f\xe4\xba\x8e\xe6\xad\xa4\xe5\x80\xbc\xef\xbc\x8c\xe5\x88\x99\xe4\xba\xa7\xe5\x93\x81\xe4\xb8\x8d\xe5\x8c\x85\xe5\x90\xab\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xbb\xe4\xbd\x93\xe4\xb8\xad\xe3\x80\x82\xe5\x80\xbc\xe4\xb8\xba""0\xe8\xa1\xa8\xe7\xa4\xba\xe8\xaf\xa5\xe9\xa1\xb9\xe6\xb2\xa1\xe6\x9c\x89\xe5\xb9\xb4\xe9\xbe\x84\xe8\xa6\x81\xe6\xb1\x82\xe3\x80\x82\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe5\x85\x81\xe8\xae\xb8\xe7\x94\xa8\xe6\x88\xb7\xe6\x9f\xa5\xe7\x9c\x8b\xe6\xb8\xb8\xe6\x88\x8f\xe4\xb8\xad\xe5\x95\x86\xe5\xba\x97\xe4\xb8\xad\xe5\x95\x86\xe5\x93\x81\xe7\x9a\x84\xe6\x9c\x80\xe4\xbd\x8e\xe5\xb9\xb4\xe9\xbe\x84\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe5\xb9\xb4\xe9\xbe\x84\xe5\xb0\x8f\xe4\xba\x8e\xe6\xad\xa4\xe5\x80\xbc\xef\xbc\x8c\xe5\x88\x99\xe4\xba\xa7\xe5\x93\x81\xe4\xb8\x8d\xe5\x8c\x85\xe5\x90\xab\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xbb\xe4\xbd\x93\xe4\xb8\xad\xe3\x80\x82\xe5\x80\xbc\xe4\xb8\xba""0\xe8\xa1\xa8\xe7\xa4\xba\xe8\xaf\xa5\xe9\xa1\xb9\xe6\xb2\xa1\xe6\x9c\x89\xe5\xb9\xb4\xe9\xbe\x84\xe8\xa6\x81\xe6\xb1\x82\xe3\x80\x82" },
	};
#endif
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_AgeLimit = { "AgeLimit", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductCategory, AgeLimit), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_AgeLimit_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_AgeLimit_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_DisplayName_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
		{ "Comment", "/**\n\x09 * @brief \xe6\x9c\xac\xe5\x9c\xb0\xe5\x8c\x96\xe6\x98\xbe\xe7\xa4\xba\xe5\x90\x8d\xe7\xa7\xb0\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe6\x9c\xac\xe5\x9c\xb0\xe5\x8c\x96\xe6\x98\xbe\xe7\xa4\xba\xe5\x90\x8d\xe7\xa7\xb0" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_DisplayName = { "DisplayName", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductCategory, DisplayName), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_DisplayName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_DisplayName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Type_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
		{ "Comment", "/**\n\x09 * @brief \xe7\xb1\xbb\xe5\x9e\x8b, \xe5\x8f\xaf\xe8\x83\xbd\xe7\x9a\x84\xe5\x80\xbc\xef\xbc\x8c""category\xef\xbc\x8cproduct\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe7\xb1\xbb\xe5\x9e\x8b, \xe5\x8f\xaf\xe8\x83\xbd\xe7\x9a\x84\xe5\x80\xbc\xef\xbc\x8c""category\xef\xbc\x8cproduct" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductCategory, Type), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Type_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Type_MetaData)) };
	const UE4CodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Children_Inner = { "Children", nullptr, (EPropertyFlags)0x0000000000000000, UE4CodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, Z_Construct_UScriptStruct_FOnePSProduct, METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Children_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
		{ "Comment", "/**\n\x09 * @brief \xe5\xad\x90\xe5\xae\xb9\xe5\x99\xa8\xe3\x80\x81\xe4\xba\xa7\xe5\x93\x81\xef\xbc\x88\xe6\x8e\xa5\xe5\x8f\xa3\xe9\xbb\x98\xe8\xae\xa4\xe8\xbf\x94\xe5\x9b\x9e\xe7\x88\xb6\xe5\xae\xb9\xe5\x99\xa8\xef\xbc\x8c\xe5\xae\x9e\xe9\x99\x85\xe7\x9a\x84\xe4\xba\xa7\xe5\x93\x81\xe8\xaf\xb7\xe4\xbb\x8e\xe8\xaf\xa5\xe5\xad\x97\xe6\xae\xb5\xe8\x8e\xb7\xe5\x8f\x96\xef\xbc\x89\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe5\xad\x90\xe5\xae\xb9\xe5\x99\xa8\xe3\x80\x81\xe4\xba\xa7\xe5\x93\x81\xef\xbc\x88\xe6\x8e\xa5\xe5\x8f\xa3\xe9\xbb\x98\xe8\xae\xa4\xe8\xbf\x94\xe5\x9b\x9e\xe7\x88\xb6\xe5\xae\xb9\xe5\x99\xa8\xef\xbc\x8c\xe5\xae\x9e\xe9\x99\x85\xe7\x9a\x84\xe4\xba\xa7\xe5\x93\x81\xe8\xaf\xb7\xe4\xbb\x8e\xe8\xaf\xa5\xe5\xad\x97\xe6\xae\xb5\xe8\x8e\xb7\xe5\x8f\x96\xef\xbc\x89" },
	};
#endif
	const UE4CodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Children = { "Children", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductCategory, Children), EArrayPropertyFlags::None, METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Children_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Children_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Media_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
		{ "Comment", "/**\n\x09 * @brief \xe5\xaa\x92\xe4\xbd\x93\xe4\xbf\xa1\xe6\x81\xaf\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe5\xaa\x92\xe4\xbd\x93\xe4\xbf\xa1\xe6\x81\xaf" },
	};
#endif
	const UE4CodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Media = { "Media", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductCategory, Media), Z_Construct_UScriptStruct_FOnePSProductMedia, METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Media_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Media_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_TotalItemCount_MetaData[] = {
		{ "Category", "OnePSProductCategory" },
		{ "Comment", "/**\n\x09 * @brief \xe5\xad\x90\xe5\xae\xb9\xe5\x99\xa8\xe6\x80\xbb\xe6\x95\xb0\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe5\xad\x90\xe5\xae\xb9\xe5\x99\xa8\xe6\x80\xbb\xe6\x95\xb0" },
	};
#endif
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_TotalItemCount = { "TotalItemCount", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductCategory, TotalItemCount), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_TotalItemCount_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_TotalItemCount_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Code,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_RawData,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_ID,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Label,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_AgeLimit,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_DisplayName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Type,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Children_Inner,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Children,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_Media,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::NewProp_TotalItemCount,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OnePSProductCategory",
		sizeof(FOnePSProductCategory),
		alignof(FOnePSProductCategory),
		Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductCategory()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOnePSProductCategory_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OnePSProductCategory"), sizeof(FOnePSProductCategory), Get_Z_Construct_UScriptStruct_FOnePSProductCategory_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOnePSProductCategory_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOnePSProductCategory_Hash() { return 3004830824U; }
class UScriptStruct* FOnePSProduct::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOnePSProduct_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSProduct, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSProduct"), sizeof(FOnePSProduct), Get_Z_Construct_UScriptStruct_FOnePSProduct_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOnePSProduct>()
{
	return FOnePSProduct::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOnePSProduct(FOnePSProduct::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OnePSProduct"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSProduct
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSProduct()
	{
		UScriptStruct::DeferCppStructOps<FOnePSProduct>(FName(TEXT("OnePSProduct")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSProduct;
	struct Z_Construct_UScriptStruct_FOnePSProduct_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ID_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ID;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Label_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Label;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AgeLimit_MetaData[];
#endif
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_AgeLimit;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DisplayName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_DisplayName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Description;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Type;
		static const UE4CodeGen_Private::FStructPropertyParams NewProp_Skus_Inner;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Skus_MetaData[];
#endif
		static const UE4CodeGen_Private::FArrayPropertyParams NewProp_Skus;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Media_MetaData[];
#endif
		static const UE4CodeGen_Private::FStructPropertyParams NewProp_Media;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProduct_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSProduct>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_ID_MetaData[] = {
		{ "Category", "OnePSProduct" },
		{ "Comment", "/**\n\x09 * @brief ID\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief ID" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_ID = { "ID", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProduct, ID), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_ID_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_ID_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Label_MetaData[] = {
		{ "Category", "OnePSProduct" },
		{ "Comment", "/**\n\x09 * @brief \xe6\xa0\x87\xe7\xad\xbe\xef\xbc\x8c\xe8\xb4\xad\xe4\xb9\xb0\xe4\xba\xa7\xe5\x93\x81\xe9\x9c\x80\xe8\xa6\x81\xe4\xbc\xa0\xe5\x85\xa5\xe8\xaf\xa5\xe5\xad\x97\xe6\xae\xb5\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe6\xa0\x87\xe7\xad\xbe\xef\xbc\x8c\xe8\xb4\xad\xe4\xb9\xb0\xe4\xba\xa7\xe5\x93\x81\xe9\x9c\x80\xe8\xa6\x81\xe4\xbc\xa0\xe5\x85\xa5\xe8\xaf\xa5\xe5\xad\x97\xe6\xae\xb5" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Label = { "Label", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProduct, Label), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Label_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Label_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_AgeLimit_MetaData[] = {
		{ "Category", "OnePSProduct" },
		{ "Comment", "/**\n\x09 * @brief \xe5\x85\x81\xe8\xae\xb8\xe7\x94\xa8\xe6\x88\xb7\xe6\x9f\xa5\xe7\x9c\x8b\xe6\xb8\xb8\xe6\x88\x8f\xe4\xb8\xad\xe5\x95\x86\xe5\xba\x97\xe4\xb8\xad\xe5\x95\x86\xe5\x93\x81\xe7\x9a\x84\xe6\x9c\x80\xe4\xbd\x8e\xe5\xb9\xb4\xe9\xbe\x84\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe5\xb9\xb4\xe9\xbe\x84\xe5\xb0\x8f\xe4\xba\x8e\xe6\xad\xa4\xe5\x80\xbc\xef\xbc\x8c\xe5\x88\x99\xe4\xba\xa7\xe5\x93\x81\xe4\xb8\x8d\xe5\x8c\x85\xe5\x90\xab\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xbb\xe4\xbd\x93\xe4\xb8\xad\xe3\x80\x82\xe5\x80\xbc\xe4\xb8\xba""0\xe8\xa1\xa8\xe7\xa4\xba\xe8\xaf\xa5\xe9\xa1\xb9\xe6\xb2\xa1\xe6\x9c\x89\xe5\xb9\xb4\xe9\xbe\x84\xe8\xa6\x81\xe6\xb1\x82\xe3\x80\x82\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe5\x85\x81\xe8\xae\xb8\xe7\x94\xa8\xe6\x88\xb7\xe6\x9f\xa5\xe7\x9c\x8b\xe6\xb8\xb8\xe6\x88\x8f\xe4\xb8\xad\xe5\x95\x86\xe5\xba\x97\xe4\xb8\xad\xe5\x95\x86\xe5\x93\x81\xe7\x9a\x84\xe6\x9c\x80\xe4\xbd\x8e\xe5\xb9\xb4\xe9\xbe\x84\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe5\xb9\xb4\xe9\xbe\x84\xe5\xb0\x8f\xe4\xba\x8e\xe6\xad\xa4\xe5\x80\xbc\xef\xbc\x8c\xe5\x88\x99\xe4\xba\xa7\xe5\x93\x81\xe4\xb8\x8d\xe5\x8c\x85\xe5\x90\xab\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xbb\xe4\xbd\x93\xe4\xb8\xad\xe3\x80\x82\xe5\x80\xbc\xe4\xb8\xba""0\xe8\xa1\xa8\xe7\xa4\xba\xe8\xaf\xa5\xe9\xa1\xb9\xe6\xb2\xa1\xe6\x9c\x89\xe5\xb9\xb4\xe9\xbe\x84\xe8\xa6\x81\xe6\xb1\x82\xe3\x80\x82" },
	};
#endif
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_AgeLimit = { "AgeLimit", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProduct, AgeLimit), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_AgeLimit_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_AgeLimit_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_DisplayName_MetaData[] = {
		{ "Category", "OnePSProduct" },
		{ "Comment", "/**\n\x09 * @brief \xe6\x9c\xac\xe5\x9c\xb0\xe5\x8c\x96\xe5\x90\x8d\xe7\xa7\xb0\xe3\x80\x82\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe6\x9c\xac\xe5\x9c\xb0\xe5\x8c\x96\xe5\x90\x8d\xe7\xa7\xb0\xe3\x80\x82" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_DisplayName = { "DisplayName", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProduct, DisplayName), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_DisplayName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_DisplayName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Description_MetaData[] = {
		{ "Category", "OnePSProduct" },
		{ "Comment", "/**\n\x09 * @brief \xe8\xaf\xa6\xe6\x83\x85\xe6\x8f\x8f\xe8\xbf\xb0\xe3\x80\x82\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe8\xaf\xa6\xe6\x83\x85\xe6\x8f\x8f\xe8\xbf\xb0\xe3\x80\x82" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProduct, Description), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Description_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Description_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Type_MetaData[] = {
		{ "Category", "OnePSProduct" },
		{ "Comment", "/**\n\x09 * @brief \xe7\xb1\xbb\xe5\x9e\x8b, \xe5\x8f\xaf\xe8\x83\xbd\xe7\x9a\x84\xe5\x80\xbc\xef\xbc\x8c""category\xef\xbc\x8cproduct\xe3\x80\x82\n\x09 * @note \xe5\x8f\xaa\xe6\x9c\x89 Type \xe7\xad\x89\xe4\xba\x8e product \xef\xbc\x8c\xe6\x89\x8d\xe5\x85\x81\xe8\xae\xb8\xe8\xb4\xad\xe4\xb9\xb0\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe7\xb1\xbb\xe5\x9e\x8b, \xe5\x8f\xaf\xe8\x83\xbd\xe7\x9a\x84\xe5\x80\xbc\xef\xbc\x8c""category\xef\xbc\x8cproduct\xe3\x80\x82\n@note \xe5\x8f\xaa\xe6\x9c\x89 Type \xe7\xad\x89\xe4\xba\x8e product \xef\xbc\x8c\xe6\x89\x8d\xe5\x85\x81\xe8\xae\xb8\xe8\xb4\xad\xe4\xb9\xb0" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProduct, Type), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Type_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Type_MetaData)) };
	const UE4CodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Skus_Inner = { "Skus", nullptr, (EPropertyFlags)0x0000000000000000, UE4CodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, Z_Construct_UScriptStruct_FOnePSProductSku, METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Skus_MetaData[] = {
		{ "Category", "OnePSProduct" },
		{ "Comment", "/**\n\x09 * @brief \xe9\x94\x80\xe5\x94\xae\xe5\x8d\x95\xe4\xbd\x8d\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe5\xb1\x95\xe7\xa4\xba\xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc\xe4\xbf\xa1\xe6\x81\xaf\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe9\x94\x80\xe5\x94\xae\xe5\x8d\x95\xe4\xbd\x8d\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe5\xb1\x95\xe7\xa4\xba\xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc\xe4\xbf\xa1\xe6\x81\xaf" },
	};
#endif
	const UE4CodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Skus = { "Skus", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProduct, Skus), EArrayPropertyFlags::None, METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Skus_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Skus_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Media_MetaData[] = {
		{ "Category", "OnePSProduct" },
		{ "Comment", "/**\n\x09 * @brief \xe5\xaa\x92\xe4\xbd\x93\xe4\xbf\xa1\xe6\x81\xaf\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe5\xaa\x92\xe4\xbd\x93\xe4\xbf\xa1\xe6\x81\xaf" },
	};
#endif
	const UE4CodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Media = { "Media", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProduct, Media), Z_Construct_UScriptStruct_FOnePSProductMedia, METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Media_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Media_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSProduct_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_ID,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Label,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_AgeLimit,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_DisplayName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Description,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Type,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Skus_Inner,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Skus,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProduct_Statics::NewProp_Media,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSProduct_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OnePSProduct",
		sizeof(FOnePSProduct),
		alignof(FOnePSProduct),
		Z_Construct_UScriptStruct_FOnePSProduct_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProduct_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProduct_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProduct_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOnePSProduct()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOnePSProduct_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OnePSProduct"), sizeof(FOnePSProduct), Get_Z_Construct_UScriptStruct_FOnePSProduct_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOnePSProduct_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOnePSProduct_Hash() { return 2800573100U; }
class UScriptStruct* FOnePSProductSku::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOnePSProductSku_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSProductSku, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSProductSku"), sizeof(FOnePSProductSku), Get_Z_Construct_UScriptStruct_FOnePSProductSku_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOnePSProductSku>()
{
	return FOnePSProductSku::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOnePSProductSku(FOnePSProductSku::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OnePSProductSku"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSProductSku
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSProductSku()
	{
		UScriptStruct::DeferCppStructOps<FOnePSProductSku>(FName(TEXT("OnePSProductSku")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSProductSku;
	struct Z_Construct_UScriptStruct_FOnePSProductSku_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ID_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ID;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Name;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Label_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Label;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_EndDate_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_EndDate;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Type;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Price_MetaData[];
#endif
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_Price;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DisplayPrice_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_DisplayPrice;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_OriginalPrice_MetaData[];
#endif
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_OriginalPrice;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DisplayOriginalPrice_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_DisplayOriginalPrice;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UseLimit_MetaData[];
#endif
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_UseLimit;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PlusUpsellPrice_MetaData[];
#endif
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_PlusUpsellPrice;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DisplayPlusUpsellPrice_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_DisplayPlusUpsellPrice;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_IsPlusPrice_MetaData[];
#endif
		static void NewProp_IsPlusPrice_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_IsPlusPrice;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AnnotationName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_AnnotationName;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductSku_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSProductSku>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_ID_MetaData[] = {
		{ "Category", "OnePSProductSku" },
		{ "Comment", "/**\n\x09 * @brief id\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief id" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_ID = { "ID", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductSku, ID), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_ID_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_ID_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Name_MetaData[] = {
		{ "Category", "OnePSProductSku" },
		{ "Comment", "/**\n\x09 * @brief \xe5\x90\x8d\xe7\xa7\xb0\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe5\x90\x8d\xe7\xa7\xb0" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductSku, Name), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Name_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Name_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Label_MetaData[] = {
		{ "Category", "OnePSProductSku" },
		{ "Comment", "/**\n\x09 * @brief \xe6\xa0\x87\xe7\xad\xbe\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe6\xa0\x87\xe7\xad\xbe" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Label = { "Label", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductSku, Label), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Label_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Label_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_EndDate_MetaData[] = {
		{ "Category", "OnePSProductSku" },
		{ "Comment", "/**\n\x09 * @brief \xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xa5\xe6\x9c\x9f\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xa5\xe6\x9c\x9f" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_EndDate = { "EndDate", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductSku, EndDate), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_EndDate_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_EndDate_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Type_MetaData[] = {
		{ "Category", "OnePSProductSku" },
		{ "Comment", "/**\n\x09 * @brief \xe7\xb1\xbb\xe5\x9e\x8b\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe7\xb1\xbb\xe5\x9e\x8b" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductSku, Type), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Type_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Type_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Price_MetaData[] = {
		{ "Category", "OnePSProductSku" },
		{ "Comment", "/**\n\x09 * @brief  \xe4\xbb\xb7\xe6\xa0\xbc\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief  \xe4\xbb\xb7\xe6\xa0\xbc" },
	};
#endif
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Price = { "Price", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductSku, Price), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Price_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Price_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayPrice_MetaData[] = {
		{ "Category", "OnePSProductSku" },
		{ "Comment", "/**\n\x09 * @brief \xe8\xa1\xa8\xe7\xa4\xba\xe6\xad\xa4\xe4\xba\xa7\xe5\x93\x81\xe7\x9a\x84\xe6\x98\xbe\xe7\xa4\xba\xe4\xbb\xb7\xe6\xa0\xbc\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe5\xad\x98\xe5\x9c\xa8\xe4\xbb\xbb\xe4\xbd\x95\xe6\x8a\x98\xe6\x89\xa3\xef\xbc\x8c\xe5\x88\x99\xe6\x98\xbe\xe7\xa4\xba\xe7\x9a\x84\xe4\xbb\xb7\xe6\xa0\xbc\xe5\x8c\x85\xe5\x90\xab\xe6\x8a\x98\xe6\x89\xa3\xe4\xbb\xb7\xe6\xa0\xbc\xe3\x80\x82\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe8\xa1\xa8\xe7\xa4\xba\xe6\xad\xa4\xe4\xba\xa7\xe5\x93\x81\xe7\x9a\x84\xe6\x98\xbe\xe7\xa4\xba\xe4\xbb\xb7\xe6\xa0\xbc\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe5\xad\x98\xe5\x9c\xa8\xe4\xbb\xbb\xe4\xbd\x95\xe6\x8a\x98\xe6\x89\xa3\xef\xbc\x8c\xe5\x88\x99\xe6\x98\xbe\xe7\xa4\xba\xe7\x9a\x84\xe4\xbb\xb7\xe6\xa0\xbc\xe5\x8c\x85\xe5\x90\xab\xe6\x8a\x98\xe6\x89\xa3\xe4\xbb\xb7\xe6\xa0\xbc\xe3\x80\x82" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayPrice = { "DisplayPrice", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductSku, DisplayPrice), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayPrice_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayPrice_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_OriginalPrice_MetaData[] = {
		{ "Category", "OnePSProductSku" },
		{ "Comment", "/**\n\x09 * @brief \xe8\xa1\xa8\xe7\xa4\xba\xe6\xad\xa4\xe4\xba\xa7\xe5\x93\x81\xe7\x9a\x84\xe5\x8e\x9f\xe5\xa7\x8b\xe4\xbb\xb7\xe6\xa0\xbc\xe3\x80\x82\xe5\xbd\x93\xe5\xad\x98\xe5\x9c\xa8\xe4\xbb\xbb\xe4\xbd\x95\xe6\x8a\x98\xe6\x89\xa3\xef\xbc\x8c\xe8\xaf\xa5\xe5\xad\x97\xe6\xae\xb5\xe6\x9c\x89\xe5\x80\xbc\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe8\xa1\xa8\xe7\xa4\xba\xe6\xad\xa4\xe4\xba\xa7\xe5\x93\x81\xe7\x9a\x84\xe5\x8e\x9f\xe5\xa7\x8b\xe4\xbb\xb7\xe6\xa0\xbc\xe3\x80\x82\xe5\xbd\x93\xe5\xad\x98\xe5\x9c\xa8\xe4\xbb\xbb\xe4\xbd\x95\xe6\x8a\x98\xe6\x89\xa3\xef\xbc\x8c\xe8\xaf\xa5\xe5\xad\x97\xe6\xae\xb5\xe6\x9c\x89\xe5\x80\xbc" },
	};
#endif
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_OriginalPrice = { "OriginalPrice", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductSku, OriginalPrice), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_OriginalPrice_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_OriginalPrice_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayOriginalPrice_MetaData[] = {
		{ "Category", "OnePSProductSku" },
		{ "Comment", "/**\n\x09 * @brief \xe8\xa1\xa8\xe7\xa4\xba\xe5\x8e\x9f\xe5\xa7\x8b\xe4\xbb\xb7\xe6\xa0\xbc\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe3\x80\x82\xe5\xa7\x8b\xe7\xbb\x88\xe4\xb8\x8e\xe5\x88\xa0\xe9\x99\xa4\xe7\xba\xbf\xe6\x90\xad\xe9\x85\x8d\xe4\xbd\xbf\xe7\x94\xa8\xe3\x80\x82\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe8\xa1\xa8\xe7\xa4\xba\xe5\x8e\x9f\xe5\xa7\x8b\xe4\xbb\xb7\xe6\xa0\xbc\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe3\x80\x82\xe5\xa7\x8b\xe7\xbb\x88\xe4\xb8\x8e\xe5\x88\xa0\xe9\x99\xa4\xe7\xba\xbf\xe6\x90\xad\xe9\x85\x8d\xe4\xbd\xbf\xe7\x94\xa8\xe3\x80\x82" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayOriginalPrice = { "DisplayOriginalPrice", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductSku, DisplayOriginalPrice), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayOriginalPrice_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayOriginalPrice_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_UseLimit_MetaData[] = {
		{ "Category", "OnePSProductSku" },
		{ "Comment", "/**\n\x09 * @brief skus \xe5\x8d\x95\xe4\xbd\x8d\xe6\x95\xb0\xe9\x87\x8f\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief skus \xe5\x8d\x95\xe4\xbd\x8d\xe6\x95\xb0\xe9\x87\x8f" },
	};
#endif
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_UseLimit = { "UseLimit", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductSku, UseLimit), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_UseLimit_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_UseLimit_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_PlusUpsellPrice_MetaData[] = {
		{ "Category", "OnePSProductSku" },
		{ "Comment", "/**\n\x09 * @brief Plus \xe4\xbc\x9a\xe5\x91\x98\xe6\x8a\x98\xe6\x89\xa3\xe4\xbb\xb7\xe3\x80\x82\xe6\xad\xa4\xe4\xbb\xb7\xe6\xa0\xbc\xe6\x98\xaf\xe4\xb8\x80\xe4\xb8\xaa\xe6\x95\xb4\xe6\x95\xb0\xef\xbc\x8c\xe4\xb8\x8d\xe8\xa1\xa8\xe7\xa4\xba\xe8\xb4\xa7\xe5\xb8\x81\xe4\xbb\xa3\xe7\xa0\x81\xe6\x88\x96\xe7\xac\xa6\xe5\x8f\xb7\xe3\x80\x82\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief Plus \xe4\xbc\x9a\xe5\x91\x98\xe6\x8a\x98\xe6\x89\xa3\xe4\xbb\xb7\xe3\x80\x82\xe6\xad\xa4\xe4\xbb\xb7\xe6\xa0\xbc\xe6\x98\xaf\xe4\xb8\x80\xe4\xb8\xaa\xe6\x95\xb4\xe6\x95\xb0\xef\xbc\x8c\xe4\xb8\x8d\xe8\xa1\xa8\xe7\xa4\xba\xe8\xb4\xa7\xe5\xb8\x81\xe4\xbb\xa3\xe7\xa0\x81\xe6\x88\x96\xe7\xac\xa6\xe5\x8f\xb7\xe3\x80\x82" },
	};
#endif
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_PlusUpsellPrice = { "PlusUpsellPrice", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductSku, PlusUpsellPrice), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_PlusUpsellPrice_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_PlusUpsellPrice_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayPlusUpsellPrice_MetaData[] = {
		{ "Category", "OnePSProductSku" },
		{ "Comment", "/**\n\x09 * @brief \xe5\xb1\x95\xe7\xa4\xba Plus \xe4\xbc\x9a\xe5\x91\x98\xe6\x8a\x98\xe6\x89\xa3\xe4\xbb\xb7\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe3\x80\x82\xe3\x80\x82\xe5\xa7\x8b\xe7\xbb\x88\xe4\xb8\x8e PlayStation\xc2\xaePlus \xe5\x9b\xbe\xe6\xa0\x87\xe6\x90\xad\xe9\x85\x8d\xe4\xbd\xbf\xe7\x94\xa8\xe3\x80\x82\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe5\xb1\x95\xe7\xa4\xba Plus \xe4\xbc\x9a\xe5\x91\x98\xe6\x8a\x98\xe6\x89\xa3\xe4\xbb\xb7\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe3\x80\x82\xe3\x80\x82\xe5\xa7\x8b\xe7\xbb\x88\xe4\xb8\x8e PlayStation\xc2\xaePlus \xe5\x9b\xbe\xe6\xa0\x87\xe6\x90\xad\xe9\x85\x8d\xe4\xbd\xbf\xe7\x94\xa8\xe3\x80\x82" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayPlusUpsellPrice = { "DisplayPlusUpsellPrice", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductSku, DisplayPlusUpsellPrice), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayPlusUpsellPrice_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayPlusUpsellPrice_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_IsPlusPrice_MetaData[] = {
		{ "Category", "OnePSProductSku" },
		{ "Comment", "/**\n\x09 * @brief  \xe4\xb8\x80\xe4\xb8\xaa\xe5\xb8\x83\xe5\xb0\x94\xe5\x80\xbc\xef\xbc\x8c\xe8\xa1\xa8\xe7\xa4\xba price \xe5\xad\x97\xe6\xae\xb5\xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba Plus \xe4\xbc\x9a\xe5\x91\x98\xe4\xbb\xb7\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief  \xe4\xb8\x80\xe4\xb8\xaa\xe5\xb8\x83\xe5\xb0\x94\xe5\x80\xbc\xef\xbc\x8c\xe8\xa1\xa8\xe7\xa4\xba price \xe5\xad\x97\xe6\xae\xb5\xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba Plus \xe4\xbc\x9a\xe5\x91\x98\xe4\xbb\xb7" },
	};
#endif
	void Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_IsPlusPrice_SetBit(void* Obj)
	{
		((FOnePSProductSku*)Obj)->IsPlusPrice = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_IsPlusPrice = { "IsPlusPrice", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(FOnePSProductSku), &Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_IsPlusPrice_SetBit, METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_IsPlusPrice_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_IsPlusPrice_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_AnnotationName_MetaData[] = {
		{ "Category", "OnePSProductSku" },
		{ "Comment", "/**\n\x09 * @brief \xe6\x8c\x87\xe7\xa4\xba Sku \xe8\xb4\xad\xe4\xb9\xb0\xe7\x8a\xb6\xe6\x80\x81\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe5\x80\xbc\xe3\x80\x82\xe2\x80\x9cNONE\xe2\x80\x9d: \xe6\x9c\xaa\xe8\xb4\xad\xe4\xb9\xb0\xe3\x80\x82\xe2\x80\x9c""BLUE_BAG\xe2\x80\x9d: \xe5\xb7\xb2\xe8\xb4\xad\xe4\xb9\xb0\xef\xbc\x8c\xe5\x8f\xaf\xe9\x87\x8d\xe6\x96\xb0\xe8\xb4\xad\xe4\xb9\xb0\xe3\x80\x82\xe2\x80\x9cRED_BAG\xe2\x80\x9d: \xe5\xb7\xb2\xe8\xb4\xad\xe4\xb9\xb0\xef\xbc\x8c\xe4\xb8\x8d\xe5\x8f\xaf\xe9\x87\x8d\xe6\x96\xb0\xe8\xb4\xad\xe4\xb9\xb0\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe6\x8c\x87\xe7\xa4\xba Sku \xe8\xb4\xad\xe4\xb9\xb0\xe7\x8a\xb6\xe6\x80\x81\xe7\x9a\x84\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe5\x80\xbc\xe3\x80\x82\xe2\x80\x9cNONE\xe2\x80\x9d: \xe6\x9c\xaa\xe8\xb4\xad\xe4\xb9\xb0\xe3\x80\x82\xe2\x80\x9c""BLUE_BAG\xe2\x80\x9d: \xe5\xb7\xb2\xe8\xb4\xad\xe4\xb9\xb0\xef\xbc\x8c\xe5\x8f\xaf\xe9\x87\x8d\xe6\x96\xb0\xe8\xb4\xad\xe4\xb9\xb0\xe3\x80\x82\xe2\x80\x9cRED_BAG\xe2\x80\x9d: \xe5\xb7\xb2\xe8\xb4\xad\xe4\xb9\xb0\xef\xbc\x8c\xe4\xb8\x8d\xe5\x8f\xaf\xe9\x87\x8d\xe6\x96\xb0\xe8\xb4\xad\xe4\xb9\xb0" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_AnnotationName = { "AnnotationName", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductSku, AnnotationName), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_AnnotationName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_AnnotationName_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSProductSku_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_ID,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Name,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Label,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_EndDate,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Type,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_Price,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayPrice,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_OriginalPrice,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayOriginalPrice,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_UseLimit,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_PlusUpsellPrice,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_DisplayPlusUpsellPrice,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_IsPlusPrice,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductSku_Statics::NewProp_AnnotationName,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSProductSku_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OnePSProductSku",
		sizeof(FOnePSProductSku),
		alignof(FOnePSProductSku),
		Z_Construct_UScriptStruct_FOnePSProductSku_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductSku_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductSku()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOnePSProductSku_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OnePSProductSku"), sizeof(FOnePSProductSku), Get_Z_Construct_UScriptStruct_FOnePSProductSku_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOnePSProductSku_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOnePSProductSku_Hash() { return 2835491414U; }
class UScriptStruct* FOnePSProductMedia::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOnePSProductMedia_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSProductMedia, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSProductMedia"), sizeof(FOnePSProductMedia), Get_Z_Construct_UScriptStruct_FOnePSProductMedia_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOnePSProductMedia>()
{
	return FOnePSProductMedia::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOnePSProductMedia(FOnePSProductMedia::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OnePSProductMedia"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSProductMedia
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSProductMedia()
	{
		UScriptStruct::DeferCppStructOps<FOnePSProductMedia>(FName(TEXT("OnePSProductMedia")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSProductMedia;
	struct Z_Construct_UScriptStruct_FOnePSProductMedia_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
		static const UE4CodeGen_Private::FStructPropertyParams NewProp_Images_Inner;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Images_MetaData[];
#endif
		static const UE4CodeGen_Private::FArrayPropertyParams NewProp_Images;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSProductMedia>();
	}
	const UE4CodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::NewProp_Images_Inner = { "Images", nullptr, (EPropertyFlags)0x0000000000000000, UE4CodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, Z_Construct_UScriptStruct_FOnePSProductMediaImage, METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::NewProp_Images_MetaData[] = {
		{ "Category", "OnePSProductMedia" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::NewProp_Images = { "Images", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductMedia, Images), EArrayPropertyFlags::None, METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::NewProp_Images_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::NewProp_Images_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::NewProp_Images_Inner,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::NewProp_Images,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OnePSProductMedia",
		sizeof(FOnePSProductMedia),
		alignof(FOnePSProductMedia),
		Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductMedia()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOnePSProductMedia_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OnePSProductMedia"), sizeof(FOnePSProductMedia), Get_Z_Construct_UScriptStruct_FOnePSProductMedia_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOnePSProductMedia_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOnePSProductMedia_Hash() { return 1568593484U; }
class UScriptStruct* FOnePSProductMediaImage::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOnePSProductMediaImage_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSProductMediaImage, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSProductMediaImage"), sizeof(FOnePSProductMediaImage), Get_Z_Construct_UScriptStruct_FOnePSProductMediaImage_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOnePSProductMediaImage>()
{
	return FOnePSProductMediaImage::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOnePSProductMediaImage(FOnePSProductMediaImage::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OnePSProductMediaImage"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSProductMediaImage
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSProductMediaImage()
	{
		UScriptStruct::DeferCppStructOps<FOnePSProductMediaImage>(FName(TEXT("OnePSProductMediaImage")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSProductMediaImage;
	struct Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Format_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Format;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Type;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Url_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Url;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSProductMediaImage>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Format_MetaData[] = {
		{ "Category", "OnePSProductMediaImage" },
		{ "Comment", "/**\n\x09 * @brief \xe5\xa6\x82,JPEG,PNG,GIF,WEBP\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe5\xa6\x82,JPEG,PNG,GIF,WEBP" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Format = { "Format", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductMediaImage, Format), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Format_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Format_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Type_MetaData[] = {
		{ "Category", "OnePSProductMediaImage" },
		{ "Comment", "/**\n\x09 * @brief \xe5\x9c\xa8PSN\xe4\xbd\xbf\xe7\x94\xa8\xe7\xb1\xbb\xe5\x9e\x8b\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe5\x9c\xa8PSN\xe4\xbd\xbf\xe7\x94\xa8\xe7\xb1\xbb\xe5\x9e\x8b" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductMediaImage, Type), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Type_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Type_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Url_MetaData[] = {
		{ "Category", "OnePSProductMediaImage" },
		{ "Comment", "/**\n\x09 * @brief \xe9\x9d\x99\xe6\x80\x81\xe5\x9b\xbe\xe7\x89\x87URL\xe5\x9c\xb0\xe5\x9d\x80\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe9\x9d\x99\xe6\x80\x81\xe5\x9b\xbe\xe7\x89\x87URL\xe5\x9c\xb0\xe5\x9d\x80" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Url = { "Url", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSProductMediaImage, Url), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Url_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Url_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Format,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Type,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::NewProp_Url,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OnePSProductMediaImage",
		sizeof(FOnePSProductMediaImage),
		alignof(FOnePSProductMediaImage),
		Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOnePSProductMediaImage()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOnePSProductMediaImage_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OnePSProductMediaImage"), sizeof(FOnePSProductMediaImage), Get_Z_Construct_UScriptStruct_FOnePSProductMediaImage_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOnePSProductMediaImage_Hash() { return 1972843184U; }
class UScriptStruct* FOnePSPurchaseForm::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOnePSPurchaseForm_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSPurchaseForm, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSPurchaseForm"), sizeof(FOnePSPurchaseForm), Get_Z_Construct_UScriptStruct_FOnePSPurchaseForm_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOnePSPurchaseForm>()
{
	return FOnePSPurchaseForm::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOnePSPurchaseForm(FOnePSPurchaseForm::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OnePSPurchaseForm"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSPurchaseForm
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSPurchaseForm()
	{
		UScriptStruct::DeferCppStructOps<FOnePSPurchaseForm>(FName(TEXT("OnePSPurchaseForm")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSPurchaseForm;
	struct Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_GameOrderId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_GameOrderId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_GameRoleId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_GameRoleId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_GameRoleName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_GameRoleName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_GameServerId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_GameServerId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ProductId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ProductId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ProductName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ProductName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_OrderAmount_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_OrderAmount;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_GameExtraInfo_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_GameExtraInfo;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_GameDescription_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_GameDescription;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PSNServerLabel_MetaData[];
#endif
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_PSNServerLabel;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSPurchaseForm>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameOrderId_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
		{ "Comment", "/**\n\x09 * @brief \xe6\xb8\xb8\xe6\x88\x8f\xe8\xae\xa2\xe5\x8d\x95Id\xef\xbc\x8c\xe5\xba\x94\xe7\x94\xb1\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe7\x94\x9f\xe6\x88\x90\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe6\xb8\xb8\xe6\x88\x8f\xe8\xae\xa2\xe5\x8d\x95Id\xef\xbc\x8c\xe5\xba\x94\xe7\x94\xb1\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe7\x94\x9f\xe6\x88\x90" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameOrderId = { "GameOrderId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSPurchaseForm, GameOrderId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameOrderId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameOrderId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameRoleId_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
		{ "Comment", "/**\n\x09 * @brief \xe5\xbd\x93\xe5\x89\x8d\xe6\xb8\xb8\xe6\x88\x8f\xe8\xa7\x92\xe8\x89\xb2\xe4\xbf\xa1\xe6\x81\xaf\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe5\xbd\x93\xe5\x89\x8d\xe6\xb8\xb8\xe6\x88\x8f\xe8\xa7\x92\xe8\x89\xb2\xe4\xbf\xa1\xe6\x81\xaf" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameRoleId = { "GameRoleId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSPurchaseForm, GameRoleId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameRoleId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameRoleId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameRoleName_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
		{ "Comment", "/**\n\x09 * @brief \xe5\xbd\x93\xe5\x89\x8d\xe6\xb8\xb8\xe6\x88\x8f\xe8\xa7\x92\xe8\x89\xb2\xe5\x90\x8d\xe7\xa7\xb0\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe5\xbd\x93\xe5\x89\x8d\xe6\xb8\xb8\xe6\x88\x8f\xe8\xa7\x92\xe8\x89\xb2\xe5\x90\x8d\xe7\xa7\xb0" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameRoleName = { "GameRoleName", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSPurchaseForm, GameRoleName), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameRoleName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameRoleName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameServerId_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
		{ "Comment", "/**\n\x09 * @brief \xe5\xbd\x93\xe5\x89\x8d\xe6\xb8\xb8\xe6\x88\x8f\xe8\xa7\x92\xe8\x89\xb2\xe6\x89\x80\xe5\x9c\xa8\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8ID\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe5\xbd\x93\xe5\x89\x8d\xe6\xb8\xb8\xe6\x88\x8f\xe8\xa7\x92\xe8\x89\xb2\xe6\x89\x80\xe5\x9c\xa8\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8ID" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameServerId = { "GameServerId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSPurchaseForm, GameServerId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameServerId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameServerId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_ProductId_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
		{ "Comment", "/**\n\x09 * @brief \xe4\xba\xa7\xe5\x93\x81ID\xef\xbc\x8c\xe8\xaf\xa5\xe5\x80\xbc\xe5\x9c\xa8PS\xe5\xb9\xb3\xe5\x8f\xb0\xe4\xb8\x8a\xef\xbc\x8c\xe5\xba\x94\xe4\xbd\xbf\xe7\x94\xa8 FOnePSProduct.Label \xe5\xad\x97\xe6\xae\xb5\xe5\xb1\x9e\xe6\x80\xa7\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe4\xba\xa7\xe5\x93\x81ID\xef\xbc\x8c\xe8\xaf\xa5\xe5\x80\xbc\xe5\x9c\xa8PS\xe5\xb9\xb3\xe5\x8f\xb0\xe4\xb8\x8a\xef\xbc\x8c\xe5\xba\x94\xe4\xbd\xbf\xe7\x94\xa8 FOnePSProduct.Label \xe5\xad\x97\xe6\xae\xb5\xe5\xb1\x9e\xe6\x80\xa7" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_ProductId = { "ProductId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSPurchaseForm, ProductId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_ProductId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_ProductId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_ProductName_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
		{ "Comment", "/**\n\x09 * @brief \xe4\xba\xa7\xe5\x93\x81\xe5\x90\x8d\xe7\xa7\xb0\xef\xbc\x8c\xe8\xaf\xa5\xe5\x80\xbc\xe5\x9c\xa8PS\xe5\xb9\xb3\xe5\x8f\xb0\xe4\xb8\x8a\xef\xbc\x8c\xe5\xba\x94\xe4\xbd\xbf\xe7\x94\xa8 FOnePSProduct.DisplayName \xe5\xad\x97\xe6\xae\xb5\xe5\xb1\x9e\xe6\x80\xa7\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe4\xba\xa7\xe5\x93\x81\xe5\x90\x8d\xe7\xa7\xb0\xef\xbc\x8c\xe8\xaf\xa5\xe5\x80\xbc\xe5\x9c\xa8PS\xe5\xb9\xb3\xe5\x8f\xb0\xe4\xb8\x8a\xef\xbc\x8c\xe5\xba\x94\xe4\xbd\xbf\xe7\x94\xa8 FOnePSProduct.DisplayName \xe5\xad\x97\xe6\xae\xb5\xe5\xb1\x9e\xe6\x80\xa7" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_ProductName = { "ProductName", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSPurchaseForm, ProductName), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_ProductName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_ProductName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_OrderAmount_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
		{ "Comment", "/**\n\x09 * @brief \xe8\xae\xa2\xe5\x8d\x95\xe6\x80\xbb\xe9\x87\x91\xe9\xa2\x9d\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe8\xae\xa2\xe5\x8d\x95\xe6\x80\xbb\xe9\x87\x91\xe9\xa2\x9d" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_OrderAmount = { "OrderAmount", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSPurchaseForm, OrderAmount), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_OrderAmount_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_OrderAmount_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameExtraInfo_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
		{ "Comment", "/**\n\x09 * @brief \xe9\x80\x8f\xe4\xbc\xa0\xe7\xbb\x99\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe7\xab\xaf\xe7\x9a\x84\xe4\xbf\xa1\xe6\x81\xaf\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe9\x80\x8f\xe4\xbc\xa0\xe7\xbb\x99\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe7\xab\xaf\xe7\x9a\x84\xe4\xbf\xa1\xe6\x81\xaf" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameExtraInfo = { "GameExtraInfo", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSPurchaseForm, GameExtraInfo), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameExtraInfo_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameExtraInfo_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameDescription_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
		{ "Comment", "/**\n\x09 * @brief \xe9\x80\x8f\xe4\xbc\xa0\xe7\xbb\x99\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe7\xab\xaf\xe7\x9a\x84\xe4\xbf\xa1\xe6\x81\xaf\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe9\x80\x8f\xe4\xbc\xa0\xe7\xbb\x99\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe7\xab\xaf\xe7\x9a\x84\xe4\xbf\xa1\xe6\x81\xaf" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameDescription = { "GameDescription", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSPurchaseForm, GameDescription), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameDescription_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameDescription_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_PSNServerLabel_MetaData[] = {
		{ "Category", "OnePSPurchaseForm" },
		{ "Comment", "/**\n\x09 * @brief PSN\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe6\xa0\x87\xe7\xad\xbe, \xe7\x9b\xae\xe5\x89\x8d\xe9\x83\xbd\xe6\x98\xaf\xe9\xbb\x98\xe8\xae\xa4\xe5\x85\xb1\xe4\xba\xabPS4\xe5\x95\x86\xe5\xba\x97\xe7\x9a\x84\xe5\x81\x9a\xe6\xb3\x95\xef\xbc\x8cPS4 \xe5\xba\x94\xe8\xaf\xa5\xe4\xbc\xa0 0\xef\xbc\x8cPS5 \xe4\xb8\xba 1\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief PSN\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe6\xa0\x87\xe7\xad\xbe, \xe7\x9b\xae\xe5\x89\x8d\xe9\x83\xbd\xe6\x98\xaf\xe9\xbb\x98\xe8\xae\xa4\xe5\x85\xb1\xe4\xba\xabPS4\xe5\x95\x86\xe5\xba\x97\xe7\x9a\x84\xe5\x81\x9a\xe6\xb3\x95\xef\xbc\x8cPS4 \xe5\xba\x94\xe8\xaf\xa5\xe4\xbc\xa0 0\xef\xbc\x8cPS5 \xe4\xb8\xba 1" },
	};
#endif
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_PSNServerLabel = { "PSNServerLabel", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSPurchaseForm, PSNServerLabel), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_PSNServerLabel_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_PSNServerLabel_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameOrderId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameRoleId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameRoleName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameServerId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_ProductId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_ProductName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_OrderAmount,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameExtraInfo,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_GameDescription,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::NewProp_PSNServerLabel,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OnePSPurchaseForm",
		sizeof(FOnePSPurchaseForm),
		alignof(FOnePSPurchaseForm),
		Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOnePSPurchaseForm()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOnePSPurchaseForm_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OnePSPurchaseForm"), sizeof(FOnePSPurchaseForm), Get_Z_Construct_UScriptStruct_FOnePSPurchaseForm_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOnePSPurchaseForm_Hash() { return 298622705U; }
class UScriptStruct* FOnePSUserProfileResponse::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSUserProfileResponse, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSUserProfileResponse"), sizeof(FOnePSUserProfileResponse), Get_Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOnePSUserProfileResponse>()
{
	return FOnePSUserProfileResponse::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOnePSUserProfileResponse(FOnePSUserProfileResponse::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OnePSUserProfileResponse"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSUserProfileResponse
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSUserProfileResponse()
	{
		UScriptStruct::DeferCppStructOps<FOnePSUserProfileResponse>(FName(TEXT("OnePSUserProfileResponse")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSUserProfileResponse;
	struct Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
		static const UE4CodeGen_Private::FStructPropertyParams NewProp_Profiles_Inner;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Profiles_MetaData[];
#endif
		static const UE4CodeGen_Private::FArrayPropertyParams NewProp_Profiles;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Total_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_Total;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NextOffset_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_NextOffset;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PreviousOffset_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_PreviousOffset;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSUserProfileResponse>();
	}
	const UE4CodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Profiles_Inner = { "Profiles", nullptr, (EPropertyFlags)0x0000000000000000, UE4CodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, Z_Construct_UScriptStruct_FOnePSUserProfile, METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Profiles_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Profiles = { "Profiles", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSUserProfileResponse, Profiles), EArrayPropertyFlags::None, METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Profiles_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Profiles_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Total_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Total = { "Total", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSUserProfileResponse, Total), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Total_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Total_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_NextOffset_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_NextOffset = { "NextOffset", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSUserProfileResponse, NextOffset), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_NextOffset_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_NextOffset_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_PreviousOffset_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_PreviousOffset = { "PreviousOffset", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSUserProfileResponse, PreviousOffset), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_PreviousOffset_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_PreviousOffset_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Profiles_Inner,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Profiles,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_Total,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_NextOffset,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::NewProp_PreviousOffset,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OnePSUserProfileResponse",
		sizeof(FOnePSUserProfileResponse),
		alignof(FOnePSUserProfileResponse),
		Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOnePSUserProfileResponse()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OnePSUserProfileResponse"), sizeof(FOnePSUserProfileResponse), Get_Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Hash() { return 777297353U; }
class UScriptStruct* FOnePSUserProfile::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOnePSUserProfile_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePSUserProfile, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePSUserProfile"), sizeof(FOnePSUserProfile), Get_Z_Construct_UScriptStruct_FOnePSUserProfile_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOnePSUserProfile>()
{
	return FOnePSUserProfile::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOnePSUserProfile(FOnePSUserProfile::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OnePSUserProfile"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSUserProfile
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSUserProfile()
	{
		UScriptStruct::DeferCppStructOps<FOnePSUserProfile>(FName(TEXT("OnePSUserProfile")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePSUserProfile;
	struct Z_Construct_UScriptStruct_FOnePSUserProfile_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AccountId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_AccountId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_OnlineId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_OnlineId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AvatarUrl_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_AvatarUrl;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AboutMe_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_AboutMe;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bIsOfficiallyVerified_MetaData[];
#endif
		static void NewProp_bIsOfficiallyVerified_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsOfficiallyVerified;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SDKuid_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_SDKuid;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ServerId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ServerId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_RoleId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_RoleId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_RoleName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_RoleName;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePSUserProfile>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AccountId_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AccountId = { "AccountId", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSUserProfile, AccountId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AccountId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AccountId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_OnlineId_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_OnlineId = { "OnlineId", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSUserProfile, OnlineId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_OnlineId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_OnlineId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AvatarUrl_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AvatarUrl = { "AvatarUrl", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSUserProfile, AvatarUrl), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AvatarUrl_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AvatarUrl_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AboutMe_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AboutMe = { "AboutMe", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSUserProfile, AboutMe), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AboutMe_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AboutMe_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_bIsOfficiallyVerified_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	void Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_bIsOfficiallyVerified_SetBit(void* Obj)
	{
		((FOnePSUserProfile*)Obj)->bIsOfficiallyVerified = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_bIsOfficiallyVerified = { "bIsOfficiallyVerified", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(FOnePSUserProfile), &Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_bIsOfficiallyVerified_SetBit, METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_bIsOfficiallyVerified_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_bIsOfficiallyVerified_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_SDKuid_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_SDKuid = { "SDKuid", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSUserProfile, SDKuid), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_SDKuid_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_SDKuid_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_ServerId_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_ServerId = { "ServerId", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSUserProfile, ServerId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_ServerId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_ServerId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_RoleId_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_RoleId = { "RoleId", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSUserProfile, RoleId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_RoleId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_RoleId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_RoleName_MetaData[] = {
		{ "Category", "OneEngineSDKPSSubsystem" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_RoleName = { "RoleName", nullptr, (EPropertyFlags)0x0010000000000014, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePSUserProfile, RoleName), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_RoleName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_RoleName_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AccountId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_OnlineId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AvatarUrl,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_AboutMe,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_bIsOfficiallyVerified,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_SDKuid,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_ServerId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_RoleId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::NewProp_RoleName,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OnePSUserProfile",
		sizeof(FOnePSUserProfile),
		alignof(FOnePSUserProfile),
		Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOnePSUserProfile()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOnePSUserProfile_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OnePSUserProfile"), sizeof(FOnePSUserProfile), Get_Z_Construct_UScriptStruct_FOnePSUserProfile_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOnePSUserProfile_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOnePSUserProfile_Hash() { return 794279796U; }
	DEFINE_FUNCTION(UOnePSLocaleEnum::execIndonesian)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Indonesian();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execVietnamese)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Vietnamese();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execThai)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Thai();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execRomanian)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Romanian();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execGreek)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Greek();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execHungarian)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Hungarian();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execCzech)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Czech();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execFrench_CA)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::French_CA();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execArabic)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Arabic();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execSpanish_LA)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Spanish_LA();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execTurkish)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Turkish();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execEnglish_GB)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::English_GB();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execPortuguese_BR)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Portuguese_BR();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execPolish)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Polish();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execNorwegian)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Norwegian();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execDanish)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Danish();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execSwedish)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Swedish();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execFinnish)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Finnish();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execChinese_S)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Chinese_S();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execChinese_T)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Chinese_T();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execKorean)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Korean();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execRussian)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Russian();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execPortuguese_PT)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Portuguese_PT();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execDutch)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Dutch();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execItalian)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Italian();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execGerman)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::German();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execSpanish)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Spanish();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execFrench)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::French();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execEnglish_US)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::English_US();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOnePSLocaleEnum::execJapanese)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=UOnePSLocaleEnum::Japanese();
		P_NATIVE_END;
	}
	void UOnePSLocaleEnum::StaticRegisterNativesUOnePSLocaleEnum()
	{
		UClass* Class = UOnePSLocaleEnum::StaticClass();
		static const FNameNativePtrPair Funcs[] = {
			{ "Arabic", &UOnePSLocaleEnum::execArabic },
			{ "Chinese_S", &UOnePSLocaleEnum::execChinese_S },
			{ "Chinese_T", &UOnePSLocaleEnum::execChinese_T },
			{ "Czech", &UOnePSLocaleEnum::execCzech },
			{ "Danish", &UOnePSLocaleEnum::execDanish },
			{ "Dutch", &UOnePSLocaleEnum::execDutch },
			{ "English_GB", &UOnePSLocaleEnum::execEnglish_GB },
			{ "English_US", &UOnePSLocaleEnum::execEnglish_US },
			{ "Finnish", &UOnePSLocaleEnum::execFinnish },
			{ "French", &UOnePSLocaleEnum::execFrench },
			{ "French_CA", &UOnePSLocaleEnum::execFrench_CA },
			{ "German", &UOnePSLocaleEnum::execGerman },
			{ "Greek", &UOnePSLocaleEnum::execGreek },
			{ "Hungarian", &UOnePSLocaleEnum::execHungarian },
			{ "Indonesian", &UOnePSLocaleEnum::execIndonesian },
			{ "Italian", &UOnePSLocaleEnum::execItalian },
			{ "Japanese", &UOnePSLocaleEnum::execJapanese },
			{ "Korean", &UOnePSLocaleEnum::execKorean },
			{ "Norwegian", &UOnePSLocaleEnum::execNorwegian },
			{ "Polish", &UOnePSLocaleEnum::execPolish },
			{ "Portuguese_BR", &UOnePSLocaleEnum::execPortuguese_BR },
			{ "Portuguese_PT", &UOnePSLocaleEnum::execPortuguese_PT },
			{ "Romanian", &UOnePSLocaleEnum::execRomanian },
			{ "Russian", &UOnePSLocaleEnum::execRussian },
			{ "Spanish", &UOnePSLocaleEnum::execSpanish },
			{ "Spanish_LA", &UOnePSLocaleEnum::execSpanish_LA },
			{ "Swedish", &UOnePSLocaleEnum::execSwedish },
			{ "Thai", &UOnePSLocaleEnum::execThai },
			{ "Turkish", &UOnePSLocaleEnum::execTurkish },
			{ "Vietnamese", &UOnePSLocaleEnum::execVietnamese },
		};
		FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics
	{
		struct OnePSLocaleEnum_eventArabic_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventArabic_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Arabic", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventArabic_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Arabic()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Arabic_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics
	{
		struct OnePSLocaleEnum_eventChinese_S_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventChinese_S_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Chinese_S", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventChinese_S_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics
	{
		struct OnePSLocaleEnum_eventChinese_T_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventChinese_T_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Chinese_T", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventChinese_T_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics
	{
		struct OnePSLocaleEnum_eventCzech_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventCzech_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Czech", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventCzech_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Czech()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Czech_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics
	{
		struct OnePSLocaleEnum_eventDanish_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventDanish_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Danish", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventDanish_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Danish()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Danish_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics
	{
		struct OnePSLocaleEnum_eventDutch_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventDutch_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Dutch", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventDutch_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Dutch()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Dutch_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics
	{
		struct OnePSLocaleEnum_eventEnglish_GB_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventEnglish_GB_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "English_GB", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventEnglish_GB_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_English_GB()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_English_GB_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics
	{
		struct OnePSLocaleEnum_eventEnglish_US_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventEnglish_US_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "English_US", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventEnglish_US_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_English_US()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_English_US_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics
	{
		struct OnePSLocaleEnum_eventFinnish_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventFinnish_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Finnish", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventFinnish_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Finnish()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Finnish_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics
	{
		struct OnePSLocaleEnum_eventFrench_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventFrench_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "French", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventFrench_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_French()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_French_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics
	{
		struct OnePSLocaleEnum_eventFrench_CA_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventFrench_CA_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "French_CA", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventFrench_CA_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_French_CA()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_French_CA_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics
	{
		struct OnePSLocaleEnum_eventGerman_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventGerman_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "German", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventGerman_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_German()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_German_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics
	{
		struct OnePSLocaleEnum_eventGreek_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventGreek_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Greek", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventGreek_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Greek()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Greek_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics
	{
		struct OnePSLocaleEnum_eventHungarian_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventHungarian_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Hungarian", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventHungarian_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics
	{
		struct OnePSLocaleEnum_eventIndonesian_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventIndonesian_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Indonesian", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventIndonesian_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics
	{
		struct OnePSLocaleEnum_eventItalian_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventItalian_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Italian", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventItalian_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Italian()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Italian_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics
	{
		struct OnePSLocaleEnum_eventJapanese_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventJapanese_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Japanese", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventJapanese_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Japanese()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Japanese_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics
	{
		struct OnePSLocaleEnum_eventKorean_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventKorean_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Korean", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventKorean_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Korean()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Korean_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics
	{
		struct OnePSLocaleEnum_eventNorwegian_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventNorwegian_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Norwegian", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventNorwegian_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics
	{
		struct OnePSLocaleEnum_eventPolish_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventPolish_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Polish", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventPolish_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Polish()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Polish_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics
	{
		struct OnePSLocaleEnum_eventPortuguese_BR_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventPortuguese_BR_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Portuguese_BR", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventPortuguese_BR_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics
	{
		struct OnePSLocaleEnum_eventPortuguese_PT_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventPortuguese_PT_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Portuguese_PT", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventPortuguese_PT_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics
	{
		struct OnePSLocaleEnum_eventRomanian_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventRomanian_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Romanian", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventRomanian_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Romanian()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Romanian_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics
	{
		struct OnePSLocaleEnum_eventRussian_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventRussian_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Russian", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventRussian_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Russian()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Russian_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics
	{
		struct OnePSLocaleEnum_eventSpanish_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventSpanish_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Spanish", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventSpanish_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Spanish()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics
	{
		struct OnePSLocaleEnum_eventSpanish_LA_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventSpanish_LA_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Spanish_LA", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventSpanish_LA_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics
	{
		struct OnePSLocaleEnum_eventSwedish_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventSwedish_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Swedish", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventSwedish_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Swedish()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Swedish_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics
	{
		struct OnePSLocaleEnum_eventThai_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventThai_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Thai", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventThai_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Thai()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Thai_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics
	{
		struct OnePSLocaleEnum_eventTurkish_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventTurkish_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Turkish", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventTurkish_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Turkish()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Turkish_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics
	{
		struct OnePSLocaleEnum_eventVietnamese_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OnePSLocaleEnum_eventVietnamese_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOnePSLocaleEnum, nullptr, "Vietnamese", nullptr, nullptr, sizeof(OnePSLocaleEnum_eventVietnamese_Parms), Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	UClass* Z_Construct_UClass_UOnePSLocaleEnum_NoRegister()
	{
		return UOnePSLocaleEnum::StaticClass();
	}
	struct Z_Construct_UClass_UOnePSLocaleEnum_Statics
	{
		static UObject* (*const DependentSingletons[])();
		static const FClassFunctionLinkInfo FuncInfo[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UOnePSLocaleEnum_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UBlueprintFunctionLibrary,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
	const FClassFunctionLinkInfo Z_Construct_UClass_UOnePSLocaleEnum_Statics::FuncInfo[] = {
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Arabic, "Arabic" }, // 3708853258
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_S, "Chinese_S" }, // 3563746317
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Chinese_T, "Chinese_T" }, // 702352451
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Czech, "Czech" }, // 1054961338
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Danish, "Danish" }, // 2607103178
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Dutch, "Dutch" }, // 3306888740
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_English_GB, "English_GB" }, // 12701216
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_English_US, "English_US" }, // 90776952
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Finnish, "Finnish" }, // 2623529379
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_French, "French" }, // 4288506844
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_French_CA, "French_CA" }, // 292057418
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_German, "German" }, // 2917881479
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Greek, "Greek" }, // 2408891060
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Hungarian, "Hungarian" }, // 3268052465
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Indonesian, "Indonesian" }, // 547560300
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Italian, "Italian" }, // 208483103
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Japanese, "Japanese" }, // 1430578134
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Korean, "Korean" }, // 4214763905
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Norwegian, "Norwegian" }, // 3649185505
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Polish, "Polish" }, // 4057121843
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_BR, "Portuguese_BR" }, // 3168847483
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Portuguese_PT, "Portuguese_PT" }, // 3387024752
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Romanian, "Romanian" }, // 510056602
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Russian, "Russian" }, // 731197990
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Spanish, "Spanish" }, // 1777478272
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Spanish_LA, "Spanish_LA" }, // 2496450489
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Swedish, "Swedish" }, // 1293869707
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Thai, "Thai" }, // 3498855446
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Turkish, "Turkish" }, // 3454435280
		{ &Z_Construct_UFunction_UOnePSLocaleEnum_Vietnamese, "Vietnamese" }, // 2994170221
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UOnePSLocaleEnum_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/// \xe4\xb8\xbb\xe8\xa6\x81\xe7\x94\xa8\xe4\xba\x8e \xe8\x84\x8f\xe8\xaf\x9d\xe8\xbf\x87\xe6\xbb\xa4\xe5\x99\xa8\n" },
		{ "IncludePath", "OneEngineSDKPSSubsystem.h" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "\xe4\xb8\xbb\xe8\xa6\x81\xe7\x94\xa8\xe4\xba\x8e \xe8\x84\x8f\xe8\xaf\x9d\xe8\xbf\x87\xe6\xbb\xa4\xe5\x99\xa8" },
	};
#endif
	const FCppClassTypeInfoStatic Z_Construct_UClass_UOnePSLocaleEnum_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UOnePSLocaleEnum>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UOnePSLocaleEnum_Statics::ClassParams = {
		&UOnePSLocaleEnum::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		FuncInfo,
		nullptr,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		UE_ARRAY_COUNT(FuncInfo),
		0,
		0,
		0x000000A0u,
		METADATA_PARAMS(Z_Construct_UClass_UOnePSLocaleEnum_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UOnePSLocaleEnum_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UOnePSLocaleEnum()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UOnePSLocaleEnum_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UOnePSLocaleEnum, 4127383938);
	template<> ONEENGINESDK_API UClass* StaticClass<UOnePSLocaleEnum>()
	{
		return UOnePSLocaleEnum::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UOnePSLocaleEnum(Z_Construct_UClass_UOnePSLocaleEnum, &UOnePSLocaleEnum::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UOnePSLocaleEnum"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UOnePSLocaleEnum);
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execSetFontPath)
	{
		P_GET_PROPERTY(FStrProperty,Z_Param_Path);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->SetFontPath(Z_Param_Path);
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetProductInfoListPS)
	{
		P_GET_PROPERTY(FIntProperty,Z_Param_ServiceLabel);
		P_GET_PROPERTY(FStrProperty,Z_Param_CategoryLabel);
		P_GET_PROPERTY_REF(FDelegateProperty,Z_Param_Out_Callback);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->GetProductInfoListPS(Z_Param_ServiceLabel,Z_Param_CategoryLabel,FOnGetProductInfoListPSDelegate(Z_Param_Out_Callback));
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execOpenCommerceDialogPremiumMode)
	{
		P_GET_PROPERTY(FDelegateProperty,Z_Param_OnOpenDialogResult);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->OpenCommerceDialogPremiumMode(FOnOpenDialogResultDelegate(Z_Param_OnOpenDialogResult));
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execFilterProfanitySync)
	{
		P_GET_PROPERTY(FStrProperty,Z_Param_Text);
		P_GET_PROPERTY(FStrProperty,Z_Param_Language);
		P_GET_PROPERTY_REF(FStrProperty,Z_Param_Out_OutResult);
		P_FINISH;
		P_NATIVE_BEGIN;
		*(int32*)Z_Param__Result=P_THIS->FilterProfanitySync(Z_Param_Text,Z_Param_Language,Z_Param_Out_OutResult);
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execFilterProfanity)
	{
		P_GET_PROPERTY(FStrProperty,Z_Param_Text);
		P_GET_PROPERTY(FStrProperty,Z_Param_Language);
		P_GET_PROPERTY(FDelegateProperty,Z_Param_OnFilterProfanityResult);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->FilterProfanity(Z_Param_Text,Z_Param_Language,FOnFilterProfanityResultDelegate(Z_Param_OnFilterProfanityResult));
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execStopNotifyPremiumFeature)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(int32*)Z_Param__Result=P_THIS->StopNotifyPremiumFeature();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execStartNotifyPremiumFeature)
	{
		P_GET_PROPERTY(FIntProperty,Z_Param_Interval);
		P_GET_PROPERTY(FIntProperty,Z_Param_Mark);
		P_FINISH;
		P_NATIVE_BEGIN;
		*(int32*)Z_Param__Result=P_THIS->StartNotifyPremiumFeature(Z_Param_Interval,Z_Param_Mark);
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execCheckPremium)
	{
		P_GET_PROPERTY(FDelegateProperty,Z_Param_OnCheckPremiumResult);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->CheckPremium(FOnCheckPremiumResultDelegate(Z_Param_OnCheckPremiumResult));
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetCommunicationRestrictionStatus)
	{
		P_GET_PROPERTY(FDelegateProperty,Z_Param_OnRestrictionStatusResult);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->GetCommunicationRestrictionStatus(FOnRestrictionStatusResultDelegate(Z_Param_OnRestrictionStatusResult));
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execHideStoreIcon)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(int32*)Z_Param__Result=P_THIS->HideStoreIcon();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execShowStoreIcon)
	{
		P_GET_ENUM(EOnePSStoreIconPos,Z_Param_Pos);
		P_FINISH;
		P_NATIVE_BEGIN;
		*(int32*)Z_Param__Result=P_THIS->ShowStoreIcon(EOnePSStoreIconPos(Z_Param_Pos));
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetCountryRegion)
	{
		P_GET_PROPERTY(FStrProperty,Z_Param_CountryCode);
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=P_THIS->GetCountryRegion(Z_Param_CountryCode);
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetCountryCode)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=P_THIS->GetCountryCode();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetOnlineId)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=P_THIS->GetOnlineId();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetAccountId)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(FString*)Z_Param__Result=P_THIS->GetAccountId();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetBlockingUsers)
	{
		P_GET_PROPERTY(FIntProperty,Z_Param_Offset);
		P_GET_PROPERTY(FIntProperty,Z_Param_Limit);
		P_GET_PROPERTY(FDelegateProperty,Z_Param_OnGetBlockingUsersResult);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->GetBlockingUsers(Z_Param_Offset,Z_Param_Limit,FOnGetBlockingUsersResultDelegate(Z_Param_OnGetBlockingUsersResult));
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetFriends)
	{
		P_GET_PROPERTY(FIntProperty,Z_Param_Offset);
		P_GET_PROPERTY(FIntProperty,Z_Param_Limit);
		P_GET_PROPERTY(FDelegateProperty,Z_Param_OnGetFriendsResult);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->GetFriends(Z_Param_Offset,Z_Param_Limit,FOnGetFriendsResultDelegate(Z_Param_OnGetFriendsResult));
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UOneEngineSDKPSSubsystem::execGetAccountState)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(EOnePsnAccountState*)Z_Param__Result=P_THIS->GetAccountState();
		P_NATIVE_END;
	}
	void UOneEngineSDKPSSubsystem::StaticRegisterNativesUOneEngineSDKPSSubsystem()
	{
		UClass* Class = UOneEngineSDKPSSubsystem::StaticClass();
		static const FNameNativePtrPair Funcs[] = {
			{ "CheckPremium", &UOneEngineSDKPSSubsystem::execCheckPremium },
			{ "FilterProfanity", &UOneEngineSDKPSSubsystem::execFilterProfanity },
			{ "FilterProfanitySync", &UOneEngineSDKPSSubsystem::execFilterProfanitySync },
			{ "GetAccountId", &UOneEngineSDKPSSubsystem::execGetAccountId },
			{ "GetAccountState", &UOneEngineSDKPSSubsystem::execGetAccountState },
			{ "GetBlockingUsers", &UOneEngineSDKPSSubsystem::execGetBlockingUsers },
			{ "GetCommunicationRestrictionStatus", &UOneEngineSDKPSSubsystem::execGetCommunicationRestrictionStatus },
			{ "GetCountryCode", &UOneEngineSDKPSSubsystem::execGetCountryCode },
			{ "GetCountryRegion", &UOneEngineSDKPSSubsystem::execGetCountryRegion },
			{ "GetFriends", &UOneEngineSDKPSSubsystem::execGetFriends },
			{ "GetOnlineId", &UOneEngineSDKPSSubsystem::execGetOnlineId },
			{ "GetProductInfoListPS", &UOneEngineSDKPSSubsystem::execGetProductInfoListPS },
			{ "HideStoreIcon", &UOneEngineSDKPSSubsystem::execHideStoreIcon },
			{ "OpenCommerceDialogPremiumMode", &UOneEngineSDKPSSubsystem::execOpenCommerceDialogPremiumMode },
			{ "SetFontPath", &UOneEngineSDKPSSubsystem::execSetFontPath },
			{ "ShowStoreIcon", &UOneEngineSDKPSSubsystem::execShowStoreIcon },
			{ "StartNotifyPremiumFeature", &UOneEngineSDKPSSubsystem::execStartNotifyPremiumFeature },
			{ "StopNotifyPremiumFeature", &UOneEngineSDKPSSubsystem::execStopNotifyPremiumFeature },
		};
		FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics
	{
		struct OneEngineSDKPSSubsystem_eventCheckPremium_Parms
		{
			FScriptDelegate OnCheckPremiumResult;
		};
		static const UE4CodeGen_Private::FDelegatePropertyParams NewProp_OnCheckPremiumResult;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FDelegatePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::NewProp_OnCheckPremiumResult = { "OnCheckPremiumResult", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventCheckPremium_Parms, OnCheckPremiumResult), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::NewProp_OnCheckPremiumResult,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09 * @brief \xe6\xa3\x80\xe6\x9f\xa5\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba\xe4\xbc\x9a\xe5\x91\x98\n\x09 * @param OnCheckPremiumResult \xe6\xa3\x80\xe6\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe7\xbb\x93\xe6\x9e\x9c\xe5\x9b\x9e\xe8\xb0\x83\xef\xbc\x8c""code \xe9\x9d\x9e""0\xe8\xa1\xa8\xe7\xa4\xba\xe6\xa3\x80\xe6\x9f\xa5\xe5\xbc\x82\xe5\xb8\xb8\xef\xbc\x88\xe6\x9c\x89\xe5\x8f\xaf\xe8\x83\xbd\xe6\x98\xaf\xe7\xbd\x91\xe7\xbb\x9c\xe5\x8e\x9f\xe5\x9b\xa0\xe5\xaf\xbc\xe8\x87\xb4\xef\xbc\x8c\xe9\x9c\x80\xe8\xa6\x81\xe8\xbf\x9b\xe8\xa1\x8c\xe9\x87\x8d\xe8\xaf\x95\xef\xbc\x89\xef\xbc\x8c""bIsPremium \xe4\xb8\xba true \xe8\xa1\xa8\xe7\xa4\xba\xe6\x98\xaf\xe4\xbc\x9a\xe5\x91\x98\xef\xbc\x8c""false \xe8\xa1\xa8\xe7\xa4\xba\xe4\xb8\x8d\xe6\x98\xaf\xe4\xbc\x9a\xe5\x91\x98\n\x09 * @warning \xe9\x9c\x80\xe8\xa6\x81\xe5\x9c\xa8\xe8\xbf\x9b\xe5\x85\xa5Premium\xe5\x8a\x9f\xe8\x83\xbd\xe4\xb9\x8b\xe5\x89\x8d\xe8\xb0\x83\xe7\x94\xa8\xe3\x80\x82\xe7\xbd\x91\xe7\xbb\x9c\xe5\xae\x9e\xe6\x97\xb6\xe5\xaf\xb9\xe6\x88\x98\xe5\xb0\xb1\xe6\x98\xafPremium\xe5\x8a\x9f\xe8\x83\xbd\xe3\x80\x82\xe5\x8f\xaf\xe6\x97\xa9\xe4\xb8\x8d\xe5\x8f\xaf\xe6\x99\x9a\xe3\x80\x82\xe5\x9b\xa0\xe6\xad\xa4\xef\xbc\x8c\xe5\xaf\xb9\xe4\xba\x8e\xe7\xbd\x91\xe7\xbb\x9c\xe6\xb8\xb8\xe6\x88\x8f\xef\xbc\x8c\xe5\x8f\xaf\xe4\xbb\xa5\xe5\x9c\xa8\xe5\x90\xaf\xe5\x8a\xa8\xe7\x9a\x84\xe6\x97\xb6\xe5\x80\x99\xe5\xb0\xb1\xe8\xb0\x83\xe7\x94\xa8.\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe6\xa3\x80\xe6\x9f\xa5\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba\xe4\xbc\x9a\xe5\x91\x98\n@param OnCheckPremiumResult \xe6\xa3\x80\xe6\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe7\xbb\x93\xe6\x9e\x9c\xe5\x9b\x9e\xe8\xb0\x83\xef\xbc\x8c""code \xe9\x9d\x9e""0\xe8\xa1\xa8\xe7\xa4\xba\xe6\xa3\x80\xe6\x9f\xa5\xe5\xbc\x82\xe5\xb8\xb8\xef\xbc\x88\xe6\x9c\x89\xe5\x8f\xaf\xe8\x83\xbd\xe6\x98\xaf\xe7\xbd\x91\xe7\xbb\x9c\xe5\x8e\x9f\xe5\x9b\xa0\xe5\xaf\xbc\xe8\x87\xb4\xef\xbc\x8c\xe9\x9c\x80\xe8\xa6\x81\xe8\xbf\x9b\xe8\xa1\x8c\xe9\x87\x8d\xe8\xaf\x95\xef\xbc\x89\xef\xbc\x8c""bIsPremium \xe4\xb8\xba true \xe8\xa1\xa8\xe7\xa4\xba\xe6\x98\xaf\xe4\xbc\x9a\xe5\x91\x98\xef\xbc\x8c""false \xe8\xa1\xa8\xe7\xa4\xba\xe4\xb8\x8d\xe6\x98\xaf\xe4\xbc\x9a\xe5\x91\x98\n@warning \xe9\x9c\x80\xe8\xa6\x81\xe5\x9c\xa8\xe8\xbf\x9b\xe5\x85\xa5Premium\xe5\x8a\x9f\xe8\x83\xbd\xe4\xb9\x8b\xe5\x89\x8d\xe8\xb0\x83\xe7\x94\xa8\xe3\x80\x82\xe7\xbd\x91\xe7\xbb\x9c\xe5\xae\x9e\xe6\x97\xb6\xe5\xaf\xb9\xe6\x88\x98\xe5\xb0\xb1\xe6\x98\xafPremium\xe5\x8a\x9f\xe8\x83\xbd\xe3\x80\x82\xe5\x8f\xaf\xe6\x97\xa9\xe4\xb8\x8d\xe5\x8f\xaf\xe6\x99\x9a\xe3\x80\x82\xe5\x9b\xa0\xe6\xad\xa4\xef\xbc\x8c\xe5\xaf\xb9\xe4\xba\x8e\xe7\xbd\x91\xe7\xbb\x9c\xe6\xb8\xb8\xe6\x88\x8f\xef\xbc\x8c\xe5\x8f\xaf\xe4\xbb\xa5\xe5\x9c\xa8\xe5\x90\xaf\xe5\x8a\xa8\xe7\x9a\x84\xe6\x97\xb6\xe5\x80\x99\xe5\xb0\xb1\xe8\xb0\x83\xe7\x94\xa8." },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "CheckPremium", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventCheckPremium_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics
	{
		struct OneEngineSDKPSSubsystem_eventFilterProfanity_Parms
		{
			FString Text;
			FString Language;
			FScriptDelegate OnFilterProfanityResult;
		};
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Text_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Text;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Language_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Language;
		static const UE4CodeGen_Private::FDelegatePropertyParams NewProp_OnFilterProfanityResult;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_Text_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_Text = { "Text", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventFilterProfanity_Parms, Text), METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_Text_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_Text_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_Language_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_Language = { "Language", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventFilterProfanity_Parms, Language), METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_Language_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_Language_MetaData)) };
	const UE4CodeGen_Private::FDelegatePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_OnFilterProfanityResult = { "OnFilterProfanityResult", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventFilterProfanity_Parms, OnFilterProfanityResult), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_Text,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_Language,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::NewProp_OnFilterProfanityResult,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09 * @brief \xe8\x84\x8f\xe8\xaf\x9d\xe8\xbf\x87\xe6\xbb\xa4\xe5\x99\xa8\xef\xbc\x8c\xe5\xae\xa1\xe6\x9f\xa5\xe7\x94\xa8\xe6\x88\xb7\xe5\x86\x85\xe5\xae\xb9\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xb8\xad\xe7\x9a\x84\xe4\xba\xb5\xe6\xb8\x8e\xe8\xa1\x8c\xe4\xb8\xba\xe3\x80\x82\xe8\xaf\xa5\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xbc\x9a\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xad\xe8\xbf\x94\xe5\x9b\x9e\xef\xbc\x8c\xe5\xb9\xb6\xe4\xb8\x94\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe7\x9a\x84\xe4\xbb\xbb\xe4\xbd\x95\xe4\xb8\x8d\xe9\x9b\x85\xe5\x86\x85\xe5\xae\xb9\xe9\x83\xbd\xe5\xb0\x86\xe6\x9b\xbf\xe6\x8d\xa2\xe4\xb8\xba\xe2\x80\x9c*\xe2\x80\x9d\xe5\xad\x97\xe7\xac\xa6\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\x9c\xa8\xe6\x96\x87\xe6\x9c\xac\xe4\xb8\xad\xe6\x9c\xaa\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe8\x84\x8f\xe8\xaf\x9d\xef\xbc\x8c\xe5\x88\x99\xe5\x8e\x9f\xe5\xb0\x81\xe4\xb8\x8d\xe5\x8a\xa8\xe5\x9c\xb0\xe8\xbf\x94\xe5\x9b\x9e\xe3\x80\x82\n\x09 * @param Text \xe8\xa6\x81\xe8\xbf\x87\xe6\xbb\xa4\xe7\x9a\x84\xe6\x96\x87\xe6\x9c\xac\xe3\x80\x82\xe6\xad\xa4\xe6\x96\x87\xe6\x9c\xac\xe7\x9a\x84\xe6\x9c\x80\xe5\xa4\xa7\xe5\xa4\xa7\xe5\xb0\x8f\xe4\xb8\xba 4KB\xe3\x80\x82\n\x09 * @param Language \xe4\xbe\x8b\xe5\xa6\x82\xef\xbc\x8c\xe2\x80\x9c""en-GB\xe2\x80\x9d,\xe5\x85\xb7\xe4\xbd\x93\xe6\x94\xaf\xe6\x8c\x81\xe7\x9a\x84locale\xe5\x80\xbc \xe8\xaf\xb7\xe5\x8f\x82\xe8\x80\x83 @see UOnePSLocaleEnum\n\x09 * @param OnFilterProfanityResult \xe5\x9b\x9e\xe8\xb0\x83\n\x09 * @note \xe4\xbd\x9c\xe4\xb8\xba\xe6\x9c\x80\xe4\xbd\xb3\xe5\xae\x9e\xe8\xb7\xb5\xef\xbc\x8c\xe5\xbf\xbd\xe7\x95\xa5\xe8\xb0\x83\xe7\x94\xa8\xe6\xad\xa4 API \xe6\x97\xb6\xe9\x81\x87\xe5\x88\xb0\xe7\x9a\x84\xe9\x94\x99\xe8\xaf\xaf\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe5\x9c\xa8\xe8\xb0\x83\xe7\x94\xa8\xe6\xad\xa4 API \xe6\x97\xb6\xe5\x8f\x91\xe7\x94\x9f\xe9\x94\x99\xe8\xaf\xaf\xef\xbc\x8c\xe5\xba\x94\xe7\x94\xa8\xe7\xa8\x8b\xe5\xba\x8f\xe5\xba\x94\xe7\xbb\xa7\xe7\xbb\xad\xe5\x89\x8d\xe8\xbf\x9b\xef\xbc\x8c\xe5\xb0\xb1\xe5\xa5\xbd\xe5\x83\x8f\xe6\x9c\xaa\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe4\xba\xb5\xe6\xb8\x8e\xe8\xa1\x8c\xe4\xb8\xba\xe4\xb8\x80\xe6\xa0\xb7\xe3\x80\x82\n\x09 * @warning \xe8\xb0\x83\xe7\x94\xa8\xe9\xa2\x91\xe7\x8e\x87\xe9\x99\x90\xe5\x88\xb6 300/minute \xef\xbc\x8c\xe8\xb6\x85\xe8\xbf\x87\xe9\x99\x90\xe5\x88\xb6\xef\xbc\x8cSIE\xe5\xb0\x86\xe9\x99\x90\xe5\x88\xb6\xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe8\x84\x8f\xe8\xaf\x9d\xe8\xbf\x87\xe6\xbb\xa4\xe5\x99\xa8\xef\xbc\x8c\xe5\xae\xa1\xe6\x9f\xa5\xe7\x94\xa8\xe6\x88\xb7\xe5\x86\x85\xe5\xae\xb9\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xb8\xad\xe7\x9a\x84\xe4\xba\xb5\xe6\xb8\x8e\xe8\xa1\x8c\xe4\xb8\xba\xe3\x80\x82\xe8\xaf\xa5\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xbc\x9a\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xad\xe8\xbf\x94\xe5\x9b\x9e\xef\xbc\x8c\xe5\xb9\xb6\xe4\xb8\x94\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe7\x9a\x84\xe4\xbb\xbb\xe4\xbd\x95\xe4\xb8\x8d\xe9\x9b\x85\xe5\x86\x85\xe5\xae\xb9\xe9\x83\xbd\xe5\xb0\x86\xe6\x9b\xbf\xe6\x8d\xa2\xe4\xb8\xba\xe2\x80\x9c*\xe2\x80\x9d\xe5\xad\x97\xe7\xac\xa6\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\x9c\xa8\xe6\x96\x87\xe6\x9c\xac\xe4\xb8\xad\xe6\x9c\xaa\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe8\x84\x8f\xe8\xaf\x9d\xef\xbc\x8c\xe5\x88\x99\xe5\x8e\x9f\xe5\xb0\x81\xe4\xb8\x8d\xe5\x8a\xa8\xe5\x9c\xb0\xe8\xbf\x94\xe5\x9b\x9e\xe3\x80\x82\n@param Text \xe8\xa6\x81\xe8\xbf\x87\xe6\xbb\xa4\xe7\x9a\x84\xe6\x96\x87\xe6\x9c\xac\xe3\x80\x82\xe6\xad\xa4\xe6\x96\x87\xe6\x9c\xac\xe7\x9a\x84\xe6\x9c\x80\xe5\xa4\xa7\xe5\xa4\xa7\xe5\xb0\x8f\xe4\xb8\xba 4KB\xe3\x80\x82\n@param Language \xe4\xbe\x8b\xe5\xa6\x82\xef\xbc\x8c\xe2\x80\x9c""en-GB\xe2\x80\x9d,\xe5\x85\xb7\xe4\xbd\x93\xe6\x94\xaf\xe6\x8c\x81\xe7\x9a\x84locale\xe5\x80\xbc \xe8\xaf\xb7\xe5\x8f\x82\xe8\x80\x83 @see UOnePSLocaleEnum\n@param OnFilterProfanityResult \xe5\x9b\x9e\xe8\xb0\x83\n@note \xe4\xbd\x9c\xe4\xb8\xba\xe6\x9c\x80\xe4\xbd\xb3\xe5\xae\x9e\xe8\xb7\xb5\xef\xbc\x8c\xe5\xbf\xbd\xe7\x95\xa5\xe8\xb0\x83\xe7\x94\xa8\xe6\xad\xa4 API \xe6\x97\xb6\xe9\x81\x87\xe5\x88\xb0\xe7\x9a\x84\xe9\x94\x99\xe8\xaf\xaf\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe5\x9c\xa8\xe8\xb0\x83\xe7\x94\xa8\xe6\xad\xa4 API \xe6\x97\xb6\xe5\x8f\x91\xe7\x94\x9f\xe9\x94\x99\xe8\xaf\xaf\xef\xbc\x8c\xe5\xba\x94\xe7\x94\xa8\xe7\xa8\x8b\xe5\xba\x8f\xe5\xba\x94\xe7\xbb\xa7\xe7\xbb\xad\xe5\x89\x8d\xe8\xbf\x9b\xef\xbc\x8c\xe5\xb0\xb1\xe5\xa5\xbd\xe5\x83\x8f\xe6\x9c\xaa\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe4\xba\xb5\xe6\xb8\x8e\xe8\xa1\x8c\xe4\xb8\xba\xe4\xb8\x80\xe6\xa0\xb7\xe3\x80\x82\n@warning \xe8\xb0\x83\xe7\x94\xa8\xe9\xa2\x91\xe7\x8e\x87\xe9\x99\x90\xe5\x88\xb6 300/minute \xef\xbc\x8c\xe8\xb6\x85\xe8\xbf\x87\xe9\x99\x90\xe5\x88\xb6\xef\xbc\x8cSIE\xe5\xb0\x86\xe9\x99\x90\xe5\x88\xb6\xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "FilterProfanity", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventFilterProfanity_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics
	{
		struct OneEngineSDKPSSubsystem_eventFilterProfanitySync_Parms
		{
			FString Text;
			FString Language;
			FString OutResult;
			int32 ReturnValue;
		};
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Text_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Text;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Language_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Language;
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_OutResult;
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_Text_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_Text = { "Text", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventFilterProfanitySync_Parms, Text), METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_Text_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_Text_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_Language_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_Language = { "Language", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventFilterProfanitySync_Parms, Language), METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_Language_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_Language_MetaData)) };
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_OutResult = { "OutResult", nullptr, (EPropertyFlags)0x0010000000000180, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventFilterProfanitySync_Parms, OutResult), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventFilterProfanitySync_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_Text,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_Language,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_OutResult,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09 * @brief  \xe8\x84\x8f\xe8\xaf\x9d\xe8\xbf\x87\xe6\xbb\xa4\xe5\x99\xa8\xef\xbc\x8c\xe5\xae\xa1\xe6\x9f\xa5\xe7\x94\xa8\xe6\x88\xb7\xe5\x86\x85\xe5\xae\xb9\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xb8\xad\xe7\x9a\x84\xe4\xba\xb5\xe6\xb8\x8e\xe8\xa1\x8c\xe4\xb8\xba\xe3\x80\x82 \xe5\x90\x8c\xe6\xad\xa5\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x8c\xe4\xbc\x9a\xe9\x98\xbb\xe5\xa1\x9e\xe5\xbd\x93\xe5\x89\x8d\xe7\xba\xbf\xe7\xa8\x8b\n\x09 * @param Text \xe8\xa6\x81\xe8\xbf\x87\xe6\xbb\xa4\xe7\x9a\x84\xe6\x96\x87\xe6\x9c\xac\xe3\x80\x82\xe6\xad\xa4\xe6\x96\x87\xe6\x9c\xac\xe7\x9a\x84\xe6\x9c\x80\xe5\xa4\xa7\xe5\xa4\xa7\xe5\xb0\x8f\xe4\xb8\xba 4KB\xe3\x80\x82\n\x09 * @param Language \xe4\xbe\x8b\xe5\xa6\x82\xef\xbc\x8c\xe2\x80\x9c""en-GB\xe2\x80\x9d,\xe5\x85\xb7\xe4\xbd\x93\xe6\x94\xaf\xe6\x8c\x81\xe7\x9a\x84locale\xe5\x80\xbc \xe8\xaf\xb7\xe5\x8f\x82\xe8\x80\x83 @see UOnePSLocaleEnum\n\x09 * @param OutResult \xe8\xaf\xa5\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xbc\x9a\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xad\xe8\xbf\x94\xe5\x9b\x9e\xef\xbc\x8c\xe5\xb9\xb6\xe4\xb8\x94\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe7\x9a\x84\xe4\xbb\xbb\xe4\xbd\x95\xe4\xb8\x8d\xe9\x9b\x85\xe5\x86\x85\xe5\xae\xb9\xe9\x83\xbd\xe5\xb0\x86\xe6\x9b\xbf\xe6\x8d\xa2\xe4\xb8\xba\xe2\x80\x9c*\xe2\x80\x9d\xe5\xad\x97\xe7\xac\xa6\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\x9c\xa8\xe6\x96\x87\xe6\x9c\xac\xe4\xb8\xad\xe6\x9c\xaa\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe8\x84\x8f\xe8\xaf\x9d\xef\xbc\x8c\xe5\x88\x99\xe5\x8e\x9f\xe5\xb0\x81\xe4\xb8\x8d\xe5\x8a\xa8\xe5\x9c\xb0\xe8\xbf\x94\xe5\x9b\x9e\xe3\x80\x82\n\x09 * @return 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe5\xa4\xb1\xe8\xb4\xa5\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief  \xe8\x84\x8f\xe8\xaf\x9d\xe8\xbf\x87\xe6\xbb\xa4\xe5\x99\xa8\xef\xbc\x8c\xe5\xae\xa1\xe6\x9f\xa5\xe7\x94\xa8\xe6\x88\xb7\xe5\x86\x85\xe5\xae\xb9\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xb8\xad\xe7\x9a\x84\xe4\xba\xb5\xe6\xb8\x8e\xe8\xa1\x8c\xe4\xb8\xba\xe3\x80\x82 \xe5\x90\x8c\xe6\xad\xa5\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x8c\xe4\xbc\x9a\xe9\x98\xbb\xe5\xa1\x9e\xe5\xbd\x93\xe5\x89\x8d\xe7\xba\xbf\xe7\xa8\x8b\n@param Text \xe8\xa6\x81\xe8\xbf\x87\xe6\xbb\xa4\xe7\x9a\x84\xe6\x96\x87\xe6\x9c\xac\xe3\x80\x82\xe6\xad\xa4\xe6\x96\x87\xe6\x9c\xac\xe7\x9a\x84\xe6\x9c\x80\xe5\xa4\xa7\xe5\xa4\xa7\xe5\xb0\x8f\xe4\xb8\xba 4KB\xe3\x80\x82\n@param Language \xe4\xbe\x8b\xe5\xa6\x82\xef\xbc\x8c\xe2\x80\x9c""en-GB\xe2\x80\x9d,\xe5\x85\xb7\xe4\xbd\x93\xe6\x94\xaf\xe6\x8c\x81\xe7\x9a\x84locale\xe5\x80\xbc \xe8\xaf\xb7\xe5\x8f\x82\xe8\x80\x83 @see UOnePSLocaleEnum\n@param OutResult \xe8\xaf\xa5\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\xe4\xbc\x9a\xe5\x9c\xa8\xe5\x93\x8d\xe5\xba\x94\xe4\xb8\xad\xe8\xbf\x94\xe5\x9b\x9e\xef\xbc\x8c\xe5\xb9\xb6\xe4\xb8\x94\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe7\x9a\x84\xe4\xbb\xbb\xe4\xbd\x95\xe4\xb8\x8d\xe9\x9b\x85\xe5\x86\x85\xe5\xae\xb9\xe9\x83\xbd\xe5\xb0\x86\xe6\x9b\xbf\xe6\x8d\xa2\xe4\xb8\xba\xe2\x80\x9c*\xe2\x80\x9d\xe5\xad\x97\xe7\xac\xa6\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\x9c\xa8\xe6\x96\x87\xe6\x9c\xac\xe4\xb8\xad\xe6\x9c\xaa\xe6\xa3\x80\xe6\xb5\x8b\xe5\x88\xb0\xe8\x84\x8f\xe8\xaf\x9d\xef\xbc\x8c\xe5\x88\x99\xe5\x8e\x9f\xe5\xb0\x81\xe4\xb8\x8d\xe5\x8a\xa8\xe5\x9c\xb0\xe8\xbf\x94\xe5\x9b\x9e\xe3\x80\x82\n@return 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe5\xa4\xb1\xe8\xb4\xa5" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "FilterProfanitySync", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventFilterProfanitySync_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics
	{
		struct OneEngineSDKPSSubsystem_eventGetAccountId_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetAccountId_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09* @brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84""AccountId\n\x09* @return FString AccountId\n\x09*/" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84""AccountId\n@return FString AccountId" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetAccountId", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventGetAccountId_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics
	{
		struct OneEngineSDKPSSubsystem_eventGetAccountState_Parms
		{
			EOnePsnAccountState ReturnValue;
		};
		static const UE4CodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
		static const UE4CodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UE4CodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, nullptr, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetAccountState_Parms, ReturnValue), Z_Construct_UEnum_OneEngineSDK_EOnePsnAccountState, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::NewProp_ReturnValue_Underlying,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09 * @brief \xe8\x8e\xb7\xe5\x8f\x96PSN\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x8a\xb6\xe6\x80\x81\n\x09 * @return EOnePsnAccountState PSN\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x8a\xb6\xe6\x80\x81\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe8\x8e\xb7\xe5\x8f\x96PSN\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x8a\xb6\xe6\x80\x81\n@return EOnePsnAccountState PSN\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x8a\xb6\xe6\x80\x81" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetAccountState", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventGetAccountState_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics
	{
		struct OneEngineSDKPSSubsystem_eventGetBlockingUsers_Parms
		{
			int32 Offset;
			int32 Limit;
			FScriptDelegate OnGetBlockingUsersResult;
		};
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_Offset;
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_Limit;
		static const UE4CodeGen_Private::FDelegatePropertyParams NewProp_OnGetBlockingUsersResult;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::NewProp_Offset = { "Offset", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetBlockingUsers_Parms, Offset), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::NewProp_Limit = { "Limit", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetBlockingUsers_Parms, Limit), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FDelegatePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::NewProp_OnGetBlockingUsersResult = { "OnGetBlockingUsersResult", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetBlockingUsers_Parms, OnGetBlockingUsersResult), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::NewProp_Offset,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::NewProp_Limit,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::NewProp_OnGetBlockingUsersResult,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09 * @brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xb1\x8f\xe8\x94\xbd\xe5\x88\x97\xe8\xa1\xa8\n\x09 * @param Offset \xe5\x81\x8f\xe7\xa7\xbb\xe9\x87\x8f\n\x09 * @param Limit \xe9\x99\x90\xe5\x88\xb6\xe6\x95\xb0\xe9\x87\x8f\n\x09 * @param OnGetBlockingUsersResult \xe8\x8e\xb7\xe5\x8f\x96\xe5\xb1\x8f\xe8\x94\xbd\xe5\x88\x97\xe8\xa1\xa8\xe5\x9b\x9e\xe8\xb0\x83\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xb1\x8f\xe8\x94\xbd\xe5\x88\x97\xe8\xa1\xa8\n@param Offset \xe5\x81\x8f\xe7\xa7\xbb\xe9\x87\x8f\n@param Limit \xe9\x99\x90\xe5\x88\xb6\xe6\x95\xb0\xe9\x87\x8f\n@param OnGetBlockingUsersResult \xe8\x8e\xb7\xe5\x8f\x96\xe5\xb1\x8f\xe8\x94\xbd\xe5\x88\x97\xe8\xa1\xa8\xe5\x9b\x9e\xe8\xb0\x83" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetBlockingUsers", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventGetBlockingUsers_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics
	{
		struct OneEngineSDKPSSubsystem_eventGetCommunicationRestrictionStatus_Parms
		{
			FScriptDelegate OnRestrictionStatusResult;
		};
		static const UE4CodeGen_Private::FDelegatePropertyParams NewProp_OnRestrictionStatusResult;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FDelegatePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::NewProp_OnRestrictionStatusResult = { "OnRestrictionStatusResult", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetCommunicationRestrictionStatus_Parms, OnRestrictionStatusResult), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::NewProp_OnRestrictionStatusResult,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09 * @brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe9\x80\x9a\xe4\xbf\xa1\xe9\x99\x90\xe5\x88\xb6\xe7\x8a\xb6\xe6\x80\x81\n\x09 * @param OnRestrictionStatusResult \xe8\x8e\xb7\xe5\x8f\x96\xe9\x80\x9a\xe4\xbf\xa1\xe9\x99\x90\xe5\x88\xb6\xe7\x8a\xb6\xe6\x80\x81\xe5\x9b\x9e\xe8\xb0\x83\xef\xbc\x8cResult \xe5\x8f\xaf\xe8\x83\xbd\xe7\x9a\x84\xe5\x80\xbc -1 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xa3\x80\xe6\x9f\xa5\xe5\xa4\xb1\xe8\xb4\xa5\xef\xbc\x8c 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x9c\xaa\xe6\xa3\x80\xe6\x9f\xa5\xef\xbc\x8c 1 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x9c\xaa\xe9\x99\x90\xe5\x88\xb6\xef\xbc\x8c 2 \xe8\xa1\xa8\xe7\xa4\xba\xe5\x8f\x97\xe9\x99\x90\xe5\x88\xb6\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe9\x80\x9a\xe4\xbf\xa1\xe9\x99\x90\xe5\x88\xb6\xe7\x8a\xb6\xe6\x80\x81\n@param OnRestrictionStatusResult \xe8\x8e\xb7\xe5\x8f\x96\xe9\x80\x9a\xe4\xbf\xa1\xe9\x99\x90\xe5\x88\xb6\xe7\x8a\xb6\xe6\x80\x81\xe5\x9b\x9e\xe8\xb0\x83\xef\xbc\x8cResult \xe5\x8f\xaf\xe8\x83\xbd\xe7\x9a\x84\xe5\x80\xbc -1 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xa3\x80\xe6\x9f\xa5\xe5\xa4\xb1\xe8\xb4\xa5\xef\xbc\x8c 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x9c\xaa\xe6\xa3\x80\xe6\x9f\xa5\xef\xbc\x8c 1 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x9c\xaa\xe9\x99\x90\xe5\x88\xb6\xef\xbc\x8c 2 \xe8\xa1\xa8\xe7\xa4\xba\xe5\x8f\x97\xe9\x99\x90\xe5\x88\xb6" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetCommunicationRestrictionStatus", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventGetCommunicationRestrictionStatus_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics
	{
		struct OneEngineSDKPSSubsystem_eventGetCountryCode_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetCountryCode_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09 * @brief \xe8\x8e\xb7\xe5\x8f\x96 CountryCode\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe8\x8e\xb7\xe5\x8f\x96 CountryCode" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetCountryCode", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventGetCountryCode_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics
	{
		struct OneEngineSDKPSSubsystem_eventGetCountryRegion_Parms
		{
			FString CountryCode;
			FString ReturnValue;
		};
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CountryCode_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_CountryCode;
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::NewProp_CountryCode_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::NewProp_CountryCode = { "CountryCode", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetCountryRegion_Parms, CountryCode), METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::NewProp_CountryCode_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::NewProp_CountryCode_MetaData)) };
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetCountryRegion_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::NewProp_CountryCode,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09 * @brief \xe6\xa0\xb9\xe6\x8d\xae\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\xe8\x8e\xb7\xe5\x8f\x96\xe5\x9b\xbd\xe5\xae\xb6\xe6\x89\x80\xe5\xb1\x9e\xe5\x9c\xb0\xe5\x8c\xba\n\x09 * @param CountryCode \xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\n\x09 * @return \xe5\x9b\xbd\xe5\xae\xb6\xe5\x9c\xb0\xe5\x8c\xba\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe6\xa0\xb9\xe6\x8d\xae\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\xe8\x8e\xb7\xe5\x8f\x96\xe5\x9b\xbd\xe5\xae\xb6\xe6\x89\x80\xe5\xb1\x9e\xe5\x9c\xb0\xe5\x8c\xba\n@param CountryCode \xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\n@return \xe5\x9b\xbd\xe5\xae\xb6\xe5\x9c\xb0\xe5\x8c\xba" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetCountryRegion", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventGetCountryRegion_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics
	{
		struct OneEngineSDKPSSubsystem_eventGetFriends_Parms
		{
			int32 Offset;
			int32 Limit;
			FScriptDelegate OnGetFriendsResult;
		};
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_Offset;
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_Limit;
		static const UE4CodeGen_Private::FDelegatePropertyParams NewProp_OnGetFriendsResult;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::NewProp_Offset = { "Offset", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetFriends_Parms, Offset), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::NewProp_Limit = { "Limit", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetFriends_Parms, Limit), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FDelegatePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::NewProp_OnGetFriendsResult = { "OnGetFriendsResult", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetFriends_Parms, OnGetFriendsResult), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::NewProp_Offset,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::NewProp_Limit,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::NewProp_OnGetFriendsResult,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09 * @brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xa5\xbd\xe5\x8f\x8b\xe5\x88\x97\xe8\xa1\xa8\n\x09 * @param Offset \xe5\x81\x8f\xe7\xa7\xbb\xe9\x87\x8f\n\x09 * @param Limit \xe9\x99\x90\xe5\x88\xb6\xe6\x95\xb0\xe9\x87\x8f\n\x09 * @param OnGetFriendsResult \xe8\x8e\xb7\xe5\x8f\x96\xe5\xa5\xbd\xe5\x8f\x8b\xe5\x88\x97\xe8\xa1\xa8\xe5\x9b\x9e\xe8\xb0\x83\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xa5\xbd\xe5\x8f\x8b\xe5\x88\x97\xe8\xa1\xa8\n@param Offset \xe5\x81\x8f\xe7\xa7\xbb\xe9\x87\x8f\n@param Limit \xe9\x99\x90\xe5\x88\xb6\xe6\x95\xb0\xe9\x87\x8f\n@param OnGetFriendsResult \xe8\x8e\xb7\xe5\x8f\x96\xe5\xa5\xbd\xe5\x8f\x8b\xe5\x88\x97\xe8\xa1\xa8\xe5\x9b\x9e\xe8\xb0\x83" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetFriends", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventGetFriends_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics
	{
		struct OneEngineSDKPSSubsystem_eventGetOnlineId_Parms
		{
			FString ReturnValue;
		};
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetOnlineId_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09* @brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84OnlineId\n\x09* @return  FString OnlineId\n\x09*/" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe8\x8e\xb7\xe5\x8f\x96\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84OnlineId\n@return  FString OnlineId" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetOnlineId", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventGetOnlineId_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics
	{
		struct OneEngineSDKPSSubsystem_eventGetProductInfoListPS_Parms
		{
			int32 ServiceLabel;
			FString CategoryLabel;
			FScriptDelegate Callback;
		};
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_ServiceLabel;
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_CategoryLabel;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Callback_MetaData[];
#endif
		static const UE4CodeGen_Private::FDelegatePropertyParams NewProp_Callback;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::NewProp_ServiceLabel = { "ServiceLabel", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetProductInfoListPS_Parms, ServiceLabel), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::NewProp_CategoryLabel = { "CategoryLabel", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetProductInfoListPS_Parms, CategoryLabel), METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::NewProp_Callback_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FDelegatePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::NewProp_Callback = { "Callback", nullptr, (EPropertyFlags)0x0010000008000182, UE4CodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventGetProductInfoListPS_Parms, Callback), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::NewProp_Callback_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::NewProp_Callback_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::NewProp_ServiceLabel,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::NewProp_CategoryLabel,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::NewProp_Callback,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09 * @brief  \xe8\x8e\xb7\xe5\x8f\x96\xe4\xba\xa7\xe5\x93\x81\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\x97\xe8\xa1\xa8\n\x09 * @param ServiceLabel \xe5\xa6\x82\xe6\x9e\x9c\xe5\x85\xb1\xe7\x94\xa8PS4\xe7\x9a\x84\xe5\x90\x8e\xe5\x8f\xb0\xe5\x95\x86\xe5\xba\x97\xe7\x9a\x84\xe8\xaf\x9d\xef\xbc\x8cPS5 \xe4\xb8\xba 1\xef\xbc\x8cPS4 \xe4\xb8\xba 0\n\x09 * @param CategoryLabel \xe9\xbb\x98\xe8\xae\xa4\xe4\xb8\xba\xe7\xa9\xba\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\n\x09 * @param Callback  \xe5\x9b\x9e\xe8\xb0\x83\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief  \xe8\x8e\xb7\xe5\x8f\x96\xe4\xba\xa7\xe5\x93\x81\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\x97\xe8\xa1\xa8\n@param ServiceLabel \xe5\xa6\x82\xe6\x9e\x9c\xe5\x85\xb1\xe7\x94\xa8PS4\xe7\x9a\x84\xe5\x90\x8e\xe5\x8f\xb0\xe5\x95\x86\xe5\xba\x97\xe7\x9a\x84\xe8\xaf\x9d\xef\xbc\x8cPS5 \xe4\xb8\xba 1\xef\xbc\x8cPS4 \xe4\xb8\xba 0\n@param CategoryLabel \xe9\xbb\x98\xe8\xae\xa4\xe4\xb8\xba\xe7\xa9\xba\xe5\xad\x97\xe7\xac\xa6\xe4\xb8\xb2\n@param Callback  \xe5\x9b\x9e\xe8\xb0\x83" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "GetProductInfoListPS", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventGetProductInfoListPS_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics
	{
		struct OneEngineSDKPSSubsystem_eventHideStoreIcon_Parms
		{
			int32 ReturnValue;
		};
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventHideStoreIcon_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09 * @brief \xe9\x9a\x90\xe8\x97\x8f\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe9\x9a\x90\xe8\x97\x8f" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "HideStoreIcon", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventHideStoreIcon_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics
	{
		struct OneEngineSDKPSSubsystem_eventOpenCommerceDialogPremiumMode_Parms
		{
			FScriptDelegate OnOpenDialogResult;
		};
		static const UE4CodeGen_Private::FDelegatePropertyParams NewProp_OnOpenDialogResult;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FDelegatePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::NewProp_OnOpenDialogResult = { "OnOpenDialogResult", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventOpenCommerceDialogPremiumMode_Parms, OnOpenDialogResult), Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::NewProp_OnOpenDialogResult,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09 * @brief \xe6\x89\x93\xe5\xbc\x80\xe5\x95\x86\xe5\xba\x97\xe5\xaf\xb9\xe8\xaf\x9d\xe6\xa1\x86,\xe9\xbc\x93\xe5\x8a\xb1\xe7\x94\xa8\xe6\x88\xb7\xe6\x88\x90\xe4\xb8\xbaPlus\xe4\xbc\x9a\xe5\x91\x98,\xe4\xbb\x85\xe6\x94\xaf\xe6\x8c\x81PS5\xe5\xb9\xb3\xe5\x8f\xb0\n\x09 * @param OnOpenDialogResult \xe6\x89\x93\xe5\xbc\x80\xe5\x95\x86\xe5\xba\x97\xe5\xaf\xb9\xe8\xaf\x9d\xe6\xa1\x86\xe5\x9b\x9e\xe8\xb0\x83\xef\xbc\x8cResult \xe4\xb8\xba 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe5\xa4\xb1\xe8\xb4\xa5\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe6\x89\x93\xe5\xbc\x80\xe5\x95\x86\xe5\xba\x97\xe5\xaf\xb9\xe8\xaf\x9d\xe6\xa1\x86,\xe9\xbc\x93\xe5\x8a\xb1\xe7\x94\xa8\xe6\x88\xb7\xe6\x88\x90\xe4\xb8\xbaPlus\xe4\xbc\x9a\xe5\x91\x98,\xe4\xbb\x85\xe6\x94\xaf\xe6\x8c\x81PS5\xe5\xb9\xb3\xe5\x8f\xb0\n@param OnOpenDialogResult \xe6\x89\x93\xe5\xbc\x80\xe5\x95\x86\xe5\xba\x97\xe5\xaf\xb9\xe8\xaf\x9d\xe6\xa1\x86\xe5\x9b\x9e\xe8\xb0\x83\xef\xbc\x8cResult \xe4\xb8\xba 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe5\xa4\xb1\xe8\xb4\xa5" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "OpenCommerceDialogPremiumMode", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventOpenCommerceDialogPremiumMode_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics
	{
		struct OneEngineSDKPSSubsystem_eventSetFontPath_Parms
		{
			FString Path;
		};
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Path_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Path;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::NewProp_Path_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::NewProp_Path = { "Path", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventSetFontPath_Parms, Path), METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::NewProp_Path_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::NewProp_Path_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::NewProp_Path,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "SetFontPath", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventSetFontPath_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics
	{
		struct OneEngineSDKPSSubsystem_eventShowStoreIcon_Parms
		{
			EOnePSStoreIconPos Pos;
			int32 ReturnValue;
		};
		static const UE4CodeGen_Private::FBytePropertyParams NewProp_Pos_Underlying;
		static const UE4CodeGen_Private::FEnumPropertyParams NewProp_Pos;
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::NewProp_Pos_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UE4CodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, nullptr, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::NewProp_Pos = { "Pos", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventShowStoreIcon_Parms, Pos), Z_Construct_UEnum_OneEngineSDK_EOnePSStoreIconPos, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventShowStoreIcon_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::NewProp_Pos_Underlying,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::NewProp_Pos,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09 * @brief \xe6\x98\xbe\xe7\xa4\xba\xe5\x95\x86\xe5\xba\x97\xe5\x9b\xbe\xe6\xa0\x87\n\x09 * @param Pos \xe6\x98\xbe\xe7\xa4\xba\xe4\xbd\x8d\xe7\xbd\xae\n\x09 * @return 0 \xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe5\xa4\xb1\xe8\xb4\xa5\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe6\x98\xbe\xe7\xa4\xba\xe5\x95\x86\xe5\xba\x97\xe5\x9b\xbe\xe6\xa0\x87\n@param Pos \xe6\x98\xbe\xe7\xa4\xba\xe4\xbd\x8d\xe7\xbd\xae\n@return 0 \xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe5\xa4\xb1\xe8\xb4\xa5" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "ShowStoreIcon", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventShowStoreIcon_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics
	{
		struct OneEngineSDKPSSubsystem_eventStartNotifyPremiumFeature_Parms
		{
			int32 Interval;
			int32 Mark;
			int32 ReturnValue;
		};
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_Interval;
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_Mark;
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::NewProp_Interval = { "Interval", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventStartNotifyPremiumFeature_Parms, Interval), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::NewProp_Mark = { "Mark", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventStartNotifyPremiumFeature_Parms, Mark), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventStartNotifyPremiumFeature_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::NewProp_Interval,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::NewProp_Mark,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09 * @brief \xe5\xbc\x80\xe5\xa7\x8b\xe5\xae\x9a\xe6\x9c\x9f\xe8\xb0\x83\xe7\x94\xa8\xe9\x80\x9a\xe7\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe7\x89\xb9\xe6\x80\xa7\xef\xbc\x8c\xe5\x9c\xa8\xe8\xb0\x83\xe7\x94\xa8\xe5\x89\x8d\xe9\x9c\x80\xe8\xa6\x81\xe5\x85\x88\xe8\xb0\x83\xe7\x94\xa8 CheckPremium \xe6\xa3\x80\xe6\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe6\x9d\x83\xe9\x99\x90\xe3\x80\x82\n\x09 * \xe9\x9c\x80\xe8\xa6\x81\xe5\x9c\xa8\xe5\x8f\x91\xe7\x94\x9f\xe5\xae\x9e\xe9\x99\x85\xe5\xa4\x9a\xe4\xba\xba\xe6\xb8\xb8\xe7\x8e\xa9\xe7\x9a\x84\xe6\x97\xb6\xe5\x80\x99\xe8\xb0\x83\xe7\x94\xa8\xef\xbc\x8c\xe4\xb8\x8d\xe5\x8c\x85\xe6\x8b\xac\xe5\xa4\xa7\xe5\x8e\x85\xe7\xad\x89\xe4\xba\x8b\xe5\x89\x8d\xe5\x87\x86\xe5\xa4\x87\xe9\x98\xb6\xe6\xae\xb5\xe3\x80\x82\n\x09 * \xe5\x9b\xa0\xe6\xad\xa4\xef\xbc\x8c\xe4\xb8\x80\xe8\x88\xac\xe6\x98\xaf\xe5\x9c\xa8\xe8\xbf\x9b\xe5\x85\xa5\xe6\xb8\xb8\xe6\x88\x8f\xef\xbc\x8c\xe7\x8e\xa9\xe5\xae\xb6\xe5\x8f\xaf\xe4\xbb\xa5\xe6\x93\x8d\xe4\xbd\x9c\xe7\x9a\x84\xe5\x90\x8c\xe6\x97\xb6\xef\xbc\x8c\xe5\xbc\x80\xe5\xa7\x8b\xe8\xb0\x83\xe7\x94\xa8\xe3\x80\x82\n\x09 * @param Interval `\xe5\xae\x9a\xe6\x9c\x9f\xe8\xb0\x83\xe7\x94\xa8\xe7\x9a\x84\xe6\x97\xb6\xe9\x97\xb4\xe9\x97\xb4\xe9\x9a\x94,\xe5\x8d\x95\xe4\xbd\x8d\xe4\xb8\xba\xe7\xa7\x92\xef\xbc\x8c\xe9\xbb\x98\xe8\xae\xa4\xe4\xb8\xba 1 \xe7\xa7\x92\xef\xbc\x88\xe5\xbb\xba\xe8\xae\xae\xef\xbc\x89\n\x09 * @param Mark \xe4\xbc\x9a\xe5\x91\x98\xe7\x89\xb9\xe6\x80\xa7\xe6\xa0\x87\xe8\xae\xb0\xef\xbc\x8c 0 \xe8\xa1\xa8\xe7\xa4\xba\xe4\xb8\x8d\xe6\x8c\x87\xe5\xae\x9a\xe4\xbb\xbb\xe4\xbd\x95\xe7\x89\xb9\xe6\x80\xa7\xef\xbc\x8c 1 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xbb\xa1\xe8\xb6\xb3\xe8\xb7\xa8\xe5\xb9\xb3\xe5\x8f\xb0\xe6\x92\xad\xe6\x94\xbe\xe6\x9d\xa1\xe4\xbb\xb6\xef\xbc\x8c 2 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xbb\xa1\xe8\xb6\xb3\xe5\xbc\x95\xe6\x93\x8e\xe5\x86\x85\xe8\xa7\x82\xe7\x9c\x8b\xe6\x9d\xa1\xe4\xbb\xb6\xef\xbc\x8c 3 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xbb\xa1\xe8\xb6\xb3 1 \xe5\x92\x8c 2 \xe7\x9a\x84\xe6\x9d\xa1\xe4\xbb\xb6\n\x09 * @return 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe5\xa4\xb1\xe8\xb4\xa5\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe5\xbc\x80\xe5\xa7\x8b\xe5\xae\x9a\xe6\x9c\x9f\xe8\xb0\x83\xe7\x94\xa8\xe9\x80\x9a\xe7\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe7\x89\xb9\xe6\x80\xa7\xef\xbc\x8c\xe5\x9c\xa8\xe8\xb0\x83\xe7\x94\xa8\xe5\x89\x8d\xe9\x9c\x80\xe8\xa6\x81\xe5\x85\x88\xe8\xb0\x83\xe7\x94\xa8 CheckPremium \xe6\xa3\x80\xe6\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe6\x9d\x83\xe9\x99\x90\xe3\x80\x82\n\xe9\x9c\x80\xe8\xa6\x81\xe5\x9c\xa8\xe5\x8f\x91\xe7\x94\x9f\xe5\xae\x9e\xe9\x99\x85\xe5\xa4\x9a\xe4\xba\xba\xe6\xb8\xb8\xe7\x8e\xa9\xe7\x9a\x84\xe6\x97\xb6\xe5\x80\x99\xe8\xb0\x83\xe7\x94\xa8\xef\xbc\x8c\xe4\xb8\x8d\xe5\x8c\x85\xe6\x8b\xac\xe5\xa4\xa7\xe5\x8e\x85\xe7\xad\x89\xe4\xba\x8b\xe5\x89\x8d\xe5\x87\x86\xe5\xa4\x87\xe9\x98\xb6\xe6\xae\xb5\xe3\x80\x82\n\xe5\x9b\xa0\xe6\xad\xa4\xef\xbc\x8c\xe4\xb8\x80\xe8\x88\xac\xe6\x98\xaf\xe5\x9c\xa8\xe8\xbf\x9b\xe5\x85\xa5\xe6\xb8\xb8\xe6\x88\x8f\xef\xbc\x8c\xe7\x8e\xa9\xe5\xae\xb6\xe5\x8f\xaf\xe4\xbb\xa5\xe6\x93\x8d\xe4\xbd\x9c\xe7\x9a\x84\xe5\x90\x8c\xe6\x97\xb6\xef\xbc\x8c\xe5\xbc\x80\xe5\xa7\x8b\xe8\xb0\x83\xe7\x94\xa8\xe3\x80\x82\n@param Interval `\xe5\xae\x9a\xe6\x9c\x9f\xe8\xb0\x83\xe7\x94\xa8\xe7\x9a\x84\xe6\x97\xb6\xe9\x97\xb4\xe9\x97\xb4\xe9\x9a\x94,\xe5\x8d\x95\xe4\xbd\x8d\xe4\xb8\xba\xe7\xa7\x92\xef\xbc\x8c\xe9\xbb\x98\xe8\xae\xa4\xe4\xb8\xba 1 \xe7\xa7\x92\xef\xbc\x88\xe5\xbb\xba\xe8\xae\xae\xef\xbc\x89\n@param Mark \xe4\xbc\x9a\xe5\x91\x98\xe7\x89\xb9\xe6\x80\xa7\xe6\xa0\x87\xe8\xae\xb0\xef\xbc\x8c 0 \xe8\xa1\xa8\xe7\xa4\xba\xe4\xb8\x8d\xe6\x8c\x87\xe5\xae\x9a\xe4\xbb\xbb\xe4\xbd\x95\xe7\x89\xb9\xe6\x80\xa7\xef\xbc\x8c 1 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xbb\xa1\xe8\xb6\xb3\xe8\xb7\xa8\xe5\xb9\xb3\xe5\x8f\xb0\xe6\x92\xad\xe6\x94\xbe\xe6\x9d\xa1\xe4\xbb\xb6\xef\xbc\x8c 2 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xbb\xa1\xe8\xb6\xb3\xe5\xbc\x95\xe6\x93\x8e\xe5\x86\x85\xe8\xa7\x82\xe7\x9c\x8b\xe6\x9d\xa1\xe4\xbb\xb6\xef\xbc\x8c 3 \xe8\xa1\xa8\xe7\xa4\xba\xe6\xbb\xa1\xe8\xb6\xb3 1 \xe5\x92\x8c 2 \xe7\x9a\x84\xe6\x9d\xa1\xe4\xbb\xb6\n@return 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe5\xa4\xb1\xe8\xb4\xa5" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "StartNotifyPremiumFeature", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventStartNotifyPremiumFeature_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics
	{
		struct OneEngineSDKPSSubsystem_eventStopNotifyPremiumFeature_Parms
		{
			int32 ReturnValue;
		};
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(OneEngineSDKPSSubsystem_eventStopNotifyPremiumFeature_Parms, ReturnValue), METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::Function_MetaDataParams[] = {
		{ "Comment", "/**\n\x09 * @brief \xe7\x84\xb6\xe5\x90\x8e\xe5\x9c\xa8\xe7\x8e\xa9\xe5\xae\xb6\xe7\xbb\x93\xe6\x9d\x9f\xe6\x9c\xac\xe5\x9c\xba\xe6\xb8\xb8\xe6\x88\x8f\xe7\x9a\x84\xe6\x97\xb6\xe5\x80\x99\xef\xbc\x8c\xe5\x81\x9c\xe6\xad\xa2\xe8\xb0\x83\xe7\x94\xa8\xe9\x80\x9a\xe7\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe7\x89\xb9\xe6\x80\xa7.\xe6\xb3\xa8\xe6\x84\x8f\xe4\xb8\x8d\xe5\x8f\xaf\xe4\xbb\xa5\xe5\x9c\xa8\xe7\x94\xbb\xe9\x9d\xa2\xe8\xbf\x81\xe7\xa7\xbb\xe5\x9b\x9e\xe5\xa4\xa7\xe5\x8e\x85\xe6\x89\x8d\xe5\x81\x9c\xe6\xad\xa2\xe8\xb0\x83\xe7\x94\xa8\xe3\x80\x82\xe8\x80\x8c\xe6\x98\xaf\xe5\xba\x94\xe8\xaf\xa5\xe5\x9c\xa8\xe5\xaf\xb9\xe5\xb1\x80\xe7\xbb\x93\xe6\x9d\x9f\xe7\x9a\x84\xe5\x90\x8c\xe6\x97\xb6\xe5\x81\x9c\xe6\xad\xa2\xe8\xb0\x83\xe7\x94\xa8\n\x09 * @return 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe5\xa4\xb1\xe8\xb4\xa5\n\x09 */" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
		{ "ToolTip", "@brief \xe7\x84\xb6\xe5\x90\x8e\xe5\x9c\xa8\xe7\x8e\xa9\xe5\xae\xb6\xe7\xbb\x93\xe6\x9d\x9f\xe6\x9c\xac\xe5\x9c\xba\xe6\xb8\xb8\xe6\x88\x8f\xe7\x9a\x84\xe6\x97\xb6\xe5\x80\x99\xef\xbc\x8c\xe5\x81\x9c\xe6\xad\xa2\xe8\xb0\x83\xe7\x94\xa8\xe9\x80\x9a\xe7\x9f\xa5\xe4\xbc\x9a\xe5\x91\x98\xe7\x89\xb9\xe6\x80\xa7.\xe6\xb3\xa8\xe6\x84\x8f\xe4\xb8\x8d\xe5\x8f\xaf\xe4\xbb\xa5\xe5\x9c\xa8\xe7\x94\xbb\xe9\x9d\xa2\xe8\xbf\x81\xe7\xa7\xbb\xe5\x9b\x9e\xe5\xa4\xa7\xe5\x8e\x85\xe6\x89\x8d\xe5\x81\x9c\xe6\xad\xa2\xe8\xb0\x83\xe7\x94\xa8\xe3\x80\x82\xe8\x80\x8c\xe6\x98\xaf\xe5\xba\x94\xe8\xaf\xa5\xe5\x9c\xa8\xe5\xaf\xb9\xe5\xb1\x80\xe7\xbb\x93\xe6\x9d\x9f\xe7\x9a\x84\xe5\x90\x8c\xe6\x97\xb6\xe5\x81\x9c\xe6\xad\xa2\xe8\xb0\x83\xe7\x94\xa8\n@return 0 \xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe5\x85\xb6\xe4\xbb\x96\xe8\xa1\xa8\xe7\xa4\xba\xe6\x89\xa7\xe8\xa1\x8c\xe5\xa4\xb1\xe8\xb4\xa5" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOneEngineSDKPSSubsystem, nullptr, "StopNotifyPremiumFeature", nullptr, nullptr, sizeof(OneEngineSDKPSSubsystem_eventStopNotifyPremiumFeature_Parms), Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	UClass* Z_Construct_UClass_UOneEngineSDKPSSubsystem_NoRegister()
	{
		return UOneEngineSDKPSSubsystem::StaticClass();
	}
	struct Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics
	{
		static UObject* (*const DependentSingletons[])();
		static const FClassFunctionLinkInfo FuncInfo[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UGameInstanceSubsystem,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
	const FClassFunctionLinkInfo Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics::FuncInfo[] = {
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_CheckPremium, "CheckPremium" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanity, "FilterProfanity" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_FilterProfanitySync, "FilterProfanitySync" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountId, "GetAccountId" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetAccountState, "GetAccountState" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetBlockingUsers, "GetBlockingUsers" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCommunicationRestrictionStatus, "GetCommunicationRestrictionStatus" }, // ********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryCode, "GetCountryCode" }, // *********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetCountryRegion, "GetCountryRegion" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetFriends, "GetFriends" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetOnlineId, "GetOnlineId" }, // **********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_GetProductInfoListPS, "GetProductInfoListPS" }, // *********
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_HideStoreIcon, "HideStoreIcon" }, // **********
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature, "OnCheckPremiumResultDelegate__DelegateSignature" }, // 3544709156
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature, "OnFilterProfanityResultDelegate__DelegateSignature" }, // 3668360130
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature, "OnGetBlockingUsersResultDelegate__DelegateSignature" }, // 1658523087
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature, "OnGetFriendsResultDelegate__DelegateSignature" }, // 1112117064
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature, "OnGetProductInfoListPSDelegate__DelegateSignature" }, // 2146382909
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature, "OnOpenDialogResultDelegate__DelegateSignature" }, // 1306962008
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature, "OnRestrictionStatusResultDelegate__DelegateSignature" }, // 2832523242
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_OpenCommerceDialogPremiumMode, "OpenCommerceDialogPremiumMode" }, // 402770834
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_SetFontPath, "SetFontPath" }, // 1760575387
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_ShowStoreIcon, "ShowStoreIcon" }, // 713318949
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StartNotifyPremiumFeature, "StartNotifyPremiumFeature" }, // 1448314144
		{ &Z_Construct_UFunction_UOneEngineSDKPSSubsystem_StopNotifyPremiumFeature, "StopNotifyPremiumFeature" }, // 2595304964
		{ &Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature, "WidgetVisibilityDelegate__DelegateSignature" }, // 3446848394
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics::Class_MetaDataParams[] = {
		{ "IncludePath", "OneEngineSDKPSSubsystem.h" },
		{ "ModuleRelativePath", "Public/OneEngineSDKPSSubsystem.h" },
	};
#endif
	const FCppClassTypeInfoStatic Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UOneEngineSDKPSSubsystem>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics::ClassParams = {
		&UOneEngineSDKPSSubsystem::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		FuncInfo,
		nullptr,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		UE_ARRAY_COUNT(FuncInfo),
		0,
		0,
		0x001000A0u,
		METADATA_PARAMS(Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UOneEngineSDKPSSubsystem()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UOneEngineSDKPSSubsystem, 1625836310);
	template<> ONEENGINESDK_API UClass* StaticClass<UOneEngineSDKPSSubsystem>()
	{
		return UOneEngineSDKPSSubsystem::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UOneEngineSDKPSSubsystem(Z_Construct_UClass_UOneEngineSDKPSSubsystem, &UOneEngineSDKPSSubsystem::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UOneEngineSDKPSSubsystem"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UOneEngineSDKPSSubsystem);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
