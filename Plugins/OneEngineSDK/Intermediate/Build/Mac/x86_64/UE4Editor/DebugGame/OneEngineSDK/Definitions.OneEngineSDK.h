#undef ONEENGINESDK_API
#undef UE_IS_ENGINE_MODULE
#undef DEPRECATED_FORGAME
#define DEPRECATED_FORGAME DEPRECATED
#undef UE_DEPRECATED_FORGAME
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define IS_PROGRAM 0
#define UE_EDITOR 1
#define HAS_METAL 1
#define GL_SILENCE_DEPRECATION 1
#define ENABLE_PGO_PROFILE 0
#define USE_VORBIS_FOR_STREAMING 1
#define USE_XMA2_FOR_STREAMING 1
#define UE_RELATIVE_BASE_DIR "../../..//"
#define WITH_DEV_AUTOMATION_TESTS 1
#define WITH_PERF_AUTOMATION_TESTS 1
#define UNICODE 1
#define _UNICODE 1
#define __UNREAL__ 1
#define IS_MONOLITHIC 0
#define WITH_ENGINE 1
#define WITH_UNREAL_DEVELOPER_TOOLS 1
#define WITH_APPLICATION_CORE 1
#define WITH_COREUOBJECT 1
#define USE_STATS_WITHOUT_ENGINE 0
#define WITH_PLUGIN_SUPPORT 0
#define WITH_ACCESSIBILITY 1
#define WITH_PERFCOUNTERS 1
#define USE_LOGGING_IN_SHIPPING 0
#define WITH_LOGGING_TO_MEMORY 0
#define USE_CACHE_FREED_OS_ALLOCS 1
#define USE_CHECKS_IN_SHIPPING 0
#define USE_ESTIMATED_UTCNOW 0
#define WITH_EDITOR 1
#define WITH_SERVER_CODE 1
#define WITH_PUSH_MODEL 1
#define WITH_CEF3 1
#define WITH_LIVE_CODING 0
#define UBT_MODULE_MANIFEST "UE4Editor.modules"
#define UBT_MODULE_MANIFEST_DEBUGGAME "UE4Editor-Mac-DebugGame.modules"
#define UBT_COMPILED_PLATFORM Mac
#define UBT_COMPILED_TARGET Editor
#define UE_APP_NAME "UE4Editor"
#define PLATFORM_MAC 1
#define PLATFORM_APPLE 1
#define WITH_TTS 0
#define WITH_SPEECH_RECOGNITION 0
#define NDEBUG 1
#define UE_BUILD_DEVELOPMENT 1
#define UE_IS_ENGINE_MODULE 0
#define UE_PROJECT_NAME OneSDKDemo
#define UE_TARGET_NAME OneSDKDemoEditor
#define KONEENGINE_REGION_MAINLAND 1
#define ENGINE_SUPPORT_SONY 0
#define UE_MODULE_NAME "OneEngineSDK"
#define UE_PLUGIN_NAME "OneEngineSDK"
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define DEPRECATED_FORGAME DEPRECATED
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UNIQUENETID_ESPMODE ESPMode::Fast
#define COREUOBJECT_API DLLIMPORT
#define UE_ENABLE_ICU 1
#define WITH_DIRECTXMATH 0
#define WITH_MALLOC_STOMP 1
#define CORE_API DLLIMPORT
#define TRACELOG_API DLLIMPORT
#define INCLUDE_CHAOS 0
#define WITH_PHYSX 1
#define WITH_CHAOS 0
#define WITH_CHAOS_CLOTHING 0
#define WITH_CHAOS_NEEDS_TO_BE_FIXED 0
#define PHYSICS_INTERFACE_PHYSX 1
#define WITH_APEX 1
#define WITH_APEX_CLOTHING 1
#define WITH_CLOTH_COLLISION_DETECTION 1
#define WITH_PHYSX_COOKING 1
#define WITH_NVCLOTH 1
#define WITH_CUSTOM_SQ_STRUCTURE 0
#define WITH_IMMEDIATE_PHYSX 0
#define GPUPARTICLE_LOCAL_VF_ONLY 0
#define WITH_ODSC 0
#define ENGINE_API DLLIMPORT
#define NETCORE_API DLLIMPORT
#define APPLICATIONCORE_API DLLIMPORT
#define RHI_API DLLIMPORT
#define JSON_API DLLIMPORT
#define WITH_FREETYPE 1
#define SLATECORE_API DLLIMPORT
#define INPUTCORE_API DLLIMPORT
#define SLATE_API DLLIMPORT
#define WITH_UNREALPNG 1
#define WITH_UNREALJPEG 1
#define WITH_LIBJPEGTURBO 0
#define WITH_UNREALEXR 1
#define IMAGEWRAPPER_API DLLIMPORT
#define MESSAGING_API DLLIMPORT
#define MESSAGINGCOMMON_API DLLIMPORT
#define RENDERCORE_API DLLIMPORT
#define ANALYTICSET_API DLLIMPORT
#define ANALYTICS_API DLLIMPORT
#define SOCKETS_PACKAGE 1
#define SOCKETS_API DLLIMPORT
#define NETCOMMON_API DLLIMPORT
#define ASSETREGISTRY_API DLLIMPORT
#define ENGINEMESSAGES_API DLLIMPORT
#define ENGINESETTINGS_API DLLIMPORT
#define SYNTHBENCHMARK_API DLLIMPORT
#define RENDERER_API DLLIMPORT
#define GAMEPLAYTAGS_API DLLIMPORT
#define DEVELOPERSETTINGS_API DLLIMPORT
#define PACKETHANDLER_API DLLIMPORT
#define RELIABILITYHANDLERCOMPONENT_API DLLIMPORT
#define AUDIOPLATFORMCONFIGURATION_API DLLIMPORT
#define MESHDESCRIPTION_API DLLIMPORT
#define STATICMESHDESCRIPTION_API DLLIMPORT
#define PAKFILE_API DLLIMPORT
#define RSA_API DLLIMPORT
#define NETWORKREPLAYSTREAMING_API DLLIMPORT
#define INCLUDE_CHAOS 0
#define WITH_PHYSX 1
#define WITH_CHAOS 0
#define WITH_CHAOS_CLOTHING 0
#define WITH_CHAOS_NEEDS_TO_BE_FIXED 0
#define PHYSICS_INTERFACE_PHYSX 1
#define WITH_APEX 1
#define WITH_APEX_CLOTHING 1
#define WITH_CLOTH_COLLISION_DETECTION 1
#define WITH_PHYSX_COOKING 1
#define WITH_NVCLOTH 1
#define WITH_CUSTOM_SQ_STRUCTURE 0
#define WITH_IMMEDIATE_PHYSX 0
#define PHYSICSCORE_API DLLIMPORT
#define COMPILE_WITHOUT_UNREAL_SUPPORT 0
#define INCLUDE_CHAOS 0
#define CHAOS_MEMORY_TRACKING 0
#define CHAOS_API DLLIMPORT
#define COMPILE_WITHOUT_UNREAL_SUPPORT 0
#define INCLUDE_CHAOS 0
#define CHAOS_CHECKED 0
#define CHAOSCORE_API DLLIMPORT
#define INTEL_ISPC 1
#define VORONOI_API DLLIMPORT
#define WITH_PHYSX_RELEASE 0
#define UE_PHYSX_SUFFIX PROFILE
#define UE_APEX_SUFFIX PROFILE
#define APEX_UE4 1
#define APEX_STATICALLY_LINKED 0
#define WITH_APEX_LEGACY 1
#define SIGNALPROCESSING_API DLLIMPORT
#define AUDIOEXTENSIONS_API DLLIMPORT
#define AUDIOMIXERCORE_API DLLIMPORT
#define PROPERTYACCESS_API DLLIMPORT
#define INCLUDE_CHAOS 0
#define WITH_PHYSX 1
#define WITH_CHAOS 0
#define WITH_CHAOS_CLOTHING 0
#define WITH_CHAOS_NEEDS_TO_BE_FIXED 0
#define PHYSICS_INTERFACE_PHYSX 1
#define WITH_APEX 1
#define WITH_APEX_CLOTHING 1
#define WITH_CLOTH_COLLISION_DETECTION 1
#define WITH_PHYSX_COOKING 1
#define WITH_NVCLOTH 1
#define WITH_CUSTOM_SQ_STRUCTURE 0
#define WITH_IMMEDIATE_PHYSX 0
#define WITH_RECAST 1
#define WITH_NAVMESH_SEGMENT_LINKS 1
#define WITH_NAVMESH_CLUSTER_LINKS 1
#define UNREALED_API DLLIMPORT
#define DIRECTORYWATCHER_API DLLIMPORT
#define DOCUMENTATION_API DLLIMPORT
#define READ_TARGET_ENABLED_PLUGINS_FROM_RECEIPT 1
#define LOAD_PLUGINS_FOR_TARGET_PLATFORMS 1
#define PROJECTS_API DLLIMPORT
#define SANDBOXFILE_API DLLIMPORT
#define EDITORSTYLE_API DLLIMPORT
#define SOURCE_CONTROL_WITH_SLATE 1
#define SOURCECONTROL_API DLLIMPORT
#define UNREALEDMESSAGES_API DLLIMPORT
#define GAMEPLAYDEBUGGER_API DLLIMPORT
#define BLUEPRINTGRAPH_API DLLIMPORT
#define EDITORSUBSYSTEM_API DLLIMPORT
#define HTTP_PACKAGE 1
#define WITH_LIBCURL 0
#define WITH_WINHTTP 0
#define HTTP_API DLLIMPORT
#define UNREALAUDIO_API DLLIMPORT
#define FUNCTIONALTESTING_API DLLIMPORT
#define AUTOMATIONCONTROLLER_API DLLIMPORT
#define LOCALIZATION_API DLLIMPORT
#define WITH_SNDFILE_IO 0
#define AUDIOEDITOR_API DLLIMPORT
#define AUDIOMIXER_API DLLIMPORT
#define TARGETPLATFORM_API DLLIMPORT
#define LEVELEDITOR_API DLLIMPORT
#define SETTINGS_API DLLIMPORT
#define INTROTUTORIALS_API DLLIMPORT
#define HEADMOUNTEDDISPLAY_API DLLIMPORT
#define VREDITOR_API DLLIMPORT
#define COMMONMENUEXTENSIONS_API DLLIMPORT
#define INCLUDE_CHAOS 0
#define WITH_PHYSX 1
#define WITH_CHAOS 0
#define WITH_CHAOS_CLOTHING 0
#define WITH_CHAOS_NEEDS_TO_BE_FIXED 0
#define PHYSICS_INTERFACE_PHYSX 1
#define WITH_APEX 1
#define WITH_APEX_CLOTHING 1
#define WITH_CLOTH_COLLISION_DETECTION 1
#define WITH_PHYSX_COOKING 1
#define WITH_NVCLOTH 1
#define WITH_CUSTOM_SQ_STRUCTURE 0
#define WITH_IMMEDIATE_PHYSX 0
#define LANDSCAPE_API DLLIMPORT
#define PROPERTYEDITOR_API DLLIMPORT
#define ACTORPICKERMODE_API DLLIMPORT
#define SCENEDEPTHPICKERMODE_API DLLIMPORT
#define DETAILCUSTOMIZATIONS_API DLLIMPORT
#define CLASSVIEWER_API DLLIMPORT
#define GRAPHEDITOR_API DLLIMPORT
#define STRUCTVIEWER_API DLLIMPORT
#define CONTENTBROWSER_API DLLIMPORT
#define CONTENTBROWSERDATA_API DLLIMPORT
#define COLLECTIONMANAGER_API DLLIMPORT
#define ENABLE_HTTP_FOR_NFS 1
#define NETWORKFILESYSTEM_API DLLIMPORT
#define UMG_API DLLIMPORT
#define MOVIESCENE_API DLLIMPORT
#define TIMEMANAGEMENT_API DLLIMPORT
#define MOVIESCENETRACKS_API DLLIMPORT
#define ANIMATIONCORE_API DLLIMPORT
#define PROPERTYPATH_API DLLIMPORT
#define INCLUDE_CHAOS 0
#define WITH_PHYSX 1
#define WITH_CHAOS 0
#define WITH_CHAOS_CLOTHING 0
#define WITH_CHAOS_NEEDS_TO_BE_FIXED 0
#define PHYSICS_INTERFACE_PHYSX 1
#define WITH_APEX 1
#define WITH_APEX_CLOTHING 1
#define WITH_CLOTH_COLLISION_DETECTION 1
#define WITH_PHYSX_COOKING 1
#define WITH_NVCLOTH 1
#define WITH_CUSTOM_SQ_STRUCTURE 0
#define WITH_IMMEDIATE_PHYSX 0
#define WITH_RECAST 1
#define WITH_NAVMESH_SEGMENT_LINKS 1
#define WITH_NAVMESH_CLUSTER_LINKS 1
#define NAVIGATIONSYSTEM_API DLLIMPORT
#define MESHBUILDER_API DLLIMPORT
#define MATERIALSHADERQUALITYSETTINGS_API DLLIMPORT
#define INTERACTIVETOOLSFRAMEWORK_API DLLIMPORT
#define TOOLMENUSEDITOR_API DLLIMPORT
#define TOOLMENUS_API DLLIMPORT
#define ASSETTAGSEDITOR_API DLLIMPORT
#define ADDCONTENTDIALOG_API DLLIMPORT
#define USE_EMBREE 1
#define MESHUTILITIES_API DLLIMPORT
#define MESHMERGEUTILITIES_API DLLIMPORT
#define HIERARCHICALLODUTILITIES_API DLLIMPORT
#define MESHREDUCTIONINTERFACE_API DLLIMPORT
#define ASSETTOOLS_API DLLIMPORT
#define KISMETCOMPILER_API DLLIMPORT
#define GAMEPLAYTASKS_API DLLIMPORT
#define WITH_RECAST 1
#define WITH_GAMEPLAY_DEBUGGER 1
#define AIMODULE_API DLLIMPORT
#define KISMET_API DLLIMPORT
#define INCLUDE_CHAOS 0
#define WITH_PHYSX 1
#define WITH_CHAOS 0
#define WITH_CHAOS_CLOTHING 0
#define WITH_CHAOS_NEEDS_TO_BE_FIXED 0
#define PHYSICS_INTERFACE_PHYSX 1
#define WITH_APEX 1
#define WITH_APEX_CLOTHING 1
#define WITH_CLOTH_COLLISION_DETECTION 1
#define WITH_PHYSX_COOKING 1
#define WITH_NVCLOTH 1
#define WITH_CUSTOM_SQ_STRUCTURE 0
#define WITH_IMMEDIATE_PHYSX 0
#define CLOTHINGSYSTEMRUNTIMEINTERFACE_API DLLIMPORT
#define JSONUTILITIES_API DLLIMPORT
#define INSTALL_ONE_ENGINE_MAC_LIBRARY 1
#define KONEENGINE_REGION_MAINLAND 1
#define ENGINE_SUPPORT_SONY 0
#define ONEENGINESDK_API DLLEXPORT
