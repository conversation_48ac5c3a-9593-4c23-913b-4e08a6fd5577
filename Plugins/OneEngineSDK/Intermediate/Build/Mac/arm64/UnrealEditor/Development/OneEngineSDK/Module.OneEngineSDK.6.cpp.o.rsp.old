-c
-pipe
-I"."
-I"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Source/OneEngineSDK/Private"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/CoreUObject/UHT"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/CoreUObject/VerseVMBytecode"
-I"Runtime/CoreUObject/Public"
-I"Runtime/Core/Public"
-I"Runtime/TraceLog/Public"
-I"Runtime/AutoRTFM/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/ImageCore/UHT"
-I"Runtime/ImageCore/Public"
-I"Runtime/CorePreciseFP/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/Engine/UHT"
-I"Runtime/Engine/Classes"
-I"Runtime/Engine/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/CoreOnline/UHT"
-I"Runtime/CoreOnline/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/FieldNotification/UHT"
-I"Runtime/FieldNotification/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/NetCore/UHT"
-I"Runtime/Net/Core/Classes"
-I"Runtime/Net/Core/Public"
-I"Runtime/Net/Common/Public"
-I"Runtime/Json/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/JsonUtilities/UHT"
-I"Runtime/JsonUtilities/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/SlateCore/UHT"
-I"Runtime/SlateCore/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/DeveloperSettings/UHT"
-I"Runtime/DeveloperSettings/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/InputCore/UHT"
-I"Runtime/InputCore/Classes"
-I"Runtime/InputCore/Public"
-I"Runtime/ApplicationCore/Public"
-I"Runtime/RHI/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/Slate/UHT"
-I"Runtime/Slate/Public"
-I"Runtime/ImageWrapper/Public"
-I"Runtime/Messaging/Public"
-I"Runtime/MessagingCommon/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/RenderCore/UHT"
-I"Runtime/RenderCore/Public"
-I"Runtime/Analytics/AnalyticsET/Public"
-I"Runtime/Analytics/Analytics/Public"
-I"Runtime/Sockets/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AssetRegistry/UHT"
-I"Runtime/AssetRegistry/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/EngineMessages/UHT"
-I"Runtime/EngineMessages/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/EngineSettings/UHT"
-I"Runtime/EngineSettings/Classes"
-I"Runtime/EngineSettings/Public"
-I"Runtime/SynthBenchmark/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/GameplayTags/UHT"
-I"Runtime/GameplayTags/Classes"
-I"Runtime/GameplayTags/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/PacketHandler/UHT"
-I"Runtime/PacketHandlers/PacketHandler/Classes"
-I"Runtime/PacketHandlers/PacketHandler/Public"
-I"Runtime/PacketHandlers/ReliabilityHandlerComponent/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AudioPlatformConfiguration/UHT"
-I"Runtime/AudioPlatformConfiguration/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/MeshDescription/UHT"
-I"Runtime/MeshDescription/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/StaticMeshDescription/UHT"
-I"Runtime/StaticMeshDescription/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/SkeletalMeshDescription/UHT"
-I"Runtime/SkeletalMeshDescription/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AnimationCore/UHT"
-I"Runtime/AnimationCore/Public"
-I"Runtime/PakFile/Public"
-I"Runtime/RSA/Public"
-I"Runtime/NetworkReplayStreaming/NetworkReplayStreaming/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/PhysicsCore/UHT"
-I"Runtime/PhysicsCore/Public"
-I"Runtime/Experimental/ChaosCore/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/Chaos/UHT"
-I"Runtime/Experimental/Chaos/Public"
-I"Runtime/Experimental/Voronoi/Public"
-I"Runtime/GeometryCore/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/ChaosVDRuntime/UHT"
-I"Runtime/Experimental/ChaosVisualDebugger/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/NNE/UHT"
-I"Runtime/NNE/Public"
-I"Runtime/SignalProcessing/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/StateStream/UHT"
-I"Runtime/StateStream/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AudioExtensions/UHT"
-I"Runtime/AudioExtensions/Public"
-I"Runtime/AudioMixerCore/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AudioMixer/UHT"
-I"Runtime/AudioMixer/Classes"
-I"Runtime/AudioMixer/Public"
-I"Developer/TargetPlatform/Public"
-I"Developer/TextureFormat/Public"
-I"Developer/DesktopPlatform/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AudioLinkEngine/UHT"
-I"Runtime/AudioLink/AudioLinkEngine/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AudioLinkCore/UHT"
-I"Runtime/AudioLink/AudioLinkCore/Public"
-I"Runtime/Networking/Public"
-I"Runtime/Experimental/IoStore/OnDemandCore/Public"
-I"Developer/TextureBuildUtilities/Public"
-I"Developer/Horde/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/ClothSysRuntimeIntrfc/UHT"
-I"Runtime/ClothingSystemRuntimeInterface/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/IrisCore/UHT"
-I"Runtime/Experimental/Iris/Core/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/MovieSceneCapture/UHT"
-I"Runtime/MovieSceneCapture/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/Renderer/UHT"
-I"Runtime/Renderer/Public"
-I"../Shaders/Public"
-I"../Shaders/Shared"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/TypedElementFramework/UHT"
-I"Runtime/TypedElementFramework/Tests"
-I"Runtime/TypedElementFramework/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/TypedElementRuntime/UHT"
-I"Runtime/TypedElementRuntime/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AnimationDataController/UHT"
-I"Developer/AnimationDataController/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AnimationBlueprintEditor/UHT"
-I"Editor/AnimationBlueprintEditor/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/Kismet/UHT"
-I"Editor/Kismet/Classes"
-I"Editor/Kismet/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/Persona/UHT"
-I"Editor/Persona/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/SkeletonEditor/UHT"
-I"Editor/SkeletonEditor/Public"
-I"Developer/AnimationWidgets/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/ToolWidgets/UHT"
-I"Developer/ToolWidgets/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/ToolMenus/UHT"
-I"Developer/ToolMenus/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AnimationEditor/UHT"
-I"Editor/AnimationEditor/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AdvancedPreviewScene/UHT"
-I"Editor/AdvancedPreviewScene/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/PropertyEditor/UHT"
-I"Editor/PropertyEditor/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/EditorConfig/UHT"
-I"Editor/EditorConfig/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/EditorFramework/UHT"
-I"Editor/EditorFramework/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/EditorSubsystem/UHT"
-I"Editor/EditorSubsystem/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/InteractiveToolsFramework/UHT"
-I"Runtime/InteractiveToolsFramework/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/UnrealEd/UHT"
-I"Programs/UnrealLightmass/Public"
-I"Editor/UnrealEd/Classes"
-I"Editor/UnrealEd/Public"
-I"Editor/AssetTagsEditor/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/CollectionManager/UHT"
-I"Developer/CollectionManager/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/ContentBrowser/UHT"
-I"Editor/ContentBrowser/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AssetTools/UHT"
-I"Developer/AssetTools/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AssetDefinition/UHT"
-I"Editor/AssetDefinition/Public"
-I"Developer/Merge/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/ContentBrowserData/UHT"
-I"Editor/ContentBrowserData/Public"
-I"Runtime/Projects/Public"
-I"Developer/MeshUtilities/Public"
-I"Developer/MeshMergeUtilities/Public"
-I"Developer/MeshReductionInterface/Public"
-I"Runtime/RawMesh/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/MaterialUtilities/UHT"
-I"Developer/MaterialUtilities/Public"
-I"Editor/KismetCompiler/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/GameplayTasks/UHT"
-I"Runtime/GameplayTasks/Classes"
-I"Runtime/GameplayTasks/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/ClassViewer/UHT"
-I"Editor/ClassViewer/Public"
-I"Developer/DirectoryWatcher/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/Documentation/UHT"
-I"Editor/Documentation/Public"
-I"Editor/MainFrame/Public"
-I"Runtime/SandboxFile/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/SourceControl/UHT"
-I"Developer/SourceControl/Public"
-I"Developer/UncontrolledChangelists/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/UnrealEdMessages/UHT"
-I"Editor/UnrealEdMessages/Classes"
-I"Editor/UnrealEdMessages/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/BlueprintGraph/UHT"
-I"Editor/BlueprintGraph/Classes"
-I"Editor/BlueprintGraph/Public"
-I"Runtime/Online/HTTP/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/FunctionalTesting/UHT"
-I"Developer/FunctionalTesting/Classes"
-I"Developer/FunctionalTesting/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AutomationController/UHT"
-I"Developer/AutomationController/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AutomationTest/UHT"
-I"Runtime/AutomationTest/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/Localization/UHT"
-I"Developer/Localization/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AudioEditor/UHT"
-I"Editor/AudioEditor/Classes"
-I"Editor/AudioEditor/Public"
-I"ThirdParty/libSampleRate/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/LevelEditor/UHT"
-I"Editor/LevelEditor/Public"
-I"Editor/CommonMenuExtensions/Public"
-I"Developer/Settings/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/VREditor/UHT"
-I"Editor/VREditor/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/ViewportInteraction/UHT"
-I"Editor/ViewportInteraction/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/HeadMountedDisplay/UHT"
-I"Runtime/HeadMountedDisplay/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/Landscape/UHT"
-I"Runtime/Landscape/Classes"
-I"Runtime/Landscape/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/DetailCustomizations/UHT"
-I"Editor/DetailCustomizations/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/GraphEditor/UHT"
-I"Editor/GraphEditor/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/StructViewer/UHT"
-I"Editor/StructViewer/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/MaterialEditor/UHT"
-I"Editor/MaterialEditor/Public"
-I"Runtime/NetworkFileSystem/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/UMG/UHT"
-I"Runtime/UMG/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/MovieScene/UHT"
-I"Runtime/MovieScene/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/TimeManagement/UHT"
-I"Runtime/TimeManagement/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/UniversalObjectLocator/UHT"
-I"Runtime/UniversalObjectLocator/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/MovieSceneTracks/UHT"
-I"Runtime/MovieSceneTracks/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/Constraints/UHT"
-I"Runtime/Experimental/Animation/Constraints/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/PropertyPath/UHT"
-I"Runtime/PropertyPath/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/NavigationSystem/UHT"
-I"Runtime/NavigationSystem/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/GeometryCollectionEngine/UHT"
-I"Runtime/Experimental/GeometryCollectionEngine/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/ChaosSolverEngine/UHT"
-I"Runtime/Experimental/ChaosSolverEngine/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/DataflowCore/UHT"
-I"Runtime/Experimental/Dataflow/Core/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/DataflowEngine/UHT"
-I"Runtime/Experimental/Dataflow/Engine/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/DataflowSimulation/UHT"
-I"Runtime/Experimental/Dataflow/Simulation/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/FieldSystemEngine/UHT"
-I"Runtime/Experimental/FieldSystem/Source/FieldSystemEngine/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/ISMPool/UHT"
-I"Runtime/Experimental/ISMPool/Public"
-I"Developer/MeshBuilder/Public"
-I"Runtime/MeshUtilitiesCommon/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/MSQS/UHT"
-I"Runtime/MaterialShaderQualitySettings/Classes"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/ToolMenusEditor/UHT"
-I"Editor/ToolMenusEditor/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/StatusBar/UHT"
-I"Editor/StatusBar/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/InterchangeCore/UHT"
-I"Runtime/Interchange/Core/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/InterchangeEngine/UHT"
-I"Runtime/Interchange/Engine/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/DeveloperToolSettings/UHT"
-I"Developer/DeveloperToolSettings/Classes"
-I"Developer/DeveloperToolSettings/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/SubobjectDataInterface/UHT"
-I"Editor/SubobjectDataInterface/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/SubobjectEditor/UHT"
-I"Editor/SubobjectEditor/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/PhysicsUtilities/UHT"
-I"Developer/PhysicsUtilities/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/WidgetRegistration/UHT"
-I"Developer/WidgetRegistration/Public"
-I"Editor/ActorPickerMode/Public"
-I"Editor/SceneDepthPickerMode/Public"
-I"../Intermediate/Build/Mac/UnrealEditor/Inc/AnimationEditMode/UHT"
-I"Editor/AnimationEditMode/Public"
-I"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks/WPOneEngineBridge.framework/Headers"
-I"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks/WMXAppKit.framework/Headers"
-I"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/Mac/UnrealEditor/Inc/OneEngineSDK/UHT"
-I"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Source"
-I"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Source/OneEngineSDK/Public"
-I"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Intermediate/Build/Mac/arm64/OneSDKDemoEditor/Development/UnrealEd"
-isystem"ThirdParty/GuidelinesSupportLibrary/GSL-1144/include"
-isystem"ThirdParty/AtomicQueue"
-isystem"ThirdParty/RapidJSON/1.1.0"
-isystem"ThirdParty/LibTiff/Source/Mac"
-isystem"ThirdParty/LibTiff/Source"
-Wall
-Werror
-Wdelete-non-virtual-dtor
-Wenum-conversion
-Wbitfield-enum-conversion
-Wno-shadow
-Wno-undefined-var-template
-Wno-unused-but-set-variable
-Wno-unused-but-set-parameter
-Wno-unused-function
-Wno-unused-lambda-capture
-Wno-unused-local-typedef
-Wno-unused-private-field
-Wno-unused-variable
-Wno-unknown-pragmas
-Wno-tautological-compare
-Wno-switch
-Wno-invalid-offsetof
-Wno-inconsistent-missing-override
-Wno-gnu-string-literal-operator-template
-Wno-invalid-unevaluated-string
-Wno-dllexport-explicit-instantiation-decl
-Wno-deprecated-copy
-Wno-ordered-compare-function-pointers
-Wno-deprecated-volatile
-Wno-deprecated-anon-enum-enum-conversion
-Wno-ambiguous-reversed-operator
-Wno-enum-float-conversion
-Wno-enum-enum-conversion
-Wno-float-conversion
-Wno-implicit-float-conversion
-Wno-implicit-int-conversion
-Wno-c++11-narrowing
-Wno-bitwise-instead-of-logical
-Wdeprecated-declarations
-Wno-error=deprecated-declarations
-Wno-deprecated-copy-with-user-provided-copy
-fdiagnostics-absolute-paths
-fdiagnostics-color
-Wno-unknown-warning-option
-Wno-range-loop-analysis
-Wno-single-bit-bitfield-constant-conversion
-ffp-contract=off
-fno-delete-null-pointer-checks
-O3
-fsigned-char
-fexceptions
-DPLATFORM_EXCEPTIONS_DISABLED=0
-gdwarf-4
-fexceptions
-DPLATFORM_EXCEPTIONS_DISABLED=0
-fvisibility-ms-compat
-fvisibility-inlines-hidden
-fno-rtti
-fmessage-length=0
-fpascal-strings
-target arm64-apple-macos14.00
-fasm-blocks
-ffp-contract=off
-arch arm64
-isysroot "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk"
-include-pch "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Intermediate/Build/Mac/arm64/OneSDKDemoEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.ValApi.ValExpApi.Cpp20.InclOrderUnreal5_3.h.gch"
-include "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/Mac/arm64/UnrealEditor/Development/OneEngineSDK/Definitions.OneEngineSDK.h"
-x objective-c++
-std=c++20
-Xclang -fno-pch-timestamp
-fpch-validate-input-files-content
-stdlib=libc++
-fmodules
-fno-implicit-modules
-fimplicit-module-maps
-Wno-module-import-in-extern-c
"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/Mac/arm64/UnrealEditor/Development/OneEngineSDK/Module.OneEngineSDK.6.cpp"
-MD -MF"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/Mac/arm64/UnrealEditor/Development/OneEngineSDK/Module.OneEngineSDK.6.cpp.d"
-o "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/Mac/arm64/UnrealEditor/Development/OneEngineSDK/Module.OneEngineSDK.6.cpp.o"
 -Wno-unused-but-set-variable