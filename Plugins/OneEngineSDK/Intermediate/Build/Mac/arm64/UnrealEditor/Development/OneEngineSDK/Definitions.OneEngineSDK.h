// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for OneEngineSDK
#pragma once
#include "SharedDefinitions.UnrealEd.Project.ValApi.ValExpApi.Cpp20.InclOrderUnreal5_3.h"
#undef ONEENGINESDK_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_VALIDATE_EXPERIMENTAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 1
#define UE_PROJECT_NAME OneSDKDemo
#define UE_TARGET_NAME OneSDKDemoEditor
#define KONEENGINE_REGION_MAINLAND 1
#define ENGINE_SUPPORT_SONY 0
#define UE_MODULE_NAME "OneEngineSDK"
#define UE_PLUGIN_NAME "OneEngineSDK"
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define INSTALL_ONE_ENGINE_MAC_LIBRARY 1
#define ONEENGINESDK_API DLLEXPORT
