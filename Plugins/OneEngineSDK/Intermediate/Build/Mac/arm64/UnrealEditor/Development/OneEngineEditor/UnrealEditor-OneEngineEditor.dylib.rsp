-L/usr/lib/swift
-arch arm64
-isysroot "/Applications/Xcode-16.2.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk"
-target arm64-apple-macos14.00
-dead_strip
-dynamiclib
-ObjC
-rpath "/usr/lib/swift"
-L /Applications/Xcode-16.2.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift
-L /Applications/Xcode-16.2.0.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/../lib/swift/macosx
-headerpad_max_install_names
-current_version 4313.93.11
-compatibility_version 5.6.0
-rpath @loader_path/ -rpath @executable_path/
"/Applications/Xcode-16.2.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/Current/MultitouchSupport.tbd"
"../Binaries/Mac/UnrealEditor-CoreUObject.dylib"
-rpath "@loader_path/../../../../../../../../../Shared/Epic Games/UE_5.6/Engine/Binaries/Mac"
"../Binaries/Mac/UnrealEditor-Engine.dylib"
"../Binaries/Mac/UnrealEditor-InputCore.dylib"
"../Binaries/Mac/UnrealEditor-Slate.dylib"
"../Binaries/Mac/UnrealEditor-SlateCore.dylib"
"../Binaries/Mac/UnrealEditor-DesktopPlatform.dylib"
"../Binaries/Mac/UnrealEditor-UnrealEd.dylib"
"../Binaries/Mac/UnrealEditor-DeveloperToolSettings.dylib"
"../Binaries/Mac/UnrealEditor-Core.dylib"
"../Binaries/Mac/UnrealEditor-Json.dylib"
-framework "AVFoundation"
-framework "CoreVideo"
-framework "CoreMedia"
-framework "Cocoa"
-framework "Carbon"
-framework "IOKit"
-framework "Security"
-framework "UniformTypeIdentifiers"
-framework "Network"
-framework "ScreenCaptureKit"
-framework "SystemConfiguration"
-install_name "@rpath/UnrealEditor-OneEngineEditor.dylib"
"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/Mac/arm64/UnrealEditor/Development/OneEngineEditor/Module.OneEngineEditor.cpp.o"
-o "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Binaries/Mac/UnrealEditor-OneEngineEditor.dylib"
