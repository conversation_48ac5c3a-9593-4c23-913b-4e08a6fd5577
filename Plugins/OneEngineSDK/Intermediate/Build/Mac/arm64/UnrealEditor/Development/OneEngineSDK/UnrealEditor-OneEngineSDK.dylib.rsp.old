-L/usr/lib/swift
-arch arm64
-isysroot "/Applications/Xcode-16.2.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk"
-target arm64-apple-macos14.00
-dead_strip
-dynamiclib
-ObjC
-rpath "/usr/lib/swift"
-L /Applications/Xcode-16.2.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift
-L /Applications/Xcode-16.2.0.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/../lib/swift/macosx
-headerpad_max_install_names
-current_version 5.6.0
-compatibility_version 5.6.0
-rpath @loader_path/ -rpath @executable_path/
-l"c++"
-l"resolv"
-l"sqlite3"
-l"z"
"/Applications/Xcode-16.2.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/Current/MultitouchSupport.tbd"
"/Applications/Xcode-16.2.0.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/Current/MultitouchSupport.tbd"
"../Binaries/Mac/UnrealEditor-CoreUObject.dylib"
-rpath "@loader_path/../../../../../../../../../Shared/UE_5.6.1/Engine/Binaries/Mac"
"../Binaries/Mac/UnrealEditor-Engine.dylib"
"../Binaries/Mac/UnrealEditor-Slate.dylib"
"../Binaries/Mac/UnrealEditor-SlateCore.dylib"
"../Binaries/Mac/UnrealEditor-InputCore.dylib"
"../Binaries/Mac/UnrealEditor-ApplicationCore.dylib"
"../Binaries/Mac/UnrealEditor-Json.dylib"
"../Binaries/Mac/UnrealEditor-JsonUtilities.dylib"
"../Binaries/Mac/UnrealEditor-ImageWrapper.dylib"
"../Binaries/Mac/UnrealEditor-Projects.dylib"
"../Binaries/Mac/UnrealEditor-Core.dylib"
"../Binaries/Mac/UnrealEditor-UMG.dylib"
"../Binaries/Mac/UnrealEditor-RHI.dylib"
"../Binaries/Mac/UnrealEditor-RenderCore.dylib"
"../Binaries/Mac/UnrealEditor-UnrealEd.dylib"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WPOneEngineBridge"
-rpath "@loader_path/../../../MacNativeSDK/Frameworks"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WPOneEngineBridgeLaohu_MacOS"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WMOneLHIAP"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WMLaohuMacSDK"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WMAFNetworking"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WMDevice"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WMMacRoute"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WMNetworkDiagnose"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WPAnalysisSDK"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WMXAppKit"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WMCategories"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WMFatigueManageSDK"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WMMasonry"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WMRedeemCode"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WMSDWebImage"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WMUActCode"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WMURedeemCode"
-F "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/MacNativeSDK/Frameworks"
-framework "WPInfoSyncSDK"
-framework "Security"
-framework "Cocoa"
-framework "Foundation"
-framework "SystemConfiguration"
-framework "CoreTelephony"
-framework "CoreGraphics"
-framework "CoreText"
-framework "AdSupport"
-framework "StoreKit"
-framework "WebKit"
-framework "Accelerate"
-framework "CoreWLAN"
-framework "LocalAuthentication"
-framework "Metal"
-framework "AVFoundation"
-framework "CoreVideo"
-framework "CoreMedia"
-framework "GameController"
-framework "Carbon"
-framework "IOKit"
-framework "UniformTypeIdentifiers"
-framework "Network"
-framework "ScreenCaptureKit"
-weak_framework "CoreLocation"
-weak_framework "QuartzCore"
-weak_framework "AuthenticationServices"
-install_name "@rpath/UnrealEditor-OneEngineSDK.dylib"
"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/Mac/arm64/UnrealEditor/Development/OneEngineSDK/Module.OneEngineSDK.1.cpp.o"
"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/Mac/arm64/UnrealEditor/Development/OneEngineSDK/Module.OneEngineSDK.2.cpp.o"
"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/Mac/arm64/UnrealEditor/Development/OneEngineSDK/Module.OneEngineSDK.3.cpp.o"
"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/Mac/arm64/UnrealEditor/Development/OneEngineSDK/Module.OneEngineSDK.4.cpp.o"
"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/Mac/arm64/UnrealEditor/Development/OneEngineSDK/Module.OneEngineSDK.5.cpp.o"
"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/Mac/arm64/UnrealEditor/Development/OneEngineSDK/Module.OneEngineSDK.6.cpp.o"
-o "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Binaries/Mac/UnrealEditor-OneEngineSDK.dylib"
