// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for OneEngineSDK
#pragma once
#include "SharedDefinitions.Engine.Project.ValApi.ValExpApi.Cpp20.InclOrderUnreal5_3.h"
#undef ONEENGINESDK_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_VALIDATE_EXPERIMENTAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 1
#define UE_PROJECT_NAME OneSDKDemo
#define UE_TARGET_NAME OneSDKDemo
#define KONEENGINE_REGION_MAINLAND 1
#define ENGINE_SUPPORT_SONY 0
#define UE_MODULE_NAME "OneEngineSDK"
#define UE_PLUGIN_NAME "OneEngineSDK"
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define READ_TARGET_ENABLED_PLUGINS_FROM_RECEIPT 0
#define LOAD_PLUGINS_FOR_TARGET_PLATFORMS 0
#define PROJECTS_API 
#define INSTALL_ONE_ENGINE_MAC_LIBRARY 1
#define ONEENGINESDK_API 
#define UMG_API 
#define HTTP_PACKAGE 1
#define WITH_WINHTTP 0
#define UE_HTTP_CONNECTION_TIMEOUT_MAX_DEVIATION 1.5
#define UE_HTTP_ACTIVITY_TIMER_START_AFTER_RECEIVED_DATA 1
#define UE_HTTP_SUPPORT_LOCAL_SERVER 1
#define UE_HTTP_SUPPORT_UNIX_SOCKET 0
#define UE_HTTP_SUPPORT_VERB_CONNECT 1
#define HTTP_API 
#define MOVIESCENE_API 
#define TIMEMANAGEMENT_API 
#define UNIVERSALOBJECTLOCATOR_API 
#define MOVIESCENETRACKS_API 
#define CONSTRAINTS_API 
#define PROPERTYPATH_API 
