// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneUserAgreementPrompt.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneUserAgreementPrompt() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserAgreementPrompt();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserAgreementPrompt_NoRegister();
UMG_API UClass* Z_Construct_UClass_UBackgroundBlur_NoRegister();
UMG_API UClass* Z_Construct_UClass_UHorizontalBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_USizeBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
UMG_API UClass* Z_Construct_UClass_UWidgetSwitcher_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneUserAgreementPrompt ************************************************
void UPSOneUserAgreementPrompt::StaticRegisterNativesUPSOneUserAgreementPrompt()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneUserAgreementPrompt;
UClass* UPSOneUserAgreementPrompt::GetPrivateStaticClass()
{
	using TClass = UPSOneUserAgreementPrompt;
	if (!Z_Registration_Info_UClass_UPSOneUserAgreementPrompt.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneUserAgreementPrompt"),
			Z_Registration_Info_UClass_UPSOneUserAgreementPrompt.InnerSingleton,
			StaticRegisterNativesUPSOneUserAgreementPrompt,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneUserAgreementPrompt.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneUserAgreementPrompt_NoRegister()
{
	return UPSOneUserAgreementPrompt::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n *\n */" },
#endif
		{ "IncludePath", "Views/PSOneUserAgreementPrompt.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackgroundBlur_MetaData[] = {
		{ "bindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AgreeImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisagreeImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BgImageSizeBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BgImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TitleBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DescBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DescBlock_1_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Left1Image_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Right1Image_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriangleImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TitleBlock_2_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DescBlock_2_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BindAccountButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreateAccountButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoginWithPwrdButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Switcher_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChildProtectionBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsMainland_MetaData[] = {
		{ "Category", "PSOneUserAgreementPrompt" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartIndex_MetaData[] = {
		{ "Category", "PSOneUserAgreementPrompt" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoginOption_MetaData[] = {
		{ "Category", "PSOneUserAgreementPrompt" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowBgImage_MetaData[] = {
		{ "Category", "PSOneUserAgreementPrompt" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BackgroundBlur;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AgreeImage;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DisagreeImage;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BgImageSizeBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BgImage;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TitleBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DescBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DescBlock_1;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Left1Image;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Right1Image;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TriangleImage;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TitleBlock_2;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DescBlock_2;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BindAccountButton;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CreateAccountButton;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LoginWithPwrdButton;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Switcher;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChildProtectionBox;
	static void NewProp_bIsMainland_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMainland;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StartIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LoginOption;
	static void NewProp_bShowBgImage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowBgImage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserAgreementPrompt>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleBox_MetaData), NewProp_ScaleBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BackgroundBlur = { "BackgroundBlur", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, BackgroundBlur), Z_Construct_UClass_UBackgroundBlur_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackgroundBlur_MetaData), NewProp_BackgroundBlur_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_AgreeImage = { "AgreeImage", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, AgreeImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AgreeImage_MetaData), NewProp_AgreeImage_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DisagreeImage = { "DisagreeImage", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, DisagreeImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisagreeImage_MetaData), NewProp_DisagreeImage_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BgImageSizeBox = { "BgImageSizeBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, BgImageSizeBox), Z_Construct_UClass_USizeBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BgImageSizeBox_MetaData), NewProp_BgImageSizeBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BgImage = { "BgImage", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, BgImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BgImage_MetaData), NewProp_BgImage_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TitleBlock = { "TitleBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, TitleBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TitleBlock_MetaData), NewProp_TitleBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock = { "DescBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, DescBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DescBlock_MetaData), NewProp_DescBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_1 = { "DescBlock_1", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, DescBlock_1), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DescBlock_1_MetaData), NewProp_DescBlock_1_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Left1Image = { "Left1Image", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, Left1Image), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Left1Image_MetaData), NewProp_Left1Image_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Right1Image = { "Right1Image", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, Right1Image), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Right1Image_MetaData), NewProp_Right1Image_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TriangleImage = { "TriangleImage", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, TriangleImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriangleImage_MetaData), NewProp_TriangleImage_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TitleBlock_2 = { "TitleBlock_2", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, TitleBlock_2), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TitleBlock_2_MetaData), NewProp_TitleBlock_2_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_2 = { "DescBlock_2", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, DescBlock_2), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DescBlock_2_MetaData), NewProp_DescBlock_2_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BindAccountButton = { "BindAccountButton", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, BindAccountButton), Z_Construct_UClass_UPSOneButtonBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BindAccountButton_MetaData), NewProp_BindAccountButton_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_CreateAccountButton = { "CreateAccountButton", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, CreateAccountButton), Z_Construct_UClass_UPSOneButtonBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreateAccountButton_MetaData), NewProp_CreateAccountButton_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_LoginWithPwrdButton = { "LoginWithPwrdButton", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, LoginWithPwrdButton), Z_Construct_UClass_UPSOneButtonBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoginWithPwrdButton_MetaData), NewProp_LoginWithPwrdButton_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Switcher = { "Switcher", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, Switcher), Z_Construct_UClass_UWidgetSwitcher_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Switcher_MetaData), NewProp_Switcher_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_ChildProtectionBox = { "ChildProtectionBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, ChildProtectionBox), Z_Construct_UClass_UHorizontalBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChildProtectionBox_MetaData), NewProp_ChildProtectionBox_MetaData) };
void Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bIsMainland_SetBit(void* Obj)
{
	((UPSOneUserAgreementPrompt*)Obj)->bIsMainland = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bIsMainland = { "bIsMainland", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPSOneUserAgreementPrompt), &Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bIsMainland_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsMainland_MetaData), NewProp_bIsMainland_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_StartIndex = { "StartIndex", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, StartIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartIndex_MetaData), NewProp_StartIndex_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_LoginOption = { "LoginOption", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, LoginOption), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoginOption_MetaData), NewProp_LoginOption_MetaData) };
void Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bShowBgImage_SetBit(void* Obj)
{
	((UPSOneUserAgreementPrompt*)Obj)->bShowBgImage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bShowBgImage = { "bShowBgImage", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPSOneUserAgreementPrompt), &Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bShowBgImage_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowBgImage_MetaData), NewProp_bShowBgImage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_ScaleBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BackgroundBlur,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_AgreeImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DisagreeImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BgImageSizeBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BgImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TitleBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_1,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Left1Image,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Right1Image,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TriangleImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TitleBlock_2,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_2,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BindAccountButton,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_CreateAccountButton,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_LoginWithPwrdButton,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Switcher,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_ChildProtectionBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bIsMainland,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_StartIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_LoginOption,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bShowBgImage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::ClassParams = {
	&UPSOneUserAgreementPrompt::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneUserAgreementPrompt()
{
	if (!Z_Registration_Info_UClass_UPSOneUserAgreementPrompt.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneUserAgreementPrompt.OuterSingleton, Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneUserAgreementPrompt.OuterSingleton;
}
UPSOneUserAgreementPrompt::UPSOneUserAgreementPrompt(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserAgreementPrompt);
UPSOneUserAgreementPrompt::~UPSOneUserAgreementPrompt() {}
// ********** End Class UPSOneUserAgreementPrompt **************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserAgreementPrompt_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneUserAgreementPrompt, UPSOneUserAgreementPrompt::StaticClass, TEXT("UPSOneUserAgreementPrompt"), &Z_Registration_Info_UClass_UPSOneUserAgreementPrompt, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneUserAgreementPrompt), 3844423904U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserAgreementPrompt_h__Script_OneEngineSDK_2882612619(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserAgreementPrompt_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserAgreementPrompt_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
