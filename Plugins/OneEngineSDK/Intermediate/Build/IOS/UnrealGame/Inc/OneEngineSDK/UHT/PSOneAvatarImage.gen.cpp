// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneAvatarImage.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneAvatarImage() {}

// ********** Begin Cross Module References ********************************************************
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2DDynamic_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneAvatarImage();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneAvatarImage_NoRegister();
UMG_API UClass* Z_Construct_UClass_UAsyncTaskDownloadImage_NoRegister();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UMG_API UClass* Z_Construct_UClass_UUserWidget();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneAvatarImage Function HandleTextureDownloaded ***********************
struct Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics
{
	struct PSOneAvatarImage_eventHandleTextureDownloaded_Parms
	{
		UTexture2DDynamic* Texture;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneAvatarImage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Texture;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::NewProp_Texture = { "Texture", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneAvatarImage_eventHandleTextureDownloaded_Parms, Texture), Z_Construct_UClass_UTexture2DDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::NewProp_Texture,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneAvatarImage, nullptr, "HandleTextureDownloaded", Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::PSOneAvatarImage_eventHandleTextureDownloaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::PSOneAvatarImage_eventHandleTextureDownloaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneAvatarImage::execHandleTextureDownloaded)
{
	P_GET_OBJECT(UTexture2DDynamic,Z_Param_Texture);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HandleTextureDownloaded(Z_Param_Texture);
	P_NATIVE_END;
}
// ********** End Class UPSOneAvatarImage Function HandleTextureDownloaded *************************

// ********** Begin Class UPSOneAvatarImage Function SetImageURL ***********************************
struct Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics
{
	struct PSOneAvatarImage_eventSetImageURL_Parms
	{
		FString URL;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneAvatarImage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_URL_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_URL;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::NewProp_URL = { "URL", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneAvatarImage_eventSetImageURL_Parms, URL), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_URL_MetaData), NewProp_URL_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::NewProp_URL,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneAvatarImage, nullptr, "SetImageURL", Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::PSOneAvatarImage_eventSetImageURL_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::PSOneAvatarImage_eventSetImageURL_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneAvatarImage::execSetImageURL)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_URL);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetImageURL(Z_Param_URL);
	P_NATIVE_END;
}
// ********** End Class UPSOneAvatarImage Function SetImageURL *************************************

// ********** Begin Class UPSOneAvatarImage Function UpdateImage ***********************************
struct Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics
{
	struct PSOneAvatarImage_eventUpdateImage_Parms
	{
		UTexture2D* Texture;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneAvatarImage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Texture;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::NewProp_Texture = { "Texture", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneAvatarImage_eventUpdateImage_Parms, Texture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::NewProp_Texture,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneAvatarImage, nullptr, "UpdateImage", Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::PSOneAvatarImage_eventUpdateImage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::PSOneAvatarImage_eventUpdateImage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneAvatarImage::execUpdateImage)
{
	P_GET_OBJECT(UTexture2D,Z_Param_Texture);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateImage(Z_Param_Texture);
	P_NATIVE_END;
}
// ********** End Class UPSOneAvatarImage Function UpdateImage *************************************

// ********** Begin Class UPSOneAvatarImage ********************************************************
void UPSOneAvatarImage::StaticRegisterNativesUPSOneAvatarImage()
{
	UClass* Class = UPSOneAvatarImage::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "HandleTextureDownloaded", &UPSOneAvatarImage::execHandleTextureDownloaded },
		{ "SetImageURL", &UPSOneAvatarImage::execSetImageURL },
		{ "UpdateImage", &UPSOneAvatarImage::execUpdateImage },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneAvatarImage;
UClass* UPSOneAvatarImage::GetPrivateStaticClass()
{
	using TClass = UPSOneAvatarImage;
	if (!Z_Registration_Info_UClass_UPSOneAvatarImage.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneAvatarImage"),
			Z_Registration_Info_UClass_UPSOneAvatarImage.InnerSingleton,
			StaticRegisterNativesUPSOneAvatarImage,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneAvatarImage.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneAvatarImage_NoRegister()
{
	return UPSOneAvatarImage::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneAvatarImage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n *\n */" },
#endif
		{ "IncludePath", "Views/PSOneAvatarImage.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneAvatarImage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Image_MetaData[] = {
		{ "Category", "PSOneAvatarImage" },
		{ "ModuleRelativePath", "Private/Views/PSOneAvatarImage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImageView_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneAvatarImage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DownloadImageTask_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneAvatarImage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Image;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ImageView;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DownloadImageTask;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded, "HandleTextureDownloaded" }, // 1741034651
		{ &Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL, "SetImageURL" }, // 980540978
		{ &Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage, "UpdateImage" }, // 1222780857
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneAvatarImage>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_Image = { "Image", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneAvatarImage, Image), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Image_MetaData), NewProp_Image_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_ImageView = { "ImageView", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneAvatarImage, ImageView), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImageView_MetaData), NewProp_ImageView_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_DownloadImageTask = { "DownloadImageTask", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneAvatarImage, DownloadImageTask), Z_Construct_UClass_UAsyncTaskDownloadImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DownloadImageTask_MetaData), NewProp_DownloadImageTask_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneAvatarImage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_Image,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_ImageView,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_DownloadImageTask,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneAvatarImage_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneAvatarImage_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneAvatarImage_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneAvatarImage_Statics::ClassParams = {
	&UPSOneAvatarImage::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UPSOneAvatarImage_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneAvatarImage_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneAvatarImage_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneAvatarImage_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneAvatarImage()
{
	if (!Z_Registration_Info_UClass_UPSOneAvatarImage.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneAvatarImage.OuterSingleton, Z_Construct_UClass_UPSOneAvatarImage_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneAvatarImage.OuterSingleton;
}
UPSOneAvatarImage::UPSOneAvatarImage(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneAvatarImage);
UPSOneAvatarImage::~UPSOneAvatarImage() {}
// ********** End Class UPSOneAvatarImage **********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneAvatarImage_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneAvatarImage, UPSOneAvatarImage::StaticClass, TEXT("UPSOneAvatarImage"), &Z_Registration_Info_UClass_UPSOneAvatarImage, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneAvatarImage), 4119195793U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneAvatarImage_h__Script_OneEngineSDK_2552233496(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneAvatarImage_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneAvatarImage_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
