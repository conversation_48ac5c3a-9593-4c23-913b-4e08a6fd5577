// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSOneUIManager.h"

#ifdef ONEENGINESDK_PSOneUIManager_generated_h
#error "PSOneUIManager.generated.h already included, missing '#pragma once' in PSOneUIManager.h"
#endif
#define ONEENGINESDK_PSOneUIManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UPSOneUIManager;

// ********** Begin Class UPSOneUIManager **********************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execHideDelTips); \
	DECLARE_FUNCTION(execShowDelTips); \
	DECLARE_FUNCTION(execHideUserCenter); \
	DECLARE_FUNCTION(execShowUserCenter); \
	DECLARE_FUNCTION(execHideRealName); \
	DECLARE_FUNCTION(execShowRealName); \
	DECLARE_FUNCTION(execHideLogin); \
	DECLARE_FUNCTION(execShowLogin); \
	DECLARE_FUNCTION(execSwitchToPreparePage); \
	DECLARE_FUNCTION(execHideUserAgreement); \
	DECLARE_FUNCTION(execShowUserAgreement); \
	DECLARE_FUNCTION(execShowLoginSuccessHint); \
	DECLARE_FUNCTION(execShowToast); \
	DECLARE_FUNCTION(execHideLoading); \
	DECLARE_FUNCTION(execShowLoading); \
	DECLARE_FUNCTION(execGet);


ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUIManager_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneUIManager(); \
	friend struct Z_Construct_UClass_UPSOneUIManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUIManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSOneUIManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSOneUIManager_NoRegister) \
	DECLARE_SERIALIZER(UPSOneUIManager)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneUIManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSOneUIManager(UPSOneUIManager&&) = delete; \
	UPSOneUIManager(const UPSOneUIManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneUIManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneUIManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneUIManager) \
	NO_API virtual ~UPSOneUIManager();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_24_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h_27_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSOneUIManager;

// ********** End Class UPSOneUIManager ************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
