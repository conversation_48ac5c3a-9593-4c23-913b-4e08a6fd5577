// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneUIManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneUIManager() {}

// ********** Begin Cross Module References ********************************************************
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUIManager();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUIManager_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneUIManager Function Get *********************************************
struct Z_Construct_UFunction_UPSOneUIManager_Get_Statics
{
	struct PSOneUIManager_eventGet_Parms
	{
		UPSOneUIManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xe5\x8d\x95\xe4\xbe\x8b\xe5\xae\x9e\xe4\xbe\x8b\xe8\x8e\xb7\xe5\x8f\x96\xe5\x87\xbd\xe6\x95\xb0 */" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\x8d\x95\xe4\xbe\x8b\xe5\xae\x9e\xe4\xbe\x8b\xe8\x8e\xb7\xe5\x8f\x96\xe5\x87\xbd\xe6\x95\xb0" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPSOneUIManager_Get_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneUIManager_eventGet_Parms, ReturnValue), Z_Construct_UClass_UPSOneUIManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUIManager_Get_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_Get_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_Get_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_Get_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "Get", Z_Construct_UFunction_UPSOneUIManager_Get_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_Get_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneUIManager_Get_Statics::PSOneUIManager_eventGet_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_Get_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_Get_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneUIManager_Get_Statics::PSOneUIManager_eventGet_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneUIManager_Get()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_Get_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execGet)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPSOneUIManager**)Z_Param__Result=UPSOneUIManager::Get();
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function Get ***********************************************

// ********** Begin Class UPSOneUIManager Function HideDelTips *************************************
struct Z_Construct_UFunction_UPSOneUIManager_HideDelTips_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_HideDelTips_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "HideDelTips", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_HideDelTips_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_HideDelTips_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPSOneUIManager_HideDelTips()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_HideDelTips_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execHideDelTips)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HideDelTips();
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function HideDelTips ***************************************

// ********** Begin Class UPSOneUIManager Function HideLoading *************************************
struct Z_Construct_UFunction_UPSOneUIManager_HideLoading_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_HideLoading_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "HideLoading", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_HideLoading_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_HideLoading_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPSOneUIManager_HideLoading()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_HideLoading_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execHideLoading)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HideLoading();
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function HideLoading ***************************************

// ********** Begin Class UPSOneUIManager Function HideLogin ***************************************
struct Z_Construct_UFunction_UPSOneUIManager_HideLogin_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_HideLogin_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "HideLogin", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_HideLogin_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_HideLogin_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPSOneUIManager_HideLogin()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_HideLogin_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execHideLogin)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HideLogin();
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function HideLogin *****************************************

// ********** Begin Class UPSOneUIManager Function HideRealName ************************************
struct Z_Construct_UFunction_UPSOneUIManager_HideRealName_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_HideRealName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "HideRealName", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_HideRealName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_HideRealName_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPSOneUIManager_HideRealName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_HideRealName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execHideRealName)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HideRealName();
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function HideRealName **************************************

// ********** Begin Class UPSOneUIManager Function HideUserAgreement *******************************
struct Z_Construct_UFunction_UPSOneUIManager_HideUserAgreement_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_HideUserAgreement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "HideUserAgreement", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_HideUserAgreement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_HideUserAgreement_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPSOneUIManager_HideUserAgreement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_HideUserAgreement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execHideUserAgreement)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HideUserAgreement();
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function HideUserAgreement *********************************

// ********** Begin Class UPSOneUIManager Function HideUserCenter **********************************
struct Z_Construct_UFunction_UPSOneUIManager_HideUserCenter_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_HideUserCenter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "HideUserCenter", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_HideUserCenter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_HideUserCenter_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPSOneUIManager_HideUserCenter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_HideUserCenter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execHideUserCenter)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HideUserCenter();
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function HideUserCenter ************************************

// ********** Begin Class UPSOneUIManager Function ShowDelTips *************************************
struct Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics
{
	struct PSOneUIManager_eventShowDelTips_Parms
	{
		FString Tips;
		FString Uid;
		FString Token;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tips_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Uid_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Token_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Tips;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Uid;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Token;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Tips = { "Tips", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneUIManager_eventShowDelTips_Parms, Tips), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tips_MetaData), NewProp_Tips_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Uid = { "Uid", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneUIManager_eventShowDelTips_Parms, Uid), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Uid_MetaData), NewProp_Uid_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Token = { "Token", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneUIManager_eventShowDelTips_Parms, Token), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Token_MetaData), NewProp_Token_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Tips,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Uid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Token,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowDelTips", Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::PSOneUIManager_eventShowDelTips_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::PSOneUIManager_eventShowDelTips_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowDelTips()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execShowDelTips)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Tips);
	P_GET_PROPERTY(FStrProperty,Z_Param_Uid);
	P_GET_PROPERTY(FStrProperty,Z_Param_Token);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShowDelTips(Z_Param_Tips,Z_Param_Uid,Z_Param_Token);
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function ShowDelTips ***************************************

// ********** Begin Class UPSOneUIManager Function ShowLoading *************************************
struct Z_Construct_UFunction_UPSOneUIManager_ShowLoading_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** UI \xe6\x8e\xa7\xe4\xbb\xb6\xe7\xae\xa1\xe7\x90\x86 */" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UI \xe6\x8e\xa7\xe4\xbb\xb6\xe7\xae\xa1\xe7\x90\x86" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowLoading_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowLoading", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowLoading_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_ShowLoading_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowLoading()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowLoading_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execShowLoading)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShowLoading();
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function ShowLoading ***************************************

// ********** Begin Class UPSOneUIManager Function ShowLogin ***************************************
struct Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics
{
	struct PSOneUIManager_eventShowLogin_Parms
	{
		bool bIsMainland;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "CPP_Default_bIsMainland", "false" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bIsMainland_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMainland;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::NewProp_bIsMainland_SetBit(void* Obj)
{
	((PSOneUIManager_eventShowLogin_Parms*)Obj)->bIsMainland = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::NewProp_bIsMainland = { "bIsMainland", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PSOneUIManager_eventShowLogin_Parms), &Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::NewProp_bIsMainland_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::NewProp_bIsMainland,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowLogin", Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::PSOneUIManager_eventShowLogin_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::PSOneUIManager_eventShowLogin_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowLogin()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execShowLogin)
{
	P_GET_UBOOL(Z_Param_bIsMainland);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShowLogin(Z_Param_bIsMainland);
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function ShowLogin *****************************************

// ********** Begin Class UPSOneUIManager Function ShowLoginSuccessHint ****************************
struct Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics
{
	struct PSOneUIManager_eventShowLoginSuccessHint_Parms
	{
		FText Text;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Text_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FTextPropertyParams NewProp_Text;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FTextPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::NewProp_Text = { "Text", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneUIManager_eventShowLoginSuccessHint_Parms, Text), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Text_MetaData), NewProp_Text_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::NewProp_Text,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowLoginSuccessHint", Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::PSOneUIManager_eventShowLoginSuccessHint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::PSOneUIManager_eventShowLoginSuccessHint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execShowLoginSuccessHint)
{
	P_GET_PROPERTY_REF(FTextProperty,Z_Param_Out_Text);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShowLoginSuccessHint(Z_Param_Out_Text);
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function ShowLoginSuccessHint ******************************

// ********** Begin Class UPSOneUIManager Function ShowRealName ************************************
struct Z_Construct_UFunction_UPSOneUIManager_ShowRealName_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowRealName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowRealName", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowRealName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_ShowRealName_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowRealName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowRealName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execShowRealName)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShowRealName();
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function ShowRealName **************************************

// ********** Begin Class UPSOneUIManager Function ShowToast ***************************************
struct Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics
{
	struct PSOneUIManager_eventShowToast_Parms
	{
		FText Text;
		float Duration;
		bool HandleFocusWidget;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "CPP_Default_Duration", "3.000000" },
		{ "CPP_Default_HandleFocusWidget", "false" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Text_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FTextPropertyParams NewProp_Text;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static void NewProp_HandleFocusWidget_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_HandleFocusWidget;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FTextPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_Text = { "Text", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneUIManager_eventShowToast_Parms, Text), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Text_MetaData), NewProp_Text_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneUIManager_eventShowToast_Parms, Duration), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_HandleFocusWidget_SetBit(void* Obj)
{
	((PSOneUIManager_eventShowToast_Parms*)Obj)->HandleFocusWidget = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_HandleFocusWidget = { "HandleFocusWidget", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PSOneUIManager_eventShowToast_Parms), &Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_HandleFocusWidget_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_Text,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_HandleFocusWidget,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowToast", Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::PSOneUIManager_eventShowToast_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::PSOneUIManager_eventShowToast_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowToast()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execShowToast)
{
	P_GET_PROPERTY_REF(FTextProperty,Z_Param_Out_Text);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_UBOOL(Z_Param_HandleFocusWidget);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShowToast(Z_Param_Out_Text,Z_Param_Duration,Z_Param_HandleFocusWidget);
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function ShowToast *****************************************

// ********** Begin Class UPSOneUIManager Function ShowUserAgreement *******************************
struct Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics
{
	struct PSOneUIManager_eventShowUserAgreement_Parms
	{
		bool bHasAgreed;
		int32 LoginOption;
		FString Title;
		FString Content;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "CPP_Default_Content", "" },
		{ "CPP_Default_Title", "" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Content_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static void NewProp_bHasAgreed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasAgreed;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LoginOption;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Title;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Content;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_bHasAgreed_SetBit(void* Obj)
{
	((PSOneUIManager_eventShowUserAgreement_Parms*)Obj)->bHasAgreed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_bHasAgreed = { "bHasAgreed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PSOneUIManager_eventShowUserAgreement_Parms), &Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_bHasAgreed_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_LoginOption = { "LoginOption", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneUIManager_eventShowUserAgreement_Parms, LoginOption), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneUIManager_eventShowUserAgreement_Parms, Title), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Title_MetaData), NewProp_Title_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_Content = { "Content", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneUIManager_eventShowUserAgreement_Parms, Content), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Content_MetaData), NewProp_Content_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_bHasAgreed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_LoginOption,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_Title,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_Content,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowUserAgreement", Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::PSOneUIManager_eventShowUserAgreement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::PSOneUIManager_eventShowUserAgreement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execShowUserAgreement)
{
	P_GET_UBOOL(Z_Param_bHasAgreed);
	P_GET_PROPERTY(FIntProperty,Z_Param_LoginOption);
	P_GET_PROPERTY(FStrProperty,Z_Param_Title);
	P_GET_PROPERTY(FStrProperty,Z_Param_Content);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShowUserAgreement(Z_Param_bHasAgreed,Z_Param_LoginOption,Z_Param_Title,Z_Param_Content);
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function ShowUserAgreement *********************************

// ********** Begin Class UPSOneUIManager Function ShowUserCenter **********************************
struct Z_Construct_UFunction_UPSOneUIManager_ShowUserCenter_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowUserCenter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowUserCenter", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowUserCenter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_ShowUserCenter_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowUserCenter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowUserCenter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execShowUserCenter)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShowUserCenter();
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function ShowUserCenter ************************************

// ********** Begin Class UPSOneUIManager Function SwitchToPreparePage *****************************
struct Z_Construct_UFunction_UPSOneUIManager_SwitchToPreparePage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_SwitchToPreparePage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "SwitchToPreparePage", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_SwitchToPreparePage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneUIManager_SwitchToPreparePage_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPSOneUIManager_SwitchToPreparePage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_SwitchToPreparePage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneUIManager::execSwitchToPreparePage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SwitchToPreparePage();
	P_NATIVE_END;
}
// ********** End Class UPSOneUIManager Function SwitchToPreparePage *******************************

// ********** Begin Class UPSOneUIManager **********************************************************
void UPSOneUIManager::StaticRegisterNativesUPSOneUIManager()
{
	UClass* Class = UPSOneUIManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "Get", &UPSOneUIManager::execGet },
		{ "HideDelTips", &UPSOneUIManager::execHideDelTips },
		{ "HideLoading", &UPSOneUIManager::execHideLoading },
		{ "HideLogin", &UPSOneUIManager::execHideLogin },
		{ "HideRealName", &UPSOneUIManager::execHideRealName },
		{ "HideUserAgreement", &UPSOneUIManager::execHideUserAgreement },
		{ "HideUserCenter", &UPSOneUIManager::execHideUserCenter },
		{ "ShowDelTips", &UPSOneUIManager::execShowDelTips },
		{ "ShowLoading", &UPSOneUIManager::execShowLoading },
		{ "ShowLogin", &UPSOneUIManager::execShowLogin },
		{ "ShowLoginSuccessHint", &UPSOneUIManager::execShowLoginSuccessHint },
		{ "ShowRealName", &UPSOneUIManager::execShowRealName },
		{ "ShowToast", &UPSOneUIManager::execShowToast },
		{ "ShowUserAgreement", &UPSOneUIManager::execShowUserAgreement },
		{ "ShowUserCenter", &UPSOneUIManager::execShowUserCenter },
		{ "SwitchToPreparePage", &UPSOneUIManager::execSwitchToPreparePage },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneUIManager;
UClass* UPSOneUIManager::GetPrivateStaticClass()
{
	using TClass = UPSOneUIManager;
	if (!Z_Registration_Info_UClass_UPSOneUIManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneUIManager"),
			Z_Registration_Info_UClass_UPSOneUIManager.InnerSingleton,
			StaticRegisterNativesUPSOneUIManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneUIManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneUIManager_NoRegister()
{
	return UPSOneUIManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneUIManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * @class UPSOneUIManager\n * @brief \xe7\xae\xa1\xe7\x90\x86 PS One UI \xe7\x9b\xb8\xe5\x85\xb3\xe7\x9a\x84\xe5\x8a\x9f\xe8\x83\xbd\xef\xbc\x8c\xe5\x8c\x85\xe6\x8b\xac\xe5\x90\x84\xe7\xa7\x8d UI \xe7\x95\x8c\xe9\x9d\xa2\xe7\x9a\x84\xe6\x98\xbe\xe7\xa4\xba\xe5\x92\x8c\xe9\x9a\x90\xe8\x97\x8f\xef\xbc\x8c\xe4\xbb\xa5\xe5\x8f\x8a\xe7\x9b\xb8\xe5\x85\xb3\xe6\x93\x8d\xe4\xbd\x9c\xe7\x9a\x84\xe5\xa4\x84\xe7\x90\x86\n */" },
#endif
		{ "IncludePath", "Views/PSOneUIManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "@class UPSOneUIManager\n@brief \xe7\xae\xa1\xe7\x90\x86 PS One UI \xe7\x9b\xb8\xe5\x85\xb3\xe7\x9a\x84\xe5\x8a\x9f\xe8\x83\xbd\xef\xbc\x8c\xe5\x8c\x85\xe6\x8b\xac\xe5\x90\x84\xe7\xa7\x8d UI \xe7\x95\x8c\xe9\x9d\xa2\xe7\x9a\x84\xe6\x98\xbe\xe7\xa4\xba\xe5\x92\x8c\xe9\x9a\x90\xe8\x97\x8f\xef\xbc\x8c\xe4\xbb\xa5\xe5\x8f\x8a\xe7\x9b\xb8\xe5\x85\xb3\xe6\x93\x8d\xe4\xbd\x9c\xe7\x9a\x84\xe5\xa4\x84\xe7\x90\x86" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UPSOneUIManager_Get, "Get" }, // 3103370841
		{ &Z_Construct_UFunction_UPSOneUIManager_HideDelTips, "HideDelTips" }, // 1557345691
		{ &Z_Construct_UFunction_UPSOneUIManager_HideLoading, "HideLoading" }, // 2549916910
		{ &Z_Construct_UFunction_UPSOneUIManager_HideLogin, "HideLogin" }, // 1382010040
		{ &Z_Construct_UFunction_UPSOneUIManager_HideRealName, "HideRealName" }, // 223762088
		{ &Z_Construct_UFunction_UPSOneUIManager_HideUserAgreement, "HideUserAgreement" }, // 1062613107
		{ &Z_Construct_UFunction_UPSOneUIManager_HideUserCenter, "HideUserCenter" }, // 2887878710
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowDelTips, "ShowDelTips" }, // 1245776909
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowLoading, "ShowLoading" }, // 2613769005
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowLogin, "ShowLogin" }, // 1146726891
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint, "ShowLoginSuccessHint" }, // 2068063767
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowRealName, "ShowRealName" }, // 347146829
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowToast, "ShowToast" }, // 4065277928
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement, "ShowUserAgreement" }, // 1554321402
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowUserCenter, "ShowUserCenter" }, // 1387314792
		{ &Z_Construct_UFunction_UPSOneUIManager_SwitchToPreparePage, "SwitchToPreparePage" }, // 166755464
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUIManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UPSOneUIManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUIManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUIManager_Statics::ClassParams = {
	&UPSOneUIManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUIManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneUIManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneUIManager()
{
	if (!Z_Registration_Info_UClass_UPSOneUIManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneUIManager.OuterSingleton, Z_Construct_UClass_UPSOneUIManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneUIManager.OuterSingleton;
}
UPSOneUIManager::UPSOneUIManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUIManager);
UPSOneUIManager::~UPSOneUIManager() {}
// ********** End Class UPSOneUIManager ************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneUIManager, UPSOneUIManager::StaticClass, TEXT("UPSOneUIManager"), &Z_Registration_Info_UClass_UPSOneUIManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneUIManager), 2619061274U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h__Script_OneEngineSDK_472516588(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUIManager_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
