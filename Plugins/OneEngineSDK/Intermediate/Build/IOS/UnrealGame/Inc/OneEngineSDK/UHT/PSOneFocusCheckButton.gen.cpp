// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneFocusCheckButton.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneFocusCheckButton() {}

// ********** Begin Cross Module References ********************************************************
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusCheckButton();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusCheckButton_NoRegister();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneFocusCheckButton ***************************************************
void UPSOneFocusCheckButton::StaticRegisterNativesUPSOneFocusCheckButton()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneFocusCheckButton;
UClass* UPSOneFocusCheckButton::GetPrivateStaticClass()
{
	using TClass = UPSOneFocusCheckButton;
	if (!Z_Registration_Info_UClass_UPSOneFocusCheckButton.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneFocusCheckButton"),
			Z_Registration_Info_UClass_UPSOneFocusCheckButton.InnerSingleton,
			StaticRegisterNativesUPSOneFocusCheckButton,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneFocusCheckButton.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneFocusCheckButton_NoRegister()
{
	return UPSOneFocusCheckButton::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneFocusCheckButton_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n *\n */" },
#endif
		{ "IncludePath", "Views/PSOneFocusCheckButton.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneFocusCheckButton.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CheckedTexture_MetaData[] = {
		{ "Category", "PSOneFocusCheckButton" },
		{ "ModuleRelativePath", "Private/Views/PSOneFocusCheckButton.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UncheckedImage_MetaData[] = {
		{ "Category", "PSOneFocusCheckButton" },
		{ "ModuleRelativePath", "Private/Views/PSOneFocusCheckButton.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CheckBoxImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneFocusCheckButton.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CheckedTexture;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_UncheckedImage;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CheckBoxImage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneFocusCheckButton>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_CheckedTexture = { "CheckedTexture", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneFocusCheckButton, CheckedTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CheckedTexture_MetaData), NewProp_CheckedTexture_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_UncheckedImage = { "UncheckedImage", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneFocusCheckButton, UncheckedImage), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UncheckedImage_MetaData), NewProp_UncheckedImage_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_CheckBoxImage = { "CheckBoxImage", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneFocusCheckButton, CheckBoxImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CheckBoxImage_MetaData), NewProp_CheckBoxImage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneFocusCheckButton_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_CheckedTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_UncheckedImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_CheckBoxImage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneFocusCheckButton_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneFocusCheckButton_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneButtonBase,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneFocusCheckButton_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneFocusCheckButton_Statics::ClassParams = {
	&UPSOneFocusCheckButton::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneFocusCheckButton_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneFocusCheckButton_Statics::PropPointers),
	0,
	0x00A010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneFocusCheckButton_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneFocusCheckButton_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneFocusCheckButton()
{
	if (!Z_Registration_Info_UClass_UPSOneFocusCheckButton.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneFocusCheckButton.OuterSingleton, Z_Construct_UClass_UPSOneFocusCheckButton_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneFocusCheckButton.OuterSingleton;
}
UPSOneFocusCheckButton::UPSOneFocusCheckButton(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneFocusCheckButton);
UPSOneFocusCheckButton::~UPSOneFocusCheckButton() {}
// ********** End Class UPSOneFocusCheckButton *****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneFocusCheckButton, UPSOneFocusCheckButton::StaticClass, TEXT("UPSOneFocusCheckButton"), &Z_Registration_Info_UClass_UPSOneFocusCheckButton, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneFocusCheckButton), 3550810915U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h__Script_OneEngineSDK_1545951989(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusCheckButton_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
