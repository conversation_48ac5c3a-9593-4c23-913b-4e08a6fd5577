// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "OneEngineSDKSubsystem.h"

#ifdef ONEENGINESDK_OneEngineSDKSubsystem_generated_h
#error "OneEngineSDKSubsystem.generated.h already included, missing '#pragma once' in OneEngineSDKSubsystem.h"
#endif
#define ONEENGINESDK_OneEngineSDKSubsystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
enum class EOneAIHelpType : uint8;
enum class EOneEngineSDKRegionType : uint8;
enum class EOneEngineThirdType : uint8;
enum class EOneNaverGameType : uint8;
enum class EOnePermissionType : uint8;
enum class EOneResEventState : uint8;
enum class EOneScreenOrientation : uint8;
enum class EOneShareAppTarget : uint8;
enum class EOneShareType : uint8;
enum class EOneUnlockSafeLockResult : uint8;
enum class EOneUnlockSafeLockType : uint8;
struct FOneActiveQualificationInfo;
struct FOneAntiAddictionInfo;
struct FOneDeviceInfo;
struct FOnePaymentInfo;
struct FOnePermissionInfo;
struct FOneProductInfo;
struct FOnePushMessage;
struct FOnePushNotDisturbInfo;
struct FOnePushStatus;
struct FOnePushTypeInfo;
struct FOneRoleInfo;
struct FOneShareData;
struct FOneURCRoleInfo;
struct FOneUserInfo;
struct FOneUserLocationInfo;
struct FUserIpInfo;

// ********** Begin Delegate FOneGenericResultDelegate *********************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_24_DELEGATE \
static void FOneGenericResultDelegate_DelegateWrapper(const FScriptDelegate& OneGenericResultDelegate, bool bSuccess, int32 Code, const FString& Msg);


// ********** End Delegate FOneGenericResultDelegate ***********************************************

// ********** Begin Delegate FOneInitDelegate ******************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_27_DELEGATE \
static void FOneInitDelegate_DelegateWrapper(const FScriptDelegate& OneInitDelegate, bool bSuccess, int32 Code, const FString& Msg);


// ********** End Delegate FOneInitDelegate ********************************************************

// ********** Begin Delegate FOneLoginResultDelegate ***********************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_48_DELEGATE \
static void FOneLoginResultDelegate_DelegateWrapper(const FMulticastScriptDelegate& OneLoginResultDelegate, bool bSuccess, int32 Code, const FString& Msg, FOneUserInfo const& UserInfo);


// ********** End Delegate FOneLoginResultDelegate *************************************************

// ********** Begin Delegate FOneGetQRCodeScanResultDelegate ***************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_56_DELEGATE \
static void FOneGetQRCodeScanResultDelegate_DelegateWrapper(const FScriptDelegate& OneGetQRCodeScanResultDelegate, const FString& CodeType, const FString& CodeLink);


// ********** End Delegate FOneGetQRCodeScanResultDelegate *****************************************

// ********** Begin Delegate FOneFetchUserTokenListDelegate ****************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_65_DELEGATE \
static void FOneFetchUserTokenListDelegate_DelegateWrapper(const FScriptDelegate& OneFetchUserTokenListDelegate, bool bSuccess, int32 Code, const FString& Msg, TArray<FOneUserInfo> const& TokenList);


// ********** End Delegate FOneFetchUserTokenListDelegate ******************************************

// ********** Begin Delegate FOnePayResultDelegate *************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_87_DELEGATE \
static void FOnePayResultDelegate_DelegateWrapper(const FMulticastScriptDelegate& OnePayResultDelegate, bool bSuccess, int32 Code, const FString& Msg, const FString& OrderId);


// ********** End Delegate FOnePayResultDelegate ***************************************************

// ********** Begin Delegate FOneProductInfoDelegate ***********************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_95_DELEGATE \
static void FOneProductInfoDelegate_DelegateWrapper(const FScriptDelegate& OneProductInfoDelegate, bool bSucceed, int32 Code, TArray<FOneProductInfo> const& ProductListResult);


// ********** End Delegate FOneProductInfoDelegate *************************************************

// ********** Begin Delegate FOneGetPlatformDelegate ***********************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_108_DELEGATE \
static void FOneGetPlatformDelegate_DelegateWrapper(const FScriptDelegate& OneGetPlatformDelegate, bool bSuccess, int32 Code, const FString& Msg, const FString& Platform);


// ********** End Delegate FOneGetPlatformDelegate *************************************************

// ********** Begin Delegate FOneGetDeviceInfoDelegate *********************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_123_DELEGATE \
static void FOneGetDeviceInfoDelegate_DelegateWrapper(const FScriptDelegate& OneGetDeviceInfoDelegate, FOneDeviceInfo const& DeviceInfo);


// ********** End Delegate FOneGetDeviceInfoDelegate ***********************************************

// ********** Begin Delegate FOneFetchAntiAddictionInfoDelegate ************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_187_DELEGATE \
static void FOneFetchAntiAddictionInfoDelegate_DelegateWrapper(const FScriptDelegate& OneFetchAntiAddictionInfoDelegate, FOneAntiAddictionInfo const& Info);


// ********** End Delegate FOneFetchAntiAddictionInfoDelegate **************************************

// ********** Begin Delegate FOneAntiAddictionTimeoutDelegate **************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_192_DELEGATE \
static void FOneAntiAddictionTimeoutDelegate_DelegateWrapper(const FMulticastScriptDelegate& OneAntiAddictionTimeoutDelegate, bool bForceKick, FOneAntiAddictionInfo const& Info);


// ********** End Delegate FOneAntiAddictionTimeoutDelegate ****************************************

// ********** Begin Delegate FOneRequestPermissionResultDelegate ***********************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_222_DELEGATE \
static void FOneRequestPermissionResultDelegate_DelegateWrapper(const FScriptDelegate& OneRequestPermissionResultDelegate, EOnePermissionType Type, bool Granted);


// ********** End Delegate FOneRequestPermissionResultDelegate *************************************

// ********** Begin Delegate FOneLogoutResultDelegate **********************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_241_DELEGATE \
static void FOneLogoutResultDelegate_DelegateWrapper(const FMulticastScriptDelegate& OneLogoutResultDelegate, bool bSuccess, int32 Code, const FString& Msg);


// ********** End Delegate FOneLogoutResultDelegate ************************************************

// ********** Begin Delegate FOneUserLocationInfoDelegate ******************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_258_DELEGATE \
static void FOneUserLocationInfoDelegate_DelegateWrapper(const FScriptDelegate& OneUserLocationInfoDelegate, FOneUserLocationInfo const& LocationInfo);


// ********** End Delegate FOneUserLocationInfoDelegate ********************************************

// ********** Begin Delegate FOneFetchUserRoleInfoListDelegate *************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_265_DELEGATE \
static void FOneFetchUserRoleInfoListDelegate_DelegateWrapper(const FScriptDelegate& OneFetchUserRoleInfoListDelegate, bool bSuccess, int32 Code, const FString& Msg, TArray<FOneURCRoleInfo> const& RoleList);


// ********** End Delegate FOneFetchUserRoleInfoListDelegate ***************************************

// ********** Begin Delegate FOneQueryActCodeResultDelegate ****************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_281_DELEGATE \
static void FOneQueryActCodeResultDelegate_DelegateWrapper(const FScriptDelegate& OneQueryActCodeResultDelegate, bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg);


// ********** End Delegate FOneQueryActCodeResultDelegate ******************************************

// ********** Begin Delegate FOneQueryUserActiveQualificationResultDelegate ************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_295_DELEGATE \
static void FOneQueryUserActiveQualificationResultDelegate_DelegateWrapper(const FScriptDelegate& OneQueryUserActiveQualificationResultDelegate, bool bSucceed, int32 Code, const FString& ErrorMsg, FOneActiveQualificationInfo const& QualificationInfo);


// ********** End Delegate FOneQueryUserActiveQualificationResultDelegate **************************

// ********** Begin Delegate FOneActivateDeviceResultDelegate **************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_302_DELEGATE \
static void FOneActivateDeviceResultDelegate_DelegateWrapper(const FScriptDelegate& OneActivateDeviceResultDelegate, bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg);


// ********** End Delegate FOneActivateDeviceResultDelegate ****************************************

// ********** Begin Delegate FOneStartUpdatePushDataDelegate ***************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_319_DELEGATE \
static void FOneStartUpdatePushDataDelegate_DelegateWrapper(const FScriptDelegate& OneStartUpdatePushDataDelegate, bool bSucceed, int32 Code, const FString& ErrorMsg, const FString& DeviceToken);


// ********** End Delegate FOneStartUpdatePushDataDelegate *****************************************

// ********** Begin Delegate FOneNotificationDelegate **********************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_324_DELEGATE \
static void FOneNotificationDelegate_DelegateWrapper(const FScriptDelegate& OneNotificationDelegate, FOnePushMessage const& Message);


// ********** End Delegate FOneNotificationDelegate ************************************************

// ********** Begin Delegate FOnePushStatusDelegate ************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_330_DELEGATE \
static void FOnePushStatusDelegate_DelegateWrapper(const FScriptDelegate& OnePushStatusDelegate, FOnePushStatus PushStatus);


// ********** End Delegate FOnePushStatusDelegate **************************************************

// ********** Begin Delegate FOnGetPushStateDelegate ***********************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_339_DELEGATE \
static void FOnGetPushStateDelegate_DelegateWrapper(const FScriptDelegate& OnGetPushStateDelegate, bool bSucceed, int32 Code, const FString& ErrorMsg, bool bEnable);


// ********** End Delegate FOnGetPushStateDelegate *************************************************

// ********** Begin Delegate FOneGetPushTypeInfoListDelegate ***************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_358_DELEGATE \
static void FOneGetPushTypeInfoListDelegate_DelegateWrapper(const FScriptDelegate& OneGetPushTypeInfoListDelegate, bool bSucceed, int32 Code, const FString& ErrorMsg, TArray<FOnePushTypeInfo> const& PushTasks);


// ********** End Delegate FOneGetPushTypeInfoListDelegate *****************************************

// ********** Begin Delegate FOnePushNotDisturbInfoDelegate ****************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_368_DELEGATE \
static void FOnePushNotDisturbInfoDelegate_DelegateWrapper(const FScriptDelegate& OnePushNotDisturbInfoDelegate, bool bSucceed, int32 Code, const FString& ErrorMsg, FOnePushNotDisturbInfo const& DisturbInfo);


// ********** End Delegate FOnePushNotDisturbInfoDelegate ******************************************

// ********** Begin Delegate FOneBindResultDelegate ************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_409_DELEGATE \
static void FOneBindResultDelegate_DelegateWrapper(const FScriptDelegate& OneBindResultDelegate, bool bSuccess, int32 Code, const FString& Msg, EOneEngineThirdType BindType, FOneUserInfo const& UserInfo);


// ********** End Delegate FOneBindResultDelegate **************************************************

// ********** Begin Delegate FOneUserAuthenticationResultDelegate **********************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_415_DELEGATE \
static void FOneUserAuthenticationResultDelegate_DelegateWrapper(const FScriptDelegate& OneUserAuthenticationResultDelegate, bool bSucceed, int32 AuthResult, bool bHasNetError, const FString& ErrorMsg);


// ********** End Delegate FOneUserAuthenticationResultDelegate ************************************

// ********** Begin Delegate FOneTranslateResultDelegate *******************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_428_DELEGATE \
static void FOneTranslateResultDelegate_DelegateWrapper(const FScriptDelegate& OneTranslateResultDelegate, bool bSucceed, const FString& Result, const FString& ErrorMsg);


// ********** End Delegate FOneTranslateResultDelegate *********************************************

// ********** Begin Delegate FOneCommonFunctionDelegate ********************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_458_DELEGATE \
static void FOneCommonFunctionDelegate_DelegateWrapper(const FScriptDelegate& OneCommonFunctionDelegate, const FString& FunctionName, int32 Result, const FString& Msg);


// ********** End Delegate FOneCommonFunctionDelegate **********************************************

// ********** Begin Delegate FOnChannelNotHavingExitViewDelegate ***********************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_463_DELEGATE \
static void FOnChannelNotHavingExitViewDelegate_DelegateWrapper(const FScriptDelegate& OnChannelNotHavingExitViewDelegate);


// ********** End Delegate FOnChannelNotHavingExitViewDelegate *************************************

// ********** Begin Delegate FOnExitDelegate *******************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_467_DELEGATE \
static void FOnExitDelegate_DelegateWrapper(const FScriptDelegate& OnExitDelegate);


// ********** End Delegate FOnExitDelegate *********************************************************

// ********** Begin Delegate FOneOnGetClientPacket *************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_475_DELEGATE \
static void FOneOnGetClientPacket_DelegateWrapper(const FScriptDelegate& OneOnGetClientPacket, TArray<uint8> const& data);


// ********** End Delegate FOneOnGetClientPacket ***************************************************

// ********** Begin Delegate FOnGetIpInfoResultDelegate ********************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_490_DELEGATE \
static void FOnGetIpInfoResultDelegate_DelegateWrapper(const FScriptDelegate& OnGetIpInfoResultDelegate, bool bSuccess, FUserIpInfo IpInfo, int32 Code, const FString& Msg);


// ********** End Delegate FOnGetIpInfoResultDelegate **********************************************

// ********** Begin Delegate FOneUnlockSafeLockResultDelegate **************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_522_DELEGATE \
static void FOneUnlockSafeLockResultDelegate_DelegateWrapper(const FScriptDelegate& OneUnlockSafeLockResultDelegate, EOneUnlockSafeLockResult UnlockResult, const FString& UnlockToken, int32 Code, const FString& ErrorMsg, EOneUnlockSafeLockType UnlockType);


// ********** End Delegate FOneUnlockSafeLockResultDelegate ****************************************

// ********** Begin Delegate FOneLoadDelegate ******************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_534_DELEGATE \
static void FOneLoadDelegate_DelegateWrapper(const FScriptDelegate& OneLoadDelegate);


// ********** End Delegate FOneLoadDelegate ********************************************************

// ********** Begin Delegate FOneUnloadDelegate ****************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_535_DELEGATE \
static void FOneUnloadDelegate_DelegateWrapper(const FScriptDelegate& OneUnloadDelegate);


// ********** End Delegate FOneUnloadDelegate ******************************************************

// ********** Begin Delegate FOneInGameMenuDelegate ************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_536_DELEGATE \
static void FOneInGameMenuDelegate_DelegateWrapper(const FScriptDelegate& OneInGameMenuDelegate, const FString& InputString);


// ********** End Delegate FOneInGameMenuDelegate **************************************************

// ********** Begin Class UOneEngineSDKSubsystem ***************************************************
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execTerminateCommunity); \
	DECLARE_FUNCTION(execOpenCommunityByGame); \
	DECLARE_FUNCTION(execStopUnlockSafeLockUsingPushNotification); \
	DECLARE_FUNCTION(execUnlockSafeLockUsingDynamicCode); \
	DECLARE_FUNCTION(execUnlockSafeLockUsingPushNotification); \
	DECLARE_FUNCTION(execGetRenderConfigFilePath); \
	DECLARE_FUNCTION(execRequestStoreReview); \
	DECLARE_FUNCTION(execInAppRequestStoreReview); \
	DECLARE_FUNCTION(execSwitchScreenPermanentBrightnessState); \
	DECLARE_FUNCTION(execRecoverScreenBrightness); \
	DECLARE_FUNCTION(execSetScreenBrightness); \
	DECLARE_FUNCTION(execGetScreenBrightness); \
	DECLARE_FUNCTION(execGetIpInfo); \
	DECLARE_FUNCTION(execACELogout); \
	DECLARE_FUNCTION(execACEClientPacketReceive); \
	DECLARE_FUNCTION(execACELogin); \
	DECLARE_FUNCTION(execKillProcess); \
	DECLARE_FUNCTION(execCallCommonFunction); \
	DECLARE_FUNCTION(execIsCommonFunctionSupported); \
	DECLARE_FUNCTION(execShouldVerifyBundleId); \
	DECLARE_FUNCTION(execSetScreenOrientation); \
	DECLARE_FUNCTION(execGetSupportedLanguageCodeList); \
	DECLARE_FUNCTION(execSetLanguage); \
	DECLARE_FUNCTION(execGetCurrentLanguage); \
	DECLARE_FUNCTION(execTranslate); \
	DECLARE_FUNCTION(execOpenCustomerService); \
	DECLARE_FUNCTION(execOpenAIHelp); \
	DECLARE_FUNCTION(execUserAuthentication); \
	DECLARE_FUNCTION(execBind); \
	DECLARE_FUNCTION(execGetRegionType); \
	DECLARE_FUNCTION(execIsDebugMode); \
	DECLARE_FUNCTION(execEnableDebugMode); \
	DECLARE_FUNCTION(execIsLoggedIn); \
	DECLARE_FUNCTION(execIsInstalledApp); \
	DECLARE_FUNCTION(execShareDataToApp); \
	DECLARE_FUNCTION(execUpdatePushNotDisturb); \
	DECLARE_FUNCTION(execGetPushNotDisturb); \
	DECLARE_FUNCTION(execUpdatePushTypeList); \
	DECLARE_FUNCTION(execGetPushTypeInfoList); \
	DECLARE_FUNCTION(execUnSetPushUserInfo); \
	DECLARE_FUNCTION(execSetPushUserInfo); \
	DECLARE_FUNCTION(execSetAnalyticsCollectionEnabled); \
	DECLARE_FUNCTION(execGetProviderPushState); \
	DECLARE_FUNCTION(execSetProviderPushState); \
	DECLARE_FUNCTION(execGetPushStatus); \
	DECLARE_FUNCTION(execSetupNotificationCallback); \
	DECLARE_FUNCTION(execStartUpdatePushData); \
	DECLARE_FUNCTION(execRedeemCouponCode); \
	DECLARE_FUNCTION(execActivateDevice); \
	DECLARE_FUNCTION(execQueryUserActiveQualification); \
	DECLARE_FUNCTION(execExchangeActCode); \
	DECLARE_FUNCTION(execQueryActCode); \
	DECLARE_FUNCTION(execDisplayCDKeyDialog); \
	DECLARE_FUNCTION(execSetShowDefaultActivationResultToast); \
	DECLARE_FUNCTION(execFetchUserRoleInfoList); \
	DECLARE_FUNCTION(execGetUserLocationInfo); \
	DECLARE_FUNCTION(execOpenUserCenter); \
	DECLARE_FUNCTION(execGetUserInfo); \
	DECLARE_FUNCTION(execSwitchAccount); \
	DECLARE_FUNCTION(execOpenApplicationSetting); \
	DECLARE_FUNCTION(execCloseClipboardPermission); \
	DECLARE_FUNCTION(execRequestPermission); \
	DECLARE_FUNCTION(execCheckSelfPermission); \
	DECLARE_FUNCTION(execGetPermissions); \
	DECLARE_FUNCTION(execEnterAccountCancellation); \
	DECLARE_FUNCTION(execOpenComplianceOnWebView); \
	DECLARE_FUNCTION(execStopAntiAddictionNotify); \
	DECLARE_FUNCTION(execStartAntiAddictionNotify); \
	DECLARE_FUNCTION(execFetchAntiAddictionInfo); \
	DECLARE_FUNCTION(execTrackEventAddExtraDeviceInfo); \
	DECLARE_FUNCTION(execTrackEventExitGameScene); \
	DECLARE_FUNCTION(execTrackEventEnterGameScene); \
	DECLARE_FUNCTION(execTrackEventAD); \
	DECLARE_FUNCTION(execTrackEvent); \
	DECLARE_FUNCTION(execGameGetServerListEvent); \
	DECLARE_FUNCTION(execGameResDecEvent); \
	DECLARE_FUNCTION(execGameUpdateAssetEvent); \
	DECLARE_FUNCTION(execGameResReqEvent); \
	DECLARE_FUNCTION(execTrackEventRoleLevelUp); \
	DECLARE_FUNCTION(execTrackEventRoleCreate); \
	DECLARE_FUNCTION(execTrackEventRoleLogout); \
	DECLARE_FUNCTION(execTrackEventRoleLoginError); \
	DECLARE_FUNCTION(execTrackEventRoleLoginSucceed); \
	DECLARE_FUNCTION(execGetDeviceInfo); \
	DECLARE_FUNCTION(execGetChannelMediaId); \
	DECLARE_FUNCTION(execGetChannelId); \
	DECLARE_FUNCTION(execGetChannelPlatform); \
	DECLARE_FUNCTION(execGetPlatformOS); \
	DECLARE_FUNCTION(execGetProductList); \
	DECLARE_FUNCTION(execPay); \
	DECLARE_FUNCTION(execGuestLogin); \
	DECLARE_FUNCTION(execThirdLogin); \
	DECLARE_FUNCTION(execTokenLogin); \
	DECLARE_FUNCTION(execGetUserTokenList); \
	DECLARE_FUNCTION(execGetQRCodeScanResult); \
	DECLARE_FUNCTION(execLogin); \
	DECLARE_FUNCTION(execGetAppId); \
	DECLARE_FUNCTION(execSetUpConfigAppID); \
	DECLARE_FUNCTION(execExaminStatus); \
	DECLARE_FUNCTION(execInit);


ONEENGINESDK_API UClass* Z_Construct_UClass_UOneEngineSDKSubsystem_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUOneEngineSDKSubsystem(); \
	friend struct Z_Construct_UClass_UOneEngineSDKSubsystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UOneEngineSDKSubsystem_NoRegister(); \
public: \
	DECLARE_CLASS2(UOneEngineSDKSubsystem, UEngineSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UOneEngineSDKSubsystem_NoRegister) \
	DECLARE_SERIALIZER(UOneEngineSDKSubsystem)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UOneEngineSDKSubsystem(); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UOneEngineSDKSubsystem(UOneEngineSDKSubsystem&&) = delete; \
	UOneEngineSDKSubsystem(const UOneEngineSDKSubsystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UOneEngineSDKSubsystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UOneEngineSDKSubsystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UOneEngineSDKSubsystem) \
	NO_API virtual ~UOneEngineSDKSubsystem();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_13_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UOneEngineSDKSubsystem;

// ********** End Class UOneEngineSDKSubsystem *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
