// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneInputPhoneNum.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneInputPhoneNum() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneConfirmButton_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneInputPhoneNum();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneInputPhoneNum_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldBase_NoRegister();
ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPSOneInputPhoneNumType ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPSOneInputPhoneNumType;
static UEnum* EPSOneInputPhoneNumType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPSOneInputPhoneNumType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPSOneInputPhoneNumType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType, (UObject*)Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EPSOneInputPhoneNumType"));
	}
	return Z_Registration_Info_UEnum_EPSOneInputPhoneNumType.OuterSingleton;
}
template<> ONEENGINESDK_API UEnum* StaticEnum<EPSOneInputPhoneNumType>()
{
	return EPSOneInputPhoneNumType_StaticEnum();
}
struct Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\x9e\x9a\xe4\xb8\xbe\n" },
#endif
		{ "Email.Name", "EPSOneInputPhoneNumType::Email" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
		{ "PhoneNum.Name", "EPSOneInputPhoneNumType::PhoneNum" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\x9e\x9a\xe4\xb8\xbe" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPSOneInputPhoneNumType::PhoneNum", (int64)EPSOneInputPhoneNumType::PhoneNum },
		{ "EPSOneInputPhoneNumType::Email", (int64)EPSOneInputPhoneNumType::Email },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
	nullptr,
	"EPSOneInputPhoneNumType",
	"EPSOneInputPhoneNumType",
	Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType()
{
	if (!Z_Registration_Info_UEnum_EPSOneInputPhoneNumType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPSOneInputPhoneNumType.InnerSingleton, Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPSOneInputPhoneNumType.InnerSingleton;
}
// ********** End Enum EPSOneInputPhoneNumType *****************************************************

// ********** Begin Class UPSOneInputPhoneNum ******************************************************
void UPSOneInputPhoneNum::StaticRegisterNativesUPSOneInputPhoneNum()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneInputPhoneNum;
UClass* UPSOneInputPhoneNum::GetPrivateStaticClass()
{
	using TClass = UPSOneInputPhoneNum;
	if (!Z_Registration_Info_UClass_UPSOneInputPhoneNum.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneInputPhoneNum"),
			Z_Registration_Info_UClass_UPSOneInputPhoneNum.InnerSingleton,
			StaticRegisterNativesUPSOneInputPhoneNum,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneInputPhoneNum.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneInputPhoneNum_NoRegister()
{
	return UPSOneInputPhoneNum::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneInputPhoneNum_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * \xe7\x94\xa8\xe4\xba\x8e\xe7\xbb\x91\xe5\xae\x9a\xe6\x89\x8b\xe6\x9c\xba\xe5\x8f\xb7\xe6\x88\x96\xe9\x82\xae\xe7\xae\xb1\xe7\x9a\x84\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86""0\n */" },
#endif
		{ "IncludePath", "Views/PSOneInputPhoneNum.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x94\xa8\xe4\xba\x8e\xe7\xbb\x91\xe5\xae\x9a\xe6\x89\x8b\xe6\x9c\xba\xe5\x8f\xb7\xe6\x88\x96\xe9\x82\xae\xe7\xae\xb1\xe7\x9a\x84\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86""0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TitleTextBlock_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \xe6\xa0\x87\xe9\xa2\x98\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe6\xa0\x87\xe9\xa2\x98" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AreaCodeButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhoneNumTextField_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// enter icon\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "enter icon" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// backspace icon\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "backspace icon" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputType_MetaData[] = {
		{ "Category", "PSOneInputPhoneNum" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TitleTextBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AreaCodeButton;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PhoneNumTextField;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActionButton;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InputType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InputType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneInputPhoneNum>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleBox_MetaData), NewProp_ScaleBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_TitleTextBlock = { "TitleTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, TitleTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TitleTextBlock_MetaData), NewProp_TitleTextBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_AreaCodeButton = { "AreaCodeButton", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, AreaCodeButton), Z_Construct_UClass_UPSOneButtonBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AreaCodeButton_MetaData), NewProp_AreaCodeButton_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_PhoneNumTextField = { "PhoneNumTextField", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, PhoneNumTextField), Z_Construct_UClass_UPSOneTextFieldBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhoneNumTextField_MetaData), NewProp_PhoneNumTextField_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_ActionButton = { "ActionButton", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, ActionButton), Z_Construct_UClass_UPSOneConfirmButton_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionButton_MetaData), NewProp_ActionButton_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnterIcon_MetaData), NewProp_EnterIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackspaceIcon_MetaData), NewProp_BackspaceIcon_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_InputType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_InputType = { "InputType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, InputType), Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputType_MetaData), NewProp_InputType_MetaData) }; // 2174053632
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneInputPhoneNum_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_ScaleBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_TitleTextBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_AreaCodeButton,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_PhoneNumTextField,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_ActionButton,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_EnterIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_BackspaceIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_InputType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_InputType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneInputPhoneNum_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::ClassParams = {
	&UPSOneInputPhoneNum::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneInputPhoneNum_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::PropPointers),
	0,
	0x00A010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneInputPhoneNum_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneInputPhoneNum()
{
	if (!Z_Registration_Info_UClass_UPSOneInputPhoneNum.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneInputPhoneNum.OuterSingleton, Z_Construct_UClass_UPSOneInputPhoneNum_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneInputPhoneNum.OuterSingleton;
}
UPSOneInputPhoneNum::UPSOneInputPhoneNum(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneInputPhoneNum);
UPSOneInputPhoneNum::~UPSOneInputPhoneNum() {}
// ********** End Class UPSOneInputPhoneNum ********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h__Script_OneEngineSDK_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPSOneInputPhoneNumType_StaticEnum, TEXT("EPSOneInputPhoneNumType"), &Z_Registration_Info_UEnum_EPSOneInputPhoneNumType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2174053632U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneInputPhoneNum, UPSOneInputPhoneNum::StaticClass, TEXT("UPSOneInputPhoneNum"), &Z_Registration_Info_UClass_UPSOneInputPhoneNum, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneInputPhoneNum), 2656521463U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h__Script_OneEngineSDK_1226314249(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h__Script_OneEngineSDK_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h__Script_OneEngineSDK_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
