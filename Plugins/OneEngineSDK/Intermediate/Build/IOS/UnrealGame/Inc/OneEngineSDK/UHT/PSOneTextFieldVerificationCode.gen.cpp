// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneTextFieldVerificationCode.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneTextFieldVerificationCode() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldBase();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldVerificationCode();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldVerificationCode_NoRegister();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneTextFieldVerificationCode ******************************************
void UPSOneTextFieldVerificationCode::StaticRegisterNativesUPSOneTextFieldVerificationCode()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneTextFieldVerificationCode;
UClass* UPSOneTextFieldVerificationCode::GetPrivateStaticClass()
{
	using TClass = UPSOneTextFieldVerificationCode;
	if (!Z_Registration_Info_UClass_UPSOneTextFieldVerificationCode.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneTextFieldVerificationCode"),
			Z_Registration_Info_UClass_UPSOneTextFieldVerificationCode.InnerSingleton,
			StaticRegisterNativesUPSOneTextFieldVerificationCode,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneTextFieldVerificationCode.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneTextFieldVerificationCode_NoRegister()
{
	return UPSOneTextFieldVerificationCode::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneTextFieldVerificationCode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n *\n */" },
#endif
		{ "IncludePath", "Views/PSOneTextFieldVerificationCode.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldVerificationCode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldVerificationCode.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActionImage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneTextFieldVerificationCode>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneTextFieldVerificationCode_Statics::NewProp_ActionImage = { "ActionImage", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneTextFieldVerificationCode, ActionImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionImage_MetaData), NewProp_ActionImage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneTextFieldVerificationCode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldVerificationCode_Statics::NewProp_ActionImage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldVerificationCode_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneTextFieldVerificationCode_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneTextFieldBase,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldVerificationCode_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneTextFieldVerificationCode_Statics::ClassParams = {
	&UPSOneTextFieldVerificationCode::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneTextFieldVerificationCode_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldVerificationCode_Statics::PropPointers),
	0,
	0x00A010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldVerificationCode_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneTextFieldVerificationCode_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneTextFieldVerificationCode()
{
	if (!Z_Registration_Info_UClass_UPSOneTextFieldVerificationCode.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneTextFieldVerificationCode.OuterSingleton, Z_Construct_UClass_UPSOneTextFieldVerificationCode_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneTextFieldVerificationCode.OuterSingleton;
}
UPSOneTextFieldVerificationCode::UPSOneTextFieldVerificationCode(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneTextFieldVerificationCode);
UPSOneTextFieldVerificationCode::~UPSOneTextFieldVerificationCode() {}
// ********** End Class UPSOneTextFieldVerificationCode ********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldVerificationCode_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneTextFieldVerificationCode, UPSOneTextFieldVerificationCode::StaticClass, TEXT("UPSOneTextFieldVerificationCode"), &Z_Registration_Info_UClass_UPSOneTextFieldVerificationCode, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneTextFieldVerificationCode), 1535134330U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldVerificationCode_h__Script_OneEngineSDK_1268313693(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldVerificationCode_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldVerificationCode_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
