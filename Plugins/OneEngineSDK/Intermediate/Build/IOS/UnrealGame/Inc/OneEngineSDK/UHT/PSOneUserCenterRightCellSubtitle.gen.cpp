// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneUserCenterRightCellSubtitle.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneUserCenterRightCellSubtitle() {}

// ********** Begin Cross Module References ********************************************************
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UMG_API UClass* Z_Construct_UClass_USizeBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneUserCenterRightCellSubtitle ****************************************
void UPSOneUserCenterRightCellSubtitle::StaticRegisterNativesUPSOneUserCenterRightCellSubtitle()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle;
UClass* UPSOneUserCenterRightCellSubtitle::GetPrivateStaticClass()
{
	using TClass = UPSOneUserCenterRightCellSubtitle;
	if (!Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneUserCenterRightCellSubtitle"),
			Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle.InnerSingleton,
			StaticRegisterNativesUPSOneUserCenterRightCellSubtitle,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister()
{
	return UPSOneUserCenterRightCellSubtitle::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n *  \xe7\x9b\xae\xe5\x89\x8d\xe7\x94\xa8\xe4\xba\x8e\xe4\xb8\xaa\xe4\xba\xba\xe4\xbf\xa1\xe6\x81\xaf\xe5\xb1\x95\xe7\xa4\xba\n */" },
#endif
		{ "IncludePath", "Views/PSOneUserCenterRightCellSubtitle.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe7\x9b\xae\xe5\x89\x8d\xe7\x94\xa8\xe4\xba\x8e\xe4\xb8\xaa\xe4\xba\xba\xe4\xbf\xa1\xe6\x81\xaf\xe5\xb1\x95\xe7\xa4\xba" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Icon_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IconImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImageBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubTitle_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubTitleColor_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// SubTitle Color, 2B2B2BFF\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "SubTitle Color, 2B2B2BFF" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HiddenArrow_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ArrowImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Icon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IconImage;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ImageBox;
	static const UECodeGen_Private::FTextPropertyParams NewProp_SubTitle;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SubTitleColor;
	static void NewProp_HiddenArrow_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_HiddenArrow;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SubTextBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ArrowImage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterRightCellSubtitle>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_Icon = { "Icon", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle, Icon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Icon_MetaData), NewProp_Icon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_IconImage = { "IconImage", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle, IconImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IconImage_MetaData), NewProp_IconImage_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_ImageBox = { "ImageBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle, ImageBox), Z_Construct_UClass_USizeBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImageBox_MetaData), NewProp_ImageBox_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTitle = { "SubTitle", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle, SubTitle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubTitle_MetaData), NewProp_SubTitle_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTitleColor = { "SubTitleColor", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle, SubTitleColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubTitleColor_MetaData), NewProp_SubTitleColor_MetaData) };
void Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_HiddenArrow_SetBit(void* Obj)
{
	((UPSOneUserCenterRightCellSubtitle*)Obj)->HiddenArrow = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_HiddenArrow = { "HiddenArrow", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPSOneUserCenterRightCellSubtitle), &Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_HiddenArrow_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HiddenArrow_MetaData), NewProp_HiddenArrow_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTextBlock = { "SubTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle, SubTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubTextBlock_MetaData), NewProp_SubTextBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_ArrowImage = { "ArrowImage", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle, ArrowImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ArrowImage_MetaData), NewProp_ArrowImage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_Icon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_IconImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_ImageBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTitle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTitleColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_HiddenArrow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTextBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_ArrowImage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneButtonBase,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::ClassParams = {
	&UPSOneUserCenterRightCellSubtitle::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::PropPointers),
	0,
	0x00A010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle()
{
	if (!Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle.OuterSingleton, Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle.OuterSingleton;
}
UPSOneUserCenterRightCellSubtitle::UPSOneUserCenterRightCellSubtitle(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterRightCellSubtitle);
UPSOneUserCenterRightCellSubtitle::~UPSOneUserCenterRightCellSubtitle() {}
// ********** End Class UPSOneUserCenterRightCellSubtitle ******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellSubtitle_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle, UPSOneUserCenterRightCellSubtitle::StaticClass, TEXT("UPSOneUserCenterRightCellSubtitle"), &Z_Registration_Info_UClass_UPSOneUserCenterRightCellSubtitle, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneUserCenterRightCellSubtitle), 3525741182U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellSubtitle_h__Script_OneEngineSDK_8373051(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellSubtitle_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellSubtitle_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
