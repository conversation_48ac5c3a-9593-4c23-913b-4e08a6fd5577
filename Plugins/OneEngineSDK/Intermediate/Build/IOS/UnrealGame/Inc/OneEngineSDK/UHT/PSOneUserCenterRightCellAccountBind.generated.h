// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Views/PSOneUserCenterRightCellAccountBind.h"

#ifdef ONEENGINESDK_PSOneUserCenterRightCellAccountBind_generated_h
#error "PSOneUserCenterRightCellAccountBind.generated.h already included, missing '#pragma once' in PSOneUserCenterRightCellAccountBind.h"
#endif
#define ONEENGINESDK_PSOneUserCenterRightCellAccountBind_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UPSOneUserCenterRightCellAccountBind *************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_NoRegister();

#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAccountBind_h_11_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneUserCenterRightCellAccountBind(); \
	friend struct Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_NoRegister(); \
public: \
	DECLARE_CLASS2(UPSOneUserCenterRightCellAccountBind, UPSOneButtonBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_NoRegister) \
	DECLARE_SERIALIZER(UPSOneUserCenterRightCellAccountBind)


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAccountBind_h_11_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneUserCenterRightCellAccountBind(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPSOneUserCenterRightCellAccountBind(UPSOneUserCenterRightCellAccountBind&&) = delete; \
	UPSOneUserCenterRightCellAccountBind(const UPSOneUserCenterRightCellAccountBind&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneUserCenterRightCellAccountBind); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneUserCenterRightCellAccountBind); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneUserCenterRightCellAccountBind) \
	NO_API virtual ~UPSOneUserCenterRightCellAccountBind();


#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAccountBind_h_8_PROLOG
#define FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAccountBind_h_11_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAccountBind_h_11_INCLASS_NO_PURE_DECLS \
	FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAccountBind_h_11_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPSOneUserCenterRightCellAccountBind;

// ********** End Class UPSOneUserCenterRightCellAccountBind ***************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAccountBind_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
