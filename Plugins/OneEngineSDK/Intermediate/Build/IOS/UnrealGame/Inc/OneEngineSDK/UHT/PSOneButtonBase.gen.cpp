// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneButtonBase.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneButtonBase() {}

// ********** Begin Cross Module References ********************************************************
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase_NoRegister();
SLATECORE_API UEnum* Z_Construct_UEnum_SlateCore_EHorizontalAlignment();
UMG_API UClass* Z_Construct_UClass_UBorder_NoRegister();
UMG_API UClass* Z_Construct_UClass_UButton_NoRegister();
UMG_API UClass* Z_Construct_UClass_UHorizontalBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
UMG_API UClass* Z_Construct_UClass_UUserWidget();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneButtonBase Function OnClick ****************************************
struct Z_Construct_UFunction_UPSOneButtonBase_OnClick_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Callback\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneButtonBase_OnClick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneButtonBase, nullptr, "OnClick", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneButtonBase_OnClick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneButtonBase_OnClick_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPSOneButtonBase_OnClick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneButtonBase_OnClick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneButtonBase::execOnClick)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnClick();
	P_NATIVE_END;
}
// ********** End Class UPSOneButtonBase Function OnClick ******************************************

// ********** Begin Class UPSOneButtonBase Function SetTitle ***************************************
struct Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics
{
	struct PSOneButtonBase_eventSetTitle_Parms
	{
		FText InTitle;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InTitle_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FTextPropertyParams NewProp_InTitle;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FTextPropertyParams Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::NewProp_InTitle = { "InTitle", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PSOneButtonBase_eventSetTitle_Parms, InTitle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InTitle_MetaData), NewProp_InTitle_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::NewProp_InTitle,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPSOneButtonBase, nullptr, "SetTitle", Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::PSOneButtonBase_eventSetTitle_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::PSOneButtonBase_eventSetTitle_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPSOneButtonBase_SetTitle()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPSOneButtonBase_SetTitle_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPSOneButtonBase::execSetTitle)
{
	P_GET_PROPERTY_REF(FTextProperty,Z_Param_Out_InTitle);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTitle(Z_Param_Out_InTitle);
	P_NATIVE_END;
}
// ********** End Class UPSOneButtonBase Function SetTitle *****************************************

// ********** Begin Class UPSOneButtonBase *********************************************************
void UPSOneButtonBase::StaticRegisterNativesUPSOneButtonBase()
{
	UClass* Class = UPSOneButtonBase::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "OnClick", &UPSOneButtonBase::execOnClick },
		{ "SetTitle", &UPSOneButtonBase::execSetTitle },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneButtonBase;
UClass* UPSOneButtonBase::GetPrivateStaticClass()
{
	using TClass = UPSOneButtonBase;
	if (!Z_Registration_Info_UClass_UPSOneButtonBase.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneButtonBase"),
			Z_Registration_Info_UClass_UPSOneButtonBase.InnerSingleton,
			StaticRegisterNativesUPSOneButtonBase,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneButtonBase.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneButtonBase_NoRegister()
{
	return UPSOneButtonBase::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneButtonBase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n *\n */" },
#endif
		{ "IncludePath", "Views/PSOneButtonBase.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[] = {
		{ "Category", "PSOneButtonBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Alignment_MetaData[] = {
		{ "Category", "PSOneButtonBase" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//\xe5\xaf\xb9\xe9\xbd\x90\xe6\x96\xb9\xe5\xbc\x8f\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xe5\xaf\xb9\xe9\xbd\x90\xe6\x96\xb9\xe5\xbc\x8f" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextHorizontalBox_MetaData[] = {
		{ "BindWidgetOptional", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextSize_MetaData[] = {
		{ "Category", "PSOneButtonBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BgBorder_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Button_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectedTexture_MetaData[] = {
		{ "Category", "PSOneButtonBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NormalTexture_MetaData[] = {
		{ "Category", "PSOneButtonBase" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// normal\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "normal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FocusTexture_MetaData[] = {
		{ "Category", "PSOneButtonBase" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// onFocus\n" },
#endif
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "onFocus" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsSelected_MetaData[] = {
		{ "Category", "PSOneButtonBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneButtonBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FTextPropertyParams NewProp_Title;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Alignment;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TextHorizontalBox;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TextSize;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BgBorder;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Button;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TextBlock;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SelectedTexture;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NormalTexture;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FocusTexture;
	static void NewProp_bIsSelected_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsSelected;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UPSOneButtonBase_OnClick, "OnClick" }, // 2535945997
		{ &Z_Construct_UFunction_UPSOneButtonBase_SetTitle, "SetTitle" }, // 1767144086
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneButtonBase>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FTextPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneButtonBase, Title), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Title_MetaData), NewProp_Title_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Alignment = { "Alignment", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneButtonBase, Alignment), Z_Construct_UEnum_SlateCore_EHorizontalAlignment, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Alignment_MetaData), NewProp_Alignment_MetaData) }; // 1062133256
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextHorizontalBox = { "TextHorizontalBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneButtonBase, TextHorizontalBox), Z_Construct_UClass_UHorizontalBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextHorizontalBox_MetaData), NewProp_TextHorizontalBox_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextSize = { "TextSize", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneButtonBase, TextSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextSize_MetaData), NewProp_TextSize_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_BgBorder = { "BgBorder", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneButtonBase, BgBorder), Z_Construct_UClass_UBorder_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BgBorder_MetaData), NewProp_BgBorder_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Button = { "Button", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneButtonBase, Button), Z_Construct_UClass_UButton_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Button_MetaData), NewProp_Button_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextBlock = { "TextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneButtonBase, TextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextBlock_MetaData), NewProp_TextBlock_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_SelectedTexture = { "SelectedTexture", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneButtonBase, SelectedTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectedTexture_MetaData), NewProp_SelectedTexture_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_NormalTexture = { "NormalTexture", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneButtonBase, NormalTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NormalTexture_MetaData), NewProp_NormalTexture_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_FocusTexture = { "FocusTexture", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneButtonBase, FocusTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FocusTexture_MetaData), NewProp_FocusTexture_MetaData) };
void Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_bIsSelected_SetBit(void* Obj)
{
	((UPSOneButtonBase*)Obj)->bIsSelected = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_bIsSelected = { "bIsSelected", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPSOneButtonBase), &Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_bIsSelected_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsSelected_MetaData), NewProp_bIsSelected_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneButtonBase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Title,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Alignment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextHorizontalBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_BgBorder,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_Button,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_TextBlock,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_SelectedTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_NormalTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_FocusTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneButtonBase_Statics::NewProp_bIsSelected,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneButtonBase_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneButtonBase_Statics::ClassParams = {
	&UPSOneButtonBase::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UPSOneButtonBase_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneButtonBase_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneButtonBase_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneButtonBase()
{
	if (!Z_Registration_Info_UClass_UPSOneButtonBase.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneButtonBase.OuterSingleton, Z_Construct_UClass_UPSOneButtonBase_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneButtonBase.OuterSingleton;
}
UPSOneButtonBase::UPSOneButtonBase(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneButtonBase);
UPSOneButtonBase::~UPSOneButtonBase() {}
// ********** End Class UPSOneButtonBase ***********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneButtonBase_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneButtonBase, UPSOneButtonBase::StaticClass, TEXT("UPSOneButtonBase"), &Z_Registration_Info_UClass_UPSOneButtonBase, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneButtonBase), 1307516610U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneButtonBase_h__Script_OneEngineSDK_986501414(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneButtonBase_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneButtonBase_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
