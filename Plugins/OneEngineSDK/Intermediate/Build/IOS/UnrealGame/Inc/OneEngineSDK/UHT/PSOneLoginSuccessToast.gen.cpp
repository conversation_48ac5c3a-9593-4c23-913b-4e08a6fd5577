// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneLoginSuccessToast.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneLoginSuccessToast() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneLoginSuccessToast();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneLoginSuccessToast_NoRegister();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UMG_API UClass* Z_Construct_UClass_USizeBox_NoRegister();
UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
UMG_API UClass* Z_Construct_UClass_UUserWidget();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneLoginSuccessToast **************************************************
void UPSOneLoginSuccessToast::StaticRegisterNativesUPSOneLoginSuccessToast()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneLoginSuccessToast;
UClass* UPSOneLoginSuccessToast::GetPrivateStaticClass()
{
	using TClass = UPSOneLoginSuccessToast;
	if (!Z_Registration_Info_UClass_UPSOneLoginSuccessToast.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneLoginSuccessToast"),
			Z_Registration_Info_UClass_UPSOneLoginSuccessToast.InnerSingleton,
			StaticRegisterNativesUPSOneLoginSuccessToast,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneLoginSuccessToast.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneLoginSuccessToast_NoRegister()
{
	return UPSOneLoginSuccessToast::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneLoginSuccessToast_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * \n */" },
#endif
		{ "IncludePath", "Views/PSOneLoginSuccessToast.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneLoginSuccessToast.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Icon_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLoginSuccessToast.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImageSizeBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLoginSuccessToast.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneLoginSuccessToast.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Icon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ImageSizeBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TextBlock;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneLoginSuccessToast>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::NewProp_Icon = { "Icon", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLoginSuccessToast, Icon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Icon_MetaData), NewProp_Icon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::NewProp_ImageSizeBox = { "ImageSizeBox", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLoginSuccessToast, ImageSizeBox), Z_Construct_UClass_USizeBox_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImageSizeBox_MetaData), NewProp_ImageSizeBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::NewProp_TextBlock = { "TextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneLoginSuccessToast, TextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextBlock_MetaData), NewProp_TextBlock_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::NewProp_Icon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::NewProp_ImageSizeBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::NewProp_TextBlock,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::ClassParams = {
	&UPSOneLoginSuccessToast::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneLoginSuccessToast()
{
	if (!Z_Registration_Info_UClass_UPSOneLoginSuccessToast.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneLoginSuccessToast.OuterSingleton, Z_Construct_UClass_UPSOneLoginSuccessToast_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneLoginSuccessToast.OuterSingleton;
}
UPSOneLoginSuccessToast::UPSOneLoginSuccessToast(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneLoginSuccessToast);
UPSOneLoginSuccessToast::~UPSOneLoginSuccessToast() {}
// ********** End Class UPSOneLoginSuccessToast ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneLoginSuccessToast_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneLoginSuccessToast, UPSOneLoginSuccessToast::StaticClass, TEXT("UPSOneLoginSuccessToast"), &Z_Registration_Info_UClass_UPSOneLoginSuccessToast, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneLoginSuccessToast), 1193358229U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneLoginSuccessToast_h__Script_OneEngineSDK_3196717212(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneLoginSuccessToast_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneLoginSuccessToast_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
