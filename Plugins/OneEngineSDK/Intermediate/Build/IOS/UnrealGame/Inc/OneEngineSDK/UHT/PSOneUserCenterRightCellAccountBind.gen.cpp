// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneUserCenterRightCellAccountBind.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneUserCenterRightCellAccountBind() {}

// ********** Begin Cross Module References ********************************************************
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_NoRegister();
UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneUserCenterRightCellAccountBind *************************************
void UPSOneUserCenterRightCellAccountBind::StaticRegisterNativesUPSOneUserCenterRightCellAccountBind()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneUserCenterRightCellAccountBind;
UClass* UPSOneUserCenterRightCellAccountBind::GetPrivateStaticClass()
{
	using TClass = UPSOneUserCenterRightCellAccountBind;
	if (!Z_Registration_Info_UClass_UPSOneUserCenterRightCellAccountBind.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneUserCenterRightCellAccountBind"),
			Z_Registration_Info_UClass_UPSOneUserCenterRightCellAccountBind.InnerSingleton,
			StaticRegisterNativesUPSOneUserCenterRightCellAccountBind,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterRightCellAccountBind.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_NoRegister()
{
	return UPSOneUserCenterRightCellAccountBind::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "IncludePath", "Views/PSOneUserCenterRightCellAccountBind.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAccountBind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsBind_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellAccountBind" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAccountBind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnBindIcon_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellAccountBind" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAccountBind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BindIcon_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellAccountBind" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAccountBind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Icon_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellAccountBind" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAccountBind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IconImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAccountBind.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BindImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAccountBind.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bIsBind_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsBind;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_UnBindIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BindIcon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Icon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IconImage;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BindImage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterRightCellAccountBind>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_bIsBind_SetBit(void* Obj)
{
	((UPSOneUserCenterRightCellAccountBind*)Obj)->bIsBind = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_bIsBind = { "bIsBind", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPSOneUserCenterRightCellAccountBind), &Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_bIsBind_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsBind_MetaData), NewProp_bIsBind_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_UnBindIcon = { "UnBindIcon", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellAccountBind, UnBindIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnBindIcon_MetaData), NewProp_UnBindIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_BindIcon = { "BindIcon", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellAccountBind, BindIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BindIcon_MetaData), NewProp_BindIcon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_Icon = { "Icon", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellAccountBind, Icon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Icon_MetaData), NewProp_Icon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_IconImage = { "IconImage", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellAccountBind, IconImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IconImage_MetaData), NewProp_IconImage_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_BindImage = { "BindImage", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellAccountBind, BindImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BindImage_MetaData), NewProp_BindImage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_bIsBind,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_UnBindIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_BindIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_Icon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_IconImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::NewProp_BindImage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneButtonBase,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::ClassParams = {
	&UPSOneUserCenterRightCellAccountBind::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind()
{
	if (!Z_Registration_Info_UClass_UPSOneUserCenterRightCellAccountBind.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneUserCenterRightCellAccountBind.OuterSingleton, Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterRightCellAccountBind.OuterSingleton;
}
UPSOneUserCenterRightCellAccountBind::UPSOneUserCenterRightCellAccountBind(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterRightCellAccountBind);
UPSOneUserCenterRightCellAccountBind::~UPSOneUserCenterRightCellAccountBind() {}
// ********** End Class UPSOneUserCenterRightCellAccountBind ***************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAccountBind_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneUserCenterRightCellAccountBind, UPSOneUserCenterRightCellAccountBind::StaticClass, TEXT("UPSOneUserCenterRightCellAccountBind"), &Z_Registration_Info_UClass_UPSOneUserCenterRightCellAccountBind, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneUserCenterRightCellAccountBind), 3181567394U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAccountBind_h__Script_OneEngineSDK_770476995(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAccountBind_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterRightCellAccountBind_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
