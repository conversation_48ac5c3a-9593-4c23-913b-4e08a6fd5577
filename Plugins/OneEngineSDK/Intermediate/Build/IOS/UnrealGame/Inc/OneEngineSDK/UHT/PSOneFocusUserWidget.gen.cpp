// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneFocusUserWidget.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneFocusUserWidget() {}

// ********** Begin Cross Module References ********************************************************
COREUOBJECT_API UClass* Z_Construct_UClass_UInterface();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneSaveFocusWidgetInterface();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_NoRegister();
UMG_API UClass* Z_Construct_UClass_UUserWidget();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Interface UPSOneSaveFocusWidgetInterface ***************************************
void UPSOneSaveFocusWidgetInterface::StaticRegisterNativesUPSOneSaveFocusWidgetInterface()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneSaveFocusWidgetInterface;
UClass* UPSOneSaveFocusWidgetInterface::GetPrivateStaticClass()
{
	using TClass = UPSOneSaveFocusWidgetInterface;
	if (!Z_Registration_Info_UClass_UPSOneSaveFocusWidgetInterface.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneSaveFocusWidgetInterface"),
			Z_Registration_Info_UClass_UPSOneSaveFocusWidgetInterface.InnerSingleton,
			StaticRegisterNativesUPSOneSaveFocusWidgetInterface,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneSaveFocusWidgetInterface.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_NoRegister()
{
	return UPSOneSaveFocusWidgetInterface::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneFocusUserWidget.h" },
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<IPSOneSaveFocusWidgetInterface>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UInterface,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics::ClassParams = {
	&UPSOneSaveFocusWidgetInterface::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	0,
	0,
	0x000840A1u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneSaveFocusWidgetInterface()
{
	if (!Z_Registration_Info_UClass_UPSOneSaveFocusWidgetInterface.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneSaveFocusWidgetInterface.OuterSingleton, Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneSaveFocusWidgetInterface.OuterSingleton;
}
UPSOneSaveFocusWidgetInterface::UPSOneSaveFocusWidgetInterface(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneSaveFocusWidgetInterface);
// ********** End Interface UPSOneSaveFocusWidgetInterface *****************************************

// ********** Begin Class UPSOneFocusUserWidget ****************************************************
void UPSOneFocusUserWidget::StaticRegisterNativesUPSOneFocusUserWidget()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneFocusUserWidget;
UClass* UPSOneFocusUserWidget::GetPrivateStaticClass()
{
	using TClass = UPSOneFocusUserWidget;
	if (!Z_Registration_Info_UClass_UPSOneFocusUserWidget.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneFocusUserWidget"),
			Z_Registration_Info_UClass_UPSOneFocusUserWidget.InnerSingleton,
			StaticRegisterNativesUPSOneFocusUserWidget,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneFocusUserWidget.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneFocusUserWidget_NoRegister()
{
	return UPSOneFocusUserWidget::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneFocusUserWidget_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "IncludePath", "Views/PSOneFocusUserWidget.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneFocusUserWidget.h" },
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneFocusUserWidget>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UPSOneFocusUserWidget_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneFocusUserWidget_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneFocusUserWidget_Statics::ClassParams = {
	&UPSOneFocusUserWidget::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	0,
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneFocusUserWidget_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneFocusUserWidget_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneFocusUserWidget()
{
	if (!Z_Registration_Info_UClass_UPSOneFocusUserWidget.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneFocusUserWidget.OuterSingleton, Z_Construct_UClass_UPSOneFocusUserWidget_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneFocusUserWidget.OuterSingleton;
}
UPSOneFocusUserWidget::UPSOneFocusUserWidget(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneFocusUserWidget);
UPSOneFocusUserWidget::~UPSOneFocusUserWidget() {}
// ********** End Class UPSOneFocusUserWidget ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneSaveFocusWidgetInterface, UPSOneSaveFocusWidgetInterface::StaticClass, TEXT("UPSOneSaveFocusWidgetInterface"), &Z_Registration_Info_UClass_UPSOneSaveFocusWidgetInterface, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneSaveFocusWidgetInterface), 4107772960U) },
		{ Z_Construct_UClass_UPSOneFocusUserWidget, UPSOneFocusUserWidget::StaticClass, TEXT("UPSOneFocusUserWidget"), &Z_Registration_Info_UClass_UPSOneFocusUserWidget, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneFocusUserWidget), 2855082660U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h__Script_OneEngineSDK_284476308(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneFocusUserWidget_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
