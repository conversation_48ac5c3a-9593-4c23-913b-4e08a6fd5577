// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Views/PSOneUserCenterLegalTerms.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePSOneUserCenterLegalTerms() {}

// ********** Begin Cross Module References ********************************************************
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterLegalTerms();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterLegalTerms_NoRegister();
ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister();
UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPSOneUserCenterLegalTerms ***********************************************
void UPSOneUserCenterLegalTerms::StaticRegisterNativesUPSOneUserCenterLegalTerms()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPSOneUserCenterLegalTerms;
UClass* UPSOneUserCenterLegalTerms::GetPrivateStaticClass()
{
	using TClass = UPSOneUserCenterLegalTerms;
	if (!Z_Registration_Info_UClass_UPSOneUserCenterLegalTerms.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PSOneUserCenterLegalTerms"),
			Z_Registration_Info_UClass_UPSOneUserCenterLegalTerms.InnerSingleton,
			StaticRegisterNativesUPSOneUserCenterLegalTerms,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterLegalTerms.InnerSingleton;
}
UClass* Z_Construct_UClass_UPSOneUserCenterLegalTerms_NoRegister()
{
	return UPSOneUserCenterLegalTerms::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPSOneUserCenterLegalTerms_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * \n */" },
#endif
		{ "IncludePath", "Views/PSOneUserCenterLegalTerms.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterLegalTerms.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LegalCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterLegalTerms.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LegalCell;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterLegalTerms>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterLegalTerms_Statics::NewProp_LegalCell = { "LegalCell", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPSOneUserCenterLegalTerms, LegalCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LegalCell_MetaData), NewProp_LegalCell_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterLegalTerms_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterLegalTerms_Statics::NewProp_LegalCell,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterLegalTerms_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPSOneUserCenterLegalTerms_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterLegalTerms_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterLegalTerms_Statics::ClassParams = {
	&UPSOneUserCenterLegalTerms::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UPSOneUserCenterLegalTerms_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterLegalTerms_Statics::PropPointers),
	0,
	0x00B010A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterLegalTerms_Statics::Class_MetaDataParams), Z_Construct_UClass_UPSOneUserCenterLegalTerms_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPSOneUserCenterLegalTerms()
{
	if (!Z_Registration_Info_UClass_UPSOneUserCenterLegalTerms.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPSOneUserCenterLegalTerms.OuterSingleton, Z_Construct_UClass_UPSOneUserCenterLegalTerms_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPSOneUserCenterLegalTerms.OuterSingleton;
}
UPSOneUserCenterLegalTerms::UPSOneUserCenterLegalTerms(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterLegalTerms);
UPSOneUserCenterLegalTerms::~UPSOneUserCenterLegalTerms() {}
// ********** End Class UPSOneUserCenterLegalTerms *************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterLegalTerms_h__Script_OneEngineSDK_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPSOneUserCenterLegalTerms, UPSOneUserCenterLegalTerms::StaticClass, TEXT("UPSOneUserCenterLegalTerms"), &Z_Registration_Info_UClass_UPSOneUserCenterLegalTerms, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPSOneUserCenterLegalTerms), 2198107622U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterLegalTerms_h__Script_OneEngineSDK_2704106452(TEXT("/Script/OneEngineSDK"),
	Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterLegalTerms_h__Script_OneEngineSDK_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_zhangjia_Desktop_pwrdwork_OneSDK_OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterLegalTerms_h__Script_OneEngineSDK_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
