// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneInputPhoneNum.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneInputPhoneNum() {}
// Cross Module References
	ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneInputPhoneNum_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneInputPhoneNum();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldBase_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneConfirmButton_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
// End Cross Module References
	static UEnum* EPSOneInputPhoneNumType_StaticEnum()
	{
		static UEnum* Singleton = nullptr;
		if (!Singleton)
		{
			Singleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EPSOneInputPhoneNumType"));
		}
		return Singleton;
	}
	template<> ONEENGINESDK_API UEnum* StaticEnum<EPSOneInputPhoneNumType>()
	{
		return EPSOneInputPhoneNumType_StaticEnum();
	}
	static FCompiledInDeferEnum Z_CompiledInDeferEnum_UEnum_EPSOneInputPhoneNumType(EPSOneInputPhoneNumType_StaticEnum, TEXT("/Script/OneEngineSDK"), TEXT("EPSOneInputPhoneNumType"), false, nullptr, nullptr);
	uint32 Get_Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType_Hash() { return 2881940660U; }
	UEnum* Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType()
	{
#if WITH_HOT_RELOAD
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UEnum* ReturnEnum = FindExistingEnumIfHotReloadOrDynamic(Outer, TEXT("EPSOneInputPhoneNumType"), 0, Get_Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType_Hash(), false);
#else
		static UEnum* ReturnEnum = nullptr;
#endif // WITH_HOT_RELOAD
		if (!ReturnEnum)
		{
			static const UE4CodeGen_Private::FEnumeratorParam Enumerators[] = {
				{ "EPSOneInputPhoneNumType::PhoneNum", (int64)EPSOneInputPhoneNumType::PhoneNum },
				{ "EPSOneInputPhoneNumType::Email", (int64)EPSOneInputPhoneNumType::Email },
			};
#if WITH_METADATA
			const UE4CodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
				{ "BlueprintType", "true" },
				{ "Comment", "// \xe6\x9e\x9a\xe4\xb8\xbe\n" },
				{ "Email.Name", "EPSOneInputPhoneNumType::Email" },
				{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
				{ "PhoneNum.Name", "EPSOneInputPhoneNumType::PhoneNum" },
				{ "ToolTip", "\xe6\x9e\x9a\xe4\xb8\xbe" },
			};
#endif
			static const UE4CodeGen_Private::FEnumParams EnumParams = {
				(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
				nullptr,
				"EPSOneInputPhoneNumType",
				"EPSOneInputPhoneNumType",
				Enumerators,
				UE_ARRAY_COUNT(Enumerators),
				RF_Public|RF_Transient|RF_MarkAsNative,
				EEnumFlags::None,
				UE4CodeGen_Private::EDynamicType::NotDynamic,
				(uint8)UEnum::ECppForm::EnumClass,
				METADATA_PARAMS(Enum_MetaDataParams, UE_ARRAY_COUNT(Enum_MetaDataParams))
			};
			UE4CodeGen_Private::ConstructUEnum(ReturnEnum, EnumParams);
		}
		return ReturnEnum;
	}
	void UPSOneInputPhoneNum::StaticRegisterNativesUPSOneInputPhoneNum()
	{
	}
	UClass* Z_Construct_UClass_UPSOneInputPhoneNum_NoRegister()
	{
		return UPSOneInputPhoneNum::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneInputPhoneNum_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TitleTextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_TitleTextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AreaCodeButton_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_AreaCodeButton;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PhoneNumTextField_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_PhoneNumTextField;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ActionButton_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ActionButton;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
		static const UE4CodeGen_Private::FBytePropertyParams NewProp_InputType_Underlying;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_InputType_MetaData[];
#endif
		static const UE4CodeGen_Private::FEnumPropertyParams NewProp_InputType;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneInputPhoneNum_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneInputPhoneNum_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n * \xe7\x94\xa8\xe4\xba\x8e\xe7\xbb\x91\xe5\xae\x9a\xe6\x89\x8b\xe6\x9c\xba\xe5\x8f\xb7\xe6\x88\x96\xe9\x82\xae\xe7\xae\xb1\xe7\x9a\x84\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86""0\n */" },
		{ "IncludePath", "Views/PSOneInputPhoneNum.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe4\xba\x8e\xe7\xbb\x91\xe5\xae\x9a\xe6\x89\x8b\xe6\x9c\xba\xe5\x8f\xb7\xe6\x88\x96\xe9\x82\xae\xe7\xae\xb1\xe7\x9a\x84\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86""0" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_ScaleBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_ScaleBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_TitleTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe6\xa0\x87\xe9\xa2\x98\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
		{ "ToolTip", "\xe6\xa0\x87\xe9\xa2\x98" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_TitleTextBlock = { "TitleTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, TitleTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_TitleTextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_TitleTextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_AreaCodeButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_AreaCodeButton = { "AreaCodeButton", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, AreaCodeButton), Z_Construct_UClass_UPSOneButtonBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_AreaCodeButton_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_AreaCodeButton_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_PhoneNumTextField_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_PhoneNumTextField = { "PhoneNumTextField", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, PhoneNumTextField), Z_Construct_UClass_UPSOneTextFieldBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_PhoneNumTextField_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_PhoneNumTextField_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_ActionButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_ActionButton = { "ActionButton", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, ActionButton), Z_Construct_UClass_UPSOneConfirmButton_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_ActionButton_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_ActionButton_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// enter icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
		{ "ToolTip", "enter icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_EnterIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_EnterIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// backspace icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
		{ "ToolTip", "backspace icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_BackspaceIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_BackspaceIcon_MetaData)) };
	const UE4CodeGen_Private::FBytePropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_InputType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UE4CodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, nullptr, METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_InputType_MetaData[] = {
		{ "Category", "PSOneInputPhoneNum" },
		{ "ModuleRelativePath", "Private/Views/PSOneInputPhoneNum.h" },
	};
#endif
	const UE4CodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_InputType = { "InputType", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneInputPhoneNum, InputType), Z_Construct_UEnum_OneEngineSDK_EPSOneInputPhoneNumType, METADATA_PARAMS(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_InputType_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_InputType_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneInputPhoneNum_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_ScaleBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_TitleTextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_AreaCodeButton,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_PhoneNumTextField,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_ActionButton,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_EnterIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_BackspaceIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_InputType_Underlying,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneInputPhoneNum_Statics::NewProp_InputType,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneInputPhoneNum_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneInputPhoneNum>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneInputPhoneNum_Statics::ClassParams = {
		&UPSOneInputPhoneNum::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneInputPhoneNum_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::PropPointers),
		0,
		0x00A010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneInputPhoneNum_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneInputPhoneNum()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneInputPhoneNum_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneInputPhoneNum, 3437304043);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneInputPhoneNum>()
	{
		return UPSOneInputPhoneNum::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneInputPhoneNum(Z_Construct_UClass_UPSOneInputPhoneNum, &UPSOneInputPhoneNum::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneInputPhoneNum"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneInputPhoneNum);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
