// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
struct FOnePSProductCategory;
struct FOnePSUserProfileResponse;
enum class EOnePSStoreIconPos : uint8;
enum class EOnePsnAccountState : uint8;
#ifdef ONEENGINESDK_OneEngineSDKPSSubsystem_generated_h
#error "OneEngineSDKPSSubsystem.generated.h already included, missing '#pragma once' in OneEngineSDKPSSubsystem.h"
#endif
#define ONEENGINESDK_OneEngineSDKPSSubsystem_generated_h

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_324_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSProductCategory_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOnePSProductCategory>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_269_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSProduct_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOnePSProduct>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_179_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSProductSku_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOnePSProductSku>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_171_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSProductMedia_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOnePSProductMedia>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_147_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSProductMediaImage_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOnePSProductMediaImage>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_81_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSPurchaseForm_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOnePSPurchaseForm>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_63_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSUserProfileResponse_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOnePSUserProfileResponse>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_30_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePSUserProfile_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOnePSUserProfile>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_659_DELEGATE \
struct OneEngineSDKPSSubsystem_eventWidgetVisibilityDelegate_Parms \
{ \
	int32 WidgetType; \
	bool bVisible; \
}; \
static inline void FWidgetVisibilityDelegate_DelegateWrapper(const FMulticastScriptDelegate& WidgetVisibilityDelegate, int32 WidgetType, bool bVisible) \
{ \
	OneEngineSDKPSSubsystem_eventWidgetVisibilityDelegate_Parms Parms; \
	Parms.WidgetType=WidgetType; \
	Parms.bVisible=bVisible ? true : false; \
	WidgetVisibilityDelegate.ProcessMulticastDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_503_DELEGATE \
struct OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms \
{ \
	bool bSucceed; \
	FOnePSProductCategory Category; \
	int32 Code; \
	FString Msg; \
}; \
static inline void FOnGetProductInfoListPSDelegate_DelegateWrapper(const FScriptDelegate& OnGetProductInfoListPSDelegate, bool bSucceed, FOnePSProductCategory const& Category, int32 Code, const FString& Msg) \
{ \
	OneEngineSDKPSSubsystem_eventOnGetProductInfoListPSDelegate_Parms Parms; \
	Parms.bSucceed=bSucceed ? true : false; \
	Parms.Category=Category; \
	Parms.Code=Code; \
	Parms.Msg=Msg; \
	OnGetProductInfoListPSDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_501_DELEGATE \
struct OneEngineSDKPSSubsystem_eventOnOpenDialogResultDelegate_Parms \
{ \
	int32 Result; \
}; \
static inline void FOnOpenDialogResultDelegate_DelegateWrapper(const FScriptDelegate& OnOpenDialogResultDelegate, int32 Result) \
{ \
	OneEngineSDKPSSubsystem_eventOnOpenDialogResultDelegate_Parms Parms; \
	Parms.Result=Result; \
	OnOpenDialogResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_499_DELEGATE \
struct OneEngineSDKPSSubsystem_eventOnFilterProfanityResultDelegate_Parms \
{ \
	int32 Code; \
	FString Text; \
}; \
static inline void FOnFilterProfanityResultDelegate_DelegateWrapper(const FScriptDelegate& OnFilterProfanityResultDelegate, int32 Code, const FString& Text) \
{ \
	OneEngineSDKPSSubsystem_eventOnFilterProfanityResultDelegate_Parms Parms; \
	Parms.Code=Code; \
	Parms.Text=Text; \
	OnFilterProfanityResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_497_DELEGATE \
struct OneEngineSDKPSSubsystem_eventOnCheckPremiumResultDelegate_Parms \
{ \
	int32 Code; \
	bool bIsPremium; \
}; \
static inline void FOnCheckPremiumResultDelegate_DelegateWrapper(const FScriptDelegate& OnCheckPremiumResultDelegate, int32 Code, bool bIsPremium) \
{ \
	OneEngineSDKPSSubsystem_eventOnCheckPremiumResultDelegate_Parms Parms; \
	Parms.Code=Code; \
	Parms.bIsPremium=bIsPremium ? true : false; \
	OnCheckPremiumResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_495_DELEGATE \
struct OneEngineSDKPSSubsystem_eventOnRestrictionStatusResultDelegate_Parms \
{ \
	int32 Result; \
}; \
static inline void FOnRestrictionStatusResultDelegate_DelegateWrapper(const FScriptDelegate& OnRestrictionStatusResultDelegate, int32 Result) \
{ \
	OneEngineSDKPSSubsystem_eventOnRestrictionStatusResultDelegate_Parms Parms; \
	Parms.Result=Result; \
	OnRestrictionStatusResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_493_DELEGATE \
struct OneEngineSDKPSSubsystem_eventOnGetBlockingUsersResultDelegate_Parms \
{ \
	FOnePSUserProfileResponse ProfileList; \
}; \
static inline void FOnGetBlockingUsersResultDelegate_DelegateWrapper(const FScriptDelegate& OnGetBlockingUsersResultDelegate, FOnePSUserProfileResponse const& ProfileList) \
{ \
	OneEngineSDKPSSubsystem_eventOnGetBlockingUsersResultDelegate_Parms Parms; \
	Parms.ProfileList=ProfileList; \
	OnGetBlockingUsersResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_491_DELEGATE \
struct OneEngineSDKPSSubsystem_eventOnGetFriendsResultDelegate_Parms \
{ \
	FOnePSUserProfileResponse ProfileList; \
}; \
static inline void FOnGetFriendsResultDelegate_DelegateWrapper(const FScriptDelegate& OnGetFriendsResultDelegate, FOnePSUserProfileResponse const& ProfileList) \
{ \
	OneEngineSDKPSSubsystem_eventOnGetFriendsResultDelegate_Parms Parms; \
	Parms.ProfileList=ProfileList; \
	OnGetFriendsResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_SPARSE_DATA
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_RPC_WRAPPERS \
 \
	DECLARE_FUNCTION(execIndonesian); \
	DECLARE_FUNCTION(execVietnamese); \
	DECLARE_FUNCTION(execThai); \
	DECLARE_FUNCTION(execRomanian); \
	DECLARE_FUNCTION(execGreek); \
	DECLARE_FUNCTION(execHungarian); \
	DECLARE_FUNCTION(execCzech); \
	DECLARE_FUNCTION(execFrench_CA); \
	DECLARE_FUNCTION(execArabic); \
	DECLARE_FUNCTION(execSpanish_LA); \
	DECLARE_FUNCTION(execTurkish); \
	DECLARE_FUNCTION(execEnglish_GB); \
	DECLARE_FUNCTION(execPortuguese_BR); \
	DECLARE_FUNCTION(execPolish); \
	DECLARE_FUNCTION(execNorwegian); \
	DECLARE_FUNCTION(execDanish); \
	DECLARE_FUNCTION(execSwedish); \
	DECLARE_FUNCTION(execFinnish); \
	DECLARE_FUNCTION(execChinese_S); \
	DECLARE_FUNCTION(execChinese_T); \
	DECLARE_FUNCTION(execKorean); \
	DECLARE_FUNCTION(execRussian); \
	DECLARE_FUNCTION(execPortuguese_PT); \
	DECLARE_FUNCTION(execDutch); \
	DECLARE_FUNCTION(execItalian); \
	DECLARE_FUNCTION(execGerman); \
	DECLARE_FUNCTION(execSpanish); \
	DECLARE_FUNCTION(execFrench); \
	DECLARE_FUNCTION(execEnglish_US); \
	DECLARE_FUNCTION(execJapanese);


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_RPC_WRAPPERS_NO_PURE_DECLS \
 \
	DECLARE_FUNCTION(execIndonesian); \
	DECLARE_FUNCTION(execVietnamese); \
	DECLARE_FUNCTION(execThai); \
	DECLARE_FUNCTION(execRomanian); \
	DECLARE_FUNCTION(execGreek); \
	DECLARE_FUNCTION(execHungarian); \
	DECLARE_FUNCTION(execCzech); \
	DECLARE_FUNCTION(execFrench_CA); \
	DECLARE_FUNCTION(execArabic); \
	DECLARE_FUNCTION(execSpanish_LA); \
	DECLARE_FUNCTION(execTurkish); \
	DECLARE_FUNCTION(execEnglish_GB); \
	DECLARE_FUNCTION(execPortuguese_BR); \
	DECLARE_FUNCTION(execPolish); \
	DECLARE_FUNCTION(execNorwegian); \
	DECLARE_FUNCTION(execDanish); \
	DECLARE_FUNCTION(execSwedish); \
	DECLARE_FUNCTION(execFinnish); \
	DECLARE_FUNCTION(execChinese_S); \
	DECLARE_FUNCTION(execChinese_T); \
	DECLARE_FUNCTION(execKorean); \
	DECLARE_FUNCTION(execRussian); \
	DECLARE_FUNCTION(execPortuguese_PT); \
	DECLARE_FUNCTION(execDutch); \
	DECLARE_FUNCTION(execItalian); \
	DECLARE_FUNCTION(execGerman); \
	DECLARE_FUNCTION(execSpanish); \
	DECLARE_FUNCTION(execFrench); \
	DECLARE_FUNCTION(execEnglish_US); \
	DECLARE_FUNCTION(execJapanese);


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUOnePSLocaleEnum(); \
	friend struct Z_Construct_UClass_UOnePSLocaleEnum_Statics; \
public: \
	DECLARE_CLASS(UOnePSLocaleEnum, UBlueprintFunctionLibrary, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UOnePSLocaleEnum)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_INCLASS \
private: \
	static void StaticRegisterNativesUOnePSLocaleEnum(); \
	friend struct Z_Construct_UClass_UOnePSLocaleEnum_Statics; \
public: \
	DECLARE_CLASS(UOnePSLocaleEnum, UBlueprintFunctionLibrary, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UOnePSLocaleEnum)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UOnePSLocaleEnum(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UOnePSLocaleEnum) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UOnePSLocaleEnum); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UOnePSLocaleEnum); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UOnePSLocaleEnum(UOnePSLocaleEnum&&); \
	NO_API UOnePSLocaleEnum(const UOnePSLocaleEnum&); \
public:


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UOnePSLocaleEnum(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()) : Super(ObjectInitializer) { }; \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UOnePSLocaleEnum(UOnePSLocaleEnum&&); \
	NO_API UOnePSLocaleEnum(const UOnePSLocaleEnum&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UOnePSLocaleEnum); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UOnePSLocaleEnum); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UOnePSLocaleEnum)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_PRIVATE_PROPERTY_OFFSET
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_388_PROLOG
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_RPC_WRAPPERS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_INCLASS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_RPC_WRAPPERS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_INCLASS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_391_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ONEENGINESDK_API UClass* StaticClass<class UOnePSLocaleEnum>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_SPARSE_DATA
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_RPC_WRAPPERS \
 \
	DECLARE_FUNCTION(execSetFontPath); \
	DECLARE_FUNCTION(execGetProductInfoListPS); \
	DECLARE_FUNCTION(execOpenCommerceDialogPremiumMode); \
	DECLARE_FUNCTION(execFilterProfanitySync); \
	DECLARE_FUNCTION(execFilterProfanity); \
	DECLARE_FUNCTION(execStopNotifyPremiumFeature); \
	DECLARE_FUNCTION(execStartNotifyPremiumFeature); \
	DECLARE_FUNCTION(execCheckPremium); \
	DECLARE_FUNCTION(execGetCommunicationRestrictionStatus); \
	DECLARE_FUNCTION(execHideStoreIcon); \
	DECLARE_FUNCTION(execShowStoreIcon); \
	DECLARE_FUNCTION(execGetCountryRegion); \
	DECLARE_FUNCTION(execGetCountryCode); \
	DECLARE_FUNCTION(execGetOnlineId); \
	DECLARE_FUNCTION(execGetAccountId); \
	DECLARE_FUNCTION(execGetBlockingUsers); \
	DECLARE_FUNCTION(execGetFriends); \
	DECLARE_FUNCTION(execGetAccountState);


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_RPC_WRAPPERS_NO_PURE_DECLS \
 \
	DECLARE_FUNCTION(execSetFontPath); \
	DECLARE_FUNCTION(execGetProductInfoListPS); \
	DECLARE_FUNCTION(execOpenCommerceDialogPremiumMode); \
	DECLARE_FUNCTION(execFilterProfanitySync); \
	DECLARE_FUNCTION(execFilterProfanity); \
	DECLARE_FUNCTION(execStopNotifyPremiumFeature); \
	DECLARE_FUNCTION(execStartNotifyPremiumFeature); \
	DECLARE_FUNCTION(execCheckPremium); \
	DECLARE_FUNCTION(execGetCommunicationRestrictionStatus); \
	DECLARE_FUNCTION(execHideStoreIcon); \
	DECLARE_FUNCTION(execShowStoreIcon); \
	DECLARE_FUNCTION(execGetCountryRegion); \
	DECLARE_FUNCTION(execGetCountryCode); \
	DECLARE_FUNCTION(execGetOnlineId); \
	DECLARE_FUNCTION(execGetAccountId); \
	DECLARE_FUNCTION(execGetBlockingUsers); \
	DECLARE_FUNCTION(execGetFriends); \
	DECLARE_FUNCTION(execGetAccountState);


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUOneEngineSDKPSSubsystem(); \
	friend struct Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics; \
public: \
	DECLARE_CLASS(UOneEngineSDKPSSubsystem, UGameInstanceSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UOneEngineSDKPSSubsystem)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_INCLASS \
private: \
	static void StaticRegisterNativesUOneEngineSDKPSSubsystem(); \
	friend struct Z_Construct_UClass_UOneEngineSDKPSSubsystem_Statics; \
public: \
	DECLARE_CLASS(UOneEngineSDKPSSubsystem, UGameInstanceSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UOneEngineSDKPSSubsystem)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UOneEngineSDKPSSubsystem(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UOneEngineSDKPSSubsystem) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UOneEngineSDKPSSubsystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UOneEngineSDKPSSubsystem); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UOneEngineSDKPSSubsystem(UOneEngineSDKPSSubsystem&&); \
	NO_API UOneEngineSDKPSSubsystem(const UOneEngineSDKPSSubsystem&); \
public:


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UOneEngineSDKPSSubsystem() { }; \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UOneEngineSDKPSSubsystem(UOneEngineSDKPSSubsystem&&); \
	NO_API UOneEngineSDKPSSubsystem(const UOneEngineSDKPSSubsystem&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UOneEngineSDKPSSubsystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UOneEngineSDKPSSubsystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UOneEngineSDKPSSubsystem)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_PRIVATE_PROPERTY_OFFSET
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_485_PROLOG
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_RPC_WRAPPERS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_INCLASS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_RPC_WRAPPERS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_INCLASS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h_488_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ONEENGINESDK_API UClass* StaticClass<class UOneEngineSDKPSSubsystem>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKPSSubsystem_h


#define FOREACH_ENUM_EONEPSNACCOUNTSTATE(op) \
	op(EOnePsnAccountState::Unknown) \
	op(EOnePsnAccountState::SignedOut) \
	op(EOnePsnAccountState::SignedIn) 

enum class EOnePsnAccountState : uint8;
template<> ONEENGINESDK_API UEnum* StaticEnum<EOnePsnAccountState>();

#define FOREACH_ENUM_EONEPSSTOREICONPOS(op) \
	op(EOnePSStoreIconPos::Center) \
	op(EOnePSStoreIconPos::Left) \
	op(EOnePSStoreIconPos::Right) 

enum class EOnePSStoreIconPos : uint8;
template<> ONEENGINESDK_API UEnum* StaticEnum<EOnePSStoreIconPos>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
