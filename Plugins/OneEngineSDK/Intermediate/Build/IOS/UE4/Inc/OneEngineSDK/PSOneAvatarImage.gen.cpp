// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneAvatarImage.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneAvatarImage() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneAvatarImage_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneAvatarImage();
	UMG_API UClass* Z_Construct_UClass_UUserWidget();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ENGINE_API UClass* Z_Construct_UClass_UTexture2DDynamic_NoRegister();
	ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UAsyncTaskDownloadImage_NoRegister();
// End Cross Module References
	DEFINE_FUNCTION(UPSOneAvatarImage::execHandleTextureDownloaded)
	{
		P_GET_OBJECT(UTexture2DDynamic,Z_Param_Texture);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->HandleTextureDownloaded(Z_Param_Texture);
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneAvatarImage::execSetImageURL)
	{
		P_GET_PROPERTY(FStrProperty,Z_Param_URL);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->SetImageURL(Z_Param_URL);
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneAvatarImage::execUpdateImage)
	{
		P_GET_OBJECT(UTexture2D,Z_Param_Texture);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->UpdateImage(Z_Param_Texture);
		P_NATIVE_END;
	}
	void UPSOneAvatarImage::StaticRegisterNativesUPSOneAvatarImage()
	{
		UClass* Class = UPSOneAvatarImage::StaticClass();
		static const FNameNativePtrPair Funcs[] = {
			{ "HandleTextureDownloaded", &UPSOneAvatarImage::execHandleTextureDownloaded },
			{ "SetImageURL", &UPSOneAvatarImage::execSetImageURL },
			{ "UpdateImage", &UPSOneAvatarImage::execUpdateImage },
		};
		FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
	}
	struct Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics
	{
		struct PSOneAvatarImage_eventHandleTextureDownloaded_Parms
		{
			UTexture2DDynamic* Texture;
		};
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_Texture;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::NewProp_Texture = { "Texture", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneAvatarImage_eventHandleTextureDownloaded_Parms, Texture), Z_Construct_UClass_UTexture2DDynamic_NoRegister, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::NewProp_Texture,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneAvatarImage.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneAvatarImage, nullptr, "HandleTextureDownloaded", nullptr, nullptr, sizeof(PSOneAvatarImage_eventHandleTextureDownloaded_Parms), Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics
	{
		struct PSOneAvatarImage_eventSetImageURL_Parms
		{
			FString URL;
		};
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_URL_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_URL;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::NewProp_URL_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::NewProp_URL = { "URL", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneAvatarImage_eventSetImageURL_Parms, URL), METADATA_PARAMS(Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::NewProp_URL_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::NewProp_URL_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::NewProp_URL,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneAvatarImage.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneAvatarImage, nullptr, "SetImageURL", nullptr, nullptr, sizeof(PSOneAvatarImage_eventSetImageURL_Parms), Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics
	{
		struct PSOneAvatarImage_eventUpdateImage_Parms
		{
			UTexture2D* Texture;
		};
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_Texture;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::NewProp_Texture = { "Texture", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneAvatarImage_eventUpdateImage_Parms, Texture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::NewProp_Texture,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneAvatarImage.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneAvatarImage, nullptr, "UpdateImage", nullptr, nullptr, sizeof(PSOneAvatarImage_eventUpdateImage_Parms), Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	UClass* Z_Construct_UClass_UPSOneAvatarImage_NoRegister()
	{
		return UPSOneAvatarImage::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneAvatarImage_Statics
	{
		static UObject* (*const DependentSingletons[])();
		static const FClassFunctionLinkInfo FuncInfo[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Image_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_Image;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ImageView_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ImageView;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DownloadImageTask_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_DownloadImageTask;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneAvatarImage_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
	const FClassFunctionLinkInfo Z_Construct_UClass_UPSOneAvatarImage_Statics::FuncInfo[] = {
		{ &Z_Construct_UFunction_UPSOneAvatarImage_HandleTextureDownloaded, "HandleTextureDownloaded" }, // 1331788499
		{ &Z_Construct_UFunction_UPSOneAvatarImage_SetImageURL, "SetImageURL" }, // 2035073886
		{ &Z_Construct_UFunction_UPSOneAvatarImage_UpdateImage, "UpdateImage" }, // 4165309545
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneAvatarImage_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n *\n */" },
		{ "IncludePath", "Views/PSOneAvatarImage.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneAvatarImage.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_Image_MetaData[] = {
		{ "Category", "PSOneAvatarImage" },
		{ "ModuleRelativePath", "Private/Views/PSOneAvatarImage.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_Image = { "Image", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneAvatarImage, Image), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_Image_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_Image_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_ImageView_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneAvatarImage.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_ImageView = { "ImageView", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneAvatarImage, ImageView), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_ImageView_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_ImageView_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_DownloadImageTask_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneAvatarImage.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_DownloadImageTask = { "DownloadImageTask", nullptr, (EPropertyFlags)0x0040000000000000, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneAvatarImage, DownloadImageTask), Z_Construct_UClass_UAsyncTaskDownloadImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_DownloadImageTask_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_DownloadImageTask_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneAvatarImage_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_Image,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_ImageView,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneAvatarImage_Statics::NewProp_DownloadImageTask,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneAvatarImage_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneAvatarImage>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneAvatarImage_Statics::ClassParams = {
		&UPSOneAvatarImage::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		FuncInfo,
		Z_Construct_UClass_UPSOneAvatarImage_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		UE_ARRAY_COUNT(FuncInfo),
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneAvatarImage_Statics::PropPointers),
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneAvatarImage_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneAvatarImage_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneAvatarImage()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneAvatarImage_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneAvatarImage, 2530336712);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneAvatarImage>()
	{
		return UPSOneAvatarImage::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneAvatarImage(Z_Construct_UClass_UPSOneAvatarImage, &UPSOneAvatarImage::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneAvatarImage"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneAvatarImage);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
