// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneUserCenterRightCellSubtitle.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneUserCenterRightCellSubtitle() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
	UMG_API UClass* Z_Construct_UClass_USizeBox_NoRegister();
	COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
	UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
// End Cross Module References
	void UPSOneUserCenterRightCellSubtitle::StaticRegisterNativesUPSOneUserCenterRightCellSubtitle()
	{
	}
	UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister()
	{
		return UPSOneUserCenterRightCellSubtitle::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Icon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_Icon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_IconImage_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_IconImage;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ImageBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ImageBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SubTitle_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_SubTitle;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SubTitleColor_MetaData[];
#endif
		static const UE4CodeGen_Private::FStructPropertyParams NewProp_SubTitleColor;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_HiddenArrow_MetaData[];
#endif
		static void NewProp_HiddenArrow_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_HiddenArrow;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SubTextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_SubTextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ArrowImage_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ArrowImage;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneButtonBase,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n *  \xe7\x9b\xae\xe5\x89\x8d\xe7\x94\xa8\xe4\xba\x8e\xe4\xb8\xaa\xe4\xba\xba\xe4\xbf\xa1\xe6\x81\xaf\xe5\xb1\x95\xe7\xa4\xba\n */" },
		{ "IncludePath", "Views/PSOneUserCenterRightCellSubtitle.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
		{ "ToolTip", "\xe7\x9b\xae\xe5\x89\x8d\xe7\x94\xa8\xe4\xba\x8e\xe4\xb8\xaa\xe4\xba\xba\xe4\xbf\xa1\xe6\x81\xaf\xe5\xb1\x95\xe7\xa4\xba" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_Icon_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_Icon = { "Icon", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle, Icon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_Icon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_Icon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_IconImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_IconImage = { "IconImage", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle, IconImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_IconImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_IconImage_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_ImageBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_ImageBox = { "ImageBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle, ImageBox), Z_Construct_UClass_USizeBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_ImageBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_ImageBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTitle_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTitle = { "SubTitle", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle, SubTitle), METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTitle_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTitle_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTitleColor_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle" },
		{ "Comment", "// SubTitle Color, 2B2B2BFF\n" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
		{ "ToolTip", "SubTitle Color, 2B2B2BFF" },
	};
#endif
	const UE4CodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTitleColor = { "SubTitleColor", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle, SubTitleColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTitleColor_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTitleColor_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_HiddenArrow_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellSubtitle" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
	};
#endif
	void Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_HiddenArrow_SetBit(void* Obj)
	{
		((UPSOneUserCenterRightCellSubtitle*)Obj)->HiddenArrow = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_HiddenArrow = { "HiddenArrow", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UPSOneUserCenterRightCellSubtitle), &Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_HiddenArrow_SetBit, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_HiddenArrow_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_HiddenArrow_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTextBlock = { "SubTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle, SubTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_ArrowImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellSubtitle.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_ArrowImage = { "ArrowImage", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellSubtitle, ArrowImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_ArrowImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_ArrowImage_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_Icon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_IconImage,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_ImageBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTitle,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTitleColor,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_HiddenArrow,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_SubTextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::NewProp_ArrowImage,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterRightCellSubtitle>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::ClassParams = {
		&UPSOneUserCenterRightCellSubtitle::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::PropPointers),
		0,
		0x00A010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneUserCenterRightCellSubtitle, 1168416075);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneUserCenterRightCellSubtitle>()
	{
		return UPSOneUserCenterRightCellSubtitle::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneUserCenterRightCellSubtitle(Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle, &UPSOneUserCenterRightCellSubtitle::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneUserCenterRightCellSubtitle"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterRightCellSubtitle);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
