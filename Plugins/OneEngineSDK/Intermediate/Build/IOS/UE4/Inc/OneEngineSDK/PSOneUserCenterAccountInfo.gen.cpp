// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneUserCenterAccountInfo.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneUserCenterAccountInfo() {}
// Cross Module References
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FPSOneAccountInfoStruct();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterAccountInfo_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterAccountInfo();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_NoRegister();
// End Cross Module References
class UScriptStruct* FPSOneAccountInfoStruct::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("PSOneAccountInfoStruct"), sizeof(FPSOneAccountInfoStruct), Get_Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FPSOneAccountInfoStruct>()
{
	return FPSOneAccountInfoStruct::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FPSOneAccountInfoStruct(FPSOneAccountInfoStruct::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("PSOneAccountInfoStruct"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFPSOneAccountInfoStruct
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFPSOneAccountInfoStruct()
	{
		UScriptStruct::DeferCppStructOps<FPSOneAccountInfoStruct>(FName(TEXT("PSOneAccountInfoStruct")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFPSOneAccountInfoStruct;
	struct Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ImageURL_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ImageURL;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Nick_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_Nick;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Mobile_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_Mobile;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Email_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_Email;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bHasPwd_MetaData[];
#endif
		static void NewProp_bHasPwd_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bHasPwd;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::Struct_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPSOneAccountInfoStruct>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_ImageURL_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_ImageURL = { "ImageURL", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneAccountInfoStruct, ImageURL), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_ImageURL_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_ImageURL_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Nick_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Nick = { "Nick", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneAccountInfoStruct, Nick), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Nick_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Nick_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Mobile_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Mobile = { "Mobile", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneAccountInfoStruct, Mobile), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Mobile_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Mobile_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Email_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Email = { "Email", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneAccountInfoStruct, Email), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Email_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Email_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_bHasPwd_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
	};
#endif
	void Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_bHasPwd_SetBit(void* Obj)
	{
		((FPSOneAccountInfoStruct*)Obj)->bHasPwd = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_bHasPwd = { "bHasPwd", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(FPSOneAccountInfoStruct), &Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_bHasPwd_SetBit, METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_bHasPwd_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_bHasPwd_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_ImageURL,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Nick,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Mobile,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_Email,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::NewProp_bHasPwd,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"PSOneAccountInfoStruct",
		sizeof(FPSOneAccountInfoStruct),
		alignof(FPSOneAccountInfoStruct),
		Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FPSOneAccountInfoStruct()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("PSOneAccountInfoStruct"), sizeof(FPSOneAccountInfoStruct), Get_Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FPSOneAccountInfoStruct_Hash() { return 1775249806U; }
	void UPSOneUserCenterAccountInfo::StaticRegisterNativesUPSOneUserCenterAccountInfo()
	{
	}
	UClass* Z_Construct_UClass_UPSOneUserCenterAccountInfo_NoRegister()
	{
		return UPSOneUserCenterAccountInfo::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AvatarCell_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_AvatarCell;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NickCell_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_NickCell;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_MobileCell_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_MobileCell;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_EmailCell_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_EmailCell;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CancellationCell_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_CancellationCell;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ChangePasswordCell_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ChangePasswordCell;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FImplementedInterfaceParams InterfaceParams[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::Class_MetaDataParams[] = {
		{ "IncludePath", "Views/PSOneUserCenterAccountInfo.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_AvatarCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// AvatarCell\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
		{ "ToolTip", "AvatarCell" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_AvatarCell = { "AvatarCell", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterAccountInfo, AvatarCell), Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_AvatarCell_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_AvatarCell_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_NickCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// NickCell\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
		{ "ToolTip", "NickCell" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_NickCell = { "NickCell", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterAccountInfo, NickCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_NickCell_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_NickCell_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_MobileCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// MobileCell\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
		{ "ToolTip", "MobileCell" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_MobileCell = { "MobileCell", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterAccountInfo, MobileCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_MobileCell_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_MobileCell_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_EmailCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// EmailCell\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
		{ "ToolTip", "EmailCell" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_EmailCell = { "EmailCell", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterAccountInfo, EmailCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_EmailCell_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_EmailCell_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_CancellationCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// CancellationCell\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
		{ "ToolTip", "CancellationCell" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_CancellationCell = { "CancellationCell", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterAccountInfo, CancellationCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_CancellationCell_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_CancellationCell_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_ChangePasswordCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "//  ChangePasswordCell\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterAccountInfo.h" },
		{ "ToolTip", "ChangePasswordCell" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_ChangePasswordCell = { "ChangePasswordCell", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterAccountInfo, ChangePasswordCell), Z_Construct_UClass_UPSOneUserCenterRightCellSubtitle_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_ChangePasswordCell_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_ChangePasswordCell_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_AvatarCell,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_NickCell,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_MobileCell,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_EmailCell,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_CancellationCell,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::NewProp_ChangePasswordCell,
	};
		const UE4CodeGen_Private::FImplementedInterfaceParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::InterfaceParams[] = {
			{ Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_NoRegister, (int32)VTABLE_OFFSET(UPSOneUserCenterAccountInfo, IPSOneSaveFocusWidgetInterface), false },
		};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterAccountInfo>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::ClassParams = {
		&UPSOneUserCenterAccountInfo::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::PropPointers,
		InterfaceParams,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::PropPointers),
		UE_ARRAY_COUNT(InterfaceParams),
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneUserCenterAccountInfo()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneUserCenterAccountInfo_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneUserCenterAccountInfo, **********);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneUserCenterAccountInfo>()
	{
		return UPSOneUserCenterAccountInfo::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneUserCenterAccountInfo(Z_Construct_UClass_UPSOneUserCenterAccountInfo, &UPSOneUserCenterAccountInfo::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneUserCenterAccountInfo"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterAccountInfo);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
