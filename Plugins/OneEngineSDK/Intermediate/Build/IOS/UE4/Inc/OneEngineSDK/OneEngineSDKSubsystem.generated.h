// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
enum class EOneUnlockSafeLockResult : uint8;
enum class EOneUnlockSafeLockType : uint8;
struct FUserIpInfo;
enum class EOneEngineThirdType : uint8;
struct FOneUserInfo;
struct FOnePushNotDisturbInfo;
struct FOnePushTypeInfo;
struct FOnePushStatus;
struct FOnePushMessage;
struct FOneActiveQualificationInfo;
struct FOneURCRoleInfo;
struct FOneUserLocationInfo;
enum class EOnePermissionType : uint8;
struct FOneAntiAddictionInfo;
struct FOneDeviceInfo;
struct FOneProductInfo;
enum class EOneNaverGameType : uint8;
enum class EOneScreenOrientation : uint8;
enum class EOneAIHelpType : uint8;
enum class EOneEngineSDKRegionType : uint8;
enum class EOneShareAppTarget : uint8;
enum class EOneShareType : uint8;
struct FOneShareData;
 
struct FOnePermissionInfo;
enum class EOneResEventState : uint8;
struct FOneRoleInfo;
struct FOnePaymentInfo;
#ifdef ONEENGINESDK_OneEngineSDKSubsystem_generated_h
#error "OneEngineSDKSubsystem.generated.h already included, missing '#pragma once' in OneEngineSDKSubsystem.h"
#endif
#define ONEENGINESDK_OneEngineSDKSubsystem_generated_h

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_536_DELEGATE \
struct OneEngineSDKSubsystem_eventOneInGameMenuDelegate_Parms \
{ \
	FString InputString; \
}; \
static inline void FOneInGameMenuDelegate_DelegateWrapper(const FScriptDelegate& OneInGameMenuDelegate, const FString& InputString) \
{ \
	OneEngineSDKSubsystem_eventOneInGameMenuDelegate_Parms Parms; \
	Parms.InputString=InputString; \
	OneInGameMenuDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_535_DELEGATE \
static inline void FOneUnloadDelegate_DelegateWrapper(const FScriptDelegate& OneUnloadDelegate) \
{ \
	OneUnloadDelegate.ProcessDelegate<UObject>(NULL); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_534_DELEGATE \
static inline void FOneLoadDelegate_DelegateWrapper(const FScriptDelegate& OneLoadDelegate) \
{ \
	OneLoadDelegate.ProcessDelegate<UObject>(NULL); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_522_DELEGATE \
struct OneEngineSDKSubsystem_eventOneUnlockSafeLockResultDelegate_Parms \
{ \
	EOneUnlockSafeLockResult UnlockResult; \
	FString UnlockToken; \
	int32 Code; \
	FString ErrorMsg; \
	EOneUnlockSafeLockType UnlockType; \
}; \
static inline void FOneUnlockSafeLockResultDelegate_DelegateWrapper(const FScriptDelegate& OneUnlockSafeLockResultDelegate, EOneUnlockSafeLockResult UnlockResult, const FString& UnlockToken, int32 Code, const FString& ErrorMsg, EOneUnlockSafeLockType UnlockType) \
{ \
	OneEngineSDKSubsystem_eventOneUnlockSafeLockResultDelegate_Parms Parms; \
	Parms.UnlockResult=UnlockResult; \
	Parms.UnlockToken=UnlockToken; \
	Parms.Code=Code; \
	Parms.ErrorMsg=ErrorMsg; \
	Parms.UnlockType=UnlockType; \
	OneUnlockSafeLockResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_490_DELEGATE \
struct OneEngineSDKSubsystem_eventOnGetIpInfoResultDelegate_Parms \
{ \
	bool bSuccess; \
	FUserIpInfo IpInfo; \
	int32 Code; \
	FString Msg; \
}; \
static inline void FOnGetIpInfoResultDelegate_DelegateWrapper(const FScriptDelegate& OnGetIpInfoResultDelegate, bool bSuccess, FUserIpInfo IpInfo, int32 Code, const FString& Msg) \
{ \
	OneEngineSDKSubsystem_eventOnGetIpInfoResultDelegate_Parms Parms; \
	Parms.bSuccess=bSuccess ? true : false; \
	Parms.IpInfo=IpInfo; \
	Parms.Code=Code; \
	Parms.Msg=Msg; \
	OnGetIpInfoResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_475_DELEGATE \
struct OneEngineSDKSubsystem_eventOneOnGetClientPacket_Parms \
{ \
	TArray<uint8> data; \
}; \
static inline void FOneOnGetClientPacket_DelegateWrapper(const FScriptDelegate& OneOnGetClientPacket, TArray<uint8> const& data) \
{ \
	OneEngineSDKSubsystem_eventOneOnGetClientPacket_Parms Parms; \
	Parms.data=data; \
	OneOnGetClientPacket.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_467_DELEGATE \
static inline void FOnExitDelegate_DelegateWrapper(const FScriptDelegate& OnExitDelegate) \
{ \
	OnExitDelegate.ProcessDelegate<UObject>(NULL); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_463_DELEGATE \
static inline void FOnChannelNotHavingExitViewDelegate_DelegateWrapper(const FScriptDelegate& OnChannelNotHavingExitViewDelegate) \
{ \
	OnChannelNotHavingExitViewDelegate.ProcessDelegate<UObject>(NULL); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_458_DELEGATE \
struct OneEngineSDKSubsystem_eventOneCommonFunctionDelegate_Parms \
{ \
	FString FunctionName; \
	int32 Result; \
	FString Msg; \
}; \
static inline void FOneCommonFunctionDelegate_DelegateWrapper(const FScriptDelegate& OneCommonFunctionDelegate, const FString& FunctionName, int32 Result, const FString& Msg) \
{ \
	OneEngineSDKSubsystem_eventOneCommonFunctionDelegate_Parms Parms; \
	Parms.FunctionName=FunctionName; \
	Parms.Result=Result; \
	Parms.Msg=Msg; \
	OneCommonFunctionDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_428_DELEGATE \
struct OneEngineSDKSubsystem_eventOneTranslateResultDelegate_Parms \
{ \
	bool bSucceed; \
	FString Result; \
	FString ErrorMsg; \
}; \
static inline void FOneTranslateResultDelegate_DelegateWrapper(const FScriptDelegate& OneTranslateResultDelegate, bool bSucceed, const FString& Result, const FString& ErrorMsg) \
{ \
	OneEngineSDKSubsystem_eventOneTranslateResultDelegate_Parms Parms; \
	Parms.bSucceed=bSucceed ? true : false; \
	Parms.Result=Result; \
	Parms.ErrorMsg=ErrorMsg; \
	OneTranslateResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_415_DELEGATE \
struct OneEngineSDKSubsystem_eventOneUserAuthenticationResultDelegate_Parms \
{ \
	bool bSucceed; \
	int32 AuthResult; \
	bool bHasNetError; \
	FString ErrorMsg; \
}; \
static inline void FOneUserAuthenticationResultDelegate_DelegateWrapper(const FScriptDelegate& OneUserAuthenticationResultDelegate, bool bSucceed, int32 AuthResult, bool bHasNetError, const FString& ErrorMsg) \
{ \
	OneEngineSDKSubsystem_eventOneUserAuthenticationResultDelegate_Parms Parms; \
	Parms.bSucceed=bSucceed ? true : false; \
	Parms.AuthResult=AuthResult; \
	Parms.bHasNetError=bHasNetError ? true : false; \
	Parms.ErrorMsg=ErrorMsg; \
	OneUserAuthenticationResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_409_DELEGATE \
struct OneEngineSDKSubsystem_eventOneBindResultDelegate_Parms \
{ \
	bool bSuccess; \
	int32 Code; \
	FString Msg; \
	EOneEngineThirdType BindType; \
	FOneUserInfo UserInfo; \
}; \
static inline void FOneBindResultDelegate_DelegateWrapper(const FScriptDelegate& OneBindResultDelegate, bool bSuccess, int32 Code, const FString& Msg, EOneEngineThirdType BindType, FOneUserInfo const& UserInfo) \
{ \
	OneEngineSDKSubsystem_eventOneBindResultDelegate_Parms Parms; \
	Parms.bSuccess=bSuccess ? true : false; \
	Parms.Code=Code; \
	Parms.Msg=Msg; \
	Parms.BindType=BindType; \
	Parms.UserInfo=UserInfo; \
	OneBindResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_368_DELEGATE \
struct OneEngineSDKSubsystem_eventOnePushNotDisturbInfoDelegate_Parms \
{ \
	bool bSucceed; \
	int32 Code; \
	FString ErrorMsg; \
	FOnePushNotDisturbInfo DisturbInfo; \
}; \
static inline void FOnePushNotDisturbInfoDelegate_DelegateWrapper(const FScriptDelegate& OnePushNotDisturbInfoDelegate, bool bSucceed, int32 Code, const FString& ErrorMsg, FOnePushNotDisturbInfo const& DisturbInfo) \
{ \
	OneEngineSDKSubsystem_eventOnePushNotDisturbInfoDelegate_Parms Parms; \
	Parms.bSucceed=bSucceed ? true : false; \
	Parms.Code=Code; \
	Parms.ErrorMsg=ErrorMsg; \
	Parms.DisturbInfo=DisturbInfo; \
	OnePushNotDisturbInfoDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_358_DELEGATE \
struct OneEngineSDKSubsystem_eventOneGetPushTypeInfoListDelegate_Parms \
{ \
	bool bSucceed; \
	int32 Code; \
	FString ErrorMsg; \
	TArray<FOnePushTypeInfo> PushTasks; \
}; \
static inline void FOneGetPushTypeInfoListDelegate_DelegateWrapper(const FScriptDelegate& OneGetPushTypeInfoListDelegate, bool bSucceed, int32 Code, const FString& ErrorMsg, TArray<FOnePushTypeInfo> const& PushTasks) \
{ \
	OneEngineSDKSubsystem_eventOneGetPushTypeInfoListDelegate_Parms Parms; \
	Parms.bSucceed=bSucceed ? true : false; \
	Parms.Code=Code; \
	Parms.ErrorMsg=ErrorMsg; \
	Parms.PushTasks=PushTasks; \
	OneGetPushTypeInfoListDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_339_DELEGATE \
struct OneEngineSDKSubsystem_eventOnGetPushStateDelegate_Parms \
{ \
	bool bSucceed; \
	int32 Code; \
	FString ErrorMsg; \
	bool bEnable; \
}; \
static inline void FOnGetPushStateDelegate_DelegateWrapper(const FScriptDelegate& OnGetPushStateDelegate, bool bSucceed, int32 Code, const FString& ErrorMsg, bool bEnable) \
{ \
	OneEngineSDKSubsystem_eventOnGetPushStateDelegate_Parms Parms; \
	Parms.bSucceed=bSucceed ? true : false; \
	Parms.Code=Code; \
	Parms.ErrorMsg=ErrorMsg; \
	Parms.bEnable=bEnable ? true : false; \
	OnGetPushStateDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_330_DELEGATE \
struct OneEngineSDKSubsystem_eventOnePushStatusDelegate_Parms \
{ \
	FOnePushStatus PushStatus; \
}; \
static inline void FOnePushStatusDelegate_DelegateWrapper(const FScriptDelegate& OnePushStatusDelegate, FOnePushStatus PushStatus) \
{ \
	OneEngineSDKSubsystem_eventOnePushStatusDelegate_Parms Parms; \
	Parms.PushStatus=PushStatus; \
	OnePushStatusDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_324_DELEGATE \
struct OneEngineSDKSubsystem_eventOneNotificationDelegate_Parms \
{ \
	FOnePushMessage Message; \
}; \
static inline void FOneNotificationDelegate_DelegateWrapper(const FScriptDelegate& OneNotificationDelegate, FOnePushMessage const& Message) \
{ \
	OneEngineSDKSubsystem_eventOneNotificationDelegate_Parms Parms; \
	Parms.Message=Message; \
	OneNotificationDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_319_DELEGATE \
struct OneEngineSDKSubsystem_eventOneStartUpdatePushDataDelegate_Parms \
{ \
	bool bSucceed; \
	int32 Code; \
	FString ErrorMsg; \
	FString DeviceToken; \
}; \
static inline void FOneStartUpdatePushDataDelegate_DelegateWrapper(const FScriptDelegate& OneStartUpdatePushDataDelegate, bool bSucceed, int32 Code, const FString& ErrorMsg, const FString& DeviceToken) \
{ \
	OneEngineSDKSubsystem_eventOneStartUpdatePushDataDelegate_Parms Parms; \
	Parms.bSucceed=bSucceed ? true : false; \
	Parms.Code=Code; \
	Parms.ErrorMsg=ErrorMsg; \
	Parms.DeviceToken=DeviceToken; \
	OneStartUpdatePushDataDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_302_DELEGATE \
struct OneEngineSDKSubsystem_eventOneActivateDeviceResultDelegate_Parms \
{ \
	bool bSucceed; \
	bool bNeedActCode; \
	FString ActCodePrompt; \
	int32 Code; \
	FString ErrorMsg; \
}; \
static inline void FOneActivateDeviceResultDelegate_DelegateWrapper(const FScriptDelegate& OneActivateDeviceResultDelegate, bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg) \
{ \
	OneEngineSDKSubsystem_eventOneActivateDeviceResultDelegate_Parms Parms; \
	Parms.bSucceed=bSucceed ? true : false; \
	Parms.bNeedActCode=bNeedActCode ? true : false; \
	Parms.ActCodePrompt=ActCodePrompt; \
	Parms.Code=Code; \
	Parms.ErrorMsg=ErrorMsg; \
	OneActivateDeviceResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_295_DELEGATE \
struct OneEngineSDKSubsystem_eventOneQueryUserActiveQualificationResultDelegate_Parms \
{ \
	bool bSucceed; \
	int32 Code; \
	FString ErrorMsg; \
	FOneActiveQualificationInfo QualificationInfo; \
}; \
static inline void FOneQueryUserActiveQualificationResultDelegate_DelegateWrapper(const FScriptDelegate& OneQueryUserActiveQualificationResultDelegate, bool bSucceed, int32 Code, const FString& ErrorMsg, FOneActiveQualificationInfo const& QualificationInfo) \
{ \
	OneEngineSDKSubsystem_eventOneQueryUserActiveQualificationResultDelegate_Parms Parms; \
	Parms.bSucceed=bSucceed ? true : false; \
	Parms.Code=Code; \
	Parms.ErrorMsg=ErrorMsg; \
	Parms.QualificationInfo=QualificationInfo; \
	OneQueryUserActiveQualificationResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_281_DELEGATE \
struct OneEngineSDKSubsystem_eventOneQueryActCodeResultDelegate_Parms \
{ \
	bool bSucceed; \
	bool bNeedActCode; \
	FString ActCodePrompt; \
	int32 Code; \
	FString ErrorMsg; \
}; \
static inline void FOneQueryActCodeResultDelegate_DelegateWrapper(const FScriptDelegate& OneQueryActCodeResultDelegate, bool bSucceed, bool bNeedActCode, const FString& ActCodePrompt, int32 Code, const FString& ErrorMsg) \
{ \
	OneEngineSDKSubsystem_eventOneQueryActCodeResultDelegate_Parms Parms; \
	Parms.bSucceed=bSucceed ? true : false; \
	Parms.bNeedActCode=bNeedActCode ? true : false; \
	Parms.ActCodePrompt=ActCodePrompt; \
	Parms.Code=Code; \
	Parms.ErrorMsg=ErrorMsg; \
	OneQueryActCodeResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_265_DELEGATE \
struct OneEngineSDKSubsystem_eventOneFetchUserRoleInfoListDelegate_Parms \
{ \
	bool bSuccess; \
	int32 Code; \
	FString Msg; \
	TArray<FOneURCRoleInfo> RoleList; \
}; \
static inline void FOneFetchUserRoleInfoListDelegate_DelegateWrapper(const FScriptDelegate& OneFetchUserRoleInfoListDelegate, bool bSuccess, int32 Code, const FString& Msg, TArray<FOneURCRoleInfo> const& RoleList) \
{ \
	OneEngineSDKSubsystem_eventOneFetchUserRoleInfoListDelegate_Parms Parms; \
	Parms.bSuccess=bSuccess ? true : false; \
	Parms.Code=Code; \
	Parms.Msg=Msg; \
	Parms.RoleList=RoleList; \
	OneFetchUserRoleInfoListDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_258_DELEGATE \
struct OneEngineSDKSubsystem_eventOneUserLocationInfoDelegate_Parms \
{ \
	FOneUserLocationInfo LocationInfo; \
}; \
static inline void FOneUserLocationInfoDelegate_DelegateWrapper(const FScriptDelegate& OneUserLocationInfoDelegate, FOneUserLocationInfo const& LocationInfo) \
{ \
	OneEngineSDKSubsystem_eventOneUserLocationInfoDelegate_Parms Parms; \
	Parms.LocationInfo=LocationInfo; \
	OneUserLocationInfoDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_241_DELEGATE \
struct OneEngineSDKSubsystem_eventOneLogoutResultDelegate_Parms \
{ \
	bool bSuccess; \
	int32 Code; \
	FString Msg; \
}; \
static inline void FOneLogoutResultDelegate_DelegateWrapper(const FMulticastScriptDelegate& OneLogoutResultDelegate, bool bSuccess, int32 Code, const FString& Msg) \
{ \
	OneEngineSDKSubsystem_eventOneLogoutResultDelegate_Parms Parms; \
	Parms.bSuccess=bSuccess ? true : false; \
	Parms.Code=Code; \
	Parms.Msg=Msg; \
	OneLogoutResultDelegate.ProcessMulticastDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_222_DELEGATE \
struct OneEngineSDKSubsystem_eventOneRequestPermissionResultDelegate_Parms \
{ \
	EOnePermissionType Type; \
	bool Granted; \
}; \
static inline void FOneRequestPermissionResultDelegate_DelegateWrapper(const FScriptDelegate& OneRequestPermissionResultDelegate, EOnePermissionType Type, bool Granted) \
{ \
	OneEngineSDKSubsystem_eventOneRequestPermissionResultDelegate_Parms Parms; \
	Parms.Type=Type; \
	Parms.Granted=Granted ? true : false; \
	OneRequestPermissionResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_192_DELEGATE \
struct OneEngineSDKSubsystem_eventOneAntiAddictionTimeoutDelegate_Parms \
{ \
	bool bForceKick; \
	FOneAntiAddictionInfo Info; \
}; \
static inline void FOneAntiAddictionTimeoutDelegate_DelegateWrapper(const FMulticastScriptDelegate& OneAntiAddictionTimeoutDelegate, bool bForceKick, FOneAntiAddictionInfo const& Info) \
{ \
	OneEngineSDKSubsystem_eventOneAntiAddictionTimeoutDelegate_Parms Parms; \
	Parms.bForceKick=bForceKick ? true : false; \
	Parms.Info=Info; \
	OneAntiAddictionTimeoutDelegate.ProcessMulticastDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_187_DELEGATE \
struct OneEngineSDKSubsystem_eventOneFetchAntiAddictionInfoDelegate_Parms \
{ \
	FOneAntiAddictionInfo Info; \
}; \
static inline void FOneFetchAntiAddictionInfoDelegate_DelegateWrapper(const FScriptDelegate& OneFetchAntiAddictionInfoDelegate, FOneAntiAddictionInfo const& Info) \
{ \
	OneEngineSDKSubsystem_eventOneFetchAntiAddictionInfoDelegate_Parms Parms; \
	Parms.Info=Info; \
	OneFetchAntiAddictionInfoDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_123_DELEGATE \
struct OneEngineSDKSubsystem_eventOneGetDeviceInfoDelegate_Parms \
{ \
	FOneDeviceInfo DeviceInfo; \
}; \
static inline void FOneGetDeviceInfoDelegate_DelegateWrapper(const FScriptDelegate& OneGetDeviceInfoDelegate, FOneDeviceInfo const& DeviceInfo) \
{ \
	OneEngineSDKSubsystem_eventOneGetDeviceInfoDelegate_Parms Parms; \
	Parms.DeviceInfo=DeviceInfo; \
	OneGetDeviceInfoDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_108_DELEGATE \
struct OneEngineSDKSubsystem_eventOneGetPlatformDelegate_Parms \
{ \
	bool bSuccess; \
	int32 Code; \
	FString Msg; \
	FString Platform; \
}; \
static inline void FOneGetPlatformDelegate_DelegateWrapper(const FScriptDelegate& OneGetPlatformDelegate, bool bSuccess, int32 Code, const FString& Msg, const FString& Platform) \
{ \
	OneEngineSDKSubsystem_eventOneGetPlatformDelegate_Parms Parms; \
	Parms.bSuccess=bSuccess ? true : false; \
	Parms.Code=Code; \
	Parms.Msg=Msg; \
	Parms.Platform=Platform; \
	OneGetPlatformDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_95_DELEGATE \
struct OneEngineSDKSubsystem_eventOneProductInfoDelegate_Parms \
{ \
	bool bSucceed; \
	int32 Code; \
	TArray<FOneProductInfo> ProductListResult; \
}; \
static inline void FOneProductInfoDelegate_DelegateWrapper(const FScriptDelegate& OneProductInfoDelegate, bool bSucceed, int32 Code, TArray<FOneProductInfo> const& ProductListResult) \
{ \
	OneEngineSDKSubsystem_eventOneProductInfoDelegate_Parms Parms; \
	Parms.bSucceed=bSucceed ? true : false; \
	Parms.Code=Code; \
	Parms.ProductListResult=ProductListResult; \
	OneProductInfoDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_87_DELEGATE \
struct OneEngineSDKSubsystem_eventOnePayResultDelegate_Parms \
{ \
	bool bSuccess; \
	int32 Code; \
	FString Msg; \
	FString OrderId; \
}; \
static inline void FOnePayResultDelegate_DelegateWrapper(const FMulticastScriptDelegate& OnePayResultDelegate, bool bSuccess, int32 Code, const FString& Msg, const FString& OrderId) \
{ \
	OneEngineSDKSubsystem_eventOnePayResultDelegate_Parms Parms; \
	Parms.bSuccess=bSuccess ? true : false; \
	Parms.Code=Code; \
	Parms.Msg=Msg; \
	Parms.OrderId=OrderId; \
	OnePayResultDelegate.ProcessMulticastDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_65_DELEGATE \
struct OneEngineSDKSubsystem_eventOneFetchUserTokenListDelegate_Parms \
{ \
	bool bSuccess; \
	int32 Code; \
	FString Msg; \
	TArray<FOneUserInfo> TokenList; \
}; \
static inline void FOneFetchUserTokenListDelegate_DelegateWrapper(const FScriptDelegate& OneFetchUserTokenListDelegate, bool bSuccess, int32 Code, const FString& Msg, TArray<FOneUserInfo> const& TokenList) \
{ \
	OneEngineSDKSubsystem_eventOneFetchUserTokenListDelegate_Parms Parms; \
	Parms.bSuccess=bSuccess ? true : false; \
	Parms.Code=Code; \
	Parms.Msg=Msg; \
	Parms.TokenList=TokenList; \
	OneFetchUserTokenListDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_56_DELEGATE \
struct OneEngineSDKSubsystem_eventOneGetQRCodeScanResultDelegate_Parms \
{ \
	FString CodeType; \
	FString CodeLink; \
}; \
static inline void FOneGetQRCodeScanResultDelegate_DelegateWrapper(const FScriptDelegate& OneGetQRCodeScanResultDelegate, const FString& CodeType, const FString& CodeLink) \
{ \
	OneEngineSDKSubsystem_eventOneGetQRCodeScanResultDelegate_Parms Parms; \
	Parms.CodeType=CodeType; \
	Parms.CodeLink=CodeLink; \
	OneGetQRCodeScanResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_48_DELEGATE \
struct OneEngineSDKSubsystem_eventOneLoginResultDelegate_Parms \
{ \
	bool bSuccess; \
	int32 Code; \
	FString Msg; \
	FOneUserInfo UserInfo; \
}; \
static inline void FOneLoginResultDelegate_DelegateWrapper(const FMulticastScriptDelegate& OneLoginResultDelegate, bool bSuccess, int32 Code, const FString& Msg, FOneUserInfo const& UserInfo) \
{ \
	OneEngineSDKSubsystem_eventOneLoginResultDelegate_Parms Parms; \
	Parms.bSuccess=bSuccess ? true : false; \
	Parms.Code=Code; \
	Parms.Msg=Msg; \
	Parms.UserInfo=UserInfo; \
	OneLoginResultDelegate.ProcessMulticastDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_27_DELEGATE \
struct OneEngineSDKSubsystem_eventOneInitDelegate_Parms \
{ \
	bool bSuccess; \
	int32 Code; \
	FString Msg; \
}; \
static inline void FOneInitDelegate_DelegateWrapper(const FScriptDelegate& OneInitDelegate, bool bSuccess, int32 Code, const FString& Msg) \
{ \
	OneEngineSDKSubsystem_eventOneInitDelegate_Parms Parms; \
	Parms.bSuccess=bSuccess ? true : false; \
	Parms.Code=Code; \
	Parms.Msg=Msg; \
	OneInitDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_24_DELEGATE \
struct OneEngineSDKSubsystem_eventOneGenericResultDelegate_Parms \
{ \
	bool bSuccess; \
	int32 Code; \
	FString Msg; \
}; \
static inline void FOneGenericResultDelegate_DelegateWrapper(const FScriptDelegate& OneGenericResultDelegate, bool bSuccess, int32 Code, const FString& Msg) \
{ \
	OneEngineSDKSubsystem_eventOneGenericResultDelegate_Parms Parms; \
	Parms.bSuccess=bSuccess ? true : false; \
	Parms.Code=Code; \
	Parms.Msg=Msg; \
	OneGenericResultDelegate.ProcessDelegate<UObject>(&Parms); \
}


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_SPARSE_DATA
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_RPC_WRAPPERS \
 \
	DECLARE_FUNCTION(execTerminateCommunity); \
	DECLARE_FUNCTION(execOpenCommunityByGame); \
	DECLARE_FUNCTION(execStopUnlockSafeLockUsingPushNotification); \
	DECLARE_FUNCTION(execUnlockSafeLockUsingDynamicCode); \
	DECLARE_FUNCTION(execUnlockSafeLockUsingPushNotification); \
	DECLARE_FUNCTION(execGetRenderConfigFilePath); \
	DECLARE_FUNCTION(execRequestStoreReview); \
	DECLARE_FUNCTION(execInAppRequestStoreReview); \
	DECLARE_FUNCTION(execSwitchScreenPermanentBrightnessState); \
	DECLARE_FUNCTION(execRecoverScreenBrightness); \
	DECLARE_FUNCTION(execSetScreenBrightness); \
	DECLARE_FUNCTION(execGetScreenBrightness); \
	DECLARE_FUNCTION(execGetIpInfo); \
	DECLARE_FUNCTION(execACELogout); \
	DECLARE_FUNCTION(execACEClientPacketReceive); \
	DECLARE_FUNCTION(execACELogin); \
	DECLARE_FUNCTION(execKillProcess); \
	DECLARE_FUNCTION(execCallCommonFunction); \
	DECLARE_FUNCTION(execIsCommonFunctionSupported); \
	DECLARE_FUNCTION(execShouldVerifyBundleId); \
	DECLARE_FUNCTION(execSetScreenOrientation); \
	DECLARE_FUNCTION(execGetSupportedLanguageCodeList); \
	DECLARE_FUNCTION(execSetLanguage); \
	DECLARE_FUNCTION(execGetCurrentLanguage); \
	DECLARE_FUNCTION(execTranslate); \
	DECLARE_FUNCTION(execOpenCustomerService); \
	DECLARE_FUNCTION(execOpenAIHelp); \
	DECLARE_FUNCTION(execUserAuthentication); \
	DECLARE_FUNCTION(execBind); \
	DECLARE_FUNCTION(execGetRegionType); \
	DECLARE_FUNCTION(execIsDebugMode); \
	DECLARE_FUNCTION(execEnableDebugMode); \
	DECLARE_FUNCTION(execIsLoggedIn); \
	DECLARE_FUNCTION(execIsInstalledApp); \
	DECLARE_FUNCTION(execShareDataToApp); \
	DECLARE_FUNCTION(execUpdatePushNotDisturb); \
	DECLARE_FUNCTION(execGetPushNotDisturb); \
	DECLARE_FUNCTION(execUpdatePushTypeList); \
	DECLARE_FUNCTION(execGetPushTypeInfoList); \
	DECLARE_FUNCTION(execUnSetPushUserInfo); \
	DECLARE_FUNCTION(execSetPushUserInfo); \
	DECLARE_FUNCTION(execSetAnalyticsCollectionEnabled); \
	DECLARE_FUNCTION(execGetProviderPushState); \
	DECLARE_FUNCTION(execSetProviderPushState); \
	DECLARE_FUNCTION(execGetPushStatus); \
	DECLARE_FUNCTION(execSetupNotificationCallback); \
	DECLARE_FUNCTION(execStartUpdatePushData); \
	DECLARE_FUNCTION(execRedeemCouponCode); \
	DECLARE_FUNCTION(execActivateDevice); \
	DECLARE_FUNCTION(execQueryUserActiveQualification); \
	DECLARE_FUNCTION(execExchangeActCode); \
	DECLARE_FUNCTION(execQueryActCode); \
	DECLARE_FUNCTION(execDisplayCDKeyDialog); \
	DECLARE_FUNCTION(execSetShowDefaultActivationResultToast); \
	DECLARE_FUNCTION(execFetchUserRoleInfoList); \
	DECLARE_FUNCTION(execGetUserLocationInfo); \
	DECLARE_FUNCTION(execOpenUserCenter); \
	DECLARE_FUNCTION(execGetUserInfo); \
	DECLARE_FUNCTION(execSwitchAccount); \
	DECLARE_FUNCTION(execOpenApplicationSetting); \
	DECLARE_FUNCTION(execCloseClipboardPermission); \
	DECLARE_FUNCTION(execRequestPermission); \
	DECLARE_FUNCTION(execCheckSelfPermission); \
	DECLARE_FUNCTION(execGetPermissions); \
	DECLARE_FUNCTION(execEnterAccountCancellation); \
	DECLARE_FUNCTION(execOpenComplianceOnWebView); \
	DECLARE_FUNCTION(execStopAntiAddictionNotify); \
	DECLARE_FUNCTION(execStartAntiAddictionNotify); \
	DECLARE_FUNCTION(execFetchAntiAddictionInfo); \
	DECLARE_FUNCTION(execTrackEventAddExtraDeviceInfo); \
	DECLARE_FUNCTION(execTrackEventExitGameScene); \
	DECLARE_FUNCTION(execTrackEventEnterGameScene); \
	DECLARE_FUNCTION(execTrackEventAD); \
	DECLARE_FUNCTION(execTrackEvent); \
	DECLARE_FUNCTION(execGameGetServerListEvent); \
	DECLARE_FUNCTION(execGameResDecEvent); \
	DECLARE_FUNCTION(execGameUpdateAssetEvent); \
	DECLARE_FUNCTION(execGameResReqEvent); \
	DECLARE_FUNCTION(execTrackEventRoleLevelUp); \
	DECLARE_FUNCTION(execTrackEventRoleCreate); \
	DECLARE_FUNCTION(execTrackEventRoleLogout); \
	DECLARE_FUNCTION(execTrackEventRoleLoginError); \
	DECLARE_FUNCTION(execTrackEventRoleLoginSucceed); \
	DECLARE_FUNCTION(execGetDeviceInfo); \
	DECLARE_FUNCTION(execGetChannelMediaId); \
	DECLARE_FUNCTION(execGetChannelId); \
	DECLARE_FUNCTION(execGetChannelPlatform); \
	DECLARE_FUNCTION(execGetPlatformOS); \
	DECLARE_FUNCTION(execGetProductList); \
	DECLARE_FUNCTION(execPay); \
	DECLARE_FUNCTION(execGuestLogin); \
	DECLARE_FUNCTION(execThirdLogin); \
	DECLARE_FUNCTION(execTokenLogin); \
	DECLARE_FUNCTION(execGetUserTokenList); \
	DECLARE_FUNCTION(execGetQRCodeScanResult); \
	DECLARE_FUNCTION(execLogin); \
	DECLARE_FUNCTION(execGetAppId); \
	DECLARE_FUNCTION(execSetUpConfigAppID); \
	DECLARE_FUNCTION(execExaminStatus); \
	DECLARE_FUNCTION(execInit);


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
 \
	DECLARE_FUNCTION(execTerminateCommunity); \
	DECLARE_FUNCTION(execOpenCommunityByGame); \
	DECLARE_FUNCTION(execStopUnlockSafeLockUsingPushNotification); \
	DECLARE_FUNCTION(execUnlockSafeLockUsingDynamicCode); \
	DECLARE_FUNCTION(execUnlockSafeLockUsingPushNotification); \
	DECLARE_FUNCTION(execGetRenderConfigFilePath); \
	DECLARE_FUNCTION(execRequestStoreReview); \
	DECLARE_FUNCTION(execInAppRequestStoreReview); \
	DECLARE_FUNCTION(execSwitchScreenPermanentBrightnessState); \
	DECLARE_FUNCTION(execRecoverScreenBrightness); \
	DECLARE_FUNCTION(execSetScreenBrightness); \
	DECLARE_FUNCTION(execGetScreenBrightness); \
	DECLARE_FUNCTION(execGetIpInfo); \
	DECLARE_FUNCTION(execACELogout); \
	DECLARE_FUNCTION(execACEClientPacketReceive); \
	DECLARE_FUNCTION(execACELogin); \
	DECLARE_FUNCTION(execKillProcess); \
	DECLARE_FUNCTION(execCallCommonFunction); \
	DECLARE_FUNCTION(execIsCommonFunctionSupported); \
	DECLARE_FUNCTION(execShouldVerifyBundleId); \
	DECLARE_FUNCTION(execSetScreenOrientation); \
	DECLARE_FUNCTION(execGetSupportedLanguageCodeList); \
	DECLARE_FUNCTION(execSetLanguage); \
	DECLARE_FUNCTION(execGetCurrentLanguage); \
	DECLARE_FUNCTION(execTranslate); \
	DECLARE_FUNCTION(execOpenCustomerService); \
	DECLARE_FUNCTION(execOpenAIHelp); \
	DECLARE_FUNCTION(execUserAuthentication); \
	DECLARE_FUNCTION(execBind); \
	DECLARE_FUNCTION(execGetRegionType); \
	DECLARE_FUNCTION(execIsDebugMode); \
	DECLARE_FUNCTION(execEnableDebugMode); \
	DECLARE_FUNCTION(execIsLoggedIn); \
	DECLARE_FUNCTION(execIsInstalledApp); \
	DECLARE_FUNCTION(execShareDataToApp); \
	DECLARE_FUNCTION(execUpdatePushNotDisturb); \
	DECLARE_FUNCTION(execGetPushNotDisturb); \
	DECLARE_FUNCTION(execUpdatePushTypeList); \
	DECLARE_FUNCTION(execGetPushTypeInfoList); \
	DECLARE_FUNCTION(execUnSetPushUserInfo); \
	DECLARE_FUNCTION(execSetPushUserInfo); \
	DECLARE_FUNCTION(execSetAnalyticsCollectionEnabled); \
	DECLARE_FUNCTION(execGetProviderPushState); \
	DECLARE_FUNCTION(execSetProviderPushState); \
	DECLARE_FUNCTION(execGetPushStatus); \
	DECLARE_FUNCTION(execSetupNotificationCallback); \
	DECLARE_FUNCTION(execStartUpdatePushData); \
	DECLARE_FUNCTION(execRedeemCouponCode); \
	DECLARE_FUNCTION(execActivateDevice); \
	DECLARE_FUNCTION(execQueryUserActiveQualification); \
	DECLARE_FUNCTION(execExchangeActCode); \
	DECLARE_FUNCTION(execQueryActCode); \
	DECLARE_FUNCTION(execDisplayCDKeyDialog); \
	DECLARE_FUNCTION(execSetShowDefaultActivationResultToast); \
	DECLARE_FUNCTION(execFetchUserRoleInfoList); \
	DECLARE_FUNCTION(execGetUserLocationInfo); \
	DECLARE_FUNCTION(execOpenUserCenter); \
	DECLARE_FUNCTION(execGetUserInfo); \
	DECLARE_FUNCTION(execSwitchAccount); \
	DECLARE_FUNCTION(execOpenApplicationSetting); \
	DECLARE_FUNCTION(execCloseClipboardPermission); \
	DECLARE_FUNCTION(execRequestPermission); \
	DECLARE_FUNCTION(execCheckSelfPermission); \
	DECLARE_FUNCTION(execGetPermissions); \
	DECLARE_FUNCTION(execEnterAccountCancellation); \
	DECLARE_FUNCTION(execOpenComplianceOnWebView); \
	DECLARE_FUNCTION(execStopAntiAddictionNotify); \
	DECLARE_FUNCTION(execStartAntiAddictionNotify); \
	DECLARE_FUNCTION(execFetchAntiAddictionInfo); \
	DECLARE_FUNCTION(execTrackEventAddExtraDeviceInfo); \
	DECLARE_FUNCTION(execTrackEventExitGameScene); \
	DECLARE_FUNCTION(execTrackEventEnterGameScene); \
	DECLARE_FUNCTION(execTrackEventAD); \
	DECLARE_FUNCTION(execTrackEvent); \
	DECLARE_FUNCTION(execGameGetServerListEvent); \
	DECLARE_FUNCTION(execGameResDecEvent); \
	DECLARE_FUNCTION(execGameUpdateAssetEvent); \
	DECLARE_FUNCTION(execGameResReqEvent); \
	DECLARE_FUNCTION(execTrackEventRoleLevelUp); \
	DECLARE_FUNCTION(execTrackEventRoleCreate); \
	DECLARE_FUNCTION(execTrackEventRoleLogout); \
	DECLARE_FUNCTION(execTrackEventRoleLoginError); \
	DECLARE_FUNCTION(execTrackEventRoleLoginSucceed); \
	DECLARE_FUNCTION(execGetDeviceInfo); \
	DECLARE_FUNCTION(execGetChannelMediaId); \
	DECLARE_FUNCTION(execGetChannelId); \
	DECLARE_FUNCTION(execGetChannelPlatform); \
	DECLARE_FUNCTION(execGetPlatformOS); \
	DECLARE_FUNCTION(execGetProductList); \
	DECLARE_FUNCTION(execPay); \
	DECLARE_FUNCTION(execGuestLogin); \
	DECLARE_FUNCTION(execThirdLogin); \
	DECLARE_FUNCTION(execTokenLogin); \
	DECLARE_FUNCTION(execGetUserTokenList); \
	DECLARE_FUNCTION(execGetQRCodeScanResult); \
	DECLARE_FUNCTION(execLogin); \
	DECLARE_FUNCTION(execGetAppId); \
	DECLARE_FUNCTION(execSetUpConfigAppID); \
	DECLARE_FUNCTION(execExaminStatus); \
	DECLARE_FUNCTION(execInit);


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUOneEngineSDKSubsystem(); \
	friend struct Z_Construct_UClass_UOneEngineSDKSubsystem_Statics; \
public: \
	DECLARE_CLASS(UOneEngineSDKSubsystem, UEngineSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UOneEngineSDKSubsystem)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_INCLASS \
private: \
	static void StaticRegisterNativesUOneEngineSDKSubsystem(); \
	friend struct Z_Construct_UClass_UOneEngineSDKSubsystem_Statics; \
public: \
	DECLARE_CLASS(UOneEngineSDKSubsystem, UEngineSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UOneEngineSDKSubsystem)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UOneEngineSDKSubsystem(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UOneEngineSDKSubsystem) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UOneEngineSDKSubsystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UOneEngineSDKSubsystem); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UOneEngineSDKSubsystem(UOneEngineSDKSubsystem&&); \
	NO_API UOneEngineSDKSubsystem(const UOneEngineSDKSubsystem&); \
public:


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UOneEngineSDKSubsystem() { }; \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UOneEngineSDKSubsystem(UOneEngineSDKSubsystem&&); \
	NO_API UOneEngineSDKSubsystem(const UOneEngineSDKSubsystem&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UOneEngineSDKSubsystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UOneEngineSDKSubsystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UOneEngineSDKSubsystem)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_PRIVATE_PROPERTY_OFFSET
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_13_PROLOG
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_RPC_WRAPPERS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_INCLASS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_INCLASS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h_16_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ONEENGINESDK_API UClass* StaticClass<class UOneEngineSDKSubsystem>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKSubsystem_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
