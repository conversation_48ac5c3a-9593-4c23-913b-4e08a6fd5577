// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ONEENGINESDK_OneEngineSDKHelper_generated_h
#error "OneEngineSDKHelper.generated.h already included, missing '#pragma once' in OneEngineSDKHelper.h"
#endif
#define ONEENGINESDK_OneEngineSDKHelper_generated_h

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_576_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FUserIpInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FUserIpInfo>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_552_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOneActiveQualificationInfo>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_533_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOnePushNotDisturbInfo>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_515_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePushMessage_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOnePushMessage>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_501_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOnePushTypeInfo>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_487_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePushStatus_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOnePushStatus>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_462_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneShareData_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOneShareData>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_446_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOneShareWeiboSuperData>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_380_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePermissionInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOnePermissionInfo>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_328_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOneURCRoleInfo>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_279_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOneAntiAddictionInfo>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_262_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneDeviceInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOneDeviceInfo>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_236_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOneUserLocationInfo>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_205_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneRoleInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOneRoleInfo>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_164_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOnePaymentInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOnePaymentInfo>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_138_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneProductInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOneProductInfo>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_77_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneUserInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOneUserInfo>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h_44_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FOneUserThirdInfo>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Public_OneEngineSDKHelper_h


#define FOREACH_ENUM_EONENAVERGAMETYPE(op) \
	op(EOneNaverGameType::Banner) \
	op(EOneNaverGameType::Sorry) \
	op(EOneNaverGameType::Board) \
	op(EOneNaverGameType::Feed) 

enum class EOneNaverGameType : uint8;
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneNaverGameType>();

#define FOREACH_ENUM_EONEUNLOCKSAFELOCKTYPE(op) \
	op(EOneUnlockSafeLockType::PushNotification) \
	op(EOneUnlockSafeLockType::DynamicCode) 

enum class EOneUnlockSafeLockType : uint8;
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneUnlockSafeLockType>();

#define FOREACH_ENUM_EONEUNLOCKSAFELOCKRESULT(op) \
	op(EOneUnlockSafeLockResult::WaitingToUnlock) \
	op(EOneUnlockSafeLockResult::UnlockSucceeded) \
	op(EOneUnlockSafeLockResult::UnlockTimedOut) \
	op(EOneUnlockSafeLockResult::UnlockFailed) 

enum class EOneUnlockSafeLockResult : uint8;
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneUnlockSafeLockResult>();

#define FOREACH_ENUM_EONEENGINESDKREGIONTYPE(op) \
	op(EOneEngineSDKRegionType::Mainland) \
	op(EOneEngineSDKRegionType::Oversea) 

enum class EOneEngineSDKRegionType : uint8;
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneEngineSDKRegionType>();

#define FOREACH_ENUM_EONESHARETYPE(op) \
	op(EOneShareType::Image) \
	op(EOneShareType::WebPage) \
	op(EOneShareType::Text) \
	op(EOneShareType::ImageSnapShot) 

enum class EOneShareType : uint8;
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneShareType>();

#define FOREACH_ENUM_EONESHAREAPPTARGET(op) \
	op(EOneShareAppTarget::PE_WeChatSession) \
	op(EOneShareAppTarget::PE_WeChatMoment) \
	op(EOneShareAppTarget::PE_QQ) \
	op(EOneShareAppTarget::PE_QZone) \
	op(EOneShareAppTarget::PE_Weibo) \
	op(EOneShareAppTarget::PE_Bilibili) \
	op(EOneShareAppTarget::PE_Facebook) \
	op(EOneShareAppTarget::PE_VK) \
	op(EOneShareAppTarget::PE_Instagram) \
	op(EOneShareAppTarget::PE_Twitter) \
	op(EOneShareAppTarget::PE_Line) \
	op(EOneShareAppTarget::PE_NaverGame) \
	op(EOneShareAppTarget::PE_TikTok) \
	op(EOneShareAppTarget::PE_Discord) \
	op(EOneShareAppTarget::PE_Telegram) 

enum class EOneShareAppTarget : uint8;
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneShareAppTarget>();

#define FOREACH_ENUM_EONESCREENORIENTATION(op) \
	op(EOneScreenOrientation::Unknown) \
	op(EOneScreenOrientation::Portrait) \
	op(EOneScreenOrientation::Landscape) 

enum class EOneScreenOrientation : uint8;
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneScreenOrientation>();

#define FOREACH_ENUM_EONEAIHELPTYPE(op) \
	op(EOneAIHelpType::Unknown) \
	op(EOneAIHelpType::RobotChat) \
	op(EOneAIHelpType::FAQ) 

enum class EOneAIHelpType : uint8;
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneAIHelpType>();

#define FOREACH_ENUM_EONEPERMISSIONTYPE(op) \
	op(EOnePermissionType::Clipboard) \
	op(EOnePermissionType::ReadExternalStorage) \
	op(EOnePermissionType::WriteExternalStorage) \
	op(EOnePermissionType::Camera) \
	op(EOnePermissionType::RecordAudio) \
	op(EOnePermissionType::CoarseLocation) \
	op(EOnePermissionType::FineLocation) \
	op(EOnePermissionType::CallPhone) \
	op(EOnePermissionType::ReadContacts) \
	op(EOnePermissionType::ReadSms) \
	op(EOnePermissionType::ReadCalendar) \
	op(EOnePermissionType::BodySensors) \
	op(EOnePermissionType::Notification) \
	op(EOnePermissionType::ATTTrack) 

enum class EOnePermissionType : uint8;
template<> ONEENGINESDK_API UEnum* StaticEnum<EOnePermissionType>();

#define FOREACH_ENUM_EONERESEVENTSTATE(op) \
	op(EOneResEventState::Begin) \
	op(EOneResEventState::Success) \
	op(EOneResEventState::Failed) 

enum class EOneResEventState : uint8;
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneResEventState>();

#define FOREACH_ENUM_EONEENGINETHIRDTYPE(op) \
	op(EOneEngineThirdType::Guest) \
	op(EOneEngineThirdType::Facebook) \
	op(EOneEngineThirdType::Google) \
	op(EOneEngineThirdType::Twitter) \
	op(EOneEngineThirdType::Line) \
	op(EOneEngineThirdType::GooglePlay) \
	op(EOneEngineThirdType::GameCenter) \
	op(EOneEngineThirdType::Phone) \
	op(EOneEngineThirdType::Email) \
	op(EOneEngineThirdType::VK) \
	op(EOneEngineThirdType::Naver) \
	op(EOneEngineThirdType::Apple) \
	op(EOneEngineThirdType::WeChat) \
	op(EOneEngineThirdType::GuestInherit) \
	op(EOneEngineThirdType::Yandex) \
	op(EOneEngineThirdType::MailRu) \
	op(EOneEngineThirdType::Infiplay) \
	op(EOneEngineThirdType::HW) \
	op(EOneEngineThirdType::ShareCode) \
	op(EOneEngineThirdType::HONOR) \
	op(EOneEngineThirdType::STEAM) \
	op(EOneEngineThirdType::PCScanCode) \
	op(EOneEngineThirdType::PlayStation) \
	op(EOneEngineThirdType::APJ) \
	op(EOneEngineThirdType::Crunchyrool) \
	op(EOneEngineThirdType::LinkAccount) \
	op(EOneEngineThirdType::NaverCafe) \
	op(EOneEngineThirdType::WMPass) 

enum class EOneEngineThirdType : uint8;
template<> ONEENGINESDK_API UEnum* StaticEnum<EOneEngineThirdType>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
