// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ONEENGINESDK_PSOneScaleWidget_generated_h
#error "PSOneScaleWidget.generated.h already included, missing '#pragma once' in PSOneScaleWidget.h"
#endif
#define ONEENGINESDK_PSOneScaleWidget_generated_h

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_SPARSE_DATA
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_RPC_WRAPPERS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_RPC_WRAPPERS_NO_PURE_DECLS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneScaleWidget(); \
	friend struct Z_Construct_UClass_UPSOneScaleWidget_Statics; \
public: \
	DECLARE_CLASS(UPSOneScaleWidget, UUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneScaleWidget)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_INCLASS \
private: \
	static void StaticRegisterNativesUPSOneScaleWidget(); \
	friend struct Z_Construct_UClass_UPSOneScaleWidget_Statics; \
public: \
	DECLARE_CLASS(UPSOneScaleWidget, UUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneScaleWidget)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneScaleWidget(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneScaleWidget) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneScaleWidget); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneScaleWidget); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneScaleWidget(UPSOneScaleWidget&&); \
	NO_API UPSOneScaleWidget(const UPSOneScaleWidget&); \
public:


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneScaleWidget(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()) : Super(ObjectInitializer) { }; \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneScaleWidget(UPSOneScaleWidget&&); \
	NO_API UPSOneScaleWidget(const UPSOneScaleWidget&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneScaleWidget); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneScaleWidget); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneScaleWidget)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_PRIVATE_PROPERTY_OFFSET
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_12_PROLOG
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_RPC_WRAPPERS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_INCLASS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_RPC_WRAPPERS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_INCLASS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h_15_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ONEENGINESDK_API UClass* StaticClass<class UPSOneScaleWidget>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneScaleWidget_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
