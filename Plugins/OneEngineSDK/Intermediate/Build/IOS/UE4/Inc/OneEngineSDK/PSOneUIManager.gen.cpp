// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneUIManager.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneUIManager() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUIManager_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUIManager();
	COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
// End Cross Module References
	DEFINE_FUNCTION(UPSOneUIManager::execHideDelTips)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->HideDelTips();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUIManager::execShowDelTips)
	{
		P_GET_PROPERTY(FStrProperty,Z_Param_Tips);
		P_GET_PROPERTY(FStrProperty,Z_Param_Uid);
		P_GET_PROPERTY(FStrProperty,Z_Param_Token);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->ShowDelTips(Z_Param_Tips,Z_Param_Uid,Z_Param_Token);
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUIManager::execHideUserCenter)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->HideUserCenter();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUIManager::execShowUserCenter)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->ShowUserCenter();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUIManager::execHideRealName)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->HideRealName();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUIManager::execShowRealName)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->ShowRealName();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUIManager::execHideLogin)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->HideLogin();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUIManager::execShowLogin)
	{
		P_GET_UBOOL(Z_Param_bIsMainland);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->ShowLogin(Z_Param_bIsMainland);
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUIManager::execSwitchToPreparePage)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->SwitchToPreparePage();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUIManager::execHideUserAgreement)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->HideUserAgreement();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUIManager::execShowUserAgreement)
	{
		P_GET_UBOOL(Z_Param_bHasAgreed);
		P_GET_PROPERTY(FIntProperty,Z_Param_LoginOption);
		P_GET_PROPERTY(FStrProperty,Z_Param_Title);
		P_GET_PROPERTY(FStrProperty,Z_Param_Content);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->ShowUserAgreement(Z_Param_bHasAgreed,Z_Param_LoginOption,Z_Param_Title,Z_Param_Content);
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUIManager::execShowLoginSuccessHint)
	{
		P_GET_PROPERTY_REF(FTextProperty,Z_Param_Out_Text);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->ShowLoginSuccessHint(Z_Param_Out_Text);
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUIManager::execShowToast)
	{
		P_GET_PROPERTY_REF(FTextProperty,Z_Param_Out_Text);
		P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
		P_GET_UBOOL(Z_Param_HandleFocusWidget);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->ShowToast(Z_Param_Out_Text,Z_Param_Duration,Z_Param_HandleFocusWidget);
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUIManager::execHideLoading)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->HideLoading();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUIManager::execShowLoading)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->ShowLoading();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUIManager::execGet)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		*(UPSOneUIManager**)Z_Param__Result=UPSOneUIManager::Get();
		P_NATIVE_END;
	}
	void UPSOneUIManager::StaticRegisterNativesUPSOneUIManager()
	{
		UClass* Class = UPSOneUIManager::StaticClass();
		static const FNameNativePtrPair Funcs[] = {
			{ "Get", &UPSOneUIManager::execGet },
			{ "HideDelTips", &UPSOneUIManager::execHideDelTips },
			{ "HideLoading", &UPSOneUIManager::execHideLoading },
			{ "HideLogin", &UPSOneUIManager::execHideLogin },
			{ "HideRealName", &UPSOneUIManager::execHideRealName },
			{ "HideUserAgreement", &UPSOneUIManager::execHideUserAgreement },
			{ "HideUserCenter", &UPSOneUIManager::execHideUserCenter },
			{ "ShowDelTips", &UPSOneUIManager::execShowDelTips },
			{ "ShowLoading", &UPSOneUIManager::execShowLoading },
			{ "ShowLogin", &UPSOneUIManager::execShowLogin },
			{ "ShowLoginSuccessHint", &UPSOneUIManager::execShowLoginSuccessHint },
			{ "ShowRealName", &UPSOneUIManager::execShowRealName },
			{ "ShowToast", &UPSOneUIManager::execShowToast },
			{ "ShowUserAgreement", &UPSOneUIManager::execShowUserAgreement },
			{ "ShowUserCenter", &UPSOneUIManager::execShowUserCenter },
			{ "SwitchToPreparePage", &UPSOneUIManager::execSwitchToPreparePage },
		};
		FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
	}
	struct Z_Construct_UFunction_UPSOneUIManager_Get_Statics
	{
		struct PSOneUIManager_eventGet_Parms
		{
			UPSOneUIManager* ReturnValue;
		};
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPSOneUIManager_Get_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneUIManager_eventGet_Parms, ReturnValue), Z_Construct_UClass_UPSOneUIManager_NoRegister, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUIManager_Get_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_Get_Statics::NewProp_ReturnValue,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_Get_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "Comment", "/** \xe5\x8d\x95\xe4\xbe\x8b\xe5\xae\x9e\xe4\xbe\x8b\xe8\x8e\xb7\xe5\x8f\x96\xe5\x87\xbd\xe6\x95\xb0 */" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
		{ "ToolTip", "\xe5\x8d\x95\xe4\xbe\x8b\xe5\xae\x9e\xe4\xbe\x8b\xe8\x8e\xb7\xe5\x8f\x96\xe5\x87\xbd\xe6\x95\xb0" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_Get_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "Get", nullptr, nullptr, sizeof(PSOneUIManager_eventGet_Parms), Z_Construct_UFunction_UPSOneUIManager_Get_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_Get_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_Get_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_Get_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_Get()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_Get_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUIManager_HideDelTips_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_HideDelTips_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_HideDelTips_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "HideDelTips", nullptr, nullptr, 0, nullptr, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_HideDelTips_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_HideDelTips_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_HideDelTips()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_HideDelTips_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUIManager_HideLoading_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_HideLoading_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_HideLoading_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "HideLoading", nullptr, nullptr, 0, nullptr, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_HideLoading_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_HideLoading_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_HideLoading()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_HideLoading_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUIManager_HideLogin_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_HideLogin_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_HideLogin_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "HideLogin", nullptr, nullptr, 0, nullptr, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_HideLogin_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_HideLogin_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_HideLogin()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_HideLogin_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUIManager_HideRealName_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_HideRealName_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_HideRealName_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "HideRealName", nullptr, nullptr, 0, nullptr, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_HideRealName_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_HideRealName_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_HideRealName()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_HideRealName_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUIManager_HideUserAgreement_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_HideUserAgreement_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_HideUserAgreement_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "HideUserAgreement", nullptr, nullptr, 0, nullptr, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_HideUserAgreement_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_HideUserAgreement_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_HideUserAgreement()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_HideUserAgreement_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUIManager_HideUserCenter_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_HideUserCenter_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_HideUserCenter_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "HideUserCenter", nullptr, nullptr, 0, nullptr, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_HideUserCenter_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_HideUserCenter_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_HideUserCenter()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_HideUserCenter_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics
	{
		struct PSOneUIManager_eventShowDelTips_Parms
		{
			FString Tips;
			FString Uid;
			FString Token;
		};
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Tips_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Tips;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Uid_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Uid;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Token_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Token;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Tips_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Tips = { "Tips", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneUIManager_eventShowDelTips_Parms, Tips), METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Tips_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Tips_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Uid_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Uid = { "Uid", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneUIManager_eventShowDelTips_Parms, Uid), METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Uid_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Uid_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Token_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Token = { "Token", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneUIManager_eventShowDelTips_Parms, Token), METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Token_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Token_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Tips,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Uid,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::NewProp_Token,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowDelTips", nullptr, nullptr, sizeof(PSOneUIManager_eventShowDelTips_Parms), Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowDelTips()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowDelTips_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUIManager_ShowLoading_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_ShowLoading_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "Comment", "/** UI \xe6\x8e\xa7\xe4\xbb\xb6\xe7\xae\xa1\xe7\x90\x86 */" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
		{ "ToolTip", "UI \xe6\x8e\xa7\xe4\xbb\xb6\xe7\xae\xa1\xe7\x90\x86" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowLoading_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowLoading", nullptr, nullptr, 0, nullptr, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_ShowLoading_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowLoading_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowLoading()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowLoading_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics
	{
		struct PSOneUIManager_eventShowLogin_Parms
		{
			bool bIsMainland;
		};
		static void NewProp_bIsMainland_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsMainland;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	void Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::NewProp_bIsMainland_SetBit(void* Obj)
	{
		((PSOneUIManager_eventShowLogin_Parms*)Obj)->bIsMainland = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::NewProp_bIsMainland = { "bIsMainland", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(PSOneUIManager_eventShowLogin_Parms), &Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::NewProp_bIsMainland_SetBit, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::NewProp_bIsMainland,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "CPP_Default_bIsMainland", "false" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowLogin", nullptr, nullptr, sizeof(PSOneUIManager_eventShowLogin_Parms), Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowLogin()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowLogin_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics
	{
		struct PSOneUIManager_eventShowLoginSuccessHint_Parms
		{
			FText Text;
		};
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Text_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_Text;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::NewProp_Text_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::NewProp_Text = { "Text", nullptr, (EPropertyFlags)0x0010000008000182, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneUIManager_eventShowLoginSuccessHint_Parms, Text), METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::NewProp_Text_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::NewProp_Text_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::NewProp_Text,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowLoginSuccessHint", nullptr, nullptr, sizeof(PSOneUIManager_eventShowLoginSuccessHint_Parms), Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUIManager_ShowRealName_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_ShowRealName_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowRealName_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowRealName", nullptr, nullptr, 0, nullptr, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_ShowRealName_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowRealName_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowRealName()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowRealName_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics
	{
		struct PSOneUIManager_eventShowToast_Parms
		{
			FText Text;
			float Duration;
			bool HandleFocusWidget;
		};
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Text_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_Text;
		static const UE4CodeGen_Private::FFloatPropertyParams NewProp_Duration;
		static void NewProp_HandleFocusWidget_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_HandleFocusWidget;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_Text_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_Text = { "Text", nullptr, (EPropertyFlags)0x0010000008000182, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneUIManager_eventShowToast_Parms, Text), METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_Text_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_Text_MetaData)) };
	const UE4CodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneUIManager_eventShowToast_Parms, Duration), METADATA_PARAMS(nullptr, 0) };
	void Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_HandleFocusWidget_SetBit(void* Obj)
	{
		((PSOneUIManager_eventShowToast_Parms*)Obj)->HandleFocusWidget = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_HandleFocusWidget = { "HandleFocusWidget", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(PSOneUIManager_eventShowToast_Parms), &Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_HandleFocusWidget_SetBit, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_Text,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_Duration,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::NewProp_HandleFocusWidget,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "CPP_Default_Duration", "3.000000" },
		{ "CPP_Default_HandleFocusWidget", "false" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowToast", nullptr, nullptr, sizeof(PSOneUIManager_eventShowToast_Parms), Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowToast()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowToast_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics
	{
		struct PSOneUIManager_eventShowUserAgreement_Parms
		{
			bool bHasAgreed;
			int32 LoginOption;
			FString Title;
			FString Content;
		};
		static void NewProp_bHasAgreed_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bHasAgreed;
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_LoginOption;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Title;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Content_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Content;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	void Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_bHasAgreed_SetBit(void* Obj)
	{
		((PSOneUIManager_eventShowUserAgreement_Parms*)Obj)->bHasAgreed = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_bHasAgreed = { "bHasAgreed", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(PSOneUIManager_eventShowUserAgreement_Parms), &Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_bHasAgreed_SetBit, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_LoginOption = { "LoginOption", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneUIManager_eventShowUserAgreement_Parms, LoginOption), METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_Title_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneUIManager_eventShowUserAgreement_Parms, Title), METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_Title_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_Title_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_Content_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_Content = { "Content", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneUIManager_eventShowUserAgreement_Parms, Content), METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_Content_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_Content_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_bHasAgreed,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_LoginOption,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_Title,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::NewProp_Content,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "CPP_Default_Content", "" },
		{ "CPP_Default_Title", "" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowUserAgreement", nullptr, nullptr, sizeof(PSOneUIManager_eventShowUserAgreement_Parms), Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUIManager_ShowUserCenter_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_ShowUserCenter_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_ShowUserCenter_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "ShowUserCenter", nullptr, nullptr, 0, nullptr, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_ShowUserCenter_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_ShowUserCenter_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_ShowUserCenter()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_ShowUserCenter_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUIManager_SwitchToPreparePage_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUIManager_SwitchToPreparePage_Statics::Function_MetaDataParams[] = {
		{ "Category", "PSOneUIManager" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUIManager_SwitchToPreparePage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUIManager, nullptr, "SwitchToPreparePage", nullptr, nullptr, 0, nullptr, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUIManager_SwitchToPreparePage_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUIManager_SwitchToPreparePage_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUIManager_SwitchToPreparePage()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUIManager_SwitchToPreparePage_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	UClass* Z_Construct_UClass_UPSOneUIManager_NoRegister()
	{
		return UPSOneUIManager::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneUIManager_Statics
	{
		static UObject* (*const DependentSingletons[])();
		static const FClassFunctionLinkInfo FuncInfo[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneUIManager_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UObject,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
	const FClassFunctionLinkInfo Z_Construct_UClass_UPSOneUIManager_Statics::FuncInfo[] = {
		{ &Z_Construct_UFunction_UPSOneUIManager_Get, "Get" }, // 119964340
		{ &Z_Construct_UFunction_UPSOneUIManager_HideDelTips, "HideDelTips" }, // 2628321571
		{ &Z_Construct_UFunction_UPSOneUIManager_HideLoading, "HideLoading" }, // 1312051080
		{ &Z_Construct_UFunction_UPSOneUIManager_HideLogin, "HideLogin" }, // 3821745710
		{ &Z_Construct_UFunction_UPSOneUIManager_HideRealName, "HideRealName" }, // 83245163
		{ &Z_Construct_UFunction_UPSOneUIManager_HideUserAgreement, "HideUserAgreement" }, // 2533610592
		{ &Z_Construct_UFunction_UPSOneUIManager_HideUserCenter, "HideUserCenter" }, // 2299193187
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowDelTips, "ShowDelTips" }, // 2980228914
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowLoading, "ShowLoading" }, // 2321275715
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowLogin, "ShowLogin" }, // 1679282322
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowLoginSuccessHint, "ShowLoginSuccessHint" }, // 1295764107
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowRealName, "ShowRealName" }, // 1306227773
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowToast, "ShowToast" }, // 1755994119
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowUserAgreement, "ShowUserAgreement" }, // 2420690514
		{ &Z_Construct_UFunction_UPSOneUIManager_ShowUserCenter, "ShowUserCenter" }, // 1686927868
		{ &Z_Construct_UFunction_UPSOneUIManager_SwitchToPreparePage, "SwitchToPreparePage" }, // 2479291613
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUIManager_Statics::Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Comment", "/**\n * @class UPSOneUIManager\n * @brief \xe7\xae\xa1\xe7\x90\x86 PS One UI \xe7\x9b\xb8\xe5\x85\xb3\xe7\x9a\x84\xe5\x8a\x9f\xe8\x83\xbd\xef\xbc\x8c\xe5\x8c\x85\xe6\x8b\xac\xe5\x90\x84\xe7\xa7\x8d UI \xe7\x95\x8c\xe9\x9d\xa2\xe7\x9a\x84\xe6\x98\xbe\xe7\xa4\xba\xe5\x92\x8c\xe9\x9a\x90\xe8\x97\x8f\xef\xbc\x8c\xe4\xbb\xa5\xe5\x8f\x8a\xe7\x9b\xb8\xe5\x85\xb3\xe6\x93\x8d\xe4\xbd\x9c\xe7\x9a\x84\xe5\xa4\x84\xe7\x90\x86\n */" },
		{ "IncludePath", "Views/PSOneUIManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUIManager.h" },
		{ "ToolTip", "@class UPSOneUIManager\n@brief \xe7\xae\xa1\xe7\x90\x86 PS One UI \xe7\x9b\xb8\xe5\x85\xb3\xe7\x9a\x84\xe5\x8a\x9f\xe8\x83\xbd\xef\xbc\x8c\xe5\x8c\x85\xe6\x8b\xac\xe5\x90\x84\xe7\xa7\x8d UI \xe7\x95\x8c\xe9\x9d\xa2\xe7\x9a\x84\xe6\x98\xbe\xe7\xa4\xba\xe5\x92\x8c\xe9\x9a\x90\xe8\x97\x8f\xef\xbc\x8c\xe4\xbb\xa5\xe5\x8f\x8a\xe7\x9b\xb8\xe5\x85\xb3\xe6\x93\x8d\xe4\xbd\x9c\xe7\x9a\x84\xe5\xa4\x84\xe7\x90\x86" },
	};
#endif
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneUIManager_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUIManager>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUIManager_Statics::ClassParams = {
		&UPSOneUIManager::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		FuncInfo,
		nullptr,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		UE_ARRAY_COUNT(FuncInfo),
		0,
		0,
		0x001000A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneUIManager_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUIManager_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneUIManager()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneUIManager_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneUIManager, 2561966151);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneUIManager>()
	{
		return UPSOneUIManager::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneUIManager(Z_Construct_UClass_UPSOneUIManager, &UPSOneUIManager::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneUIManager"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUIManager);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
