// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneSelectAreaCode.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneSelectAreaCode() {}
// Cross Module References
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FPSOneAreaCode();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneSelectAreaCode_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneSelectAreaCode();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UScrollBox_NoRegister();
// End Cross Module References
class UScriptStruct* FPSOneAreaCode::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FPSOneAreaCode_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FPSOneAreaCode, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("PSOneAreaCode"), sizeof(FPSOneAreaCode), Get_Z_Construct_UScriptStruct_FPSOneAreaCode_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FPSOneAreaCode>()
{
	return FPSOneAreaCode::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FPSOneAreaCode(FPSOneAreaCode::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("PSOneAreaCode"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFPSOneAreaCode
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFPSOneAreaCode()
	{
		UScriptStruct::DeferCppStructOps<FPSOneAreaCode>(FName(TEXT("PSOneAreaCode")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFPSOneAreaCode;
	struct Z_Construct_UScriptStruct_FPSOneAreaCode_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Id_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_Id;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Code_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_Code;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Area_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Area;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPSOneAreaCode>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Id_MetaData[] = {
		{ "Category", "PSOneAreaCode" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Id = { "Id", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneAreaCode, Id), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Id_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Id_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Code_MetaData[] = {
		{ "Category", "PSOneAreaCode" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Code = { "Code", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneAreaCode, Code), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Code_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Code_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Area_MetaData[] = {
		{ "Category", "PSOneAreaCode" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Area = { "Area", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneAreaCode, Area), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Area_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Area_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Id,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Code,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::NewProp_Area,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"PSOneAreaCode",
		sizeof(FPSOneAreaCode),
		alignof(FPSOneAreaCode),
		Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FPSOneAreaCode()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FPSOneAreaCode_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("PSOneAreaCode"), sizeof(FPSOneAreaCode), Get_Z_Construct_UScriptStruct_FPSOneAreaCode_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FPSOneAreaCode_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FPSOneAreaCode_Hash() { return 3973100089U; }
	DEFINE_FUNCTION(UPSOneSelectAreaCode::execBindData)
	{
		P_GET_TARRAY_REF(FPSOneAreaCode,Z_Param_Out_InAreaCodes);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->BindData(Z_Param_Out_InAreaCodes);
		P_NATIVE_END;
	}
	void UPSOneSelectAreaCode::StaticRegisterNativesUPSOneSelectAreaCode()
	{
		UClass* Class = UPSOneSelectAreaCode::StaticClass();
		static const FNameNativePtrPair Funcs[] = {
			{ "BindData", &UPSOneSelectAreaCode::execBindData },
		};
		FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
	}
	struct Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics
	{
		struct PSOneSelectAreaCode_eventBindData_Parms
		{
			TArray<FPSOneAreaCode> InAreaCodes;
		};
		static const UE4CodeGen_Private::FStructPropertyParams NewProp_InAreaCodes_Inner;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_InAreaCodes_MetaData[];
#endif
		static const UE4CodeGen_Private::FArrayPropertyParams NewProp_InAreaCodes;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::NewProp_InAreaCodes_Inner = { "InAreaCodes", nullptr, (EPropertyFlags)0x0000000000000000, UE4CodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, Z_Construct_UScriptStruct_FPSOneAreaCode, METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::NewProp_InAreaCodes_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::NewProp_InAreaCodes = { "InAreaCodes", nullptr, (EPropertyFlags)0x0010000008000182, UE4CodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneSelectAreaCode_eventBindData_Parms, InAreaCodes), EArrayPropertyFlags::None, METADATA_PARAMS(Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::NewProp_InAreaCodes_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::NewProp_InAreaCodes_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::NewProp_InAreaCodes_Inner,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::NewProp_InAreaCodes,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::Function_MetaDataParams[] = {
		{ "Comment", "// bind data\n" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
		{ "ToolTip", "bind data" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneSelectAreaCode, nullptr, "BindData", nullptr, nullptr, sizeof(PSOneSelectAreaCode_eventBindData_Parms), Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneSelectAreaCode_BindData()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneSelectAreaCode_BindData_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	UClass* Z_Construct_UClass_UPSOneSelectAreaCode_NoRegister()
	{
		return UPSOneSelectAreaCode::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneSelectAreaCode_Statics
	{
		static UObject* (*const DependentSingletons[])();
		static const FClassFunctionLinkInfo FuncInfo[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ScrollBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ScrollBox;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneSelectAreaCode_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
	const FClassFunctionLinkInfo Z_Construct_UClass_UPSOneSelectAreaCode_Statics::FuncInfo[] = {
		{ &Z_Construct_UFunction_UPSOneSelectAreaCode_BindData, "BindData" }, // 974434707
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneSelectAreaCode_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n *\n */" },
		{ "IncludePath", "Views/PSOneSelectAreaCode.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneSelectAreaCode, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_ScaleBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_ScaleBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// enter icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
		{ "ToolTip", "enter icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneSelectAreaCode, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_EnterIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_EnterIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// backspace icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
		{ "ToolTip", "backspace icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneSelectAreaCode, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_BackspaceIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_BackspaceIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_ScrollBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe6\x98\xbe\xe7\xa4\xba\xe7\x9a\x84\xe5\x8c\xba\xe5\x9f\x9f\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneSelectAreaCode.h" },
		{ "ToolTip", "\xe6\x98\xbe\xe7\xa4\xba\xe7\x9a\x84\xe5\x8c\xba\xe5\x9f\x9f" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_ScrollBox = { "ScrollBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneSelectAreaCode, ScrollBox), Z_Construct_UClass_UScrollBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_ScrollBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_ScrollBox_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneSelectAreaCode_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_ScaleBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_EnterIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_BackspaceIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneSelectAreaCode_Statics::NewProp_ScrollBox,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneSelectAreaCode_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneSelectAreaCode>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneSelectAreaCode_Statics::ClassParams = {
		&UPSOneSelectAreaCode::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		FuncInfo,
		Z_Construct_UClass_UPSOneSelectAreaCode_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		UE_ARRAY_COUNT(FuncInfo),
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneSelectAreaCode_Statics::PropPointers),
		0,
		0x00A010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneSelectAreaCode_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneSelectAreaCode_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneSelectAreaCode()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneSelectAreaCode_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneSelectAreaCode, 3213583331);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneSelectAreaCode>()
	{
		return UPSOneSelectAreaCode::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneSelectAreaCode(Z_Construct_UClass_UPSOneSelectAreaCode, &UPSOneSelectAreaCode::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneSelectAreaCode"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneSelectAreaCode);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
