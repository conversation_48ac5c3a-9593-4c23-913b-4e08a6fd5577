// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ONEENGINESDK_PSOneInputPhoneNum_generated_h
#error "PSOneInputPhoneNum.generated.h already included, missing '#pragma once' in PSOneInputPhoneNum.h"
#endif
#define ONEENGINESDK_PSOneInputPhoneNum_generated_h

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_SPARSE_DATA
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_RPC_WRAPPERS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_RPC_WRAPPERS_NO_PURE_DECLS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneInputPhoneNum(); \
	friend struct Z_Construct_UClass_UPSOneInputPhoneNum_Statics; \
public: \
	DECLARE_CLASS(UPSOneInputPhoneNum, UPSOneFocusUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneInputPhoneNum)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_INCLASS \
private: \
	static void StaticRegisterNativesUPSOneInputPhoneNum(); \
	friend struct Z_Construct_UClass_UPSOneInputPhoneNum_Statics; \
public: \
	DECLARE_CLASS(UPSOneInputPhoneNum, UPSOneFocusUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneInputPhoneNum)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneInputPhoneNum(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneInputPhoneNum) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneInputPhoneNum); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneInputPhoneNum); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneInputPhoneNum(UPSOneInputPhoneNum&&); \
	NO_API UPSOneInputPhoneNum(const UPSOneInputPhoneNum&); \
public:


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneInputPhoneNum(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()) : Super(ObjectInitializer) { }; \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneInputPhoneNum(UPSOneInputPhoneNum&&); \
	NO_API UPSOneInputPhoneNum(const UPSOneInputPhoneNum&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneInputPhoneNum); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneInputPhoneNum); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneInputPhoneNum)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_PRIVATE_PROPERTY_OFFSET
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_24_PROLOG
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_RPC_WRAPPERS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_INCLASS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_RPC_WRAPPERS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_INCLASS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h_27_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ONEENGINESDK_API UClass* StaticClass<class UPSOneInputPhoneNum>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneInputPhoneNum_h


#define FOREACH_ENUM_EPSONEINPUTPHONENUMTYPE(op) \
	op(EPSOneInputPhoneNumType::PhoneNum) \
	op(EPSOneInputPhoneNumType::Email) 

enum class EPSOneInputPhoneNumType : uint8;
template<> ONEENGINESDK_API UEnum* StaticEnum<EPSOneInputPhoneNumType>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
