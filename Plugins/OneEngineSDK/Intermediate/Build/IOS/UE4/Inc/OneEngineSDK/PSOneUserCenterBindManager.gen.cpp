// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneUserCenterBindManager.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneUserCenterBindManager() {}
// Cross Module References
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FPSOneBindItem();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterBindManager_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterBindManager();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UScrollBox_NoRegister();
// End Cross Module References
class UScriptStruct* FPSOneBindItem::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FPSOneBindItem_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FPSOneBindItem, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("PSOneBindItem"), sizeof(FPSOneBindItem), Get_Z_Construct_UScriptStruct_FPSOneBindItem_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FPSOneBindItem>()
{
	return FPSOneBindItem::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FPSOneBindItem(FPSOneBindItem::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("PSOneBindItem"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFPSOneBindItem
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFPSOneBindItem()
	{
		UScriptStruct::DeferCppStructOps<FPSOneBindItem>(FName(TEXT("PSOneBindItem")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFPSOneBindItem;
	struct Z_Construct_UScriptStruct_FPSOneBindItem_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneBindItem_Statics::Struct_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterBindManager.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FPSOneBindItem_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPSOneBindItem>();
	}
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPSOneBindItem_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"PSOneBindItem",
		sizeof(FPSOneBindItem),
		alignof(FPSOneBindItem),
		nullptr,
		0,
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneBindItem_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneBindItem_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FPSOneBindItem()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FPSOneBindItem_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("PSOneBindItem"), sizeof(FPSOneBindItem), Get_Z_Construct_UScriptStruct_FPSOneBindItem_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FPSOneBindItem_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FPSOneBindItem_Hash() { return 3817937972U; }
	void UPSOneUserCenterBindManager::StaticRegisterNativesUPSOneUserCenterBindManager()
	{
	}
	UClass* Z_Construct_UClass_UPSOneUserCenterBindManager_NoRegister()
	{
		return UPSOneUserCenterBindManager::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneUserCenterBindManager_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TitleBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_TitleBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ScrollBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ScrollBox;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n * \n */" },
		{ "IncludePath", "Views/PSOneUserCenterBindManager.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterBindManager.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::NewProp_TitleBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterBindManager.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::NewProp_TitleBlock = { "TitleBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterBindManager, TitleBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::NewProp_TitleBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::NewProp_TitleBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::NewProp_ScrollBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterBindManager.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::NewProp_ScrollBox = { "ScrollBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterBindManager, ScrollBox), Z_Construct_UClass_UScrollBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::NewProp_ScrollBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::NewProp_ScrollBox_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::NewProp_TitleBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::NewProp_ScrollBox,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterBindManager>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::ClassParams = {
		&UPSOneUserCenterBindManager::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::PropPointers),
		0,
		0x00A010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneUserCenterBindManager()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneUserCenterBindManager_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneUserCenterBindManager, 219116173);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneUserCenterBindManager>()
	{
		return UPSOneUserCenterBindManager::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneUserCenterBindManager(Z_Construct_UClass_UPSOneUserCenterBindManager, &UPSOneUserCenterBindManager::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneUserCenterBindManager"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterBindManager);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
