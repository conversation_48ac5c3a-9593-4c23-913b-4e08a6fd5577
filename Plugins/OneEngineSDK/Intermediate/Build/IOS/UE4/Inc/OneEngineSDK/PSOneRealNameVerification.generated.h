// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ONEENGINESDK_PSOneRealNameVerification_generated_h
#error "PSOneRealNameVerification.generated.h already included, missing '#pragma once' in PSOneRealNameVerification.h"
#endif
#define ONEENGINESDK_PSOneRealNameVerification_generated_h

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_SPARSE_DATA
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_RPC_WRAPPERS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_RPC_WRAPPERS_NO_PURE_DECLS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneRealNameVerification(); \
	friend struct Z_Construct_UClass_UPSOneRealNameVerification_Statics; \
public: \
	DECLARE_CLASS(UPSOneRealNameVerification, UPSOneFocusUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneRealNameVerification)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_INCLASS \
private: \
	static void StaticRegisterNativesUPSOneRealNameVerification(); \
	friend struct Z_Construct_UClass_UPSOneRealNameVerification_Statics; \
public: \
	DECLARE_CLASS(UPSOneRealNameVerification, UPSOneFocusUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneRealNameVerification)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneRealNameVerification(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneRealNameVerification) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneRealNameVerification); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneRealNameVerification); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneRealNameVerification(UPSOneRealNameVerification&&); \
	NO_API UPSOneRealNameVerification(const UPSOneRealNameVerification&); \
public:


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneRealNameVerification(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()) : Super(ObjectInitializer) { }; \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneRealNameVerification(UPSOneRealNameVerification&&); \
	NO_API UPSOneRealNameVerification(const UPSOneRealNameVerification&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneRealNameVerification); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneRealNameVerification); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneRealNameVerification)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_PRIVATE_PROPERTY_OFFSET
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_13_PROLOG
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_RPC_WRAPPERS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_INCLASS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_INCLASS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h_16_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ONEENGINESDK_API UClass* StaticClass<class UPSOneRealNameVerification>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneRealNameVerification_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
