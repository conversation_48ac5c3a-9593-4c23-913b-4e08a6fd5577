// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ONEENGINESDK_PSOneTextFieldBase_generated_h
#error "PSOneTextFieldBase.generated.h already included, missing '#pragma once' in PSOneTextFieldBase.h"
#endif
#define ONEENGINESDK_PSOneTextFieldBase_generated_h

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_SPARSE_DATA
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_RPC_WRAPPERS \
 \
	DECLARE_FUNCTION(execDelayFocus); \
	DECLARE_FUNCTION(execInternalOnTextCommitted); \
	DECLARE_FUNCTION(execInternalOnTextChanged);


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_RPC_WRAPPERS_NO_PURE_DECLS \
 \
	DECLARE_FUNCTION(execDelayFocus); \
	DECLARE_FUNCTION(execInternalOnTextCommitted); \
	DECLARE_FUNCTION(execInternalOnTextChanged);


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneTextFieldBase(); \
	friend struct Z_Construct_UClass_UPSOneTextFieldBase_Statics; \
public: \
	DECLARE_CLASS(UPSOneTextFieldBase, UUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneTextFieldBase)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_INCLASS \
private: \
	static void StaticRegisterNativesUPSOneTextFieldBase(); \
	friend struct Z_Construct_UClass_UPSOneTextFieldBase_Statics; \
public: \
	DECLARE_CLASS(UPSOneTextFieldBase, UUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneTextFieldBase)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneTextFieldBase(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneTextFieldBase) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneTextFieldBase); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneTextFieldBase); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneTextFieldBase(UPSOneTextFieldBase&&); \
	NO_API UPSOneTextFieldBase(const UPSOneTextFieldBase&); \
public:


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneTextFieldBase(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()) : Super(ObjectInitializer) { }; \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneTextFieldBase(UPSOneTextFieldBase&&); \
	NO_API UPSOneTextFieldBase(const UPSOneTextFieldBase&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneTextFieldBase); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneTextFieldBase); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneTextFieldBase)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_PRIVATE_PROPERTY_OFFSET
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_9_PROLOG
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_RPC_WRAPPERS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_INCLASS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_RPC_WRAPPERS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_INCLASS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h_12_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ONEENGINESDK_API UClass* StaticClass<class UPSOneTextFieldBase>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneTextFieldBase_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
