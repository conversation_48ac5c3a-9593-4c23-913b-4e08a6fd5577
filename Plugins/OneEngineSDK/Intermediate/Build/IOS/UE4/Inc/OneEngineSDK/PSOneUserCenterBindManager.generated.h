// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ONEENGINESDK_PSOneUserCenterBindManager_generated_h
#error "PSOneUserCenterBindManager.generated.h already included, missing '#pragma once' in PSOneUserCenterBindManager.h"
#endif
#define ONEENGINESDK_PSOneUserCenterBindManager_generated_h

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_13_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPSOneBindItem_Statics; \
	ONEENGINESDK_API static class UScriptStruct* StaticStruct();


template<> ONEENGINESDK_API UScriptStruct* StaticStruct<struct FPSOneBindItem>();

#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_SPARSE_DATA
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_RPC_WRAPPERS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_RPC_WRAPPERS_NO_PURE_DECLS
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPSOneUserCenterBindManager(); \
	friend struct Z_Construct_UClass_UPSOneUserCenterBindManager_Statics; \
public: \
	DECLARE_CLASS(UPSOneUserCenterBindManager, UPSOneFocusUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneUserCenterBindManager)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_INCLASS \
private: \
	static void StaticRegisterNativesUPSOneUserCenterBindManager(); \
	friend struct Z_Construct_UClass_UPSOneUserCenterBindManager_Statics; \
public: \
	DECLARE_CLASS(UPSOneUserCenterBindManager, UPSOneFocusUserWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OneEngineSDK"), NO_API) \
	DECLARE_SERIALIZER(UPSOneUserCenterBindManager)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneUserCenterBindManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneUserCenterBindManager) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneUserCenterBindManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneUserCenterBindManager); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneUserCenterBindManager(UPSOneUserCenterBindManager&&); \
	NO_API UPSOneUserCenterBindManager(const UPSOneUserCenterBindManager&); \
public:


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UPSOneUserCenterBindManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()) : Super(ObjectInitializer) { }; \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	NO_API UPSOneUserCenterBindManager(UPSOneUserCenterBindManager&&); \
	NO_API UPSOneUserCenterBindManager(const UPSOneUserCenterBindManager&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPSOneUserCenterBindManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPSOneUserCenterBindManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPSOneUserCenterBindManager)


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_PRIVATE_PROPERTY_OFFSET
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_21_PROLOG
#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_RPC_WRAPPERS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_INCLASS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_PRIVATE_PROPERTY_OFFSET \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_SPARSE_DATA \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_RPC_WRAPPERS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_INCLASS_NO_PURE_DECLS \
	OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h_24_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ONEENGINESDK_API UClass* StaticClass<class UPSOneUserCenterBindManager>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID OneSDKUE5_6_union_Plugins_OneEngineSDK_Source_OneEngineSDK_Private_Views_PSOneUserCenterBindManager_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
