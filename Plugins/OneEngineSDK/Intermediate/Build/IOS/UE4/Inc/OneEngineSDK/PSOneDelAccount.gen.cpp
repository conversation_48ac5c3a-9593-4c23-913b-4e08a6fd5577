// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneDelAccount.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneDelAccount() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneDelAccount_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneDelAccount();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldBase_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneConfirmButton_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
// End Cross Module References
	void UPSOneDelAccount::StaticRegisterNativesUPSOneDelAccount()
	{
	}
	UClass* Z_Construct_UClass_UPSOneDelAccount_NoRegister()
	{
		return UPSOneDelAccount::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneDelAccount_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TitleTextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_TitleTextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DescTextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_DescTextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_InputTextField_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_InputTextField;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ConfirmButton_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ConfirmButton;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneDelAccount_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelAccount_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n *\n */" },
		{ "IncludePath", "Views/PSOneDelAccount.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelAccount.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelAccount.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneDelAccount, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_ScaleBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_ScaleBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_TitleTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelAccount.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_TitleTextBlock = { "TitleTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneDelAccount, TitleTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_TitleTextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_TitleTextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_DescTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelAccount.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_DescTextBlock = { "DescTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneDelAccount, DescTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_DescTextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_DescTextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_InputTextField_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// InputTextField\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelAccount.h" },
		{ "ToolTip", "InputTextField" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_InputTextField = { "InputTextField", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneDelAccount, InputTextField), Z_Construct_UClass_UPSOneTextFieldBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_InputTextField_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_InputTextField_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_ConfirmButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// ConfirmButton\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelAccount.h" },
		{ "ToolTip", "ConfirmButton" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_ConfirmButton = { "ConfirmButton", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneDelAccount, ConfirmButton), Z_Construct_UClass_UPSOneConfirmButton_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_ConfirmButton_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_ConfirmButton_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// enter icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelAccount.h" },
		{ "ToolTip", "enter icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneDelAccount, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_EnterIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_EnterIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// backspace icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneDelAccount.h" },
		{ "ToolTip", "backspace icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneDelAccount, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_BackspaceIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_BackspaceIcon_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneDelAccount_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_ScaleBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_TitleTextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_DescTextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_InputTextField,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_ConfirmButton,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_EnterIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneDelAccount_Statics::NewProp_BackspaceIcon,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneDelAccount_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneDelAccount>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneDelAccount_Statics::ClassParams = {
		&UPSOneDelAccount::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneDelAccount_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelAccount_Statics::PropPointers),
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneDelAccount_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneDelAccount_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneDelAccount()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneDelAccount_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneDelAccount, **********);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneDelAccount>()
	{
		return UPSOneDelAccount::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneDelAccount(Z_Construct_UClass_UPSOneDelAccount, &UPSOneDelAccount::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneDelAccount"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneDelAccount);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
