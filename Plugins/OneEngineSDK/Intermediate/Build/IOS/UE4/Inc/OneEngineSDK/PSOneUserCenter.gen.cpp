// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneUserCenter.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneUserCenter() {}
// Cross Module References
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FPSOneUserCenterModel();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenter_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenter();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UBackgroundBlur_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UVerticalBox_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneAvatarImage_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UWidgetSwitcher_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterBindManager_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterAccountInfo_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterIDInfo_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterDeviceManager_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterLegalTerms_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterOther_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
// End Cross Module References
class UScriptStruct* FPSOneUserCenterModel::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FPSOneUserCenterModel_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FPSOneUserCenterModel, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("PSOneUserCenterModel"), sizeof(FPSOneUserCenterModel), Get_Z_Construct_UScriptStruct_FPSOneUserCenterModel_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FPSOneUserCenterModel>()
{
	return FPSOneUserCenterModel::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FPSOneUserCenterModel(FPSOneUserCenterModel::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("PSOneUserCenterModel"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFPSOneUserCenterModel
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFPSOneUserCenterModel()
	{
		UScriptStruct::DeferCppStructOps<FPSOneUserCenterModel>(FName(TEXT("PSOneUserCenterModel")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFPSOneUserCenterModel;
	struct Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UserName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_UserName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NickName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_NickName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CellPhone_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_CellPhone;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ShowCellPhone_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ShowCellPhone;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ShowEmail_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ShowEmail;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_HeadImg_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_HeadImg;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_RealName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_RealName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_IDNumber_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_IDNumber;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Email_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Email;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UserID_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_UserID;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_OneID_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_OneID;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Token_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Token;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NewUser_MetaData[];
#endif
		static void NewProp_NewUser_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_NewUser;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UserIP_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_UserIP;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_HavePwd_MetaData[];
#endif
		static void NewProp_HavePwd_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_HavePwd;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::Struct_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPSOneUserCenterModel>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserName_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserName = { "UserName", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneUserCenterModel, UserName), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NickName_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NickName = { "NickName", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneUserCenterModel, NickName), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NickName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NickName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_CellPhone_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_CellPhone = { "CellPhone", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneUserCenterModel, CellPhone), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_CellPhone_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_CellPhone_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_ShowCellPhone_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_ShowCellPhone = { "ShowCellPhone", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneUserCenterModel, ShowCellPhone), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_ShowCellPhone_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_ShowCellPhone_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_ShowEmail_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_ShowEmail = { "ShowEmail", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneUserCenterModel, ShowEmail), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_ShowEmail_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_ShowEmail_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HeadImg_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HeadImg = { "HeadImg", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneUserCenterModel, HeadImg), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HeadImg_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HeadImg_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_RealName_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_RealName = { "RealName", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneUserCenterModel, RealName), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_RealName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_RealName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_IDNumber_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_IDNumber = { "IDNumber", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneUserCenterModel, IDNumber), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_IDNumber_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_IDNumber_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_Email_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_Email = { "Email", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneUserCenterModel, Email), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_Email_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_Email_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserID_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserID = { "UserID", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneUserCenterModel, UserID), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserID_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserID_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_OneID_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_OneID = { "OneID", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneUserCenterModel, OneID), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_OneID_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_OneID_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_Token_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_Token = { "Token", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneUserCenterModel, Token), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_Token_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_Token_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NewUser_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	void Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NewUser_SetBit(void* Obj)
	{
		((FPSOneUserCenterModel*)Obj)->NewUser = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NewUser = { "NewUser", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(FPSOneUserCenterModel), &Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NewUser_SetBit, METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NewUser_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NewUser_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserIP_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserIP = { "UserIP", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FPSOneUserCenterModel, UserIP), METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserIP_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserIP_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HavePwd_MetaData[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	void Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HavePwd_SetBit(void* Obj)
	{
		((FPSOneUserCenterModel*)Obj)->HavePwd = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HavePwd = { "HavePwd", nullptr, (EPropertyFlags)0x0010000000000000, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(FPSOneUserCenterModel), &Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HavePwd_SetBit, METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HavePwd_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HavePwd_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NickName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_CellPhone,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_ShowCellPhone,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_ShowEmail,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HeadImg,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_RealName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_IDNumber,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_Email,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserID,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_OneID,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_Token,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_NewUser,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_UserIP,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::NewProp_HavePwd,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"PSOneUserCenterModel",
		sizeof(FPSOneUserCenterModel),
		alignof(FPSOneUserCenterModel),
		Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FPSOneUserCenterModel()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FPSOneUserCenterModel_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("PSOneUserCenterModel"), sizeof(FPSOneUserCenterModel), Get_Z_Construct_UScriptStruct_FPSOneUserCenterModel_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FPSOneUserCenterModel_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FPSOneUserCenterModel_Hash() { return 10698409U; }
	DEFINE_FUNCTION(UPSOneUserCenter::execUpdateBoundAccounts)
	{
		P_GET_TMAP_REF(int32,bool,Z_Param_Out_Accounts);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->UpdateBoundAccounts(Z_Param_Out_Accounts);
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUserCenter::execRefreshDeviceData)
	{
		P_GET_PROPERTY(FStrProperty,Z_Param_JsonString);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->RefreshDeviceData(Z_Param_JsonString);
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUserCenter::execRefreshData)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->RefreshData();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneUserCenter::execBindData)
	{
		P_GET_PROPERTY(FStrProperty,Z_Param_JsonString);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->BindData(Z_Param_JsonString);
		P_NATIVE_END;
	}
	void UPSOneUserCenter::StaticRegisterNativesUPSOneUserCenter()
	{
		UClass* Class = UPSOneUserCenter::StaticClass();
		static const FNameNativePtrPair Funcs[] = {
			{ "BindData", &UPSOneUserCenter::execBindData },
			{ "RefreshData", &UPSOneUserCenter::execRefreshData },
			{ "RefreshDeviceData", &UPSOneUserCenter::execRefreshDeviceData },
			{ "UpdateBoundAccounts", &UPSOneUserCenter::execUpdateBoundAccounts },
		};
		FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
	}
	struct Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics
	{
		struct PSOneUserCenter_eventBindData_Parms
		{
			FString JsonString;
		};
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_JsonString_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_JsonString;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::NewProp_JsonString_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::NewProp_JsonString = { "JsonString", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneUserCenter_eventBindData_Parms, JsonString), METADATA_PARAMS(Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::NewProp_JsonString_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::NewProp_JsonString_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::NewProp_JsonString,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUserCenter, nullptr, "BindData", nullptr, nullptr, sizeof(PSOneUserCenter_eventBindData_Parms), Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUserCenter_BindData()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUserCenter_BindData_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUserCenter_RefreshData_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUserCenter_RefreshData_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUserCenter_RefreshData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUserCenter, nullptr, "RefreshData", nullptr, nullptr, 0, nullptr, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUserCenter_RefreshData_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_RefreshData_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUserCenter_RefreshData()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUserCenter_RefreshData_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics
	{
		struct PSOneUserCenter_eventRefreshDeviceData_Parms
		{
			FString JsonString;
		};
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_JsonString_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_JsonString;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::NewProp_JsonString_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::NewProp_JsonString = { "JsonString", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneUserCenter_eventRefreshDeviceData_Parms, JsonString), METADATA_PARAMS(Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::NewProp_JsonString_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::NewProp_JsonString_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::NewProp_JsonString,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUserCenter, nullptr, "RefreshDeviceData", nullptr, nullptr, sizeof(PSOneUserCenter_eventRefreshDeviceData_Parms), Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics
	{
		struct PSOneUserCenter_eventUpdateBoundAccounts_Parms
		{
			TMap<int32,bool> Accounts;
		};
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_Accounts_ValueProp;
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_Accounts_Key_KeyProp;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Accounts_MetaData[];
#endif
		static const UE4CodeGen_Private::FMapPropertyParams NewProp_Accounts;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::NewProp_Accounts_ValueProp = { "Accounts", nullptr, (EPropertyFlags)0x0000000000000000, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), 0, nullptr, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::NewProp_Accounts_Key_KeyProp = { "Accounts_Key", nullptr, (EPropertyFlags)0x0000000000000000, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::NewProp_Accounts_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::NewProp_Accounts = { "Accounts", nullptr, (EPropertyFlags)0x0010000008000182, UE4CodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneUserCenter_eventUpdateBoundAccounts_Parms, Accounts), EMapPropertyFlags::None, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::NewProp_Accounts_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::NewProp_Accounts_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::NewProp_Accounts_ValueProp,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::NewProp_Accounts_Key_KeyProp,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::NewProp_Accounts,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneUserCenter, nullptr, "UpdateBoundAccounts", nullptr, nullptr, sizeof(PSOneUserCenter_eventUpdateBoundAccounts_Parms), Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	UClass* Z_Construct_UClass_UPSOneUserCenter_NoRegister()
	{
		return UPSOneUserCenter::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneUserCenter_Statics
	{
		static UObject* (*const DependentSingletons[])();
		static const FClassFunctionLinkInfo FuncInfo[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BackgroundBlur_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BackgroundBlur;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ContentBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ContentBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AvatarImage_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_AvatarImage;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_MainIdText_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_MainIdText;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NickTextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_NickTextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NickText_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_NickText;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UIDTextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_UIDTextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UIDText_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_UIDText;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BindAccountTabCell_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BindAccountTabCell;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AccountTabCell_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_AccountTabCell;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_RealNameTabCell_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_RealNameTabCell;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DevicesTabCell_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_DevicesTabCell;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_LawTabCell_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_LawTabCell;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_OtherTabCell_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_OtherTabCell;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TabSwitcher_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_TabSwitcher;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BindAccountManagerWidget_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BindAccountManagerWidget;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AccountInfoWidget_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_AccountInfoWidget;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_IDInfoWidget_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_IDInfoWidget;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DeviceManagerWidget_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_DeviceManagerWidget;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_LegalTermsWidget_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_LegalTermsWidget;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_OtherWidget_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_OtherWidget;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bIsMainLand_MetaData[];
#endif
		static void NewProp_bIsMainLand_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsMainLand;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneUserCenter_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
	const FClassFunctionLinkInfo Z_Construct_UClass_UPSOneUserCenter_Statics::FuncInfo[] = {
		{ &Z_Construct_UFunction_UPSOneUserCenter_BindData, "BindData" }, // **********
		{ &Z_Construct_UFunction_UPSOneUserCenter_RefreshData, "RefreshData" }, // **********
		{ &Z_Construct_UFunction_UPSOneUserCenter_RefreshDeviceData, "RefreshDeviceData" }, // *********
		{ &Z_Construct_UFunction_UPSOneUserCenter_UpdateBoundAccounts, "UpdateBoundAccounts" }, // ********
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::Class_MetaDataParams[] = {
		{ "IncludePath", "Views/PSOneUserCenter.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_ScaleBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_ScaleBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BackgroundBlur_MetaData[] = {
		{ "bindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BackgroundBlur = { "BackgroundBlur", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, BackgroundBlur), Z_Construct_UClass_UBackgroundBlur_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BackgroundBlur_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BackgroundBlur_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_ContentBox_MetaData[] = {
		{ "bindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_ContentBox = { "ContentBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, ContentBox), Z_Construct_UClass_UVerticalBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_ContentBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_ContentBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AvatarImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe5\xa4\xb4\xe5\x83\x8f\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
		{ "ToolTip", "\xe5\xa4\xb4\xe5\x83\x8f" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AvatarImage = { "AvatarImage", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, AvatarImage), Z_Construct_UClass_UPSOneAvatarImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AvatarImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AvatarImage_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_MainIdText_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// Id\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
		{ "ToolTip", "Id" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_MainIdText = { "MainIdText", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, MainIdText), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_MainIdText_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_MainIdText_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_NickTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_NickTextBlock = { "NickTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, NickTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_NickTextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_NickTextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_NickText_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8d\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8d" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_NickText = { "NickText", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, NickText), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_NickText_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_NickText_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_UIDTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_UIDTextBlock = { "UIDTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, UIDTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_UIDTextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_UIDTextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_UIDText_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// UID\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
		{ "ToolTip", "UID" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_UIDText = { "UIDText", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, UIDText), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_UIDText_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_UIDText_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BindAccountTabCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe7\xbb\x91\xe5\xae\x9a\xe8\xb4\xa6\xe5\x8f\xb7 cell\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
		{ "ToolTip", "\xe7\xbb\x91\xe5\xae\x9a\xe8\xb4\xa6\xe5\x8f\xb7 cell" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BindAccountTabCell = { "BindAccountTabCell", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, BindAccountTabCell), Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BindAccountTabCell_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BindAccountTabCell_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AccountTabCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe8\xb4\xa6\xe5\x8f\xb7\xe4\xbf\xa1\xe6\x81\xaf cell\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
		{ "ToolTip", "\xe8\xb4\xa6\xe5\x8f\xb7\xe4\xbf\xa1\xe6\x81\xaf cell" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AccountTabCell = { "AccountTabCell", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, AccountTabCell), Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AccountTabCell_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AccountTabCell_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_RealNameTabCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe5\xae\x9e\xe5\x90\x8d\xe4\xbf\xa1\xe6\x81\xaf\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
		{ "ToolTip", "\xe5\xae\x9e\xe5\x90\x8d\xe4\xbf\xa1\xe6\x81\xaf" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_RealNameTabCell = { "RealNameTabCell", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, RealNameTabCell), Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_RealNameTabCell_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_RealNameTabCell_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_DevicesTabCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe8\xae\xbe\xe5\xa4\x87\xe7\xae\xa1\xe7\x90\x86\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
		{ "ToolTip", "\xe8\xae\xbe\xe5\xa4\x87\xe7\xae\xa1\xe7\x90\x86" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_DevicesTabCell = { "DevicesTabCell", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, DevicesTabCell), Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_DevicesTabCell_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_DevicesTabCell_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_LawTabCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe6\xb3\x95\xe5\xbe\x8b\xe6\x9d\xa1\xe6\xac\xbe\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
		{ "ToolTip", "\xe6\xb3\x95\xe5\xbe\x8b\xe6\x9d\xa1\xe6\xac\xbe" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_LawTabCell = { "LawTabCell", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, LawTabCell), Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_LawTabCell_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_LawTabCell_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_OtherTabCell_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe5\x85\xb6\xe4\xbb\x96\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
		{ "ToolTip", "\xe5\x85\xb6\xe4\xbb\x96" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_OtherTabCell = { "OtherTabCell", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, OtherTabCell), Z_Construct_UClass_UPSOneUserCenterLeftCell_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_OtherTabCell_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_OtherTabCell_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_TabSwitcher_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// tabSwitcher\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
		{ "ToolTip", "tabSwitcher" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_TabSwitcher = { "TabSwitcher", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, TabSwitcher), Z_Construct_UClass_UWidgetSwitcher_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_TabSwitcher_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_TabSwitcher_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BindAccountManagerWidget_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BindAccountManagerWidget = { "BindAccountManagerWidget", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, BindAccountManagerWidget), Z_Construct_UClass_UPSOneUserCenterBindManager_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BindAccountManagerWidget_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BindAccountManagerWidget_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AccountInfoWidget_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AccountInfoWidget = { "AccountInfoWidget", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, AccountInfoWidget), Z_Construct_UClass_UPSOneUserCenterAccountInfo_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AccountInfoWidget_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AccountInfoWidget_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_IDInfoWidget_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_IDInfoWidget = { "IDInfoWidget", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, IDInfoWidget), Z_Construct_UClass_UPSOneUserCenterIDInfo_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_IDInfoWidget_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_IDInfoWidget_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_DeviceManagerWidget_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_DeviceManagerWidget = { "DeviceManagerWidget", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, DeviceManagerWidget), Z_Construct_UClass_UPSOneUserCenterDeviceManager_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_DeviceManagerWidget_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_DeviceManagerWidget_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_LegalTermsWidget_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_LegalTermsWidget = { "LegalTermsWidget", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, LegalTermsWidget), Z_Construct_UClass_UPSOneUserCenterLegalTerms_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_LegalTermsWidget_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_LegalTermsWidget_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_OtherWidget_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_OtherWidget = { "OtherWidget", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, OtherWidget), Z_Construct_UClass_UPSOneUserCenterOther_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_OtherWidget_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_OtherWidget_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// enter icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
		{ "ToolTip", "enter icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_EnterIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_EnterIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// backspace icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
		{ "ToolTip", "backspace icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenter, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BackspaceIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BackspaceIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_bIsMainLand_MetaData[] = {
		{ "Category", "PSOneUserCenter" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenter.h" },
	};
#endif
	void Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_bIsMainLand_SetBit(void* Obj)
	{
		((UPSOneUserCenter*)Obj)->bIsMainLand = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_bIsMainLand = { "bIsMainLand", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UPSOneUserCenter), &Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_bIsMainLand_SetBit, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_bIsMainLand_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_bIsMainLand_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenter_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_ScaleBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BackgroundBlur,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_ContentBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AvatarImage,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_MainIdText,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_NickTextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_NickText,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_UIDTextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_UIDText,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BindAccountTabCell,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AccountTabCell,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_RealNameTabCell,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_DevicesTabCell,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_LawTabCell,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_OtherTabCell,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_TabSwitcher,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BindAccountManagerWidget,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_AccountInfoWidget,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_IDInfoWidget,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_DeviceManagerWidget,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_LegalTermsWidget,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_OtherWidget,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_EnterIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_BackspaceIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenter_Statics::NewProp_bIsMainLand,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneUserCenter_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenter>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenter_Statics::ClassParams = {
		&UPSOneUserCenter::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		FuncInfo,
		Z_Construct_UClass_UPSOneUserCenter_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		UE_ARRAY_COUNT(FuncInfo),
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::PropPointers),
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenter_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenter_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneUserCenter()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneUserCenter_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneUserCenter, 2217998702);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneUserCenter>()
	{
		return UPSOneUserCenter::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneUserCenter(Z_Construct_UClass_UPSOneUserCenter, &UPSOneUserCenter::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneUserCenter"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenter);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
