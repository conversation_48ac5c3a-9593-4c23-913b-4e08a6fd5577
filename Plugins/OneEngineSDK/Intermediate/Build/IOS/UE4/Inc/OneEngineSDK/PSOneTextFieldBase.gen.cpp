// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneTextFieldBase.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneTextFieldBase() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldBase_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldBase();
	UMG_API UClass* Z_Construct_UClass_UUserWidget();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	SLATECORE_API UEnum* Z_Construct_UEnum_SlateCore_ETextCommit();
	UMG_API UEnum* Z_Construct_UEnum_UMG_EVirtualKeyboardType();
	UMG_API UClass* Z_Construct_UClass_UBorder_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UEditableText_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
	ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
// End Cross Module References
	DEFINE_FUNCTION(UPSOneTextFieldBase::execDelayFocus)
	{
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->DelayFocus();
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneTextFieldBase::execInternalOnTextCommitted)
	{
		P_GET_PROPERTY_REF(FTextProperty,Z_Param_Out_Text);
		P_GET_PROPERTY(FByteProperty,Z_Param_CommitType);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->InternalOnTextCommitted(Z_Param_Out_Text,ETextCommit::Type(Z_Param_CommitType));
		P_NATIVE_END;
	}
	DEFINE_FUNCTION(UPSOneTextFieldBase::execInternalOnTextChanged)
	{
		P_GET_PROPERTY_REF(FTextProperty,Z_Param_Out_Text);
		P_FINISH;
		P_NATIVE_BEGIN;
		P_THIS->InternalOnTextChanged(Z_Param_Out_Text);
		P_NATIVE_END;
	}
	void UPSOneTextFieldBase::StaticRegisterNativesUPSOneTextFieldBase()
	{
		UClass* Class = UPSOneTextFieldBase::StaticClass();
		static const FNameNativePtrPair Funcs[] = {
			{ "DelayFocus", &UPSOneTextFieldBase::execDelayFocus },
			{ "InternalOnTextChanged", &UPSOneTextFieldBase::execInternalOnTextChanged },
			{ "InternalOnTextCommitted", &UPSOneTextFieldBase::execInternalOnTextCommitted },
		};
		FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
	}
	struct Z_Construct_UFunction_UPSOneTextFieldBase_DelayFocus_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneTextFieldBase_DelayFocus_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneTextFieldBase_DelayFocus_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneTextFieldBase, nullptr, "DelayFocus", nullptr, nullptr, 0, nullptr, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneTextFieldBase_DelayFocus_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneTextFieldBase_DelayFocus_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneTextFieldBase_DelayFocus()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneTextFieldBase_DelayFocus_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics
	{
		struct PSOneTextFieldBase_eventInternalOnTextChanged_Parms
		{
			FText Text;
		};
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Text_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_Text;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::NewProp_Text_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::NewProp_Text = { "Text", nullptr, (EPropertyFlags)0x0010000008000182, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneTextFieldBase_eventInternalOnTextChanged_Parms, Text), METADATA_PARAMS(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::NewProp_Text_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::NewProp_Text_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::NewProp_Text,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneTextFieldBase, nullptr, "InternalOnTextChanged", nullptr, nullptr, sizeof(PSOneTextFieldBase_eventInternalOnTextChanged_Parms), Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00440401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	struct Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics
	{
		struct PSOneTextFieldBase_eventInternalOnTextCommitted_Parms
		{
			FText Text;
			TEnumAsByte<ETextCommit::Type> CommitType;
		};
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Text_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_Text;
		static const UE4CodeGen_Private::FBytePropertyParams NewProp_CommitType;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Function_MetaDataParams[];
#endif
		static const UE4CodeGen_Private::FFunctionParams FuncParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::NewProp_Text_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::NewProp_Text = { "Text", nullptr, (EPropertyFlags)0x0010000008000182, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneTextFieldBase_eventInternalOnTextCommitted_Parms, Text), METADATA_PARAMS(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::NewProp_Text_MetaData, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::NewProp_Text_MetaData)) };
	const UE4CodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::NewProp_CommitType = { "CommitType", nullptr, (EPropertyFlags)0x0010000000000080, UE4CodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(PSOneTextFieldBase_eventInternalOnTextCommitted_Parms, CommitType), Z_Construct_UEnum_SlateCore_ETextCommit, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::NewProp_Text,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::NewProp_CommitType,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
#endif
	const UE4CodeGen_Private::FFunctionParams Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPSOneTextFieldBase, nullptr, "InternalOnTextCommitted", nullptr, nullptr, sizeof(PSOneTextFieldBase_eventInternalOnTextCommitted_Parms), Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::PropPointers), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00440401, 0, 0, METADATA_PARAMS(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::Function_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::Function_MetaDataParams)) };
	UFunction* Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted()
	{
		static UFunction* ReturnFunction = nullptr;
		if (!ReturnFunction)
		{
			UE4CodeGen_Private::ConstructUFunction(ReturnFunction, Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted_Statics::FuncParams);
		}
		return ReturnFunction;
	}
	UClass* Z_Construct_UClass_UPSOneTextFieldBase_NoRegister()
	{
		return UPSOneTextFieldBase::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneTextFieldBase_Statics
	{
		static UObject* (*const DependentSingletons[])();
		static const FClassFunctionLinkInfo FuncInfo[];
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bIsPassword_MetaData[];
#endif
		static void NewProp_bIsPassword_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsPassword;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_VirtualKeyboardType_MetaData[];
#endif
		static const UE4CodeGen_Private::FBytePropertyParams NewProp_VirtualKeyboardType;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DefaultText_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_DefaultText;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_HintText_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_HintText;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BgBorder_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BgBorder;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TextField_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_TextField;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_HintTextBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_HintTextBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NormalTexture_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_NormalTexture;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_FocusTexture_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_FocusTexture;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneTextFieldBase_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
	const FClassFunctionLinkInfo Z_Construct_UClass_UPSOneTextFieldBase_Statics::FuncInfo[] = {
		{ &Z_Construct_UFunction_UPSOneTextFieldBase_DelayFocus, "DelayFocus" }, // 3779902564
		{ &Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextChanged, "InternalOnTextChanged" }, // 2831639814
		{ &Z_Construct_UFunction_UPSOneTextFieldBase_InternalOnTextCommitted, "InternalOnTextCommitted" }, // 426856956
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneTextFieldBase_Statics::Class_MetaDataParams[] = {
		{ "IncludePath", "Views/PSOneTextFieldBase.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_bIsPassword_MetaData[] = {
		{ "Category", "PSOneTextFieldBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
#endif
	void Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_bIsPassword_SetBit(void* Obj)
	{
		((UPSOneTextFieldBase*)Obj)->bIsPassword = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_bIsPassword = { "bIsPassword", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UPSOneTextFieldBase), &Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_bIsPassword_SetBit, METADATA_PARAMS(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_bIsPassword_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_bIsPassword_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_VirtualKeyboardType_MetaData[] = {
		{ "Category", "PSOneTextFieldBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
#endif
	const UE4CodeGen_Private::FBytePropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_VirtualKeyboardType = { "VirtualKeyboardType", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneTextFieldBase, VirtualKeyboardType), Z_Construct_UEnum_UMG_EVirtualKeyboardType, METADATA_PARAMS(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_VirtualKeyboardType_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_VirtualKeyboardType_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_DefaultText_MetaData[] = {
		{ "Category", "PSOneTextFieldBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_DefaultText = { "DefaultText", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneTextFieldBase, DefaultText), METADATA_PARAMS(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_DefaultText_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_DefaultText_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_HintText_MetaData[] = {
		{ "Category", "PSOneTextFieldBase" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_HintText = { "HintText", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneTextFieldBase, HintText), METADATA_PARAMS(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_HintText_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_HintText_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_BgBorder_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_BgBorder = { "BgBorder", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneTextFieldBase, BgBorder), Z_Construct_UClass_UBorder_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_BgBorder_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_BgBorder_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_TextField_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_TextField = { "TextField", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneTextFieldBase, TextField), Z_Construct_UClass_UEditableText_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_TextField_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_TextField_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_HintTextBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_HintTextBlock = { "HintTextBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneTextFieldBase, HintTextBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_HintTextBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_HintTextBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_NormalTexture_MetaData[] = {
		{ "Category", "PSOneTextFieldBase" },
		{ "Comment", "// normal\n" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
		{ "ToolTip", "normal" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_NormalTexture = { "NormalTexture", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneTextFieldBase, NormalTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_NormalTexture_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_NormalTexture_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_FocusTexture_MetaData[] = {
		{ "Category", "PSOneTextFieldBase" },
		{ "Comment", "// onFocus\n" },
		{ "ModuleRelativePath", "Private/Views/PSOneTextFieldBase.h" },
		{ "ToolTip", "onFocus" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_FocusTexture = { "FocusTexture", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneTextFieldBase, FocusTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_FocusTexture_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_FocusTexture_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneTextFieldBase_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_bIsPassword,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_VirtualKeyboardType,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_DefaultText,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_HintText,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_BgBorder,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_TextField,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_HintTextBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_NormalTexture,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneTextFieldBase_Statics::NewProp_FocusTexture,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneTextFieldBase_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneTextFieldBase>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneTextFieldBase_Statics::ClassParams = {
		&UPSOneTextFieldBase::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		FuncInfo,
		Z_Construct_UClass_UPSOneTextFieldBase_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		UE_ARRAY_COUNT(FuncInfo),
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldBase_Statics::PropPointers),
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneTextFieldBase_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneTextFieldBase_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneTextFieldBase()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneTextFieldBase_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneTextFieldBase, 1895931661);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneTextFieldBase>()
	{
		return UPSOneTextFieldBase::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneTextFieldBase(Z_Construct_UClass_UPSOneTextFieldBase, &UPSOneTextFieldBase::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneTextFieldBase"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneTextFieldBase);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
