// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneChangNick.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneChangNick() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneChangNick_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneChangNick();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldBase_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneConfirmButton_NoRegister();
// End Cross Module References
	void UPSOneChangNick::StaticRegisterNativesUPSOneChangNick()
	{
	}
	UClass* Z_Construct_UClass_UPSOneChangNick_NoRegister()
	{
		return UPSOneChangNick::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneChangNick_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NameTextField_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_NameTextField;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ConfirmButton_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ConfirmButton;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneChangNick_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneChangNick_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n * \n */" },
		{ "IncludePath", "Views/PSOneChangNick.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneChangNick.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneChangNick.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneChangNick, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_ScaleBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_ScaleBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_NameTextField_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneChangNick.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_NameTextField = { "NameTextField", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneChangNick, NameTextField), Z_Construct_UClass_UPSOneTextFieldBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_NameTextField_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_NameTextField_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// enter icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneChangNick.h" },
		{ "ToolTip", "enter icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneChangNick, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_EnterIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_EnterIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// backspace icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneChangNick.h" },
		{ "ToolTip", "backspace icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneChangNick, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_BackspaceIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_BackspaceIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_ConfirmButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneChangNick.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_ConfirmButton = { "ConfirmButton", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneChangNick, ConfirmButton), Z_Construct_UClass_UPSOneConfirmButton_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_ConfirmButton_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_ConfirmButton_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneChangNick_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_ScaleBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_NameTextField,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_EnterIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_BackspaceIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneChangNick_Statics::NewProp_ConfirmButton,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneChangNick_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneChangNick>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneChangNick_Statics::ClassParams = {
		&UPSOneChangNick::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneChangNick_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneChangNick_Statics::PropPointers),
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneChangNick_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneChangNick_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneChangNick()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneChangNick_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneChangNick, 4125551370);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneChangNick>()
	{
		return UPSOneChangNick::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneChangNick(Z_Construct_UClass_UPSOneChangNick, &UPSOneChangNick::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneChangNick"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneChangNick);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
