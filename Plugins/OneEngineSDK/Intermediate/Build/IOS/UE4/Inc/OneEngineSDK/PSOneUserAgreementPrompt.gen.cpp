// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneUserAgreementPrompt.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneUserAgreementPrompt() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserAgreementPrompt_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserAgreementPrompt();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UBackgroundBlur_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
	UMG_API UClass* Z_Construct_UClass_USizeBox_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UWidgetSwitcher_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UHorizontalBox_NoRegister();
// End Cross Module References
	void UPSOneUserAgreementPrompt::StaticRegisterNativesUPSOneUserAgreementPrompt()
	{
	}
	UClass* Z_Construct_UClass_UPSOneUserAgreementPrompt_NoRegister()
	{
		return UPSOneUserAgreementPrompt::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BackgroundBlur_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BackgroundBlur;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AgreeImage_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_AgreeImage;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DisagreeImage_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_DisagreeImage;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BgImageSizeBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BgImageSizeBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BgImage_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BgImage;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TitleBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_TitleBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DescBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_DescBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DescBlock_1_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_DescBlock_1;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Left1Image_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_Left1Image;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Right1Image_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_Right1Image;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TriangleImage_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_TriangleImage;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TitleBlock_2_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_TitleBlock_2;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DescBlock_2_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_DescBlock_2;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BindAccountButton_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BindAccountButton;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CreateAccountButton_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_CreateAccountButton;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_LoginWithPwrdButton_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_LoginWithPwrdButton;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Switcher_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_Switcher;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ChildProtectionBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ChildProtectionBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bIsMainland_MetaData[];
#endif
		static void NewProp_bIsMainland_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsMainland;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_StartIndex_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_StartIndex;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_LoginOption_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_LoginOption;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bShowBgImage_MetaData[];
#endif
		static void NewProp_bShowBgImage_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bShowBgImage;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n *\n */" },
		{ "IncludePath", "Views/PSOneUserAgreementPrompt.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_ScaleBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_ScaleBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BackgroundBlur_MetaData[] = {
		{ "bindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BackgroundBlur = { "BackgroundBlur", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, BackgroundBlur), Z_Construct_UClass_UBackgroundBlur_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BackgroundBlur_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BackgroundBlur_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_AgreeImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_AgreeImage = { "AgreeImage", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, AgreeImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_AgreeImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_AgreeImage_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DisagreeImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DisagreeImage = { "DisagreeImage", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, DisagreeImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DisagreeImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DisagreeImage_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BgImageSizeBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BgImageSizeBox = { "BgImageSizeBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, BgImageSizeBox), Z_Construct_UClass_USizeBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BgImageSizeBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BgImageSizeBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BgImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BgImage = { "BgImage", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, BgImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BgImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BgImage_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TitleBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TitleBlock = { "TitleBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, TitleBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TitleBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TitleBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock = { "DescBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, DescBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_1_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_1 = { "DescBlock_1", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, DescBlock_1), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_1_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_1_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Left1Image_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Left1Image = { "Left1Image", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, Left1Image), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Left1Image_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Left1Image_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Right1Image_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Right1Image = { "Right1Image", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, Right1Image), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Right1Image_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Right1Image_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TriangleImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TriangleImage = { "TriangleImage", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, TriangleImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TriangleImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TriangleImage_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TitleBlock_2_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TitleBlock_2 = { "TitleBlock_2", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, TitleBlock_2), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TitleBlock_2_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TitleBlock_2_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_2_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_2 = { "DescBlock_2", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, DescBlock_2), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_2_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_2_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BindAccountButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BindAccountButton = { "BindAccountButton", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, BindAccountButton), Z_Construct_UClass_UPSOneButtonBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BindAccountButton_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BindAccountButton_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_CreateAccountButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_CreateAccountButton = { "CreateAccountButton", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, CreateAccountButton), Z_Construct_UClass_UPSOneButtonBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_CreateAccountButton_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_CreateAccountButton_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_LoginWithPwrdButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_LoginWithPwrdButton = { "LoginWithPwrdButton", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, LoginWithPwrdButton), Z_Construct_UClass_UPSOneButtonBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_LoginWithPwrdButton_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_LoginWithPwrdButton_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Switcher_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Switcher = { "Switcher", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, Switcher), Z_Construct_UClass_UWidgetSwitcher_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Switcher_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Switcher_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_ChildProtectionBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_ChildProtectionBox = { "ChildProtectionBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, ChildProtectionBox), Z_Construct_UClass_UHorizontalBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_ChildProtectionBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_ChildProtectionBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bIsMainland_MetaData[] = {
		{ "Category", "PSOneUserAgreementPrompt" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	void Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bIsMainland_SetBit(void* Obj)
	{
		((UPSOneUserAgreementPrompt*)Obj)->bIsMainland = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bIsMainland = { "bIsMainland", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UPSOneUserAgreementPrompt), &Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bIsMainland_SetBit, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bIsMainland_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bIsMainland_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_StartIndex_MetaData[] = {
		{ "Category", "PSOneUserAgreementPrompt" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_StartIndex = { "StartIndex", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, StartIndex), METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_StartIndex_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_StartIndex_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_LoginOption_MetaData[] = {
		{ "Category", "PSOneUserAgreementPrompt" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_LoginOption = { "LoginOption", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserAgreementPrompt, LoginOption), METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_LoginOption_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_LoginOption_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bShowBgImage_MetaData[] = {
		{ "Category", "PSOneUserAgreementPrompt" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserAgreementPrompt.h" },
	};
#endif
	void Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bShowBgImage_SetBit(void* Obj)
	{
		((UPSOneUserAgreementPrompt*)Obj)->bShowBgImage = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bShowBgImage = { "bShowBgImage", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(UPSOneUserAgreementPrompt), &Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bShowBgImage_SetBit, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bShowBgImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bShowBgImage_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_ScaleBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BackgroundBlur,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_AgreeImage,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DisagreeImage,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BgImageSizeBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BgImage,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TitleBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_1,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Left1Image,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Right1Image,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TriangleImage,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_TitleBlock_2,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_DescBlock_2,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_BindAccountButton,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_CreateAccountButton,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_LoginWithPwrdButton,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_Switcher,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_ChildProtectionBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bIsMainland,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_StartIndex,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_LoginOption,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::NewProp_bShowBgImage,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserAgreementPrompt>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::ClassParams = {
		&UPSOneUserAgreementPrompt::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::PropPointers),
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneUserAgreementPrompt()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneUserAgreementPrompt_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneUserAgreementPrompt, 1776029684);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneUserAgreementPrompt>()
	{
		return UPSOneUserAgreementPrompt::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneUserAgreementPrompt(Z_Construct_UClass_UPSOneUserAgreementPrompt, &UPSOneUserAgreementPrompt::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneUserAgreementPrompt"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserAgreementPrompt);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
