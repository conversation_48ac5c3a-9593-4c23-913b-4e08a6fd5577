// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneFocusCheckButton.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneFocusCheckButton() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusCheckButton_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusCheckButton();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
// End Cross Module References
	void UPSOneFocusCheckButton::StaticRegisterNativesUPSOneFocusCheckButton()
	{
	}
	UClass* Z_Construct_UClass_UPSOneFocusCheckButton_NoRegister()
	{
		return UPSOneFocusCheckButton::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneFocusCheckButton_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CheckedTexture_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_CheckedTexture;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UncheckedImage_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_UncheckedImage;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CheckBoxImage_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_CheckBoxImage;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneFocusCheckButton_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneButtonBase,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneFocusCheckButton_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n *\n */" },
		{ "IncludePath", "Views/PSOneFocusCheckButton.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneFocusCheckButton.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_CheckedTexture_MetaData[] = {
		{ "Category", "PSOneFocusCheckButton" },
		{ "ModuleRelativePath", "Private/Views/PSOneFocusCheckButton.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_CheckedTexture = { "CheckedTexture", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneFocusCheckButton, CheckedTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_CheckedTexture_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_CheckedTexture_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_UncheckedImage_MetaData[] = {
		{ "Category", "PSOneFocusCheckButton" },
		{ "ModuleRelativePath", "Private/Views/PSOneFocusCheckButton.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_UncheckedImage = { "UncheckedImage", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneFocusCheckButton, UncheckedImage), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_UncheckedImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_UncheckedImage_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_CheckBoxImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneFocusCheckButton.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_CheckBoxImage = { "CheckBoxImage", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneFocusCheckButton, CheckBoxImage), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_CheckBoxImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_CheckBoxImage_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneFocusCheckButton_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_CheckedTexture,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_UncheckedImage,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneFocusCheckButton_Statics::NewProp_CheckBoxImage,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneFocusCheckButton_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneFocusCheckButton>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneFocusCheckButton_Statics::ClassParams = {
		&UPSOneFocusCheckButton::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneFocusCheckButton_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneFocusCheckButton_Statics::PropPointers),
		0,
		0x00A010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneFocusCheckButton_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneFocusCheckButton_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneFocusCheckButton()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneFocusCheckButton_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneFocusCheckButton, 356328779);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneFocusCheckButton>()
	{
		return UPSOneFocusCheckButton::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneFocusCheckButton(Z_Construct_UClass_UPSOneFocusCheckButton, &UPSOneFocusCheckButton::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneFocusCheckButton"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneFocusCheckButton);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
