// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeOneEngineSDK_init() {}
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneGenericResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneInitDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneLoginResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneGetQRCodeScanResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneFetchUserTokenListDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OnePayResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneProductInfoDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneGetPlatformDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneGetDeviceInfoDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneFetchAntiAddictionInfoDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneAntiAddictionTimeoutDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneRequestPermissionResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneLogoutResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneUserLocationInfoDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneFetchUserRoleInfoListDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneQueryActCodeResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneQueryUserActiveQualificationResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneActivateDeviceResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneStartUpdatePushDataDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneNotificationDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OnePushStatusDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OnGetPushStateDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneGetPushTypeInfoListDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OnePushNotDisturbInfoDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneBindResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneUserAuthenticationResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneTranslateResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneCommonFunctionDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OnChannelNotHavingExitViewDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OnExitDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneOnGetClientPacket__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OnGetIpInfoResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneUnlockSafeLockResultDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneLoadDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneUnloadDelegate__DelegateSignature();
	ONEENGINESDK_API UFunction* Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneInGameMenuDelegate__DelegateSignature();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK()
	{
		static UPackage* ReturnPackage = nullptr;
		if (!ReturnPackage)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetFriendsResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetBlockingUsersResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnRestrictionStatusResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnCheckPremiumResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnFilterProfanityResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnOpenDialogResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_OnGetProductInfoListPSDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKPSSubsystem_WidgetVisibilityDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneGenericResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneInitDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneLoginResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneGetQRCodeScanResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneFetchUserTokenListDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OnePayResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneProductInfoDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneGetPlatformDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneGetDeviceInfoDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneFetchAntiAddictionInfoDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneAntiAddictionTimeoutDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneRequestPermissionResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneLogoutResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneUserLocationInfoDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneFetchUserRoleInfoListDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneQueryActCodeResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneQueryUserActiveQualificationResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneActivateDeviceResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneStartUpdatePushDataDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneNotificationDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OnePushStatusDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OnGetPushStateDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneGetPushTypeInfoListDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OnePushNotDisturbInfoDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneBindResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneUserAuthenticationResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneTranslateResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneCommonFunctionDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OnChannelNotHavingExitViewDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OnExitDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneOnGetClientPacket__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OnGetIpInfoResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneUnlockSafeLockResultDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneLoadDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneUnloadDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOneEngineSDKSubsystem_OneInGameMenuDelegate__DelegateSignature,
			};
			static const UE4CodeGen_Private::FPackageParams PackageParams = {
				"/Script/OneEngineSDK",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0xCBEB77CE,
				0xF6DBE84D,
				METADATA_PARAMS(nullptr, 0)
			};
			UE4CodeGen_Private::ConstructUPackage(ReturnPackage, PackageParams);
		}
		return ReturnPackage;
	}
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
