// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneUserCenterRightCellAvatar.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneUserCenterRightCellAvatar() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAvatar();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneAvatarImage_NoRegister();
// End Cross Module References
	void UPSOneUserCenterRightCellAvatar::StaticRegisterNativesUPSOneUserCenterRightCellAvatar()
	{
	}
	UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_NoRegister()
	{
		return UPSOneUserCenterRightCellAvatar::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ImageIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ImageIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AvatarImage_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_AvatarImage;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneButtonBase,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n * \n */" },
		{ "IncludePath", "Views/PSOneUserCenterRightCellAvatar.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAvatar.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::NewProp_ImageIcon_MetaData[] = {
		{ "Category", "PSOneUserCenterRightCellAvatar" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAvatar.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::NewProp_ImageIcon = { "ImageIcon", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellAvatar, ImageIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::NewProp_ImageIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::NewProp_ImageIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::NewProp_AvatarImage_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterRightCellAvatar.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::NewProp_AvatarImage = { "AvatarImage", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterRightCellAvatar, AvatarImage), Z_Construct_UClass_UPSOneAvatarImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::NewProp_AvatarImage_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::NewProp_AvatarImage_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::NewProp_ImageIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::NewProp_AvatarImage,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterRightCellAvatar>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::ClassParams = {
		&UPSOneUserCenterRightCellAvatar::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::PropPointers),
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneUserCenterRightCellAvatar()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneUserCenterRightCellAvatar_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneUserCenterRightCellAvatar, 2584118872);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneUserCenterRightCellAvatar>()
	{
		return UPSOneUserCenterRightCellAvatar::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneUserCenterRightCellAvatar(Z_Construct_UClass_UPSOneUserCenterRightCellAvatar, &UPSOneUserCenterRightCellAvatar::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneUserCenterRightCellAvatar"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterRightCellAvatar);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
