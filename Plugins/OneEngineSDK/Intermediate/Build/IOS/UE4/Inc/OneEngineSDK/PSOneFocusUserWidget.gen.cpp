// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneFocusUserWidget.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneFocusUserWidget() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneSaveFocusWidgetInterface();
	COREUOBJECT_API UClass* Z_Construct_UClass_UInterface();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	UMG_API UClass* Z_Construct_UClass_UUserWidget();
// End Cross Module References
	void UPSOneSaveFocusWidgetInterface::StaticRegisterNativesUPSOneSaveFocusWidgetInterface()
	{
	}
	UClass* Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_NoRegister()
	{
		return UPSOneSaveFocusWidgetInterface::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UInterface,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics::Class_MetaDataParams[] = {
		{ "ModuleRelativePath", "Private/Views/PSOneFocusUserWidget.h" },
	};
#endif
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<IPSOneSaveFocusWidgetInterface>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics::ClassParams = {
		&UPSOneSaveFocusWidgetInterface::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		nullptr,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		0,
		0,
		0x000840A1u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneSaveFocusWidgetInterface()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneSaveFocusWidgetInterface_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneSaveFocusWidgetInterface, 2191402897);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneSaveFocusWidgetInterface>()
	{
		return UPSOneSaveFocusWidgetInterface::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneSaveFocusWidgetInterface(Z_Construct_UClass_UPSOneSaveFocusWidgetInterface, &UPSOneSaveFocusWidgetInterface::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneSaveFocusWidgetInterface"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneSaveFocusWidgetInterface);
	void UPSOneFocusUserWidget::StaticRegisterNativesUPSOneFocusUserWidget()
	{
	}
	UClass* Z_Construct_UClass_UPSOneFocusUserWidget_NoRegister()
	{
		return UPSOneFocusUserWidget::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneFocusUserWidget_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneFocusUserWidget_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneFocusUserWidget_Statics::Class_MetaDataParams[] = {
		{ "IncludePath", "Views/PSOneFocusUserWidget.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneFocusUserWidget.h" },
	};
#endif
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneFocusUserWidget_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneFocusUserWidget>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneFocusUserWidget_Statics::ClassParams = {
		&UPSOneFocusUserWidget::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		nullptr,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		0,
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneFocusUserWidget_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneFocusUserWidget_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneFocusUserWidget()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneFocusUserWidget_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneFocusUserWidget, 2505065879);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneFocusUserWidget>()
	{
		return UPSOneFocusUserWidget::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneFocusUserWidget(Z_Construct_UClass_UPSOneFocusUserWidget, &UPSOneFocusUserWidget::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneFocusUserWidget"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneFocusUserWidget);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
