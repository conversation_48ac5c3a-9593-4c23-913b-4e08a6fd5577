// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Public/OneEngineSDKHelper.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeOneEngineSDKHelper() {}
// Cross Module References
	ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneNaverGameType();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockType();
	ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockResult();
	ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneEngineSDKRegionType();
	ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneShareType();
	ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneShareAppTarget();
	ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneScreenOrientation();
	ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneAIHelpType();
	ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOnePermissionType();
	ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneResEventState();
	ONEENGINESDK_API UEnum* Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FUserIpInfo();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneActiveQualificationInfo();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePushNotDisturbInfo();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePushMessage();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePushTypeInfo();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePushStatus();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneShareData();
	ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneShareWeiboSuperData();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePermissionInfo();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneURCRoleInfo();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneAntiAddictionInfo();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneDeviceInfo();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneUserLocationInfo();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneRoleInfo();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOnePaymentInfo();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneProductInfo();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneUserInfo();
	ONEENGINESDK_API UScriptStruct* Z_Construct_UScriptStruct_FOneUserThirdInfo();
// End Cross Module References
	static UEnum* EOneNaverGameType_StaticEnum()
	{
		static UEnum* Singleton = nullptr;
		if (!Singleton)
		{
			Singleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneNaverGameType, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneNaverGameType"));
		}
		return Singleton;
	}
	template<> ONEENGINESDK_API UEnum* StaticEnum<EOneNaverGameType>()
	{
		return EOneNaverGameType_StaticEnum();
	}
	static FCompiledInDeferEnum Z_CompiledInDeferEnum_UEnum_EOneNaverGameType(EOneNaverGameType_StaticEnum, TEXT("/Script/OneEngineSDK"), TEXT("EOneNaverGameType"), false, nullptr, nullptr);
	uint32 Get_Z_Construct_UEnum_OneEngineSDK_EOneNaverGameType_Hash() { return 881118102U; }
	UEnum* Z_Construct_UEnum_OneEngineSDK_EOneNaverGameType()
	{
#if WITH_HOT_RELOAD
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UEnum* ReturnEnum = FindExistingEnumIfHotReloadOrDynamic(Outer, TEXT("EOneNaverGameType"), 0, Get_Z_Construct_UEnum_OneEngineSDK_EOneNaverGameType_Hash(), false);
#else
		static UEnum* ReturnEnum = nullptr;
#endif // WITH_HOT_RELOAD
		if (!ReturnEnum)
		{
			static const UE4CodeGen_Private::FEnumeratorParam Enumerators[] = {
				{ "EOneNaverGameType::Banner", (int64)EOneNaverGameType::Banner },
				{ "EOneNaverGameType::Sorry", (int64)EOneNaverGameType::Sorry },
				{ "EOneNaverGameType::Board", (int64)EOneNaverGameType::Board },
				{ "EOneNaverGameType::Feed", (int64)EOneNaverGameType::Feed },
			};
#if WITH_METADATA
			const UE4CodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
				{ "Banner.Name", "EOneNaverGameType::Banner" },
				{ "BlueprintType", "true" },
				{ "Board.Name", "EOneNaverGameType::Board" },
				{ "Feed.Name", "EOneNaverGameType::Feed" },
				{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
				{ "Sorry.Name", "EOneNaverGameType::Sorry" },
			};
#endif
			static const UE4CodeGen_Private::FEnumParams EnumParams = {
				(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
				nullptr,
				"EOneNaverGameType",
				"EOneNaverGameType",
				Enumerators,
				UE_ARRAY_COUNT(Enumerators),
				RF_Public|RF_Transient|RF_MarkAsNative,
				EEnumFlags::None,
				UE4CodeGen_Private::EDynamicType::NotDynamic,
				(uint8)UEnum::ECppForm::EnumClass,
				METADATA_PARAMS(Enum_MetaDataParams, UE_ARRAY_COUNT(Enum_MetaDataParams))
			};
			UE4CodeGen_Private::ConstructUEnum(ReturnEnum, EnumParams);
		}
		return ReturnEnum;
	}
	static UEnum* EOneUnlockSafeLockType_StaticEnum()
	{
		static UEnum* Singleton = nullptr;
		if (!Singleton)
		{
			Singleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockType, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneUnlockSafeLockType"));
		}
		return Singleton;
	}
	template<> ONEENGINESDK_API UEnum* StaticEnum<EOneUnlockSafeLockType>()
	{
		return EOneUnlockSafeLockType_StaticEnum();
	}
	static FCompiledInDeferEnum Z_CompiledInDeferEnum_UEnum_EOneUnlockSafeLockType(EOneUnlockSafeLockType_StaticEnum, TEXT("/Script/OneEngineSDK"), TEXT("EOneUnlockSafeLockType"), false, nullptr, nullptr);
	uint32 Get_Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockType_Hash() { return 1548064503U; }
	UEnum* Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockType()
	{
#if WITH_HOT_RELOAD
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UEnum* ReturnEnum = FindExistingEnumIfHotReloadOrDynamic(Outer, TEXT("EOneUnlockSafeLockType"), 0, Get_Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockType_Hash(), false);
#else
		static UEnum* ReturnEnum = nullptr;
#endif // WITH_HOT_RELOAD
		if (!ReturnEnum)
		{
			static const UE4CodeGen_Private::FEnumeratorParam Enumerators[] = {
				{ "EOneUnlockSafeLockType::PushNotification", (int64)EOneUnlockSafeLockType::PushNotification },
				{ "EOneUnlockSafeLockType::DynamicCode", (int64)EOneUnlockSafeLockType::DynamicCode },
			};
#if WITH_METADATA
			const UE4CodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
				{ "BlueprintType", "true" },
				{ "Comment", "// \xe5\xae\x89\xe5\x85\xa8\xe9\x94\x81\xe8\xa7\xa3\xe9\x94\x81\xe6\x96\xb9\xe5\xbc\x8f\xe6\x9e\x9a\xe4\xb8\xbe\xe7\xb1\xbb\xe5\x9e\x8b\n" },
				{ "DynamicCode.Comment", "// \xe6\x8e\xa8\xe9\x80\x81\n" },
				{ "DynamicCode.Name", "EOneUnlockSafeLockType::DynamicCode" },
				{ "DynamicCode.ToolTip", "\xe6\x8e\xa8\xe9\x80\x81" },
				{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
				{ "PushNotification.Name", "EOneUnlockSafeLockType::PushNotification" },
				{ "ToolTip", "\xe5\xae\x89\xe5\x85\xa8\xe9\x94\x81\xe8\xa7\xa3\xe9\x94\x81\xe6\x96\xb9\xe5\xbc\x8f\xe6\x9e\x9a\xe4\xb8\xbe\xe7\xb1\xbb\xe5\x9e\x8b" },
			};
#endif
			static const UE4CodeGen_Private::FEnumParams EnumParams = {
				(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
				nullptr,
				"EOneUnlockSafeLockType",
				"EOneUnlockSafeLockType",
				Enumerators,
				UE_ARRAY_COUNT(Enumerators),
				RF_Public|RF_Transient|RF_MarkAsNative,
				EEnumFlags::None,
				UE4CodeGen_Private::EDynamicType::NotDynamic,
				(uint8)UEnum::ECppForm::EnumClass,
				METADATA_PARAMS(Enum_MetaDataParams, UE_ARRAY_COUNT(Enum_MetaDataParams))
			};
			UE4CodeGen_Private::ConstructUEnum(ReturnEnum, EnumParams);
		}
		return ReturnEnum;
	}
	static UEnum* EOneUnlockSafeLockResult_StaticEnum()
	{
		static UEnum* Singleton = nullptr;
		if (!Singleton)
		{
			Singleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockResult, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneUnlockSafeLockResult"));
		}
		return Singleton;
	}
	template<> ONEENGINESDK_API UEnum* StaticEnum<EOneUnlockSafeLockResult>()
	{
		return EOneUnlockSafeLockResult_StaticEnum();
	}
	static FCompiledInDeferEnum Z_CompiledInDeferEnum_UEnum_EOneUnlockSafeLockResult(EOneUnlockSafeLockResult_StaticEnum, TEXT("/Script/OneEngineSDK"), TEXT("EOneUnlockSafeLockResult"), false, nullptr, nullptr);
	uint32 Get_Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockResult_Hash() { return 198846712U; }
	UEnum* Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockResult()
	{
#if WITH_HOT_RELOAD
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UEnum* ReturnEnum = FindExistingEnumIfHotReloadOrDynamic(Outer, TEXT("EOneUnlockSafeLockResult"), 0, Get_Z_Construct_UEnum_OneEngineSDK_EOneUnlockSafeLockResult_Hash(), false);
#else
		static UEnum* ReturnEnum = nullptr;
#endif // WITH_HOT_RELOAD
		if (!ReturnEnum)
		{
			static const UE4CodeGen_Private::FEnumeratorParam Enumerators[] = {
				{ "EOneUnlockSafeLockResult::WaitingToUnlock", (int64)EOneUnlockSafeLockResult::WaitingToUnlock },
				{ "EOneUnlockSafeLockResult::UnlockSucceeded", (int64)EOneUnlockSafeLockResult::UnlockSucceeded },
				{ "EOneUnlockSafeLockResult::UnlockTimedOut", (int64)EOneUnlockSafeLockResult::UnlockTimedOut },
				{ "EOneUnlockSafeLockResult::UnlockFailed", (int64)EOneUnlockSafeLockResult::UnlockFailed },
			};
#if WITH_METADATA
			const UE4CodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
				{ "BlueprintType", "true" },
				{ "Comment", "// \xe5\xae\x9a\xe4\xb9\x89\xe8\xa7\xa3\xe9\x94\x81\xe7\xbb\x93\xe6\x9e\x9c\xe7\x9a\x84\xe6\x9e\x9a\xe4\xb8\xbe\xe7\xb1\xbb\xe5\x9e\x8b\n" },
				{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
				{ "ToolTip", "\xe5\xae\x9a\xe4\xb9\x89\xe8\xa7\xa3\xe9\x94\x81\xe7\xbb\x93\xe6\x9e\x9c\xe7\x9a\x84\xe6\x9e\x9a\xe4\xb8\xbe\xe7\xb1\xbb\xe5\x9e\x8b" },
				{ "UnlockFailed.Comment", "// \xe8\xa7\xa3\xe9\x94\x81\xe8\xb6\x85\xe6\x97\xb6\xef\xbc\x8c\xe7\x94\xa8\xe6\x88\xb7\xe6\x9c\xaa\xe5\x9c\xa8\xe8\xa7\x84\xe5\xae\x9a\xe6\x97\xb6\xe9\x97\xb4\xe5\x86\x85\xe8\xa7\xa3\xe9\x94\x81(\xe8\xbd\xae\xe8\xaf\xa2\xe6\x97\xb6\xe9\x97\xb4\xe5\x88\xb0)\n" },
				{ "UnlockFailed.Name", "EOneUnlockSafeLockResult::UnlockFailed" },
				{ "UnlockFailed.ToolTip", "\xe8\xa7\xa3\xe9\x94\x81\xe8\xb6\x85\xe6\x97\xb6\xef\xbc\x8c\xe7\x94\xa8\xe6\x88\xb7\xe6\x9c\xaa\xe5\x9c\xa8\xe8\xa7\x84\xe5\xae\x9a\xe6\x97\xb6\xe9\x97\xb4\xe5\x86\x85\xe8\xa7\xa3\xe9\x94\x81(\xe8\xbd\xae\xe8\xaf\xa2\xe6\x97\xb6\xe9\x97\xb4\xe5\x88\xb0)" },
				{ "UnlockSucceeded.Comment", "// \xe5\xb7\xb2\xe8\xa7\xa6\xe5\x8f\x91\xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf\xe6\x8e\xa8\xe9\x80\x81\xef\xbc\x8c\xe7\xad\x89\xe5\xbe\x85\xe7\x94\xa8\xe6\x88\xb7\xe8\xa7\xa3\xe9\x94\x81\n" },
				{ "UnlockSucceeded.Name", "EOneUnlockSafeLockResult::UnlockSucceeded" },
				{ "UnlockSucceeded.ToolTip", "\xe5\xb7\xb2\xe8\xa7\xa6\xe5\x8f\x91\xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf\xe6\x8e\xa8\xe9\x80\x81\xef\xbc\x8c\xe7\xad\x89\xe5\xbe\x85\xe7\x94\xa8\xe6\x88\xb7\xe8\xa7\xa3\xe9\x94\x81" },
				{ "UnlockTimedOut.Comment", "// \xe8\xa7\xa3\xe9\x94\x81\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8c\xe6\x84\x8f\xe8\xa7\xa3\xe9\x94\x81\n" },
				{ "UnlockTimedOut.Name", "EOneUnlockSafeLockResult::UnlockTimedOut" },
				{ "UnlockTimedOut.ToolTip", "\xe8\xa7\xa3\xe9\x94\x81\xe6\x88\x90\xe5\x8a\x9f\xef\xbc\x8c\xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8c\xe6\x84\x8f\xe8\xa7\xa3\xe9\x94\x81" },
				{ "WaitingToUnlock.Name", "EOneUnlockSafeLockResult::WaitingToUnlock" },
			};
#endif
			static const UE4CodeGen_Private::FEnumParams EnumParams = {
				(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
				nullptr,
				"EOneUnlockSafeLockResult",
				"EOneUnlockSafeLockResult",
				Enumerators,
				UE_ARRAY_COUNT(Enumerators),
				RF_Public|RF_Transient|RF_MarkAsNative,
				EEnumFlags::None,
				UE4CodeGen_Private::EDynamicType::NotDynamic,
				(uint8)UEnum::ECppForm::EnumClass,
				METADATA_PARAMS(Enum_MetaDataParams, UE_ARRAY_COUNT(Enum_MetaDataParams))
			};
			UE4CodeGen_Private::ConstructUEnum(ReturnEnum, EnumParams);
		}
		return ReturnEnum;
	}
	static UEnum* EOneEngineSDKRegionType_StaticEnum()
	{
		static UEnum* Singleton = nullptr;
		if (!Singleton)
		{
			Singleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneEngineSDKRegionType, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneEngineSDKRegionType"));
		}
		return Singleton;
	}
	template<> ONEENGINESDK_API UEnum* StaticEnum<EOneEngineSDKRegionType>()
	{
		return EOneEngineSDKRegionType_StaticEnum();
	}
	static FCompiledInDeferEnum Z_CompiledInDeferEnum_UEnum_EOneEngineSDKRegionType(EOneEngineSDKRegionType_StaticEnum, TEXT("/Script/OneEngineSDK"), TEXT("EOneEngineSDKRegionType"), false, nullptr, nullptr);
	uint32 Get_Z_Construct_UEnum_OneEngineSDK_EOneEngineSDKRegionType_Hash() { return 663177017U; }
	UEnum* Z_Construct_UEnum_OneEngineSDK_EOneEngineSDKRegionType()
	{
#if WITH_HOT_RELOAD
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UEnum* ReturnEnum = FindExistingEnumIfHotReloadOrDynamic(Outer, TEXT("EOneEngineSDKRegionType"), 0, Get_Z_Construct_UEnum_OneEngineSDK_EOneEngineSDKRegionType_Hash(), false);
#else
		static UEnum* ReturnEnum = nullptr;
#endif // WITH_HOT_RELOAD
		if (!ReturnEnum)
		{
			static const UE4CodeGen_Private::FEnumeratorParam Enumerators[] = {
				{ "EOneEngineSDKRegionType::Mainland", (int64)EOneEngineSDKRegionType::Mainland },
				{ "EOneEngineSDKRegionType::Oversea", (int64)EOneEngineSDKRegionType::Oversea },
			};
#if WITH_METADATA
			const UE4CodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
				{ "BlueprintType", "true" },
				{ "Mainland.Name", "EOneEngineSDKRegionType::Mainland" },
				{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
				{ "Oversea.Comment", "//\xe5\xa4\xa7\xe9\x99\x86\n" },
				{ "Oversea.Name", "EOneEngineSDKRegionType::Oversea" },
				{ "Oversea.ToolTip", "\xe5\xa4\xa7\xe9\x99\x86" },
			};
#endif
			static const UE4CodeGen_Private::FEnumParams EnumParams = {
				(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
				nullptr,
				"EOneEngineSDKRegionType",
				"EOneEngineSDKRegionType",
				Enumerators,
				UE_ARRAY_COUNT(Enumerators),
				RF_Public|RF_Transient|RF_MarkAsNative,
				EEnumFlags::None,
				UE4CodeGen_Private::EDynamicType::NotDynamic,
				(uint8)UEnum::ECppForm::EnumClass,
				METADATA_PARAMS(Enum_MetaDataParams, UE_ARRAY_COUNT(Enum_MetaDataParams))
			};
			UE4CodeGen_Private::ConstructUEnum(ReturnEnum, EnumParams);
		}
		return ReturnEnum;
	}
	static UEnum* EOneShareType_StaticEnum()
	{
		static UEnum* Singleton = nullptr;
		if (!Singleton)
		{
			Singleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneShareType, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneShareType"));
		}
		return Singleton;
	}
	template<> ONEENGINESDK_API UEnum* StaticEnum<EOneShareType>()
	{
		return EOneShareType_StaticEnum();
	}
	static FCompiledInDeferEnum Z_CompiledInDeferEnum_UEnum_EOneShareType(EOneShareType_StaticEnum, TEXT("/Script/OneEngineSDK"), TEXT("EOneShareType"), false, nullptr, nullptr);
	uint32 Get_Z_Construct_UEnum_OneEngineSDK_EOneShareType_Hash() { return 1497909789U; }
	UEnum* Z_Construct_UEnum_OneEngineSDK_EOneShareType()
	{
#if WITH_HOT_RELOAD
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UEnum* ReturnEnum = FindExistingEnumIfHotReloadOrDynamic(Outer, TEXT("EOneShareType"), 0, Get_Z_Construct_UEnum_OneEngineSDK_EOneShareType_Hash(), false);
#else
		static UEnum* ReturnEnum = nullptr;
#endif // WITH_HOT_RELOAD
		if (!ReturnEnum)
		{
			static const UE4CodeGen_Private::FEnumeratorParam Enumerators[] = {
				{ "EOneShareType::Image", (int64)EOneShareType::Image },
				{ "EOneShareType::WebPage", (int64)EOneShareType::WebPage },
				{ "EOneShareType::Text", (int64)EOneShareType::Text },
				{ "EOneShareType::ImageSnapShot", (int64)EOneShareType::ImageSnapShot },
			};
#if WITH_METADATA
			const UE4CodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
				{ "BlueprintType", "true" },
				{ "Comment", "// \xe5\x88\x86\xe4\xba\xab\xe5\x86\x85\xe5\xae\xb9\xe7\xb1\xbb\xe5\x9e\x8b\n" },
				{ "Image.Name", "EOneShareType::Image" },
				{ "ImageSnapShot.Comment", "//\xe6\x96\x87\xe6\x9c\xac\n" },
				{ "ImageSnapShot.Name", "EOneShareType::ImageSnapShot" },
				{ "ImageSnapShot.ToolTip", "\xe6\x96\x87\xe6\x9c\xac" },
				{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
				{ "Text.Comment", "//\xe7\xbd\x91\xe9\xa1\xb5\n" },
				{ "Text.Name", "EOneShareType::Text" },
				{ "Text.ToolTip", "\xe7\xbd\x91\xe9\xa1\xb5" },
				{ "ToolTip", "\xe5\x88\x86\xe4\xba\xab\xe5\x86\x85\xe5\xae\xb9\xe7\xb1\xbb\xe5\x9e\x8b" },
				{ "WebPage.Comment", "//\xe5\x9b\xbe\xe7\x89\x87\n" },
				{ "WebPage.Name", "EOneShareType::WebPage" },
				{ "WebPage.ToolTip", "\xe5\x9b\xbe\xe7\x89\x87" },
			};
#endif
			static const UE4CodeGen_Private::FEnumParams EnumParams = {
				(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
				nullptr,
				"EOneShareType",
				"EOneShareType",
				Enumerators,
				UE_ARRAY_COUNT(Enumerators),
				RF_Public|RF_Transient|RF_MarkAsNative,
				EEnumFlags::None,
				UE4CodeGen_Private::EDynamicType::NotDynamic,
				(uint8)UEnum::ECppForm::EnumClass,
				METADATA_PARAMS(Enum_MetaDataParams, UE_ARRAY_COUNT(Enum_MetaDataParams))
			};
			UE4CodeGen_Private::ConstructUEnum(ReturnEnum, EnumParams);
		}
		return ReturnEnum;
	}
	static UEnum* EOneShareAppTarget_StaticEnum()
	{
		static UEnum* Singleton = nullptr;
		if (!Singleton)
		{
			Singleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneShareAppTarget, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneShareAppTarget"));
		}
		return Singleton;
	}
	template<> ONEENGINESDK_API UEnum* StaticEnum<EOneShareAppTarget>()
	{
		return EOneShareAppTarget_StaticEnum();
	}
	static FCompiledInDeferEnum Z_CompiledInDeferEnum_UEnum_EOneShareAppTarget(EOneShareAppTarget_StaticEnum, TEXT("/Script/OneEngineSDK"), TEXT("EOneShareAppTarget"), false, nullptr, nullptr);
	uint32 Get_Z_Construct_UEnum_OneEngineSDK_EOneShareAppTarget_Hash() { return 2493733766U; }
	UEnum* Z_Construct_UEnum_OneEngineSDK_EOneShareAppTarget()
	{
#if WITH_HOT_RELOAD
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UEnum* ReturnEnum = FindExistingEnumIfHotReloadOrDynamic(Outer, TEXT("EOneShareAppTarget"), 0, Get_Z_Construct_UEnum_OneEngineSDK_EOneShareAppTarget_Hash(), false);
#else
		static UEnum* ReturnEnum = nullptr;
#endif // WITH_HOT_RELOAD
		if (!ReturnEnum)
		{
			static const UE4CodeGen_Private::FEnumeratorParam Enumerators[] = {
				{ "EOneShareAppTarget::PE_WeChatSession", (int64)EOneShareAppTarget::PE_WeChatSession },
				{ "EOneShareAppTarget::PE_WeChatMoment", (int64)EOneShareAppTarget::PE_WeChatMoment },
				{ "EOneShareAppTarget::PE_QQ", (int64)EOneShareAppTarget::PE_QQ },
				{ "EOneShareAppTarget::PE_QZone", (int64)EOneShareAppTarget::PE_QZone },
				{ "EOneShareAppTarget::PE_Weibo", (int64)EOneShareAppTarget::PE_Weibo },
				{ "EOneShareAppTarget::PE_Bilibili", (int64)EOneShareAppTarget::PE_Bilibili },
				{ "EOneShareAppTarget::PE_Facebook", (int64)EOneShareAppTarget::PE_Facebook },
				{ "EOneShareAppTarget::PE_VK", (int64)EOneShareAppTarget::PE_VK },
				{ "EOneShareAppTarget::PE_Instagram", (int64)EOneShareAppTarget::PE_Instagram },
				{ "EOneShareAppTarget::PE_Twitter", (int64)EOneShareAppTarget::PE_Twitter },
				{ "EOneShareAppTarget::PE_Line", (int64)EOneShareAppTarget::PE_Line },
				{ "EOneShareAppTarget::PE_NaverGame", (int64)EOneShareAppTarget::PE_NaverGame },
				{ "EOneShareAppTarget::PE_TikTok", (int64)EOneShareAppTarget::PE_TikTok },
				{ "EOneShareAppTarget::PE_Discord", (int64)EOneShareAppTarget::PE_Discord },
				{ "EOneShareAppTarget::PE_Telegram", (int64)EOneShareAppTarget::PE_Telegram },
			};
#if WITH_METADATA
			const UE4CodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
				{ "BlueprintType", "true" },
				{ "Comment", "// \xe5\x88\x86\xe4\xba\xab\xe5\xb9\xb3\xe5\x8f\xb0\n" },
				{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
				{ "PE_Bilibili.Name", "EOneShareAppTarget::PE_Bilibili" },
				{ "PE_Discord.Name", "EOneShareAppTarget::PE_Discord" },
				{ "PE_Facebook.Name", "EOneShareAppTarget::PE_Facebook" },
				{ "PE_Instagram.Name", "EOneShareAppTarget::PE_Instagram" },
				{ "PE_Line.Name", "EOneShareAppTarget::PE_Line" },
				{ "PE_NaverGame.Name", "EOneShareAppTarget::PE_NaverGame" },
				{ "PE_QQ.Name", "EOneShareAppTarget::PE_QQ" },
				{ "PE_QZone.Name", "EOneShareAppTarget::PE_QZone" },
				{ "PE_Telegram.Name", "EOneShareAppTarget::PE_Telegram" },
				{ "PE_TikTok.Name", "EOneShareAppTarget::PE_TikTok" },
				{ "PE_Twitter.Name", "EOneShareAppTarget::PE_Twitter" },
				{ "PE_VK.Name", "EOneShareAppTarget::PE_VK" },
				{ "PE_WeChatMoment.Name", "EOneShareAppTarget::PE_WeChatMoment" },
				{ "PE_WeChatSession.Name", "EOneShareAppTarget::PE_WeChatSession" },
				{ "PE_Weibo.Name", "EOneShareAppTarget::PE_Weibo" },
				{ "ToolTip", "\xe5\x88\x86\xe4\xba\xab\xe5\xb9\xb3\xe5\x8f\xb0" },
			};
#endif
			static const UE4CodeGen_Private::FEnumParams EnumParams = {
				(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
				nullptr,
				"EOneShareAppTarget",
				"EOneShareAppTarget",
				Enumerators,
				UE_ARRAY_COUNT(Enumerators),
				RF_Public|RF_Transient|RF_MarkAsNative,
				EEnumFlags::None,
				UE4CodeGen_Private::EDynamicType::NotDynamic,
				(uint8)UEnum::ECppForm::EnumClass,
				METADATA_PARAMS(Enum_MetaDataParams, UE_ARRAY_COUNT(Enum_MetaDataParams))
			};
			UE4CodeGen_Private::ConstructUEnum(ReturnEnum, EnumParams);
		}
		return ReturnEnum;
	}
	static UEnum* EOneScreenOrientation_StaticEnum()
	{
		static UEnum* Singleton = nullptr;
		if (!Singleton)
		{
			Singleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneScreenOrientation, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneScreenOrientation"));
		}
		return Singleton;
	}
	template<> ONEENGINESDK_API UEnum* StaticEnum<EOneScreenOrientation>()
	{
		return EOneScreenOrientation_StaticEnum();
	}
	static FCompiledInDeferEnum Z_CompiledInDeferEnum_UEnum_EOneScreenOrientation(EOneScreenOrientation_StaticEnum, TEXT("/Script/OneEngineSDK"), TEXT("EOneScreenOrientation"), false, nullptr, nullptr);
	uint32 Get_Z_Construct_UEnum_OneEngineSDK_EOneScreenOrientation_Hash() { return 1010454056U; }
	UEnum* Z_Construct_UEnum_OneEngineSDK_EOneScreenOrientation()
	{
#if WITH_HOT_RELOAD
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UEnum* ReturnEnum = FindExistingEnumIfHotReloadOrDynamic(Outer, TEXT("EOneScreenOrientation"), 0, Get_Z_Construct_UEnum_OneEngineSDK_EOneScreenOrientation_Hash(), false);
#else
		static UEnum* ReturnEnum = nullptr;
#endif // WITH_HOT_RELOAD
		if (!ReturnEnum)
		{
			static const UE4CodeGen_Private::FEnumeratorParam Enumerators[] = {
				{ "EOneScreenOrientation::Unknown", (int64)EOneScreenOrientation::Unknown },
				{ "EOneScreenOrientation::Portrait", (int64)EOneScreenOrientation::Portrait },
				{ "EOneScreenOrientation::Landscape", (int64)EOneScreenOrientation::Landscape },
			};
#if WITH_METADATA
			const UE4CodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
				{ "BlueprintType", "true" },
				{ "Comment", "// \xe5\xb1\x8f\xe5\xb9\x95\xe6\x96\xb9\xe5\x90\x91\n" },
				{ "Landscape.Name", "EOneScreenOrientation::Landscape" },
				{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
				{ "Portrait.Name", "EOneScreenOrientation::Portrait" },
				{ "ToolTip", "\xe5\xb1\x8f\xe5\xb9\x95\xe6\x96\xb9\xe5\x90\x91" },
				{ "Unknown.Name", "EOneScreenOrientation::Unknown" },
			};
#endif
			static const UE4CodeGen_Private::FEnumParams EnumParams = {
				(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
				nullptr,
				"EOneScreenOrientation",
				"EOneScreenOrientation",
				Enumerators,
				UE_ARRAY_COUNT(Enumerators),
				RF_Public|RF_Transient|RF_MarkAsNative,
				EEnumFlags::None,
				UE4CodeGen_Private::EDynamicType::NotDynamic,
				(uint8)UEnum::ECppForm::EnumClass,
				METADATA_PARAMS(Enum_MetaDataParams, UE_ARRAY_COUNT(Enum_MetaDataParams))
			};
			UE4CodeGen_Private::ConstructUEnum(ReturnEnum, EnumParams);
		}
		return ReturnEnum;
	}
	static UEnum* EOneAIHelpType_StaticEnum()
	{
		static UEnum* Singleton = nullptr;
		if (!Singleton)
		{
			Singleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneAIHelpType, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneAIHelpType"));
		}
		return Singleton;
	}
	template<> ONEENGINESDK_API UEnum* StaticEnum<EOneAIHelpType>()
	{
		return EOneAIHelpType_StaticEnum();
	}
	static FCompiledInDeferEnum Z_CompiledInDeferEnum_UEnum_EOneAIHelpType(EOneAIHelpType_StaticEnum, TEXT("/Script/OneEngineSDK"), TEXT("EOneAIHelpType"), false, nullptr, nullptr);
	uint32 Get_Z_Construct_UEnum_OneEngineSDK_EOneAIHelpType_Hash() { return 1228029232U; }
	UEnum* Z_Construct_UEnum_OneEngineSDK_EOneAIHelpType()
	{
#if WITH_HOT_RELOAD
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UEnum* ReturnEnum = FindExistingEnumIfHotReloadOrDynamic(Outer, TEXT("EOneAIHelpType"), 0, Get_Z_Construct_UEnum_OneEngineSDK_EOneAIHelpType_Hash(), false);
#else
		static UEnum* ReturnEnum = nullptr;
#endif // WITH_HOT_RELOAD
		if (!ReturnEnum)
		{
			static const UE4CodeGen_Private::FEnumeratorParam Enumerators[] = {
				{ "EOneAIHelpType::Unknown", (int64)EOneAIHelpType::Unknown },
				{ "EOneAIHelpType::RobotChat", (int64)EOneAIHelpType::RobotChat },
				{ "EOneAIHelpType::FAQ", (int64)EOneAIHelpType::FAQ },
			};
#if WITH_METADATA
			const UE4CodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
				{ "BlueprintType", "true" },
				{ "Comment", "// AIHelp\xe5\xae\xa2\xe6\x9c\x8d\xe7\xb1\xbb\xe5\x9e\x8b\n" },
				{ "FAQ.Name", "EOneAIHelpType::FAQ" },
				{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
				{ "RobotChat.Name", "EOneAIHelpType::RobotChat" },
				{ "ToolTip", "AIHelp\xe5\xae\xa2\xe6\x9c\x8d\xe7\xb1\xbb\xe5\x9e\x8b" },
				{ "Unknown.Name", "EOneAIHelpType::Unknown" },
			};
#endif
			static const UE4CodeGen_Private::FEnumParams EnumParams = {
				(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
				nullptr,
				"EOneAIHelpType",
				"EOneAIHelpType",
				Enumerators,
				UE_ARRAY_COUNT(Enumerators),
				RF_Public|RF_Transient|RF_MarkAsNative,
				EEnumFlags::None,
				UE4CodeGen_Private::EDynamicType::NotDynamic,
				(uint8)UEnum::ECppForm::EnumClass,
				METADATA_PARAMS(Enum_MetaDataParams, UE_ARRAY_COUNT(Enum_MetaDataParams))
			};
			UE4CodeGen_Private::ConstructUEnum(ReturnEnum, EnumParams);
		}
		return ReturnEnum;
	}
	static UEnum* EOnePermissionType_StaticEnum()
	{
		static UEnum* Singleton = nullptr;
		if (!Singleton)
		{
			Singleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOnePermissionType, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOnePermissionType"));
		}
		return Singleton;
	}
	template<> ONEENGINESDK_API UEnum* StaticEnum<EOnePermissionType>()
	{
		return EOnePermissionType_StaticEnum();
	}
	static FCompiledInDeferEnum Z_CompiledInDeferEnum_UEnum_EOnePermissionType(EOnePermissionType_StaticEnum, TEXT("/Script/OneEngineSDK"), TEXT("EOnePermissionType"), false, nullptr, nullptr);
	uint32 Get_Z_Construct_UEnum_OneEngineSDK_EOnePermissionType_Hash() { return 1437656063U; }
	UEnum* Z_Construct_UEnum_OneEngineSDK_EOnePermissionType()
	{
#if WITH_HOT_RELOAD
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UEnum* ReturnEnum = FindExistingEnumIfHotReloadOrDynamic(Outer, TEXT("EOnePermissionType"), 0, Get_Z_Construct_UEnum_OneEngineSDK_EOnePermissionType_Hash(), false);
#else
		static UEnum* ReturnEnum = nullptr;
#endif // WITH_HOT_RELOAD
		if (!ReturnEnum)
		{
			static const UE4CodeGen_Private::FEnumeratorParam Enumerators[] = {
				{ "EOnePermissionType::Clipboard", (int64)EOnePermissionType::Clipboard },
				{ "EOnePermissionType::ReadExternalStorage", (int64)EOnePermissionType::ReadExternalStorage },
				{ "EOnePermissionType::WriteExternalStorage", (int64)EOnePermissionType::WriteExternalStorage },
				{ "EOnePermissionType::Camera", (int64)EOnePermissionType::Camera },
				{ "EOnePermissionType::RecordAudio", (int64)EOnePermissionType::RecordAudio },
				{ "EOnePermissionType::CoarseLocation", (int64)EOnePermissionType::CoarseLocation },
				{ "EOnePermissionType::FineLocation", (int64)EOnePermissionType::FineLocation },
				{ "EOnePermissionType::CallPhone", (int64)EOnePermissionType::CallPhone },
				{ "EOnePermissionType::ReadContacts", (int64)EOnePermissionType::ReadContacts },
				{ "EOnePermissionType::ReadSms", (int64)EOnePermissionType::ReadSms },
				{ "EOnePermissionType::ReadCalendar", (int64)EOnePermissionType::ReadCalendar },
				{ "EOnePermissionType::BodySensors", (int64)EOnePermissionType::BodySensors },
				{ "EOnePermissionType::Notification", (int64)EOnePermissionType::Notification },
				{ "EOnePermissionType::ATTTrack", (int64)EOnePermissionType::ATTTrack },
			};
#if WITH_METADATA
			const UE4CodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
				{ "ATTTrack.Comment", "//\xe9\x80\x9a\xe7\x9f\xa5\xe6\x9d\x83\xe9\x99\x90\n" },
				{ "ATTTrack.Name", "EOnePermissionType::ATTTrack" },
				{ "ATTTrack.ToolTip", "\xe9\x80\x9a\xe7\x9f\xa5\xe6\x9d\x83\xe9\x99\x90" },
				{ "BlueprintType", "true" },
				{ "BodySensors.Comment", "//\xe8\xaf\xbb\xe5\x8f\x96\xe6\x97\xa5\xe5\x8e\x86\n" },
				{ "BodySensors.Name", "EOnePermissionType::BodySensors" },
				{ "BodySensors.ToolTip", "\xe8\xaf\xbb\xe5\x8f\x96\xe6\x97\xa5\xe5\x8e\x86" },
				{ "CallPhone.Comment", "//GPS\xe5\xae\x9a\xe4\xbd\x8d\n" },
				{ "CallPhone.Name", "EOnePermissionType::CallPhone" },
				{ "CallPhone.ToolTip", "GPS\xe5\xae\x9a\xe4\xbd\x8d" },
				{ "Camera.Comment", "//\xe5\x86\x99\xe5\x85\xa5\xe5\xad\x98\xe5\x82\xa8\xe5\x8d\xa1\n" },
				{ "Camera.Name", "EOnePermissionType::Camera" },
				{ "Camera.ToolTip", "\xe5\x86\x99\xe5\x85\xa5\xe5\xad\x98\xe5\x82\xa8\xe5\x8d\xa1" },
				{ "Clipboard.Name", "EOnePermissionType::Clipboard" },
				{ "CoarseLocation.Comment", "//\xe9\xba\xa6\xe5\x85\x8b\xe9\xa3\x8e\xe9\x9f\xb3\xe9\xa2\x91\n" },
				{ "CoarseLocation.Name", "EOnePermissionType::CoarseLocation" },
				{ "CoarseLocation.ToolTip", "\xe9\xba\xa6\xe5\x85\x8b\xe9\xa3\x8e\xe9\x9f\xb3\xe9\xa2\x91" },
				{ "FineLocation.Comment", "//\xe7\xbd\x91\xe7\xbb\x9c\xe5\xae\x9a\xe4\xbd\x8d\n" },
				{ "FineLocation.Name", "EOnePermissionType::FineLocation" },
				{ "FineLocation.ToolTip", "\xe7\xbd\x91\xe7\xbb\x9c\xe5\xae\x9a\xe4\xbd\x8d" },
				{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
				{ "Notification.Comment", "//\xe4\xbc\xa0\xe6\x84\x9f\xe5\x99\xa8\n" },
				{ "Notification.Name", "EOnePermissionType::Notification" },
				{ "Notification.ToolTip", "\xe4\xbc\xa0\xe6\x84\x9f\xe5\x99\xa8" },
				{ "ReadCalendar.Comment", "//\xe8\xaf\xbb\xe5\x8f\x96\xe7\x9f\xad\xe4\xbf\xa1\n" },
				{ "ReadCalendar.Name", "EOnePermissionType::ReadCalendar" },
				{ "ReadCalendar.ToolTip", "\xe8\xaf\xbb\xe5\x8f\x96\xe7\x9f\xad\xe4\xbf\xa1" },
				{ "ReadContacts.Comment", "//\xe6\x89\x93\xe7\x94\xb5\xe8\xaf\x9d\n" },
				{ "ReadContacts.Name", "EOnePermissionType::ReadContacts" },
				{ "ReadContacts.ToolTip", "\xe6\x89\x93\xe7\x94\xb5\xe8\xaf\x9d" },
				{ "ReadExternalStorage.Comment", "//\xe5\x89\xaa\xe8\xb4\xb4\xe6\x9d\xbf\n" },
				{ "ReadExternalStorage.Name", "EOnePermissionType::ReadExternalStorage" },
				{ "ReadExternalStorage.ToolTip", "\xe5\x89\xaa\xe8\xb4\xb4\xe6\x9d\xbf" },
				{ "ReadSms.Comment", "//\xe8\xaf\xbb\xe5\x8f\x96\xe9\x80\x9a\xe8\xae\xaf\xe5\xbd\x95\n" },
				{ "ReadSms.Name", "EOnePermissionType::ReadSms" },
				{ "ReadSms.ToolTip", "\xe8\xaf\xbb\xe5\x8f\x96\xe9\x80\x9a\xe8\xae\xaf\xe5\xbd\x95" },
				{ "RecordAudio.Comment", "//\xe7\x9b\xb8\xe6\x9c\xba\n" },
				{ "RecordAudio.Name", "EOnePermissionType::RecordAudio" },
				{ "RecordAudio.ToolTip", "\xe7\x9b\xb8\xe6\x9c\xba" },
				{ "WriteExternalStorage.Comment", "//\xe8\xaf\xbb\xe5\x8f\x96\xe5\xad\x98\xe5\x82\xa8\xe5\x8d\xa1\n" },
				{ "WriteExternalStorage.Name", "EOnePermissionType::WriteExternalStorage" },
				{ "WriteExternalStorage.ToolTip", "\xe8\xaf\xbb\xe5\x8f\x96\xe5\xad\x98\xe5\x82\xa8\xe5\x8d\xa1" },
			};
#endif
			static const UE4CodeGen_Private::FEnumParams EnumParams = {
				(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
				nullptr,
				"EOnePermissionType",
				"EOnePermissionType",
				Enumerators,
				UE_ARRAY_COUNT(Enumerators),
				RF_Public|RF_Transient|RF_MarkAsNative,
				EEnumFlags::None,
				UE4CodeGen_Private::EDynamicType::NotDynamic,
				(uint8)UEnum::ECppForm::EnumClass,
				METADATA_PARAMS(Enum_MetaDataParams, UE_ARRAY_COUNT(Enum_MetaDataParams))
			};
			UE4CodeGen_Private::ConstructUEnum(ReturnEnum, EnumParams);
		}
		return ReturnEnum;
	}
	static UEnum* EOneResEventState_StaticEnum()
	{
		static UEnum* Singleton = nullptr;
		if (!Singleton)
		{
			Singleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneResEventState, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneResEventState"));
		}
		return Singleton;
	}
	template<> ONEENGINESDK_API UEnum* StaticEnum<EOneResEventState>()
	{
		return EOneResEventState_StaticEnum();
	}
	static FCompiledInDeferEnum Z_CompiledInDeferEnum_UEnum_EOneResEventState(EOneResEventState_StaticEnum, TEXT("/Script/OneEngineSDK"), TEXT("EOneResEventState"), false, nullptr, nullptr);
	uint32 Get_Z_Construct_UEnum_OneEngineSDK_EOneResEventState_Hash() { return 2487436517U; }
	UEnum* Z_Construct_UEnum_OneEngineSDK_EOneResEventState()
	{
#if WITH_HOT_RELOAD
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UEnum* ReturnEnum = FindExistingEnumIfHotReloadOrDynamic(Outer, TEXT("EOneResEventState"), 0, Get_Z_Construct_UEnum_OneEngineSDK_EOneResEventState_Hash(), false);
#else
		static UEnum* ReturnEnum = nullptr;
#endif // WITH_HOT_RELOAD
		if (!ReturnEnum)
		{
			static const UE4CodeGen_Private::FEnumeratorParam Enumerators[] = {
				{ "EOneResEventState::Begin", (int64)EOneResEventState::Begin },
				{ "EOneResEventState::Success", (int64)EOneResEventState::Success },
				{ "EOneResEventState::Failed", (int64)EOneResEventState::Failed },
			};
#if WITH_METADATA
			const UE4CodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
				{ "Begin.Name", "EOneResEventState::Begin" },
				{ "BlueprintType", "true" },
				{ "Comment", "// \xe8\xb5\x84\xe6\xba\x90\xe6\x89\x93\xe7\x82\xb9\xe7\xb1\xbb\xe5\x9e\x8b\n" },
				{ "Failed.Name", "EOneResEventState::Failed" },
				{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
				{ "Success.Name", "EOneResEventState::Success" },
				{ "ToolTip", "\xe8\xb5\x84\xe6\xba\x90\xe6\x89\x93\xe7\x82\xb9\xe7\xb1\xbb\xe5\x9e\x8b" },
			};
#endif
			static const UE4CodeGen_Private::FEnumParams EnumParams = {
				(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
				nullptr,
				"EOneResEventState",
				"EOneResEventState",
				Enumerators,
				UE_ARRAY_COUNT(Enumerators),
				RF_Public|RF_Transient|RF_MarkAsNative,
				EEnumFlags::None,
				UE4CodeGen_Private::EDynamicType::NotDynamic,
				(uint8)UEnum::ECppForm::EnumClass,
				METADATA_PARAMS(Enum_MetaDataParams, UE_ARRAY_COUNT(Enum_MetaDataParams))
			};
			UE4CodeGen_Private::ConstructUEnum(ReturnEnum, EnumParams);
		}
		return ReturnEnum;
	}
	static UEnum* EOneEngineThirdType_StaticEnum()
	{
		static UEnum* Singleton = nullptr;
		if (!Singleton)
		{
			Singleton = GetStaticEnum(Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("EOneEngineThirdType"));
		}
		return Singleton;
	}
	template<> ONEENGINESDK_API UEnum* StaticEnum<EOneEngineThirdType>()
	{
		return EOneEngineThirdType_StaticEnum();
	}
	static FCompiledInDeferEnum Z_CompiledInDeferEnum_UEnum_EOneEngineThirdType(EOneEngineThirdType_StaticEnum, TEXT("/Script/OneEngineSDK"), TEXT("EOneEngineThirdType"), false, nullptr, nullptr);
	uint32 Get_Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType_Hash() { return 4156207797U; }
	UEnum* Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType()
	{
#if WITH_HOT_RELOAD
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UEnum* ReturnEnum = FindExistingEnumIfHotReloadOrDynamic(Outer, TEXT("EOneEngineThirdType"), 0, Get_Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType_Hash(), false);
#else
		static UEnum* ReturnEnum = nullptr;
#endif // WITH_HOT_RELOAD
		if (!ReturnEnum)
		{
			static const UE4CodeGen_Private::FEnumeratorParam Enumerators[] = {
				{ "EOneEngineThirdType::Guest", (int64)EOneEngineThirdType::Guest },
				{ "EOneEngineThirdType::Facebook", (int64)EOneEngineThirdType::Facebook },
				{ "EOneEngineThirdType::Google", (int64)EOneEngineThirdType::Google },
				{ "EOneEngineThirdType::Twitter", (int64)EOneEngineThirdType::Twitter },
				{ "EOneEngineThirdType::Line", (int64)EOneEngineThirdType::Line },
				{ "EOneEngineThirdType::GooglePlay", (int64)EOneEngineThirdType::GooglePlay },
				{ "EOneEngineThirdType::GameCenter", (int64)EOneEngineThirdType::GameCenter },
				{ "EOneEngineThirdType::Phone", (int64)EOneEngineThirdType::Phone },
				{ "EOneEngineThirdType::Email", (int64)EOneEngineThirdType::Email },
				{ "EOneEngineThirdType::VK", (int64)EOneEngineThirdType::VK },
				{ "EOneEngineThirdType::Naver", (int64)EOneEngineThirdType::Naver },
				{ "EOneEngineThirdType::Apple", (int64)EOneEngineThirdType::Apple },
				{ "EOneEngineThirdType::WeChat", (int64)EOneEngineThirdType::WeChat },
				{ "EOneEngineThirdType::GuestInherit", (int64)EOneEngineThirdType::GuestInherit },
				{ "EOneEngineThirdType::Yandex", (int64)EOneEngineThirdType::Yandex },
				{ "EOneEngineThirdType::MailRu", (int64)EOneEngineThirdType::MailRu },
				{ "EOneEngineThirdType::Infiplay", (int64)EOneEngineThirdType::Infiplay },
				{ "EOneEngineThirdType::HW", (int64)EOneEngineThirdType::HW },
				{ "EOneEngineThirdType::ShareCode", (int64)EOneEngineThirdType::ShareCode },
				{ "EOneEngineThirdType::HONOR", (int64)EOneEngineThirdType::HONOR },
				{ "EOneEngineThirdType::STEAM", (int64)EOneEngineThirdType::STEAM },
				{ "EOneEngineThirdType::PCScanCode", (int64)EOneEngineThirdType::PCScanCode },
				{ "EOneEngineThirdType::PlayStation", (int64)EOneEngineThirdType::PlayStation },
				{ "EOneEngineThirdType::APJ", (int64)EOneEngineThirdType::APJ },
				{ "EOneEngineThirdType::Crunchyrool", (int64)EOneEngineThirdType::Crunchyrool },
				{ "EOneEngineThirdType::LinkAccount", (int64)EOneEngineThirdType::LinkAccount },
				{ "EOneEngineThirdType::NaverCafe", (int64)EOneEngineThirdType::NaverCafe },
				{ "EOneEngineThirdType::WMPass", (int64)EOneEngineThirdType::WMPass },
			};
#if WITH_METADATA
			const UE4CodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
				{ "APJ.Comment", "//PlayStation \n" },
				{ "APJ.Name", "EOneEngineThirdType::APJ" },
				{ "APJ.ToolTip", "PlayStation" },
				{ "Apple.Comment", "//Naver\n" },
				{ "Apple.Name", "EOneEngineThirdType::Apple" },
				{ "Apple.ToolTip", "Naver" },
				{ "BlueprintType", "true" },
				{ "Crunchyrool.Comment", "//APJ\n" },
				{ "Crunchyrool.Name", "EOneEngineThirdType::Crunchyrool" },
				{ "Crunchyrool.ToolTip", "APJ" },
				{ "Email.Comment", "//\xe6\x89\x8b\xe6\x9c\xba\n" },
				{ "Email.Name", "EOneEngineThirdType::Email" },
				{ "Email.ToolTip", "\xe6\x89\x8b\xe6\x9c\xba" },
				{ "Facebook.Comment", "//\xe6\xb8\xb8\xe5\xae\xa2\n" },
				{ "Facebook.Name", "EOneEngineThirdType::Facebook" },
				{ "Facebook.ToolTip", "\xe6\xb8\xb8\xe5\xae\xa2" },
				{ "GameCenter.Comment", "//GooglePlay\n" },
				{ "GameCenter.Name", "EOneEngineThirdType::GameCenter" },
				{ "GameCenter.ToolTip", "GooglePlay" },
				{ "Google.Comment", "//Facebook\n" },
				{ "Google.Name", "EOneEngineThirdType::Google" },
				{ "Google.ToolTip", "Facebook" },
				{ "GooglePlay.Comment", "//Line\n" },
				{ "GooglePlay.Name", "EOneEngineThirdType::GooglePlay" },
				{ "GooglePlay.ToolTip", "Line" },
				{ "Guest.Name", "EOneEngineThirdType::Guest" },
				{ "GuestInherit.Comment", "//\xe5\xbe\xae\xe4\xbf\xa1\n" },
				{ "GuestInherit.Name", "EOneEngineThirdType::GuestInherit" },
				{ "GuestInherit.ToolTip", "\xe5\xbe\xae\xe4\xbf\xa1" },
				{ "HONOR.Comment", "//ShareCode\n" },
				{ "HONOR.Name", "EOneEngineThirdType::HONOR" },
				{ "HONOR.ToolTip", "ShareCode" },
				{ "HW.Comment", "//Infiplay\n" },
				{ "HW.Name", "EOneEngineThirdType::HW" },
				{ "HW.ToolTip", "Infiplay" },
				{ "Infiplay.Comment", "//MailRu\n" },
				{ "Infiplay.Name", "EOneEngineThirdType::Infiplay" },
				{ "Infiplay.ToolTip", "MailRu" },
				{ "Line.Comment", "//Twitter\n" },
				{ "Line.Name", "EOneEngineThirdType::Line" },
				{ "Line.ToolTip", "Twitter" },
				{ "LinkAccount.Comment", "//Crunchyrool\n" },
				{ "LinkAccount.Name", "EOneEngineThirdType::LinkAccount" },
				{ "LinkAccount.ToolTip", "Crunchyrool" },
				{ "MailRu.Comment", "//Yandex\n" },
				{ "MailRu.Name", "EOneEngineThirdType::MailRu" },
				{ "MailRu.ToolTip", "Yandex" },
				{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
				{ "Naver.Comment", "//VK\n" },
				{ "Naver.Name", "EOneEngineThirdType::Naver" },
				{ "Naver.ToolTip", "VK" },
				{ "NaverCafe.Comment", "//\xe8\xbf\x9e\xe6\x90\xbaID\n" },
				{ "NaverCafe.Name", "EOneEngineThirdType::NaverCafe" },
				{ "NaverCafe.ToolTip", "\xe8\xbf\x9e\xe6\x90\xbaID" },
				{ "PCScanCode.Comment", "//steam\n" },
				{ "PCScanCode.Name", "EOneEngineThirdType::PCScanCode" },
				{ "PCScanCode.ToolTip", "steam" },
				{ "Phone.Comment", "//GameCenter\n" },
				{ "Phone.Name", "EOneEngineThirdType::Phone" },
				{ "Phone.ToolTip", "GameCenter" },
				{ "PlayStation.Comment", "//PCScanCode\n" },
				{ "PlayStation.Name", "EOneEngineThirdType::PlayStation" },
				{ "PlayStation.ToolTip", "PCScanCode" },
				{ "ShareCode.Comment", "//\xe5\x8d\x8e\xe4\xb8\xba\n" },
				{ "ShareCode.Name", "EOneEngineThirdType::ShareCode" },
				{ "ShareCode.ToolTip", "\xe5\x8d\x8e\xe4\xb8\xba" },
				{ "STEAM.Comment", "//\xe8\x8d\xa3\xe8\x80\x80\n" },
				{ "STEAM.Name", "EOneEngineThirdType::STEAM" },
				{ "STEAM.ToolTip", "\xe8\x8d\xa3\xe8\x80\x80" },
				{ "Twitter.Comment", "//Google\n" },
				{ "Twitter.Name", "EOneEngineThirdType::Twitter" },
				{ "Twitter.ToolTip", "Google" },
				{ "VK.Comment", "//Email\n" },
				{ "VK.Name", "EOneEngineThirdType::VK" },
				{ "VK.ToolTip", "Email" },
				{ "WeChat.Comment", "//Apple\n" },
				{ "WeChat.Name", "EOneEngineThirdType::WeChat" },
				{ "WeChat.ToolTip", "Apple" },
				{ "WMPass.Comment", "//NaverCafe\n" },
				{ "WMPass.Name", "EOneEngineThirdType::WMPass" },
				{ "WMPass.ToolTip", "NaverCafe" },
				{ "Yandex.Comment", "//\xe6\xb8\xb8\xe5\xae\xa2\xe7\xbb\xa7\xe6\x89\xbf\xe7\xa0\x81\n" },
				{ "Yandex.Name", "EOneEngineThirdType::Yandex" },
				{ "Yandex.ToolTip", "\xe6\xb8\xb8\xe5\xae\xa2\xe7\xbb\xa7\xe6\x89\xbf\xe7\xa0\x81" },
			};
#endif
			static const UE4CodeGen_Private::FEnumParams EnumParams = {
				(UObject*(*)())Z_Construct_UPackage__Script_OneEngineSDK,
				nullptr,
				"EOneEngineThirdType",
				"EOneEngineThirdType",
				Enumerators,
				UE_ARRAY_COUNT(Enumerators),
				RF_Public|RF_Transient|RF_MarkAsNative,
				EEnumFlags::None,
				UE4CodeGen_Private::EDynamicType::NotDynamic,
				(uint8)UEnum::ECppForm::EnumClass,
				METADATA_PARAMS(Enum_MetaDataParams, UE_ARRAY_COUNT(Enum_MetaDataParams))
			};
			UE4CodeGen_Private::ConstructUEnum(ReturnEnum, EnumParams);
		}
		return ReturnEnum;
	}
class UScriptStruct* FUserIpInfo::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FUserIpInfo_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FUserIpInfo, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("UserIpInfo"), sizeof(FUserIpInfo), Get_Z_Construct_UScriptStruct_FUserIpInfo_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FUserIpInfo>()
{
	return FUserIpInfo::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FUserIpInfo(FUserIpInfo::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("UserIpInfo"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFUserIpInfo
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFUserIpInfo()
	{
		UScriptStruct::DeferCppStructOps<FUserIpInfo>(FName(TEXT("UserIpInfo")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFUserIpInfo;
	struct Z_Construct_UScriptStruct_FUserIpInfo_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Attribution_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Attribution;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CountryCode_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_CountryCode;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CityCode_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_CityCode;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Country_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Country;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Region_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Region;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_City_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_City;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FUserIpInfo_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FUserIpInfo>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Attribution_MetaData[] = {
		{ "Category", "UserIpInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Attribution = { "Attribution", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FUserIpInfo, Attribution), METADATA_PARAMS(Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Attribution_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Attribution_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_CountryCode_MetaData[] = {
		{ "Category", "UserIpInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_CountryCode = { "CountryCode", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FUserIpInfo, CountryCode), METADATA_PARAMS(Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_CountryCode_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_CountryCode_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_CityCode_MetaData[] = {
		{ "Category", "UserIpInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_CityCode = { "CityCode", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FUserIpInfo, CityCode), METADATA_PARAMS(Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_CityCode_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_CityCode_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Country_MetaData[] = {
		{ "Category", "UserIpInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Country = { "Country", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FUserIpInfo, Country), METADATA_PARAMS(Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Country_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Country_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Region_MetaData[] = {
		{ "Category", "UserIpInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Region = { "Region", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FUserIpInfo, Region), METADATA_PARAMS(Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Region_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Region_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_City_MetaData[] = {
		{ "Category", "UserIpInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_City = { "City", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FUserIpInfo, City), METADATA_PARAMS(Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_City_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_City_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FUserIpInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Attribution,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_CountryCode,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_CityCode,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Country,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_Region,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUserIpInfo_Statics::NewProp_City,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FUserIpInfo_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"UserIpInfo",
		sizeof(FUserIpInfo),
		alignof(FUserIpInfo),
		Z_Construct_UScriptStruct_FUserIpInfo_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUserIpInfo_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FUserIpInfo_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUserIpInfo_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FUserIpInfo()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FUserIpInfo_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("UserIpInfo"), sizeof(FUserIpInfo), Get_Z_Construct_UScriptStruct_FUserIpInfo_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FUserIpInfo_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FUserIpInfo_Hash() { return 148373366U; }
class UScriptStruct* FOneActiveQualificationInfo::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneActiveQualificationInfo, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneActiveQualificationInfo"), sizeof(FOneActiveQualificationInfo), Get_Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOneActiveQualificationInfo>()
{
	return FOneActiveQualificationInfo::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOneActiveQualificationInfo(FOneActiveQualificationInfo::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OneActiveQualificationInfo"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneActiveQualificationInfo
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneActiveQualificationInfo()
	{
		UScriptStruct::DeferCppStructOps<FOneActiveQualificationInfo>(FName(TEXT("OneActiveQualificationInfo")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOneActiveQualificationInfo;
	struct Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Status_MetaData[];
#endif
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_Status;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DeviceTotal_MetaData[];
#endif
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_DeviceTotal;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_WhiteList_MetaData[];
#endif
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_WhiteList;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DeviceLogged_MetaData[];
#endif
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_DeviceLogged;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneActiveQualificationInfo>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_Status_MetaData[] = {
		{ "Category", "OneEngineSDK|ActiveQualification" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe6\xbf\x80\xe6\xb4\xbb\xe8\xb5\x84\xe6\xa0\xbc\xef\xbc\x88""0\xef\xbc\x9a\xe6\x97\xa0\xe8\xb5\x84\xe6\xa0\xbc\xef\xbc\x8c""1\xef\xbc\x9a\xe6\x9c\x89\xe8\xb5\x84\xe6\xa0\xbc)\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe6\xbf\x80\xe6\xb4\xbb\xe8\xb5\x84\xe6\xa0\xbc\xef\xbc\x88""0\xef\xbc\x9a\xe6\x97\xa0\xe8\xb5\x84\xe6\xa0\xbc\xef\xbc\x8c""1\xef\xbc\x9a\xe6\x9c\x89\xe8\xb5\x84\xe6\xa0\xbc)" },
	};
#endif
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_Status = { "Status", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneActiveQualificationInfo, Status), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_Status_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_Status_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_DeviceTotal_MetaData[] = {
		{ "Category", "OneEngineSDK|ActiveQualification" },
		{ "Comment", "// \xe5\xb7\xb2\xe7\x99\xbb\xe5\xbd\x95\xe8\xae\xbe\xe5\xa4\x87\xe6\x80\xbb\xe6\x95\xb0\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xb7\xb2\xe7\x99\xbb\xe5\xbd\x95\xe8\xae\xbe\xe5\xa4\x87\xe6\x80\xbb\xe6\x95\xb0" },
	};
#endif
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_DeviceTotal = { "DeviceTotal", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneActiveQualificationInfo, DeviceTotal), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_DeviceTotal_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_DeviceTotal_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_WhiteList_MetaData[] = {
		{ "Category", "OneEngineSDK|ActiveQualification" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe6\x98\xaf\xe5\x90\xa6\xe6\xb7\xbb\xe5\x8a\xa0\xe5\x88\xb0\xe7\x99\xbd\xe5\x90\x8d\xe5\x8d\x95\xef\xbc\x88""0\xef\xbc\x9a\xe6\xb2\xa1\xe6\x9c\x89\xe6\xb7\xbb\xe5\x8a\xa0\xef\xbc\x8c""1\xef\xbc\x9a\xe5\xb7\xb2\xe6\xb7\xbb\xe5\x8a\xa0\xef\xbc\x89\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe6\x98\xaf\xe5\x90\xa6\xe6\xb7\xbb\xe5\x8a\xa0\xe5\x88\xb0\xe7\x99\xbd\xe5\x90\x8d\xe5\x8d\x95\xef\xbc\x88""0\xef\xbc\x9a\xe6\xb2\xa1\xe6\x9c\x89\xe6\xb7\xbb\xe5\x8a\xa0\xef\xbc\x8c""1\xef\xbc\x9a\xe5\xb7\xb2\xe6\xb7\xbb\xe5\x8a\xa0\xef\xbc\x89" },
	};
#endif
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_WhiteList = { "WhiteList", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneActiveQualificationInfo, WhiteList), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_WhiteList_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_WhiteList_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_DeviceLogged_MetaData[] = {
		{ "Category", "OneEngineSDK|ActiveQualification" },
		{ "Comment", "// \xe5\xbd\x93\xe5\x89\x8d\xe8\xae\xbe\xe5\xa4\x87\xe6\x98\xaf\xe5\x90\xa6\xe5\x9c\xa8\xe5\xb7\xb2\xe7\x99\xbb\xe5\xbd\x95\xe8\xae\xbe\xe5\xa4\x87\xe4\xb8\xad\xef\xbc\x88""0\xef\xbc\x9a\xe4\xb8\x8d\xe5\x9c\xa8\xef\xbc\x8c""1\xef\xbc\x9a\xe5\x9c\xa8\xef\xbc\x89\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xbd\x93\xe5\x89\x8d\xe8\xae\xbe\xe5\xa4\x87\xe6\x98\xaf\xe5\x90\xa6\xe5\x9c\xa8\xe5\xb7\xb2\xe7\x99\xbb\xe5\xbd\x95\xe8\xae\xbe\xe5\xa4\x87\xe4\xb8\xad\xef\xbc\x88""0\xef\xbc\x9a\xe4\xb8\x8d\xe5\x9c\xa8\xef\xbc\x8c""1\xef\xbc\x9a\xe5\x9c\xa8\xef\xbc\x89" },
	};
#endif
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_DeviceLogged = { "DeviceLogged", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneActiveQualificationInfo, DeviceLogged), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_DeviceLogged_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_DeviceLogged_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_Status,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_DeviceTotal,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_WhiteList,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::NewProp_DeviceLogged,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OneActiveQualificationInfo",
		sizeof(FOneActiveQualificationInfo),
		alignof(FOneActiveQualificationInfo),
		Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOneActiveQualificationInfo()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OneActiveQualificationInfo"), sizeof(FOneActiveQualificationInfo), Get_Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOneActiveQualificationInfo_Hash() { return 3329285502U; }
class UScriptStruct* FOnePushNotDisturbInfo::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePushNotDisturbInfo, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePushNotDisturbInfo"), sizeof(FOnePushNotDisturbInfo), Get_Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOnePushNotDisturbInfo>()
{
	return FOnePushNotDisturbInfo::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOnePushNotDisturbInfo(FOnePushNotDisturbInfo::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OnePushNotDisturbInfo"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePushNotDisturbInfo
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePushNotDisturbInfo()
	{
		UScriptStruct::DeferCppStructOps<FOnePushNotDisturbInfo>(FName(TEXT("OnePushNotDisturbInfo")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePushNotDisturbInfo;
	struct Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bNotDisturb_MetaData[];
#endif
		static void NewProp_bNotDisturb_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bNotDisturb;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NotDisturbStartTime_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_NotDisturbStartTime;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NotDisturbEndTime_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_NotDisturbEndTime;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePushNotDisturbInfo>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_bNotDisturb_MetaData[] = {
		{ "Category", "OnePushNotDisturbInfo" },
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe5\xbc\x80\xe5\x90\xaf\xe5\xa4\x9c\xe9\x97\xb4\xe5\x8b\xbf\xe6\x89\xb0\xef\xbc\x8cNO-\xe4\xb8\x8d\xe5\xbc\x80\xe5\x90\xaf\xef\xbc\x8cYES-\xe5\xbc\x80\xe5\x90\xaf\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe5\xbc\x80\xe5\x90\xaf\xe5\xa4\x9c\xe9\x97\xb4\xe5\x8b\xbf\xe6\x89\xb0\xef\xbc\x8cNO-\xe4\xb8\x8d\xe5\xbc\x80\xe5\x90\xaf\xef\xbc\x8cYES-\xe5\xbc\x80\xe5\x90\xaf" },
	};
#endif
	void Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_bNotDisturb_SetBit(void* Obj)
	{
		((FOnePushNotDisturbInfo*)Obj)->bNotDisturb = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_bNotDisturb = { "bNotDisturb", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(FOnePushNotDisturbInfo), &Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_bNotDisturb_SetBit, METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_bNotDisturb_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_bNotDisturb_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_NotDisturbStartTime_MetaData[] = {
		{ "Category", "OnePushNotDisturbInfo" },
		{ "Comment", "// \xe5\xa4\x9c\xe9\x97\xb4\xe5\x8b\xbf\xe6\x89\xb0\xe5\xbc\x80\xe5\xa7\x8b\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\xbc\x80\xe5\xa7\x8b\xe6\x97\xb6\xe9\x97\xb4\xe5\xa4\xa7\xe4\xba\x8e\xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\x88\x99\xe4\xbb\xa3\xe8\xa1\xa8\xe8\xb7\xa8\xe5\xa4\xa9\xe3\x80\x82\xe7\xa4\xba\xe4\xbe\x8b\xef\xbc\x9a\"01:00\"\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xa4\x9c\xe9\x97\xb4\xe5\x8b\xbf\xe6\x89\xb0\xe5\xbc\x80\xe5\xa7\x8b\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\xbc\x80\xe5\xa7\x8b\xe6\x97\xb6\xe9\x97\xb4\xe5\xa4\xa7\xe4\xba\x8e\xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\x88\x99\xe4\xbb\xa3\xe8\xa1\xa8\xe8\xb7\xa8\xe5\xa4\xa9\xe3\x80\x82\xe7\xa4\xba\xe4\xbe\x8b\xef\xbc\x9a\"01:00\"" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_NotDisturbStartTime = { "NotDisturbStartTime", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePushNotDisturbInfo, NotDisturbStartTime), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_NotDisturbStartTime_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_NotDisturbStartTime_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_NotDisturbEndTime_MetaData[] = {
		{ "Category", "OnePushNotDisturbInfo" },
		{ "Comment", "// \xe5\xa4\x9c\xe9\x97\xb4\xe5\x8b\xbf\xe6\x89\xb0\xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\xbc\x80\xe5\xa7\x8b\xe6\x97\xb6\xe9\x97\xb4\xe5\xa4\xa7\xe4\xba\x8e\xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\x88\x99\xe4\xbb\xa3\xe8\xa1\xa8\xe8\xb7\xa8\xe5\xa4\xa9\xe3\x80\x82\xe7\xa4\xba\xe4\xbe\x8b\xef\xbc\x9a\"10:00\"\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xa4\x9c\xe9\x97\xb4\xe5\x8b\xbf\xe6\x89\xb0\xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe5\xbc\x80\xe5\xa7\x8b\xe6\x97\xb6\xe9\x97\xb4\xe5\xa4\xa7\xe4\xba\x8e\xe7\xbb\x93\xe6\x9d\x9f\xe6\x97\xb6\xe9\x97\xb4\xef\xbc\x8c\xe5\x88\x99\xe4\xbb\xa3\xe8\xa1\xa8\xe8\xb7\xa8\xe5\xa4\xa9\xe3\x80\x82\xe7\xa4\xba\xe4\xbe\x8b\xef\xbc\x9a\"10:00\"" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_NotDisturbEndTime = { "NotDisturbEndTime", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePushNotDisturbInfo, NotDisturbEndTime), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_NotDisturbEndTime_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_NotDisturbEndTime_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_bNotDisturb,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_NotDisturbStartTime,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::NewProp_NotDisturbEndTime,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OnePushNotDisturbInfo",
		sizeof(FOnePushNotDisturbInfo),
		alignof(FOnePushNotDisturbInfo),
		Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOnePushNotDisturbInfo()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OnePushNotDisturbInfo"), sizeof(FOnePushNotDisturbInfo), Get_Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOnePushNotDisturbInfo_Hash() { return 1118217284U; }
class UScriptStruct* FOnePushMessage::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOnePushMessage_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePushMessage, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePushMessage"), sizeof(FOnePushMessage), Get_Z_Construct_UScriptStruct_FOnePushMessage_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOnePushMessage>()
{
	return FOnePushMessage::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOnePushMessage(FOnePushMessage::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OnePushMessage"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePushMessage
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePushMessage()
	{
		UScriptStruct::DeferCppStructOps<FOnePushMessage>(FName(TEXT("OnePushMessage")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePushMessage;
	struct Z_Construct_UScriptStruct_FOnePushMessage_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Title;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Content_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Content;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_MessageId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_MessageId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Ext_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Ext;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[];
#endif
		static void NewProp_bIsActive_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushMessage_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePushMessage>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Title_MetaData[] = {
		{ "Category", "OnePushMessage" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000015, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePushMessage, Title), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Title_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Title_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Content_MetaData[] = {
		{ "Category", "OnePushMessage" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Content = { "Content", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePushMessage, Content), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Content_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Content_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_MessageId_MetaData[] = {
		{ "Category", "OnePushMessage" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_MessageId = { "MessageId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePushMessage, MessageId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_MessageId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_MessageId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Ext_MetaData[] = {
		{ "Category", "OnePushMessage" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Ext = { "Ext", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePushMessage, Ext), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Ext_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Ext_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_bIsActive_MetaData[] = {
		{ "Category", "OnePushMessage" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	void Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_bIsActive_SetBit(void* Obj)
	{
		((FOnePushMessage*)Obj)->bIsActive = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(FOnePushMessage), &Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_bIsActive_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_bIsActive_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePushMessage_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Title,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Content,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_MessageId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_Ext,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushMessage_Statics::NewProp_bIsActive,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePushMessage_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OnePushMessage",
		sizeof(FOnePushMessage),
		alignof(FOnePushMessage),
		Z_Construct_UScriptStruct_FOnePushMessage_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushMessage_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushMessage_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushMessage_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOnePushMessage()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOnePushMessage_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OnePushMessage"), sizeof(FOnePushMessage), Get_Z_Construct_UScriptStruct_FOnePushMessage_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOnePushMessage_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOnePushMessage_Hash() { return 3606399469U; }
class UScriptStruct* FOnePushTypeInfo::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOnePushTypeInfo_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePushTypeInfo, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePushTypeInfo"), sizeof(FOnePushTypeInfo), Get_Z_Construct_UScriptStruct_FOnePushTypeInfo_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOnePushTypeInfo>()
{
	return FOnePushTypeInfo::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOnePushTypeInfo(FOnePushTypeInfo::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OnePushTypeInfo"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePushTypeInfo
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePushTypeInfo()
	{
		UScriptStruct::DeferCppStructOps<FOnePushTypeInfo>(FName(TEXT("OnePushTypeInfo")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePushTypeInfo;
	struct Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Name;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bOpen_MetaData[];
#endif
		static void NewProp_bOpen_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bOpen;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PushType_MetaData[];
#endif
		static const UE4CodeGen_Private::FUnsizedIntPropertyParams NewProp_PushType;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePushTypeInfo>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_Name_MetaData[] = {
		{ "Category", "OnePushTypeInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000015, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePushTypeInfo, Name), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_Name_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_Name_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_bOpen_MetaData[] = {
		{ "Category", "OnePushTypeInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	void Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_bOpen_SetBit(void* Obj)
	{
		((FOnePushTypeInfo*)Obj)->bOpen = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_bOpen = { "bOpen", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(FOnePushTypeInfo), &Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_bOpen_SetBit, METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_bOpen_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_bOpen_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_PushType_MetaData[] = {
		{ "Category", "OnePushTypeInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FUnsizedIntPropertyParams Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_PushType = { "PushType", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePushTypeInfo, PushType), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_PushType_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_PushType_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_Name,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_bOpen,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::NewProp_PushType,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OnePushTypeInfo",
		sizeof(FOnePushTypeInfo),
		alignof(FOnePushTypeInfo),
		Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOnePushTypeInfo()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOnePushTypeInfo_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OnePushTypeInfo"), sizeof(FOnePushTypeInfo), Get_Z_Construct_UScriptStruct_FOnePushTypeInfo_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOnePushTypeInfo_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOnePushTypeInfo_Hash() { return 3766604432U; }
class UScriptStruct* FOnePushStatus::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOnePushStatus_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePushStatus, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePushStatus"), sizeof(FOnePushStatus), Get_Z_Construct_UScriptStruct_FOnePushStatus_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOnePushStatus>()
{
	return FOnePushStatus::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOnePushStatus(FOnePushStatus::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OnePushStatus"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePushStatus
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePushStatus()
	{
		UScriptStruct::DeferCppStructOps<FOnePushStatus>(FName(TEXT("OnePushStatus")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePushStatus;
	struct Z_Construct_UScriptStruct_FOnePushStatus_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bIsSysOpen_MetaData[];
#endif
		static void NewProp_bIsSysOpen_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsSysOpen;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bIsAppOpen_MetaData[];
#endif
		static void NewProp_bIsAppOpen_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsAppOpen;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushStatus_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePushStatus>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsSysOpen_MetaData[] = {
		{ "Category", "OnePushStatus" },
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe6\x98\xaf\xe6\x89\x93\xe5\xbc\x80\xe4\xba\x86\xe6\x8e\xa8\xe9\x80\x81\xe6\x9d\x83\xe9\x99\x90\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe6\x98\xaf\xe6\x89\x93\xe5\xbc\x80\xe4\xba\x86\xe6\x8e\xa8\xe9\x80\x81\xe6\x9d\x83\xe9\x99\x90" },
	};
#endif
	void Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsSysOpen_SetBit(void* Obj)
	{
		((FOnePushStatus*)Obj)->bIsSysOpen = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsSysOpen = { "bIsSysOpen", nullptr, (EPropertyFlags)0x0010000000000015, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(FOnePushStatus), &Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsSysOpen_SetBit, METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsSysOpen_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsSysOpen_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsAppOpen_MetaData[] = {
		{ "Category", "OnePushStatus" },
		{ "Comment", "//app \xe6\x98\xaf\xe5\x90\xa6\xe6\x89\x93\xe5\xbc\x80\xe6\x8e\xa8\xe9\x80\x81\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "app \xe6\x98\xaf\xe5\x90\xa6\xe6\x89\x93\xe5\xbc\x80\xe6\x8e\xa8\xe9\x80\x81" },
	};
#endif
	void Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsAppOpen_SetBit(void* Obj)
	{
		((FOnePushStatus*)Obj)->bIsAppOpen = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsAppOpen = { "bIsAppOpen", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(FOnePushStatus), &Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsAppOpen_SetBit, METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsAppOpen_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsAppOpen_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePushStatus_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsSysOpen,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePushStatus_Statics::NewProp_bIsAppOpen,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePushStatus_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OnePushStatus",
		sizeof(FOnePushStatus),
		alignof(FOnePushStatus),
		Z_Construct_UScriptStruct_FOnePushStatus_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushStatus_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePushStatus_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePushStatus_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOnePushStatus()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOnePushStatus_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OnePushStatus"), sizeof(FOnePushStatus), Get_Z_Construct_UScriptStruct_FOnePushStatus_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOnePushStatus_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOnePushStatus_Hash() { return 2252381172U; }
class UScriptStruct* FOneShareData::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOneShareData_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneShareData, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneShareData"), sizeof(FOneShareData), Get_Z_Construct_UScriptStruct_FOneShareData_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOneShareData>()
{
	return FOneShareData::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOneShareData(FOneShareData::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OneShareData"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneShareData
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneShareData()
	{
		UScriptStruct::DeferCppStructOps<FOneShareData>(FName(TEXT("OneShareData")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOneShareData;
	struct Z_Construct_UScriptStruct_FOneShareData_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_WebPageUrl_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_WebPageUrl;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Title;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Content_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Content;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Thumbnail_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_Thumbnail;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Image_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_Image;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NetImageUrl_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_NetImageUrl;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_LocalImagePath_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_LocalImagePath;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TopicId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_TopicId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SinaSuperGroup_MetaData[];
#endif
		static const UE4CodeGen_Private::FStructPropertyParams NewProp_SinaSuperGroup;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneShareData_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Comment", "// \xe5\x88\x86\xe4\xba\xab\xe6\x95\xb0\xe6\x8d\xae\xe5\x86\x85\xe5\xae\xb9\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\x88\x86\xe4\xba\xab\xe6\x95\xb0\xe6\x8d\xae\xe5\x86\x85\xe5\xae\xb9" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOneShareData_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneShareData>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_WebPageUrl_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_WebPageUrl = { "WebPageUrl", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneShareData, WebPageUrl), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_WebPageUrl_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_WebPageUrl_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Title_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneShareData, Title), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Title_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Title_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Content_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Content = { "Content", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneShareData, Content), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Content_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Content_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Thumbnail_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Thumbnail = { "Thumbnail", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneShareData, Thumbnail), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Thumbnail_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Thumbnail_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Image_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Image = { "Image", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneShareData, Image), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Image_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Image_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_NetImageUrl_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_NetImageUrl = { "NetImageUrl", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneShareData, NetImageUrl), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_NetImageUrl_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_NetImageUrl_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_LocalImagePath_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_LocalImagePath = { "LocalImagePath", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneShareData, LocalImagePath), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_LocalImagePath_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_LocalImagePath_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_TopicId_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_TopicId = { "TopicId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneShareData, TopicId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_TopicId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_TopicId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_SinaSuperGroup_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_SinaSuperGroup = { "SinaSuperGroup", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneShareData, SinaSuperGroup), Z_Construct_UScriptStruct_FOneShareWeiboSuperData, METADATA_PARAMS(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_SinaSuperGroup_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_SinaSuperGroup_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneShareData_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_WebPageUrl,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Title,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Content,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Thumbnail,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_Image,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_NetImageUrl,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_LocalImagePath,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_TopicId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareData_Statics::NewProp_SinaSuperGroup,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneShareData_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OneShareData",
		sizeof(FOneShareData),
		alignof(FOneShareData),
		Z_Construct_UScriptStruct_FOneShareData_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareData_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOneShareData_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareData_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOneShareData()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOneShareData_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OneShareData"), sizeof(FOneShareData), Get_Z_Construct_UScriptStruct_FOneShareData_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOneShareData_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOneShareData_Hash() { return 3538763364U; }
class UScriptStruct* FOneShareWeiboSuperData::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneShareWeiboSuperData, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneShareWeiboSuperData"), sizeof(FOneShareWeiboSuperData), Get_Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOneShareWeiboSuperData>()
{
	return FOneShareWeiboSuperData::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOneShareWeiboSuperData(FOneShareWeiboSuperData::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OneShareWeiboSuperData"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneShareWeiboSuperData
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneShareWeiboSuperData()
	{
		UScriptStruct::DeferCppStructOps<FOneShareWeiboSuperData>(FName(TEXT("OneShareWeiboSuperData")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOneShareWeiboSuperData;
	struct Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SuperGroup_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_SuperGroup;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Section_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Section;
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ExtraInfo_ValueProp;
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ExtraInfo_Key_KeyProp;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ExtraInfo_MetaData[];
#endif
		static const UE4CodeGen_Private::FMapPropertyParams NewProp_ExtraInfo;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Comment", "// \xe5\xbe\xae\xe5\x8d\x9a\xe8\xb6\x85\xe8\xaf\x9d\xe5\x88\x86\xe4\xba\xab\xe5\x86\x85\xe5\xae\xb9\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xbe\xae\xe5\x8d\x9a\xe8\xb6\x85\xe8\xaf\x9d\xe5\x88\x86\xe4\xba\xab\xe5\x86\x85\xe5\xae\xb9" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneShareWeiboSuperData>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_SuperGroup_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_SuperGroup = { "SuperGroup", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneShareWeiboSuperData, SuperGroup), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_SuperGroup_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_SuperGroup_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_Section_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_Section = { "Section", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneShareWeiboSuperData, Section), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_Section_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_Section_MetaData)) };
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_ExtraInfo_ValueProp = { "ExtraInfo", nullptr, (EPropertyFlags)0x0000000000000001, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, 1, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_ExtraInfo_Key_KeyProp = { "ExtraInfo_Key", nullptr, (EPropertyFlags)0x0000000000000001, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_ExtraInfo_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_ExtraInfo = { "ExtraInfo", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneShareWeiboSuperData, ExtraInfo), EMapPropertyFlags::None, METADATA_PARAMS(Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_ExtraInfo_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_ExtraInfo_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_SuperGroup,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_Section,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_ExtraInfo_ValueProp,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_ExtraInfo_Key_KeyProp,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::NewProp_ExtraInfo,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OneShareWeiboSuperData",
		sizeof(FOneShareWeiboSuperData),
		alignof(FOneShareWeiboSuperData),
		Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOneShareWeiboSuperData()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OneShareWeiboSuperData"), sizeof(FOneShareWeiboSuperData), Get_Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOneShareWeiboSuperData_Hash() { return 4171597793U; }
class UScriptStruct* FOnePermissionInfo::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOnePermissionInfo_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePermissionInfo, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePermissionInfo"), sizeof(FOnePermissionInfo), Get_Z_Construct_UScriptStruct_FOnePermissionInfo_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOnePermissionInfo>()
{
	return FOnePermissionInfo::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOnePermissionInfo(FOnePermissionInfo::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OnePermissionInfo"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePermissionInfo
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePermissionInfo()
	{
		UScriptStruct::DeferCppStructOps<FOnePermissionInfo>(FName(TEXT("OnePermissionInfo")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePermissionInfo;
	struct Z_Construct_UScriptStruct_FOnePermissionInfo_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
		static const UE4CodeGen_Private::FBytePropertyParams NewProp_PermissionType_Underlying;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PermissionType_MetaData[];
#endif
		static const UE4CodeGen_Private::FEnumPropertyParams NewProp_PermissionType;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Title;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Desc_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Desc;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePermissionInfo>();
	}
	const UE4CodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_PermissionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UE4CodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, nullptr, METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_PermissionType_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "Comment", "// \xe6\x9d\x83\xe9\x99\x90\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x9d\x83\xe9\x99\x90" },
	};
#endif
	const UE4CodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_PermissionType = { "PermissionType", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePermissionInfo, PermissionType), Z_Construct_UEnum_OneEngineSDK_EOnePermissionType, METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_PermissionType_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_PermissionType_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_Title_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "Comment", "// \xe6\x9d\x83\xe9\x99\x90\xe5\x90\x8d\xe7\xa7\xb0\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x9d\x83\xe9\x99\x90\xe5\x90\x8d\xe7\xa7\xb0" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePermissionInfo, Title), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_Title_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_Title_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_Desc_MetaData[] = {
		{ "Category", "OneEngineSDK" },
		{ "Comment", "// \xe6\x9d\x83\xe9\x99\x90\xe6\x8f\x8f\xe8\xbf\xb0\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x9d\x83\xe9\x99\x90\xe6\x8f\x8f\xe8\xbf\xb0" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_Desc = { "Desc", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePermissionInfo, Desc), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_Desc_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_Desc_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_PermissionType_Underlying,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_PermissionType,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_Title,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::NewProp_Desc,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OnePermissionInfo",
		sizeof(FOnePermissionInfo),
		alignof(FOnePermissionInfo),
		Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOnePermissionInfo()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOnePermissionInfo_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OnePermissionInfo"), sizeof(FOnePermissionInfo), Get_Z_Construct_UScriptStruct_FOnePermissionInfo_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOnePermissionInfo_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOnePermissionInfo_Hash() { return 2968488142U; }
class UScriptStruct* FOneURCRoleInfo::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOneURCRoleInfo_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneURCRoleInfo, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneURCRoleInfo"), sizeof(FOneURCRoleInfo), Get_Z_Construct_UScriptStruct_FOneURCRoleInfo_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOneURCRoleInfo>()
{
	return FOneURCRoleInfo::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOneURCRoleInfo(FOneURCRoleInfo::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OneURCRoleInfo"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneURCRoleInfo
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneURCRoleInfo()
	{
		UScriptStruct::DeferCppStructOps<FOneURCRoleInfo>(FName(TEXT("OneURCRoleInfo")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOneURCRoleInfo;
	struct Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UserId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_UserId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_RoleId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_RoleId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_RoleName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_RoleName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Level_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Level;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ServerId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ServerId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ServerName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ServerName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Gender_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Gender;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Occupation_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Occupation;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_LastLoginTime_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_LastLoginTime;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Comment", "// \xe5\x85\x91\xe6\x8d\xa2\xe7\xa0\x81\xe6\x9c\x8d\xe5\x8a\xa1\xe8\x8e\xb7\xe5\x8f\x96\xe7\x94\xa8\xe6\x88\xb7\xe8\xa7\x92\xe8\x89\xb2\xe5\x88\x97\xe8\xa1\xa8\xe6\x95\xb0\xe6\x8d\xae\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\x85\x91\xe6\x8d\xa2\xe7\xa0\x81\xe6\x9c\x8d\xe5\x8a\xa1\xe8\x8e\xb7\xe5\x8f\x96\xe7\x94\xa8\xe6\x88\xb7\xe8\xa7\x92\xe8\x89\xb2\xe5\x88\x97\xe8\xa1\xa8\xe6\x95\xb0\xe6\x8d\xae" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneURCRoleInfo>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_UserId_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7ID\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7ID" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_UserId = { "UserId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneURCRoleInfo, UserId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_UserId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_UserId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_RoleId_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2ID\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2ID" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_RoleId = { "RoleId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneURCRoleInfo, RoleId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_RoleId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_RoleId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_RoleName_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2\xe5\x90\x8d\xe7\xa7\xb0\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2\xe5\x90\x8d\xe7\xa7\xb0" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_RoleName = { "RoleName", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneURCRoleInfo, RoleName), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_RoleName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_RoleName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Level_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2\xe7\xad\x89\xe7\xba\xa7\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2\xe7\xad\x89\xe7\xba\xa7" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneURCRoleInfo, Level), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Level_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Level_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_ServerId_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
		{ "Comment", "// \xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8ID\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8ID" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_ServerId = { "ServerId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneURCRoleInfo, ServerId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_ServerId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_ServerId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_ServerName_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
		{ "Comment", "// \xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe5\x90\x8d\xe7\xa7\xb0\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe5\x90\x8d\xe7\xa7\xb0" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_ServerName = { "ServerName", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneURCRoleInfo, ServerName), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_ServerName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_ServerName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Gender_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2\xe6\x80\xa7\xe5\x88\xab\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2\xe6\x80\xa7\xe5\x88\xab" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Gender = { "Gender", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneURCRoleInfo, Gender), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Gender_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Gender_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Occupation_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2\xe8\x81\x8c\xe4\xb8\x9a\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2\xe8\x81\x8c\xe4\xb8\x9a" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Occupation = { "Occupation", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneURCRoleInfo, Occupation), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Occupation_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Occupation_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_LastLoginTime_MetaData[] = {
		{ "Category", "OneEngineSDK|URedeemCode" },
		{ "Comment", "// \xe6\x9c\x80\xe5\x90\x8e\xe4\xb8\x80\xe6\xac\xa1\xe7\x99\xbb\xe5\xbd\x95\xe7\x9a\x84\xe6\x97\xb6\xe9\x97\xb4\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x9c\x80\xe5\x90\x8e\xe4\xb8\x80\xe6\xac\xa1\xe7\x99\xbb\xe5\xbd\x95\xe7\x9a\x84\xe6\x97\xb6\xe9\x97\xb4" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_LastLoginTime = { "LastLoginTime", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneURCRoleInfo, LastLoginTime), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_LastLoginTime_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_LastLoginTime_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_UserId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_RoleId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_RoleName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Level,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_ServerId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_ServerName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Gender,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_Occupation,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::NewProp_LastLoginTime,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OneURCRoleInfo",
		sizeof(FOneURCRoleInfo),
		alignof(FOneURCRoleInfo),
		Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOneURCRoleInfo()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOneURCRoleInfo_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OneURCRoleInfo"), sizeof(FOneURCRoleInfo), Get_Z_Construct_UScriptStruct_FOneURCRoleInfo_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOneURCRoleInfo_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOneURCRoleInfo_Hash() { return 2253766042U; }
class UScriptStruct* FOneAntiAddictionInfo::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneAntiAddictionInfo, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneAntiAddictionInfo"), sizeof(FOneAntiAddictionInfo), Get_Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOneAntiAddictionInfo>()
{
	return FOneAntiAddictionInfo::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOneAntiAddictionInfo(FOneAntiAddictionInfo::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OneAntiAddictionInfo"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneAntiAddictionInfo
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneAntiAddictionInfo()
	{
		UScriptStruct::DeferCppStructOps<FOneAntiAddictionInfo>(FName(TEXT("OneAntiAddictionInfo")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOneAntiAddictionInfo;
	struct Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AppID_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_AppID;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UserId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_UserId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Status_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_Status;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_HeartbeatInterval_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_HeartbeatInterval;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BannedType_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_BannedType;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BannedReason_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_BannedReason;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BreakNotice_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_BreakNotice;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Realuser_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_Realuser;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CivicType_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_CivicType;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Age_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_Age;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Gender_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_Gender;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AccountType_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_AccountType;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_RequestIp_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_RequestIp;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DayOnlineDuration_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_DayOnlineDuration;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Comment", "//\xe9\x98\xb2\xe6\xb2\x89\xe8\xbf\xb7\xe4\xbf\xa1\xe6\x81\xaf\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe9\x98\xb2\xe6\xb2\x89\xe8\xbf\xb7\xe4\xbf\xa1\xe6\x81\xaf" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneAntiAddictionInfo>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_AppID_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
		{ "Comment", "// \xe5\xba\x94\xe7\x94\xa8ID\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xba\x94\xe7\x94\xa8ID" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_AppID = { "AppID", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, AppID), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_AppID_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_AppID_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_UserId_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7ID\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7ID" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_UserId = { "UserId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, UserId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_UserId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_UserId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Status_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
		{ "Comment", "// \xe9\x98\xb2\xe6\xb2\x89\xe8\xbf\xb7\xe7\x8a\xb6\xe6\x80\x81: 0 \xe6\xad\xa3\xe5\xb8\xb8\xe5\x8f\xaf\xe8\xbf\x9b\xe5\x85\xa5\xe6\xb8\xb8\xe6\x88\x8f; 1 \xe4\xb8\x8d\xe5\x8f\xaf\xe8\xbf\x9b\xe5\x85\xa5\xe6\xb8\xb8\xe6\x88\x8f\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe9\x98\xb2\xe6\xb2\x89\xe8\xbf\xb7\xe7\x8a\xb6\xe6\x80\x81: 0 \xe6\xad\xa3\xe5\xb8\xb8\xe5\x8f\xaf\xe8\xbf\x9b\xe5\x85\xa5\xe6\xb8\xb8\xe6\x88\x8f; 1 \xe4\xb8\x8d\xe5\x8f\xaf\xe8\xbf\x9b\xe5\x85\xa5\xe6\xb8\xb8\xe6\x88\x8f" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Status = { "Status", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, Status), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Status_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Status_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_HeartbeatInterval_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
		{ "Comment", "// \xe5\xbf\x83\xe8\xb7\xb3\xe9\x97\xb4\xe9\x9a\x94\xef\xbc\x8c\xe5\x8d\x95\xe4\xbd\x8d:\xe7\xa7\x92\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xbf\x83\xe8\xb7\xb3\xe9\x97\xb4\xe9\x9a\x94\xef\xbc\x8c\xe5\x8d\x95\xe4\xbd\x8d:\xe7\xa7\x92" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_HeartbeatInterval = { "HeartbeatInterval", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, HeartbeatInterval), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_HeartbeatInterval_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_HeartbeatInterval_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BannedType_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
		{ "Comment", "// \xe5\xb0\x81\xe7\xa6\x81\xe7\xb1\xbb\xe5\x9e\x8b: 1 \xe5\xb0\x81\xe7\xa6\x81\xef\xbc\x9b""2 \xe5\xae\xb5\xe7\xa6\x81\xef\xbc\x9b""3 \xe5\x8d\x95\xe6\xac\xa1\xe5\x9c\xa8\xe7\xba\xbf\xe6\x97\xb6\xe9\x95\xbf\xe8\xb6\x85\xe9\x99\x90\xef\xbc\x9b""4 \xe5\xbd\x93\xe6\x97\xa5\xe7\xb4\xaf\xe8\xae\xa1\xe5\x9c\xa8\xe7\xba\xbf\xe6\x97\xb6\xe9\x95\xbf\xe8\xb6\x85\xe9\x99\x90 5\xe6\x98\xaf\xe6\x9c\xaa\xe5\xae\x9e\xe5\x90\x8d\xe4\xbd\x93\xe9\xaa\x8c\xe6\x97\xb6\xe9\x97\xb4\xe5\x88\xb0\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xb0\x81\xe7\xa6\x81\xe7\xb1\xbb\xe5\x9e\x8b: 1 \xe5\xb0\x81\xe7\xa6\x81\xef\xbc\x9b""2 \xe5\xae\xb5\xe7\xa6\x81\xef\xbc\x9b""3 \xe5\x8d\x95\xe6\xac\xa1\xe5\x9c\xa8\xe7\xba\xbf\xe6\x97\xb6\xe9\x95\xbf\xe8\xb6\x85\xe9\x99\x90\xef\xbc\x9b""4 \xe5\xbd\x93\xe6\x97\xa5\xe7\xb4\xaf\xe8\xae\xa1\xe5\x9c\xa8\xe7\xba\xbf\xe6\x97\xb6\xe9\x95\xbf\xe8\xb6\x85\xe9\x99\x90 5\xe6\x98\xaf\xe6\x9c\xaa\xe5\xae\x9e\xe5\x90\x8d\xe4\xbd\x93\xe9\xaa\x8c\xe6\x97\xb6\xe9\x97\xb4\xe5\x88\xb0" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BannedType = { "BannedType", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, BannedType), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BannedType_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BannedType_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BannedReason_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
		{ "Comment", "// \xe7\xa6\x81\xe7\x8e\xa9\xe7\x90\x86\xe7\x94\xb1\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe8\x87\xaa\xe8\xa1\x8c\xe5\xae\x9e\xe7\x8e\xb0\xe6\x8f\x90\xe9\x86\x92\xe6\x88\x96\xe4\xb8\x8b\xe7\xba\xbf\xe7\x9a\x84UI\xef\xbc\x8c\xe8\xaf\xb7\xe8\xaf\xbb\xe5\x8f\x96\xe6\x9c\xac\xe5\xad\x97\xe6\xae\xb5\xe7\x94\xa8\xe4\xba\x8e\xe6\x98\xbe\xe7\xa4\xba\xe3\x80\x82\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\xa6\x81\xe7\x8e\xa9\xe7\x90\x86\xe7\x94\xb1\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe8\x87\xaa\xe8\xa1\x8c\xe5\xae\x9e\xe7\x8e\xb0\xe6\x8f\x90\xe9\x86\x92\xe6\x88\x96\xe4\xb8\x8b\xe7\xba\xbf\xe7\x9a\x84UI\xef\xbc\x8c\xe8\xaf\xb7\xe8\xaf\xbb\xe5\x8f\x96\xe6\x9c\xac\xe5\xad\x97\xe6\xae\xb5\xe7\x94\xa8\xe4\xba\x8e\xe6\x98\xbe\xe7\xa4\xba\xe3\x80\x82" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BannedReason = { "BannedReason", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, BannedReason), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BannedReason_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BannedReason_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BreakNotice_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
		{ "Comment", "// \xe4\xbc\x91\xe6\x81\xaf\xe9\x80\x9a\xe7\x9f\xa5\xef\xbc\x8c\xe4\xbb\x85\xe6\x8f\x90\xe9\x86\x92\xef\xbc\x8c\xe4\xb8\x8d\xe8\xa6\x81\xe6\xb1\x82\xe7\x99\xbb\xe5\x87\xba\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe4\xbc\x91\xe6\x81\xaf\xe9\x80\x9a\xe7\x9f\xa5\xef\xbc\x8c\xe4\xbb\x85\xe6\x8f\x90\xe9\x86\x92\xef\xbc\x8c\xe4\xb8\x8d\xe8\xa6\x81\xe6\xb1\x82\xe7\x99\xbb\xe5\x87\xba" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BreakNotice = { "BreakNotice", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, BreakNotice), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BreakNotice_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BreakNotice_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Realuser_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
		{ "Comment", "// \xe5\xae\x9e\xe5\x90\x8d\xe7\xb1\xbb\xe5\x9e\x8b: 0 \xe6\x9c\xaa\xe5\xae\x9e\xe5\x90\x8d; 1 \xe8\xba\xab\xe4\xbb\xbd\xe8\xaf\x81\xe5\xae\x9e\xe5\x90\x8d; 2 \xe6\x8a\xa4\xe7\x85\xa7\xe5\xae\x9e\xe5\x90\x8d\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xae\x9e\xe5\x90\x8d\xe7\xb1\xbb\xe5\x9e\x8b: 0 \xe6\x9c\xaa\xe5\xae\x9e\xe5\x90\x8d; 1 \xe8\xba\xab\xe4\xbb\xbd\xe8\xaf\x81\xe5\xae\x9e\xe5\x90\x8d; 2 \xe6\x8a\xa4\xe7\x85\xa7\xe5\xae\x9e\xe5\x90\x8d" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Realuser = { "Realuser", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, Realuser), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Realuser_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Realuser_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_CivicType_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
		{ "Comment", "// \xe5\x85\xac\xe6\xb0\x91\xe7\xb1\xbb\xe5\x9e\x8b: 0 \xe6\x9c\xaa\xe5\xae\x9e\xe5\x90\x8d; 1 \xe4\xb8\xad\xe5\x9b\xbd\xe5\xa4\xa7\xe9\x99\x86; 2 \xe6\xb5\xb7\xe5\xa4\x96\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\x85\xac\xe6\xb0\x91\xe7\xb1\xbb\xe5\x9e\x8b: 0 \xe6\x9c\xaa\xe5\xae\x9e\xe5\x90\x8d; 1 \xe4\xb8\xad\xe5\x9b\xbd\xe5\xa4\xa7\xe9\x99\x86; 2 \xe6\xb5\xb7\xe5\xa4\x96" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_CivicType = { "CivicType", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, CivicType), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_CivicType_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_CivicType_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Age_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
		{ "Comment", "// \xe5\xb9\xb4\xe9\xbe\x84: 0 \xe6\x9c\xaa\xe7\x9f\xa5; \xe5\x85\xb6\xe5\xae\x83\xe6\x95\xb0\xe5\x80\xbc\xe4\xb8\xba\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe5\xb9\xb4\xe9\xbe\x84\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xb9\xb4\xe9\xbe\x84: 0 \xe6\x9c\xaa\xe7\x9f\xa5; \xe5\x85\xb6\xe5\xae\x83\xe6\x95\xb0\xe5\x80\xbc\xe4\xb8\xba\xe5\xbd\x93\xe5\x89\x8d\xe7\x94\xa8\xe6\x88\xb7\xe7\x9a\x84\xe5\xb9\xb4\xe9\xbe\x84" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Age = { "Age", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, Age), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Age_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Age_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Gender_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
		{ "Comment", "// \xe6\x80\xa7\xe5\x88\xab: 0 \xe6\x9c\xaa\xe7\x9f\xa5; 1 \xe7\x94\xb7; 2 \xe5\xa5\xb3\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x80\xa7\xe5\x88\xab: 0 \xe6\x9c\xaa\xe7\x9f\xa5; 1 \xe7\x94\xb7; 2 \xe5\xa5\xb3" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Gender = { "Gender", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, Gender), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Gender_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Gender_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_AccountType_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
		{ "Comment", "// \xe8\xb4\xa6\xe5\x8f\xb7\xe7\xb1\xbb\xe5\x9e\x8b: 0 \xe6\x9c\xaa\xe6\x88\x90\xe5\xb9\xb4;1 \xe6\x88\x90\xe5\xb9\xb4\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xb4\xa6\xe5\x8f\xb7\xe7\xb1\xbb\xe5\x9e\x8b: 0 \xe6\x9c\xaa\xe6\x88\x90\xe5\xb9\xb4;1 \xe6\x88\x90\xe5\xb9\xb4" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_AccountType = { "AccountType", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, AccountType), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_AccountType_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_AccountType_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_RequestIp_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
		{ "Comment", "// \xe8\xaf\xb7\xe6\xb1\x82IP\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xaf\xb7\xe6\xb1\x82IP" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_RequestIp = { "RequestIp", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, RequestIp), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_RequestIp_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_RequestIp_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_DayOnlineDuration_MetaData[] = {
		{ "Category", "OneEngineSDK|AntiAddictionInfo" },
		{ "Comment", "// \xe5\xbd\x93\xe6\x97\xa5\xe5\x9c\xa8\xe7\xba\xbf\xe6\x97\xb6\xe9\x95\xbf\xef\xbc\x8c\xe5\x8d\x95\xe4\xbd\x8d\xef\xbc\x9a\xe5\x88\x86\xe9\x92\x9f\xe3\x80\x82\xe5\x85\xb3\xe9\x97\xad\xe9\x98\xb2\xe6\xb2\x89\xe8\xbf\xb7\xe6\x97\xb6\xe8\xaf\xa5\xe9\xa1\xb9\xe4\xb8\xba 0 \n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xbd\x93\xe6\x97\xa5\xe5\x9c\xa8\xe7\xba\xbf\xe6\x97\xb6\xe9\x95\xbf\xef\xbc\x8c\xe5\x8d\x95\xe4\xbd\x8d\xef\xbc\x9a\xe5\x88\x86\xe9\x92\x9f\xe3\x80\x82\xe5\x85\xb3\xe9\x97\xad\xe9\x98\xb2\xe6\xb2\x89\xe8\xbf\xb7\xe6\x97\xb6\xe8\xaf\xa5\xe9\xa1\xb9\xe4\xb8\xba 0" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_DayOnlineDuration = { "DayOnlineDuration", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneAntiAddictionInfo, DayOnlineDuration), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_DayOnlineDuration_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_DayOnlineDuration_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_AppID,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_UserId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Status,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_HeartbeatInterval,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BannedType,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BannedReason,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_BreakNotice,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Realuser,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_CivicType,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Age,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_Gender,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_AccountType,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_RequestIp,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::NewProp_DayOnlineDuration,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OneAntiAddictionInfo",
		sizeof(FOneAntiAddictionInfo),
		alignof(FOneAntiAddictionInfo),
		Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOneAntiAddictionInfo()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OneAntiAddictionInfo"), sizeof(FOneAntiAddictionInfo), Get_Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOneAntiAddictionInfo_Hash() { return 2160042976U; }
class UScriptStruct* FOneDeviceInfo::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOneDeviceInfo_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneDeviceInfo, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneDeviceInfo"), sizeof(FOneDeviceInfo), Get_Z_Construct_UScriptStruct_FOneDeviceInfo_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOneDeviceInfo>()
{
	return FOneDeviceInfo::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOneDeviceInfo(FOneDeviceInfo::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OneDeviceInfo"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneDeviceInfo
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneDeviceInfo()
	{
		UScriptStruct::DeferCppStructOps<FOneDeviceInfo>(FName(TEXT("OneDeviceInfo")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOneDeviceInfo;
	struct Z_Construct_UScriptStruct_FOneDeviceInfo_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DeviceId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_DeviceId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_DeviceSys_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_DeviceSys;
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Ext_ValueProp;
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Ext_Key_KeyProp;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Ext_MetaData[];
#endif
		static const UE4CodeGen_Private::FMapPropertyParams NewProp_Ext;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Comment", "// \xe8\xae\xbe\xe5\xa4\x87\xe4\xbf\xa1\xe6\x81\xaf\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xae\xbe\xe5\xa4\x87\xe4\xbf\xa1\xe6\x81\xaf" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneDeviceInfo>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_DeviceId_MetaData[] = {
		{ "Category", "OneEngineSDK|DeviceInfo" },
		{ "Comment", "//\xe8\xae\xbe\xe5\xa4\x87\xe5\x94\xaf\xe4\xb8\x80\xe6\xa0\x87\xe8\xaf\x86\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xae\xbe\xe5\xa4\x87\xe5\x94\xaf\xe4\xb8\x80\xe6\xa0\x87\xe8\xaf\x86" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_DeviceId = { "DeviceId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneDeviceInfo, DeviceId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_DeviceId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_DeviceId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_DeviceSys_MetaData[] = {
		{ "Category", "OneEngineSDK|DeviceInfo" },
		{ "Comment", "//\xe8\xae\xbe\xe5\xa4\x87\xe6\x93\x8d\xe4\xbd\x9c\xe7\xb3\xbb\xe7\xbb\x9f\xe7\x89\x88\xe6\x9c\xac\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xae\xbe\xe5\xa4\x87\xe6\x93\x8d\xe4\xbd\x9c\xe7\xb3\xbb\xe7\xbb\x9f\xe7\x89\x88\xe6\x9c\xac" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_DeviceSys = { "DeviceSys", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneDeviceInfo, DeviceSys), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_DeviceSys_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_DeviceSys_MetaData)) };
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_Ext_ValueProp = { "Ext", nullptr, (EPropertyFlags)0x0000000000000001, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, 1, METADATA_PARAMS(nullptr, 0) };
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_Ext_Key_KeyProp = { "Ext_Key", nullptr, (EPropertyFlags)0x0000000000000001, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_Ext_MetaData[] = {
		{ "Category", "OneEngineSDK|DeviceInfo" },
		{ "Comment", "//\xe8\xae\xbe\xe5\xa4\x87\xe5\x85\xb6\xe4\xbb\x96\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe6\xb8\xb8\xe6\x88\x8f\xe6\x8c\x89\xe7\x85\xa7\xe9\x9c\x80\xe8\xa6\x81\xe8\xbf\x9b\xe8\xa1\x8c\xe8\x8e\xb7\xe5\x8f\x96\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xae\xbe\xe5\xa4\x87\xe5\x85\xb6\xe4\xbb\x96\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe6\xb8\xb8\xe6\x88\x8f\xe6\x8c\x89\xe7\x85\xa7\xe9\x9c\x80\xe8\xa6\x81\xe8\xbf\x9b\xe8\xa1\x8c\xe8\x8e\xb7\xe5\x8f\x96" },
	};
#endif
	const UE4CodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_Ext = { "Ext", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneDeviceInfo, Ext), EMapPropertyFlags::None, METADATA_PARAMS(Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_Ext_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_Ext_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_DeviceId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_DeviceSys,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_Ext_ValueProp,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_Ext_Key_KeyProp,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::NewProp_Ext,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OneDeviceInfo",
		sizeof(FOneDeviceInfo),
		alignof(FOneDeviceInfo),
		Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOneDeviceInfo()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOneDeviceInfo_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OneDeviceInfo"), sizeof(FOneDeviceInfo), Get_Z_Construct_UScriptStruct_FOneDeviceInfo_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOneDeviceInfo_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOneDeviceInfo_Hash() { return 2768638308U; }
class UScriptStruct* FOneUserLocationInfo::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOneUserLocationInfo_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneUserLocationInfo, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneUserLocationInfo"), sizeof(FOneUserLocationInfo), Get_Z_Construct_UScriptStruct_FOneUserLocationInfo_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOneUserLocationInfo>()
{
	return FOneUserLocationInfo::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOneUserLocationInfo(FOneUserLocationInfo::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OneUserLocationInfo"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneUserLocationInfo
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneUserLocationInfo()
	{
		UScriptStruct::DeferCppStructOps<FOneUserLocationInfo>(FName(TEXT("OneUserLocationInfo")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOneUserLocationInfo;
	struct Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CountryAbbr_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_CountryAbbr;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Country_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Country;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Province_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Province;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_City_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_City;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CountryCode_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_CountryCode;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_IP_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_IP;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe4\xbd\x8d\xe7\xbd\xae\xe4\xbf\xa1\xe6\x81\xaf\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe4\xbd\x8d\xe7\xbd\xae\xe4\xbf\xa1\xe6\x81\xaf" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneUserLocationInfo>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_CountryAbbr_MetaData[] = {
		{ "Category", "OneEngineSDK|LocationInfo" },
		{ "Comment", "//\xe6\x9c\x8d\xe5\x8a\xa1\xe7\xab\xaf\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x9c\x8d\xe5\x8a\xa1\xe7\xab\xaf\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_CountryAbbr = { "CountryAbbr", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserLocationInfo, CountryAbbr), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_CountryAbbr_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_CountryAbbr_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_Country_MetaData[] = {
		{ "Category", "OneEngineSDK|LocationInfo" },
		{ "Comment", "//\xe5\x9b\xbd\xe5\xae\xb6\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\x9b\xbd\xe5\xae\xb6" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_Country = { "Country", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserLocationInfo, Country), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_Country_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_Country_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_Province_MetaData[] = {
		{ "Category", "OneEngineSDK|LocationInfo" },
		{ "Comment", "//\xe7\x9c\x81\xe4\xbb\xbd\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x9c\x81\xe4\xbb\xbd" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_Province = { "Province", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserLocationInfo, Province), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_Province_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_Province_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_City_MetaData[] = {
		{ "Category", "OneEngineSDK|LocationInfo" },
		{ "Comment", "//\xe5\x9f\x8e\xe5\xb8\x82\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\x9f\x8e\xe5\xb8\x82" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_City = { "City", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserLocationInfo, City), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_City_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_City_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_CountryCode_MetaData[] = {
		{ "Category", "OneEngineSDK|LocationInfo" },
		{ "Comment", "//\xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_CountryCode = { "CountryCode", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserLocationInfo, CountryCode), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_CountryCode_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_CountryCode_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_IP_MetaData[] = {
		{ "Category", "OneEngineSDK|LocationInfo" },
		{ "Comment", "//ip\xe5\x9c\xb0\xe5\x9d\x80\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "ip\xe5\x9c\xb0\xe5\x9d\x80" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_IP = { "IP", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserLocationInfo, IP), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_IP_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_IP_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_CountryAbbr,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_Country,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_Province,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_City,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_CountryCode,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::NewProp_IP,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OneUserLocationInfo",
		sizeof(FOneUserLocationInfo),
		alignof(FOneUserLocationInfo),
		Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOneUserLocationInfo()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOneUserLocationInfo_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OneUserLocationInfo"), sizeof(FOneUserLocationInfo), Get_Z_Construct_UScriptStruct_FOneUserLocationInfo_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOneUserLocationInfo_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOneUserLocationInfo_Hash() { return 4195672215U; }
class UScriptStruct* FOneRoleInfo::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOneRoleInfo_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneRoleInfo, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneRoleInfo"), sizeof(FOneRoleInfo), Get_Z_Construct_UScriptStruct_FOneRoleInfo_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOneRoleInfo>()
{
	return FOneRoleInfo::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOneRoleInfo(FOneRoleInfo::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OneRoleInfo"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneRoleInfo
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneRoleInfo()
	{
		UScriptStruct::DeferCppStructOps<FOneRoleInfo>(FName(TEXT("OneRoleInfo")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOneRoleInfo;
	struct Z_Construct_UScriptStruct_FOneRoleInfo_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_RoleId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_RoleId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_RoleName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_RoleName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Vip_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Vip;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Level_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Level;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ServerId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ServerId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ServerName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ServerName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CombatValue_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_CombatValue;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneRoleInfo_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2\xe4\xbf\xa1\xe6\x81\xaf\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2\xe4\xbf\xa1\xe6\x81\xaf" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneRoleInfo>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_RoleId_MetaData[] = {
		{ "Category", "OneEngineSDK|RoleInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_RoleId = { "RoleId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneRoleInfo, RoleId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_RoleId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_RoleId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_RoleName_MetaData[] = {
		{ "Category", "OneEngineSDK|RoleInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_RoleName = { "RoleName", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneRoleInfo, RoleName), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_RoleName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_RoleName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_Vip_MetaData[] = {
		{ "Category", "OneEngineSDK|RoleInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_Vip = { "Vip", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneRoleInfo, Vip), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_Vip_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_Vip_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_Level_MetaData[] = {
		{ "Category", "OneEngineSDK|RoleInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneRoleInfo, Level), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_Level_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_Level_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_ServerId_MetaData[] = {
		{ "Category", "OneEngineSDK|RoleInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_ServerId = { "ServerId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneRoleInfo, ServerId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_ServerId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_ServerId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_ServerName_MetaData[] = {
		{ "Category", "OneEngineSDK|RoleInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_ServerName = { "ServerName", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneRoleInfo, ServerName), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_ServerName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_ServerName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_CombatValue_MetaData[] = {
		{ "Category", "OneEngineSDK|RoleInfo" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_CombatValue = { "CombatValue", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneRoleInfo, CombatValue), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_CombatValue_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_CombatValue_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneRoleInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_RoleId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_RoleName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_Vip,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_Level,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_ServerId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_ServerName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneRoleInfo_Statics::NewProp_CombatValue,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneRoleInfo_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OneRoleInfo",
		sizeof(FOneRoleInfo),
		alignof(FOneRoleInfo),
		Z_Construct_UScriptStruct_FOneRoleInfo_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneRoleInfo_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOneRoleInfo()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOneRoleInfo_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OneRoleInfo"), sizeof(FOneRoleInfo), Get_Z_Construct_UScriptStruct_FOneRoleInfo_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOneRoleInfo_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOneRoleInfo_Hash() { return 2296941675U; }
class UScriptStruct* FOnePaymentInfo::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOnePaymentInfo_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOnePaymentInfo, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OnePaymentInfo"), sizeof(FOnePaymentInfo), Get_Z_Construct_UScriptStruct_FOnePaymentInfo_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOnePaymentInfo>()
{
	return FOnePaymentInfo::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOnePaymentInfo(FOnePaymentInfo::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OnePaymentInfo"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePaymentInfo
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePaymentInfo()
	{
		UScriptStruct::DeferCppStructOps<FOnePaymentInfo>(FName(TEXT("OnePaymentInfo")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOnePaymentInfo;
	struct Z_Construct_UScriptStruct_FOnePaymentInfo_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_OrderId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_OrderId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Price_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Price;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ProductName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ProductName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_GameServerId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_GameServerId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ServerName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ServerName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_RoleId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_RoleId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_RoleName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_RoleName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ProductId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ProductId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ProductCount_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ProductCount;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ExtInfo_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ExtInfo;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_PaySuccessUrl_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_PaySuccessUrl;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Comment", "// \xe6\x94\xaf\xe4\xbb\x98\xe5\x8f\x82\xe6\x95\xb0\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x94\xaf\xe4\xbb\x98\xe5\x8f\x82\xe6\x95\xb0" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOnePaymentInfo>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_OrderId_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
		{ "Comment", "// \xe8\xae\xa2\xe5\x8d\x95\xe5\x8f\xb7\xef\xbc\x8c\xe5\xbf\x85\xe9\xa1\xbb\xe5\x94\xaf\xe4\xb8\x80\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xae\xa2\xe5\x8d\x95\xe5\x8f\xb7\xef\xbc\x8c\xe5\xbf\x85\xe9\xa1\xbb\xe5\x94\xaf\xe4\xb8\x80" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_OrderId = { "OrderId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePaymentInfo, OrderId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_OrderId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_OrderId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_Price_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_Price = { "Price", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePaymentInfo, Price), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_Price_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_Price_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductName_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81\xe5\x90\x8d\xe7\xa7\xb0\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81\xe5\x90\x8d\xe7\xa7\xb0" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductName = { "ProductName", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePaymentInfo, ProductName), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_GameServerId_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
		{ "Comment", "// \xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8ID\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8ID" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_GameServerId = { "GameServerId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePaymentInfo, GameServerId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_GameServerId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_GameServerId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ServerName_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
		{ "Comment", "// \xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe5\x90\x8d\xe7\xa7\xb0\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\xb8\xb8\xe6\x88\x8f\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe5\x90\x8d\xe7\xa7\xb0" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ServerName = { "ServerName", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePaymentInfo, ServerName), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ServerName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ServerName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_RoleId_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2ID\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2ID" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_RoleId = { "RoleId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePaymentInfo, RoleId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_RoleId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_RoleId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_RoleName_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
		{ "Comment", "// \xe8\xa7\x92\xe8\x89\xb2\xe5\x90\x8d\xe7\xa7\xb0\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xa7\x92\xe8\x89\xb2\xe5\x90\x8d\xe7\xa7\xb0" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_RoleName = { "RoleName", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePaymentInfo, RoleName), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_RoleName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_RoleName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductId_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81ID\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81ID" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductId = { "ProductId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePaymentInfo, ProductId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductCount_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81\xe6\x95\xb0\xe9\x87\x8f\xef\xbc\x8c\xe9\xbb\x98\xe8\xae\xa4""1\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81\xe6\x95\xb0\xe9\x87\x8f\xef\xbc\x8c\xe9\xbb\x98\xe8\xae\xa4""1" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductCount = { "ProductCount", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePaymentInfo, ProductCount), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductCount_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductCount_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ExtInfo_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
		{ "Comment", "// \xe6\x89\xa9\xe5\xb1\x95\xe5\xad\x97\xe6\xae\xb5\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe9\x80\x8f\xe4\xbc\xa0\xe4\xbf\xa1\xe6\x81\xaf\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x89\xa9\xe5\xb1\x95\xe5\xad\x97\xe6\xae\xb5\xef\xbc\x8c\xe7\x94\xa8\xe4\xba\x8e\xe9\x80\x8f\xe4\xbc\xa0\xe4\xbf\xa1\xe6\x81\xaf" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ExtInfo = { "ExtInfo", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePaymentInfo, ExtInfo), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ExtInfo_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ExtInfo_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_PaySuccessUrl_MetaData[] = {
		{ "Category", "OneEngineSDK|Pay" },
		{ "Comment", "// \xe6\x94\xaf\xe4\xbb\x98\xe6\x88\x90\xe5\x8a\x9f\xe4\xbb\xa5\xe5\x90\x8e\xef\xbc\x8c\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe7\x9a\x84\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xef\xbc\x8c\xe6\xb5\xb7\xe5\xa4\x96\xe6\x97\xb6\xe4\xb8\x8d\xe8\x83\xbd\xe4\xb8\xba\xe7\xa9\xba\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x94\xaf\xe4\xbb\x98\xe6\x88\x90\xe5\x8a\x9f\xe4\xbb\xa5\xe5\x90\x8e\xef\xbc\x8c\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x99\xa8\xe7\x9a\x84\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xef\xbc\x8c\xe6\xb5\xb7\xe5\xa4\x96\xe6\x97\xb6\xe4\xb8\x8d\xe8\x83\xbd\xe4\xb8\xba\xe7\xa9\xba" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_PaySuccessUrl = { "PaySuccessUrl", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOnePaymentInfo, PaySuccessUrl), METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_PaySuccessUrl_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_PaySuccessUrl_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_OrderId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_Price,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_GameServerId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ServerName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_RoleId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_RoleName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ProductCount,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_ExtInfo,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::NewProp_PaySuccessUrl,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OnePaymentInfo",
		sizeof(FOnePaymentInfo),
		alignof(FOnePaymentInfo),
		Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOnePaymentInfo()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOnePaymentInfo_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OnePaymentInfo"), sizeof(FOnePaymentInfo), Get_Z_Construct_UScriptStruct_FOnePaymentInfo_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOnePaymentInfo_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOnePaymentInfo_Hash() { return 4192616871U; }
class UScriptStruct* FOneProductInfo::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOneProductInfo_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneProductInfo, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneProductInfo"), sizeof(FOneProductInfo), Get_Z_Construct_UScriptStruct_FOneProductInfo_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOneProductInfo>()
{
	return FOneProductInfo::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOneProductInfo(FOneProductInfo::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OneProductInfo"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneProductInfo
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneProductInfo()
	{
		UScriptStruct::DeferCppStructOps<FOneProductInfo>(FName(TEXT("OneProductInfo")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOneProductInfo;
	struct Z_Construct_UScriptStruct_FOneProductInfo_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ProductId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ProductId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Price_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Price;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Currency_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Currency;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SymbolPrice_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_SymbolPrice;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Title;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Desc_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Desc;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneProductInfo_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Comment", "// \xe6\x94\xaf\xe4\xbb\x98\xe6\xa1\xa3\xe4\xbd\x8d\xe4\xbf\xa1\xe6\x81\xaf\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x94\xaf\xe4\xbb\x98\xe6\xa1\xa3\xe4\xbd\x8d\xe4\xbf\xa1\xe6\x81\xaf" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneProductInfo>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_ProductId_MetaData[] = {
		{ "Category", "OneEngineSDK|ProductInfo" },
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81ID\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81ID" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_ProductId = { "ProductId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneProductInfo, ProductId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_ProductId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_ProductId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Price_MetaData[] = {
		{ "Category", "OneEngineSDK|ProductInfo" },
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Price = { "Price", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneProductInfo, Price), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Price_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Price_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Currency_MetaData[] = {
		{ "Category", "OneEngineSDK|ProductInfo" },
		{ "Comment", "// \xe8\xb4\xa7\xe5\xb8\x81\xe4\xbb\xa3\xe7\xa0\x81\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe8\xb4\xa7\xe5\xb8\x81\xe4\xbb\xa3\xe7\xa0\x81" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Currency = { "Currency", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneProductInfo, Currency), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Currency_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Currency_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_SymbolPrice_MetaData[] = {
		{ "Category", "OneEngineSDK|ProductInfo" },
		{ "Comment", "// \xe5\xb8\xa6\xe8\xb4\xa7\xe5\xb8\x81\xe7\xac\xa6\xe5\x8f\xb7\xe7\x9a\x84\xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xb8\xa6\xe8\xb4\xa7\xe5\xb8\x81\xe7\xac\xa6\xe5\x8f\xb7\xe7\x9a\x84\xe5\x95\x86\xe5\x93\x81\xe4\xbb\xb7\xe6\xa0\xbc" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_SymbolPrice = { "SymbolPrice", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneProductInfo, SymbolPrice), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_SymbolPrice_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_SymbolPrice_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Title_MetaData[] = {
		{ "Category", "OneEngineSDK|ProductInfo" },
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81\xe6\xa0\x87\xe9\xa2\x98\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81\xe6\xa0\x87\xe9\xa2\x98" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneProductInfo, Title), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Title_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Title_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Desc_MetaData[] = {
		{ "Category", "OneEngineSDK|ProductInfo" },
		{ "Comment", "// \xe5\x95\x86\xe5\x93\x81\xe6\x8f\x8f\xe8\xbf\xb0\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\x95\x86\xe5\x93\x81\xe6\x8f\x8f\xe8\xbf\xb0" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Desc = { "Desc", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneProductInfo, Desc), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Desc_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Desc_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneProductInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_ProductId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Price,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Currency,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_SymbolPrice,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Title,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneProductInfo_Statics::NewProp_Desc,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneProductInfo_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OneProductInfo",
		sizeof(FOneProductInfo),
		alignof(FOneProductInfo),
		Z_Construct_UScriptStruct_FOneProductInfo_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneProductInfo_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOneProductInfo_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneProductInfo_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOneProductInfo()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOneProductInfo_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OneProductInfo"), sizeof(FOneProductInfo), Get_Z_Construct_UScriptStruct_FOneProductInfo_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOneProductInfo_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOneProductInfo_Hash() { return 1739066781U; }
class UScriptStruct* FOneUserInfo::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOneUserInfo_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneUserInfo, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneUserInfo"), sizeof(FOneUserInfo), Get_Z_Construct_UScriptStruct_FOneUserInfo_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOneUserInfo>()
{
	return FOneUserInfo::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOneUserInfo(FOneUserInfo::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OneUserInfo"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneUserInfo
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneUserInfo()
	{
		UScriptStruct::DeferCppStructOps<FOneUserInfo>(FName(TEXT("OneUserInfo")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOneUserInfo;
	struct Z_Construct_UScriptStruct_FOneUserInfo_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UserId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_UserId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Token_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Token;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Phone_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Phone;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Avatar_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Avatar;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UserName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_UserName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_InheritCode_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_InheritCode;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bPasswordExist_MetaData[];
#endif
		static void NewProp_bPasswordExist_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bPasswordExist;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CountryCode_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_CountryCode;
		static const UE4CodeGen_Private::FStructPropertyParams NewProp_Thirds_Inner;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Thirds_MetaData[];
#endif
		static const UE4CodeGen_Private::FArrayPropertyParams NewProp_Thirds;
		static const UE4CodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[];
#endif
		static const UE4CodeGen_Private::FEnumPropertyParams NewProp_Type;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bIsNewCreate_MetaData[];
#endif
		static void NewProp_bIsNewCreate_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsNewCreate;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Age_MetaData[];
#endif
		static const UE4CodeGen_Private::FIntPropertyParams NewProp_Age;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_bIsAdult_MetaData[];
#endif
		static void NewProp_bIsAdult_SetBit(void* Obj);
		static const UE4CodeGen_Private::FBoolPropertyParams NewProp_bIsAdult;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_AgeCountryCode_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_AgeCountryCode;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserInfo_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe4\xbf\xa1\xe6\x81\xaf\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe4\xbf\xa1\xe6\x81\xaf" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneUserInfo>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_UserId_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7ID\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7ID" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_UserId = { "UserId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserInfo, UserId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_UserId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_UserId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Token_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7token\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7token" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Token = { "Token", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserInfo, Token), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Token_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Token_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Phone_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe6\x89\x8b\xe6\x9c\xba\xe5\x8f\xb7\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x89\x8b\xe6\x9c\xba\xe5\x8f\xb7" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Phone = { "Phone", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserInfo, Phone), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Phone_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Phone_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Avatar_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe5\xa4\xb4\xe5\x83\x8f\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe5\xa4\xb4\xe5\x83\x8f" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Avatar = { "Avatar", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserInfo, Avatar), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Avatar_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Avatar_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_UserName_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8d\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8d" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_UserName = { "UserName", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserInfo, UserName), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_UserName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_UserName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_InheritCode_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe7\xbb\xa7\xe6\x89\xbf\xe7\xa0\x81\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\xbb\xa7\xe6\x89\xbf\xe7\xa0\x81" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_InheritCode = { "InheritCode", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserInfo, InheritCode), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_InheritCode_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_InheritCode_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bPasswordExist_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe5\xad\x98\xe5\x9c\xa8\xe6\x89\x8b\xe6\x9c\xba\xe8\xb4\xa6\xe5\x8f\xb7\xe5\xaf\x86\xe7\xa0\x81\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe5\xad\x98\xe5\x9c\xa8\xe6\x89\x8b\xe6\x9c\xba\xe8\xb4\xa6\xe5\x8f\xb7\xe5\xaf\x86\xe7\xa0\x81" },
	};
#endif
	void Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bPasswordExist_SetBit(void* Obj)
	{
		((FOneUserInfo*)Obj)->bPasswordExist = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bPasswordExist = { "bPasswordExist", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(FOneUserInfo), &Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bPasswordExist_SetBit, METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bPasswordExist_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bPasswordExist_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_CountryCode_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xae\xa2\xe6\x88\xb7\xe7\xab\xaf\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_CountryCode = { "CountryCode", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserInfo, CountryCode), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_CountryCode_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_CountryCode_MetaData)) };
	const UE4CodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Thirds_Inner = { "Thirds", nullptr, (EPropertyFlags)0x0000000000000000, UE4CodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, Z_Construct_UScriptStruct_FOneUserThirdInfo, METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Thirds_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "//  \xe4\xb8\x89\xe6\x96\xb9\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\x97\xe8\xa1\xa8\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe4\xb8\x89\xe6\x96\xb9\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\x97\xe8\xa1\xa8" },
	};
#endif
	const UE4CodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Thirds = { "Thirds", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserInfo, Thirds), EArrayPropertyFlags::None, METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Thirds_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Thirds_MetaData)) };
	const UE4CodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UE4CodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, nullptr, METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Type_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "//  \xe5\xb9\xb3\xe5\x8f\xb0\xe7\xb1\xbb\xe5\x9e\x8b\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xb9\xb3\xe5\x8f\xb0\xe7\xb1\xbb\xe5\x9e\x8b" },
	};
#endif
	const UE4CodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserInfo, Type), Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType, METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Type_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Type_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsNewCreate_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba\xe6\x96\xb0\xe7\x94\xa8\xe6\x88\xb7\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba\xe6\x96\xb0\xe7\x94\xa8\xe6\x88\xb7" },
	};
#endif
	void Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsNewCreate_SetBit(void* Obj)
	{
		((FOneUserInfo*)Obj)->bIsNewCreate = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsNewCreate = { "bIsNewCreate", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(FOneUserInfo), &Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsNewCreate_SetBit, METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsNewCreate_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsNewCreate_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Age_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe5\xb9\xb4\xe9\xbe\x84\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xb9\xb4\xe9\xbe\x84" },
	};
#endif
	const UE4CodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Age = { "Age", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserInfo, Age), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Age_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Age_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsAdult_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe6\x98\xaf\xe5\x90\xa6\xe6\x88\x90\xe5\xb9\xb4\xe4\xba\xba\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe6\x98\xaf\xe5\x90\xa6\xe6\x88\x90\xe5\xb9\xb4\xe4\xba\xba" },
	};
#endif
	void Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsAdult_SetBit(void* Obj)
	{
		((FOneUserInfo*)Obj)->bIsAdult = 1;
	}
	const UE4CodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsAdult = { "bIsAdult", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Bool | UE4CodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, 1, sizeof(bool), sizeof(FOneUserInfo), &Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsAdult_SetBit, METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsAdult_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsAdult_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_AgeCountryCode_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\x9b\xbd\xe5\xae\xb6\xe4\xbb\xa3\xe7\xa0\x81" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_AgeCountryCode = { "AgeCountryCode", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserInfo, AgeCountryCode), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_AgeCountryCode_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_AgeCountryCode_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneUserInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_UserId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Token,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Phone,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Avatar,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_UserName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_InheritCode,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bPasswordExist,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_CountryCode,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Thirds_Inner,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Thirds,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Type_Underlying,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Type,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsNewCreate,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_Age,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_bIsAdult,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserInfo_Statics::NewProp_AgeCountryCode,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneUserInfo_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OneUserInfo",
		sizeof(FOneUserInfo),
		alignof(FOneUserInfo),
		Z_Construct_UScriptStruct_FOneUserInfo_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserInfo_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserInfo_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOneUserInfo()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOneUserInfo_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OneUserInfo"), sizeof(FOneUserInfo), Get_Z_Construct_UScriptStruct_FOneUserInfo_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOneUserInfo_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOneUserInfo_Hash() { return 2416585564U; }
class UScriptStruct* FOneUserThirdInfo::StaticStruct()
{
	static class UScriptStruct* Singleton = NULL;
	if (!Singleton)
	{
		extern ONEENGINESDK_API uint32 Get_Z_Construct_UScriptStruct_FOneUserThirdInfo_Hash();
		Singleton = GetStaticStruct(Z_Construct_UScriptStruct_FOneUserThirdInfo, Z_Construct_UPackage__Script_OneEngineSDK(), TEXT("OneUserThirdInfo"), sizeof(FOneUserThirdInfo), Get_Z_Construct_UScriptStruct_FOneUserThirdInfo_Hash());
	}
	return Singleton;
}
template<> ONEENGINESDK_API UScriptStruct* StaticStruct<FOneUserThirdInfo>()
{
	return FOneUserThirdInfo::StaticStruct();
}
static FCompiledInDeferStruct Z_CompiledInDeferStruct_UScriptStruct_FOneUserThirdInfo(FOneUserThirdInfo::StaticStruct, TEXT("/Script/OneEngineSDK"), TEXT("OneUserThirdInfo"), false, nullptr, nullptr);
static struct FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneUserThirdInfo
{
	FScriptStruct_OneEngineSDK_StaticRegisterNativesFOneUserThirdInfo()
	{
		UScriptStruct::DeferCppStructOps<FOneUserThirdInfo>(FName(TEXT("OneUserThirdInfo")));
	}
} ScriptStruct_OneEngineSDK_StaticRegisterNativesFOneUserThirdInfo;
	struct Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics
	{
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[];
#endif
		static void* NewStructOps();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UserId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_UserId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ThirdId_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_ThirdId;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Avatar_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Avatar;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_UserName_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_UserName;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Email_MetaData[];
#endif
		static const UE4CodeGen_Private::FStrPropertyParams NewProp_Email;
		static const UE4CodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[];
#endif
		static const UE4CodeGen_Private::FEnumPropertyParams NewProp_Type;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const UE4CodeGen_Private::FStructParams ReturnStructParams;
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe4\xb8\x89\xe6\x96\xb9\xe5\xb9\xb3\xe5\x8f\xb0\xe4\xbf\xa1\xe6\x81\xaf\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe4\xb8\x89\xe6\x96\xb9\xe5\xb9\xb3\xe5\x8f\xb0\xe4\xbf\xa1\xe6\x81\xaf" },
	};
#endif
	void* Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FOneUserThirdInfo>();
	}
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_UserId_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7ID\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7ID" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_UserId = { "UserId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserThirdInfo, UserId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_UserId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_UserId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_ThirdId_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7ThirdId\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7ThirdId" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_ThirdId = { "ThirdId", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserThirdInfo, ThirdId), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_ThirdId_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_ThirdId_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Avatar_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe5\xa4\xb4\xe5\x83\x8f\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe5\xa4\xb4\xe5\x83\x8f" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Avatar = { "Avatar", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserThirdInfo, Avatar), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Avatar_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Avatar_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_UserName_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// \xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8d\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe7\x94\xa8\xe6\x88\xb7\xe5\x90\x8d" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_UserName = { "UserName", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserThirdInfo, UserName), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_UserName_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_UserName_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Email_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "// Email\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "Email" },
	};
#endif
	const UE4CodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Email = { "Email", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserThirdInfo, Email), METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Email_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Email_MetaData)) };
	const UE4CodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UE4CodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, 1, 0, nullptr, METADATA_PARAMS(nullptr, 0) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Type_MetaData[] = {
		{ "Category", "OneEngineSDK|UserInfo" },
		{ "Comment", "//  \xe5\xb9\xb3\xe5\x8f\xb0\xe7\xb1\xbb\xe5\x9e\x8b\n" },
		{ "ModuleRelativePath", "Public/OneEngineSDKHelper.h" },
		{ "ToolTip", "\xe5\xb9\xb3\xe5\x8f\xb0\xe7\xb1\xbb\xe5\x9e\x8b" },
	};
#endif
	const UE4CodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000005, UE4CodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(FOneUserThirdInfo, Type), Z_Construct_UEnum_OneEngineSDK_EOneEngineThirdType, METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Type_MetaData, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Type_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_UserId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_ThirdId,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Avatar,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_UserName,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Email,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Type_Underlying,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::NewProp_Type,
	};
	const UE4CodeGen_Private::FStructParams Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::ReturnStructParams = {
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
		nullptr,
		&NewStructOps,
		"OneUserThirdInfo",
		sizeof(FOneUserThirdInfo),
		alignof(FOneUserThirdInfo),
		Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::PropPointers,
		UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::PropPointers),
		RF_Public|RF_Transient|RF_MarkAsNative,
		EStructFlags(0x00000001),
		METADATA_PARAMS(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::Struct_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::Struct_MetaDataParams))
	};
	UScriptStruct* Z_Construct_UScriptStruct_FOneUserThirdInfo()
	{
#if WITH_HOT_RELOAD
		extern uint32 Get_Z_Construct_UScriptStruct_FOneUserThirdInfo_Hash();
		UPackage* Outer = Z_Construct_UPackage__Script_OneEngineSDK();
		static UScriptStruct* ReturnStruct = FindExistingStructIfHotReloadOrDynamic(Outer, TEXT("OneUserThirdInfo"), sizeof(FOneUserThirdInfo), Get_Z_Construct_UScriptStruct_FOneUserThirdInfo_Hash(), false);
#else
		static UScriptStruct* ReturnStruct = nullptr;
#endif
		if (!ReturnStruct)
		{
			UE4CodeGen_Private::ConstructUScriptStruct(ReturnStruct, Z_Construct_UScriptStruct_FOneUserThirdInfo_Statics::ReturnStructParams);
		}
		return ReturnStruct;
	}
	uint32 Get_Z_Construct_UScriptStruct_FOneUserThirdInfo_Hash() { return 779426228U; }
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
