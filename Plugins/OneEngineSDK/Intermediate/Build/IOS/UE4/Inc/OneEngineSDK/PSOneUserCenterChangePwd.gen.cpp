// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OneEngineSDK/Private/Views/PSOneUserCenterChangePwd.h"
#ifdef _MSC_VER
#pragma warning (push)
#pragma warning (disable : 4883)
#endif
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePSOneUserCenterChangePwd() {}
// Cross Module References
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterChangePwd_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneUserCenterChangePwd();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneFocusUserWidget();
	UPackage* Z_Construct_UPackage__Script_OneEngineSDK();
	UMG_API UClass* Z_Construct_UClass_UScaleBox_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UTextBlock_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneTextFieldBase_NoRegister();
	ONEENGINESDK_API UClass* Z_Construct_UClass_UPSOneButtonBase_NoRegister();
	UMG_API UClass* Z_Construct_UClass_UImage_NoRegister();
// End Cross Module References
	void UPSOneUserCenterChangePwd::StaticRegisterNativesUPSOneUserCenterChangePwd()
	{
	}
	UClass* Z_Construct_UClass_UPSOneUserCenterChangePwd_NoRegister()
	{
		return UPSOneUserCenterChangePwd::StaticClass();
	}
	struct Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics
	{
		static UObject* (*const DependentSingletons[])();
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam Class_MetaDataParams[];
#endif
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ScaleBox_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ScaleBox;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TitleText_MetaData[];
#endif
		static const UE4CodeGen_Private::FTextPropertyParams NewProp_TitleText;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_TitleBlock_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_TitleBlock;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SendCodeTip_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_SendCodeTip;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_CodeInputTextField_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_CodeInputTextField;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_SetNewPwdTip_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_SetNewPwdTip;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_NewPwdTextField_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_NewPwdTextField;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ConfirmPwdTextField_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ConfirmPwdTextField;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_ConfirmButton_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_ConfirmButton;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_EnterIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_EnterIcon;
#if WITH_METADATA
		static const UE4CodeGen_Private::FMetaDataPairParam NewProp_BackspaceIcon_MetaData[];
#endif
		static const UE4CodeGen_Private::FObjectPropertyParams NewProp_BackspaceIcon;
		static const UE4CodeGen_Private::FPropertyParamsBase* const PropPointers[];
		static const FCppClassTypeInfoStatic StaticCppClassTypeInfo;
		static const UE4CodeGen_Private::FClassParams ClassParams;
	};
	UObject* (*const Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::DependentSingletons[])() = {
		(UObject* (*)())Z_Construct_UClass_UPSOneFocusUserWidget,
		(UObject* (*)())Z_Construct_UPackage__Script_OneEngineSDK,
	};
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::Class_MetaDataParams[] = {
		{ "Comment", "/**\n *\n */" },
		{ "IncludePath", "Views/PSOneUserCenterChangePwd.h" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterChangePwd.h" },
	};
#endif
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_ScaleBox_MetaData[] = {
		{ "BindWidget", "" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterChangePwd.h" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_ScaleBox = { "ScaleBox", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterChangePwd, ScaleBox), Z_Construct_UClass_UScaleBox_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_ScaleBox_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_ScaleBox_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_TitleText_MetaData[] = {
		{ "Category", "PSOneUserCenterChangePwd" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterChangePwd.h" },
	};
#endif
	const UE4CodeGen_Private::FTextPropertyParams Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_TitleText = { "TitleText", nullptr, (EPropertyFlags)0x0010000000000001, UE4CodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterChangePwd, TitleText), METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_TitleText_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_TitleText_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_TitleBlock_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// Title\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterChangePwd.h" },
		{ "ToolTip", "Title" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_TitleBlock = { "TitleBlock", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterChangePwd, TitleBlock), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_TitleBlock_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_TitleBlock_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_SendCodeTip_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe5\x8f\x91\xe9\x80\x81\xe6\x8f\x90\xe7\xa4\xba\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterChangePwd.h" },
		{ "ToolTip", "\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe5\x8f\x91\xe9\x80\x81\xe6\x8f\x90\xe7\xa4\xba" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_SendCodeTip = { "SendCodeTip", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterChangePwd, SendCodeTip), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_SendCodeTip_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_SendCodeTip_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_CodeInputTextField_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterChangePwd.h" },
		{ "ToolTip", "\xe9\xaa\x8c\xe8\xaf\x81\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_CodeInputTextField = { "CodeInputTextField", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterChangePwd, CodeInputTextField), Z_Construct_UClass_UPSOneTextFieldBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_CodeInputTextField_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_CodeInputTextField_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_SetNewPwdTip_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe8\xae\xbe\xe7\xbd\xae\xe6\x96\xb0\xe5\xaf\x86\xe7\xa0\x81\xe6\x8f\x90\xe7\xa4\xba\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterChangePwd.h" },
		{ "ToolTip", "\xe8\xae\xbe\xe7\xbd\xae\xe6\x96\xb0\xe5\xaf\x86\xe7\xa0\x81\xe6\x8f\x90\xe7\xa4\xba" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_SetNewPwdTip = { "SetNewPwdTip", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterChangePwd, SetNewPwdTip), Z_Construct_UClass_UTextBlock_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_SetNewPwdTip_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_SetNewPwdTip_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_NewPwdTextField_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe6\x96\xb0\xe5\xaf\x86\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterChangePwd.h" },
		{ "ToolTip", "\xe6\x96\xb0\xe5\xaf\x86\xe7\xa0\x81\xe8\xbe\x93\xe5\x85\xa5\xe6\xa1\x86" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_NewPwdTextField = { "NewPwdTextField", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterChangePwd, NewPwdTextField), Z_Construct_UClass_UPSOneTextFieldBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_NewPwdTextField_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_NewPwdTextField_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_ConfirmPwdTextField_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe7\xa1\xae\xe8\xae\xa4\xe6\x96\xb0\xe5\xaf\x86\xe7\xa0\x81\xe6\x8f\x90\xe7\xa4\xba\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterChangePwd.h" },
		{ "ToolTip", "\xe7\xa1\xae\xe8\xae\xa4\xe6\x96\xb0\xe5\xaf\x86\xe7\xa0\x81\xe6\x8f\x90\xe7\xa4\xba" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_ConfirmPwdTextField = { "ConfirmPwdTextField", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterChangePwd, ConfirmPwdTextField), Z_Construct_UClass_UPSOneTextFieldBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_ConfirmPwdTextField_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_ConfirmPwdTextField_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_ConfirmButton_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// \xe7\xa1\xae\xe8\xae\xa4\xe6\x8c\x89\xe9\x92\xae\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterChangePwd.h" },
		{ "ToolTip", "\xe7\xa1\xae\xe8\xae\xa4\xe6\x8c\x89\xe9\x92\xae" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_ConfirmButton = { "ConfirmButton", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterChangePwd, ConfirmButton), Z_Construct_UClass_UPSOneButtonBase_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_ConfirmButton_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_ConfirmButton_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_EnterIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// enter icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterChangePwd.h" },
		{ "ToolTip", "enter icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_EnterIcon = { "EnterIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterChangePwd, EnterIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_EnterIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_EnterIcon_MetaData)) };
#if WITH_METADATA
	const UE4CodeGen_Private::FMetaDataPairParam Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_BackspaceIcon_MetaData[] = {
		{ "BindWidget", "" },
		{ "Comment", "// backspace icon\n" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Private/Views/PSOneUserCenterChangePwd.h" },
		{ "ToolTip", "backspace icon" },
	};
#endif
	const UE4CodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_BackspaceIcon = { "BackspaceIcon", nullptr, (EPropertyFlags)0x0010000000080008, UE4CodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, 1, STRUCT_OFFSET(UPSOneUserCenterChangePwd, BackspaceIcon), Z_Construct_UClass_UImage_NoRegister, METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_BackspaceIcon_MetaData, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_BackspaceIcon_MetaData)) };
	const UE4CodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::PropPointers[] = {
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_ScaleBox,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_TitleText,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_TitleBlock,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_SendCodeTip,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_CodeInputTextField,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_SetNewPwdTip,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_NewPwdTextField,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_ConfirmPwdTextField,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_ConfirmButton,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_EnterIcon,
		(const UE4CodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::NewProp_BackspaceIcon,
	};
	const FCppClassTypeInfoStatic Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPSOneUserCenterChangePwd>::IsAbstract,
	};
	const UE4CodeGen_Private::FClassParams Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::ClassParams = {
		&UPSOneUserCenterChangePwd::StaticClass,
		nullptr,
		&StaticCppClassTypeInfo,
		DependentSingletons,
		nullptr,
		Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::PropPointers,
		nullptr,
		UE_ARRAY_COUNT(DependentSingletons),
		0,
		UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::PropPointers),
		0,
		0x00B010A0u,
		METADATA_PARAMS(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::Class_MetaDataParams, UE_ARRAY_COUNT(Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::Class_MetaDataParams))
	};
	UClass* Z_Construct_UClass_UPSOneUserCenterChangePwd()
	{
		static UClass* OuterClass = nullptr;
		if (!OuterClass)
		{
			UE4CodeGen_Private::ConstructUClass(OuterClass, Z_Construct_UClass_UPSOneUserCenterChangePwd_Statics::ClassParams);
		}
		return OuterClass;
	}
	IMPLEMENT_CLASS(UPSOneUserCenterChangePwd, 3893630269);
	template<> ONEENGINESDK_API UClass* StaticClass<UPSOneUserCenterChangePwd>()
	{
		return UPSOneUserCenterChangePwd::StaticClass();
	}
	static FCompiledInDefer Z_CompiledInDefer_UClass_UPSOneUserCenterChangePwd(Z_Construct_UClass_UPSOneUserCenterChangePwd, &UPSOneUserCenterChangePwd::StaticClass, TEXT("/Script/OneEngineSDK"), TEXT("UPSOneUserCenterChangePwd"), false, nullptr, nullptr, nullptr);
	DEFINE_VTABLE_PTR_HELPER_CTOR(UPSOneUserCenterChangePwd);
PRAGMA_ENABLE_DEPRECATION_WARNINGS
#ifdef _MSC_VER
#pragma warning (pop)
#endif
