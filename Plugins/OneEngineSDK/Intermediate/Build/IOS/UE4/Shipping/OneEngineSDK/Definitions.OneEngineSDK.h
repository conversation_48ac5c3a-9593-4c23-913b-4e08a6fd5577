#undef ONEENGINESDK_API
#undef UE_IS_ENGINE_MODULE
#undef DEPRECATED_FORGAME
#define DEPRECATED_FORGAME DEPRECATED
#undef UE_DEPRECATED_FORGAME
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define IS_PROGRAM 0
#define UE_GAME 1
#define HAS_METAL 1
#define ENABLE_PGO_PROFILE 0
#define USE_VORBIS_FOR_STREAMING 1
#define USE_XMA2_FOR_STREAMING 1
#define WITH_DEV_AUTOMATION_TESTS 0
#define WITH_PERF_AUTOMATION_TESTS 0
#define UNICODE 1
#define _UNICODE 1
#define __UNREAL__ 1
#define IS_MONOLITHIC 1
#define WITH_ENGINE 1
#define WITH_UNREAL_DEVELOPER_TOOLS 0
#define WITH_APPLICATION_CORE 1
#define WITH_COREUOBJECT 1
#define USE_STATS_WITHOUT_ENGINE 0
#define WITH_PLUGIN_SUPPORT 0
#define WITH_ACCESSIBILITY 1
#define WITH_PERFCOUNTERS 0
#define USE_LOGGING_IN_SHIPPING 0
#define WITH_LOGGING_TO_MEMORY 0
#define USE_CACHE_FREED_OS_ALLOCS 1
#define USE_CHECKS_IN_SHIPPING 0
#define USE_ESTIMATED_UTCNOW 0
#define WITH_EDITOR 0
#define WITH_EDITORONLY_DATA 0
#define WITH_SERVER_CODE 1
#define WITH_PUSH_MODEL 0
#define WITH_CEF3 0
#define WITH_LIVE_CODING 0
#define UBT_MODULE_MANIFEST "UE4-IOS-Shipping.modules"
#define UBT_MODULE_MANIFEST_DEBUGGAME "UE4-IOS-DebugGame.modules"
#define UBT_COMPILED_PLATFORM IOS
#define UBT_COMPILED_TARGET Game
#define UE_APP_NAME "UE4"
#define PLATFORM_IOS 1
#define PLATFORM_APPLE 1
#define WITH_TTS 0
#define WITH_SPEECH_RECOGNITION 0
#define WITH_EDITOR 0
#define USE_NULL_RHI 0
#define NOTIFICATIONS_ENABLED 0
#define BACKGROUNDFETCH_ENABLED 0
#define FILESHARING_ENABLED 0
#define UE_DISABLE_FORCE_INLINE 0
#define WITH_SIMULATOR 0
#define ENABLE_ADVERTISING_IDENTIFIER 1
#define MINIMUM_UE4_COMPILED_IOS_VERSION 120000
#define NDEBUG 1
#define UE_BUILD_SHIPPING 1
#define UE_IS_ENGINE_MODULE 0
#define UE_PROJECT_NAME OneSDKDemo
#define UE_TARGET_NAME OneSDKDemo
#define KONEENGINE_REGION_MAINLAND 1
#define ENGINE_SUPPORT_SONY 0
#define UE_MODULE_NAME "OneEngineSDK"
#define UE_PLUGIN_NAME "OneEngineSDK"
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define DEPRECATED_FORGAME DEPRECATED
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UNIQUENETID_ESPMODE ESPMode::Fast
#define COREUOBJECT_API 
#define UE_ENABLE_ICU 1
#define WITH_DIRECTXMATH 0
#define WITH_MALLOC_STOMP 0
#define CORE_API 
#define TRACELOG_API 
#define INCLUDE_CHAOS 0
#define WITH_PHYSX 1
#define WITH_CHAOS 0
#define WITH_CHAOS_CLOTHING 0
#define WITH_CHAOS_NEEDS_TO_BE_FIXED 0
#define PHYSICS_INTERFACE_PHYSX 1
#define WITH_APEX 0
#define WITH_APEX_CLOTHING 0
#define WITH_CLOTH_COLLISION_DETECTION 0
#define WITH_PHYSX_COOKING 0
#define WITH_NVCLOTH 0
#define WITH_CUSTOM_SQ_STRUCTURE 0
#define WITH_IMMEDIATE_PHYSX 0
#define GPUPARTICLE_LOCAL_VF_ONLY 0
#define WITH_ODSC 0
#define ENGINE_API 
#define NETCORE_API 
#define APPLICATIONCORE_API 
#define RHI_API 
#define JSON_API 
#define WITH_FREETYPE 1
#define SLATECORE_API 
#define INPUTCORE_API 
#define SLATE_API 
#define WITH_UNREALPNG 1
#define WITH_UNREALJPEG 1
#define WITH_LIBJPEGTURBO 0
#define WITH_UNREALEXR 0
#define IMAGEWRAPPER_API 
#define MESSAGING_API 
#define MESSAGINGCOMMON_API 
#define RENDERCORE_API 
#define ANALYTICSET_API 
#define ANALYTICS_API 
#define SOCKETS_PACKAGE 1
#define SOCKETS_API 
#define NETCOMMON_API 
#define ASSETREGISTRY_API 
#define ENGINEMESSAGES_API 
#define ENGINESETTINGS_API 
#define SYNTHBENCHMARK_API 
#define RENDERER_API 
#define GAMEPLAYTAGS_API 
#define DEVELOPERSETTINGS_API 
#define PACKETHANDLER_API 
#define RELIABILITYHANDLERCOMPONENT_API 
#define AUDIOPLATFORMCONFIGURATION_API 
#define MESHDESCRIPTION_API 
#define STATICMESHDESCRIPTION_API 
#define PAKFILE_API 
#define RSA_API 
#define NETWORKREPLAYSTREAMING_API 
#define INCLUDE_CHAOS 0
#define WITH_PHYSX 1
#define WITH_CHAOS 0
#define WITH_CHAOS_CLOTHING 0
#define WITH_CHAOS_NEEDS_TO_BE_FIXED 0
#define PHYSICS_INTERFACE_PHYSX 1
#define WITH_APEX 0
#define WITH_APEX_CLOTHING 0
#define WITH_CLOTH_COLLISION_DETECTION 0
#define WITH_PHYSX_COOKING 0
#define WITH_NVCLOTH 0
#define WITH_CUSTOM_SQ_STRUCTURE 0
#define WITH_IMMEDIATE_PHYSX 0
#define PHYSICSCORE_API 
#define COMPILE_WITHOUT_UNREAL_SUPPORT 0
#define INCLUDE_CHAOS 0
#define CHAOS_MEMORY_TRACKING 0
#define CHAOS_API 
#define COMPILE_WITHOUT_UNREAL_SUPPORT 0
#define INCLUDE_CHAOS 0
#define CHAOS_CHECKED 0
#define CHAOSCORE_API 
#define INTEL_ISPC 0
#define VORONOI_API 
#define WITH_PHYSX_RELEASE 1
#define SIGNALPROCESSING_API 
#define AUDIOEXTENSIONS_API 
#define AUDIOMIXERCORE_API 
#define PROPERTYACCESS_API 
#define INCLUDE_CHAOS 0
#define WITH_PHYSX 1
#define WITH_CHAOS 0
#define WITH_CHAOS_CLOTHING 0
#define WITH_CHAOS_NEEDS_TO_BE_FIXED 0
#define PHYSICS_INTERFACE_PHYSX 1
#define WITH_APEX 0
#define WITH_APEX_CLOTHING 0
#define WITH_CLOTH_COLLISION_DETECTION 0
#define WITH_PHYSX_COOKING 0
#define WITH_NVCLOTH 0
#define WITH_CUSTOM_SQ_STRUCTURE 0
#define WITH_IMMEDIATE_PHYSX 0
#define CLOTHINGSYSTEMRUNTIMEINTERFACE_API 
#define AUDIOMIXER_API 
#define TARGETPLATFORM_API 
#define ANIMATIONCORE_API 
#define JSONUTILITIES_API 
#define READ_TARGET_ENABLED_PLUGINS_FROM_RECEIPT 0
#define LOAD_PLUGINS_FOR_TARGET_PLATFORMS 0
#define PROJECTS_API 
#define KONEENGINE_REGION_MAINLAND 1
#define ENGINE_SUPPORT_SONY 0
#define ONEENGINESDK_API 
#define UMG_API 
#define HTTP_PACKAGE 1
#define WITH_LIBCURL 0
#define WITH_WINHTTP 0
#define HTTP_API 
#define MOVIESCENE_API 
#define TIMEMANAGEMENT_API 
#define MOVIESCENETRACKS_API 
#define PROPERTYPATH_API 
