-c
-pipe
-I"."
-I"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Source/OneEngineSDK/Private"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/CoreUObject/UHT"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/CoreUObject/VerseVMBytecode"
-I"Runtime/CoreUObject/Public"
-I"Runtime/Core/Public"
-I"Runtime/TraceLog/Public"
-I"Runtime/AutoRTFM/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/ImageCore/UHT"
-I"Runtime/ImageCore/Public"
-I"Runtime/CorePreciseFP/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/Engine/UHT"
-I"Runtime/Engine/Classes"
-I"Runtime/Engine/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/CoreOnline/UHT"
-I"Runtime/CoreOnline/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/FieldNotification/UHT"
-I"Runtime/FieldNotification/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/NetCore/UHT"
-I"Runtime/Net/Core/Classes"
-I"Runtime/Net/Core/Public"
-I"Runtime/Net/Common/Public"
-I"Runtime/Json/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/JsonUtilities/UHT"
-I"Runtime/JsonUtilities/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/SlateCore/UHT"
-I"Runtime/SlateCore/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/DeveloperSettings/UHT"
-I"Runtime/DeveloperSettings/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/InputCore/UHT"
-I"Runtime/InputCore/Classes"
-I"Runtime/InputCore/Public"
-I"Runtime/ApplicationCore/Public/IOS"
-I"Runtime/ApplicationCore/Private/Apple"
-I"Runtime/ApplicationCore/Public"
-I"Runtime/RHI/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/Slate/UHT"
-I"Runtime/Slate/Public"
-I"Runtime/ImageWrapper/Public"
-I"Runtime/Messaging/Public"
-I"Runtime/MessagingCommon/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/RenderCore/UHT"
-I"Runtime/RenderCore/Public"
-I"Runtime/Analytics/AnalyticsET/Public"
-I"Runtime/Analytics/Analytics/Public"
-I"Runtime/Sockets/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/AssetRegistry/UHT"
-I"Runtime/AssetRegistry/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/EngineMessages/UHT"
-I"Runtime/EngineMessages/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/EngineSettings/UHT"
-I"Runtime/EngineSettings/Classes"
-I"Runtime/EngineSettings/Public"
-I"Runtime/SynthBenchmark/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/GameplayTags/UHT"
-I"Runtime/GameplayTags/Classes"
-I"Runtime/GameplayTags/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/PacketHandler/UHT"
-I"Runtime/PacketHandlers/PacketHandler/Classes"
-I"Runtime/PacketHandlers/PacketHandler/Public"
-I"Runtime/PacketHandlers/ReliabilityHandlerComponent/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/AudioPlatformConfiguration/UHT"
-I"Runtime/AudioPlatformConfiguration/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/MeshDescription/UHT"
-I"Runtime/MeshDescription/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/StaticMeshDescription/UHT"
-I"Runtime/StaticMeshDescription/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/SkeletalMeshDescription/UHT"
-I"Runtime/SkeletalMeshDescription/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/AnimationCore/UHT"
-I"Runtime/AnimationCore/Public"
-I"Runtime/PakFile/Public"
-I"Runtime/RSA/Public"
-I"Runtime/NetworkReplayStreaming/NetworkReplayStreaming/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/PhysicsCore/UHT"
-I"Runtime/PhysicsCore/Public"
-I"Runtime/Experimental/ChaosCore/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/Chaos/UHT"
-I"Runtime/Experimental/Chaos/Public"
-I"Runtime/Experimental/Voronoi/Public"
-I"Runtime/GeometryCore/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/ChaosVDRuntime/UHT"
-I"Runtime/Experimental/ChaosVisualDebugger/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/NNE/UHT"
-I"Runtime/NNE/Public"
-I"Runtime/SignalProcessing/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/StateStream/UHT"
-I"Runtime/StateStream/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/AudioExtensions/UHT"
-I"Runtime/AudioExtensions/Public"
-I"Runtime/AudioMixerCore/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/AudioMixer/UHT"
-I"Runtime/AudioMixer/Classes"
-I"Runtime/AudioMixer/Public"
-I"Developer/TargetPlatform/Public"
-I"Developer/TextureFormat/Public"
-I"Developer/DesktopPlatform/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/AudioLinkEngine/UHT"
-I"Runtime/AudioLink/AudioLinkEngine/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/AudioLinkCore/UHT"
-I"Runtime/AudioLink/AudioLinkCore/Public"
-I"Runtime/Networking/Public"
-I"Runtime/Experimental/IoStore/OnDemandCore/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/ClothSysRuntimeIntrfc/UHT"
-I"Runtime/ClothingSystemRuntimeInterface/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/IrisCore/UHT"
-I"Runtime/Experimental/Iris/Core/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/MovieSceneCapture/UHT"
-I"Runtime/MovieSceneCapture/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/Renderer/UHT"
-I"Runtime/Renderer/Public"
-I"../Shaders/Public"
-I"../Shaders/Shared"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/TypedElementFramework/UHT"
-I"Runtime/TypedElementFramework/Tests"
-I"Runtime/TypedElementFramework/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/TypedElementRuntime/UHT"
-I"Runtime/TypedElementRuntime/Public"
-I"Runtime/IOS/IOSPlatformFeatures/Public"
-I"Runtime/Projects/Public"
-I"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/IOS/UnrealGame/Inc/OneEngineSDK/UHT"
-I"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Source"
-I"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Source/OneEngineSDK/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/UMG/UHT"
-I"Runtime/UMG/Public"
-I"Runtime/Online/HTTP/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/MovieScene/UHT"
-I"Runtime/MovieScene/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/TimeManagement/UHT"
-I"Runtime/TimeManagement/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/UniversalObjectLocator/UHT"
-I"Runtime/UniversalObjectLocator/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/MovieSceneTracks/UHT"
-I"Runtime/MovieSceneTracks/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/Constraints/UHT"
-I"Runtime/Experimental/Animation/Constraints/Public"
-I"../Intermediate/Build/IOS/UnrealGame/Inc/PropertyPath/UHT"
-I"Runtime/PropertyPath/Public"
-I"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Intermediate/Build/IOS/arm64/OneSDKDemo/Development/Engine"
-isystem"ThirdParty/GuidelinesSupportLibrary/GSL-1144/include"
-isystem"ThirdParty/AtomicQueue"
-isystem"ThirdParty/RapidJSON/1.1.0"
-Wall
-Werror
-Wdelete-non-virtual-dtor
-Wenum-conversion
-Wbitfield-enum-conversion
-Wno-shadow
-Wno-undefined-var-template
-Wno-unused-but-set-variable
-Wno-unused-but-set-parameter
-Wno-unused-function
-Wno-unused-lambda-capture
-Wno-unused-local-typedef
-Wno-unused-private-field
-Wno-unused-variable
-Wno-unknown-pragmas
-Wno-tautological-compare
-Wno-switch
-Wno-invalid-offsetof
-Wno-inconsistent-missing-override
-Wno-gnu-string-literal-operator-template
-Wno-invalid-unevaluated-string
-Wno-dllexport-explicit-instantiation-decl
-Wno-deprecated-copy
-Wno-ordered-compare-function-pointers
-Wno-deprecated-volatile
-Wno-deprecated-anon-enum-enum-conversion
-Wno-ambiguous-reversed-operator
-Wno-enum-float-conversion
-Wno-enum-enum-conversion
-Wno-float-conversion
-Wno-implicit-float-conversion
-Wno-implicit-int-conversion
-Wno-c++11-narrowing
-Wno-bitwise-instead-of-logical
-Wdeprecated-declarations
-Wno-error=deprecated-declarations
-Wno-deprecated-copy-with-user-provided-copy
-fdiagnostics-absolute-paths
-fdiagnostics-color
-Wno-unknown-warning-option
-Wno-range-loop-analysis
-Wno-single-bit-bitfield-constant-conversion
-Wno-nonportable-include-path
-ffp-contract=off
-fno-delete-null-pointer-checks
-O3
-fsigned-char
-fno-exceptions
-DPLATFORM_EXCEPTIONS_DISABLED=1
-gdwarf-4
-fvisibility=hidden
-fvisibility-inlines-hidden
-fno-rtti
-fmessage-length=0
-fpascal-strings
-target arm64-apple-ios15.0
-arch arm64
-isysroot "/Applications/Xcode-16.2.0.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk"
-F "../Intermediate/UnzippedFrameworks/WPOneEngineBridge/WPOneEngineBridge.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WPOneEngineBridgeLaohu/WPOneEngineBridgeLaohu.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMFatigueManageSDK/WMFatigueManageSDK.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/LHSocialCore/LHSocialCore.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/TYRZUISDK/TYRZUISDK.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMFMDB/WMFMDB.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMLaohuSDK/WMLaohuSDK.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMLaohuShareSDK/WMLaohuShareSDK.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMNetworkDiagnose/WMNetworkDiagnose.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMOneLHIAP/WMOneLHIAP.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WPAdSupport/WPAdSupport.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WPAnalysisSDK/WPAnalysisSDK.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMTOMLSerialization/WMTOMLSerialization.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/AutoShowTerms/AutoShowTerms.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMTransitionAnimator/WMTransitionAnimator.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMUActCode/WMUActCode.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMURedeemCode/WMURedeemCode.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMRedeemCode/WMRedeemCode.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMAFNetworking/WMAFNetworking.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMCategories/WMCategories.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMDevice/WMDevice.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMMBProgressHUD/WMMBProgressHUD.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMMasonry/WMMasonry.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/ncnn/ncnn.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/opencv2/opencv2.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/openmp/openmp.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMFasCore/WMFasCore.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMFasSDK/WMFasSDK.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WPCustomUITheme/WPCustomUITheme.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WMCollectInfoSDK/WMCollectInfoSDK.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WPAppPrivacy/WPAppPrivacy.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WPPushSDK/WPPushSDK.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/WPInfoSyncSDK/WPInfoSyncSDK.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/TencentOpenAPI/TencentOpenAPI.embeddedframework"
-F "../Intermediate/UnzippedFrameworks/BiliFollowing/BiliFollowing.embeddedframework"
-include-pch "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Intermediate/Build/IOS/arm64/OneSDKDemo/Development/Engine/SharedPCH.Engine.Project.ValApi.ValExpApi.Cpp20.InclOrderUnreal5_3.h.gch"
-include "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/IOS/arm64/UnrealGame/Development/OneEngineSDK/Definitions.OneEngineSDK.h"
-x objective-c++
-std=c++20
-Xclang -fno-pch-timestamp
-fpch-validate-input-files-content
-stdlib=libc++
-fmodules
-fno-implicit-modules
-fimplicit-module-maps
-Wno-module-import-in-extern-c
"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/IOS/arm64/UnrealGame/Development/OneEngineSDK/Module.OneEngineSDK.1.cpp"
-MD -MF"/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/IOS/arm64/UnrealGame/Development/OneEngineSDK/Module.OneEngineSDK.1.cpp.d"
-o "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE5.6_union/Plugins/OneEngineSDK/Intermediate/Build/IOS/arm64/UnrealGame/Development/OneEngineSDK/Module.OneEngineSDK.1.cpp.o"
-fno-objc-exceptions